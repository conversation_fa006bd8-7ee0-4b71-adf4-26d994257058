module.exports = {
  plugins: ['vue'],
  extends: [
    "airbnb-base",
    "plugin:vue/recommended",
    "eslint:recommended",
    'prettier',
  ],
  rules: {
    "no-console": process.env.NODE_ENV === "production" ? ["error", { allow: ["error"] }] : "off",
    "no-debugger": process.env.NODE_ENV === "production" ? "error" : "off",
    "no-param-reassign": ["error", { props: false }],
    "import/no-unresolved": "off",
    "no-useless-return": "off",
    "quotes": "off",
    "semi": ["error", "always"],
    "comma-dangle": ["error", {
      "arrays": "always-multiline",
      "objects": "always-multiline",
      "imports": "always-multiline",
      "exports": "always-multiline",
      "functions": "never",
    }],
    "no-else-return": ["error", { "allowElseIf": true }],
    "no-unused-expressions": ["error", { "allowTernary": true }],
    "space-before-function-paren": ["error", {
      "anonymous": "always",
      "named": "never",
      "asyncArrow": "always",
    }],
    "prefer-const": ["error", {
      "destructuring": "all",
      "ignoreReadBeforeAssign": true,
    }],
    "vue/html-indent": ["error", 2],
    "vue/html-quotes": ["error", "double", {
      "avoidEscape": true,
    }],
    "vue/attributes-order": ["error", {
      "alphabetical": false,
    }],
    "vue/max-attributes-per-line": ["error", {
      "singleline": {
        "max": 1,
      },
      "multiline": {
        "max": 1,
      },
    }],
    "vue/html-closing-bracket-newline": ["error", {
      "singleline": "never",
      "multiline": "always",
    }],
    "vue/html-self-closing": ["error", {
      "html": {
        "void": "never",
        "normal": "always",
        "component": "always",
      },
      "svg": "always",
      "math": "always",
    }],
    "vue/multi-word-component-names": 0,
    "valid-typeof": "off",
    "vue/require-prop-types": "off",
  },
  globals: {
    "moment": "readonly",
    "Vue": "readonly",
    "VueRouter": "readonly",
    "Vuex": "readonly",
    "mixpanel": "readonly",
    "Bugsnag": "readonly",
    "SimpleBar": "readonly",
    "$SiteScroll": "readonly",
    "$permissions": "writable",
    "$workspaceSelectedFromDropdown": "writable",
    "$workspace": "writable",
    "getWorkspaceFromStorage": "writable",
    "setWorkspaceToStorage": "writable",
    "getCompanyFromStorage": "writable",
    "setCompanyToStorage": "writable",
    "hasWriteAllPermission": 'writable',
  },
  parserOptions: {
    parser: 'babel-eslint',
    ecmaVersion: 2022,
    "sourceType": "module",
  },
  settings: {
    "import/resolver": {
      node: {
        paths: ["./app/javascript"],
      },
    },
  },
  ignorePatterns: [
    "public/**/*.js",
  ],
};
