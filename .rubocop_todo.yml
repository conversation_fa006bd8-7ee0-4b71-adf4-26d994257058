# Offense count: 48
Bundler/OrderedGems:
  Exclude:
    - 'Gemfile'


# Offense count: 136
Layout/BlockEndNewline:
  Enabled: false

# Offense count: 84
Layout/ClosingParenthesisIndentation:
  Enabled: false

# Offense count: 7
Layout/ConditionPosition:
  Exclude:
    - 'app/models/integrations/microsoft/app.rb'
    - 'app/models/integrations/okta/app.rb'
    - 'app/models/integrations/one_login/app.rb'
    - 'app/models/integrations/salesforce/app.rb'
    - 'app/services/import_export/assets/base.rb'
    - 'app/sidekiq/integrations/gsuite/sync_data_worker.rb'

# Offense count: 46
Layout/DotPosition:
  Exclude:
    - 'app/controllers/options/assigned_to_options_controller.rb'
    - 'app/controllers/staff_list_options_controller.rb'
    - 'app/models/company_user.rb'
    - 'app/models/help_ticket.rb'
    - 'app/models/user.rb'
    - 'app/models/vendor.rb'
    - 'app/services/automated_tasks/task_execute.rb'
    - 'app/services/help_desk/abbreviated_ticket_load.rb'
    - 'app/services/help_desk/inbound_email/ticket_email_create.rb'
    - 'app/services/options/priority_options.rb'
    - 'app/services/options/status_options.rb'
    - 'app/services/workspace_resolver.rb'
    - 'spec/rails_helper.rb'
    - 'spec/services/build_service_spec.rb'

# Offense count: 382
Layout/EmptyLineAfterGuardClause:
  Enabled: false

# Offense count: 131
Layout/EmptyLineBetweenDefs:
  Enabled: false

# Offense count: 163
Layout/EmptyLinesAroundAccessModifier:
  Enabled: false

# Offense count: 13
Layout/EmptyLinesAroundAttributeAccessor:
  Exclude:
    - 'app/models/company.rb'
    - 'app/models/concerns/automated_tasks/event_firing.rb'
    - 'app/models/concerns/default_tagging.rb'
    - 'app/models/concerns/rewards.rb'
    - 'app/models/contract.rb'
    - 'app/models/custom_form.rb'
    - 'app/models/custom_form_field.rb'
    - 'app/models/location.rb'
    - 'app/models/user.rb'
    - 'app/services/automated_tasks/task_executor.rb'
    - 'app/services/file_manipulation_service.rb'
    - 'app/services/warranty_checker.rb'
    - 'app/sidekiq/integrations/quickbooks/sync_data_worker.rb'

# Offense count: 388
Layout/EmptyLinesAroundBlockBody:
  Enabled: false

# Offense count: 22
Layout/EmptyLinesAroundMethodBody:
  Enabled: false

# Offense count: 41
Layout/EmptyLinesAroundModuleBody:
  Enabled: false

# Offense count: 30
Layout/EndOfLine:
  Enabled: false

# Offense count: 344
Layout/ExtraSpacing:
  Enabled: false

# Offense count: 152
Layout/FirstArgumentIndentation:
  Enabled: false

# Offense count: 43
Layout/FirstArrayElementIndentation:
  Enabled: false

# Offense count: 556
Layout/FirstHashElementIndentation:
  Enabled: false

# Offense count: 2324
Layout/HashAlignment:
  Enabled: false

# Offense count: 304
Layout/IndentationConsistency:
  Enabled: false

# Offense count: 112
Layout/IndentationStyle:
  Enabled: false

# Offense count: 382
Layout/IndentationWidth:
  Enabled: false


# Offense count: 153
Layout/LeadingCommentSpace:
  Enabled: false

# Offense count: 15
Layout/MultilineArrayBraceLayout:
  Exclude:
    - 'app/controllers/categories_based_insights_controller.rb'
    - 'app/controllers/products_based_insights_controller.rb'
    - 'app/controllers/tags_based_insights_controller.rb'
    - 'app/controllers/telecom_providers_controller.rb'
    - 'app/controllers/vendors_based_insights_controller.rb'
    - 'app/models/experience_point.rb'
    - 'app/models/general_transaction_tag.rb'
    - 'lib/tasks/company.rake'
    - 'lib/tasks/managed_asset_summary.rake'
    - 'spec/controllers/help_tickets/project_task_positions_controller_spec.rb'
    - 'spec/services/integrations/google_assets/fetch_data_spec.rb'
    - 'spec/sidekiq/azure_assets/sync_data_worker_spec.rb'
    - 'spec/sidekiq/meraki/sync_data_worker_spec.rb'
    - 'spec/sidekiq/microsoft/sync_data_worker_spec.rb'

# Offense count: 173
Layout/MultilineBlockLayout:
  Enabled: false

# Offense count: 301
Layout/MultilineHashBraceLayout:
  Enabled: false

# Offense count: 194
Layout/MultilineMethodCallBraceLayout:
  Enabled: false

# Offense count: 307
Layout/MultilineMethodCallIndentation:
  Enabled: false

# Offense count: 66
Layout/MultilineOperationIndentation:
  Enabled: false

# Offense count: 308
Layout/SpaceAfterColon:
  Enabled: false

# Offense count: 224
Layout/SpaceAfterComma:
  Enabled: false


# Offense count: 15
Layout/SpaceAfterNot:
  Exclude:
    - 'app/admin/company.rb'
    - 'app/commands/authenticate_mobile_user.rb'
    - 'app/commands/authenticate_user.rb'
    - 'app/controllers/api/v2/sessions_controller.rb'
    - 'app/controllers/ip_lookups_controller.rb'
    - 'app/controllers/monitoring_devices_controller.rb'
    - 'app/controllers/phone_number_lookups_controller.rb'
    - 'app/controllers/subnet_calculator_controller.rb'
    - 'app/controllers/users/sessions_controller.rb'
    - 'app/models/assets_csv_file.rb'
    - 'spec/models/ability_spec.rb'

# Offense count: 28
Layout/SpaceAroundBlockParameters:
  Exclude:
    - 'app/controllers/api/v1/base_controller.rb'
    - 'app/controllers/api/v1/sessions_controller.rb'
    - 'app/controllers/api/v2/base_controller.rb'
    - 'app/controllers/api/v2/sessions_controller.rb'
    - 'app/controllers/api/v1/authenticate_users_controller.rb'
    - 'app/controllers/current_company_users_controller.rb'
    - 'app/models/telecom_services_csv_file.rb'
    - 'app/services/custom_form_report.rb'
    - 'app/services/import_export/custom_entity/xls_export.rb'
    - 'app/services/import_export/vendors/vendor.rb'
    - 'db/migrate/20180216082755_add_guid_value_to_company_user.rb'

# Offense count: 89
Layout/SpaceAroundEqualsInParameterDefault:
  Enabled: false


# Offense count: 3331
Layout/SpaceAroundOperators:
  Enabled: false

# Offense count: 367
Layout/SpaceBeforeBlockBraces:
  Enabled: false

# Offense count: 85
Layout/SpaceBeforeComma:
  Enabled: false

# Offense count: 25
Layout/SpaceBeforeFirstArg:
  Enabled: false

# Offense count: 323
Layout/SpaceInsideArrayLiteralBrackets:
  Enabled: false

# Offense count: 455
Layout/SpaceInsideBlockBraces:
  Enabled: false

# Offense count: 2135
Layout/SpaceInsideHashLiteralBraces:
  Enabled: false

# Offense count: 231
Layout/SpaceInsideParens:
  Enabled: false

# Offense count: 69
Layout/SpaceInsidePercentLiteralDelimiters:
  Enabled: false

# Offense count: 108
Layout/SpaceInsideStringInterpolation:
  Enabled: false

# Offense count: 333
Layout/TrailingEmptyLines:
  Enabled: false

# Offense count: 1581
Layout/TrailingWhitespace:
  Enabled: false


# Offense count: 37
Lint/AmbiguousOperatorPrecedence:
  Enabled: false

# Offense count: 21
Lint/AmbiguousRange:
  Enabled: false

# Offense count: 2
Lint/AmbiguousRegexpLiteral:
  Exclude:
    - 'config/application.rb'

# Offense count: 33
Lint/AssignmentInCondition:
  Enabled: false


# Offense count: 15
Lint/ConstantDefinitionInBlock:
  Exclude:
    - 'app/models/concerns/fiscal_year.rb'
    - 'app/models/concerns/ms_teams_constants.rb'
    - 'app/models/concerns/regions.rb'
    - 'app/models/concerns/slack_constants.rb'
    - 'app/models/concerns/states.rb'
    - 'lib/tasks/default_vendors.rake'
    - 'spec/controllers/integrations/slack/consent_controller_spec.rb'
    - 'spec/requests/check_email_responses_spec.rb'
    - 'spec/services/help_desk/values_update_from_email_spec.rb'
    - 'spec/sidekiq/inbound_email_worker_spec.rb'
    - 'spec/sidekiq/microsoft/sync_data_worker_spec.rb'


# Offense count: 63
Lint/DuplicateBranch:
  Enabled: false

# Offense count: 3
Lint/DuplicateCaseCondition:
  Exclude:
    - 'app/services/automated_tasks/string_interpolate.rb'
    - 'app/services/base_asset_service.rb'
    - 'lib/tasks/managed_asset.rake'

# Offense count: 1
Lint/DuplicateElsifCondition:
  Exclude:
    - 'app/models/user.rb'

# Offense count: 39
Lint/DuplicateMethods:
  Enabled: false

# Offense count: 3
Lint/ElseLayout:
  Exclude:
    - 'app/services/help_desk/ticket_query_old.rb'
    - 'app/services/help_tickets/activities.rb'
    - 'lib/tasks/integration_configuration.rake'

# Offense count: 50
Lint/EmptyBlock:
  Enabled: false

# Offense count: 2
Lint/EmptyClass:
  Exclude:
    - 'app/models/marketplace.rb'
    - 'app/models/monitoring.rb'

# Offense count: 7
Lint/EmptyConditionalBody:
  Exclude:
    - 'app/controllers/users/invitations_controller.rb'
    - 'app/models/integrations/microsoft/app.rb'
    - 'app/models/integrations/okta/app.rb'
    - 'app/models/integrations/one_login/app.rb'
    - 'app/models/integrations/salesforce/app.rb'
    - 'app/services/import_export/assets/base.rb'
    - 'app/sidekiq/integrations/gsuite/sync_data_worker.rb'

# Offense count: 1
Lint/EmptyFile:
  Exclude:
    - 'spec/controllers/automated_tasks/event_subject_types_controller_spec.rb'

# Offense count: 7
Lint/FloatComparison:
  Exclude:
    - 'app/controllers/general_transaction_monthly_breakdowns_controller.rb'
    - 'app/controllers/general_transaction_spends_controller.rb'
    - 'app/models/time_spent.rb'
    - 'app/sidekiq/integrations/aws/sync_data_worker.rb'
    - 'app/sidekiq/integrations/azure/sync_data_worker.rb'

# Offense count: 342
Lint/ImplicitStringConcatenation:
  Enabled: false

# Offense count: 4
Lint/IneffectiveAccessModifier:
  Exclude:
    - 'app/models/location.rb'
    - 'app/services/import_export/contracts/contract.rb'
    - 'app/services/import_export/general_transactions/general_transaction.rb'
    - 'app/services/import_export/telecom_services/telecom_service.rb'

# Offense count: 1
Lint/InheritException:
  Exclude:
    - 'app/services/ignorable_exception.rb'

# Offense count: 1
Lint/LiteralInInterpolation:
  Exclude:
    - 'spec/controllers/products_controller_spec.rb'

# Offense count: 2
Lint/Loop:
  Exclude:
    - 'app/models/user.rb'

# Offense count: 29
Lint/MissingSuper:
  Enabled: false

# Offense count: 7
Lint/NoReturnInBeginEndBlocks:
  Exclude:
    - 'app/controllers/concerns/multi_company/general_scoping.rb'
    - 'app/controllers/ip_addresses_controller.rb'
    - 'app/controllers/phone_numbers_controller.rb'
    - 'app/controllers/webhooks/integrations/helpdesk/slack/base_controller.rb'
    - 'app/services/integrations/slack/client.rb'

# Offense count: 5
Lint/NonLocalExitFromIterator:
  Exclude:
    - 'app/controllers/custom_form_attachments_controller.rb'
    - 'app/controllers/help_tickets/attachments_controller.rb'
    - 'app/services/import_export/import_base.rb'
    - 'app/services/import_export/xls_import_base.rb'
    - 'app/sidekiq/integrations/gsuite/sync_data_worker.rb'

# Offense count: 7
Lint/OrAssignmentToConstant:
  Exclude:
    - 'app/models/concerns/regions.rb'
    - 'app/models/concerns/saas_companies.rb'
    - 'db/seeds/07_default_products.rb'
    - 'db/seeds/custom_form_templates/_fields_setup.rb'
    - 'db/seeds/custom_form_templates/_location_fields_setup.rb'
    - 'db/seeds/custom_form_templates/_staff_fields_setup.rb'
    - 'lib/paperclip/validators/attachment_size_validator.rb'

# Offense count: 80
Lint/ParenthesesAsGroupedExpression:
  Enabled: false

# Offense count: 19
Lint/RaiseException:
  Exclude:
    - 'app/controllers/contract_alert_dates_controller.rb'
    - 'app/services/integrations/azure/fetch_data.rb'
    - 'app/services/integrations/azure_assets/fetch_data.rb'
    - 'app/services/integrations/bill/fetch_data.rb'
    - 'app/services/integrations/expensify/fetch_data.rb'
    - 'app/services/integrations/meraki/fetch_data.rb'
    - 'app/services/integrations/okta/fetch_data.rb'
    - 'app/services/integrations/one_login/fetch_data.rb'
    - 'app/services/integrations/quickbooks/fetch_data.rb'
    - 'app/services/integrations/sage_accounting/fetch_data.rb'
    - 'app/services/integrations/salesforce/fetch_data.rb'
    - 'app/services/integrations/ubiquiti/fetch_data.rb'
    - 'app/sidekiq/integrations/azure/sync_data_worker.rb'
    - 'app/sidekiq/integrations/azure_assets/sync_data_worker.rb'

# Offense count: 2
Lint/RedundantDirGlobSort:
  Exclude:
    - 'db/seeds.rb'
    - 'db/seeds/18_custom_form_templates.rb'

# Offense count: 3
Lint/RedundantSafeNavigation:
  Exclude:
    - 'app/controllers/concerns/custom_field_activity_helper.rb'
    - 'app/sidekiq/inbound_email_worker.rb'

# Offense count: 8
Lint/RedundantSplatExpansion:
  Exclude:
    - 'app/services/help_desk/ticket_query/field_sort_clause.rb'
    - 'app/services/help_desk/ticket_query/involved_clause.rb'
    - 'app/services/help_desk/ticket_query/staff_list_sort_clause.rb'
    - 'app/services/reporting/contract/contract_data.rb'
    - 'app/services/reporting/vendor/vendor_data.rb'

# Offense count: 15
Lint/RedundantStringCoercion:
  Exclude:
    - 'app/services/automated_tasks/string_interpolate.rb'
    - 'app/services/integrations/expensify/fetch_data.rb'
    - 'app/services/integrations/gsuite/fetch_data.rb'
    - 'app/services/integrations/gsuite_ad/fetch_data.rb'
    - 'app/sidekiq/integrations/gsuite/save_apps_usage.rb'
    - 'app/sidekiq/integrations/gsuite/sync_data_worker.rb'
    - 'app/sidekiq/integrations/gsuite_ad/fetch_groups_and_org.rb'
    - 'app/sidekiq/integrations/gsuite_ad/sync_data_worker.rb'

# Offense count: 21
Lint/RedundantWithIndex:
  Exclude:
    - 'app/services/import_export/import_base.rb'
    - 'spec/services/import_export/assets/xls_import_spec.rb'
    - 'spec/sidekiq/import_export/company_users/import_worker_spec.rb'
    - 'spec/sidekiq/import_export/contracts/import_worker_spec.rb'
    - 'spec/sidekiq/import_export/general_transactions/import_worker_spec.rb'
    - 'spec/sidekiq/import_export/ip_addresses/import_worker_spec.rb'
    - 'spec/sidekiq/import_export/locations/import_worker_spec.rb'
    - 'spec/sidekiq/import_export/phone_numbers/import_worker_spec.rb'
    - 'spec/sidekiq/import_export/telecom_services/import_worker_spec.rb'
    - 'spec/sidekiq/import_export/vendors/import_worker_spec.rb'

# Offense count: 128
Lint/RescueException:
  Enabled: false

# Offense count: 57
Lint/SafeNavigationChain:
  Enabled: false

# Offense count: 2
Lint/SafeNavigationConsistency:
  Exclude:
    - 'app/controllers/users/registrations_controller.rb'
    - 'app/services/automated_tasks/actions/send_email.rb'

# Offense count: 2
Lint/SelfAssignment:
  Exclude:
    - 'app/controllers/registration_emails_controller.rb'

# Offense count: 1
Lint/ShadowedArgument:
  Exclude:
    - 'lib/paperclip/validators/attachment_size_validator.rb'

# Offense count: 37
Lint/ShadowingOuterLocalVariable:
  Enabled: false

# Offense count: 2
Lint/SuppressedException:
  Exclude:
    - 'app/services/file_manipulation_service.rb'
    - 'app/sidekiq/integrations/jamf_pro/sync_data_worker.rb'

# Offense count: 1055
Lint/SymbolConversion:
  Enabled: false

# Offense count: 225
Lint/TripleQuotes:
  Enabled: false

# Offense count: 2
Lint/UnderscorePrefixedVariableName:
  Exclude:
    - 'app/controllers/vendor_alert_summaries_controller.rb'
    - 'app/models/vendor.rb'

# Offense count: 31
Lint/UnexpectedBlockArity:
  Enabled: false

# Offense count: 1
Lint/UnreachableCode:
  Exclude:
    - 'app/services/telecom_validator.rb'

# Offense count: 54
Lint/UnusedBlockArgument:
  Enabled: false

# Offense count: 114
Lint/UnusedMethodArgument:
  Enabled: false

# Offense count: 1
Lint/UriEscapeUnescape:
  Exclude:
    - 'lib/tasks/help_ticket.rake'

# Offense count: 3
Lint/UselessAccessModifier:
  Exclude:
    - 'app/controllers/msp/templates/custom_forms_controller.rb'
    - 'app/controllers/webhooks/integrations/net_suite_controller.rb'
    - 'app/controllers/webhooks/integrations/sage_intacct_controller.rb'

# Offense count: 592
Lint/UselessAssignment:
  Enabled: false

# Offense count: 4
Lint/UselessMethodDefinition:
  Exclude:
    - 'app/models/company_member.rb'
    - 'app/services/ignorable_exception.rb'
    - 'spec/controllers/application_controller_spec.rb'

# Offense count: 7
Lint/Void:
  Exclude:
    - 'app/controllers/import_xls_controller.rb'
    - 'app/models/company_mailer.rb'
    - 'lib/tasks/help_ticket_email.rake'
    - 'lib/tasks/sample_companies.rake'
    - 'spec/controllers/help_tickets/in_progress_ticket_summaries_controller_spec.rb'
    - 'spec/requests/check_email_responses_spec.rb'

# Offense count: 1214
Metrics/AbcSize:
  Max: 292
  Exclude:
    - 'app/controllers/oauth2_controller.rb'

# Offense count: 999
Metrics/BlockLength:
  Max: 1600
  Exclude:
    - 'config/routes.rb'

# Offense count: 89
Metrics/BlockNesting:
  Max: 10

# Offense count: 174
Metrics/ClassLength:
  Max: 1396

# Offense count: 463
Metrics/CyclomaticComplexity:
  Max: 100

# Offense count: 1541
Metrics/MethodLength:
  Max: 190

# Offense count: 10
Metrics/ModuleLength:
  Max: 580

# Offense count: 11
Metrics/ParameterLists:
  MaxOptionalParameters: 5
  Max: 8

# Offense count: 422
Metrics/PerceivedComplexity:
  Max: 100

# Offense count: 100
Naming/AccessorMethodName:
  Enabled: false

# Offense count: 3
Naming/BinaryOperatorParameterName:
  Exclude:
    - 'app/models/time_spent.rb'

# Offense count: 4
Naming/HeredocDelimiterCase:
  Exclude:
    - 'app/services/attachment_service.rb'
    - 'spec/controllers/help_tickets/ticket_comments_controller_spec.rb'

# Offense count: 88
Naming/HeredocDelimiterNaming:
  Enabled: false

# Offense count: 87
Naming/MemoizedInstanceVariableName:
  Enabled: false

# Offense count: 6
Naming/MethodName:
  Exclude:
    - 'app/controllers/azure_licenses_controller.rb'
    - 'app/controllers/general_transaction_groups_controller.rb'
    - 'app/services/import_export/custom_entity/sample_xls_export.rb'
    - 'app/services/import_export/custom_entity/xls_export.rb'
    - 'app/services/pusher_actions/form_field_updated/base.rb'
    - 'app/sidekiq/integrations/gsuite_ad/sync_data_worker.rb'

# Offense count: 18
Naming/MethodParameterName:
  Exclude:
    - 'app/controllers/api/v2/asset_collections_controller.rb'
    - 'app/controllers/concerns/multi_company/general_scoping.rb'
    - 'app/models/time_spent.rb'
    - 'app/services/automated_tasks/concerns/telecom_service_matching.rb'
    - 'app/services/integrations/okta/fetch_data.rb'
    - 'app/services/integrations/slack/client.rb'
    - 'app/services/reporting/reports_service.rb'
    - 'app/sidekiq/integrations/bill/sync_data_worker.rb'
    - 'app/sidekiq/integrations/expensify/sync_data_worker.rb'
    - 'app/sidekiq/integrations/gsuite_ad/sync_data_worker.rb'
    - 'app/sidekiq/integrations/quickbooks/sync_data_worker.rb'
    - 'lib/tasks/cognito.rake'
    - 'spec/support/login_helper.rb'

# Offense count: 160
Naming/PredicateName:
  Enabled: false

# Offense count: 14
Naming/RescuedExceptionsVariableName:
  Exclude:
    - 'app/controllers/webhooks/integrations/net_suite_controller.rb'
    - 'app/controllers/webhooks/integrations/sage_intacct_controller.rb'
    - 'app/services/credit_card_service.rb'
    - 'app/services/general_transaction_service.rb'
    - 'app/services/stripe_subscription_service.rb'
    - 'app/sidekiq/verify_email_by_user_worker.rb'
    - 'lib/tasks/default_vendors.rake'

# Offense count: 28
Naming/VariableName:
  Exclude:
    - 'app/models/concerns/fiscal_year.rb'
    - 'app/models/gen_asset_attributes.rb'
    - 'app/models/helpdesk_setting.rb'
    - 'app/services/company_module/custom_forms/entity_data_for_discovered_user.rb'
    - 'app/services/company_module/custom_forms/entity_data_from_controller.rb'
    - 'app/services/help_desk/time_spent_activity_create.rb'
    - 'app/services/reporting/reports_service.rb'
    - 'app/sidekiq/integrations/gsuite_ad/sync_data_worker.rb'
    - 'lib/tasks/managed_asset_summary.rake'

# Offense count: 129
Naming/VariableNumber:
  Enabled: false

# Offense count: 3
Rails/ActionFilter:
  Exclude:
    - 'app/controllers/users/invitations_controller.rb'

# Offense count: 45
Rails/ActiveRecordCallbacksOrder:
  Enabled: false

# Offense count: 15
Rails/AddColumnIndex:
  Exclude:
    - 'db/migrate/20160720124606_change_assignment_info_model_attrs.rb'
    - 'db/migrate/20180703140932_add_deleted_at_to_credit_cards_and_accounts.rb'
    - 'db/migrate/20180713063318_add_deleted_at_to_plaid_item.rb'
    - 'db/migrate/20180716173725_move_proprties_from_general_services_to_vendors.rb'
    - 'db/migrate/20190802113214_add_config_id_to_microsoft_apps_and_users.rb'
    - 'db/migrate/20190802132923_add_config_id_to_integration_user_sources_and_app_sources.rb'
    - 'db/migrate/20190805080545_add_config_id_to_one_login_apps_and_users.rb'
    - 'db/migrate/20190821095058_add_column_to_tables.rb'
    - 'db/migrate/20211206143738_add_missing_columns_to_disc_user.rb'
    - 'db/migrate/20220810132349_add_help_ticket_reference_in_preference.rb'
    - 'db/migrate/20220927075055_add_workspace_to_company_users.rb'

# Offense count: 2
Rails/ApplicationController:
  Exclude:
    - 'app/controllers/software_migration_messages_controller.rb'
    - 'app/controllers/software_types_controller.rb'

# Offense count: 171
Rails/ApplicationRecord:
  Enabled: false

# Offense count: 64
Rails/Blank:
  Enabled: false

# Offense count: 120
Rails/BulkChangeTable:
  Enabled: false

# Offense count: 46
Rails/CreateTableWithTimestamps:
  Enabled: false

# Offense count: 460
Rails/Date:
  Enabled: false

# Offense count: 81
Rails/Delegate:
  Enabled: false

# Offense count: 2
Rails/DuplicateAssociation:
  Exclude:
    - 'app/models/contract.rb'

# Offense count: 4
Rails/DuplicateScope:
  Exclude:
    - 'app/models/telecom_provider.rb'
    - 'app/models/vendor.rb'

# Offense count: 4
Rails/DurationArithmetic:
  Exclude:
    - 'app/sidekiq/integrations/gsuite/save_apps_usage.rb'
    - 'app/sidekiq/integrations/gsuite/sync_data_worker.rb'

# Offense count: 248
Rails/DynamicFindBy:
  Enabled: false

# Offense count: 4
Rails/EagerEvaluationLogMessage:
  Exclude:
    - 'app/models/workspace.rb'
    - 'app/services/automated_tasks/task_executor.rb'

# Offense count: 66
Rails/EnumHash:
  Enabled: false

# Offense count: 42
Rails/FilePath:
  Enabled: false

# Offense count: 3
Rails/FindEach:
  Exclude:
    - 'app/models/company.rb'

# Offense count: 94
Rails/HasManyOrHasOneDependent:
  Enabled: false

# Offense count: 17
Rails/HelperInstanceVariable:
  Exclude:
    - 'app/helpers/application_helper.rb'
    - 'app/helpers/email_format_helper.rb'
    - 'app/helpers/help_tickets_helper.rb'
    - 'app/helpers/insight_helper.rb'
    - 'app/helpers/permissions_helper.rb'

# Offense count: 868
Rails/HttpStatus:
  Enabled: false

# Offense count: 61
Rails/I18nLocaleTexts:
  Enabled: false

# Offense count: 178
Rails/InverseOf:
  Enabled: false

# Offense count: 24
Rails/LexicallyScopedActionFilter:
  Enabled: false

# Offense count: 74
Rails/NegateInclude:
  Enabled: false

# Offense count: 3
Rails/OutputSafety:
  Exclude:
    - 'app/admin/feature_requests.rb'
    - 'app/helpers/devise_helper.rb'
    - 'config/initializers/form_errors.rb'

# Offense count: 2
Rails/Pick:
  Exclude:
    - 'app/models/csv_file.rb'
    - 'spec/controllers/onboarding_controller_spec.rb'

# Offense count: 49
Rails/PluralizationGrammar:
  Enabled: false

# Offense count: 161
Rails/Presence:
  Enabled: false

# Offense count: 12
Rails/Present:
  Exclude:
    - 'app/controllers/api/v1/base_tickets_controller.rb'
    - 'app/controllers/help_tickets/base_tickets_controller.rb'
    - 'app/controllers/store_accesses_controller.rb'
    - 'app/models/contract.rb'
    - 'app/models/integrations/salesforce/app.rb'
    - 'app/services/email_identity_service.rb'
    - 'app/services/import_export/assets/base.rb'
    - 'lib/paperclip/validators/attachment_size_validator.rb'
    - 'lib/tasks/automated_tasks.rake'
    - 'lib/tasks/custom_form_fields.rake'

# Offense count: 75
Rails/RedundantForeignKey:
  Enabled: false

# Offense count: 17
Rails/RedundantPresenceValidationOnBelongsTo:
  Enabled: false

# Offense count: 144
Rails/ReversibleMigration:
  Enabled: false

# Offense count: 1
Rails/RootPublicPath:
  Exclude:
    - 'app/controllers/managed_asset_images_controller.rb'

# Offense count: 3
Rails/SafeNavigationWithBlank:
  Exclude:
    - 'app/services/import_export/assets/base.rb'

# Offense count: 402
Rails/SkipsModelValidations:
  Enabled: false

# Offense count: 131
Rails/TimeZone:
  Enabled: false

# Offense count: 3
Rails/ToFormattedS:
  Exclude:
    - 'app/services/import_export/general_transactions/prepare_data_for_general_transaction.rb'
    - 'app/sidekiq/integrations/okta/create_app_usage_worker.rb'

# Offense count: 4
Rails/TransactionExitStatement:
  Exclude:
    - 'app/controllers/contract_hierarchy_controller.rb'
    - 'app/services/company_module/custom_forms/entity_create.rb'
    - 'app/sidekiq/update_expanded_privileges_worker.rb'

# Offense count: 1
Rails/UniqBeforePluck:
  Exclude:
    - 'db/migrate/20170720021102_remove_duplicate_users.rb'

# Offense count: 10
Rails/UniqueValidationWithoutIndex:
  Exclude:
    - 'app/models/category.rb'
    - 'app/models/custom_form_field_template.rb'
    - 'app/models/msp/templates/asset_type.rb'
    - 'app/models/msp/templates/category.rb'
    - 'app/models/msp/templates/document.rb'
    - 'app/models/msp/templates/group.rb'
    - 'app/models/msp/templates/snippet.rb'
    - 'app/models/snippet.rb'
    - 'app/models/telecom_provider.rb'
    - 'app/models/ticket_list_column.rb'

# Offense count: 82
Rails/UnknownEnv:
  Enabled: false

# Offense count: 147
Rails/Validation:
  Enabled: false

# Offense count: 62
Rails/WhereEquals:
  Enabled: false

# Offense count: 11
Rails/WhereExists:
  Exclude:
    - 'app/controllers/help_tickets/ticket_emails_controller.rb'
    - 'app/models/contributor.rb'
    - 'app/models/vendor.rb'
    - 'app/services/help_desk/inbound_email/ticket_email_create.rb'
    - 'app/services/pusher_actions/form_field_updated/help_ticket.rb'
    - 'app/sidekiq/inbound_email_worker.rb'

# Offense count: 11
Rails/WhereNot:
  Exclude:
    - 'app/controllers/oauth2_controller.rb'
    - 'app/models/integrations/google_assets/config.rb'
    - 'app/models/user.rb'
    - 'app/services/api_event_checker.rb'
    - 'db/migrate/20190719195739_remove_deleted_accounts_transactions_and_vendors.rb'
    - 'db/migrate/20190812162353_remove_deleted_at_from_general_services.rb'
    - 'lib/tasks/company_users.rake'

# Offense count: 2
Security/Eval:
  Exclude:
    - 'app/controllers/webhooks/integrations/helpdesk/slack/interactivity_controller.rb'
    - 'app/sidekiq/plaid_account_logo_worker.rb'

# Offense count: 6
Security/Open:
  Exclude:
    - 'app/mailers/ticket_mailer.rb'
    - 'app/services/file_manipulation_service.rb'
    - 'app/sidekiq/update_gsuite_signup_path_worker.rb'
    - 'lib/tasks/default_vendors.rake'

# Offense count: 2
Style/AccessorGrouping:
  Exclude:
    - 'app/models/user.rb'

# Offense count: 4
Style/Alias:
  Exclude:
    - 'app/models/company_user.rb'
    - 'app/models/concerns/validations.rb'
    - 'config/initializers/kaminari.rb'

# Offense count: 30
Style/AndOr:
  Enabled: false

# Offense count: 2
Style/ArgumentsForwarding:
  Exclude:
    - 'app/services/application_service.rb'

# Offense count: 14
Style/BlockComments:
  Exclude:
    - 'app/controllers/concerns/multi_company/general_scoping.rb'
    - 'app/controllers/managed_asset_images_controller.rb'
    - 'app/services/help_desk/create_mailer.rb'
    - 'app/services/pusher_actions/base.rb'
    - 'config/initializers/active_admin.rb'
    - 'config/initializers/devise.rb'
    - 'db/seeds/11_helpdesk_settings.rb'
    - 'db/seeds/14_automated_tasks.rb'
    - 'spec/services/phone_number_lookup_service_spec.rb'
    - 'spec/spec_helper.rb'
    - 'spec/sidekiq/gsuite/sync_data_worker_spec.rb'

# Offense count: 830
Style/BlockDelimiters:
  Enabled: false

# Offense count: 53
Style/CaseEquality:
  Enabled: false

# Offense count: 77
Style/CaseLikeIf:
  Enabled: false

# Offense count: 2
Style/CharacterLiteral:
  Exclude:
    - 'app/services/file_manipulation_service.rb'

# Offense count: 443
Style/ClassAndModuleChildren:
  Enabled: false

# Offense count: 39
Style/ClassEqualityComparison:
  Enabled: false

# Offense count: 2
Style/ClassMethods:
  Exclude:
    - 'app/controllers/concerns/crypt.rb'

# Offense count: 2
Style/ClassVars:
  Enabled: false

# Offense count: 3
Style/ColonMethodCall:
  Enabled: false

# Offense count: 3
  Exclude:
    - 'app/controllers/custom_form_fields_controller.rb'
    - 'app/controllers/msp/templates/field_types_for_custom_form_fields_controller.rb'
    - 'app/controllers/oauth2_controller.rb'

# Offense count: 1
Style/CombinableLoops:
  Exclude:
    - 'app/sidekiq/integrations/azure_assets/sync_data_worker.rb'

# Offense count: 14
Style/CommentAnnotation:
  Exclude:
    - 'app/controllers/api/v1/asset_collections_controller.rb'
    - 'app/controllers/api/v2/asset_collections_controller.rb'
    - 'app/controllers/api/v3/app_versions_controller.rb'
    - 'app/controllers/api/v3/base_assets_controller.rb'
    - 'app/controllers/integrations/apps_controller.rb'
    - 'app/controllers/managed_assets_controller.rb'
    - 'app/services/discovered_asset_service.rb'
    - 'app/sidekiq/integrations/azure_assets/sync_data_worker.rb'
    - 'spec/controllers/integrations/netsuite/configs_controller_spec.rb'
    - 'spec/models/integrations/netsuite/config_spec.rb'
    - 'spec/services/integrations/netsuite/fetch_data_spec.rb'
    - 'spec/sidekiq/inbound_email_worker_spec.rb'
    - 'spec/sidekiq/netsuite/sync_data_worker_spec.rb'

# Offense count: 153
Style/ConditionalAssignment:
  Enabled: false

# Offense count: 2488
Style/Documentation:
  Enabled: false

# Offense count: 5
Style/DoubleNegation:
  Exclude:
    - 'app/services/automated_tasks/subjects/ticket_form_field.rb'
    - 'app/services/base_asset_service.rb'
    - 'app/services/import_export/xls_import_base.rb'
    - 'app/sidekiq/inbound_email_worker.rb'

# Offense count: 1
Style/EachWithObject:
  Exclude:
    - 'app/services/transaction_mapping.rb'

# Offense count: 51
Style/EmptyElse:
  Enabled: false

# Offense count: 23
Style/EmptyMethod:
  Enabled: false

# Offense count: 11
Style/Encoding:
  Exclude:
    - 'app/uploaders/company_logo_uploader.rb'
    - 'app/uploaders/document_uploader.rb'
    - 'app/uploaders/message_attachment_uploader.rb'
    - 'app/uploaders/ms_teams_app_uploader.rb'
    - 'app/uploaders/staff_discovery_uploader.rb'
    - 'app/uploaders/staff_exe_uploader.rb'
    - 'app/uploaders/staff_script_uploader.rb'
    - 'app/uploaders/upload_discovery_uploader.rb'
    - 'app/uploaders/upload_exe_uploader.rb'
    - 'app/uploaders/upload_installer_uploader.rb'
    - 'app/uploaders/upload_script_uploader.rb'

# Offense count: 2
Style/ExpandPathArguments:
  Exclude:
    - 'spec/rails_helper.rb'
    - 'spec/spec_helper.rb'

# Offense count: 1
Style/ExplicitBlockArgument:
  Exclude:
    - 'app/controllers/application_controller.rb'

# Offense count: 18
Style/FetchEnvVar:
  Exclude:
    - 'app/controllers/api/v1/abbreviated_tickets_controller.rb'
    - 'config/schedule.rb'
    - 'lib/tasks/help_ticket_email.rake'
    - 'spec/controllers/authenticate_users_controller_spec.rb'
    - 'spec/controllers/integrations/azure/configs_controller_spec.rb'
    - 'spec/controllers/integrations/azure_ad_assets/configs_controller_spec.rb'
    - 'spec/controllers/integrations/kaseya/config_controller_spec.rb'
    - 'spec/controllers/integrations/ms_intune_assets/configs_controller_spec.rb'
    - 'spec/controllers/store_accesses_controller_specs.rb'
    - 'spec/services/integrations/gsuite/fetch_data_spec.rb'
    - 'spec/services/integrations/gsuite_ad/fetch_data_spec.rb'
    - 'spec/services/integrations/salesforce/fetch_data_spec.rb'

# Offense count: 9
Style/FileWrite:
  Exclude:
    - 'app/mailers/ticket_mailer.rb'
    - 'app/services/help_desk/email_attachments_create.rb'
    - 'app/services/help_desk/values_update_from_email.rb'
    - 'app/services/integrations/gsuite/fetch_data.rb'
    - 'app/services/integrations/gsuite_ad/fetch_data.rb'
    - 'app/sidekiq/integrations/gsuite/save_apps_usage.rb'
    - 'app/sidekiq/integrations/gsuite/sync_data_worker.rb'
    - 'app/sidekiq/integrations/gsuite_ad/fetch_groups_and_org.rb'
    - 'app/sidekiq/integrations/gsuite_ad/sync_data_worker.rb'

# Offense count: 4
Style/FloatDivision:
  Exclude:
    - 'app/controllers/asset_depreciation_breakdowns_controller.rb'
    - 'app/helpers/application_helper.rb'
    - 'app/services/reporting/contract/contract_data.rb'

# Offense count: 10
Style/For:
  Exclude:
    - 'app/controllers/asset_depreciation_breakdowns_controller.rb'
    - 'app/mailers/ticket_mailer.rb'
    - 'app/services/help_desk/email_attachments_create.rb'
    - 'app/services/help_desk/insight.rb'
    - 'app/services/help_desk/values_update_from_email.rb'

# Offense count: 2
Style/FormatString:
  Exclude:
    - 'app/services/reporting/formats/shared.rb'
    - 'config/initializers/uri.rb'

# Offense count: 2
Style/FormatStringToken:
  EnforcedStyle: template

# Offense count: 3363
Style/FrozenStringLiteralComment:
  Enabled: false

# Offense count: 601
Style/GuardClause:
  Enabled: false

# Offense count: 19
Style/HashAsLastArrayItem:
  Exclude:
    - 'app/admin/companies/required_approval.rb'
    - 'app/admin/company.rb'
    - 'app/admin/trials.rb'
    - 'app/controllers/application_controller.rb'
    - 'app/controllers/concerns/service_params.rb'
    - 'app/controllers/custom_forms_controller.rb'
    - 'app/controllers/groups_controller.rb'
    - 'app/controllers/help_tickets/project_tasks_controller.rb'
    - 'app/controllers/msp/templates/custom_forms_controller.rb'
    - 'app/controllers/options/group_options_controller.rb'
    - 'app/controllers/probe_configs_controller.rb'
    - 'app/services/msp/template.rb'
    - 'app/models/article.rb'

# Offense count: 40
Style/HashEachMethods:
  Enabled: false

# Offense count: 1
Style/HashLikeCase:
  Exclude:
    - 'app/controllers/general_transactions_controller.rb'

# Offense count: 1885
Style/HashSyntax:
  Enabled: false

# Offense count: 43
Style/IdenticalConditionalBranches:
  Enabled: false

# Offense count: 43
Style/IfInsideElse:
  Enabled: false

# Offense count: 552
Style/IfUnlessModifier:
  Enabled: false

# Offense count: 7
Style/IfWithBooleanLiteralBranches:
  Exclude:
    - 'app/services/import_export/contracts/contract.rb'
    - 'app/services/integrations/expensify/fetch_data.rb'
    - 'app/services/telecom_validator.rb'

# Offense count: 11
Style/InverseMethods:
  Exclude:
    - 'app/controllers/custom_forms_controller.rb'
    - 'app/controllers/integrations/ubiquiti/configs_controller.rb'
    - 'app/controllers/msp/templates/custom_forms_controller.rb'
    - 'app/models/concerns/default_tagging.rb'
    - 'app/models/contract.rb'
    - 'app/services/help_desk/abbreviated_ticket_load.rb'
    - 'app/services/integrations/expensify/fetch_data.rb'
    - 'app/services/msp/template.rb'
    - 'app/sidekiq/integrations/meraki/fetch_topologies_worker.rb'
    - 'lib/tasks/managed_asset_summary.rake'

# Offense count: 12
Style/Lambda:
  Exclude:
    - 'app/admin/company_integration.rb'
    - 'app/models/help_ticket.rb'
    - 'app/models/integrations/aws/config.rb'
    - 'app/models/integrations/aws_assets/config.rb'
    - 'app/models/user.rb'
    - 'app/models/vendor.rb'
    - 'config/routes.rb'

# Offense count: 59
Style/MethodCallWithoutArgsParentheses:
  Enabled: false

# Offense count: 440
Style/MethodDefParentheses:
  Enabled: false

# Offense count: 15
Style/MissingRespondToMissing:
  Exclude:
    - 'app/services/custom_forms/forms_query.rb'
    - 'app/services/help_desk/inbound_email/ticket_create_from_email.rb'
    - 'app/services/help_desk/inbound_email/ticket_email_create.rb'
    - 'app/services/help_desk/ticket_query/base_clause.rb'
    - 'app/services/import_export/assets/base.rb'
    - 'app/services/import_export/company_users/company_user.rb'
    - 'app/services/import_export/contracts/contract.rb'
    - 'app/services/import_export/custom_entity/entity_import.rb'
    - 'app/services/import_export/general_transactions/general_transaction.rb'
    - 'app/services/import_export/help_tickets/help_ticket.rb'
    - 'app/services/import_export/ip_addresses/ip_address.rb'
    - 'app/services/import_export/locations/location.rb'
    - 'app/services/import_export/phone_numbers/phone_number.rb'
    - 'app/services/import_export/telecom_services/telecom_service.rb'
    - 'app/services/import_export/vendors/vendor.rb'

# Offense count: 494
Style/MixinUsage:
  Enabled: false

# Offense count: 16
Style/MultilineIfModifier:
  Exclude:
    - 'app/controllers/company_users_controller.rb'
    - 'app/controllers/custom_form_values_controller.rb'
    - 'app/controllers/groups_controller.rb'
    - 'app/controllers/help_tickets/merged_tickets_controller.rb'
    - 'app/controllers/ip_addresses_controller.rb'
    - 'app/controllers/managed_assets_controller.rb'
    - 'app/controllers/phone_numbers_controller.rb'
    - 'app/models/integrations/one_login/app.rb'
    - 'app/models/workspace.rb'
    - 'app/services/help_desk/abbreviated_ticket_load.rb'
    - 'app/services/options/location_options.rb'
    - 'app/sidekiq/create_batch_transaction_worker.rb'
    - 'lib/tasks/integrations.rake'

# Offense count: 13
Style/MultipleComparison:
  Exclude:
    - 'app/models/integrations/salesforce/app.rb'
    - 'app/services/help_desk/ticket_query/status_clause.rb'
    - 'app/services/help_desk/ticket_query_old.rb'
    - 'app/services/import_export/assets/prepare_data_for_asset.rb'
    - 'app/services/import_export/common_modules/common_module.rb'
    - 'app/services/import_export/contracts/prepare_data_for_contract.rb'
    - 'app/services/import_export/custom_entity/row_data.rb'
    - 'app/services/reporting/vendor/vendor_data.rb'
    - 'lib/tasks/automated_tasks.rake'

# Offense count: 215
Style/MutableConstant:
  Enabled: false

# Offense count: 112
Style/NegatedIf:
  Enabled: false

# Offense count: 65
Style/NegatedIfElseCondition:
  Enabled: false

# Offense count: 44
Style/NestedParenthesizedCalls:
  Enabled: false

# Offense count: 2
Style/NestedTernaryOperator:
  Exclude:
    - 'app/services/import_export/import_base.rb'

# Offense count: 103
Style/Next:
  Enabled: false

# Offense count: 12
Style/NilComparison:
  Exclude:
    - 'app/admin/asset_discovery_log.rb'
    - 'app/controllers/discovered_data_services_controller.rb'
    - 'app/controllers/discovered_voice_services_controller.rb'
    - 'app/controllers/oauth2_controller.rb'
    - 'app/helpers/assets_sample_file_data_checker.rb'
    - 'app/models/user.rb'
    - 'app/services/html_to_markdown_converter.rb'
    - 'app/services/import_export/assets/base.rb'
    - 'lib/tasks/discovered_users.rake'
    - 'lib/tasks/grant_access.rake'
    - 'lib/tasks/help_ticket_activities.rake'


# Offense count: 2
Style/NumericLiteralPrefix:
  Exclude:
    - 'app/controllers/companies_controller.rb'

# Offense count: 116
Style/NumericLiterals:
  MinDigits: 16

# Offense count: 182
Style/NumericPredicate:
  Enabled: false

# Offense count: 230
Style/OpenStructUse:
  Enabled: false

# Offense count: 5
Style/OptionalArguments:
  Exclude:
    - 'app/services/help_desk/insight.rb'
    - 'app/services/import_export/xls_export_base.rb'
    - 'app/services/location/location_query.rb'
    - 'app/services/msp/template.rb'
    - 'app/services/transaction_mapping.rb'

# Offense count: 19
Style/OptionalBooleanParameter:
  Enabled: false

# Offense count: 3
Style/OrAssignment:
  Exclude:
    - 'app/controllers/companies_controller.rb'
    - 'app/controllers/concerns/ms_teams_options.rb'
    - 'app/models/contract.rb'

# Offense count: 133
Style/ParenthesesAroundCondition:
  Enabled: false

# Offense count: 135
Style/PercentLiteralDelimiters:
  Enabled: false

# Offense count: 39
Style/PerlBackrefs:
  Enabled: false

# Offense count: 80
Style/Proc:
  Enabled: false

# Offense count: 908
Style/QuotedSymbols:
  Enabled: false

# Offense count: 28
Style/RaiseArgs:
  Exclude:
    - 'app/controllers/contract_alert_dates_controller.rb'
    - 'app/mailers/application_mailer.rb'
    - 'app/mailers/ticket_mailer.rb'
    - 'app/services/integrations/azure/fetch_data.rb'
    - 'app/services/integrations/azure_assets/fetch_data.rb'
    - 'app/services/integrations/bill/fetch_data.rb'
    - 'app/services/integrations/expensify/fetch_data.rb'
    - 'app/services/integrations/meraki/fetch_data.rb'
    - 'app/services/integrations/okta/fetch_data.rb'
    - 'app/services/integrations/one_login/fetch_data.rb'
    - 'app/services/integrations/sage_accounting/fetch_data.rb'
    - 'app/services/integrations/ubiquiti/fetch_data.rb'
    - 'app/sidekiq/inbound_email_worker.rb'
    - 'app/sidekiq/integrations/azure/sync_data_worker.rb'
    - 'app/sidekiq/integrations/azure_assets/sync_data_worker.rb'

# Offense count: 3
Style/RandomWithOffset:
  Exclude:
    - 'app/services/cognito_service.rb'
    - 'lib/tasks/migrate_user.rake'
    - 'spec/factories/time_spents.rb'

# Offense count: 34
Style/RedundantArgument:
  Enabled: false

# Offense count: 41
Style/RedundantAssignment:
  Enabled: false

# Offense count: 101
Style/RedundantBegin:
  Enabled: false

# Offense count: 25
Style/RedundantCondition:
  Enabled: false

# Offense count: 2
Style/RedundantFetchBlock:
  Exclude:
    - 'config/puma.rb'

# Offense count: 2
Style/RedundantFileExtensionInRequire:
  Exclude:
    - 'app/services/help_desk/comment_create.rb'
    - 'spec/support/company_user_helper.rb'

# Offense count: 5
Style/RedundantFreeze:
  Exclude:
    - 'app/models/concerns/manufacturers.rb'

# Offense count: 9
Style/RedundantInitialize:
  Exclude:
    - 'app/services/automated_tasks/timed_task_executor.rb'
    - 'app/services/ignorable_exception.rb'
    - 'app/services/import_export/company_users/sample_xls_export.rb'
    - 'app/services/import_export/contracts/sample_xls_export.rb'
    - 'app/services/import_export/ip_addresses/sample_xls_export.rb'
    - 'app/services/import_export/phone_numbers/sample_xls_export.rb'
    - 'app/services/import_export/vendors/sample_xls_export.rb'
    - 'app/services/reporting/contract/columns.rb'
    - 'app/services/reporting/vendor/columns.rb'

# Offense count: 186
Style/RedundantInterpolation:
  Enabled: false

# Offense count: 107
Style/RedundantParentheses:
  Enabled: false

# Offense count: 2
Style/RedundantRegexpCharacterClass:
  Exclude:
    - 'app/models/user.rb'

# Offense count: 57
Style/RedundantRegexpEscape:
  Enabled: false

# Offense count: 379
Style/RedundantReturn:
  Enabled: false

# Offense count: 1036
Style/RedundantSelf:
  Enabled: false

# Offense count: 40
Style/RegexpLiteral:
  Enabled: false

# Offense count: 54
Style/RescueModifier:
  Enabled: false

# Offense count: 181
Style/RescueStandardError:
  Enabled: false

# Offense count: 62
Style/SafeNavigation:
  Enabled: false

# Offense count: 29
Style/SelfAssignment:
  Enabled: false

# Offense count: 18
Style/Semicolon:
  Enabled: false

# Offense count: 15
Style/SlicingWithRange:
  Exclude:
    - 'app/mailers/ticket_mailer.rb'
    - 'app/models/company_user.rb'
    - 'app/services/help_desk/inbound_email/ticket_email_filter.rb'
    - 'app/services/help_desk/values_update_from_email.rb'
    - 'db/migrate/20170720021102_remove_duplicate_users.rb'
    - 'db/migrate/20180511061959_add_unique_index_to_help_ticket_number.rb'
    - 'db/migrate/20181227163437_add_constraint_for_general_transaction_tags.rb'
    - 'lib/tasks/help_ticket.rake'
    - 'spec/services/email_identity_service_spec.rb'
    - 'spec/services/ticket_email_filter_spec.rb'

# Offense count: 46
Style/SoleNestedConditional:
  Enabled: false

# Offense count: 39
Style/StringConcatenation:
  Enabled: false

# Offense count: 24543
Style/StringLiterals:
  Enabled: false

# Offense count: 85
Style/StringLiteralsInInterpolation:
  Enabled: false

# Offense count: 478
Style/SymbolArray:
  EnforcedStyle: brackets

# Offense count: 69
Style/SymbolProc:
  Enabled: false

# Offense count: 1
Style/TernaryParentheses:
  Exclude:
    - 'app/services/import_export/contracts/contract.rb'

# Offense count: 917
Style/TrailingCommaInHashLiteral:
  Enabled: false

# Offense count: 21
Style/TrivialAccessors:
  Exclude:
    - 'app/controllers/linkable_links_controller.rb'
    - 'app/controllers/merge_managed_assets_controller.rb'
    - 'app/controllers/service_summaries_controller.rb'
    - 'app/mailers/application_mailer.rb'
    - 'app/services/automated_tasks/timed_task_executor.rb'
    - 'app/services/company_module/custom_forms/entity_create.rb'
    - 'app/services/help_desk/abbreviated_ticket_load.rb'
    - 'app/services/help_desk/inbound_email/ticket_email_create.rb'
    - 'app/services/help_desk/ticket_query/field_sort_clause.rb'
    - 'app/services/import_export/custom_entity/xls_export.rb'
    - 'app/services/warranty/hp_service.rb'
    - 'app/sidekiq/inbound_email_worker.rb'
    - 'app/sidekiq/integrations/azure_assets/sync_data_worker.rb'
    - 'app/sidekiq/monitoring/sync_remote_user_worker.rb'

# Offense count: 1
Style/UnlessElse:
  Exclude:
    - 'db/migrate/20180802164807_format_phone_numbers_from_data.rb'

# Offense count: 232
Style/WordArray:
  Enabled: false

# Offense count: 61
Style/ZeroLengthPredicate:
  Enabled: false

# Offense count: 2929
Layout/LineLength:
  Max: 93018
