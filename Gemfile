source 'https://rubygems.org'

git_source(:github) do |repo_name|
  repo_name = "#{repo_name}/#{repo_name}" unless repo_name.include?("/")
  "https://github.com/#{repo_name}.git"
end

# Bundle edge Rails instead: gem 'rails', github: 'rails/rails'
gem 'rails', '~> 7.0.2'
gem 'mini_portile2', '~>2.8', '>=2.8.7'

gem 'capybara', '~> 3.40'
gem 'selenium-webdriver'
gem 'webdrivers', require: false
gem 'nokogiri'
gem 'unifi'
gem 'oauth2', '~> 2.0', '>= 2.0.9'
# Use postgresql as the database for Active Record
gem 'pg', '~> 1.5', '>= 1.5.9'
gem 'creek', '~> 2.6', '>= 2.6.3'
gem 'activerecord-import', '~> 1.8', '>= 1.8.1'
# Use <PERSON><PERSON> as the app server
gem 'puma', '6.4.2'
# Use SCSS for stylesheets
gem 'sass-rails', '~> 5'
# Use Uglifier as compressor for JavaScript assets
gem 'uglifier', '~> 4.2', '>= 4.2.1'
# See https://github.com/rails/execjs#readme for more supported runtimes
# gem 'therubyracer', platforms: :ruby
gem 'faker'
# We have decided to forego jQuery
# gem 'jquery-rails'
gem 'vanilla-ujs'
gem 'intercom', '4.2.1'
gem "intercom-rails", '1.0.6'
# Turbolinks makes navigating your web application faster. Read more: https://github.com/turbolinks/turbolinks
gem 'turbolinks', '~> 5'
# Build JSON APIs with ease. Read more: https://github.com/rails/jbuilder
gem 'jbuilder', '~> 2.13'

gem 'puma-metrics'
gem 'webpacker', '5.0.0'
gem 'rails-controller-testing'
gem 'bootstrap', '~> 4.1.3'
gem 'devise', '~> 4.9', '>= 4.9.4'
gem "slim-rails"
gem 'devise_invitable', '~> 2.0', '>= 2.0.9'
gem 'fog-aws'
gem 'rubyXL', '3.3.29 '
gem 'will_paginate', '~> 3.3.1'
gem "paperclip", "~> 6.0.0"
gem 'net-ssh'
gem "mini_magick"
gem 'rest-client'
gem 'activeadmin'
gem 'formadmin'
gem 'aws-sdk', '~> 3'
gem 'carrierwave'
gem 'rubyzip'
gem 'omniauth'
gem 'whenever'
gem 'jwt'
gem 'simple_command'
gem 'rack-attack'
gem 'sidekiq', '~> 7.3', '>= 7.3.5'
gem 'sidekiq-scheduler', '~> 5.0', '>= 5.0.6'
gem 'sidekiq-limit_fetch', '~> 4.4', '>= 4.4.1'
gem 'sinatra', require: false
gem "paranoia", "~> 2.2"
gem 'pusher', '~> 1.3.1' # For real-time communication.
gem 'fcm'
gem 'oj', '~> 3.15.1'
gem 'olive_branch'
gem 'pg_search'
gem 'twilio-ruby', '~> 5.5.0'
gem 'geocoder'
gem 'ipaddress'
gem "roo", "~> 2.7.0"
gem "plaid", "~> 12.0.0"
gem "sanitize"
gem "email_reply_parser"
gem "rack-cors", require: "rack/cors"
gem 'string-similarity'
gem 'fuzzy_match'
gem 'stripe', "~> 5.26.0"
gem 'audited'
gem 'onelogin'
gem 'xeroizer', git: "https://github.com/waynerobinson/xeroizer"
gem 'oktakit', git: "https://github.com/j642d4/oktakit_v2"
gem 'google-api-client'
gem 'googleauth'
gem 'signet'
gem 'qbo_api'
gem 'wicked_pdf'
gem 'wkhtmltopdf-binary', '0.12.4'
gem 'puppeteer-ruby'
gem 'slack-ruby-client'
gem 'fix-db-schema-conflicts'
gem 'scout_apm'
gem 'newrelic_rpm', '~> 9.15'
gem 'newrelic-infinite_tracing', '~> 9.15'
gem 'clearbit'
gem 'slack_mrkdwn'
gem 'countries', '~> 4.1'
gem 'json', '2.5.1'
gem 'no_cache_control', '~> 1.0'
gem 'htmlentities', '~> 4.3', '>= 4.3.4'
gem 'dotenv-rails' # Using in rails 7 for GCP integration
gem 'active_storage_validations'
gem 'dalli', '~> 2.7', '>= 2.7.6'
gem 'rmagick'
gem 'quickchart'
gem 'grpc', '~> 1.60'
gem 'psych', '< 4'
gem 'irb', '~> 1.4', '>= 1.4.1'
gem 'shared-mime-info'
gem 'activeadmin-searchable_select'
gem 'nio4r', '~> 2.5.9' # Fixes installation issues on M1+ Apple devices
gem 'redis', '~> 4.0'

#Adds unique constraints to the sidekiq queues
gem 'sidekiq-unique-jobs', '~> 8.0', '>= 8.0.10'

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem 'tzinfo-data', platforms: [:mingw, :mswin, :x64_mingw, :jruby]
gem 'recaptcha', '~> 5.19', require: 'recaptcha/rails'
gem 'activerecord-session_store', '>= 1.1.3'

gem 'premailer-rails'

gem "mixpanel-ruby", "~> 2.2"
gem 'working_hours'
gem "digest", "~> 3.1"
gem 'truemail'
gem 'rotp'
gem 'malloc_trim'
gem 'rack-utf8_sanitizer'

group :staging, :production do
  gem 'bugsnag'
end

group :development, :test do
  # Call 'byebug' anywhere in the code to stop execution and get a debugger console
  gem 'byebug', platform: :mri
  gem 'simplecov', require: false, group: :test
  gem 'better_errors'
  gem 'pry-rails'
  gem 'traceroute'
  gem 'binding_of_caller'
  gem "rails-erd"
  gem 'factory_bot_rails'
  gem 'rspec-rails'
  gem 'timecop'
  gem 'database_cleaner'
  gem 'webmock'
  gem 'rubocop-rails', require: false
  gem 'letter_opener'
end

group :test do
  gem 'shoulda-matchers'
  gem 'shoulda-callback-matchers', '~> 1.1.1'
end

group :development do
  # Access an IRB console on exception pages or by using <%= console %> anywhere in the code.
  gem 'web-console', '>= 3.3.0'
  gem 'listen', '~> 3.0.5'
  # Spring speeds up development by keeping your application running in the background. Read more: https://github.com/rails/spring
  gem 'spring', '~> 3.0.0'
  gem 'bullet'
  gem 'active_record_doctor'
  gem 'flamegraph'
  gem 'stackprof'
  gem 'rack-mini-profiler'
  gem 'foreman', '~> 0.87.2'
end
