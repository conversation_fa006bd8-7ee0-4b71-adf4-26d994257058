class HandleBadRequest
  RETRY_LIMIT = 4

  def initialize(app)
    @app = app
  end

  def call(env)
    retries = 0

    begin
      begin
        if env['HTTP_HOST']&.match?(/localhost|secure|gogenuity/i) || is_custom_domain?(env['HTTP_HOST'])
          env['QUERY_STRING'] = parse_query_string(env['QUERY_STRING'])  
        else
          env['QUERY_STRING'] = ''
        end

        full_path = "#{env['PATH_INFO']}?#{env['QUERY_STRING']}"
        Rack::Utils.parse_nested_query(full_path)
      rescue Rack::Utils::InvalidParameterError => e
        env['PATH_INFO'] = '/'
        env['QUERY_STRING'] = ''
      end

      @app.call(env)
    rescue ActionController::BadRequest => e
      Rails.logger.error("ActionController::BadRequest rescued: #{e.message}")
      env['QUERY_STRING'] = sanitize_query_string(env['QUERY_STRING'])
      retries += 1
      retry if retries < RETRY_LIMIT
      raise e
    end
  end

  private

  def sanitize_query_string(query_string)
    query_string.gsub(/[^a-zA-Z0-9\-._~!$&'()*+,;=:@]/, '')
  end

  # TO-DO - we need to implement cache on this check
  def is_custom_domain?(domain_name)
    CustomDomain.find_by("name ilike ?", "#{domain_name}").present?
  end

  def parse_query_string(query_string)
    query_string.gsub(/%([8-9A-Fa-f][0-9A-Fa-f])/, '')
  end
end
