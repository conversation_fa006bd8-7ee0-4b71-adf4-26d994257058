# class HandleInvalidMimeType
#   RETRY_LIMIT = 1

#   def initialize(app)
#     @app = app
#   end

#   def call(env)
#     retries = 0

#     begin
#       request = ActionDispatch::Request.new(env)
#       request.formats
#     rescue ActionDispatch::Http::MimeNegotiation::InvalidType
#       if retries < RETRY_LIMIT
#         retries += 1
#         request.set_header "CONTENT_TYPE", "text/html"
#         request.set_header "HTTP_ACCEPT", "text/html"

#         retry
#       else
#         return [400, { "Content-Type" => "text/plain" }, ["Invalid Headers"]]
#       end
#     end

#     @app.call(env)
#   end
# end
