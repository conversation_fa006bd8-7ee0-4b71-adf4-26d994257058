[{"name": "Ticket Summary Prompt", "example_output": "### Summary  \nThis ticket, currently with **Low Priority** and **Project Ongoing** status, was initiated by **<PERSON>** requesting **<PERSON>** to print bin racking labels for relabeling shelves starting with row **MR01**.  \nThe ticket status has progressed through different stages, and an inquiry was made to confirm if the issue has been resolved.  \nAdditionally, the ticket was unmerged from the **Gate City & DC** workspace and assigned to the **U3PL** department.\n\n### Next Steps  \n- Confirm if the bin label request has been resolved.\n\n### Actions Taken  \n- The ticket was created via Email.  \n- The ticket was created via an Automated task.  \n- The ticket was created via an Automated task.  \n- The ticket was assigned to **<PERSON>** by a User.  \n- A User changed the ticket status from **Open** to **In Progress**.  \n- The ticket was created via an Automated task.  \n- The task description was updated by a User to ask: \"Can you confirm if this was resolved?\"  \n- The ticket was created via an Automated task.  \n- A User unmerged the ticket (ticket number **1433**, subject \"Bin Labels Needed\") from the **Gate City & DC** workspace to the **U3PL** workspace.  \n- The ticket Department was changed from **None** to **U3PL** by a User.  \n- A User changed the ticket status from **In Progress** to **Project Ongoing**.  \n- The ticket was created via an Automated task.\n", "response_format": "summary\nSummary of the ticket.\n\nnext_steps\nList of actions that need to be taken next.\n\nactions_taken\nList of actions that have already been completed on the ticket.", "model": "gpt-4.1-nano", "fields_description": "- **summary**: (String) A brief summary of the core issue and its resolution or current status.\n    - **IMPORTANT: The summary must be detailed and comprehensive, ensuring no key information is missed and everything is clearly explained so a person can easily understand the whole scenario.**\n    - **IMPORTANT: The goal is a summary that is should be detailed and also exceptionally clear and easy to comprehend, even for someone with no prior context of the ticket.**\n    - **IMPORTANT: The summary must reflect a thorough understanding of all provided comments and activities, ensuring their essence is captured and nothing significant is missed from them.**\n    - **IMPORTANT: Ensure the summary integrates key information from BOTH comments AND activities, presented in a logical sequence that makes the overall narrative easy to follow.**\n    - **IMPORTANT: Conclude the summary with a clear statement of the ticket's final resolution status (e.g., \"The issue is now resolved.\", \"The ticket remains open and is pending further action.\", \"Work on this is still in progress.\"). This helps ensure the summary's overall flow is properly understandable and conclusive.**\n    - **When incorporating information from comments or activities (especially structured activity data), rephrase it into natural, flowing language. Do not simply copy structured data or technical-looking field names.**\n    - Do NOT include the ticket number anywhere in the summary.\n    - Mention key individuals (using their names from the comments) involved if relevant to the summary.\n    - **If provided in the input, also include the ticket's subject, status, and priority in natural language within the summary.**\n- **actions_taken**: (List of strings) A list detailing ALL actions performed and ALL relevant information from comments.\n    - **ABSOLUTELY CRITICAL: You must list EVERY SINGLE activity from the 'Activities' section as a distinct action. Do not summarize, combine, or omit any activity.**\n    - **ABSOLUTELY CRITICAL: You must process EVERY SINGLE comment. If a comment describes an action taken, or provides information directly relevant to an action or the ticket's progression, it MUST be converted into a distinct descriptive item in this list. When doing so, summarize the relevant information from the comment concisely; do NOT include the exact comment body.** Do not omit any comment that contains action-related information or updates.\n    - **Each item in this list MUST be described in natural, flowing language. Ensure descriptions are detailed, explicit, and comprehensive, clearly explaining what was done or stated. Even if an activity is presented in a structured format (e.g., `user: X activity_type: Y data: Z`), you MUST rephrase this information into a human-readable sentence.**\n    - **ABSOLUTELY CRITICAL: Every item in this list MUST be a complete, natural language sentence describing who did what.**\n    - **Strive for variety in sentence structure, similar to how a human would describe a sequence of events.**\n    - **Avoid repetitive phrasing like \"User did X. User did Y. User did Z.\" Instead, use different sentence constructions (e.g., active voice, passive voice where appropriate, starting sentences differently).**\n    - **For example, instead of always saying \"[User Unknown] activity: Ticket status changed from Open to In Progress.\", the output MUST be varied like: \"User changed the ticket status from Open to In Progress.\", \"The ticket status was changed from Open to In Progress by User.\", \"Subsequently, the ticket status was updated to In Progress by a User.\"**\n    - **If a specific user name is provided (e.g., Brandon Reavis), the sentence MUST integrate this name naturally and also with variety: \"Brandon Reavis changed the ticket status from Open to In Progress.\", \"The ticket status was then changed by Brandon Reavis to Open.\"**\n    - **Never use bracketed placeholders like '[User Unknown]' or '[Contributor Unknown]' in the output; always replace them with 'User' if the specific name is not available.**\n    - Do NOT include the ticket number in any action description.\n    - Each distinct activity and each distinct piece of action-related information from a comment should be a separate string in the list. (For example, if there are 20 listed activities and 5 comments describing actions, this list should contain 25 strings).\n    - **IMPORTANT: The items in the 'actions_taken' list MUST be presented in a logical order that reflects the sequence of events or a sensible flow of information as the activities and comments are already given in order.**\n- **next_steps**: (List of strings) A list containing any pending actions or agreed-upon next steps. Each distinct step should be a separate string in the list. If the issue is resolved or no next steps are required, please indicate this by using a phrase such as 'No further action are currently required.'. **Consider BOTH comments AND activities when identifying next steps. If anyone mentions in the conversation or comments that they will perform an action later, include this as a next step.**", "additional_instructions": "analyze the input carefully and generate the detail output according to the format that is mentioned", "is_active": true}, {"name": "Action & Next Steps Ticket Brief", "example_output": "### Overview  \nCustomer **Tech Solutions Inc.** reported an inability to access the new `'Analytics Pro'` module after their recent subscription upgrade. The issue was traced to a provisioning delay.\n\n### What Was Done  \n- Support Agent `'<PERSON>'` verified the subscription upgrade and payment.  \n- Confirmed with engineering that the `'Analytics Pro'` module provisioning was stuck in the queue.  \n- Engineering team manually triggered and completed the provisioning for **Tech Solutions Inc.**.  \n- Agent `'<PERSON>'` confirmed with the customer that access to `'Analytics Pro'` is now functional.\n\n### Upcoming Tasks  \n- Monitor the automated provisioning queue for the next 24 hours to ensure no further delays.  \n- Follow up with `'Tech Solutions Inc.'` in 2 days to ensure continued satisfaction with the `'Analytics Pro'` module.\n", "response_format": "overview\nA short summary that describes the situation or issue in the ticket.\n\nwhat_was_done\nA list of actions that have already been taken to address the issue.\n\nupcoming_tasks\nA list of steps that still need to be completed or followed up on.", "model": "gpt-4.1-nano", "fields_description": "- **overview**: (String) A brief narrative (2-3 sentences) that concisely explains the core problem or request in the ticket and its general context.\n- **what_was_done**: (List of strings) A list of distinct, significant actions, steps, or communications that have already occurred in relation to the ticket. Each item should clearly state what was done and by whom if relevant.\n- **upcoming_tasks**: (List of strings) A list of pending actions, next steps, or follow-ups required by any party. If there are no upcoming tasks, please indicate this by using a phrase such as 'No upcoming tasks are currently pending.'.", "additional_instructions": "Generate a clear and concise overview of the ticket's main subject.\nDetail the key actions already performed in the 'what_was_done' list. Ensure these are distinct and meaningful steps.\nList any clear next steps or pending items in the 'upcoming_tasks' list.\nIf an action was performed by a specific person/team, mention it naturally in the 'what_was_done' items.\nThe 'overview' should set the stage for the actions and next steps.", "is_active": false}, {"name": "Status Snapshot Ticket Summary", "example_output": "### Title  \nWebsite Access Issue for **Global Corp** Resolved; Documentation Update Pending.\n\n### Resolved Items  \n- Restored website access for **Global Corp** users by clearing a stuck session.  \n- Verified login functionality with their primary contact, **Ms. <PERSON>.**\n\n### Pending Items  \n- Update internal knowledge base with details of this session clearing procedure.  \n- Schedule a review of session timeout configurations for the web server.", "response_format": "title\nA short and catchy one-line summary describing the situation.\n\nresolved_items\nA list of things that have already been completed or fixed.\n\npending_items\nA list of things that still need to be done or are awaiting action.", "model": "gpt-4.1-nano", "fields_description": "- **title**: (String) A very brief, impactful, and catchy one-line summary that captures the essence of the ticket and its current overall status (e.g., what was fixed, what's still open).\n- **details**: (Object) An object containing lists of resolved and pending items.\n  - **resolved_items**: (List of strings) A list of specific issues that have been addressed, problems solved, or questions answered within this ticket. Each item should be a concise statement of resolution.\n  - **pending_items**: (List of strings) A list of specific issues, tasks, or questions that are still outstanding or require further action. Each item should be a concise statement of what's pending. Use a phrase like 'No items are pending currently.' if nothing is pending.", "additional_instructions": "Create a very short, attention-grabbing 'title' that summarizes the ticket's main theme and overall progress.\nClearly distinguish between what has been 'resolved_items' and what remains as 'pending_items'.\nEach item in the 'resolved_items' and 'pending_items' lists should be a clear, concise description of a specific point.\nIf there are no pending items, provide an empty list for 'pending_items'.\nThe title should give an immediate sense of the ticket's status.", "is_active": false}]