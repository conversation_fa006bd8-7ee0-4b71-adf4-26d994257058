class LogFormatter
  def call(severity, time, progname, msg = '', request = nil)
    return '' if msg.blank?

    if progname.present?
      return "timestamp='#{time}' level=#{severity} progname='#{progname}' message='#{msg}'}\n"
    end

    "timestamp='#{time}' level=#{severity} source_class='#{extract_source_class}' message='#{msg}'\n"
  end

  private

  def extract_source_class
    # Extracts the source class from the caller stack trace
    caller_locations(3, 1)[0].label
  rescue
    'Unknown'
  end
end
