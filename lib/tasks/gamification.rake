namespace :gamification do
  desc "It will update gamification badges for all company users"
  task update_badges: :environment do
    CompanyUser.where.not(company_users: {granted_access_at: nil}).each do |u|
      Events::UpdateUserGamificationBadgesWorker.perform_async(u.id)
    end
  end

  desc "It will update gamification badges for all company users"
  task update: :environment do
    CompanyUser.where.not(company_users: {granted_access_at: nil}).each do |u|
      Events::UpdateUserGamificationBadgesWorker.perform_async(u.id)
    end
  end
end
