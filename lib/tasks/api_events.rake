namespace :api_events do
  desc "update responses of integrations duration in jsonb format"
  task update_integrations_duration_response: :environment do
    Logs::ApiEvent.where(api_type: 'integration_duration').find_each do |event|
      if event.response.is_a?(Float)
        event.update_columns(response: { duration: event.response.ceil })
      elsif event.response.is_a?(Hash) && event.response["duration"].is_a?(Float)
        event.update_columns(response: { duration: event.response["duration"].ceil })
      end
    end
  end
end
