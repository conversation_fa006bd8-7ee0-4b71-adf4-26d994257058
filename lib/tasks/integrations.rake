namespace :integrations do
  task google_assets_sync: :environment do
    CronLock.lock("integrations:google_assets_sync") do
      google_assets_configs = Integrations::GoogleAssets::Config.joins(company_integration: :company)
                                                                .where("companies.is_sample_company = ? AND company_integrations.active = ? AND company_integrations.is_force_stopped = ?", false, true, false)
      google_assets_configs.find_each do |google_assets_config|
        Integrations::GoogleAssets::SyncDataWorker.perform_async(google_assets_config.id, false) if can_sync?(google_assets_config, "asset_management")
      end
    end
  end

  task one_login_async: :environment do
    CronLock.lock("integrations:one_login_async") do
      one_login_configs = Integrations::OneLogin::Config.joins(company_integration: :company)
                                                        .where("companies.is_sample_company = ? AND company_integrations.active = ? AND company_integrations.is_force_stopped = ?", false, true, false)
      one_login_configs.find_each.with_index do |one_login_config, index|
        Integrations::OneLogin::SyncDataWorker.perform_in((index * 5).minute, one_login_config.id , false) if can_sync?(one_login_config, "vendor_management")
      end
    end
  end

  task quickbooks_data_sync: :environment do
    CronLock.lock("integrations:quickbooks_data_sync") do
      quicbooks_configs = Integrations::Quickbooks::Config.joins(company_integration: :company)
                                                          .where("companies.is_sample_company = ? AND company_integrations.active = ? AND company_integrations.is_force_stopped = ?", false, true, false)
      quicbooks_configs.find_each do |quickbooks_config|
        Integrations::Quickbooks::SyncDataWorker.perform_async(quickbooks_config.id, false) if can_sync?(quickbooks_config, "vendor_management")
      end
    end
  end

  task sage_accounting_data_sync: :environment do
    CronLock.lock("integrations:sage_accounting_data_sync") do
      sage_accounting_configs = Integrations::SageAccounting::Config.joins(company_integration: :company)
                                                                    .where("companies.is_sample_company = ? AND company_integrations.active = ? AND company_integrations.is_force_stopped = ?", false, true, false)
      sage_accounting_configs.find_each do |sage_accounting|
        Integrations::SageAccounting::SyncDataWorker.perform_async(sage_accounting.id, false) if can_sync?(sage_accounting, "vendor_management")
      end
    end
  end

  task gsuite_data_sync: :environment do
    CronLock.lock("integrations:gsuite_data_sync") do
      gsuite_configs = Integrations::Gsuite::Config.joins(company_integration: :company)
                                                   .where("companies.is_sample_company = ? AND company_integrations.active = ? AND company_integrations.is_force_stopped = ?", false, true, false)
      gsuite_configs.find_each do |gsuite_config|
        Integrations::Gsuite::SyncDataWorker.perform_async(gsuite_config.id, false) if can_sync?(gsuite_config, "vendor_management") && gsuite_config.refresh_token
      end
    end
  end

  task gsuite_ad_data_sync: :environment do
    CronLock.lock("integrations:gsuite_ad_data_sync") do
      gsuite_ad_configs = Integrations::GsuiteAd::Config.joins(company_integration: :company)
                                                        .where("companies.is_sample_company = ? AND company_integrations.active = ? AND company_integrations.is_force_stopped = ?", false, true, false)
      gsuite_ad_configs.find_each do |gsuite_ad_config|
        Integrations::GsuiteAd::SyncDataWorker.perform_async(
          gsuite_ad_config.id, 
          false, 
          gsuite_ad_config.group_ids, 
          gsuite_ad_config.organization_paths, 
          gsuite_ad_config.sync_all_users) if gsuite_ad_config.refresh_token && can_sync?(gsuite_ad_config)
      end
    end
  end

  task microsoft_async: :environment do
    CronLock.lock("integrations:microsoft_async") do
      microsoft_configs = Integrations::Microsoft::Config.joins(company_integration: :company)
                                                         .where("companies.is_sample_company = ? AND company_integrations.active = ? AND company_integrations.is_force_stopped = ?", false, true, false)
      microsoft_configs.find_each do |microsoft_config|
        Integrations::Microsoft::SyncDataWorker.perform_async(microsoft_config.id, false) if can_sync?(microsoft_config, "vendor_management")
      end
    end
  end

  task azure_ad_async: :environment do
    CronLock.lock("integrations:azure_ad_async") do
      azure_ad_configs = Integrations::AzureAd::Config.joins(company_integration: :company)
                                                      .where("companies.is_sample_company = ? AND company_integrations.active = ? AND company_integrations.is_force_stopped = ?", false, true, false)
      azure_ad_configs.find_each do |azure_ad_config|
        Integrations::AzureAd::SyncDataWorker.perform_async(
          azure_ad_config.id, false,
          azure_ad_config.group_ids,
          azure_ad_config.sync_all_users) if can_sync?(azure_ad_config)
      end
    end
  end

  task xero_async: :environment do
    CronLock.lock("integrations:xero_async") do
      xero_configs = Integrations::Xero::Config.joins(company_integration: :company)
                                               .where("companies.is_sample_company = ? AND company_integrations.active = ? AND company_integrations.is_force_stopped = ?", false, true, false)
      xero_configs.find_each do |xero_config|
        Integrations::Xero::SyncDataWorker.perform_async(xero_config.company_id, false) if can_sync?(xero_config, "vendor_management")
      end
    end
  end

  task expensify_async: :environment do
    CronLock.lock("integrations:expensify_async") do
      expensify_configs = Integrations::Expensify::Config.joins(company_integration: :company)
                                                         .where("companies.is_sample_company = ? AND company_integrations.active = ? AND company_integrations.is_force_stopped = ?", false, true, false)
      expensify_configs.find_each do |expensify_config|
        Integrations::Expensify::SyncDataWorker.perform_async(expensify_config.company_id, false) if can_sync?(expensify_config, "vendor_management")
      end
    end
  end

  task :meraki_async, [:is_stopped] => :environment do |t, args|
    is_stopped = args[:is_stopped] == "true"
    
    CronLock.lock("integrations:meraki_async") do
      meraki_configs = Integrations::Meraki::Config
                            .joins(company_integration: :company)
                            .where(companies: { is_sample_company: false }, company_integrations: { active: true, is_force_stopped: is_stopped })
      meraki_configs.find_each do |meraki_config|
        Integrations::Meraki::SyncDataWorker.perform_async(meraki_config.id, false) if can_sync?(meraki_config, "asset_management")
      end
    end
  end

  task ubiquiti_async: :environment do
    CronLock.lock("integrations:ubiquiti_async") do
      ubiquiti_configs = Integrations::Ubiquiti::Config.joins(company_integration: :company)
                                                       .where("companies.is_sample_company = ? AND company_integrations.active = ? AND company_integrations.is_force_stopped = ?", false, true, false)
      ubiquiti_configs.find_each do |ubiquiti_config|
        Integrations::Ubiquiti::SyncDataWorker.perform_async(ubiquiti_config.id, false) if can_sync?(ubiquiti_config, "asset_management")
      end
    end
  end

  task salesforce_async: :environment do
    CronLock.lock("integrations:salesforce_async") do
      Rake::Task["integrations:salesforce_refresh_token"].execute
      salesforce_configs = Integrations::Salesforce::Config.joins(company_integration: :company)
                                                           .where("companies.is_sample_company = ? AND company_integrations.active = ? AND company_integrations.is_force_stopped = ?", false, true, false)
      salesforce_configs.find_each do |salesforce_config|
        if salesforce_config.token != "-1"
          Integrations::Salesforce::SyncDataWorker.perform_async(salesforce_config.id, false) if can_sync?(salesforce_config, "vendor_management")
        end
      end
    end
  end

  task bill_data_sync: :environment do
    CronLock.lock("integrations:bill_data_sync") do
      bill_configs = Integrations::Bill::Config.joins(company_integration: :company)
                                               .where("companies.is_sample_company = ? AND company_integrations.active = ? AND company_integrations.is_force_stopped = ?", false, true, false)
      bill_configs.find_each do |bill_config|
        Integrations::Bill::SyncDataWorker.perform_async(bill_config.company_id, false) if can_sync?(bill_config, "vendor_management")
      end
    end
  end

  task :azure_ad_assets_async, [:is_stopped] => :environment do |t, args|
    is_stopped = args[:is_stopped] == "true"
    
    CronLock.lock("integrations:azure_ad_assets_async") do
      azure_ad_assets_configs = Integrations::AzureAdAssets::Config
                                    .joins(company_integration: :company)
                                    .where(companies: { is_sample_company: false }, company_integrations: { active: true, is_force_stopped: is_stopped })
      azure_ad_assets_configs.find_each do |azure_ad_assets_config|
        Integrations::AzureAdAssets::SyncDataWorker.perform_async(azure_ad_assets_config.id, false) if can_sync?(azure_ad_assets_config, "asset_management")
      end
    end
  end

  task azure_assets_sync: :environment do
    CronLock.lock("integrations:azure_assets_sync") do
      azure_assets_configs = Integrations::AzureAssets::Config.joins(company_integration: :company)
                                                              .where("companies.is_sample_company = ? AND company_integrations.active = ? AND company_integrations.is_force_stopped = ?", false, true, false)
      azure_assets_configs.find_each do |azure_assets_config|
        Integrations::AzureAssets::SyncDataWorker.perform_async(azure_assets_config.id, false) if can_sync?(azure_assets_config, "asset_management")
      end
    end
  end

  task :intune_async, [:is_stopped] => :environment do |t, args|
    is_stopped = args[:is_stopped] == "true"

    CronLock.lock("integrations:intune_async") do
      ms_intune_assets_configs = Integrations::MsIntuneAssets::Config
                                      .joins(company_integration: :company)
                                      .where(companies: { is_sample_company: false }, company_integrations: { active: true, is_force_stopped: is_stopped })      
      ms_intune_assets_configs.find_each do |intune_config|
        Integrations::MsIntuneAssets::SyncDataWorker.perform_async(intune_config.id, false) if can_sync?(intune_config, "asset_management")
      end
    end
  end

  task jamf_pro_async: :environment do
    CronLock.lock("integrations:jamf_pro_async") do
      jamf_pro_configs = Integrations::JamfPro::Config.joins(company_integration: :company)
                                                      .where("companies.is_sample_company = ? AND company_integrations.active = ? AND company_integrations.is_force_stopped = ?", false, true, false)
      jamf_pro_configs.find_each do |jamf_pro_config|
        Integrations::JamfPro::SyncDataWorker.perform_async(jamf_pro_config.id, false) if can_sync?(jamf_pro_config, "asset_management")
      end
    end
  end

  task google_workspace_async: :environment do
    CronLock.lock("integrations:google_workspace_async") do
      google_workspace_configs = Integrations::GoogleWorkspace::Config.joins(company_integration: :company)
                                                              .where("companies.is_sample_company = ? AND company_integrations.active = ? AND company_integrations.is_force_stopped = ?", false, true, false)
      google_workspace_configs.find_each do |google_workspace_config|
        Integrations::GoogleWorkspace::SyncDataWorker.perform_async(google_workspace_config.id, false) if can_sync?(google_workspace_config, "asset_management")
      end
    end
  end

  task salesforce_refresh_token: :environment do
    CronLock.lock("integrations:salesforce_refresh_token") do
      salesforce_configs = Integrations::Salesforce::Config.joins(:company).where("companies.is_sample_company = ?", false)
      salesforce_configs.find_each do |salesforce_config|
        next unless can_sync?(salesforce_config, "vendor_management")

        current_company = salesforce_config.company
        salesforce_service = Integrations::Salesforce::FetchData.new(current_company.id)
        refresh_token_detail = salesforce_service.refresh_token

        if refresh_token_detail["access_token"]
          salesforce_config.update!(
            token: refresh_token_detail["access_token"],
            issued_at: refresh_token_detail["issued_at"],
            skip_callbacks: true)
        else
          salesforce_config.update!(
            token: "-1",
            issued_at: "-1",
            skip_callbacks: true)

          company_integration = salesforce_config.company_integration

          error_msg = refresh_token_detail["error_description"] ? refresh_token_detail["error_description"] : "Unable to fetch access token"

          company_integration.update!(sync_status: :failed,
                                      status: false,
                                      last_synced_at: DateTime.now,
                                      error_message: error_msg,
                                      active: false)
        end
      end
    end
  end

  task kaseya_async_daily: :environment do
    if !Date.today.saturday?
      CronLock.lock("integrations:kaseya_async") do
        kaseya_configs = Integrations::Kaseya::Config.joins(company_integration: :company)
                                                     .where("companies.is_sample_company = ? AND company_integrations.active = ? AND company_integrations.is_force_stopped = ?", false, true, false)
        kaseya_configs.find_each do |kaseya_config|
          Integrations::Kaseya::SyncDataWorker.perform_async(kaseya_config.id, false) if can_sync?(kaseya_config, "asset_management")
        end
      end
    end
  end

  task kaseya_async_weekly: :environment do
    CronLock.lock("integrations:kaseya_async") do
      kaseya_configs = Integrations::Kaseya::Config.joins(company_integration: :company)
                                                   .where("companies.is_sample_company = ? AND company_integrations.active = ? AND company_integrations.is_force_stopped = ?", false, true, false)
      kaseya_configs.find_each do |kaseya_config|
        Integrations::Kaseya::SyncDataWorker.perform_async(kaseya_config.id, false, true) if can_sync?(kaseya_config, "asset_management")
      end
    end
  end

  task kandji_async: :environment do
    CronLock.lock("integrations:kandji_async") do
      kandji_configs = Integrations::Kandji::Config.joins(company_integration: :company)
                                                   .where("companies.is_sample_company = ? AND company_integrations.active = ? AND company_integrations.is_force_stopped = ?", false, true, false)
      kandji_configs.find_each do |kandji_config|
        Integrations::Kandji::SyncDataWorker.perform_async(kandji_config.id, false) if can_sync?(kandji_config, "asset_management")
      end
    end
  end

  desc "It will update okta data"
  task okta_sync_data: :environment do
    CronLock.lock("integrations:okta_sync_data") do
      okta_configs = Integrations::Okta::Config.joins(company_integration: :company)
                                               .where("companies.is_sample_company = ? AND company_integrations.active = ? AND company_integrations.is_force_stopped = ?", false, true, false)
      okta_configs.find_each.with_index do |conf, index|
        Integrations::Okta::SyncDataWorker.perform_in((index * 15).minute, conf.id, false) if can_sync?(conf, "vendor_management")
      end
    end
  end

  desc "It will update aws cloud usage data"
  task sync_aws_cloud_data: :environment do
    CronLock.lock("integrations:sync_aws_cloud_data") do
      aws_configs = Integrations::Aws::Config.joins(company_integration: :company)
                                             .where("companies.is_sample_company = ? AND company_integrations.active = ? AND company_integrations.is_force_stopped = ?", false, true, false)
      aws_configs.find_each do |conf|
        Integrations::Aws::SyncDataWorker.perform_async(conf.id, false) if can_sync?(conf, "vendor_management")
      end
    end
  end

  desc "It will update aws cloud assets"
  task aws_assets_sync: :environment do
    CronLock.lock("integrations:aws_assets_sync") do
      aws_assets_configs = Integrations::AwsAssets::Config.joins(company_integration: :company)
                                                          .where("companies.is_sample_company = ? AND company_integrations.active = ? AND company_integrations.is_force_stopped = ?", false, true, false)
      aws_assets_configs.find_each do |conf|
        Integrations::AwsAssets::SyncDataWorker.perform_async(conf.id, false) if can_sync?(conf, "asset_management")
      end
    end
  end

  desc "It will update azure cloud usage data"
  task sync_azure_cloud_data: :environment do
    CronLock.lock("integrations:sync_azure_cloud_data") do
      azure_configs = Integrations::Azure::Config.joins(company_integration: :company)
                                                 .where("companies.is_sample_company = ? AND company_integrations.active = ? AND company_integrations.is_force_stopped = ?", false, true, false)
      azure_configs.find_each do |conf|
        Integrations::Azure::SyncDataWorker.perform_async(conf.id, false) if can_sync?(conf, "vendor_management")
      end
    end
  end

  desc "delete all integrations data"
  task delete_integrations_data: :environment do
    CronLock.lock("integrations:delete_integrations_data") do
      $stdout.puts "Do you confirm to delete all integrations data for all companies? (y/n)"
      input = $stdin.gets.strip
      if input == 'y'
        ActiveRecord::Base.transaction do
          gsuite_configs = Integrations::Gsuite::Config.destroy_all
          microsoft_configs = Integrations::Microsoft::Config.destroy_all
          meraki_configs = Integrations::Meraki::Config.destroy_all
          okta_configs = Integrations::Okta::Config.destroy_all
          one_login_configs = Integrations::OneLogin::Config.destroy_all
          quickbooks_configs = Integrations::Quickbooks::Config.destroy_all
          sage_accounting_configs = Integrations::SageAccounting::Config.destroy_all
          xero_configs = Integrations::Xero::Config.destroy_all
          salesforce_configs = Integrations::Salesforce::Config.destroy_all
          ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
        rescue => e
          Rails.logger.error("Transaction failed: #{e.message}")
          ActiveRecord::Base.connection.execute "ROLLBACK"
        end
      end
    end
  end

  desc "create company integration for existing plaid accounts"
  task create_plaid_company_integration: :environment do
    CronLock.lock("integrations:create_plaid_company_integration") do
      intg = Integration.find_by(name: 'plaid')
      plaid_accounts = PlaidAccount.all
      plaid_accounts.find_each do |account|
        if account.company.present?
          CompanyIntegration.find_or_create_by!(integrable: account, integration_id: intg.id, company_id: account.company.id, status: true, sync_status: :successful)
        end
      end
    end
  end

  desc "update last synced at column"
  task populate_last_synced_at: :environment do
    company_integrations = CompanyIntegration.all
    company_integrations.find_each do |company_integration|
      company_integration.update(last_synced_at: company_integration.updated_at)
    end
  end

  desc "It will send mailer with errors from API integrations"
  task send_errors_mailer: :environment do
    IntegrationErrorsMailer.integration_errors().deliver_now!
  end

  desc "create app direct companies and their users for existing plaid companies"
  task create_app_direct_companies_and_users: :environment do
    CronLock.lock("integrations:create_app_direct_companies_and_users") do
      Company.find_each do |company|
        company.create_app_direct_company
      end

      CompanyUser.find_each do |company_user|
        company_user.create_app_direct_user
      end
    end
  end

  desc "Delete duplicate"
  task delete_duplicates: :environment do
    grouped = Integrations::Gsuite::App.all.group_by{ |model| [ model.name, model.company_id ] }
    grouped.values.each do |duplicates|
      duplicates.shift # or pop for last one
      duplicates.each{|double| double.destroy} # duplicates can now be destroyed
    end

    grouped = Integrations::Microsoft::App.all.group_by{ |model| [ model.app_display_name, model.company_id ] }
    grouped.values.each do |duplicates|
      duplicates.shift # or pop for last one
      duplicates.each{|double| double.destroy} # duplicates can now be destroyed
    end

    grouped = Integrations::Salesforce::App.all.group_by{ |model| [ model.app_name, model.company_id ] }
    grouped.values.each do |duplicates|
      duplicates.shift # or pop for last one
      duplicates.each{|double| double.destroy} # duplicates can now be destroyed
    end

    grouped = Integrations::Okta::App.all.group_by{ |model| [ model.name, model.company_id ] }
    grouped.values.each do |duplicates|
      duplicates.shift # or pop for last one
      duplicates.each{|double| double.destroy} # duplicates can now be destroyed
    end

    grouped = Integrations::OneLogin::App.all.group_by{ |model| [ model.app_name, model.company_id ] }
    grouped.values.each do |duplicates|
      duplicates.shift # or pop for last one
      duplicates.each{|double| double.destroy} # duplicates can now be destroyed
    end

    classes = ["Gsuite", "Microsoft", "Salesforce", "Okta", "OneLogin"]
    classes.each do |c|
      grouped = "Integrations::#{c}::User".constantize.all.group_by{ |model| [ model.email, model.company_id ] }
      grouped.values.each do |duplicates|
        duplicates.shift # or pop for last one
        duplicates.each{|double| double.destroy} # duplicates can now be destroyed
      end
    end

    grouped = Integrations::App.where.not(status: "linked").group_by{ |model| [ model.name, model.company_id ] }
    grouped.values.each do |duplicates|
      duplicates.shift # or pop for last one
      duplicates.each{|double| double.destroy} # duplicates can now be destroyed
    end

    grouped = Integrations::User.all.group_by{ |model| [ model.email, model.company_id ] }
    grouped.values.each do |duplicates|
      duplicates.shift # or pop for last one
      duplicates.each{|double| double.destroy} # duplicates can now be destroyed
    end
  end

  desc "Stop synced Gsuite integrations"
  task stop_integrations: :environment do
    company_integrations = CompanyIntegration.where(
      integrable_type: "Integrations::GsuiteAd::Config",
      sync_status: "successful",
      status: true
    )

    company_integrations.find_each do |integration|
      integration.update_columns(
        sync_status: :failed,
        status: false,
        last_synced_at: DateTime.now,
        error_message: "SCOPE_ERR"
      )
    end
  end

  desc 'Delete the all sage accounting config and integration except latest one'
  task delete_sage_acct_config_and_integ: :environment do
    Company.find_each do |company|
      sage_accounting_configs = Integrations::SageAccounting::Config.where(company_id: company.id)
      if sage_accounting_configs.count > 1
        latest_sage_acct_conf_id = sage_accounting_configs.order('id desc').first.id
        sage_accounting_configs.where.not(id: latest_sage_acct_conf_id).delete_all
        sage_accounting = Integration.find_by(name: 'sage_accounting')
        CompanyIntegration.where(integration_id: sage_accounting.id, company_id: company.id).where.not(integrable_id: latest_sage_acct_conf_id).delete_all
      end
    end
  end

  desc 'Ensure rescheduling of sage intacct'
  task rescheduling_sage_intacct: :environment do
    configs = Integrations::SageIntacct::Config.joins(:company_integration).where("company_integrations.active = ?", true)
    configs.find_each do |config|
      config.schedule_hotglue_job("ENABLED")
    end
  end

  desc 'Ensure rescheduling of netsuite'
  task rescheduling_netsuite: :environment do
    configs = Integrations::Netsuite::Config.joins(:company_integration).where("company_integrations.active = ?", true)
    configs.find_each do |config|
      config.schedule_hotglue_job("ENABLED")
    end
  end

  def can_sync?(config, integration_type = "")
    company = config.company
    return true if company&.has_current_free_trial?

    subscriptions = company.subscriptions.where(status: ["active", "canceled"])
                                          .where("end_date IS NULL OR end_date >= ?", Date.today)

    return true if integration_type.present? && subscriptions.exists?(module_type: ["full_platform", integration_type])

    return true if integration_type.blank? && subscriptions.exists? # For free modules
  end

  desc 'Remove hostname of discovered asset hardware detail to fix hostname history'
  task remove_kaseya_das_hardware_detail_hostname: :environment do
    DiscoveredAsset.where(source: "kaseya").find_each do |das|
      das_hardware_detail = das.discovered_assets_hardware_detail
      if das.managed_asset.present?
        asset_hardware_detail = das.managed_asset.hardware_detail
        if das_hardware_detail.try(:hostname).present? && asset_hardware_detail.try(:hostname).present? && asset_hardware_detail.hostname != das_hardware_detail.hostname
          das_hardware_detail.update_column(:hostname, nil)
        end
      else
        das_hardware_detail.update_column(:hostname, nil) if das_hardware_detail.try(:hostname).present?
      end
    end
  end
end
