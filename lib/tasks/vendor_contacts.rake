namespace :vendor_contacts do
  
  desc "It will create missing vendors and update all faulty contracts"
  task create_missing_vendors_for_contracts: :environment do
    Company.find_each do |company|
      contracts_vendor_ids = company.contracts.pluck(:vendor_id)
      company_vendor_ids = company.vendors.pluck(:id)
      faulty_vendor_ids = contracts_vendor_ids - company_vendor_ids
      category_id = company.categories.find_by(name: "Uncategorized").id

      faulty_vendor_ids.compact.each do |vendor_id|
        vendor_name = Vendor.find(vendor_id).name
        existing_vendor = company.vendors.find_by_name(vendor_name)
        faulty_contracts = company.contracts.where(vendor_id: vendor_id)

        if existing_vendor.present?
          faulty_contracts.update_all(vendor_id: existing_vendor.id)
        else
          new_vendor = Vendor.create(
            name: vendor_name,
            category_id: category_id,
            company_id: company.id,
            vendor_source: faulty_contracts.first
          )
          faulty_contracts.update_all(vendor_id: new_vendor.id)
        end
      end
    end
  end

  desc "It will change the department enum to text"
  task change_dept_enum_to_text: :environment do
    department = %w(TechSupport CustomerService Sales Billing Other)

    VendorContact.find_each do |vc|
      vc.update(department: department[vc.department.to_i].titleize) if vc.department.present?
    end
  end

  desc "It will change secondary contact into primary contact if primary contact is blank"
  task update_primary_contact: :environment do
    Vendor.where(primary_internal_contact_id: nil)
      .where.not(secondary_internal_contact_id: nil)
      .update_all("primary_internal_contact_id = secondary_internal_contact_id, secondary_internal_contact_id = NULL")
  end
end
