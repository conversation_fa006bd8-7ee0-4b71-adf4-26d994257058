namespace :asset_softwares do
  desc "Remove non-ascii unicode control codes from asset software name"
  task remove_non_ascii_unicode_control_codes_from_asset_software_name: :environment do
    AssetSoftware.where("name IS NOT NULL AND managed_asset_id IS NOT NULL AND name ~ '[^[:ascii:]]'").find_each do |as|
      next unless as.name.include_unicode_control_codes?

      puts "Software ID: #{as.id}, Name: #{as.name}"

      ascii_name = as.name.remove_unicode_control_codes
      as.update_columns(name: ascii_name)
    end
  end

  desc 'Add default name in asset softwares for nil values'
  task add_default_name_in_asset_softwares: :environment do
    AssetSoftware.where(name: nil).update_all(name: "")
  end
end
