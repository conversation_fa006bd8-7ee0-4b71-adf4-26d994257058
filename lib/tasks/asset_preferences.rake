namespace :asset_preferences do
  desc "Change Asset friendly Name"
  task change_asset_friendly_names: :environment do
    AssetPreference.find_each do |preference|
      selected_cols = preference.preference
      asset_preference_name = selected_cols.select{ |item| item["friendly_name"] == "Asset" }
      if asset_preference_name.length > 0
        asset_preference_name.each do |hash|
          asset_preference_name.each do |key , value|
            if value == "Asset"
              hash[key] = "Asset Name"
            end
          end
        end
      end
      preference.update_columns(preference: selected_cols)
    end
  end

  desc "Change status column friendly name"
  task change_asset_status_column_friendly_name: :environment do
    preference_ids = AssetPreference.where("EXISTS (SELECT 1 FROM json_array_elements(preference) obj WHERE obj->>'name' = ?)", "status").pluck(:id)
    AssetPreference.where(id: preference_ids).find_each do |asset_preference|
      updated_preference = asset_preference.preference.map do |obj|
        if obj['name'] == 'status'
          obj['friendly_name'] = 'Usage Status'
        end
        obj
      end

      asset_preference.update(preference: updated_preference)
    end
  end

  desc "Add fixed column into Asset Preferences"
  task update_asset_preferences: :environment do
    AssetPreference.find_each do |preference|
      selected_cols = preference.preference
      asset_preference_name = selected_cols.select{ |item| item["name"] == "name" }
      if asset_preference_name.length < 1
        selected_cols.insert(0, { "name"=>"name", "friendly_name"=>"Asset Name", "type"=>"string", "id"=>2, "active"=>false})
      end
      asset_preference_type = selected_cols.select{ |item| item["name"] == "asset_type" }
      if asset_preference_type.length < 1
        selected_cols.insert(0, { "name"=>"asset_type", "friendly_name"=>"Type", "id"=>1, "active"=>false})
      end
      preference.update_columns(preference: selected_cols)
    end
  end

  desc "Sort Asset according to Fix Columns"
  task sort_assets_fixed_columns: :environment do
    AssetPreference.find_each do |preference|
      selected_cols = preference.preference
      asset_preference_name = selected_cols.select{ |item| item["name"] == "name" }
      asset_preference_type = selected_cols.select{ |item| item["name"] == "asset_type" }
      selected_cols.delete(asset_preference_name[0])
      selected_cols.delete(asset_preference_type[0])
      selected_cols.insert(0, { "name"=>"name", "friendly_name"=>"Asset Name", "type"=>"string", "id"=>2, "active"=>false})
      selected_cols.insert(0, { "name"=>"asset_type", "friendly_name"=>"Type", "id"=>1, "active"=>false})
      preference.update_columns(preference: selected_cols)
    end
  end

  desc "Remove duplicate manufacturer option"
  task remove_duplicate_manufacturer_option: :environment do
    preferences = []
    AssetPreference.find_each do |pref|
      preferences << pref if pref.preference.include?({"name"=>"manufacturer", "friendly_name"=>"Manufacturer", "id"=>28, "type"=>"string", "active"=>false})
    end

    preferences.each do |pref|
      dupes = pref.preference.select{ |x| x["name"] == "manufacturer" }
      if dupes.length == 1
        pref.preference.delete({"name"=>"manufacturer", "friendly_name"=>"Manufacturer", "id"=>28, "type"=>"string", "active"=>false})
        pref.preference.push({"name"=>"manufacturer", "friendly_name"=>"Manufacturer", "id"=>14, "type"=>"string", "active"=>false})
      elsif dupes.length == 2
        duplicate_manufacturer_obj = pref.preference.select{ |x| x["id"] == 28 }
        pref.preference = pref.preference - duplicate_manufacturer_obj
      end
      pref.save!
    end
  end

  desc "Change Warranty to Warranty Status"
  task change_warranty_to_warranty_status: :environment do
    AssetPreference.find_each do |pref|
      selected_cols = pref.preference.select{ |item| item["name"] == "warranty_expiration" && item["friendly_name"] == "Warranty" }
      if selected_cols.length == 1
        selected_cols[0]['friendly_name'] = 'Warranty Status'
      end
      pref.save
    end
  end

  desc "Change Description to Notes"
  task change_description_to_notes: :environment do
    AssetPreference.find_each do |pref|
      selected_cols = pref.preference.select{ |item| item["name"] == "description" && item["friendly_name"] == "Description" }
      if selected_cols.length == 1
        selected_cols[0]['friendly_name'] = 'Notes'
      end
      pref.save!
    end
  end

  desc "Change Cost to Purchase Price"
  task change_cost_to_purchase_price: :environment do
    AssetPreference.find_each do |pref|
      selected_cols = pref.preference.select{ |item| item["name"] == "cost" && item["friendly_name"] == "Cost" }
      if selected_cols.length == 1
        selected_cols[0]['name'] = 'purchase_price'
        selected_cols[0]['friendly_name'] = 'Purchase Price'
      end
      pref.save!
    end
  end

  desc "Remove vendor_id column from preference"
  task remove_vendor_id_column: :environment do
    AssetPreference.find_each do |pref|
      pref.preference.delete_if { |element| element["name"] == "vendor_id" }
      pref.save!
    end
  end

  desc "set assets analytics preferences"
  task set_asset_analytics_preferences: :environment do
    AssetPreference.update_all('analytics_preference = preference')
  end

  desc 'Change Ip Address friendly name to IP Address'
  task capitalize_ip_fiendly_name: :environment do
    AssetPreference.find_each do |asset_preference|
      preferences_updated = false
    
      %w[preference analytics_preference].each do |preference_type|
        preferences = asset_preference.send(preference_type)
        next if preferences.blank?
    
        preferences.map! do |pref|
          if pref['friendly_name'] == 'Ip Address'
            pref['friendly_name'] = 'IP Address'
            preferences_updated = true
          end
          pref
        end
    
        asset_preference[preference_type] = preferences
      end
    
      asset_preference.save! if preferences_updated
    end
  end

  desc "Add card preference"
  task add_card_preference: :environment do
    AssetPreference.update_all(card_preference: ManagedAsset::SELECTED_CARD_DATA)
  end
end
