namespace :msp_build_elements do
  desc "copy templates data to company build"
  task copy_msp_builds_elements: :environment do
    CompanyBuild.find_each do |build|
      begin
        next unless build.build_data_set.present?
      
        process_associations(build, build.build_data_set.categories, Msp::Templates::Category, method(:copy_category))
        process_associations(build, build.build_data_set.asset_types, Msp::Templates::AssetType, method(:copy_asset_type))
        process_associations(build, build.build_data_set.responses, Msp::Templates::Snippet, method(:copy_response))
        process_associations(build, build.build_data_set.documents, Msp::Templates::Document, method(:copy_document))
        process_associations(build, build.build_data_set.faqs, Msp::Templates::HelpdeskFaq, method(:copy_faq))
        process_associations(build, build.build_data_set.blocked_keywords, Msp::Templates::BlockedKeyword, method(:copy_blocked_keyword))
        process_associations(build, build.build_data_set.automated_tasks, Msp::Templates::AutomatedTask, method(:copy_automated_tasks))
        process_associations(build, build.build_data_set.custom_forms, Msp::Templates::CustomForm, method(:copy_custom_forms))
        puts "copied data successfully for build ID: #{build.id}"
      rescue => e
        Rails.logger.warn "Issue copying build elements ID: #{build.id}. Error message: #{e.message}"
      end
    end
  end

  task create_access_groups: :environment do
    Company.where(reseller_company_id: nil, is_sample_company: false).each do |company|
      if company.has_child_companies?
        create_msp_admins_group(company)
        create_msp_helpdesk_agents_group(company)
      end
    end
  end

  desc "Rename duplicate custom_form_field names within each MSP template"
  task rename_duplicate_fields: :environment do
    Msp::Templates::CustomForm.includes(:msp_templates_custom_form_fields).find_each do |custom_form|
      puts "Processing MSP Custom Form #{custom_form.id}"
      fields = custom_form.msp_templates_custom_form_fields
      duplicates = fields.group_by(&:name).select { |_, records| records.size > 1 }
      next if duplicates.empty?

      existing_names = fields.pluck(:name).to_set
      duplicates.values.flatten.each_with_index do |record, index|
        next if index == 0

        base_name = record.name.sub(/(_\d+)$/, '')
        max_suffix = existing_names.map { |n| n.split('_').last.to_i }.max || 0
        new_name = "#{base_name}_#{max_suffix + 1}"
        new_name = "#{base_name}_#{existing_names.size + 1}" while existing_names.include?(new_name)
        existing_names.add(new_name)
        puts "Renaming #{record.name} to #{new_name}"
        record.update_columns(name: new_name)
      end
    end

    puts "Duplicate field renaming complete!"
  end
end

def create_msp_admins_group(company)
  admin_group = Group.find_by(company: company, name: 'Admins')
  admin_access_group = Msp::Templates::Group.find_or_create_by(company: company, name: 'Msp Admins')
  create_privileges(admin_access_group, admin_group, company)
  create_members(admin_access_group, admin_group)
end

def create_msp_helpdesk_agents_group(company)
  agent_group = Group.find_by(company: company, name: 'Help Desk Agents')
  agent_access_group = Msp::Templates::Group.find_or_create_by(company: company, name: 'Msp Agents')
  create_privileges(agent_access_group, agent_group, company)
  create_members(agent_access_group, agent_group)
end

def create_privileges(res, admin_group, company)
  res.clear_permissions if res.id
  privileges = []
  default_workspace_id = company.default_workspace.id

  admin_group.contributor.privileges.each do |privilege|
    if privilege.name != "HelpTicket" || (privilege.name == "HelpTicket" && privilege.workspace_id == default_workspace_id)
      privileges << Msp::Templates::GroupPrivilege.new(
        name: privilege['name'],
        msp_templates_group_id: res.id,
        permission_type: privilege['permission_type']
      )
    end
  end

  Msp::Templates::GroupPrivilege.import privileges if privileges.any?
end

def create_members(res, admin_group)
  res.clear_members if res.id
  members = []
  admin_group.group_members.each do |member|
    contributor = member.contributor
    if contributor&.contributor_type == "CompanyUser"
      members << Msp::Templates::GroupMember.new(msp_templates_group_id: res.id, company_user_id: contributor.company_user.id)
    end
  end
  Msp::Templates::GroupMember.import members if members.any?
end

def process_associations(build, association_ids, model_class, copy_method)
  association_ids.each do |assoc_id|
    record = model_class.find_by(id: assoc_id)
    next unless record

    if record.company_build_id.present?
      copy_method.call(record, build.company, build.id)
    else
      record.update(company_build_id: build.id)
    end
  end
end

def copy_custom_forms(item, company, company_build_id)
  custom_form = company.msp_templates_custom_forms.find_by(form_name: item['form_name'], company_module: item['company_module'])
  new_msp_custom_form = company.msp_templates_custom_forms.find_or_initialize_by(form_name: custom_form.form_name, company_module: custom_form["company_module"], company_build_id: company_build_id)
  new_msp_custom_form.show_in_open_portal = custom_form["show_in_open_portal"]
  clone_custom_form_fields(new_msp_custom_form, custom_form.msp_templates_custom_form_fields)
  new_msp_custom_form.save!
end

def clone_custom_form_fields(form, fields)
  msp_templates_custom_form_fields = form.msp_templates_custom_form_fields

  fields.each do |field|
    new_form_field = msp_templates_custom_form_fields.find_or_initialize_by(name: field["name"])
    new_form_field.assign_attributes(name: field["name"],
                                     field_attribute_type: field["field_attribute_type"],
                                     order_position: field["order_position"],
                                     label: field["label"],
                                     required: field["required"],
                                     note: field["note"],
                                     default_value: field["default_value"],
                                     private: field["private"],
                                     permit_view_default: field["permit_view_default"],
                                     permit_edit_default: field["permit_edit_default"],
                                     required_to_close: field["required_to_close"],
                                     list_type: field["list_type"],
                                     sort_list: field["sort_list"],
                                     audience: field["audience"],
                                     options: field["options"]
                                    )
    new_form_field.save!
    msp_templates_field_position = Msp::Templates::FieldPosition.find_or_initialize_by(
      msp_templates_custom_form_field_id: new_form_field.id
    )
    msp_templates_field_position.position = field.msp_templates_field_position["position"]
    msp_templates_field_position.save!
  end
end

def copy_automated_tasks(item, company, company_build_id)
  task = company.msp_automated_tasks.find_by(id: item[:id])
  msp_new_task = company.msp_automated_tasks.new
  msp_new_task.name = item["name"]
  msp_new_task.company_build_id = company_build_id
  creator = Msp::AutomatedTaskCreate.new(msp_new_task)
  msp_new_task = creator.create(task_json(task, company).as_json)
  msp_new_task.save!
end

def task_json(task, company)
  Msp::AutomatedTaskJsonOutput.new(company).json(task)
end

def copy_category(category, company, company_build_id)
  company.msp_categories.find_or_create_by(name: category.name, company_build_id: company_build_id)
end

def copy_asset_type(asset_type, company, company_build_id)
  company.msp_asset_types.find_or_create_by(name: asset_type.name, company_build_id: company_build_id)
end

def copy_response(response, company, company_build_id)
  company.msp_snippets.find_or_create_by(title: response.title, company_build_id: company_build_id)
end

def copy_document(document, company, company_build_id)
  new_document = company.msp_templates_documents.find_or_initialize_by(name: document.name, company_build_id: company_build_id)
  
  if document.attached_file.attached?
    attach_document_file(new_document, document)
  end
  new_document.save
end

def attach_document_file(new_document, document)
  file_url = document.attached_file.url
  return unless valid_file_url?(file_url)

  new_document.attached_file.attach(
    io: URI.parse(file_url).open,
    filename: document.attached_file.filename.to_s,
    content_type: document.attached_file.content_type
  )
end

def copy_faq(faq, company, company_build_id)
  company.msp_templates_helpdesk_faqs.create(
    question_body: faq.question_body,
    answer_body: faq.answer_body,
    category_id: faq.category_id,
    company_build_id: company_build_id
  )
end

def copy_blocked_keyword(blocked_keyword, company, company_build_id)
  company.msp_blocked_keywords.find_or_create_by(keyword: blocked_keyword.keyword, company_build_id: company_build_id)
end

def valid_file_url?(url)
  return false if url.nil?

  response = RestClient.get(url) rescue nil
  response&.code.to_s.start_with?('2')
end
