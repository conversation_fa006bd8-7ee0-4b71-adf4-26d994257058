namespace :active_storage do
  desc "Fix Blob keys to have size before filename"
  
  # Helper method to check if key ends with filename
  def correct_key_format?(parts, filename)
    parts.last&.include?(filename)
  end
  
  task fix_attachment_path: :environment do
    updated_count = 0
    
    blobs = ActiveStorage::Blob.where("key LIKE ?", "%/size%")
    
    blobs.find_each do |blob|
      puts "Processing Blob ##{blob.id}..."
    
      original_key = blob.key
      filename = blob.filename.to_s
      parts = original_key.split('/')
    
      next if correct_key_format?(parts, filename)
    
      parts.delete_if { |p| p.include?(filename) }
      size_part = parts.find { |p| p.start_with?('size') }
      parts.delete(size_part) if size_part
    
      new_key_parts = parts
      new_key_parts << size_part if size_part
      new_key_parts << filename
      new_key = new_key_parts.join('/')
    
      puts "Old key: #{original_key}"
      puts "New key: #{new_key}"
    
      blob.update!(key: new_key)
      updated_count += 1
    
    end
    
    puts "#{updated_count} blob keys updated."
  end
end
