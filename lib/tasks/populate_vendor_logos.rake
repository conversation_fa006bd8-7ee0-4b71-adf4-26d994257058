namespace :vendor_logos do
  desc "Adds missing vendors logo_url based on default vendor logo_url"
  task populate: :environment do
    DefaultVendor.where.not(logo_url: [nil, ""]).find_each do |dv|
      Vendor.where(name: dv.name, logo_url: [nil, ""]).find_each do |v|
        v.logo_url = dv.logo_url
        v.save!
      end
    end
  end

  desc "Adds and updates all vendors logo_url based on default vendor logo_url"
  task repopulate: :environment do
    $stdout.puts "This will overwrite any existing vendor logos. Are you sure you want to continue? Type 'Yes' to confirm."
    input = $stdin.gets.chomp
    raise "Rake rejected. Shutting down." unless input.downcase == "yes"

    DefaultVendor.where.not(logo_url: [nil, ""]).find_each do |dv|
      Vendor.where(name: dv.name).find_each do |v|
        v.logo_url = dv.logo_url
        v.save!
      end
    end
  end
end
