namespace :ubiquiti_controller do
  desc "Populate ubiquiti_controller from ubiquiti_configs"
  task populate_ubiquiti_controller: :environment do
    Integrations::Ubiquiti::Config.find_each do |config|
      config.ubiquiti_controllers.create(url: config[:url],
                                         username: config[:username],
                                         password: config[:password],
                                         sites: config[:sites]
                                    )
    end
  end

  desc "Update ubiquiti integration locations address"
  task update_ubiquiti_locations: :environment do
    Integrations::Ubiquiti::Config.find_each do |config|
      config.ubiquiti_controllers.each do |ubi_controller|
        sites = ubi_controller.sites
        if sites
          ubi_config = JSON.parse(ubi_controller.to_json).symbolize_keys
          client = Integrations::Ubiquiti::FetchData.new(ubi_config, false, config.company)
          response = client.list_sites
          sites.each do |site|
            location = Integrations::Location.find_by(address: site, company_id: config.company.id)
            site_detail = response.find { |s| s["name"] == site}
            if location && site_detail
              address = "#{site_detail["name"]}-#{site_detail["_id"]}"
              location.update_columns(address: address)
              sites.delete(site)
              sites.push(address)
            end
          end
        end
        ubi_controller.update_columns(sites: sites)
      end
    end
  end
end
