namespace :vendor do
  desc "It will change 'Advertising' tags to 'Business Asvertising'"
  task alter_tag_names: :environment do
    GeneralTransactionTag.where(tag: "Advertising").update_all(tag: "Business Advertising")
  end

  desc 'It will remove the trailing spaces from tags, default tags and delete the duplicate tags'
  task remove_trailing_spaces_and_delete_duplicate_tags: :environment do
    GeneralTransactionTag.where("tag like ? OR tag like ?"," %" ,"% ").find_each do |gtt|
      begin
        gtt.update_columns(tag: gtt.tag.strip)
      rescue ActiveRecord::RecordNotUnique => e
        gtt.destroy
      end
    end

    Vendor.find_each do |ven|
      updated_tags = ven.default_tags.map { |tag| tag.strip }.uniq
      ven.update_columns(default_tags: updated_tags) unless updated_tags == ven.default_tags
    end
  end
end
