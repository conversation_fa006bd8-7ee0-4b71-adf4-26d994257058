namespace :vendor do
  desc "It will create vendors from vendor names in other modules"
  task update_telecom_vendors: :environment do
    services = GeneralService.where.not(vendor_name: nil).where(vendor_id: nil)
    services.each do |service|
      begin
        vendor = Vendor.find_by(name: service.vendor_name)
        if vendor.present?
          service.update(vendor_id: vendor.id)
        end
      rescue => e
        Rails.logger.warn e.message
        next
      end
    end
  end

  desc "it will update vendors category "
  task update_vendor_category: :environment do
    Company.find_each do |company|
      category = company.categories.find_by(name: "Uncategorized")
      if category.present?
        vendors = Vendor.where(name: ["Reamaze", "Ring Central"], category_id: category.id)
        category_id = Category.find_by(name: "IT and Security").id
        vendors.update_all(category_id: category_id)
      end
    end
  end
end