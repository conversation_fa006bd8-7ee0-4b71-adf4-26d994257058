namespace :custom_surveys do
  desc "Add default & sample survey to all workspaces"
  task add_sample_surveys_to_all_workspaces: :environment do
    Workspace.all.find_each do |workspace|
      puts "Creating sample surveys for workspace with ID: #{workspace.id}"
      service = CustomSurveys::SurveyCreator.new(workspace.id)
      service.call # Create sample surveys

      custom_form_ids = workspace.custom_forms.joins(:helpdesk_custom_form).where(helpdesk_custom_forms: { collect_closing_survey: true }).pluck(:id)
      custom_form_ids = [0] if custom_form_ids.empty?
      visible = custom_form_ids != [0]

      puts "Creating default survey for workspace with ID: #{workspace.id}"
      # Create default survey
      service.create_ticket_feedback_survey('Existing Survey', custom_form_ids, true, visible)
    rescue => e
      puts "Error: #{e} while creating surveys for workspace with ID: #{workspace.id}"
    end
  end
end
