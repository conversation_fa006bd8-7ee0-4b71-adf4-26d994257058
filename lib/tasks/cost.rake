namespace :costs do
  desc "It will set default value of salvage"
  task set_default_value_salvage: :environment do
    Cost.where(salvage: nil).update_all(salvage: 0.0)
  end

  desc "It will migrate depreciation values to cost"
  task migrate_depreciation_to_cost: :environment do
    dep_data_hash = Depreciation.where(depreciation_type: 'straight_line')
                               .pluck(:id, :useful_life_in_years)
                               .to_h

    Cost.find_in_batches do |batch|
      costs_to_update = []
      batch.each do |cost|
        cost.assign_attributes(useful_life: dep_data_hash[cost.depreciation_id], purchase_price: cost.cost.to_f)
        costs_to_update << cost
      end
      Cost.upsert_all(costs_to_update.map(&:attributes), unique_by: :managed_asset_id) unless costs_to_update.empty?
    end
  end

  desc "It will migrate depreciations with type 'straight_line' to asset lifecycles and link managed assets"
  task migrate_depreciations_to_asset_lifecycles: :environment do

    Company.find_each do |company|
      company.depreciations.where(depreciation_type: 'straight_line').find_each do |depreciation|
        begin
          asset_lifecycle = AssetLifecycle.create!(
            name: depreciation.name,
            purchase_price: 0.0,
            replacement_cost: 0.0,
            useful_life: depreciation.useful_life_in_years,
            salvage: 0.0,
            lifecycle_type: 'custom',
            company_id: company.id
          )

          if asset_lifecycle.persisted?
            cost_ids = depreciation.costs.pluck(:id)
            Cost.where(id: cost_ids).update_all(asset_lifecycle_id: asset_lifecycle.id)
          end
          depreciation.destroy!
        rescue StandardError => e
          Rails.logger.error "Failed to migrate depreciation #{depreciation.id}: #{e.message}"
          Bugsnag.notify(e) if (Rails.env.production? || Rails.env.staging?)
        end
      end
    end
  end
end
