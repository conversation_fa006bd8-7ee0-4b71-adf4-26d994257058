namespace :telecom_provider do
  desc "Set vendor"
  task set_vendor: :environment do
    ids = TelecomProvider.where.not(vendor_id: nil).pluck(:id) - TelecomProvider.where.not(vendor_id: nil).joins(:vendor).pluck(:id)
    TelecomProvider.where(id: ids).find_each do |tp|
      tp.add_vendor
    end
  end

  desc "Add vendor"
  task add_vendor: :environment do
    ids = TelecomProvider.where.not(vendor_id: nil).pluck(:id) - TelecomProvider.where.not(vendor_id: nil).joins(:vendor).pluck(:id)
    ids += TelecomProvider.where(vendor_id: nil, status: "complete").pluck(:id)
    TelecomProvider.where(id: ids).find_each do |tp|
      tp.add_vendor
    end
  end

  desc "Update provider to complete for services having a discovered provider"
  task update_provider: :environment do
    ids = TelecomService.joins(:telecom_provider).where("telecom_providers.status = 2").pluck(:telecom_provider_id)
    TelecomProvider.where(id: ids).find_each do |tp|
      tp.status = :complete
      tp.save
    end
  end
end
