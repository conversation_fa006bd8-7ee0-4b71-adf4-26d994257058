namespace :alert_dates do
  desc "Shift data from contract_id field to alertable field"
  task shift_contract_id_to_alertable: :environment do
    AlertDate.where.not(contract_id: nil).each do |alert_date|
      alert_date.alertable_type = "Contract"
      alert_date.alertable_id = alert_date.contract_id
      alert_date.save(validate: false)
    end
  end

  desc "It will add default recipients to managed asset alerts"
  task add_default_recipients_to_alerts: :environment do
    alerts = AlertDate.where(alertable_type: "ManagedAsset").where("DATE(date) >= ?", Date.today)
    alerts.find_each do |alert|
      assignment_info = alert.alertable.assignment_information
      recipient_ids = [assignment_info.used_by_contributor_id, assignment_info.managed_by_contributor_id, alert.alertable.creator_id].compact
      alert.update_column(:recipients, recipient_ids) if recipient_ids.present?
    end
  end
end
