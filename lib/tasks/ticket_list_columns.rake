namespace :ticket_list_columns do
  desc "add company preferences to workspaces preferences"
  task add_company_columns_to_workspaces: :environment do
    Company.all.find_each do |company|
      ticket_list_column = company.ticket_list_columns.where(workspace_id: nil)
      default_ticket_list_column = company.default_workspace.ticket_list_column
      if ticket_list_column.present? && default_ticket_list_column.present?
          default_ticket_list_column.update_columns(columns: ticket_list_column.columns)
      end
    end
  end

  desc "update staff_list field type to people_list"
  task update_staff_list_field_type_to_people_list: :environment do
    TicketListColumn.find_each do |list|
      list.columns.each do |col|
        col['field_type'] = 'people_list' if col['field_type'] == 'staff_list'
      end
      list.save if list.changed?
    end
  end

  desc "update impacted_users field name to followers"
  task update_impacted_users_field_name_to_followers: :environment do
    TicketListColumn.find_each do |list|
      list.columns.each do |col|
        col['field_name'] = 'followers' if col['field_name'] == 'impacted_users'
      end
      list.save if list.changed?
    end
  end
end
