namespace :helpdesk_custom_email do
  
  desc "destroy custom email whose associated custom form has been deleted"
  task destroy_custom_email_with_no_helpdesk_custom_form: :environment do
    HelpdeskCustomEmail.all.find_each do |custom_email|
      if custom_email.helpdesk_custom_form.nil?
        custom_email.destroy
      end
    end
  end

  desc "convert all custom emails to lowercase"
  task convert_emails_to_lower_case: :environment do
    HelpdeskCustomEmail.where.not(email: ["", nil]).find_each do |custom_email|
      custom_email.update_column(:email, custom_email.email.downcase)
    end
  end

  desc "It will ensure all HelpdeskCustom emails are not empty string"
  task update_empty_custom_emails: :environment do
    ActiveRecord::Base.transaction do
      # Ensure all blank values are nil to accomodate nullable db index
      HelpdeskCustomEmail.where("trim(email) is null OR trim(email) = ''").update_all(email: nil)
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
    end
  end

  desc "Update verified column of custom email to sync with Aws::SES"
  task sync_custom_email_with_SES_verification_status: :environment do
    unique_emails = HelpdeskCustomEmail.where.not(email: nil).distinct.pluck(:email)

    unique_emails.each do |email|
      email_verified = EmailIdentityService.new.check_email_verification?(email)

      HelpdeskCustomEmail.where(email: email).where.not(verified: email_verified).update_all(verified: email_verified)
    end
  end
end
