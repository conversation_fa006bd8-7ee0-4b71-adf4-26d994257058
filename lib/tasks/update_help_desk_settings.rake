namespace :helpdesk do
  desc "Add helpdesk settings and update the order"
  task update_help_desk_settings: :environment do
    helpdesk_settings = [
      # Access Settings
      {
        setting_type: "allow_followers_see_tickets",
        order: 0
      },
      # Automation Settings
      {
        setting_type: "disable_automated_tasks_for_status_field_resolution_comment",
        order: 1
      },
      {
        setting_type: "allow_self_action_notification",
        order: 2
      },
      {
        setting_type: 'set_assigned_to_first_agent_respond_to_ticket',
        order: 3
      },
      # Display Settings
      {
        setting_type: "customize_ticket_number_for_help_desk",
        order: 4
      },
      # Help Center Portal Settings
      {
        setting_type: "include_workspace_in_help_center",
        order: 5
      },
      {
        setting_type: "select_new_ui_for_help_center",
        order: 6
      },
      {
        section_name: "help_center_settings",
        setting_type: "custom_logo_for_help_center",
        title: "Help Center Logo",
        description: "Uploaded a custom Help Center logo — it will display instead of your Company Logo.",
        order: 7
      },
      {
        section_name: "help_center_settings",
        setting_type: "custom_favicon_for_help_center",
        title: "Help Center Favicon",
        description: "Uploaded a custom favicon image — it will display instead of our default Genuity favicon.",
        order: 8
      },
      {
        setting_type: "bg_color_for_help_center",
        order: 9
      },
      {
        setting_type: "hyperlinks_color_for_help_center",
        order: 10
      },
      {
        setting_type: "allow_public_custom_forms_to_logged_out_users",
        order: 11
      },
      {
        setting_type: "allow_faq_page_to_logged_out_users",
        order: 12
      },
      {
        setting_type: "allow_public_articles_to_logged_out_users",
        order: 13
      },
      # Email Settings
      {
        setting_type: "allow_the_attachment_links_in_email",
        order: 14
      },
      {
        setting_type: "allow_incoming_requests",
        order: 15
      },
      {
        setting_type: "allow_non_user_to_get_update_about_their_tickets",
        order: 16
      },
      {
        setting_type: "allow_making_email_recipients_ticket_followers",
        order: 17
      },
      {
        setting_type: "only_include_last_email_response_to_ticket_comment",
        order: 18
      },
      {
        setting_type: "disable_adding_email_body_attachments_for_help_ticket_comments",
        order: 19
      },
      {
        setting_type: "response_to_closed_ticket",
        order: 20
      }
    ]

    helpdesk_settings.each do |setting|
      existing_setting = DefaultHelpdeskSetting.find_by(setting_type: setting[:setting_type])
      if existing_setting
        existing_setting.update(order: setting[:order])
        puts "Updated #{setting[:setting_type]} to order #{setting[:order]}"
      else
        DefaultHelpdeskSetting.create(setting)
        puts "Created new setting #{setting[:setting_type]} with order #{setting[:order]}"
      end
    end
  end
end
