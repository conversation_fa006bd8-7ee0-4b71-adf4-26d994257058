require 'uri'
require Rails.root.join('app', 'models', 'concerns', 'logos')

namespace :default_vendor do
  include Logos

  desc "It will add URLs to default vendors"
  task add_urls_to_default_vendors: :environment do
    DefaultVendor.all.each do |vendor|
      if vendor.url.blank?
        if vendor.name.include? "."
          url = "http://www.#{vendor.name.gsub(" ", "").downcase}"
        else
          url = "http://www.#{vendor.name.gsub(" ", "").downcase}.com"
        end
        vendor.update(url: url) if valid_url?(url, vendor.name)
      end
    end
  end

  def valid_url?(url, vendor_name)
    uri = URI.parse(url)
    uri.is_a?(URI::HTTP) && !uri.host.nil?
  rescue URI::InvalidURIError
    Rails.logger.warn("Error setting URL for default vendor: #{vendor_name}")
    false
  end

  desc "it will add logos to default vendors"
  task add_logos_to_default_vendors: :environment do
    client = Aws::S3::Client.new(
      region: Rails.application.credentials.aws[:s3][:region],
      access_key_id: Rails.application.credentials.aws[:s3][:access_key_id],
      secret_access_key: Rails.application.credentials.aws[:s3][:secret_access_key]
    )
    S3_BUCKET = Aws::S3::Resource.new(client: client).bucket('nulodgic-static-assets')

    DefaultVendor.where(logo_url: nil).find_each do |vendor|
      begin
        local_image_path = vendor.name.gsub("&", "") + ".jpg"
        remote_image_path = "images/vendor_logos/#{local_image_path}"

        if S3_BUCKET.object(remote_image_path).exists?
          vendor.logo_url = "https://s3.amazonaws.com/#{S3_BUCKET.name}/" + remote_image_path
          vendor.save!
        else
          img_url = vendor.fetch_logo
          image_file = open(img_url)
          IO.copy_stream(image_file, local_image_path)
          S3_BUCKET.object(remote_image_path).upload_file(local_image_path, { acl: 'public-read' })
          File.delete(local_image_path)
          vendor.logo_url = "https://s3.amazonaws.com/#{S3_BUCKET.name}/" + remote_image_path
          vendor.save!
        end
      rescue OpenURI::HTTPError => ex
        Rails.logger.warn("Error setting logo for default vendor: #{vendor.name}")
        next
      end
    end
  end

  desc "fix friendly names for default vendors"
  task fix_friendly_names_for_vendors: :environment do
    DefaultVendor.all.find_each do |vendor|
      vendor.friendly_name = vendor.friendly_name.uniq
      vendor.save!
    end
  end

  desc "fix default_category for default vendors"
  task fix_default_category_for_vendors: :environment do
    defaults_vendors = DefaultVendor.where(name: ["Reamaze", "Ring Central"], default_category_id: 0)
    category_id = DefaultCategory.find_by(name: "IT and Security").id
    defaults_vendors.update_all(default_category_id: category_id)
  end
end

# Default Vendors that still need logos
# 7Geese
# Backhub
# Balsamiq
# Base CRM
# Bluehost
# BlueJeans Network
# Bonusly
# CloudApp
# Clubhouse
# DataHero
# Effortless
# Elastic
# Front
# Gong
# Gorgias
# GreenCloud
# Heap
# HelpDocs
# Highrise
# HireFire
# Hiver
# Hound
# Iterate
# JIRA Agile
# Juicer
# Kajabi
# LeadPages
# Lighthouse
# LiveChat
# ngrok
# Office365
# Papertrail
# Pixelme
# Postman
# Procore Technologies
# RapidScale
# Recognize
# Rewind
# Robin
# Rocketbook
# ServInt
# Snips
# Socedo
# Spiro Technologies
# StackPath
# Stackpoint
# Statbot
# Statsbot
# Stunning
# Sumo Logic
# Symbiont
# Symphony
# Tala
# The Neat
# Timely
# TOTEM
# TouchStream
# Troops
# Upwork
# Veracode
# Vero
# Vyte
# Wave HQ
# Weaveworks
# Xactly
# Xapo
# ZEIT
# Zenefits
# Zoom Video Communications