namespace :article_privileges do
  desc "update readscoped and scoped permissions to write"
  task update_readscoped_and_scoped_permissions: :environment do
    ArticlePrivilege.where(permission_type: ["readscoped", "scoped"]).update_all(permission_type: "write")
  end

  desc "make those articles public whose workspace setting has faq public access enabled"
  task make_articles_public: :environment do
    articles_default_setting_id = DefaultHelpdeskSetting.find_by(setting_type: "allow_public_articles_to_logged_out_users").id
    Workspace.includes(:helpdesk_settings).where(helpdesk_settings: { default_helpdesk_setting_id: articles_default_setting_id, enabled: true }).find_each do |workspace|
      workspace.articles.update_all(public: true)
    end
  end
end
