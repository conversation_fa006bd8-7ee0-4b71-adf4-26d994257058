namespace :database_migrations do
  desc "move asset discovery logs data from old db to new db created during migration"
  task move_asset_discovery_logs_created_during_migration: :environment do
    two_days_ago = 2.days.ago
    two_days_new_record_ids = Logs::AssetDiscoveryLog.where("created_at >= ?", two_days_ago).pluck(:id)
    records_to_copy = AssetDiscoveryLog.where("created_at >= ?", two_days_ago)
                                       .where.not(id: two_days_new_record_ids)

    records_to_copy.each do |record|
      Logs::AssetDiscoveryLog.create!(record.attributes)
    end
  end
end
