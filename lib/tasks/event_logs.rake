namespace :helpdesk_event_logs do
  desc "move custom_form_id into entity_id for custom form event logs"
  task update_entity_id_to_custom_form_id: :environment do
    EventLog.where(module_name: 'helpdesk').where.not(custom_form_id: nil).find_each do |event_log|
      event_log.update_columns(entity_id: event_log.custom_form_id, entity_type: "CustomForm")
    end
  end

  desc "move custom_form_id into entity_id for helpdesk event logs"
  task update_helpdesk_form_id_to_entity_id: :environment do
    EventLog.where(module_name: 'helpdesk').where(custom_form_id: nil, entity_type: nil).find_each do |event_log|
      event_log.update_columns(entity_id: event_log.data["form_id"], entity_type: "CustomForm")
    end
  end

  desc "move custom_form_id into entity_id for location event logs"
  task update_location_form_id_to_entity_id: :environment do
    EventLog.where(module_name: 'location').find_each do |event_log|
      event_log.update_columns(entity_id: event_log.data["form_id"], entity_type: "Location")
    end
  end

  desc "move custom_form_id into entity_id for company_user event logs"
  task update_company_user_form_id_to_entity_id: :environment do
    EventLog.where(module_name: 'company_user').find_each do |event_log|
      event_log.update_columns(entity_id: event_log.data["form_id"], entity_type: "CompanyUser")
    end
  end
end
