namespace :contributors do
  desc "Create contributors"
  task create: :environment do
    CompanyUser.where(contributor_id: nil).find_each do |company_user|
      company_user.send(:create_contributor)
    end
  end

  desc "Create contributors of archived users"
  task create_contributor_of_archived_user: :environment do
    CompanyUser.unscoped.where(contributor_id: nil).where.not(company_id: nil).find_each do |company_user|
      company_user.send(:create_contributor)
    end
  end

  desc "Create assigned contributors from assigned to for help tickets"
  task create_ticket_assignments: :environment do
    HelpTicketActivity.unscoped.where(activity_type: 'assignment').find_each do |activity|
      previous = nil
      current = nil
      previous = CompanyUser.find_by(id: activity.data['previous_assigned_to'])
      current = CompanyUser.find_by(id: activity.data['current_assigned_to'])
      if previous || current
        previous_company_user = CompanyUser.find_by(id: previous)
        current_company_user = CompanyUser.find_by(id: current)
        activity.data = {
          previous_contributor: previous_company_user&.contributor_id,
          current_contributor: current_company_user&.contributor_id,
        }
        activity.activity_type = 'assigned_contributor'
        activity.save!
      end
    end
  end

  desc "Delete extra contributors"
  task delete_extra_contributors: :environment do
    contributors = GroupMember.unscoped.pluck(:contributor_id) + Group.unscoped.pluck(:contributor_id) + CompanyUser.unscoped.pluck(:contributor_id)
    Contributor.where.not(id: contributors.uniq).destroy_all
  end

  desc "Delete contributors have no root"
  task delete_contributor_have_no_root: :environment do
    con_ids = []
    Contributor.includes(:group, :company_user).all.each do |c|
      if c.group.nil? && CompanyUser.unscoped.find_by(contributor_id: c.id).nil?
        con_ids << c.id
      end
    end
    Contributor.where(id: con_ids).destroy_all
  end
end
