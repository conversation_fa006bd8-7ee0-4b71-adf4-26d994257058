namespace :sample_companies do
  desc "It will send events to eventing system"
  task send_events: :environment do
    Company.where(is_sample_company: true).joins(:parent_company).find_each do |company|
      parent_company_user = company.parent_company.company_users.order(:created_at).first
      first_company_user = company.company_users.find_by(user_id: parent_company_user.user_id)
      first_company_user
      company.vendors.map do |v|
        v.created_by = first_company_user
        v.event_type = "created"
        v.award_points_to_user
      end
      company.help_tickets.map do |t|
        t.created_by = first_company_user
        t.event_type = "created"
        t.award_points_to_user
      end
      company.company_users.map do |u|
        u.created_by = first_company_user
        u.event_type = "created"
        u.award_points_to_user
      end
      company.managed_assets.map do |a|
        a.created_by = first_company_user
        a.event_type = "created"
        a.award_points_to_user
      end
      company.contracts.map do |c|
        c.created_by = first_company_user
        c.event_type = "created"
        c.award_points_to_user
      end
      company.telecom_services.map do |s|
        s.created_by = first_company_user
        s.event_type = "created"
        s.award_points_to_user
      end
    end
  end

  desc "It will create sample data for telecom expenses"
  task create_sample_data_for_telecom_expense: :environment do
    companies = Company.all.where(is_sample_company: true)
    companies.each do |company|
      company.general_services.each do |gs|
        gs.price_changes.each do |price_change|
          price_change.created_at = Time.now - 4.months if price_change.created_at  > Time.now - 4.months
          price_change.cost = 4000 if price_change.cost.nil?
          price_change.company_id = company.id
          price_change.save!
        end
      end
    end
  end

  desc "It will create imported discovered assets"
  task create_all_discovered_assets: :environment do
    sample_company_id = Rails.application.credentials.sample_company[:parent_id]
    sample = Company.find sample_company_id
    companies = Company.all.where(is_sample_company: true)
    companies.each do |company|
      company.discovered_assets.destroy_all
      sample.discovered_assets.each do |discovered_asset|
        discovered_asset_dup = discovered_asset.dup
        discovered_asset_dup.company_id = nil
        company.discovered_assets << discovered_asset_dup
      end
      company.save!
    end
  end

  desc "It will create probe locations"
  task create_probe_locations: :environment do
    companies = Company.all.where(is_sample_company: true)
    companies.each do |company|
      sample.probe_locations.each do |probe_location|
        pl = probe_location.dup
        pl.company_id = nil
        company.probe_locations << pl
      end
      company.save!
    end
  end

  desc "It will delete sample companies"
  task delete_sample_companies: :environment do
    Company.where(is_sample_company: true).find_each do |company|
      p "-------------------------------------------------------"

      begin
        company.nuke_it! 
        p "Company deleted with id: #{company.id}"
      rescue Exception => e
        p "Company cannot be deleted with id: #{company.id}"
      end
    end
  end

  desc "It will create sample company"
  task create_sample_company_data: :environment do
    RecreateSampleCompanyWorker.perform_async()
  end

  desc "It will set telecom providers for services missing telecom provider"
  task set_telecom_providers_for_services: :environment do
    master_telecom_services = Company.find_by_subdomain('master-company').telecom_services
    sample_company = Company.find_by(is_sample_company: true)

    sample_company.telecom_services.where(telecom_provider_id: nil).each do |service|
      master_telecom_provider_name = master_telecom_services.find_by(name: service.name).telecom_provider.name
      service.telecom_provider = sample_company.telecom_providers.find_by(name: master_telecom_provider_name)
      service.save
    end
  end

  desc "It remove linkables for sample company users"
  task remove_sample_comp_users_linkables: :environment do
    sample_company = Company.find_by(is_sample_company: true)
    cu_linkables = Linkable.where(company_id: sample_company.id, linkable_type: "CompanyUser")
    cu_linkables.find_each do |cu_lb|
      cu_lb.destroy! if cu_lb.linkable.is_sample_company_user
    end
  end

  desc "It will create missing privilege for asset module of sample company"
  task create_sample_company_asset_privilege: :environment do
    sample_company = Company.find_by(is_sample_company: true, subdomain: 'sample')
    privileges = sample_company.admins.contributor.privileges
    asset_privilege = privileges.find_by(name: "ManagedAsset")
    privileges.create!(name: 'ManagedAsset', permission_type: 'write') unless asset_privilege
  end
end
