namespace :locations do
  desc "It will add a default avatar URL to locations that do not have an avatar present"
  task add_default_avatars: :environment do
    locations = []
    locations << Location.where(avatar_file_name: nil, default_avatar_url: nil)
    locations << Location.where(avatar_file_name: nil, default_avatar_url: "")
    locations.flatten.each do |location|
      location.update(default_avatar_url: "https://s3.amazonaws.com/nulodgic-static-assets/images/location_defaults/default#{rand(1..16)}_thumbnail.png")
    end
  end

  desc "It will strip white spaces"
  task strip_white_space: :environment do
    ActiveRecord::Base.transaction do
      Location.all.each do |location|
        begin
          location.update(name: location.name.strip)
        rescue => e
          Rails.logger.warn "Issue updating location with ID: #{location.id}. Error message: #{e.message}"
          next
        end
      end
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
    end
  end

  desc "Update locations' phone numbers and remove unwanted numbers"
  task update_location_phone_numbers: :environment do
    Location.find_each do |loc|
      phone_value = loc.custom_form_values
        .includes(:custom_form_field)
        .find_by(custom_form_fields: { name: "phone_number" })

      if phone_value.present? && phone_value.value_str == "US,1"
        if loc.phone_number.present? && loc.phone_number_country_code.present?
          phone_value.update_columns(value_str: "#{loc.phone_number_country_code},#{loc.phone_number}")
        else
          phone_value.destroy!
        end
      end
    end
  end

  desc "Delete Locations duplicate custom form values"
  task delete_location_duplicate_custom_form_values: :environment do
    Location.all.find_each do |loc|
      if loc.custom_form.present?
        loc.custom_form.custom_form_fields.each do |field|
          if field.singular?
            values = loc.custom_form_values.where(custom_form_field_id: field.id).order('created_at DESC')
            if values.count > 1
              values = values.drop(1)
              values.each do |val|
                val.destroy
              end
            end
          end
        end
      end
    end
  end

  desc "Delete locations that have empty custom form values"
  task delete_locations_with_empty_custom_form_values: :environment do
    Location.find_each.with_index(1) do |location, index|
      puts "Row Number : #{index}"
      if location.custom_form_values.empty?
        location.destroy!
        puts "Deleted location with ID #{location.id}"
      end
    end
  end
end
