namespace :quick_view_filters do
  desc 'It will add is_default attribute in quick view filters'
  task add_default_attr_in_quick_view_filters: :environment do
    QuickViewFilter.find_each do |filter|
      filters_data = filter.filters_data
      default_quick_views = ['My Active Tickets', 'All Active Tickets', 'Unassigned', 'Closed'];
      filters_data = filters_data.map do |obj|
        if default_quick_views.include?(obj['title'])
          { 
            title: obj['title'],
            name: obj['name'],
            active: obj['active'],
            id: obj['id'],
            order: obj['order'],
            is_default: true
          }
        else
          obj
        end
      end
      filter.update_columns(filters_data: filters_data)
    end
  end
end
