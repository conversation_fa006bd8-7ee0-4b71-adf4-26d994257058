namespace :custom_reports do
  desc "add custom forms to custom reports"
  task add_workspace: :environment do
    Company.all.find_each do |company|
      help_ticket_custom_reports = company.custom_reports.where(module_name: 'help_tickets', workspace_id: nil)
      help_ticket_custom_reports.update_all(workspace_id: company&.default_workspace&.id)
    end
  end

  desc "It will fix the json file format images for already downloaded json files"
  task fix_file_format: :environment do
    CustomReport.all.find_each do |custom_report|
      if (custom_report.name.split('.').last == 'json')
        custom_report.update_column(:report_content_type, 'json')
      end
    end
  end
end
