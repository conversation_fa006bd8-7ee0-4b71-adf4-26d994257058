namespace :linkables do
  desc "Create linkables"
  task create: :environment do
    CompanyUser.where(linkable_id: nil).find_each do |item|
      item.send(:create_linkable)
    end
    Group.where(linkable_id: nil).find_each do |item|
      item.send(:create_linkable)
    end
    ManagedAsset.where(linkable_id: nil).find_each do |item|
      item.send(:create_linkable)
    end
    Vendor.where(linkable_id: nil).find_each do |item|
      item.send(:create_linkable)
    end
    Contract.where(linkable_id: nil).find_each do |item|
      item.send(:create_linkable)
    end
    TelecomService.where(linkable_id: nil).find_each do |item|
      item.send(:create_linkable)
    end
    HelpTicket.where(linkable_id: nil).find_each do |item|
      item.send(:create_linkable)
    end
    Location.where(linkable_id: nil).find_each do |item|
      item.send(:create_linkable)
    end
  end

  desc "Updates incorrect names for groups' linkable"
  task update_linkable_names: :environment do
    Group.find_each do |grp|
      link = grp.linkable
      if link.name.include?("[<ActionController::Parameters")
        link.name = grp.name
        link.save!
      end
    end
  end

  desc "Update linkables for existing GeneralTransactions"
  task update_linkables: :environment do
    existing_companies_cache = {}
    linkables_to_upsert = []

    GeneralTransaction.find_each do |transaction|
      name = transaction.product_name
      company_id = transaction.company_id

      unless existing_companies_cache.key?(company_id)
        existing_companies_cache[company_id] = Company.exists?(company_id)
      end

      if existing_companies_cache[company_id]
        linkables_to_upsert << {
          linkable_type: "GeneralTransaction",
          linkable_id: transaction.id,
          name: name,
          company_id: company_id,
        }
      end
    end

    if linkables_to_upsert.any?
      Linkable.upsert_all(linkables_to_upsert, unique_by: [:linkable_id, :linkable_type])
      puts "Upserted #{linkables_to_upsert.size} Linkables"
    else
      puts "No Linkables to upsert"
    end
  end

end
