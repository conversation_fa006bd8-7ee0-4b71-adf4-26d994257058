namespace :free_trial do
  desc "It will send free trial reminder mails to company admins"
  task send_reminders: :environment do
    level = Rails.logger.level
    Rails.logger = ActiveSupport::Logger.new('log/free_trial_reminder_emails.log')
    Rails.logger.level = level
    ActiveRecord::Base.logger = Rails.logger
    CronLock.lock("free_trial:send_reminders") do
      Company.not_sample.find_each do |company|
        if (company.has_current_free_trial? && !company.has_active_subscription?)
          SendFreeTrialReminderWorker.perform_async(company['id'])
        end
      end
    end
  end
end
