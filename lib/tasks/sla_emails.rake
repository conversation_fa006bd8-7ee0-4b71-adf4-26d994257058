namespace :sla_emails do
  
  desc "Send SLA scheduled emails"
  task send_sla_scheduled_emails: :environment do
    ScheduledSlaWorker.perform_async()
  end

  desc 'Add name and change email type in sla emails'
  task add_name_and_change_email_type: :environment do
    Sla::Email.find_each do |sla_email|
      puts(sla_email.id)
      sla_email.update_column(:name, sla_email.subject)
      if sla_email.email_type == 'resolution_target_escalation'
        sla_email.update_column(:email_type, 'resolution_time_is_not_met_escalation')
      elsif sla_email.email_type == 'first_response_target_escalation'
        sla_email.update_column(:email_type, 'first_response_target_is_not_met_escalation')
      elsif sla_email.email_type == 'resolution_target_reminder'
        sla_email.update_column(:email_type, 'resolution_time_reminder')
      end
    end
  end
end
