namespace :status do
  desc "check status iof various services"
  task check_logo_status: :environment do
    CronLock.lock("status:check_logo_status", Time.zone.now.hour) do
      Checks::LogoCheck.new.check_logo_service
    end
  end

  task check_cognito_status: :environment do
    CronLock.lock("status:check_cognito_status", Time.zone.now.hour) do
      Checks::CognitoCheck.new.check_cognito_service
    end
  end

  task check_sidekiq_status: :environment do
    CronLock.lock("status:check_sidekiq_status", Time.zone.now.hour) do
      Checks::SidekiqCheck.new.check_sidekiq_service
    end
  end

  task check_hp_status: :environment do
    CronLock.lock("status:check_hp_status", Time.zone.now.hour) do
      CheckHpWarrantyWorker.perform_async
    end
  end

  task check_dell_status: :environment do
    CronLock.lock("status:check_dell_status", Time.zone.now.hour) do
      Checks::DellWarrantyCheck.new.check_dell_warranty_service
    end
  end

  task check_lenovo_status: :environment do
    CronLock.lock("status:check_lenovo_status", Time.zone.now.hour) do
      Checks::LenovoWarrantyCheck.new.check_lenovo_warranty_service
    end
  end

  task check_lenovo_warranty_v2_status: :environment do
    CronLock.lock('status:check_lenovo_warranty_v2_status', Time.zone.now.hour) do
      Checks::LenovoWarrantyV2Check.new.check_lenovo_warranty_v2_service
    end
  end

  task send_email: :environment do
    CronLock.lock("status:send_email", Time.zone.now.hour) do
      Checks::EmailCheck.new.send_status_email
    end
  end

  task check_email_status: :environment do
    CronLock.lock("status:check_email_status", Time.zone.now.hour) do
      CheckHelpTicketEmailWorker.perform_in(10.minutes)
    end
  end
end
