namespace :admin_panel do
  desc "Syncs microsoft azure products licenses information"
  task sync_microsoft_licenses_info: :environment do
    AzureLicensesDataWorker.perform_async()
  end

  desc 'Enable 2FA of app authentication for super admins'
  task enable_super_admins_mfa: :environment do
    User.super_admins.find_each do |user|
      system_user = user.company_users.first
      mfa_secret_key = ROTP::Base32.random
      system_user.update_columns(mfa_verified: true, mfa_secret_key: mfa_secret_key, required_device_verification: true, mfa_attempts_remaining: 5)
    end
  end
end
