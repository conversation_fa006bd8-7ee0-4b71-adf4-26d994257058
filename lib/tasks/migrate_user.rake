require 'aws-sdk'

namespace :migrate_user do
  desc "migrate user from local db to cognito user_pool with dummy password and force password update"
  task migrate_cognito_user: :environment do
    User.find_each do |user|
      user.password = generate_random_password
      begin
        cognito_user = CognitoService.new(user).sign_up_user
        if cognito_user
          user_sub = cognito_user.user.attributes.select{|item| item[:name] == "sub"}[0].value
          user.update_columns(guid: user_sub, encrypted_password: nil)
        end
      rescue Aws::CognitoIdentityProvider::Errors::UsernameExistsException => e
        puts "User already Exist ====> #{user.email}"
      end
    end
  end
end

def generate_random_password
  (1..8).map { (65 + rand(26)).chr }.join
end
