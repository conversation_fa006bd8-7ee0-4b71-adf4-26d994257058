namespace :task_checklists do
  desc "It will update sample checklist name and tasks for offboarding user"
  task fix_sample_onboarding_checklists: :environment do
    sample_tasks = [
      "Backup user data folders",
      "Backup user email",
      "Disable user account",
      "Remove devices from email sync if applicable",
      "Collect company's equipment if applicable",
      "Provide access to user's data to manager",
      "Verify all user's data is accessible"
    ]

    TaskChecklist.where(name: "Sample Onboarding").find_each do |task_checklist|
      task_checklist.update_column(:name, "Sample Offboarding User")
      task_checklist.tasks.each_with_index do |task, index|
        if index >= sample_tasks.count
          task.destroy
        else
          task.update_column(:description, sample_tasks[index])
        end
      end
    end
  end

  desc "It will update Onboarding User checklist name to Sample Onboarding User"
  task update_onboarding_user_checklist_name: :environment do
    TaskChecklist.where(name: "Onboarding User").update_all(name: "Sample Onboarding User")
  end

  desc "It will update checklist tasks orders which have order equal to 0"
  task update_checklists_tasks_order: :environment do
    TaskChecklist.includes(:tasks).find_each do |task_checklist|
      tasks = task_checklist.tasks
      if tasks.size > 1 && tasks.all? { |task| task.order == 0 }
        sorted_tasks = tasks.sort_by(&:created_at)

        sorted_tasks.each_with_index do |task, index|
          task.update(order: index)
        end
      end
    end
  end
end
