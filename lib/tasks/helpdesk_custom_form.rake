namespace :helpdesk_custom_form do
  
  desc "convert uppercase emails to downcase"
  task convert_emails_to_downcase: :environment do
    HelpdeskCustomForm.all.find_each do |hcf|
      if hcf.email.present? && (hcf.email =~ /[A-Z]/).present?
        hcf.update(email: hcf.email.downcase)
      end
    end
  end

  desc "It will ensure all HelpdeskCustomForm emails are unique"
  task ensure_unique_emails: :environment do
    ActiveRecord::Base.transaction do

      # Ensure all blank values are nil to accomodate nullable db index
      HelpdeskCustomForm.where("trim(email) is null OR trim(email) = ''").update_all(email: nil)
      
      # Ensure that emails that functionally match actually match, for later query (since emails ignore case and leading or trailing whitespace)
      HelpdeskCustomForm.where.not("email = lower(trim(email))").each do |form|
        form.email = form.email.downcase.strip
        form.save
      end

      # In order to sort by boolean without relying on riskier sql queries, just ensure every form has an explicit true/false value in the default column
      HelpdeskCustomForm.where(default: nil).update_all(default: false)
      
      # Group matching emails, iterate over them, and nullify the email of all but the oldest or default form, to backfill the new default email
      HelpdeskCustomForm.select("email").group("email").having("count(*) > 1").each do |f|
        HelpdeskCustomForm.where(email: f[:email]).order(default: :desc, created_at: :asc).drop(1).each do |form|
          form.email = nil
          form.save
        end
      end
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
    end
  end

  desc "It will populate default emails where nil"
  task populate_default_emails: :environment do
    # Trigger a save to allow default email to fill in
    HelpdeskCustomForm.includes(:custom_form).where("email IS NULL").each do |form|
      form.save
    end
  end

end
