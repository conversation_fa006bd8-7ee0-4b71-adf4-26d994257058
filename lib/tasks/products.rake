namespace :products do
  desc "It will update microsoft products names"
  task update_microsoft_product_names: :environment do
    Product.where(name: "Office 365 Business").update_all(name: "Microsoft 365 Apps For Business")
    Product.where(name: "Office 365 Business Premium").update_all(name: "Microsoft 365 Business Standard")
    Product.where(name: "Office 365 Business Essentials").update_all(name: "Microsoft 365 Business Basic")
    Product.where(name: "Microsoft 365 Business").update_all(name: "Microsoft 365 Business Premium")
  end
end
