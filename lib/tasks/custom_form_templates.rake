namespace :custom_form_templates do
  desc "remove it helpdesk impacted_users templates"
  task remove_it_helpdesk_impacted_users_templates: :environment do
    custom_form_field_templates = CustomFormFieldTemplate.joins(:custom_form_template)
                                                        .where(name: "impacted_users", field_attribute_type: 'people_list', custom_form_templates: { company_module: "helpdesk" })
  custom_form_field_templates.destroy_all
  end

  desc "remove remote user and admin user templates"
  task remove_remote_user_and_admin_templates: :environment do
    CustomFormTemplate.where(form_name: ['Remote User', 'Admin User']).destroy_all
  end
end
