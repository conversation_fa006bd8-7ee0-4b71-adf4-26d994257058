namespace :monitoring do
  
  desc "It will destroy all archived company users from genuity monitoring side"
  task destroy_archived_remote_users: :environment do
    CompanyUser.where(type: 'CompanyMember').deleted.find_each do |company_user|
      Monitoring::DestroyRemoteCompanyUserWorker.perform_async(company_user.guid)
    end
  end

  desc "It will update subscriptions for all companies on genuity monitoring side"
  task update_subscriptions_for_remote_companies: :environment do
    Company.find_each do |company|
      Monitoring::SyncRemoteCompanySubscriptionWorker.perform_async(company.id)
    end
  end

  desc "It will update or create all companies on genuity monitoring side"
  task update_or_create_remote_companies: :environment do
    Company.find_each do |company|
      Monitoring::SyncRemoteCompanyWorker.perform_async(company.id)
    end
  end

  desc "It will update or create all company users on genuity monitoring side"
  task update_or_create_remote_users: :environment do
    CompanyUser.where(type: 'CompanyMember').find_each do |company_user|
      Monitoring::SyncRemoteUserWorker.perform_async(company_user.company_id, company_user.guid, company_user.user_id)
    end
  end

  desc "It will setup monitoring for not monitoring ready companies"
  task run_pending_monitoring_setups: :environment do
    Company.where(monitoring_ready: false).find_each do |company|
      Monitoring::SetupRemoteCompanyWorker.perform_async(company.id)
    end
  end
end
