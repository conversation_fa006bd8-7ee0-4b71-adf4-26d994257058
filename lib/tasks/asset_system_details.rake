namespace :asset_system_details do
  desc "Update logical disk info"
  task :update_logical_disk_info => :environment do
    faulty_data = []
    SystemDetail.where(detail_category: "logical_disk_info").find_each do |sdt|
      formatted_data = calculate_disk_info(sdt.detail_data) if sdt.detail_data
      if formatted_data
        sdt.detail_data = formatted_data
        sdt.save!
      else
        faulty_data << { sytem_detail_id: sdt.id, detail_data: sdt.detail_data }
      end
    end
    puts("---------------Faulty Data-------------")
    puts(faulty_data)
  end
end

def calculate_disk_info(detail_data)
  detail_data.map do |disk|
    total_space_gb = disk["space"].to_f
    free_space_gb = free_space(disk)
    if free_space_gb
      free_space_gb = free_space_gb.split(' ').first.to_f
      used_space_gb = total_space_gb - free_space_gb
      percentage_used = (used_space_gb / total_space_gb * 100).round(2)
      percentage_available = (free_space_gb / total_space_gb * 100).round(2)

      {
        "name" => disk["name"],
        "used" => "#{used_space_gb.round(2)} GB",
        "capacity" => "#{total_space_gb.round(1)} GB",
        "available" => "#{free_space_gb.round(1)} GB",
        "percentageUsed" => "#{percentage_used}%",
        "percentageAvailable" => "#{percentage_available}%"
      }
    else
      return nil
    end
  end
end

def free_space(disk)
  disk["freeSpace"] || disk["free_space"]
end
