namespace :valid_email_extensions do
  desc "It will create dafault valid email extensions"
  task create_dafault_valid_email_extensions: :environment do
    Workspace.find_each do |workspace|
      extensions_data = [
        { category_type: :html_file_types, extensions: [".html"] },
        { category_type: :image_types, extensions: [".jpg", ".jpeg", ".png", ".gif"] },
        { category_type: :document_types, extensions: [".doc", ".xls", ".docx", ".xlsx", ".pdf", ".msg", ".txt", ".ppt", ".pptx", ".tiff", ".csv"] },
        { category_type: :video_types, extensions: [".mov", ".mp4"] },
        { category_type: :compression_types, extensions: [] },
        { category_type: :audio_types, extensions: [] },
        { category_type: :misc_types, extensions: [] }
      ]
      extensions_data.each do |extension_data|
        begin
          extension = ValidEmailExtension.find_or_initialize_by(workspace: workspace, category_type: extension_data[:category_type])
          extension.company_id = workspace.company_id
          extension.extensions = extension_data[:extensions]
          extension.save!
          puts 'extension saved successfully'
        rescue => e
          Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
          puts "Error creating valid email extensions: #{e.message}"
          next
        end
      end
    end
  end
end
