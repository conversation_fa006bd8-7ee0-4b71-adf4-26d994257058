namespace :slack_email_limit_reach do
  desc "Send a Slack alert for company emails limit reached"
  task notify_slack_email_limit_reach: :environment do
    include EmailLimit

    Company.active_companies.not_sample.find_each do |company|
      if company_sent_emails_count(company) >= company_allowed_limit
        NotifySlackEmailLimitReachWorker.perform_async(company['id'])
      end
    end
  end
end
