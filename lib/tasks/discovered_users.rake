namespace :discovered_users do
  desc "It will add discovered user sources"
  task add_discovered_user_source: :environment do
    discovered_users = DiscoveredUser.all
    discovered_users.find_each do |user|
      if user.source == nil || user.source == "Active Directory"
        source = :genuity_active_directory
      else
        source = :microsoft_active_directory
      end

      DiscoveredUserSource.find_or_create_by!(discovered_user_id: user.id, source: source)
    end
  end

  desc "It will update last sync of old discovered users"
  task add_discovered_user_last_sync: :environment do
    DiscoveredUser.all.find_each do |du|
      du.update_column(:last_synced_at, du.updated_at)
    end
  end

  desc "It will update status to ignored of old discovered users"
  task update_discovered_user_old_status: :environment do
    DiscoveredUser.where(status: "archived").update_all(status: "ignored")
  end
end
