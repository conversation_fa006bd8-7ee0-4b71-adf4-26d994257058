include ActionView::Helpers::SanitizeHelper

namespace :custom_form_values do
  desc "Save company id in custom form values"
  task save_company_to_values: :environment do
    CustomFormValue.where(company_id: nil).find_each do |value|
      value.update_columns(company_id: value.module.company_id)
    end
  end

  desc "delete custom form values have no attachments"
  task delete_nil_attachemnts_values: :environment do
    have_attachment_ids = CustomFormValue
      .includes(:custom_form_field)
      .where(custom_form_fields: { field_attribute_type: "attachment" })
      .joins(:custom_form_attachment)
      .pluck(:id)

    CustomFormValue.where.not(id: have_attachment_ids)
      .includes(:custom_form_field)
      .where(custom_form_fields: { field_attribute_type: "attachment" })
      .destroy_all
  end

  desc "convert phone numbers to new format"
  task convert_phone_numbers_to_new_format: :environment do
    CustomFormValue.includes(:custom_form_field)
      .where(custom_form_fields: { field_attribute_type: "phone" })
      .find_each do |field|
        country_code = field.phone_number_country_code || "US"
        phone_number = field.value_str
        field.update(value_str: "#{country_code},#{phone_number}")
      end
  end

  desc "Set empty subject to subject missing"
  task set_subject: :environment do
    spaces = 0.upto(50).map{|i| " "*i}
    CustomFormValue.includes(:custom_form_field)
      .where(custom_form_fields: { name: "subject" })
      .where(custom_form_values: {value_str: spaces})
      .map {|v| v.update_columns(value_str: "Subject Missing")}
  end

  desc "delete form values"
  task delete_form_values: :environment do
    class_types = {
      'people_list' => Contributor,
      'external_user' => Contributor,
      'asset_list' => ManagedAsset,
      'location_list' => Location,
      'telecom_list' => TelecomService,
      'vendor_list' => Vendor,
    }

    class_types.keys.each do |field_type|
      form_values = CustomFormValue.includes(:custom_form_field)
        .where(custom_form_fields: { field_attribute_type: field_type })
        .where(value_str: nil)

      form_values.find_each do |fv|
        fv.destroy if class_types[field_type].find_by_id(fv.value_int).nil?
      end
    end
  end

  desc "Delete form values have value_int and value_str nil"
  task delete_form_values_have_value_int_and_value_str_nil: :environment do
    CustomFormValue.includes(:custom_form_field)
    .where(custom_form_fields: { field_attribute_type: ["asset_list", "people_list", "location_list", "contract_list", "vendor_list", "telecom_list"]})
    .where(value_str: nil, value_int: nil).destroy_all
  end

  desc "Remove trailing space from external_user email"
  task remove_trailing_space_from_external_user_email: :environment do
    cfv = CustomFormValue.includes(:custom_form_field)
      .where(custom_form_fields: { field_attribute_type: "external_user" })
      cfv.find_each do |field|
        created_by = field.value_str
        field.update_columns(value_str: "#{created_by.strip}") if created_by && created_by.include?(' ')
      end
  end

  desc "save location in custom form values"
  task save_location_to_values: :environment do
    Company.find_each do |company|
      form = company.custom_forms.find_by(form_name: "Base Location")
      if form.present?
        company.locations.each do |location|
          if location.custom_form_id.nil?
            location.custom_form_id = form.id
            form.custom_form_fields.each do |custom_form_field|
              if custom_form_field.name == "phone_number"
                location.custom_form_values.find_or_initialize_by(
                  custom_form_field_id: custom_form_field.id,
                  custom_form_id: form.id,
                  company_id: company.id,
                  module_id: location.id,
                  module_type: "Location",
                  value_str: "#{location.phone_number_country_code},#{location.phone_number}"
                )
              elsif custom_form_field.name == "location_avatar"
                location.custom_form_values.find_or_initialize_by(
                  custom_form_field_id: custom_form_field.id,
                  custom_form_id: form.id,
                  company_id: company.id,
                  module_id: location.id,
                  module_type: "Location"
                )
              elsif custom_form_field.name == "address_2"
                location.custom_form_values.find_or_initialize_by(
                  custom_form_field_id: custom_form_field.id,
                  custom_form_id: form.id,
                  company_id: company.id,
                  module_id: location.id,
                  module_type: "Location",
                  value_str: location.address2
                )
              else
                location.custom_form_values.find_or_initialize_by(
                  custom_form_field_id: custom_form_field.id,
                  custom_form_id: form.id,
                  company_id: company.id,
                  module_id: location.id,
                  module_type: "Location",
                  value_str: location.read_attribute(custom_form_field.name)
                )
              end
            end
            location.save!
          end
        end
      end
    end
  end

  desc "save location avatar in custom form attachments"
  task save_location_avatar_to_attachments: :environment do
    Location.where.not(avatar_file_name: nil).each do |location|
      if location.avatar.present? && location.avatar.url.present?
        CustomFormAttachment.create(
          custom_form_value_id: location.location_avatar.id,
          company_id: location.company_id,
          attachment: location.avatar,
          attachment_content_type: location.avatar_content_type,
          attachment_file_size: location.avatar_file_size
        )
      end
    end
  end

  desc "Update value_str data into value_int for smart lists"
  task update_value_from_string_to_integer: :environment do
    cfvs = CustomFormValue.includes(:custom_form_field).where(custom_form_fields: { field_attribute_type: ["asset_list", "people_list", "location_list", "contract_list", "vendor_list", "telecom_list"]})
    cfvs.find_each do |cfv|
      if cfv.value_int.nil? && cfv.value_str.present?
        parsed_values = JSON.parse(cfv.value_str)
        if parsed_values.present?
          parsed_values.each do |pv|
            CustomFormValue.find_or_create_by!(company_id: cfv.company_id, custom_form_field_id: cfv.custom_form_field_id, value_int: pv, module_id: cfv.module_id, module_type: "HelpTicket")
          end
        cfv.destroy!
        end
      end
    end
  end

  desc "destroy custom form values for smart list, where value_str is populated wrongly"
  task destroy_values_for_wrong_value_str_population: :environment do
    cfvs = CustomFormValue.includes(:custom_form_field).where(custom_form_fields: { field_attribute_type: ["asset_list", "people_list", "location_list", "contract_list", "vendor_list", "telecom_list"]})
    cfvs.find_each do |cfv|
      if cfv.value_int.nil? && cfv.value_str.present?
        cfv.destroy!
      end
    end
  end

  desc "set custom form in CFV"
  task set_form: :environment do
    CustomFormValue.where(custom_form_id: nil).find_each do |cfv|
      cfv.update_columns(custom_form_id: cfv.custom_form_field.custom_form_id)
    end
  end

  desc "save company user in custom form values"
  task save_company_user_to_values: :environment do
    Company.find_each do |company|
      form = company.custom_forms.find_by(form_name: "Teammates")
      if form.present?
        form_fields = form.custom_form_fields.where.not("name IN ('first_name', 'last_name', 'email')")
        company.company_users.find_each do |cu|
          cu.custom_form_id = form.id
          form_fields.each do |custom_form_field|
            if custom_form_field.name == "mobile_phone" && cu.mobile_phone_country_code.present? && cu['mobile_phone'].present?
              val = "#{cu.mobile_phone_country_code},#{cu['mobile_phone']}"
            elsif custom_form_field.name == "work_phone" && cu.work_phone_country_code.present? && cu.work_phone.present?
              val = "#{cu.mobile_phone_country_code},#{cu['mobile_phone']}"
            elsif custom_form_field.name == "title" && cu.title.present?
              val = cu.title
            elsif custom_form_field.name == "department" && cu.department.present?
              val = cu.department
            elsif custom_form_field.name == "location" && cu.location_id.present?
              val = cu.location_id
            elsif custom_form_field.name == "supervisor" && cu.supervisor.present? && cu.supervisor.contributor_id.present?
              val = cu.supervisor.contributor_id
            elsif custom_form_field.name == "user_avatar"
              val = ""
            else
              val = cu.read_attribute(custom_form_field.name)
            end

            if val.present?
              if custom_form_field.field_attribute_type == "people_list" || custom_form_field.field_attribute_type == "location_list" 
                cu.custom_form_values.find_or_initialize_by(
                  custom_form_field_id: custom_form_field.id,
                  custom_form_id: form.id,
                  company_id: company.id,
                  module_id: cu.id,
                  module_type: "CompanyUser",
                  value_int: val
                )
              else
                cu.custom_form_values.find_or_initialize_by(
                  custom_form_field_id: custom_form_field.id,
                  custom_form_id: form.id,
                  company_id: company.id,
                  module_id: cu.id,
                  module_type: "CompanyUser",
                  value_str: val
                )
              end
            elsif custom_form_field.name == "user_avatar"
              cu.custom_form_values.find_or_initialize_by(
                custom_form_field_id: custom_form_field.id,
                custom_form_id: form.id,
                company_id: company.id,
                module_id: cu.id,
                module_type: "CompanyUser"
              )
            end
          end
          cu.save!
        end
      end
    end
  end

  desc "save user avatar in custom form attachments"
  task save_user_avatar_to_attachments: :environment do
    User.where.not(avatar_file_name: nil).find_each do |user|
      if user.company_users.present?
        user.company_users.find_each do |cu|
          if user.avatar.present? && user.avatar.url.present? && cu.user_avatar.present?
            existing_attachments = CustomFormAttachment.where(
                                    custom_form_value_id: cu.user_avatar.id,
                                    company_id: cu.company_id,
                                    attachment_content_type: user.avatar_content_type,
                                    attachment_file_size: user.avatar_file_size
                                  )
            unless existing_attachments.present?
              CustomFormAttachment.create(
                custom_form_value_id: cu.user_avatar.id,
                company_id: cu.company_id,
                attachment: user.avatar,
                attachment_content_type: user.avatar_content_type,
                attachment_file_size: user.avatar_file_size
              )
            end
          end
        end
      end
    end
  end

  desc "update helpdesk format of date type fields to YYYY-MM-DD"
  task update_helpdesk_date_type_field_format: :environment do
    Company.all.find_each do |company|
      cfvs = CustomFormValue.includes(:custom_form_field).where(custom_form_fields: { field_attribute_type: "date"}, company_id: company.id, module_type: "HelpTicket")
      cfvs.each do |cfv|
          if cfv.present?
              cfv.update(value_str: cfv&.value_str&.to_date&.strftime("%Y-%m-%d"))
          end
      end
    end
  end

  desc "destroy custom form values of attachment type form fields with no attachment"
  task destroy_custom_form_values_with_no_attachment: :environment do
    CustomFormValue.includes(:custom_form_field).where(custom_form_fields: { field_attribute_type: "attachment" }).each do |cfv|
      if cfv.attachment.nil?
        cfv.destroy
      end
    end
  end

  desc "destroy invalid us phone number for company users"
  task destroy_invalid_us_phone_numbers: :environment do
    def is_invalid?(value)
      value.present? &&
      value.split(',')[0] == 'US' &&
      value.split(',')[1].blank?
    end

    CustomFormValue.joins(:custom_form_field).where(custom_form_fields: { field_attribute_type: 'phone'}).find_each do |value|
      if is_invalid?(value.value_str)
        value.destroy
      end
    end
  end

  desc 'Destroy custom form values where module id is null'
  task destroy_empty_module_id_values: :environment do
    CustomFormValue.where(module_id: nil).where("created_at < ?", Time.new(2022, 8, 11)).each do |cfv|
      if cfv.custom_form_attachment.present?
        cfv.custom_form_attachment.destroy
      end
      cfv.delete
    end
  end

  desc 'destroy avatar custom form values where custom form attachment is absent'
  task destroy_values_where_attachment_is_missing: :environment do
    avatar_field_ids = CustomFormField.where(field_attribute_type: "avatar").pluck(:id)
    values_with_no_attachments = CustomFormValue.where(custom_form_field_id: avatar_field_ids)
                                                .where.not(id: CustomFormAttachment.pluck(:custom_form_value_id))

    values_with_no_attachments.find_each do |value|
      value.destroy
    end
  end

  desc 'destroy duplicate custom form values for the location'
  task destroy_duplicate_values_for_location: :environment do
    duplicates = []
    CompanyUser.includes(custom_form_values: :custom_form_field).find_each do |cu|
      locations = cu.custom_form_values.joins(:custom_form_field)
                                          .where(custom_form_fields: { field_attribute_type: "location_list" })
      locations_values = locations.pluck(:value_int).compact
      if locations_values.length != locations_values.uniq.length
        location_custom_form_values = cu.custom_form_values.where(value_int: locations_values)
        grouped_locations = location_custom_form_values.group_by { |record| [record.value_int, record.custom_form_field_id] }

        duplicates << grouped_locations.select { |_, records| records.length > 1 }.values.flatten[1..]
      end
    end
    duplicates = duplicates.flatten.compact
    duplicates.each do |value|
      value.destroy
    end
  end

  desc 'Add default value of status in tickets.'
  task add_status_value: :environment do
    HelpTicket.where(status: nil).find_each do |ht|
      puts(ht.id)
      status_field = ht.custom_form.custom_form_fields.find_by(name: 'status')
      cfv = ht.custom_form_values.find_by(custom_form_field_id: status_field.id)
      if cfv.nil?
        token_text = strip_tags('Open').gsub("'", "''")
        sql = """SELECT to_tsvector('simple', coalesce('#{token_text}', ''))"""
        results = ActiveRecord::Base.connection.execute(sql)
      
        form_values = [{ 
          custom_form_field_id: status_field.id,
          custom_form_id: status_field.custom_form_id,
          value_str: 'Open',
          company_id: ht.company_id,
          module_id: ht.id,
          module_type: "HelpTicket",
          value_str_tokens: results.as_json.last['to_tsvector'],
        }]

        ht.update_column(:status, 'Open')
        CustomFormValue.insert_all form_values
      else
        ht.update_column(:status, cfv.value_str)
      end
    end
  end
end
