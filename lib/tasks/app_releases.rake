namespace :app_releases do
  desc "migrate latest desktop app versions to the new table"
  task migrate_desktop_apps_versions: :environment do
    migrate_windows_apps_records
    migrate_mac_agent_records
    migrate_self_on_boarding_record
  end

  def migrate_windows_apps_records
    latest_scripts = WindowScript.where(name: ['agent', 'self_on_boarding', 'network_discovery'])
                                 .order('name, created_at DESC')
                                 .select('DISTINCT ON (name) *')
    latest_scripts.each do |script|
      AppRelease.create!(
        app_name: script.name,
        app_type: 'Windows',
        version: script.version,
        description: script.description
      )
    end
  end

  def migrate_mac_agent_records
    old_records = AppVersion.order('is_admin_app, created_at DESC').select('DISTINCT ON (is_admin_app) *')
    old_records.each do |old_record|
      AppRelease.create!(
        app_name: 'agent',
        app_enabled: true,
        app_type: 'MAC',
        description: old_record.description,
        version: old_record.app_version,
        is_admin_app: old_record.is_admin_app,
        is_package_build: old_record.is_pkg_build,
        build_version: old_record.version,
        installer_link: old_record.installer_link
      )
    end
  end

  def migrate_self_on_boarding_record
    old_record = WindowsExe.last
    AppRelease.create!(
      app_name: 'self_on_boarding',
      app_enabled: true,
      app_type: 'MAC',
      description: old_record.description,
      version: old_record.app_version,
      build_version: old_record.version,
      installer_link: old_record.exe_link
    )
  end
end
