namespace :workspace_agents do
  desc "replace Helpdesk Agents with workspace agents for automated tasks"
  task filtered_automated_tasks_helpdesk_agents: :environment do
    sql = """SELECT companies.id FROM companies JOIN workspaces ON companies.id = workspaces.company_id GROUP BY companies.id HAVING COUNT(workspaces.id) > 1;"""
    company_ids = ActiveRecord::Base.connection.execute(sql).pluck('id')
    default_helpdesk_agents = Group.where(name: "Help Desk Agents", default: true, company_id: company_ids).group_by(&:company_id)
    workspaces = Workspace.where(company_id: company_ids)
    workspaces.find_each do |workspace|
      agent_group = default_helpdesk_agents[workspace.company_id].first
      agent_contributor_id = agent_group.contributor_id
      next if agent_group.workspace_id == workspace.id

      workspace_agent = workspace.workspace_agents
      automated_task_ids = workspace.automated_tasks.pluck(:id)
      update_event_details(automated_task_ids, agent_contributor_id, workspace_agent, workspace)
      update_task_actions(automated_task_ids, agent_contributor_id, workspace_agent, workspace)
    end
  end

  desc "replace Helpdesk Agents with workspace agents for helpdesk"
  task filtered_helpdesk_agents_in_helpdesk_module: :environment do
    sql = """SELECT companies.id FROM companies JOIN workspaces ON companies.id = workspaces.company_id GROUP BY companies.id HAVING COUNT(workspaces.id) > 1;"""
    company_ids = ActiveRecord::Base.connection.execute(sql).pluck('id')
    default_helpdesk_agents = Group.where(name: "Help Desk Agents", default: true, company_id: company_ids).group_by(&:company_id)
    workspaces = Workspace.where(company_id: company_ids)
    workspaces.find_each do |workspace|
      agent_group = default_helpdesk_agents[workspace.company_id].first
      agent_contributor_id = agent_group.contributor_id
      next if agent_group.workspace_id == workspace.id

      workspace_agent = workspace.workspace_agents
      update_workspace_privileges(workspace, agent_contributor_id, workspace_agent)
      update_articles(workspace, agent_contributor_id, workspace_agent)
      update_tasks(workspace, agent_contributor_id, workspace_agent)
      update_scheduled_tasks(workspace, agent_contributor_id, workspace_agent)
      update_analytics_report_templates(workspace, agent_contributor_id, workspace_agent)
      update_custom_forms(workspace, agent_contributor_id, workspace_agent)
      update_sla_policies(workspace, agent_contributor_id, workspace_agent)
    end
  end

  desc "replace Helpdesk Agents with workspace agents for help tickets"
  task filtered_helpdesk_agents_in_tickets: :environment do
    sql = """SELECT companies.id FROM companies JOIN workspaces ON companies.id = workspaces.company_id GROUP BY companies.id HAVING COUNT(workspaces.id) > 1;"""
    company_ids = ActiveRecord::Base.connection.execute(sql).pluck('id')
    default_helpdesk_agents = Group.where(name: "Help Desk Agents", default: true, company_id: company_ids).group_by(&:company_id)
    workspaces = Workspace.where(company_id: company_ids)
    workspaces.find_each do |workspace|
      agent_group = default_helpdesk_agents[workspace.company_id].first
      agent_contributor_id = agent_group.contributor_id
      next if agent_group.workspace_id == workspace.id

      workspace_agent = workspace.workspace_agents
      workspace.help_tickets.find_each do |ticket|
        update_linkables(ticket, agent_contributor_id, workspace_agent)
        update_project_task(ticket, agent_contributor_id, workspace_agent)
        update_custom_form_values(ticket, agent_contributor_id, workspace_agent)
      end
    end
  end
end

def update_custom_form_values(ticket, agent_contributor_id, workspace_agent)
  custom_form_values = ticket.custom_form_values.includes(:custom_form_field).where(custom_form_field: { field_attribute_type: "people_list"},  value_int: agent_contributor_id)
  custom_form_values.find_each do |custom_form_value|
    begin
      custom_form_value.update_columns(value_int: workspace_agent.contributor_id)
      custom_form_value.add_module_assigned_to_value if custom_form_value.is_helpticket_assigned_to_field?
      custom_form_value.update_module_group_members if custom_form_value.is_helpticket_people_list_field?
      create_workspace_agents_success_log(ticket.company_id, ticket.workspace_id, custom_form_value.id, custom_form_value.class.name)
      puts 'custom form value updated successfully'
    rescue => e
      Rails.logger.warn "Issue updating custom form value with ID: #{custom_form_value.id}. Error message: #{e.message}"
      create_workspace_agents_error_log(e, ticket.company_id, ticket.workspace_id, custom_form_value.id, custom_form_value.class.name)
    end
  end
end

def update_project_task(ticket, agent_contributor_id, workspace_agent)
  project_task_ids = ticket.project_tasks.pluck(:id)
  TaskAssignee.where(project_task_id: project_task_ids, contributor_id: agent_contributor_id).update_all(contributor_id: workspace_agent.contributor_id)
  puts 'Ticket project tasks updated successfully'
  create_workspace_agents_success_log(ticket.company_id, ticket.workspace_id, ticket.id, 'HelpTicket project tasks')
rescue => e
  Rails.logger.warn "Issue updating ticket project tasks with ticket ID: #{ticket.id}. Error message: #{e.message}"
  create_workspace_agents_error_log(e, ticket.company_id, ticket.workspace_id, ticket.id, 'HelpTicket project tasks')
end

def update_linkables(ticket, agent_contributor_id, workspace_agent)
  linkable_target_ids = ticket.linkable.source_linkable_links.pluck(:target_id)
  Linkable.where(id: linkable_target_ids, linkable_id: agent_contributor_id).update_all(linkable_id: workspace_agent.id)
  puts 'Ticket Linkables updated successfully'
  create_workspace_agents_success_log(ticket.company_id, ticket.workspace_id, ticket.id, 'HelpTicket linkables')
rescue => e
  Rails.logger.warn "Issue updating ticket linkables with ticket ID: #{ticket.id}. Error message: #{e.message}"
  create_workspace_agents_error_log(e, ticket.company_id, ticket.workspace_id, ticket.id, 'HelpTicket linkables')
end

def update_workspace_privileges(workspace, agent_contributor_id, workspace_agent)
  w_privilege = workspace.privileges.find_by(contributor_id: agent_contributor_id)
  if w_privilege
    w_privilege.update_columns(contributor_id: workspace_agent.contributor_id)
    w_privilege.create_expanded_privilege
    create_workspace_agents_success_log(workspace.company_id, workspace.id, nil, 'Workspace privileges')
    puts 'Workspace privileges updated successfully'
  end
rescue => e
  Rails.logger.warn "Issue updating ticket linkables with ticket ID: #{ticket.id}. Error message: #{e.message}"
  create_workspace_agents_error_log(e, workspace.company_id, workspace.id, nil, 'Workspace privileges')
end

def update_custom_forms(workspace, agent_contributor_id, workspace_agent)
  custom_forms = workspace.custom_forms.where(company_module: "helpdesk").pluck(:id)
  CustomFormField.where(custom_form_id: custom_forms).find_each do |field|
    begin
      if field.field_attribute_type == "people_list"
        agent_options = JSON.parse(field.options) if field.options
        agent_option = agent_options&.find { |option| option['contributorId'] == agent_contributor_id }

        if agent_option
          agent_option['contributorId'] = workspace_agent.contributor_id
          agent_option['name'] = workspace_agent.name
          field.options = agent_options.to_json
          field.update_column(:options, field.options) 
          create_workspace_agents_success_log(workspace.company_id, workspace.id, field.id, field.class.name)
        end

        if field.default_value.present? && field.default_value.class == String && string_is_array?(field.default_value)
          default_options = JSON.parse(field.default_value)
        end
        if default_options
          default_options.map! { |option| option == agent_contributor_id ? workspace_agent.contributor_id : option }
          field.default_value = default_options.to_json
        end
        if field.changed?
          field.update_column(:default_value, field.default_value) 
          create_workspace_agents_success_log(workspace.company_id, workspace.id, field.id, field.class.name)
        end
        puts 'Form Field updated successfully'
      end

      custom_form_field_permissions = field.custom_form_field_permissions.where(contributor_id: agent_contributor_id)
      custom_form_field_permissions.find_each do |permission|
        permission.update_columns(contributor_id: workspace_agent.contributor_id)
        permission.populate_form_field_expanded_permission
        create_workspace_agents_success_log(workspace.company_id, workspace.id, field.id, field.class.name)
        puts 'Form Field permissions updated successfully'
      end
    rescue => e
      Rails.logger.warn "Issue updating custom form value with ID: #{field.id}. Error message: #{e.message}"
      create_workspace_agents_error_log(e, workspace.company_id, workspace.id, field.id, field.class.name)
    end
  end
end

def update_sla_policies(workspace, agent_contributor_id, workspace_agent)
  custom_form_ids = workspace.custom_forms.where(company_module: "helpdesk").pluck(:id)
  policies = Sla::Policy.where(custom_form_id: custom_form_ids).pluck(:id)
  update_sla_emails(workspace, agent_contributor_id, workspace_agent, policies)
  update_sla_conditions(workspace, agent_contributor_id, workspace_agent, policies)
end

def update_sla_conditions(workspace, agent_contributor_id, workspace_agent, policies)
  sla_conditions = Sla::Condition.where(sla_policy_id: policies, condition_type: ['form_field', "created_by_group_member"])

  sla_conditions.find_each do |sla_condition|
    begin
      value = JSON.parse(sla_condition.value)
      if sla_condition.condition_type == "form_field"
        if value['form_value'].is_a?(Array) && value['form_value'].include?(agent_contributor_id)
          value['form_value'].delete(agent_contributor_id)
          value['form_value'] << workspace_agent.contributor_id.to_s
          sla_condition.update_column(:value, value.to_json)
          create_workspace_agents_success_log(workspace.company_id, workspace.id, sla_condition.id, sla_condition.class.name)
          puts 'sla condition updated successfully'
        end
      else
        contributor = value['contributors'].find { |contributor| contributor['id'] == agent_contributor_id }
        if contributor
          contributor['id'] = workspace_agent.contributor_id
          contributor['name'] = workspace_agent.name
          sla_condition.update_column(:value, value.to_json)
          create_workspace_agents_success_log(workspace.company_id, workspace.id, sla_condition.id, sla_condition.class.name)
          puts 'sla condition updated successfully'
        end
      end
    rescue => e
      Rails.logger.warn "Issue updating sla condition value with ID: #{sla_condition.id}. Error message: #{e.message}"
      create_workspace_agents_error_log(e, workspace.company_id, workspace.id, sla_condition.id, sla_condition.class.name)
    end
  end
end

def update_sla_emails(workspace, agent_contributor_id, workspace_agent, policies)
  sla_emails = Sla::Email.where(sla_policy_id: policies)
  sla_emails.find_each do |email|
    begin
      if email.target.include?(agent_contributor_id.to_s)
        email.target.delete(agent_contributor_id.to_s)
        email.target << workspace_agent.contributor_id.to_s
        email.update_column(:target, email.target)
        create_workspace_agents_success_log(workspace.company_id, workspace.id, email.id, email.class.name)
        puts 'sla email updated successfully'
      end
    rescue => e
      Rails.logger.warn "Issue updating sla email value with ID: #{email.id}. Error message: #{e.message}"
      create_workspace_agents_error_log(e, workspace.company_id, workspace.id, email.id, email.class.name)
    end
  end
end

def update_tasks(workspace, agent_contributor_id, workspace_agent)
  workspace.tasks.find_each do |task|
    begin
      if task.assignees.include?(agent_contributor_id.to_s)
        task.assignees.delete(agent_contributor_id.to_s)
        task.assignees << workspace_agent.contributor_id.to_s
        task.update_column(:assignees, task.assignees)
        create_workspace_agents_success_log(workspace.company_id, workspace.id, task.id, task.class.name)
        puts 'workspace task updated successfully'
      end
    rescue => e
      Rails.logger.warn "Issue updating Task value with ID: #{task.id}. Error message: #{e.message}"
      create_workspace_agents_error_log(e, workspace.company_id, workspace.id, task.id, task.class.name)
    end
  end
end

def update_scheduled_tasks(workspace, agent_contributor_id, workspace_agent)
  workspace.scheduled_tasks.find_each do |task|
    begin
      task.scheduled_task_notifications.find_each do |notification|
        recipients = notification.value['recipients']
        if recipients&.include?(agent_contributor_id)
          recipients.delete(agent_contributor_id)
          recipients << workspace_agent.contributor_id
          notification.update_column(:value, notification.value)
          create_workspace_agents_success_log(workspace.company_id, workspace.id, task.id, task.class.name)
          puts 'workspace scheduled task updated successfully'
        end
      end
    rescue => e
      Rails.logger.warn "Issue updating Scheduled Task value with ID: #{task.id}. Error message: #{e.message}"
      create_workspace_agents_error_log(e, workspace.company_id, workspace.id, task.id, task.class.name)
    end
  end
end

def update_analytics_report_templates(workspace, agent_contributor_id, workspace_agent)
  workspace.analytics_report_templates.find_each do |report|
    begin
      report.scheduled_reports.find_each do |s_report|
        recipients = s_report.recipient_ids
        if recipients&.include?(agent_contributor_id)
          recipients.delete(agent_contributor_id)
          recipients << workspace_agent.contributor_id
          s_report.update_column(:recipient_ids, recipients)
          create_workspace_agents_success_log(workspace.company_id, workspace.id, report.id, report.class.name)
          puts 'Analytics report template updated successfully'
        end
      end
    rescue => e
      Rails.logger.warn "Issue updating Report Template value with ID: #{report.id}. Error message: #{e.message}"
      create_workspace_agents_error_log(e, workspace.company_id, workspace.id, report.id, report.class.name)
    end
  end
end

def update_articles(workspace, agent_contributor_id, workspace_agent)
  workspace.articles.find_each do |article|
    begin
      article.article_privileges.where(contributor_id: agent_contributor_id).find_each do |privilege|
        privilege.update(contributor_id: workspace_agent.contributor_id)
        create_workspace_agents_success_log(workspace.company_id, workspace.id, article.id, article.class.name)
        puts 'Article privilege updated successfully'
      end
    rescue => e
      Rails.logger.warn "Issue updating Article value with ID: #{article.id}. Error message: #{e.message}"
      create_workspace_agents_error_log(e, workspace.company_id, workspace.id, article.id, article.class.name)
    end
  end
end

def update_event_details(automated_task_ids, agent_contributor_id, workspace_agent, workspace)
  events =  ["{a comment} is added to a ticket", "{a project task} is added to a ticket", "{a project task} is updated", "{a time entry} is added to a ticket", "{a ticket} is created", "{a ticket} is updated"]

  task_events_ids = AutomatedTasks::TaskEvent.includes(:event_type).where(automated_task_id: automated_task_ids, event_type: { name: events }).pluck(:id)

  AutomatedTasks::EventDetail.where(task_event_id: task_events_ids).find_each do |event|
    begin
      value_hash = JSON.parse(event.value) if event.value.present?
      if value_hash.present?
        if value_hash.key?('contributors') && value_hash['contributors'].include?(agent_contributor_id)
          value_hash['contributors'].delete(agent_contributor_id)
          value_hash['contributors'] << workspace_agent.contributor_id
          event.update_column(:value, value_hash.to_json)
          create_workspace_agents_success_log(workspace.company_id, workspace.id, event.id, event.class.name)
          puts 'AutomatedTasks::EventDetail updated successfully'
        end
        if value_hash['form_field']&.dig('field_attribute_type') == 'people_list'
          options = value_hash['form_field']['options']
          if options.present?
            option = options.find { |opt| opt['contributor_id'] == agent_contributor_id }
            if option
              option['name'] = workspace_agent.name
              option['id'] = workspace_agent.id
              option['contributor_id'] = workspace_agent.contributor_id
              event.update_column(:value, value_hash.to_json)
              create_workspace_agents_success_log(workspace.company_id, workspace.id, event.id, event.class.name)
              puts 'AutomatedTasks::EventDetail updated successfully'
            end
          end
        end
      end
    rescue => e
      Rails.logger.warn "Issue updating Automated Tasks Events value with ID: #{event.id}. Error message: #{e.message}"
      create_workspace_agents_error_log(e, workspace.company_id, workspace.id, event.id, event.class.name)
    end
  end
end

def update_task_actions(automated_task_ids, agent_contributor_id, workspace_agent, workspace)
  task_actions = AutomatedTasks::TaskAction.includes(:action_type).where(automated_task_id: automated_task_ids)
  task_actions.find_each do |action|
    begin
      task_value = JSON.parse(action.value) if action.value.present?
      if task_value.present?
        if ["AddAlert", "SendSms", "SendEmail"].include?(action.action_type.action_class)
          agent_contributor = task_value["contributors"]&.find { |contributor| contributor['id'] == agent_contributor_id}
          if agent_contributor
            agent_contributor.merge!(
              'id' => workspace_agent.contributor_id,
              'name' => workspace_agent.name,
              'root_id' => workspace_agent.id,
              'avatar_thumb_url' => nil
            )
            action.update_column(:value, task_value.to_json)
            create_workspace_agents_success_log(workspace.company_id, workspace.id, action.id, action.class.name)
            puts 'AutomatedTasks::TaskAction updated successfully'
          end
        elsif action.action_type.action_class == "SetFormField"
          if task_value["form_field"] && task_value.dig("form_field", "field_attribute_type") == "people_list"
            options = task_value.dig("form_field", "options") || []
            option = options.find { |opt| opt['contributor_id'] == agent_contributor_id }
            if option
              option.merge!(
                'name' => workspace_agent.name,
                'id' => workspace_agent.id,
                'contributor_id' => workspace_agent.contributor_id
              )
              action.update_column(:value, task_value.to_json)
              create_workspace_agents_success_log(workspace.company_id, workspace.id, action.id, action.class.name)
              puts 'AutomatedTasks::TaskAction updated successfully'
            end
            contributors = task_value["form_value"].is_a?(Array) ? task_value["form_value"] : task_value.dig("form_value", "contributors")
            if contributors.is_a?(Array) && contributors.include?(agent_contributor_id)
              contributors.map! { |contributor| contributor == agent_contributor_id ? workspace_agent.contributor_id : contributor }
              action.update_column(:value, task_value.to_json)
              create_workspace_agents_success_log(workspace.company_id, workspace.id, action.id, action.class.name)
              puts 'AutomatedTasks::TaskAction updated successfully'
            end
          end
        elsif action.action_type.action_class == "AddTicket"
          task_value["values"].each do |val|
            if val["value_int"].present? && val["value_int"] == agent_contributor_id
              val["value_int"] = workspace_agent.contributor_id
            end
          end
          if action.changed?
            action.update_column(:value, task_value.to_json)
            create_workspace_agents_success_log(workspace.company_id, workspace.id, action.id, action.class.name)
          end
          puts 'AutomatedTasks::TaskAction updated successfully'
        end
      end
    rescue => e
      Rails.logger.warn "Issue updating Automated Tasks Actions value with ID: #{action.id}. Error message: #{e.message}"
      create_workspace_agents_error_log(e, workspace.company_id, workspace.id, action.id, action.class.name)
    end
  end
end

def create_workspace_agents_error_log(error, company_id, workspace_id, record_id = nil, record_class = nil)
  api_event = Logs::ApiEvent.new(company_id: company_id, activity: 1, api_type: "Workspace Agents", status: 0)
  error_detail = { company_id: company_id, error: error, workspace_id: workspace_id, record_id: record_id, record_class: record_class}
  api_event.class_name = 'Workspace::Agents'
  api_event.error_detail = error_detail
  api_event.save!
end

def create_workspace_agents_success_log(company_id, workspace_id, record_id = nil, record_class = nil)
  api_event = Logs::ApiEvent.new(company_id: company_id, activity: 1, api_type: "Workspace Agents", status: 1)
  success_detail = { company_id: company_id, workspace_id: workspace_id, record_id: record_id, record_class: record_class}
  api_event.class_name = 'Workspace::Agents'
  api_event.response = success_detail.to_s
  api_event.save!
end

def string_is_array?(str)
  str.match(/^\[.*\]$/) ? true : false
end
