namespace :verified_identities do
  desc "Add <NAME_EMAIL> in all verified identities for complaint notification"
  task add_sns_for_complaint: :environment do
    ses_verified_identity_emails.each do |email|
      aws_ses_client.set_identity_notification_topic({
        identity: email,
        notification_type: "Complaint",
        sns_topic: Rails.application.credentials.aws[:s3][:logs_sns_topic],
      })

      puts email
      sleep 3
    end
  end

  desc "Add <NAME_EMAIL> in all verified identities for bounce notification"
  task add_sns_for_bounce: :environment do
    ses_verified_identity_emails.each do |email|
      aws_ses_client.set_identity_notification_topic({
        identity: email,
        notification_type: "Bounce",
        sns_topic: Rails.application.credentials.aws[:s3][:logs_sns_topic],
      })

      puts email
      sleep 3
    end
  end

  desc "Remove <NAME_EMAIL> in all verified identities for complaint notification"
  task remove_sns_for_complaint: :environment do
    ses_verified_identity_emails.each do |email|
      aws_ses_client.set_identity_notification_topic({
        identity: email,
        notification_type: "<PERSON><PERSON>lain<PERSON>",
      })

      puts email
      sleep 3
    end
  end

  desc "Remove <NAME_EMAIL> in all verified identities for bounce notification"
  task remove_sns_for_bounce: :environment do
    ses_verified_identity_emails.each do |email|
      aws_ses_client.set_identity_notification_topic({
        identity: email,
        notification_type: "Bounce",
      })

      puts email
      sleep 3
    end
  end
end

def aws_ses_client
  Aws::SES::Client.new(
    region: Rails.application.credentials.aws[:region],
    access_key_id: Rails.application.credentials.aws[:s3][:access_key_id],
    secret_access_key: Rails.application.credentials.aws[:s3][:secret_access_key]
  )
end

def ses_verified_identity_emails
  verified_identities = aws_ses_client.list_identities()
  verified_identities.identities
end
