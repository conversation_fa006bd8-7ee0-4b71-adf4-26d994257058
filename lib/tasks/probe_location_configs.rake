namespace :probe_location do
  desc "Adds missing probe config to probe locations"
  task update_probe_configs: :environment do
    location_ids = ProbeLocation.find_each.map{ |loc| loc.id if !loc.probe_config.present?}.compact
    location_ids.each do |id|
      location = ProbeLocation.find(id)
      ProbeConfig.create(company_id: location.company_id, probe_location_id: id)
    end
  end

  desc "Populate manager contributor from company user"
  task update_managed_by_contributor_id: :environment do
    ProbeLocation.where.not(company_user_id: nil).find_each do |probe|
      managed_by_contributor_id = probe.company_user&.contributor&.id
      probe.update_columns(managed_by_contributor_id: managed_by_contributor_id)
    end
  end

  desc "Update the probe config schedule from single to multiple time schedules"
  task update_probe_configs_schedule: :environment do
    ProbeConfig.where.not(schedule_setting: nil).find_each do |pc|
      days_array = ["schedule_sunday", "schedule_monday", "schedule_tuesday", "schedule_wednesday", "schedule_thursday", "schedule_friday", "schedule_saturday"]
      old_setting = pc.schedule_setting
      sch_enabled = old_setting["schedule_enabled"]
      sch_time = old_setting["schedule_time"]
      new_setting = {schedule_enabled: sch_enabled, scheduled_days: []}
      days_array.each_with_index do |day, indx|
        new_setting[:scheduled_days] << {"scheduled_day": indx, "scheduled_times": [sch_time]} if old_setting[day]
      end
      pc.schedule_setting = JSON.parse(new_setting.to_json)
      pc.save!
    end
  end

  desc "Add exclusion list array in the probe configs"
  task add_exclusion_list: :environment do
    ProbeConfig.where.not(ip_range: nil).find_each do |pc|
      new_range = []
      pc.ip_range.each do |item|
        new_range << JSON.parse(item.merge(exclusion_list: [], display_exclusion: "" ).to_json)
      end
      pc.ip_range = new_range
      pc.save!
    end
  end

  desc "Add cidr field in the probe configs"
  task add_cidr_fields: :environment do
    ProbeConfig.where.not(ip_range: nil).find_each do |pc|
      new_range = []
      pc.ip_range.each do |item|
        new_range << JSON.parse(item.merge(ip_format: "iprange", cidr_ip: "", cidr_subnet: "24").to_json)
      end
      pc.ip_range = new_range
      pc.save!
    end
  end
end
