namespace :add_user_roles_to_sample_companies do
  desc "add user roles to sample companies if not present."
  task add_user_roles: :environment do
    sample = Company.find_by(id: Rails.application.credentials.sample_company[:parent_id])
    Company.where(is_sample_company: true).each do |clone|
      ActiveRecord::Base.transaction do
        parent_company = clone.parent_company
        next if parent_company.blank?
        clone.user_roles.where(built_in: true).destroy_all
        # Make user role hash from sample company for clone company.
        sample.user_roles.each do |role|
          clone_user_roles = clone.user_roles.where(name: role.name)
          if clone_user_roles.blank?
            user_role_privileges = %w{}
            duplicate_role       = role.dup
            duplicate_role.user_role_assignments_count = nil
            user_role_privileges = role.user_role_privileges.map(&:dup)
            duplicate_role.user_role_privileges = user_role_privileges
            clone.user_roles << duplicate_role
          else
            clone_user_role = clone_user_roles.first
            clone_user_role.user_role_privileges.destroy_all
            user_role_privileges = role.user_role_privileges.map(&:dup)
            clone_user_role.user_role_privileges = user_role_privileges
          end 
        end
        # save all user roles to clone company.
        clone.save!

        # Assign user roles to company users.
        sample.company_users.each do |company_user|
          clone_company_user = clone.company_users.find_by_user_id(company_user.user_id)
          if clone_company_user
            # Destroy old assignments to avoid bad data.
            company_user.user_role_assignments.each do |assignment|
              if clone_company_user.user_roles.where(name: assignment.user_role.name).blank?
                clone_company_user.user_role_assignments.find_or_create_by!(user_role: clone.user_roles.find_by_name(assignment.user_role.name))
              end
            end
          end
        end

        admin_role = clone.user_roles.admin.first
        parent_company.admin_company_users.each do |admin|
          cloned_cu = clone.company_users.find_or_create_by!(user_id: admin.user_id)
          cloned_cu.granted_access_at ||= DateTime.now
          cloned_cu.save!
          cloned_cu.user_role_assignments.find_or_create_by!({user_role_id: admin_role.id})
        end
        ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
      rescue => e
        Rails.logger.error("Transaction failed: #{e.message}")
        ActiveRecord::Base.connection.execute "ROLLBACK"
      end
    end
  end
end
