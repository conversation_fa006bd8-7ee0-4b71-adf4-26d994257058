namespace :intercom do
  desc "TODO"
  task create_users: :environment do
    if Rails.env.production? || Rails.env.test?
      CompanyUser.where.not(company_users: {granted_access_at: nil}).each do |u|
        Events::CreateIntercomUserWorker.perform_async({ id: u.id }.as_json)
      end
    end
  end

  desc "first admin of company will be the user of previously created events"
  task create_events: :environment do
    Contract.unscoped.all.each do |contract|
      if company = contract.company
        admin_user = company.user_roles.admin.first.company_users.first
        contract.created_by = admin_user
        contract.event_type = "created"
        contract.create_intercom_event
      end
    end

    ManagedAsset.unscoped.all.each do |manages_asset|
      if company = manages_asset.company
        admin_user = company.user_roles.admin.first.company_users.first
        manages_asset.created_by = admin_user
        manages_asset.event_type = "created"
        manages_asset.create_intercom_event
      end
    end

    HelpTicket.unscoped.all.each do |help_ticket|
      if company = help_ticket.company
        admin_user = company.user_roles.admin.first.company_users.first
        help_ticket.created_by = admin_user
        help_ticket.event_type = "created"
        help_ticket.create_intercom_event
      end
    end

    CompanyUser.unscoped.all.each do |company_user|
      if company = company_user.company
        admin_user = company.user_roles.admin.first.company_users.first
        company_user.created_by = admin_user
        company_user.event_type = "created"
        if admin_user.present?
          company_user.create_intercom_event
        end
      end
    end

    Vendor.unscoped.all.each do |vendor|
      if company = vendor.company
        admin_user = company.user_roles.admin.first.company_users.first
        vendor.created_by = admin_user
        vendor.event_type = "created"
        vendor.create_intercom_event
      end
    end

    TelecomService.unscoped.all.each do |telecom_service|
      if company = telecom_service.company
        admin_user = company.user_roles.admin.first.company_users.first
        telecom_service.created_by = admin_user
        telecom_service.event_type = "created"
        telecom_service.create_intercom_event
      end
    end
  end
end
