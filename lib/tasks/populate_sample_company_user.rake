namespace :populate_sample_company_user do
  desc "create company user for sample company with users having admin access"
  task update_admins: :environment do
    UserRoleAssignment.joins(:user_role).where("user_roles.name = 'Admin'").each do |user_role_assignment|
      sample_company = Company.find_by(is_sample_company: true)
      company_user = sample_company.company_users.where(user_id: user_role_assignment.company_user.user_id, is_sample_company_user: true).first
      unless company_user
        cloned_cu = sample_company.company_users.find_or_create_by!(user_id: user_role_assignment.company_user.user_id)
        cloned_cu.is_sample_company_user = true
        cloned_cu.granted_access_at ||= DateTime.now
        cloned_cu.save
        UserRoleAssignment.find_or_create_by!({user_role_id: user_role_assignment.user_role.id, company_user_id: cloned_cu.id})
      end
    rescue => e
      Rails.logger.info e
    end
  end
end
