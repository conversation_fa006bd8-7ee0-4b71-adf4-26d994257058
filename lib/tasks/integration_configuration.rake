namespace :integration_configuration do
  task gsuite_signup_path_default: :environment do
    if Rails.env.development?
      path = "https://genuity-development.auth.us-west-2.amazoncognito.com/oauth2/authorize?identity_provider=Google&redirect_uri=http://localhost:3000/consume_code&response_type=CODE&client_id=6vjr43dmslt8evkaivull0f1f6&scope=aws.cognito.signin.user.admin email openid phone profile"
    elsif Rails.env.staging?
      path = "https://genuity-staging.auth.us-west-2.amazoncognito.com/oauth2/authorize?identity_provider=Google&redirect_uri=https://secure.gogenuity-staging.com/consume_code&response_type=CODE&client_id=5k2g6nobh48v82j9u2t1ssur58&state=STATE&scope=aws.cognito.signin.user.admin email openid profile"
    else Rails.env.production?
      path = "https://gogenuity.auth.us-west-2.amazoncognito.com/oauth2/authorize?identity_provider=Google&redirect_uri=https://secure.gogenuity.com/consume_code&response_type=CODE&client_id=2mmtvmr556g6th0r3pompjls0v&state=STATE&scope=aws.cognito.signin.user.admin email openid profile"
    end
    gsuite_signup = IntegrationConfiguration.find_or_create_by!({ key: "gsuite_signup" })
    gsuite_signup.update_column('value', path)
  end

  task gsuite_signup_path_update: :environment do
    UpdateGsuiteSignupPathWorker.perform_async
  end

  desc "Populate default workspace_id in old Slack configurations"
  task set_default_workspace_in_old_slack_configs: :environment do
    ::Integrations::Slack::Config.where(workspace_id: nil).find_each do |config|
      config.update_columns(workspace_id: config.company.default_workspace.id)
    end
  end

  desc 'It will create default automated tasks of old Slack configurations'
  task create_default_automated_tasks_of_old_slack_configs: :environment do
    ::Integrations::Slack::Config.active.find_each { |config| config.create_default_automated_tasks }
  end
end
