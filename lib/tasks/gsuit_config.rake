namespace :gsuite_config do
  desc "Sync the gsuite client_id"
  task sync_gsuite_client_id: :environment do
    old_configs = Integrations::Gsuite::Config.where(client_id: "997960466792-ug27lkbf3dv0vtq40l9oe17ehg197k9s.apps.googleusercontent.com")
    puts "Found #{old_configs.count} records with the old client_id."
    
    updated_count = old_configs.update_all(client_id: Rails.application.credentials.gsuite[:client_id])
    puts "Updated #{updated_count} records."
  end
end
