namespace :populate_mobile_app_permissions do
  desc "Create mobile app permissions for admin and help desk agents"
  task populate: :environment do
    ActiveRecord::Base.transaction do
      privileges = []
      contributor_ids = []

      Group.where("name = ? OR workspace_id IS NOT NULL", "Admins").find_each do |group|
        privileges << Privilege.new(
          name: '<PERSON><PERSON><PERSON>',
          workspace_id: nil,
          permission_type: 'write',
          contributor_id: group.contributor_id
        )
        contributor_ids << group.contributor_id
      end

      Privilege.import privileges

      contributor_ids.each do |contributor_id|
        UpdateExpandedPrivilegesWorker.new.perform(contributor_id)
      end
    end
  end
end
