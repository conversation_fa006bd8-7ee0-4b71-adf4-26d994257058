namespace :discovered_assets do
  desc "It will rename description key to details in optional_details"
  task rename_description_key_to_details_in_optional_details: :environment do
    DiscoveredAsset.where("optional_details->>'description' IS NOT NULL").find_each do |d_asset|
      d_asset.optional_details['details'] = d_asset.optional_details.delete('description')
      d_asset.update_columns(optional_details: d_asset.optional_details)
    end
  end

  desc "It will change default system_uuid from nil to empty string"
  task set_system_uuid_default: :environment do
    DiscoveredAsset.where(system_uuid: nil).update_all(system_uuid: '')
  end

  desc "It will update discovered asset details from string to json format"
  task change_discovered_asset_details_to_jsonb: :environment do
    DiscoveredAsset.all.find_each do |asset|
      if asset.optional_details_old.present?
        required_details = {}
        asset_details = asset.optional_details_old
        asset_details.keys.each do |key|
          if key == "device_type"
            required_details[key] = asset_details[key]
          else
            values_array = asset_details[key].split("\;") if asset_details[key].class == String
            if values_array.present?
              values_array.each do |val|
                if val.present?
                  pair = val.split(":", 2)
                  if pair.count == 2
                    required_details[pair[0].strip] = pair[1].strip
                  end
                end
              end
            end
          end
        end
        asset.optional_details = required_details
      else
        asset.optional_details = asset.optional_details_old
      end
      asset.skip_callbacks = true
      asset.save
    end
  end

  desc "Set default machine_serial_no from nil to empty string"
  task set_machine_serial_no_default: :environment do
    DiscoveredAsset.where(machine_serial_no: nil).update_all(machine_serial_no: "")
  end

  desc "Delete DiscoveredAssets with empty fields"
  task delete_empty_assets: :environment do
    records = DiscoveredAsset.where(
      "COALESCE(display_name, '') = '' AND 
       COALESCE(mac_address, '') = '' AND 
       COALESCE(ip_address, '') = '' AND 
       COALESCE(machine_serial_no, '') = '' AND 
       COALESCE(manufacturer, '') = ''"
    )

    count = records.count
    
    if count.positive?
      puts "Deleting #{count} empty DiscoveredAssets..."
      records.destroy_all
      puts "Deletion complete."
    else
      puts "No matching records found."
    end
  end

end
