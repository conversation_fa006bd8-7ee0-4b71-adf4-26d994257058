namespace :integration_api_events do
  desc "Add missing integrations in api events"
  task add_integrations: :environment do
    integ = { Gsuite: "gsuite", Ok<PERSON>: "okta", OneLogin: "one_login", Microsoft: "microsoft",
              Quickbooks: "quickbooks", Xero: "xero", Salesforce: "salesforce", Aws: "aws",
              Gcp: "gcp", Azure: "azure", PlaidAccount: "plaid", <PERSON><PERSON><PERSON>: "meraki",
            }

    Logs::ApiEvent.where("integration_id is NULL and class_name NOT IN (?)", ['Warranty::LenovoService', 'Warranty::HpService', 'Warranty::DellService'])
              .each do |event|
      integ_name  = event.class_name === "PlaidAccount" ? "PlaidAccount" : event.class_name.split('::')[1]
      integ_id = Integration.find_by(name: integ[integ_name.to_sym]).id
      event.integration_id = integ_id
      event.save!
    end
  end
end
