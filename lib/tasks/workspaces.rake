namespace :workspaces do
  desc "Create default workspace"
  task create: :environment do
    Company.find_each do |company|
      puts company.id
      company.send(:default_workspace)
    end
  end

  desc "Create the helpdesk email format for workspaces"
  task create_workspace_helpdesk_email_format: :environment do
    failed_workspaces = []
    Workspace.includes(:company).find_each do |workspace|
      company = workspace.company
      email_format = company.email_format
      puts "--------------------------------"
      puts "Current Company: #{company.name}"
      puts "Current workspace: #{workspace.name}"

      helpdesk_email_format = HelpdeskEmailFormat.find_or_initialize_by(company_id: company.id, workspace_id: workspace.id)
      next unless helpdesk_email_format.new_record?

      if email_format.nil? || !email_format.enabled
        begin
          helpdesk_email_format.save!
          puts "Created helpdesk email format for workspace #{workspace.id}"
        rescue => e
          puts "Error creating helpdesk email format for workspace #{workspace.id}: #{e.message}"
          failed_workspaces << workspace.id
        end
      else
        begin
          helpdesk_email_format.assign_attributes(
            header_text: email_format.header_text,
            footer_text: email_format.footer_text,
            hide_footer: email_format.hide_footer,
            header_customization: email_format.header_customization,
            footer_customization: email_format.footer_customization,
            enabled: true
          )

          if email_format.header_image.attached?
            puts "Attaching header image"
            helpdesk_email_format.header_image.attach(
              io: StringIO.new(email_format.header_image.download),
              filename: email_format.header_image.filename.to_s,
              content_type: email_format.header_image.content_type
            )
          end

          if email_format.footer_image.attached?
            puts "Attaching footer image"
            helpdesk_email_format.footer_image.attach(
              io: StringIO.new(email_format.footer_image.download),
              filename: email_format.footer_image.filename.to_s,
              content_type: email_format.footer_image.content_type
            )
          end

          helpdesk_email_format.save!
          puts "Saved helpdesk email format for workspace #{workspace.id}"
        rescue => e
          puts "Error saving helpdesk email format for workspace #{workspace.id}: #{e.message}"
          failed_workspaces << workspace.id
        end
      end
    end
    puts "Failed workspaces: #{failed_workspaces}" if failed_workspaces.any?
  end

  desc "Initializes companies to work with workspaces"
  task initialize: :environment do
    Rake::Task["workspaces:create"].invoke
    sql = """
    update company_mailers 
      set workspace_id = w2.id 
    from (
      select 
        company_id, min(created_at) as created_at
      from workspaces www
      group by www.company_id
    ) w1
      INNER JOIN workspaces w2 ON w2.company_id = w1.company_id
    where company_mailers.workspace_id IS NULL
      and company_mailers.company_id = w1.company_id
      and w1.created_at = w2.created_at
    """
    ActiveRecord::Base.connection.execute sql

    puts "Added workspaces_id in company mailer"

    sql = """
    update helpdesk_settings 
      set workspace_id = w2.id 
    from (
      select 
        company_id, min(created_at) as created_at
      from workspaces www
      group by www.company_id
    ) w1
      INNER JOIN workspaces w2 ON w2.company_id = w1.company_id
    where helpdesk_settings.workspace_id IS NULL
      and helpdesk_settings.company_id = w1.company_id
      and w1.created_at = w2.created_at
    """
    ActiveRecord::Base.connection.execute sql

    puts "Added workspaces_id in help desk settings"

    sql = """
    update custom_forms 
      set workspace_id = w2.id 
    from (
      select 
        company_id, min(created_at) as created_at
      from workspaces www
      group by www.company_id
    ) w1
      INNER JOIN workspaces w2 ON w2.company_id = w1.company_id
    where custom_forms.workspace_id IS NULL
      and custom_forms.company_id = w1.company_id
      and w1.created_at = w2.created_at
      and custom_forms.company_module = ?
    """
    query = ActiveRecord::Base.send(:sanitize_sql, [sql, CustomFormTemplate.company_modules["helpdesk"]])
    ActiveRecord::Base.connection.execute(query)

    puts "Added workspaces_id in custom forms"

    sql = """
    update help_tickets 
      set workspace_id = w2.id 
    from (
      select 
        company_id, min(created_at) as created_at
      from workspaces www
      group by www.company_id
    ) w1
      INNER JOIN workspaces w2 ON w2.company_id = w1.company_id
    where help_tickets.workspace_id IS NULL
      and help_tickets.company_id = w1.company_id
      and w1.created_at = w2.created_at
    """
    ActiveRecord::Base.connection.execute sql

    puts "Added workspaces_id in help tickets"

    sql = """
    update automated_tasks_automated_tasks 
      set workspace_id = w2.id 
    from (
      select 
        company_id, min(created_at) as created_at
      from workspaces www
      group by www.company_id
    ) w1
      INNER JOIN workspaces w2 ON w2.company_id = w1.company_id
    where automated_tasks_automated_tasks.workspace_id IS NULL
      and automated_tasks_automated_tasks.company_id = w1.company_id
      and w1.created_at = w2.created_at
    """
    ActiveRecord::Base.connection.execute sql

    puts "Added workspaces_id in automated tasks"

    sql = """
    update ticket_emails 
      set workspace_id = w2.id 
    from (
      select 
        company_id, min(created_at) as created_at
      from workspaces www
      group by www.company_id
    ) w1
      INNER JOIN workspaces w2 ON w2.company_id = w1.company_id
    where ticket_emails.workspace_id IS NULL
      and ticket_emails.company_id = w1.company_id
      and w1.created_at = w2.created_at
    """
    ActiveRecord::Base.connection.execute sql

    puts "Added workspaces_id in ticket emails"

    Workspace.where(name: 'Helpdesk').includes(:custom_forms).find_each do |w|
      puts w.id

      form = w.custom_forms.helpdesk.find_by(default: true) || w.custom_forms.helpdesk.order(:created_at).first
      helpdesk_form = form.helpdesk_custom_form
      subdomain = form.company.subdomain
      email = "helpdesk@#{subdomain}.#{Rails.application.credentials.root_domain}"
      existing = HelpdeskCustomForm.find_by(email: email)
      helpdesk_form.email ||= "helpdesk@#{subdomain}.#{Rails.application.credentials.root_domain}" unless existing
      helpdesk_form.save!
    end

    HelpTicket.where(guid: nil).find_each { |t| t.create_guid && t.save! }
    Location.where(guid: nil).find_each { |t| t.create_guid && t.save! }
    Rake::Task["expanded_privileges:populate"].invoke
    Rake::Task["custom_reports:add_workspace"].invoke
  end

  desc "Ensure followers people_list is in all existing and new default forms"
  task followers_staff_list_in_default_forms: :environment do
    Workspace.includes(:custom_forms).find_each do |w|
      puts w.id

      form = w.custom_forms.helpdesk.find_by(form_name: "IT General Request") || w.custom_forms.helpdesk.find_by(default: true) 
      if form.present?
        form.custom_form_fields.find_or_create_by!(name: "followers", field_attribute_type: CustomFormFieldTemplate.field_attribute_types["people_list"]) do |field|
          field.label = "Followers"
        end
      end
    end
  end

  desc "Link workspace with ms teams"
  task add_workspace_id_to_ms_teams: :environment do
    Integrations::MsTeams::Config.find_each do |ms_teams_config|
      default_workspace_id = ms_teams_config.company.default_workspace.id
      ms_teams_config.update!(workspace_id: default_workspace_id)
    end
  end

  desc "Add admin group privileges to all existing workspaces"
  task add_admin_group_privileges: :environment do

    companies_missing_admin_privileges = Group.where(name: "Admins").includes(:contributor, :company).each_with_object([]) do |group, result|
      result << group.company if group.contributor.privileges.where(name: "HelpTicket").blank?
    end

    companies_missing_admin_privileges.each do |company|
      # Find admin group
      admin_group_contributor = company.groups.find_by(name: 'Admins').contributor
      # Loop through all workspaces for this company
      company.workspaces.find_each do |workspace|
        Rails.logger.info("Adding admin privileges for workspace: #{workspace.id}")
        # Call create_privilege method for each workspace to create privileges
        create_privilege(workspace, admin_group_contributor)
      end
    end
  end

  desc "Create default quick view filters for all existing workspaces"
  task create_default_quick_view_filters: :environment do
    filters_data = [
      {
        title: "My Active Tickets",
        name: "My Active Tickets",
        active: false,
        id: 1,
        order: 1,
        is_default: true
      },
      {
        title: "All Active Tickets",
        name: "All Active Tickets",
        active: false,
        id: 2,
        order: 2,
        is_default: true
      },
      {
        title: "Unassigned",
        name: "Unassigned",
        active: false,
        id: 3,
        order: 3,
        is_default: true
      },
      {
        title: "Closed",
        name: "Closed",
        active: false,
        id: 4,
        order: 4,
        is_default: true
      }
    ]
    Workspace.find_each do |workspace|
      QuickViewFilter.create!(workspace_id: workspace.id, company_id: workspace.company_id, filters_data: filters_data)
    end
  end

  def create_privilege(workspace, admin_group_contributor)
    workspace.privileges.where(
      contributor: admin_group_contributor,
      name: 'HelpTicket',
      permission_type: 'write'
    ).first_or_create!
  end

  desc "Add two default task checklists to all workspaces"
  task add_default_checklists: :environment do
    Workspace.all.find_each do |workspace|
      workspace.create_default_checklists
      puts workspace.id
    rescue => e
      puts "Exception in workspace: #{workspace.id}"
      next
    end
  end

  desc "Create default email templates for old workspaces"
  task create_default_templates: :environment do
    Workspace.find_each do |workspace|
      workspace.create_default_email_templates
    end
  end

  desc "add default icon and color classes"
  task add_default_icon_and_color_classes: :environment do
    Workspace.find_each do |workspace|
      workspace.update_columns(icon_class: "genuicon-workspace", color_class: "primary")
    end
  end

  desc "Update existing QuickViewFilters for all workspaces with company_user_id nil"
  task update_quick_view_filters: :environment do
    Workspace.find_each do |workspace|
      updated_count = 0
      QuickViewFilter.where(company_user_id: nil, workspace_id: workspace.id).find_each do |quick_view_filter|
        filters_data = quick_view_filter.filters_data

        drafts_filter_found = false

        filters_data.each do |filter|
          if filter["name"] == "Closed"
            filter["id"] = 5
            filter["order"] = 5
          elsif filter["name"] == "Drafts"
            drafts_filter_found = true
          end
        end

        unless drafts_filter_found
          filters_data << {
            "title" => "Drafts",
            "name" => "Drafts",
            "active" => false,
            "id" => 4,
            "order" => 4,
            "is_default" => true
          }
        end

        quick_view_filter.update!(filters_data: filters_data)
        updated_count += 1
      end

      if updated_count > 0
        puts "Workspace ID #{workspace.id} has been fixed."
      else
        puts "Workspace ID #{workspace.id} had no QuickViewFilters to fix."
      end
    end
  end

  desc "Update QuickViewFilters for workspaces where company_user_id exists and add Drafts filter at the last"
  task add_drafts_to_existing_filters: :environment do
    Workspace.find_each do |workspace|
      updated_count = 0

      QuickViewFilter.where.not(company_user_id: nil).where(workspace_id: workspace.id).find_each do |quick_view_filter|
        filters_data = quick_view_filter.filters_data

        drafts_filter_exists = filters_data.any? { |f| f["name"] == "Drafts" }

        unless drafts_filter_exists
          closed_filter = filters_data.find { |f| f["name"] == "Closed" }

          if closed_filter
            closed_order = closed_filter["order"]

            closed_filter["order"] += 1

            filters_data.each do |filter|
              if filter["order"] > closed_order
                filter["order"] += 1
              end
            end

            filters_data.insert(filters_data.index(closed_filter), {
              "title" => "Drafts",
              "name" => "Drafts",
              "active" => false,
              "id" => filters_data.length + 1,
              "order" => closed_order,
              "is_default" => true
            })
          end

          quick_view_filter.update!(filters_data: filters_data)
          updated_count += 1
        end
      end

      if updated_count > 0
        puts "Workspace ID #{workspace.id} has been updated."
      else
        puts "Workspace ID #{workspace.id} had no QuickViewFilters to update."
      end
    end
  end

  desc "Enforce max character limit on workspace names"
  task enforce_max_char_limit: :environment do
    max_length = 60
    
    Workspace.where("LENGTH(name) > ?", max_length).find_each do |workspace|
      original_name = workspace.name
      workspace.update_column(:name, original_name[0, max_length])
      puts "Workspace name truncated from '#{original_name}' to '#{workspace.name}'"
    end
  end  
end
