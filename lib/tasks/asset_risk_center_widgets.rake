namespace :asset_risk_center_widgets do
  desc "Create default asset risk center widgets for existing companies"
  task create_default_asset_risk_center_widgets: :environment do

    Company.find_each do |company|
      puts "#{company.id} - #{company.name}"

      ManagedAssets::RiskCenter::CreateDefaultRiskCenterWidgets.new(company).call
      
      puts "Successfully created widgets for company: #{company.id}"
    end
  end

  desc "Prepopulate no software options for default widgets"
  task update_selected_options: :environment do
    begin
      Company.find_each do |company|
        puts "#{company.id} - #{company.name}"

        ManagedAssets::RiskCenter::CreateDefaultRiskCenterWidgets.new(company).create_widgets

        puts "Successfully updated widgets selected options for company: #{company.id}"
      end
    rescue StandardError => e
      puts "Failed to update selected options for company: #{company.id} - #{e.message}"
    end
  end

  desc "Copy name value to label for risk center widgets"
  task update_widget_label: :environment do
    AssetRiskCenterWidget.find_each do |widget|
      begin
        widget.update_column(:label, widget.name)
        puts "Widget label updated successfully for ID #{widget.id}"
      rescue StandardError => e
        puts "Failed to update widget with ID #{widget.id}: #{e.message}"
      end
    end
  end

  desc "Update Data Encryption widget in asset risk center"
  task manage_data_encryption_widgets: :environment do
    begin
      AssetRiskCenterWidget
        .where(name: 'Data Encryption')
        .update_all(selected_options: [])

      puts "Updated Data Encryption asset risk center widgets with empty selected_options."

      RiskCenterWidgetOption
        .where(widget_type: 'Data Encryption')
        .delete_all

      puts "Deleted Data Encryption widget options successfully."
    rescue => e
      puts "Failed to update Data Encryption widgets: #{e.message}"
    end
  end
end
