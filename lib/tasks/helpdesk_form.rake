namespace :helpdesk_form do
  desc "Adds helpdesk_custom_forms"
  task add_helpdesk_custom_forms: :environment do
    CustomForm.where(company_module: 'helpdesk').find_each do |form|
      form.helpdesk_custom_form = HelpdeskCustomForm.new(
        collect_closing_survey: form.collect_closing_survey,
        default: form.default,
        show_in_open_portal: form.show_in_open_portal
      )
      form.helpdesk_custom_form.save!
    end
  end
end
