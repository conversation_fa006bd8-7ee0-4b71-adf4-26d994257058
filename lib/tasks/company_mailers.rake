namespace :company_mailers do
  desc "Create company mailers"
  task create: :environment do
    count = 0
    Company.not_sample.find_each do |company|
      users = company.admin_company_users.order(:created_at)
      admin = users.first || company.company_users.first
      admin.company_user_mailers.find_each do |mailer|
        company_mailer = CompanyMailer.find_or_initialize_by(company: company, default_mailer: mailer.default_mailer)
        if (!company_mailer.id)
          company_mailer.opted_in = mailer.opted_in
          company_mailer.save!
          count += 1
        end
      end
    end
    puts "Created #{count} mailers"
  end

  desc "Create survey spent mailer"
  task add_survey_sent: :environment do
    default_mailer = DefaultMailer.find_by(event: 'survey_sent')
    Company.find_each do |company|
      company_mailer = CompanyMailer.find_or_create_by(default_mailer: default_mailer, company: company)
      company_mailer.update_automated_task
      company_mailer.automated_task.disabled_at = Time.zone.now unless company_mailer.automated_task.id
      company_mailer.automated_task&.save!
    end
  end
end
