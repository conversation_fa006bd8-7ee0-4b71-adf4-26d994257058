namespace :custom_form_fields do
  desc "Update 'location' fields to 'location_list' for IT General Request forms"
  task update_it_general_location_field: :environment do
    count = CustomFormFieldTemplate.joins(:custom_form_template)
                                    .where(custom_form_templates: { form_name: 'IT General Request' })
                                    .where(field_attribute_type: "location_list", name: "location")
                                    .update_all(name: "location_list")
    puts "#{count} record(s) updated successfully."
  rescue => e
    puts "An error occurred: #{e.message}"
  end

  desc "Update 'location' fields to 'location_list' for all custom form fields"
  task update_location_fields_name: :environment do
    count = CustomFormField.where(field_attribute_type: "location_list", name: "location")
                           .update_all(name: "location_list")
    puts "#{count} record(s) updated successfully."
  rescue => e
    puts "An error occurred: #{e.message}"
  end
end
