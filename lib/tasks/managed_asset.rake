namespace :managed_asset do
  desc "add hardware detail where it's missing to assets"
  task add_hardware_detail_to_assets: :environment do
    ActiveRecord::Base.transaction do
      ManagedAsset.includes(:asset_type, :hardware_detail).find_each do |asset|
        if asset.asset_type && ["Software", "Other"].include?(asset.asset_type.name) && asset.hardware_detail.nil?
          asset.hardware_detail = asset.hardware_detail_type.constantize.new if asset.hardware_detail_type.present?
          asset.save!
        end
      end
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
    end
  end

  desc "add OS version for MAC systems"
  task add_mac_os_versions: :environment do
    managed_assets = ManagedAsset.joins(:discovered_asset).joins(:asset_softwares).where("asset_softwares.name = ?",  "MAC OS")
    managed_assets.each do |managed_asset|
      asset_softwares = managed_asset.asset_softwares.where(name: "MAC OS")
      discovered_asset = managed_asset.discovered_asset
      os_version = discovered_asset.os_version
      os_name = discovered_asset.os_name
      software_name = ""
      os_version_build = os_version.gsub("Version ", "")

      if os_version_build.starts_with?('10')
        build_version = os_version_build.split(" ")[0].to_f
        full_version = os_version_build.split(" ")[0]

        case build_version
        when 10.15
          software_name = "Catalina (#{full_version})"
        when 10.14
          software_name = "Mojave (#{full_version})"
        when 10.13
          software_name = "High Sierra (#{full_version})"
        when 10.12
          software_name = "Sierra (#{full_version})"
        when 10.11
          software_name = "El Capitan (#{full_version})"
        when 10.10
          software_name = "Yosemite (#{full_version})"
        when 10.9
          software_name = "Mavericks (#{full_version})"
        when 10.8
          software_name = "Mountain Lion (#{full_version})"
        when 10.7
          software_name = "Lion (#{full_version})"
        when 10.6
          software_name = "Snow Leopard (#{full_version})"
        when 10.5
          software_name = "Leopard (#{full_version})"
        when 10.4
          software_name = "Tiger (#{full_version})"
        when 10.3
          software_name = "Panther (#{full_version})"
        when 10.2
          software_name = "Jaguar (#{full_version})"
        when 10.1
          software_name = "Puma (#{full_version})"
        when 10.0
          software_name = "Cheetah (#{full_version})"
        else
          software_name = "MAC OS (#{full_version})"
        end
      else
        software_name = "#{os_name} (#{os_version})"
      end

      asset_softwares.update_all(name: software_name)
    end
  end

  desc "move hardware details to managed assets"
  task move_hardware_details: :environment do
    ManagedAsset.find_each do |managed_asset|
      if managed_asset.hardware_detail.present?
        unless managed_asset.hardware_detail.class.name === "OtherDetail"
          # skip callbacks and audit.
          managed_asset.update_columns(mac_addresses: managed_asset.hardware_detail.mac_addresses,
                                       machine_serial_number: managed_asset.hardware_detail.serial_number)
        end
      end
    end
  end

  desc 'merge description and details keys in details field and make data uniq'
  task merge_description_and_details_keys_in_details: :environment do
    ManagedAsset.where("details IS NOT NULL AND details <> ''").find_each do |managed_asset|
      h = eval_to_hash(managed_asset.details)
      next if h.blank?

      description = h.delete('description')&.split(';')&.reject(&:blank?)&.map(&:strip) || []
      details = h.delete('details')&.split(';')&.reject(&:blank?)&.map(&:strip) || []
      details = (description + details).uniq.join('; ')
      h['details'] = "#{details}\;"

      managed_asset.update_columns(details: h.to_s)
    end
  end

  desc "unlink agent from merged assets"
  task unlink_agent_from_merged_assets: :environment do
    ManagedAsset.where(merged: true).update_all(agent_location_id: nil)
  end

  desc 'correct assets notes and details data'
  task correct_asset_notes_and_details_data: :environment do
    ManagedAsset.where("description IS NOT NULL AND description <> '' OR details IS NOT NULL AND details <> ''").find_each do |managed_asset|
      next if managed_asset.details.blank? && managed_asset.description.blank?

      notes = eval_to_hash(managed_asset.description)
      if notes.present? && notes.is_a?(Hash)
        if managed_asset.details.blank?
          puts "ManagedAsset(#{managed_asset.id}): Swapping notes with details"
          managed_asset.description, managed_asset.details = managed_asset.details, managed_asset.description
        else
          details = eval_to_hash(managed_asset.details)
          if details.present? && details.is_a?(Hash)
            puts "ManagedAsset(#{managed_asset.id}): Merging notes to details and nullifying notes"
            managed_asset.details = details.merge(notes).to_s
            managed_asset.description = ""
          else
            puts "ManagedAsset(#{managed_asset.id}): Swapping details with notes"
            managed_asset.description, managed_asset.details = managed_asset.details, managed_asset.description
          end
        end
      else
        details = eval_to_hash(managed_asset.details)

        if details.blank? && managed_asset.details.present?
          if managed_asset.description.present?
            puts "ManagedAsset(#{managed_asset.id}): Merging details to notes and nullifying details"
            managed_asset.description = "#{managed_asset.description}\n#{managed_asset.details}"
          else
            puts "ManagedAsset(#{managed_asset.id}): Assigning details to notes and nullifying details"
            managed_asset.description = managed_asset.details
          end
          managed_asset.details = ""
        end
      end
      managed_asset.update_columns(details: managed_asset.details, description: managed_asset.description)
    end
  end

  def eval_to_hash(val)
    JSON.parse(val.gsub('=>', ':').gsub("nil", "\"\""))
  rescue Exception => e
    puts "Not a hash: #{val}"
    nil
  end

  desc "delete soft deleted assets permanently"
  task deletion_of_soft_deleted_assets: :environment do
    ManagedAsset.where.not(deleted_at: nil).each do |asset|
      if asset.destroy
        company = asset.company
        company.discovered_assets.where(managed_asset_id: asset).destroy_all
      end
    end
  end

  desc "delete soft deleted assets permanently"
  task format_and_validate_mac_addresses: :environment do
    ManagedAsset.find_each do |asset|
      filtered_mac_addresses = []
      mac_addresses = asset.mac_addresses

      filtered_mac_addresses = mac_addresses.map {|mac_address| mac_address.upcase.gsub("-", ":") }.uniq

      asset.assign_attributes(mac_addresses: filtered_mac_addresses)
      asset.save_without_auditing
    end

    DiscoveredAsset.find_each do |asset|
      filtered_mac_addresses = []
      mac_addresses = asset.mac_addresses

      filtered_mac_addresses = mac_addresses.map {|mac_address| mac_address.upcase.gsub("-", ":") }.uniq
      asset.update_columns(mac_addresses: filtered_mac_addresses)
    end
  end

  desc "Send managed asset event emails"
  task event_emails: :environment do
    ManagedAssetEventEmailsWorker.perform_async
  end

  desc "Populate empty string to null"
  task assign_empty_string: :environment do
    ManagedAsset.find_each do |asset|
      if asset.hardware_detail.present?
        if asset.hardware_detail.class.name === "ComputerDetail"
          if asset.hardware_detail.hard_drive.nil?
            asset.hardware_detail.update_column(:hard_drive, "")
          end
          if asset.hardware_detail.processor.nil?
            asset.hardware_detail.update_column(:processor, "")
          end
          if asset.hardware_detail.memory.nil?
            asset.hardware_detail.update_column(:memory, "")
          end
        end
      end
    end
  end

  desc "Update managed assets description to empty string where nil"
  task assign_empty_string_to_description: :environment do
    ManagedAsset.where(description: nil).update_all(description: "")
  end

  desc "It identifies ids for assets which have false types attached."
  task detect_assets_with_incorrect_types: :environment do
    invalid = []
    network_types = ["router", "switch", "firewall", "wap"]
    computer_types = ["desktop", "laptop", "server", "virtual machine", "thin client"]
    mobile_types = ["mobile", "tablet"]
    phone_types = ["phone", "phone system"]
    printer_types = ["printer"]
    other_types = ["other"]
    ManagedAsset.all.find_each do |asset|
      type = asset.asset_type.name.downcase
      if asset.hardware_detail_type == "ComputerDetail"
        unless computer_types.include?(type)
          invalid << asset.id
        end
      elsif asset.hardware_detail_type == "Networkingdevicedetail"
        unless network_types.include? type
          invalid << asset.id
        end
      elsif asset.hardware_detail_type == "MobileDetail"
        unless mobile_types.include? type
          invalid << asset.id
        end
      elsif asset.hardware_detail_type == "PhoneDetail"
        unless phone_types.include? type
          invalid << asset.id
        end
      elsif asset.hardware_detail_type == "PrinterDetail"
        unless printer_types.include? type
          invalid << asset.id
        end  
      elsif asset.hardware_detail_type == "OtherDetail"
        unless other_types.include? type
          invalid << asset.id
        end    
      end
    end
  end

  desc "It update hardware detail of assets according to asset type."
  task update_assets_hardware_detail: :environment do
    assets_not_update = []
    ManagedAsset.where.not(company_asset_type_id: nil).each do |ma|
      asset_type_name = ma.asset_type.name
      if ["Apple Device", "Desktop", "Laptop", "Server", "Virtual Machine", "Thin Client", "Windows"].include?(asset_type_name)
        present_harware_detail = "ComputerDetail"
      elsif ["Phone", "Phone System"].include?(asset_type_name)
        present_harware_detail = "PhoneDetail"
      elsif ["Router", "Switch", "Firewall", "WAP"].include?(asset_type_name)
        present_harware_detail = "NetworkingDeviceDetail"
      elsif ["Mobile", "Tablet", "iPhone", "iPad"].include?(asset_type_name)
        present_harware_detail = "MobileDetail"
      elsif asset_type_name == "Printer"
        present_harware_detail = "PrinterDetail"
      elsif asset_type_name == "Other"
        present_harware_detail = "OtherDetail"
      else
        present_harware_detail = "OtherDetail"
      end
      if ma.hardware_detail.present? && ma.hardware_detail_type != present_harware_detail
        detail_to_destroy = ma.hardware_detail
        old_hardware_detail = ma.hardware_detail.dup
        ma.hardware_detail = ma.asset_type.hardware_detail_class&.new(managed_asset_id: ma.id)
        if ma.hardware_detail.present?
          valid_keys = ma.hardware_detail.attributes.keys - ["id", "created_at", "updated_at", "managed_asset_id"]
          valid_keys.each do |key|
            ma.hardware_detail[key.to_sym] = old_hardware_detail[key.to_sym] if ma.hardware_detail.attributes.keys.include?(key)
          end
        end
        if ma.save
          detail_to_destroy.destroy
        else
          assets_not_update << ma.id
        end
      end
    end
  end

  desc "It added missing managed asset id to its hardware detail."
  task add_managed_asset_id_in_hardware_detail: :environment do
    ManagedAsset.where.not(hardware_detail_id: nil).find_each do |asset|
      if asset.hardware_detail.present? && asset.hardware_detail.managed_asset_id.blank?
        asset.hardware_detail.update_column(:managed_asset_id, asset.id)
      end
    end
  end

  desc "It will keep only one cost for one managed_asset_id."
  task remove_duplicate_costs: :environment do
    ManagedAsset.find_each do |asset|
      costs = Cost.where(managed_asset_id: asset.id)
      if (costs.count > 1)
        lastest_record = costs.last.id
        costs = costs.where.not(id: lastest_record)
        costs.destroy_all
      end
    end
  end

  desc "udpate asset with missing hardware detail association"
  task update_missing_hardware_detail: :environment do
    ManagedAsset.includes(:hardware_detail)
      .where.not(hardware_detail_id: nil, hardware_detail_type: "OtherDetail")
      .find_each { |asset| update_missing_hardware_association(asset) if asset.hardware_detail.nil? }
  end

  def update_missing_hardware_association(asset)
    asset.hardware_detail = asset.asset_type.hardware_detail_class.new(managed_asset_id: asset.id)
    asset.save!
  end

  desc "It will convert all the data from notes to details column if asset is discovered"
  task convert_notes_data_to_details_column: :environment do
    assets = ManagedAsset.where.not(description: "")
    assets.find_each do |asset|
      if asset.description.include?"{\"description\"=>"
        asset.details = asset.description
        asset.description = ""
        asset.save_without_auditing
      end
    end
  end

  desc "It will make asset software type consistent"
  task make_os_consistent: :environment do
    AssetSoftware.where(software_type: "OS").update_all(software_type: "Operating System")
  end

  desc "It will add default Desktop asset type to all companies"
  task create_desktop_asset_types: :environment do
    attributes = AssetType.find_by(name: 'Desktop').attributes.except('id', 'created_at', 'updated_at')
    Company.find_each do |company|
      if company.asset_types.find_by(name: 'Desktop').blank?
        company.asset_types << CompanyAssetType.new(attributes)
      end
    end
  end

  desc "It will update assets source from azure_ad_assets to azure_ad_devices"
  task update_azure_ad_devices_assets_source: :environment do
    ManagedAsset.where(source:'azure_ad_assets').update_all(source: 'azure_ad_devices')
    DiscoveredAsset.where(source:'azure_ad_assets').update_all(source: 'azure_ad_devices')
    AssetSource.where(source:'azure_ad_assets').update_all(source: 'azure_ad_devices')
  end

  desc "It will update default of asset tag from nil to empty string"
  task set_default_of_asset_tag: :environment do
    ManagedAsset.where(asset_tag: nil).update_all(asset_tag: '')
  end

  desc "It will move data from string-type details to jsonb-type details"
  task migrate_details_to_jsonb: :environment do
    ManagedAsset.all.find_each do |asset|
      if asset.details_old.present?
        required_details = {}
        asset_details = JSON.parse(asset.details_old.gsub("=>", ":").gsub("nil", "null"))
        asset_details.keys.each do |key|
          if key == "device_type"
            required_details[key] = asset_details[key]
          else
            values_array = asset_details[key].split("\;") if asset_details[key].class == String
            if values_array.present?
              values_array.each do |val|
                if val.present?
                  pair = val.split(":", 2)
                  if pair.count == 2
                    required_details[pair[0].strip] = pair[1].strip
                  end
                end
              end
            end
          end
        end
        asset.details = required_details
      else
        asset.details = asset.details_old
      end
      asset.skip_callbacks = true
      asset.save_without_auditing
    end
  end

  desc "Add current status to company's asset statuses"
  task add_current_status_to_company_asset_status: :environment do
    previous_statuses = ManagedAsset.statuses.keys
    Company.find_each do |company|
      previous_statuses.each do |status|
        custom_status = CompanyAssetStatus.find_by(name: status.titleize, company: company)
        ManagedAsset.unscoped.where(company: company, status: status).update_all(company_asset_status_id: custom_status.id)
      end
    end
  end

  desc "Add new default statuses to company's asset statuses & update existing statuses"
  task add_new_statuses_to_company_asset_status: :environment do
    new_statuses = ManagedAsset.statuses.keys
    status_icons = {
      in_use: 'genuicon-checkmark',
      ready_to_use: 'nulodgicon-arrow-right-b',
      needs_attention: 'genuicon-exclamation',
      unusable: '&times;'
    }.with_indifferent_access

    Company.find_each do |company|
      new_statuses.each do |new_status|
        status_name = new_status == 'ready_to_use' ? 'Ready to Use' : new_status.titleize
        comp_status = CompanyAssetStatus.find_by(name: status_name, company: company)
        if comp_status.present?
          comp_status.update(icon: status_icons[new_status])
        else
          company.asset_statuses.create(name: status_name, icon: status_icons[new_status])
        end
      end
    end
    CompanyAssetStatus.where(name: 'In Repair').update_all(icon: '&times;')
    CompanyAssetStatus.where(name: 'Replace Soon').update_all(icon: 'genuicon-exclamation')
  end

  desc "Add managed asset id to it's hardware details"
  task add_managed_asset_id_to_hardware_detail: :environment do
    ManagedAsset.unscoped.where.not(hardware_detail_id: nil).find_each do |managed_asset|
      hardware_detail = managed_asset.hardware_detail
      hardware_detail.update_columns(managed_asset_id: managed_asset.id) if hardware_detail.present? && hardware_detail.managed_asset_id.nil?
    end
  end

  desc "remove hardware detail in import assets which have other detail as a hardware detail type"
  task remove_hardware_deatil: :environment do
    ManagedAsset.where(hardware_detail_type: "OtherDetail", source: "uploaded").find_each do |managed_asset|
      hardware_detail = managed_asset.hardware_detail
      managed_asset.hardware_detail.destroy if hardware_detail.present?
    end
  end

  desc "Create SmartAlerts for all ManagedAssets where smart_alerts are empty"
  task create_smart_alerts: :environment do
    created_count = 0

    ManagedAsset.left_outer_joins(:smart_alerts).where(smart_alerts: { id: nil }).find_in_batches do |assets|
      smart_alerts = assets.map do |asset|
        { managed_asset_id: asset.id }
      end
      SmartAlert.upsert_all(smart_alerts) if smart_alerts.any?
      created_count += smart_alerts.count
      puts "Created #{smart_alerts.count} SmartAlerts for #{assets.count} ManagedAssets"
    end

    puts "Total ManagedAssets with new SmartAlerts created: #{created_count}"
  end

  desc "Create AssignmentInformation for all ManagedAssets where assignment_information is missing"
  task create_assignment_information: :environment do
    created_count = 0

    ManagedAsset.left_outer_joins(:assignment_information).where(assignment_informations: { id: nil }).find_each(batch_size: 1000) do |managed_asset|
      managed_asset.create_assignment_information
      created_count += 1
    end

    puts "Total AssignmentInformation records created: #{created_count}"
  end
end
