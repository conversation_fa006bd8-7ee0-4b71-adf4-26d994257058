namespace :grant_access do
  desc "Grant access to company user where user confirmed accounts"
  task grant_user_access: :environment do
    users = User.all
    users.each do |user|
      if user.has_confirmed_email?
        company_user = user.company_users.first
        if company_user.present? && company_user.granted_access_at == nil
          company_user.granted_access_at = DateTime.current
          company_user.save!
        end
      end
    end
  end
end
