namespace :free_trial_expired do
  desc "It will send free trial expired mail to company admins"
  task send_free_trial_expired_email: :environment do
    level = Rails.logger.level
    Rails.logger = ActiveSupport::Logger.new('log/free_trial_expired_email.log')
    Rails.logger.level = level
    ActiveRecord::Base.logger = Rails.logger
    CronLock.lock("free_trial_expired:send_free_trial_expired_email") do
      Company.not_sample.find_each do |company|
        if (!company.has_current_free_trial? && !company.has_active_subscription?)
          trial_end_date = company.free_trial_expiration_date
          if trial_end_date.present? && trial_end_date == Date.yesterday
            SendFreeTrialExpiredEmailWorker.perform_async(company['id'])
          end
        end
      end
    end
  end
end
