namespace :add_new_asset_types do
  desc "add windows and apple device to asset types"
  task add_windows_apple_asset_type: :environment do
    windows_device = AssetType.find_by(name: "Windows")
    apple_device = AssetType.find_by(name: "<PERSON> Device")
    iphone_device = AssetType.find_by(name: "iPhone")
    ipad_device = AssetType.find_by(name: "iPad")
    tv_device = AssetType.find_by(name: "Tv")
    dongle_device = AssetType.find_by(name: "<PERSON><PERSON>")
    
    Company.find_each do |company|
      company.asset_types.find_or_create_by(
        name: windows_device.name,
        parent_type_id: windows_device.parent_type_id,
        has_child: windows_device.has_child,
        ancestry: windows_device.ancestry,
        private_asset_attributes: windows_device.private_asset_attributes,
        nested_attributes: windows_device.nested_attributes
      )
      company.asset_types.find_or_create_by(
        name: apple_device.name,
        parent_type_id: apple_device.parent_type_id,
        has_child: apple_device.has_child,
        ancestry: apple_device.ancestry,
        private_asset_attributes: apple_device.private_asset_attributes,
        nested_attributes: apple_device.nested_attributes
      )
      company.asset_types.find_or_create_by(
        name: iphone_device.name,
        parent_type_id: iphone_device.parent_type_id,
        has_child: iphone_device.has_child,
        ancestry: iphone_device.ancestry,
        private_asset_attributes: iphone_device.private_asset_attributes,
        nested_attributes: iphone_device.nested_attributes
      )
      company.asset_types.find_or_create_by(
        name: ipad_device.name,
        parent_type_id: ipad_device.parent_type_id,
        has_child: ipad_device.has_child,
        ancestry: ipad_device.ancestry,
        private_asset_attributes: ipad_device.private_asset_attributes,
        nested_attributes: ipad_device.nested_attributes
      )
      company.asset_types.find_or_create_by(
        name: tv_device.name,
        parent_type_id: tv_device.parent_type_id,
        has_child: tv_device.has_child,
        ancestry: tv_device.ancestry,
        private_asset_attributes: tv_device.private_asset_attributes,
        nested_attributes: tv_device.nested_attributes
      )
      company.asset_types.find_or_create_by(
        name: dongle_device.name,
        parent_type_id: dongle_device.parent_type_id,
        has_child: dongle_device.has_child,
        ancestry: dongle_device.ancestry,
        private_asset_attributes: dongle_device.private_asset_attributes,
        nested_attributes: dongle_device.nested_attributes
      )
    end
  end

  desc "update windows and apple device to asset types"
  task update_windows_apple_asset_type: :environment do
    windows_device = AssetType.find_by(name: "Windows")
    apple_device = AssetType.find_by(name: "Apple Device")
    iphone_device = AssetType.find_by(name: "iPhone")
    ipad_device = AssetType.find_by(name: "iPad")
    tv_device = AssetType.find_by(name: "Tv")
    dongle_device = AssetType.find_by(name: "Dongle")

    Company.find_each do |company|
      company.asset_types.find_by(name: windows_device.name)&.update_columns(
        parent_type_id: windows_device.parent_type_id,
        has_child: windows_device.has_child,
        ancestry: windows_device.ancestry,
        private_asset_attributes: windows_device.private_asset_attributes,
        nested_attributes: windows_device.nested_attributes
      )
      company.asset_types.find_by(name: apple_device.name)&.update_columns(
        parent_type_id: apple_device.parent_type_id,
        has_child: apple_device.has_child,
        ancestry: apple_device.ancestry,
        private_asset_attributes: apple_device.private_asset_attributes,
        nested_attributes: apple_device.nested_attributes
      )
      company.asset_types.find_by(name: iphone_device.name)&.update_columns(
        parent_type_id: iphone_device.parent_type_id,
        has_child: iphone_device.has_child,
        ancestry: iphone_device.ancestry,
        private_asset_attributes: iphone_device.private_asset_attributes,
        nested_attributes: iphone_device.nested_attributes
      )
      company.asset_types.find_or_create_by(name: ipad_device.name)&.update_columns(
        parent_type_id: ipad_device.parent_type_id,
        has_child: ipad_device.has_child,
        ancestry: ipad_device.ancestry,
        private_asset_attributes: ipad_device.private_asset_attributes,
        nested_attributes: ipad_device.nested_attributes
      )
      company.asset_types.find_by(name: tv_device.name)&.update_columns(
        parent_type_id: tv_device.parent_type_id,
        has_child: tv_device.has_child,
        ancestry: tv_device.ancestry,
        private_asset_attributes: tv_device.private_asset_attributes,
        nested_attributes: tv_device.nested_attributes
      )
      company.asset_types.find_or_create_by(name: dongle_device.name)&.update_columns(
        parent_type_id: dongle_device.parent_type_id,
        has_child: dongle_device.has_child,
        ancestry: dongle_device.ancestry,
        private_asset_attributes: dongle_device.private_asset_attributes,
        nested_attributes: dongle_device.nested_attributes
      )
    end
  end
end
