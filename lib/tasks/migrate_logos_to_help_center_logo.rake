namespace :custom_domain do
  desc "Migrate logos and favicons from CustomDomain to HelpCenterLogo"
  task migrate_logos_to_help_center_logo: :environment do
    custom_domain_error_ids = []

    CustomDomain.find_each do |custom_domain|
      begin
        next unless custom_domain.respond_to?(:logo) || custom_domain.respond_to?(:favicon)

        if custom_domain.logo.attached? || custom_domain.favicon.attached?
          help_center_logo = HelpCenterLogo.new(company: custom_domain.company)

          if custom_domain.logo.attached?
            help_center_logo.logo.attach(custom_domain.logo.blob)
            puts "Migrated logo for CustomDomain with ID #{custom_domain.id}"
          end

          if custom_domain.favicon.attached?
            help_center_logo.favicon.attach(custom_domain.favicon.blob)
            puts "Migrated favicon for CustomDomain with ID #{custom_domain.id}"
          end

          if help_center_logo.save
            puts "Successfully migrated logo and favicon for CustomDomain with ID #{custom_domain.id} to HelpCenterLogo with ID #{help_center_logo.id}"
          else
            custom_domain_error_ids << custom_domain.id
            puts "Failed to save HelpCenterLogo for CustomDomain with ID #{custom_domain.id}"
          end
        else
          puts "No logo or favicon attached for CustomDomain with ID #{custom_domain.id}, skipping."
        end
      rescue => e
        custom_domain_error_ids << custom_domain.id
        puts "Error migrating CustomDomain with ID #{custom_domain.id}: #{e.message}"
      end
    end

    puts "Migration complete!"
    puts "Error CustomDomain IDs: #{custom_domain_error_ids}"
  end
end
