namespace :asset_attachments do
  desc "remove all extra asset attachments which don't belong to any asset"
  task remove_orphan_attachments: :environment do
    ActiveRecord::Base.transaction do
      all_managed_asset_ids = ManagedAsset.ids

      ManagedAssetAttachment.where.not(managed_asset_id: all_managed_asset_ids).where(library_document_id: nil).delete_all
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
    end
  end
end
