namespace :test_user do
  desc "Create super user for test mode."
  task create_test_super_user: :environment do
    user = User.new(email: Rails.application.credentials.admin[:email],
                    first_name: "Super admin",
                    last_name: "admin" ,
                    super_admin: true,
                    password: Rails.application.credentials.admin[:password],
                    has_confirmed_email: true,
                    has_seen_sample_onboarding: true )
    user.skip_invitation = true
    user.save!
  end
end