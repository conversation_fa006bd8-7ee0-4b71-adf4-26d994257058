namespace :integration_apps do
  desc "update nil friendly names to names"
  task update_friendly_names: :environment do
    Integrations::App.find_each do |app|
      if app.friendly_name.nil? || (app.friendly_name == app.name && app.name == app.name.upcase)
        app.update_columns(friendly_name: app.name.titleize)
      end
    end
  end

  desc "Prettify's ugly imported friendly names"
  task prettify_friendly_names: :environment do
    Integrations::App.find_each do |app|
      if app.friendly_name == app.name && app.name == app.name.upcase
        app.update_columns(friendly_name: app.name.titleize)
      end
    end
  end
end
