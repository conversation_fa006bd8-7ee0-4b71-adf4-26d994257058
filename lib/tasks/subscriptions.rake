namespace :subscriptions do
  desc "Set subscription subscriber IDs"
  task set_subscriber_id: :environment do
    Subscription.find_each do |subscription|
      company = subscription.company
      if company.present?
        company_user = company.company_users.joins(:user).where("users.email = ?", subscription.email)&.first
        if company_user.present?
          subscription.update(subscriber_id: company_user.id)
        else
          company_admin = company.admin_company_users.first
          subscription.update(subscriber_id: company_admin.id)
        end
      end
    end
  end

  desc "create a subscription for the company"
  task :create_subscription, [:subdomain, :subscription_plan] => :environment do |t, args|
    company = Company.find_by(subdomain: args[:subdomain])
    subs_plan = SubscriptionPlan.find_by(interval: args[:subscription_plan])
    return unless company.present? && subs_plan.present?
    end_date = subs_plan.month? ? Date.current + 1.month : Date.current + 1.year
    subscription = company.subscription
    subscription ||= company.create_subscription!(status: 0, subscription_plan_id: subs_plan.id, start_date: Date.current, end_date: end_date)
    subscription.subscription_activities.create!(owner_id: company.admin_company_users.first.id, activity_type: "subscription", activity_action: "created")
    subscription.stripe_transactions.create!(amount: subs_plan.price / 100, status: 0, initiated_at: DateTime.current, company_id: company.id)
  end

  desc "adding end dates for manually created subscriptions"
  task add_end_date: :environment do
    Subscription.where(stripe_subscription_id: nil, end_date: nil).find_each do |subscription|
      subs_plan = subscription.subscription_plan
      end_date = subs_plan.month? ? subscription.start_date + 1.month : subscription.start_date + 1.year
      subscription.update(end_date: end_date)
    end
  end

  desc "update status when end date for manually created subscriptions has expired"
  task update_subscription_status: :environment do
    Subscription.where(stripe_subscription_id: nil, status: 'active')
                .where("end_date < ?", Date.current)
                .update_all(status: 'insolvent')
  end

  desc "It will update all subscriptions module type"
  task update_subscriptions_module_type: :environment do
    Subscription.update_all(module_type: 'full_platform')
  end

  desc "It will update erronous module types of subscriptions"
  task update_subscription_module_type: :environment do
    Subscription.where(module_type: 'basic').update_all(module_type: "full_platform")
  end

  desc "update legacy companies subscription which will be expiring the next day"
  task update_old_company_subscriptions: :environment do
    # need to run this script after 2nd July, 2024
    UpdateOldCompanySubscriptionsWorker.perform_async
  end
end

def cancel_stripe_subscription(customer, user, subscription)
  StripeSubscriptionService.new({ customer: customer, current_user: user, subscription: subscription }).cancel_subscription
end

def create_stripe_subscription(customer, user, subscription_plan)
  StripeSubscriptionService.new({ customer: customer, current_user: user, subscription_plan: subscription_plan }).create_subscription
end

def retrieve_subscription(subscription)
  Stripe::Subscription.retrieve(subscription.stripe_subscription_id)
end

def update_plan(company, subscription, result, subscription_plan)
  subscription.update(
    status: "active",
    stripe_subscription_id: result.id,
    end_date: nil,
    start_date: Time.at(result["current_period_start"]).in_time_zone(company.timezone).to_date,
    subscription_plan_id: subscription_plan.id,
    module_type: subscription_module_type(subscription_plan.name)
  )
end

def create_plan(company, subscription_plan, result, customer)
  Subscription.create(
    company: company,
    subscription_plan_id: subscription_plan.id,
    status: "active",
    stripe_subscription_id: result.id,
    start_date: Date.today,
    subscriber_id: company.admin_company_users.first.id,
    email: customer.email,
    module_type: subscription_module_type(subscription_plan.name)
  )
end

def subscription_module_type(plan_name)
  if ["Basic Plan", "Yearly Plan"].include?(plan_name)
    return "full_platform"
  end

  words = plan_name.split(' ')
  words.pop
  module_name = words.join(' ')
  module_name.downcase.gsub(" ", "_")
end

def create_subscription_log(company, new_plan_name, existing_plan_name, user_email, status, response)
  log_params = {
    company_id: company.id,
    company_subdomain: company.subdomain,
    response: response,
    new_plan: new_plan_name,
    existing_plan: existing_plan_name,
    user_email: user_email,
    status: status
  }

  UpdateSubscriptionLog.create(log_params)
end

def update_existing_subscription(company, subscription, new_stripe_subscription, subscription_plan)
  subscription.update_columns(
    stripe_subscription_id: new_stripe_subscription.id,
    subscription_plan_id: subscription_plan.id,
    status: 'insolvent'
  )
  company.update_column(:is_legacy_company, false)
end

def create_new_subscription(company, new_stripe_subscription, subscription_plan, user)
  subscription = Subscription.new(
    company_id: company.id,
    subscription_plan_id: subscription_plan.id,
    stripe_subscription_id: new_stripe_subscription.id,
    status: 'insolvent',
    start_date: Date.today,
    email: user.email,
    module_type: subscription_module_type(subscription_plan.name),
    subscriber_id: user.company_users.first.id
  )
  subscription.save
  company.update_column(:is_legacy_company, false)
end
