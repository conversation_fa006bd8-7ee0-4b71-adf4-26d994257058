namespace :help_ticket_activity do
  desc "Add ticket subject and ticket number to activity data"
  task add_activity_type_temporary: :environment do
    HelpTicketActivity.find_each do |activity|
      activity.update(activity_type_temporary: activity.activity_type) if activity.activity_type
    end
  end

  desc "Save data to temporary column"
  task save_data_to_temp_data: :environment do
    HelpTicketActivity.find_each do |activity|
      activity.update_columns(temp_data: activity.data) if activity.data
    end
  end

  desc "Add ticket subject to help ticket activity data where subject is missing"
  task add_ticket_subject_to_help_ticket_activity_data: :environment do
    HelpTicketActivity.where("data->'ticket_subject' IS NULL").find_each do |activity|
      data = activity.data
      data['ticket_subject'] = activity.help_ticket.subject
      activity.update_columns(data: data)
    end
  end

  desc "Add key activity_label in activities with type static_text"
  task add_key_activity_label_in_static_text: :environment do
    HelpTicketActivity.where(activity_type: "static_text").find_each do |activity|
      value = activity.data
      if activity.activity_type_temporary.present?
        value["activity_label"] = activity.activity_type_temporary
        activity.update_columns(data: value)
      end
    end
  end

  desc "delete activities with type static_text"
  task delete_static_text_activities: :environment do
    HelpTicketActivity.where(activity_type: "static_text").find_each do |activity|
      if activity.activity_type_temporary.blank?
        activity.destroy
      end
    end
  end

  desc "Delete incorrect ticket activities"
  task delete_invalid_staff_list_activities: :environment do
    activities = []
    HelpTicketActivity.where(activity_type: "people_list").find_each do |act|
      activities << act.id if act.data["current_value"] == nil && act.data["previous_value"] == nil
    end
    HelpTicketActivity.where(id: activities).destroy_all
  end

  desc "Add ticket subject and ticket number to activity data"
  task add_ticket_subject_and_number_to_data: :environment do
    HelpTicketActivity.find_each do |activity|
      data = activity.data
      if activity.help_ticket.custom_form_values
        data["ticket_subject"] = activity.help_ticket.custom_form_values.select { |value| value.custom_form_field.label == "Subject" }&.first&.value
      end
      data["ticket_number"] = activity.help_ticket.ticket_number
      data["owner_id"] = activity.owner_id
      activity.update_columns(data: data)
    end
  end

  desc "Convert old activities to custom forms paradigm"
  task convert_preexisting_activities: :environment do
    activities = HelpTicketActivity.includes(help_ticket: { company: [:company_users, :locations] } )
    activities.find_each do |activity|
      @previous_activity_type = activity.activity_type_temporary
      @new_current_val = nil
      @new_previous_val = nil
      data = activity.data

      if task_activities.include?(@previous_activity_type)
        activity.update_columns(activity_type: @previous_activity_type)
        next
      elsif @previous_activity_type == "created"
        activity.help_ticket.company.company_users.each do |cu|
          @new_current_val = cu.contributor.id if defined?(cu.full_name) && data['current_creator'] == cu.full_name
          @new_previous_val = nil
        end
      elsif @previous_activity_type == "location" # location was saving the name vs the ID
        data.each do |k, v|
          loc_name_current = v if k.include?("current")
          loc_name_previous = v if k.include?("previous")
          activity.help_ticket.company.locations.each do |location|
            @new_current_val = location.id if loc_name_current == location.name
            @new_previous_val = location.id if loc_name_previous == location.name
          end
        end
      elsif @previous_activity_type == "delete_time_spent"
        @new_current_val = nil
        @new_previous_val = data["current_time_spent"]
        @previous_activity_type = "time_spent"
      elsif @previous_activity_type == "create_time_spent"
        @new_current_val = data["current_time_spent"]
        @new_previous_val = nil
        @previous_activity_type = "time_spent"
      elsif @previous_activity_type == "update_time_spent"
        @new_current_val = data["current_time_spent"]
        @new_previous_val = data["previous_time_spent"]
        @previous_activity_type = "time_spent"
      else
        data.each do |k, v|
          @new_current_val = v if (k.include?("current") || k.include?("new") || k.include?("note_body"))
          @new_previous_val = v if k.include?("previous")
        end
      end

      if @previous_activity_type == 'note'
        activity.update_columns(
          activity_type: "note",
          data: {
            new_comment_body: @new_current_val,
            previous_comment_body: @new_previous_val,
            activity_label: activity_label,
            ticket_subject: activity.help_ticket.subject,
            ticket_number: activity.help_ticket.ticket_number,
            owner_id: activity.owner_id
          }
        )
      else
        activity.update_columns(
          activity_type: activity_type,
          data: {
            previous_value: @new_previous_val,
            current_value: @new_current_val,
            activity_label: activity_label,
            ticket_subject: activity.help_ticket.subject,
            ticket_number: activity.help_ticket.ticket_number,
            owner_id: activity.owner_id
          }
        )
      end
    end
  end

  desc "update_merge_ticket_activities"
  task update_merge_ticket_activities: :environment do
    HelpTicketActivity.where(activity_type_temporary: "merge_ticket").find_each do |activity|
      if activity.data["child_tickets"].present? && activity.data["parent_ticket"].present?
        data =  activity.temp_data
        company = activity.help_ticket.company
        child_tickets = ""
        data["child_tickets"].split.each_with_index do |child_ticket, index|
          subject = company.help_tickets.find_by_ticket_number(child_ticket.tr('#', ''))["subject"]
          child_tickets = child_tickets + child_ticket.tr(',', '') + " " + subject
          child_tickets = child_tickets + ", " if index + 1 != data["child_tickets"].split.length
        end

        data["type"] = "parent"
        data["ticket_number"] = data["parent_ticket"]
        data["ticket_subject"] = company.help_tickets.find_by_ticket_number(data["parent_ticket"])["subject"]
        data["child_tickets"] = child_tickets
        activity.update_columns(data: data)
        activity.update_columns(activity_type: "merge_ticket")
      end
    end
  end

  desc "update_external_user_activities"
  task update_external_user_activities: :environment do
    HelpTicketActivity.all.find_each do |activity|
      unless activity.data.include?("ticket_number")
        data = activity.data
        data["ticket_number"] = activity.help_ticket.ticket_number.to_s
        activity.update_columns(data: data)
      end
    end
  end

  desc "Update activity_type to 'source' for HelpTicketActivity with activity_type 'move_ticket' and 'source' key in data"
  task update_activity_type: :environment do
    batch_size = 5000
    help_ticket_activities = HelpTicketActivity.where(activity_type: "move_ticket").where("data ? 'source'")

    help_ticket_activities.find_in_batches(batch_size: batch_size) do |batch|
      updated_count = HelpTicketActivity.where(id: batch.pluck(:id)).update_all(activity_type: "source")
      puts "Updated #{updated_count} HelpTicketActivity records in this batch."
    end
  end

  def activity_type
    if new_list[@previous_activity_type]
      @previous_activity_type
    else
      get_new_activity_type
    end
  end

  def activity_label
    if new_list[@previous_activity_type]
      @previous_activity_type.titleize
    elsif @previous_activity_type == "created"
      "Created By"
    else
      @previous_activity_type&.titleize&.pluralize
    end
  end

  def get_new_activity_type
    if staff_list_activities.include?(@previous_activity_type)
      return 'people_list'
    elsif @previous_activity_type == "impacted_device"
      return 'asset_list'
    elsif @previous_activity_type == "location"
      return 'location_list'
    elsif @previous_activity_type == "archived"
      ## TODO: Deal with saving archiving activity
    elsif @previous_activity_type == "merge_ticket"
      ## TODO: Deal with saving archiving activity
    elsif @previous_activity_type == "description"
      return 'rich_text'
    end
  end

  # ones that are not in the new list
  def staff_list_activities
    %w(created assignment assigned_contributor)
  end

  def task_activities
    %w(create_task create_checklist_task delete_task delete_checklist_task due_date completed_date task_assignees task_description)
  end

  def new_list
    HelpTicketActivity.activity_types
  end
end
