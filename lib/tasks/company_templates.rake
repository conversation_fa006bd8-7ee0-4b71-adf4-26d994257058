namespace :company_templates do
  desc 'It will create a default template for company which all msp items selected'
  task create_default_company_template: :environment do
    Company.includes(
      :company_templates,
      :company_users,
      :groups,
      :msp_categories,
      :msp_templates_custom_forms,
      :msp_asset_types,
      :msp_automated_tasks,
      :msp_snippets,
      :msp_templates_documents,
      :msp_templates_helpdesk_faqs,
      :msp_blocked_keywords
    ).find_each do |company|
      next if company.company_templates.find_by(name: 'Default Template').present?
      default_template = company.company_templates.create(
        name: 'Default Template',
        description: 'Standard template for child companies'
      )

      template_data_set = TemplateDataSet.new
      template_data_set.company_template_id = default_template.id
      template_data_set.people = company.company_users.pluck(:id)
      template_data_set.groups = company.groups.where.not(name: 'Everyone').pluck(:id)
      template_data_set.categories = company.msp_categories.pluck(:id)
      template_data_set.custom_forms = company.msp_templates_custom_forms.pluck(:id)
      template_data_set.asset_types = company.msp_asset_types.pluck(:id)
      template_data_set.automated_tasks = company.msp_automated_tasks.pluck(:id)
      template_data_set.responses = company.msp_snippets.pluck(:id)
      template_data_set.documents = company.msp_templates_documents.pluck(:id)
      template_data_set.faqs = company.msp_templates_helpdesk_faqs.pluck(:id)
      template_data_set.blocked_keywords = company.msp_blocked_keywords.pluck(:id)

      template_data_set.save
    end
  end
end
