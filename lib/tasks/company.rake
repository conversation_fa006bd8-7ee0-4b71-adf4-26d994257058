namespace :company do
  desc "It will create default groups for all companies"
  task create_default_groups: :environment do
    def create_company_default_groups company
      admin_group = company.groups.find_or_initialize_by(name: 'Admins')
      admin_group.default = true
  
      if admin_group.new_record?
        admin_group.save!
        privileges = []
        Privilege::PRIVILEGES.each do |name|
          privileges << admin_group.group_privileges.new(name: name, permission_type: 'write')
        end
        GroupPrivilege.import privileges
      else
        admin_group.save!
      end
  
      agent_group = company.groups.find_or_initialize_by(name: 'Help Desk Agents')
      agent_group.default = true
  
      if agent_group.new_record?
        agent_group.save!
        agent_group.group_privileges.create(name: "HelpTicket", permission_type: 'write')
      else
        agent_group.save!
      end
    end

    Company.find_each do |company|
      create_company_default_groups company
    end
  end
  
  desc "It will return an array having company subdomains expired since 30 or more days"
  task get_companies_expired_since_30_days: :environment do
    expired_companies = []
    Company.find_each do |company|
      if company.expired_since_30_days?
        expired_companies << company.subdomain
      end
    end
  end

  desc "It will return company subdomains expired since 30 or more days with little amount of data"
  task get_little_companies_expired_since_30_days: :environment do
    def is_massive? company
      max_count = [company.managed_assets.count,
                   company.contracts.count,
                   company.vendors.count,
                   company.telecom_services.count,
                   company.telecom_providers.count,
                   company.help_tickets.count
                  ].max
      max_count >= 100
    end

    expired_companies = []
    Company.find_each do |company|
      if company.expired_since_30_days? && !is_massive?(company)
        expired_companies << company.subdomain
      end
    end
  end

  desc "It will poulate last_logged_in_at for existing companies"
  task populate_last_logged_in_at: :environment do
    Company.not_sample.find_each do |company|
      admin = company.admin_company_users.active.order("last_logged_in_at desc")
      company.update_attribute('last_logged_in_at', admin.first.last_logged_in_at) if admin.first.present?
    end
  end

  desc "Delete unnecessary logs"
  task del_unnecessary_logs: :environment do
    Audited::Audit.where(auditable_type: "ManagedAsset").find_each do |log|
      unless ManagedAsset.find_by(id: log.auditable_id)
        Audited::Audit.where(associated_type: "ManagedAsset", associated_id: log.auditable_id).or(Audited::Audit.where(auditable_type: "ManagedAsset", auditable_id: log.auditable_id)).destroy_all
      end
    end
    Audited::Audit.where(auditable_type: "Contract").find_each do |log|
      unless Contract.find_by(id: log.auditable_id)
        Audited::Audit.where(associated_type: "Contract", associated_id: log.auditable_id).or(Audited::Audit.where(auditable_type: "Contract", auditable_id: log.auditable_id)).destroy_all
      end
    end
  end

  desc "It will add a default logo URL to companies that do not have a logo present"
  task add_default_logos: :environment do
    Company.not_sample.all.each do |company|
      begin
        unless company.logo.exists?
          company.logo = URI.parse(company.fetch_logo).open
          company.save
        end
        if company.default_logo_url.blank?
          company.update(default_logo_url: "https://s3.amazonaws.com/nulodgic-static-assets/images/location_defaults/default#{rand(1..16)}_thumbnail.png")
        end
      rescue => e
        Rails.logger.warn "Issue adding logo to company ID #{company.id}. Error message: #{e.message}"
        next
      end
    end
  end

  task add_original_logos: :environment do
    Company.not_sample.where(original_logo_url: nil).find_each do |company|
      begin
        if company.logo.attached?
          company.original_logo_url = company.logo.url
          company.save!
        end
      rescue => e
        Rails.logger.warn "Issue adding original logo URL to company ID #{company.id}. Error message: #{e.message}"
        next
      end
    end
  end

  task add_active_storage_original_logos: :environment do
    Company.not_sample.joins(:logo_attachment).find_each do |company|
      company.update_columns(original_logo_url: company.logo.url) if company.logo.attached?
    end
  end

  desc "It will delete duplicate discovered users"
  task delete_duplicate_discovered_users: :environment do
    Company.all.each do |company|
      discovered_users_group = company.discovered_users.group_by{|discovered_user| [discovered_user.email] }
      discovered_users_group.values.each do |duplicates|
        first_one = duplicates.shift
        duplicates.each{|double| double.destroy}
      end
    end
  end

  desc "create default help desk settings"
  task create_default_helpdesk_settings: :environment do
    Company.find_each do |c|
      DefaultHelpdeskSetting.find_each do |setting|
        c.helpdesk_settings.find_or_create_by(company_id: c.id, default_helpdesk_setting_id: setting.id)
      end
    end
  end

  desc "update default help desk settings"
  task update_default_helpdesk_settings: :environment do
    old_setting = DefaultHelpdeskSetting.find_by(setting_type: "allow_making_email_cc_users_ticket_impacted_users")
    if old_setting
      new_setting = DefaultHelpdeskSetting.find_by(setting_type: "allow_making_email_recipients_ticket_impacted_users")
      HelpdeskSetting.where(default_helpdesk_setting_id: old_setting.id).update_all(default_helpdesk_setting_id: new_setting.id)
      old_setting.destroy!
    end
  end

  desc "create default asset preference columns"
  task create_default_asset_preference: :environment do
    Company.find_each do |c|
      c.create_asset_preference!(preference: ManagedAsset::SELECTED_COLUMNS) if !c.asset_preference.present?
    end
  end

  desc "create default discovered asset preference columns"
  task create_default_discovered_asset_preference: :environment do
    Company.find_each do |c|
      c.create_discovered_asset_preference!(preference: ManagedAsset::DISC_ASSET_SELECTED_COLUMNS) if !c.discovered_asset_preference.present?
    end
  end

  desc "create company mailers"
  task create_company_mailers: :environment do
    Company.find_each do |company|
      admin = company.admin_company_users.first

      admin&.company_user_mailers&.each do |company_user_mailer|
        CompanyMailer.find_or_create_by!(default_mailer: company_user_mailer.default_mailer, company: company) do |mailer|
          mailer.opted_in = company_user_mailer.opted_in
          mailer.automated_task&.save!
          mailer.save!
        end
      end
    end
  end

  desc "Create apps logs option for each company."
  task add_companies_apps_logs_options: :environment do
    Company.find_each do |company|
      AppsLogsOption.create(company_id: company.id)
    end
  end

  desc "Create ticket list view options for each company."
  task add_ticket_list_view_options: :environment do
    Company.find_each do |company|
      company.create_help_ticket_preference!(preference: HelpTickets::HelpTicketPreference.new(company).selected_columns)
    end
  end

  desc "Deleting unnecessary privileges"
  task del_unnecessary_privileges: :environment do
    Privilege.where(contributor_id: nil, company_user_id: nil).destroy_all
    Privilege.where(contributor_id: nil).find_each do |p|
      p.contributor_id = p.company_user&.contributor_id
      p.save!
    end
    Privilege.where(contributor_id: nil).destroy_all
  end

  desc "Create company business hours"
  task create_business_hour: :environment do
    Company.find_each do |company|
      BusinessHour.create(
        company_id: company.id,
        business_days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
        start_time: "09:00",
        end_time: "17:00"
      )
    end
  end

  desc 'Get users with email domain different than company subdomain'
  task email_domain_different_company_domain: :environment do
    c_user = []
    companies_subdomain = Company.all.pluck(:subdomain)

    Company.find_each do |company|
      CompanyUser.where(company_id: company.id).find_each do |company_user|
        cu_email = company_user.email.split('@')
        email_domain = cu_email.split('.com')
        if company.subdomain != email_domain && email_domain.include?(companies_subdomain)
          c_user << company_user
        end
      end
    end
  end

  desc "Reset reseller company id, if parent company is not exist"
  task reset_reseller_company_id: :environment do
    Company.where.not(reseller_company_id: nil).find_each do |com|
      com.update_column(:reseller_company_id, nil) if com.reseller_company.blank?
    end
  end

  desc "Add default asset statuses in all companies' asset statuses"
  task add_default_asset_statuses_in_all_companies: :environment do
    Company.find_each do |com|
      DefaultAssetStatus.find_each do |default_asset_status|
        com.asset_statuses.find_or_create_by!(default_asset_status.attributes.except('id', 'created_at', 'updated_at'))
      end
    end
  end

  desc "Create app direct for child companies of orphan parent companies"
  task create_app_direct: :environment do
    reseller_companies = Company.where.not(reseller_company_id: nil).pluck(:reseller_company_id).uniq
    orphan_resellers = Company.where(id: reseller_companies, is_reseller_company: false).pluck(:id)
    orphan_non_resellers = Company.where(reseller_company_id: orphan_resellers).pluck(:id)
    orphan_non_resellers.each do |orphan_non_reseller|
      AppDirect::SyncCompanyWorker.perform_async(orphan_non_reseller)
    end
  end

  desc "Update companies logo with logos that are public"
  task update_companies_logo_to_public: :environment do
    six_weeks_ago = 6.weeks.ago
    companies = Company.where("created_at >= :six_weeks_ago OR updated_at >= :six_weeks_ago", six_weeks_ago: six_weeks_ago)

    companies.each do |company|
      company_logo = company.logo
      folder_name = "CompanyLogos"

      if company_logo.present? && company_logo.blob.present?
        folder_path = File.join(Dir.home, folder_name, company.subdomain)
        FileUtils.mkdir_p(folder_path)
        file_path = File.join(folder_path, company_logo.blob.filename.to_s)

        File.binwrite(file_path, company_logo.download)
      end
    end

    directory_path = File.join(Dir.home, "CompanyLogos")

    Dir.foreach(directory_path) do |folder_name|
      folder_path = File.join(directory_path, folder_name)
      next unless File.directory?(folder_path)

      company_subdomain = folder_name
      company = Company.find_by(subdomain: company_subdomain)

      if company.present?
        image_file = Dir.glob(File.join(folder_path, "*")).first

        if image_file.present?
          filename = File.basename(image_file)
          content_type = Mime::Type.lookup_by_extension(File.extname(image_file)[1..]).to_s
          company.logo.attach(io: File.open(image_file), filename: filename, content_type: content_type)
          company.update_columns(original_logo_url: company.logo.url)
          company.save
        end
      end
    end

    Company.find_each do |company|
      company_logo_blob = company.logo&.blob
      company_logo_blob.update_columns(service_name: "amazon_public") if company_logo_blob.present?
    end
  end

  desc 'Find those company who has same name'
  task same_name_companies: :environment do
    Company.group(:name).having('count(*) > 1').pluck(:name)
  end

  desc 'Delete custom emails for expired companies'
  task delete_custom_emails_for_expired_companies: :environment do
    CronLock.lock("company:delete_custom_emails_for_expired_companies") do
      expired_trial_companies = Company.where.missing(:subscriptions)
        .select { |company| company.created_at + company.free_trial_days.days < Time.current }.pluck(:id)
      expired_paid_companies = Subscription.where('end_date < ?', Date.today).pluck(:company_id)

      expired_companies = expired_trial_companies | expired_paid_companies
      custom_emails = HelpdeskCustomEmail.where(company_id: expired_companies)

      custom_emails.find_each do |custom_email|
        if custom_email.email.present?
          EmailIdentityService.new.remove_email_identity(custom_email.email)
          custom_email.destroy
        end
      end
    end
  end

  desc "Update is_reseller_company for parent reseller companies"
  task update_parent_resellers: :environment do
    reseller_companies = Company.where.not(reseller_company_id: nil).pluck(:reseller_company_id).uniq
    orphan_resellers = Company.where(id: reseller_companies, is_reseller_company: false)
    orphan_resellers.update_all(is_reseller_company: true)
  end

  desc "copy default agent group to all company workspaces"
  task copy_default_agent_group_to_company_workspaces: :environment do
    Company.not_sample.includes(:groups, :workspaces).each do |company|
      agent_group = company.groups.find { |g| g.name == "Help Desk Agents" }
      next unless agent_group.present?
  
      helpdesk_workspace = company.workspaces.find { |w| w.name == 'Helpdesk' } || company.default_workspace
      agent_group.update_columns(workspace_id: helpdesk_workspace.id)

      workspaces = company.workspaces.reject { |w| w.id == helpdesk_workspace.id }
      puts company.id
      next if workspaces.count == 0

      agent_group_members_cont_ids = agent_group.group_members.pluck(:contributor_id)
      old_privilages = agent_group.contributor.privileges
      workspaces.each do |workspace|
        begin
          group = Group.find_or_initialize_by(workspace_id: workspace.id, company: company)
          next if group.id

          puts workspace.id
          group.default = true
          group.name = "#{workspace.name} Agents"
          group.save!
          agent_group_members_cont_ids.each do |cont_id|
            GroupMember.find_or_create_by(contributor_id: cont_id, group: group)
          end
          old_privilages.each do |pr|
            new_privilege = pr.dup
            new_privilege.contributor_id = group.contributor_id
            new_privilege.save!
          end
        rescue => e
          Rails.logger.warn "Issue copying helpdesk agent to workspace with ID: #{workspace.id}. Error message: #{e.message}"
          create_workspace_agents_error_log(e, company.id, workspace.id, nil, 'Workspace Groups')
        end
      end
    end
  end

  desc "It will update all companies as legacy companies"
  task update_company_as_legacy: :environment do
    Company.joins(:subscriptions).where(subscriptions: { status: 'active' }).update_all(is_legacy_company: true)
  end

  desc "Adds missing domains for companies based on their user email domains and company subdomain"
  task add_domains: :environment do
    success_entries = []
    error_entries = []
    
    Company.find_each do |company|
      begin 
        user_domains = company.users.map { |user| user.email.split('@').last.split('.').first }.uniq - [company.subdomain]

        user_domains.each do |domain_name|
          CompanyDomain.create!(company_id: company.id, domain_name: domain_name, is_registered: false)
        end

        CompanyDomain.create!(company_id: company.id, domain_name: company.subdomain, is_registered: true)
        success_entries << { company_id: company.id, name: company.name }
      rescue
        error_entries << { company_id: company.id, name: company.name, error: e.message }
      end
    end
    
    puts "Successfully updated companies: #{success_entries.size}"
    success_entries.each do |entry|
      puts "Company ID: #{entry[:company_id]}, Name: #{entry[:name]}"
    end
  
    puts "\nErrors encountered: #{error_entries.size}"
    error_entries.each do |entry|
      puts "Company ID: #{entry[:company_id]}, Name: #{entry[:name]}, Error: #{entry[:error]}"
    end
  end

  desc "Add 'Unrecognized' asset type to all companies"
  task add_unrecognized_to_all_companies: :environment do
    unrecognized_asset_type = AssetType.find_or_create_by!(name: 'Unrecognized')
    failed_companies = []

    Company.find_each do |company|
      begin
        if company.asset_types.exists?(name: unrecognized_asset_type.name)
          puts "Already exists for Company ID #{company.id} (#{company.name})"
          next
        end

        CompanyAssetType.create!(
          name: unrecognized_asset_type.name,
          description: unrecognized_asset_type.description,
          parent_type_id: unrecognized_asset_type.parent_type_id,
          ancestry: unrecognized_asset_type.ancestry,
          has_child: unrecognized_asset_type.has_child,
          private_asset_attributes: unrecognized_asset_type.private_asset_attributes,
          nested_attributes: unrecognized_asset_type.nested_attributes,
          company_id: company.id
        )
        puts "Added 'Unrecognized' asset type to Company ID #{company.id} (#{company.name})"
      rescue => e
        failed_companies << { company_id: company.id, name: company.name, error: e.message }
      end
    end

    if failed_companies.any?
      puts "\nFailed to add asset type in the following companies:"
      failed_companies.each do |entry|
        puts "Company ID: #{entry[:company_id]}, Name: #{entry[:name]}, Error: #{entry[:error]}"
      end
    else
      puts "\n'Unrecognized' asset type added to all companies successfully."
    end
  end
end

def create_workspace_agents_error_log(error, company_id, workspace_id, record_id = nil, record_class = nil)
  api_event = Logs::ApiEvent.new(company_id: company_id, activity: 1, api_type: "Workspace Agents", status: 0)
  error_detail = { company_id: company_id, error: error, workspace_id: workspace_id, record_id: record_id, record_class: record_class}
  api_event.class_name = 'Workspace::Agents'
  api_event.error_detail = error_detail
  api_event.save!
end
