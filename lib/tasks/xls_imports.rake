namespace :xls_imports do
  desc "Delete idle xls import files, for which company no longer exists"
  task delete_idle_xls_imports: :environment do
    XlsImport.find_each do |file|
      file.destroy unless file.company
    end
  end

  desc 'Delete the contracts pending import files'
  task delete_contract_pending_imports: :environment do
    XlsImport.joins(:xls_import_rows)
             .where(module_name: "contracts", record_type: nil)
             .where("xls_import_rows.status = ?", false)
             .distinct
             .destroy_all
  end

  desc 'Delete the transactions pending import files'
  task delete_transactions_pending_imports: :environment do
    XlsImport.joins(:xls_import_rows)
             .where(module_name: "general_transactions", record_type: nil)
             .where("xls_import_rows.status = ?", false)
             .distinct
             .destroy_all
  end

  desc 'Add duplicate data of successfully imported rows in Xls Import columns'
  task add_dup_successfully_imported_rows_xls_import: :environment do
    XlsImport.find_each do |active_import|
      total_rows = active_import.xls_import_rows.count
      imported_rows = active_import.xls_import_rows.where(status: true).count
      duplicate_rows = active_import.xls_import_rows.where(is_duplicate: true).count
      active_import.update_columns(imported_rows_count: imported_rows, duplicate_rows_count: duplicate_rows, total_rows_count: total_rows)
      puts "Xls Import with ID: #{active_import.id} updated successfully"
    rescue => e
      puts "Error #{e} on Import with ID: #{active_import.id}"
      next
    end
  end
  
  # Need to execute this rake task after the above rake task
  desc 'Delete successfully imported rows from all modules'
  task delete_successfully_imported_rows: :environment do
    XlsImport.find_each do |active_import|
      active_import.xls_import_rows.where(status: true).in_batches(of: 1000) do |rows|
        rows.delete_all
      end
    end
  end
end
