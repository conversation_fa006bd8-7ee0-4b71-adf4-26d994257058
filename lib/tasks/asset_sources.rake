namespace :asset_sources do
  desc "It will make asset source for imported and manually added assets."
  task make_sources: :environment do
    ## Query for getting assets where their source does not exist
    unlinked_asset = ManagedAsset.left_outer_joins(:asset_sources).where(asset_sources: { managed_asset_id: nil })

    ## Filter out assets made with manually added or uploaded
    man_added = unlinked_asset.where({ source: ["manually_added", "uploaded"] })

    ## making source for each asset
    man_added.all.each do |asset|
      data = {
        os: asset.operating_system,
        status: asset.status,
        ipAddress: asset.ip_address,
        manufacturer: asset.manufacturer,
        machineSerialNo: asset.machine_serial_number,
        macAddresses: asset.mac_addresses,
      }
      data[:memory] = asset.hardware_detail.try(:memory)
      data[:harddisk] = asset.hardware_detail.try(:hard_drive)
      data[:processor] = asset.hardware_detail.try(:processor)
      managed_asset_source = AssetSource.find_or_initialize_by(managed_asset_id: asset.id, source: asset.source)
      managed_asset_source.asset_data = data
      managed_asset_source.save!
    end
  end

  desc "Fixing asset source data linked with probe and agent dicovered assets."
  task make_sources: :environment do
    ## Query for getting all associated assets
    linked_assets = ManagedAsset.joins(:asset_sources)

    ## Filter out assets made with agent and probe
    probe_agent_assets = linked_assets.where(source: ["agent", "probe"])

    ## Fixing source data from managed asset to discovered asset data
    probe_agent_assets.each do |asset|
      asset_sources = AssetSource.where(managed_asset_id: asset.id)
      asset_sources.each do |asset_source|
        discovered_asset = DiscoveredAsset.find(asset_source.discovered_asset_id)
        asset_source.asset_data = JSON.parse(discovered_asset.to_json).except("id").except("asset_softwares").except("asset_sources_data").except("asset_sources")
        asset_source.save!
      end
    end
  end

  desc 'Fix nil asset_data values in asset sources'
  task fix_nil_asset_data_in_asset_sources: :environment do
    AssetSource.where(asset_data: nil).update_all(asset_data: {})
  end
end
