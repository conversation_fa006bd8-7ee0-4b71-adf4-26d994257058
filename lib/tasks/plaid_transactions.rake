namespace :plaid_transactions do
  desc "Fetching transactions for non-sample companies"
  task fetch_non_sample_transactions: :environment do
    CronLock.lock("plaid_transactions:fetch_non_sample_transactions") do
      company_ids = Company.not_sample.all(&:allow_access?).pluck(:id)
      run_fetching(company_ids, "non_sample")
    end
  end

  desc "Fetching transactions for sample companies"
  task fetch_sample_transactions: :environment do
    CronLock.lock("plaid_transactions:fetch_non_sample_transactions") do
      company_ids = Company.sample_companies.all(&:allow_access?).pluck(:id)
      run_fetching(company_ids, "sample")
    end
  end

  desc "Remove transactions that do not have permitted categories"
  task remove_unpermitted_category_transactions: :environment do
    unpermitted_primaries = ['Bank Fees', 'Cash Advance', 'Interest', 'Tax']
    unpermitted_secondaries = ['ATM', 'Save As You Go']
    begin
      GeneralTransaction.all.each do |transaction|
        if unpermitted_primaries.include?(transaction.primary_category)
          transaction.destroy!
        end

        if transaction.primary_category == 'Payment' && unpermitted_secondaries.include?(transaction.secondary_category)
          transaction.destroy!
        end
      end
    rescue => e
      Rails.logger.warn("Issue removing transaction-#{transaction.id}")
      next
    end
  end

  desc "Rename Slack George transactions to just Slack"
  task rename_slack_george_transactions: :environment do
    GeneralTransaction.where(product_name: "Slack George").update_all(product_name: "SLACK")
  end

  def run_fetching(company_ids, sample_type)
    Sidekiq.logger.level = Rails.logger.level
    started_at = Time.now.strftime("%Y-%m-%d.%H")
    Rails.logger.info("Started plaid_transactions:fetch_#{sample_type}_transactions-#{started_at}")
    batch_timer = 1
    company_ids.in_groups_of(50) do |id_group|
      BatchCompanyWorker.perform_in(batch_timer.minutes, id_group.compact)
      batch_timer += 2
    end
    Rails.logger.info("Completed plaid_transactions:fetch_#{sample_type}_transactions-#{started_at}")
  end
end
