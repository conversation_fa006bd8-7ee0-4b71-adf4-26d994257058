namespace :integrations do
  desc "Sync force stopped value from AzureAdAssets, Meraki, and MsIntuneAssets to CompanyIntegrations"
  task sync_force_stopped: :environment do
    Integrations::AzureAdAssets::Config.where(is_force_stopped: true).find_each do |config|
      company_integration = config.company_integration
      if company_integration
        company_integration.update_column(:is_force_stopped, true)
        puts "Updated CompanyIntegration for AzureAdAssets with ID: #{company_integration.id}"
      end
    end

    Integrations::Meraki::Config.where(is_force_stopped: true).find_each do |config|
      company_integration = config.company_integration
      if company_integration
        company_integration.update_column(:is_force_stopped, true)
        puts "Updated CompanyIntegration for Meraki with ID: #{company_integration.id}"
      end
    end

    Integrations::MsIntuneAssets::Config.where(is_force_stopped: true).find_each do |config|
      company_integration = config.company_integration
      if company_integration
        company_integration.update_column(:is_force_stopped, true)
        puts "Updated CompanyIntegration for MsIntuneAssets with ID: #{company_integration.id}"
      end
    end

    puts "Force stopped sync completed."
  end
end
