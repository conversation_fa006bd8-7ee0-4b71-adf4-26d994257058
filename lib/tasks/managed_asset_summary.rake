namespace :managed_asset_summary do
  desc "populate sources summary data while considering precedence"
  task populate_data: :environment do
    ManagedAsset.unscoped.find_each do |asset|
      @managed_asset = asset
      asset_attributes = ManagedAsset.column_names.select { |key| @managed_asset[key].present? } - attrs_to_scrub
      attribute_description = asset_attributes.map { |attr_name| attribute_description_data(attr_name, @managed_asset[attr_name]) }.compact

      if @managed_asset.hardware_detail.present?
        hardware_attributes = @managed_asset.hardware_detail.attributes.keys.select { |key| @managed_asset.hardware_detail[key].present? } - attrs_to_scrub
        hardware_srcs_array = hardware_attributes.map { |attr_name| attribute_description_data(attr_name, @managed_asset.hardware_detail[attr_name]) }.compact
        attribute_description = [attribute_description, hardware_srcs_array].flatten
      end

      @managed_asset.attributes_description ||= AttributesDescription.new({ data: [] })
      @managed_asset.attributes_description.data = attribute_description
      @managed_asset.attributes_description.save!
      
      puts "Successfully added summary data to '#{@managed_asset.name}' with id '#{@managed_asset.id}'."
    end
  end

  desc "populate missing display name value for uploaded source in asset summary"
  task uploaded_display_name: :environment do
    ManagedAsset.unscoped.uploaded.find_each do |asset|
      if asset.name.present? && asset.asset_sources.size == 1 && asset.asset_sources.first.uploaded?
        asset.attributes_description.data << { key: "displayName", value: asset.name, source: 'uploaded' }.as_json
        asset.attributes_description.save!
      end
      puts "Successfully updated summary data with display name to '#{asset.name}' with id '#{asset.id}'."
    end
  end

  desc "removing data from attributes description, holding empty processor value"
  task fix_empty_processor: :environment do
    attrs_descriptions = AttributesDescription.where('data @> ?', [{ "key"=>"processor", "value"=>" " }].to_json)
    attrs_descriptions.find_each do |attrs_disc|
      attrs_disc.data = attrs_disc.data.select { |obj| obj["key"] != "processor" }
      attrs_disc.save!
      puts "Successfully removed empty processor value from attribute description with id #{attrs_disc.id}"
    end
  end

  desc "populating missing data for imported discovered assets"
  task fix_imported_assets_from_connectors: :environment do
    ManagedAsset.unscoped.find_each do |asset|
      @managed_asset = asset
      selected_source = ["probe", "meraki", "ubiquiti", "aws", "azure", "google"] & @managed_asset.asset_sources.pluck(:source)

      if selected_source.present?
        asset_attributes = ManagedAsset.column_names.select { |key| @managed_asset[key].present? } - attrs_to_scrub
        attribute_description = asset_attributes.map { |attr_name| attribute_description_data(attr_name, @managed_asset[attr_name]) }.compact
  
        @managed_asset.attributes_description ||= AttributesDescription.new({ data: [] })
        @managed_asset.attributes_description.data = updated_attributes_data(attribute_description)
        @managed_asset.attributes_description.save!
        
        puts "Successfully updated summary data to asset '#{@managed_asset.name}' with id '#{@managed_asset.id}'."
      end
    end
  end

  def updated_attributes_data(new_data)
    combined_array = [@managed_asset.attributes_description.data, new_data]
    combined_array.compact.map { |arr| arr.group_by { |e| e["key"] } }.reduce(&:merge).flat_map(&:last)
  end

  def attribute_description_data(attr_name, attr_value)
    attribute_data = nil
    sources_precedence = %w[
      agent selfonboarding probe meraki ubiquiti aws azure google manually_added uploaded]
    sources_precedence = is_meraki_device? ? sources_precedence.unshift('meraki') : sources_precedence
    sources_precedence = is_ubiquiti_device? ? sources_precedence.unshift('ubiquiti') : sources_precedence

    if attr_name == "name" && name_manually_added
      return { key: "displayName", value: attr_value, source: 'manually_added' }.as_json 
    end
    
    sources_precedence.each do |src|
      asset_data = @managed_asset.asset_sources.find_by_source(src)&.asset_data
      next if asset_data.blank?

      attribute_name = mapping_name(attr_name, src)
      if asset_data[attribute_name] == attr_value
        attribute_data = { key: "#{attribute_name}".camelcase(:lower), value: attr_value, source: src }.as_json
        break
      end
    end
  
    attribute_data
  end
  
  def mapping_name(attribute_name, src)
    filteredNames = {
      name: 'display_name',
      machine_serial_number: 'machine_serial_no',
      hard_drive: 'harddisk',
    }

    attribute_name = filteredNames.stringify_keys[attribute_name] || attribute_name
    attribute_name = src == 'manually_added' ? attribute_name.camelize(:lower) : attribute_name

    if attribute_name == 'processor' && src != 'manually_added'
      attribute_name = "processor_name"
    end

    attribute_name
  end
  
  def attrs_to_scrub
    %w[id guid source sources created_at vendor_id
       company_id system_uuid managed_asset_id meraki_network_id
       update_by_source_at hardware_detail_id hardware_detail_type
       company_asset_type_id private_asset_attributes screen_size
       integrations_locations_id created_at updated_at last_synced_at 
       protocols location_id agent_location_id
    ]
  end

  def name_manually_added
    @managed_asset.asset_sources.order(created_at: :asc).first&.source == "manually_added" ||
    @managed_asset.audits.any? { |audit| audit.audited_changes["name"] && audit.user_id }
  end

  def is_meraki_device?
    discovered_asset = @managed_asset.discovered_asset
    discovered_asset.present? && discovered_asset.meraki? && discovered_asset.device?
  end

  def is_ubiquiti_device?
    discovered_asset = @managed_asset.discovered_asset
    discovered_asset.present? && discovered_asset.ubiquiti? && discovered_asset.device?
  end
end
