namespace :guests do
  desc "update workspace id of guests which has no workspace id"
  task update_workspace_id_of_guests: :environment do
    Guest.where(workspace_id: nil).find_each do |guest|
      if guest.company.workspaces.count == 1
        guest.update(workspace_id: guest.company.workspaces.first.id)
      else
        help_ticket = HelpTicket.where(company_id: guest.company.id)
                        .joins(:custom_form_values)
                        .where(custom_form_values: { value_int: guest.contributor_id })&.first
        guest.update(workspace_id: help_ticket.workspace_id) if help_ticket.present?
      end
    end
  end
end
