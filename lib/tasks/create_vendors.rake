namespace :vendor do
  desc "It will create vendors from vendor names in other modules"
  task create_vendors: :environment do
    services = GeneralService.where.not(vendor_name: nil)
    services.each do |service|
      begin
        vendor = Vendor.new(name: service.vendor_name, company_id: service.company_id, imported: Time.now, monthly_cost: service.monthly_cost)
        vendor.save(validate: false)
      rescue => e
        Rails.logger.warn e.message
        next
      end
    end

    contracts = Contract.where.not(vendor_name: nil)
    contracts.each do |contract|
      begin
        vendor = Vendor.new(name: contract.vendor_name, company_id: contract.company_id, imported: Time.now, contract_id: contract.id)
        vendor.save(validate: false)
        contract.update(vendor_id: vendor.id)
      rescue => e
        Rails.logger.warn e.message
        next
      end
    end
  end

  task populate_vendor_names: :environment do
    vendors = Vendor.where("lower(name) = ?", "aws")
    vendors.each do |vendor|
      vendor.update(name: "Amazon Web Services")
    end

    vendors = Vendor.where("lower(name) = ?", "gcp")
    vendors.each do |vendor|
      vendor.update(name: "Google Cloud Products")
    end
  end

  task populate_vendor_logo_url: :environment do
    vendors_array = ["gcp", "aws", "amazon web services", "microsoft azure", "azure", "digitalocean", "adobe"]
    vendors = Vendor.where("lower(name) IN (?)",  vendors_array)
    vendors.each do |vendor|
      default_vendor = DefaultVendor.find_by_name (vendor.name)
      if default_vendor
        is_cloud_platform = default_vendor.is_cloud_platform
        vendor.is_cloud_platform = is_cloud_platform
        vendor.logo_url = default_vendor.logo_url
        vendor.save
      end
    end
  end

  task populate_friendly_names: :environment do
    vendors = Vendor.all
    vendors.find_each do |vendor|
      vendor.friendly_name << vendor.name
      vendor.save
    end
  end

  task add_user_friendly_names: :environment do
    vendors = Vendor.where("lower(name) = ?",  "aws")
    vendors.each do |vendor|
      vendor.friendly_name << "Amazon Web Services"
      vendor.save
    end

    vendors = Vendor.where("lower(name) = ?",  "amazon web services")
    vendors.each do |vendor|
      vendor.friendly_name << "AWS"
      vendor.save
    end

    vendors = Vendor.where("lower(name) = ?",  "gcp")
    vendors.each do |vendor|
      vendor.friendly_name << "Google Cloud Products"
      vendor.save
    end

    vendors = Vendor.where("lower(name) = ?",  "Google Cloud Products")
    vendors.each do |vendor|
      vendor.friendly_name << "GCP"
      vendor.save
    end
  end

  task fix_cloud_vendors: :environment do
    vendors_array = ["aws", "amazon web services", "microsoft azure", "azure", "gcp", "gogole cloud products"]
    vendors = Vendor.where("lower(name) IN (?)",  vendors_array)
    category = Category.find_by(name: "DevOps", module_type: 'company')
    vendors.find_each do |vendor|
      vendor.category_id =  category.id
      vendor.default_tags << "Cloud" unless vendor.default_tags.include?("Cloud")
      vendor.save
    end
  end

  desc "fix friendly names for vendors"
  task fix_friendly_names_for_vendors: :environment do
    Vendor.all.find_each do |vendor|
      vendor.friendly_name = vendor.friendly_name.uniq
      vendor.save!
    end
  end

  desc "fix cloud platform for exisitng vendors"
  task fix_cloud_platform_check: :environment do
    vendors = Vendor.where("name = ? OR name = ?", "Azure", "Amazon Web Services").where(is_cloud_platform: false)
    vendors.each do |vendor|
      if vendor.company.azure_config
        vendor.update(is_cloud_platform: true)
      end
    end
  end

  desc "fix attributes for aws vendor"
  task fix_aws_vendor_attrs: :environment do
    default_vendor = DefaultVendor.find_by(name: "Amazon Web Services")
    Vendor.where(name: default_vendor.name).find_each do |vendor|
      if vendor.is_cloud_platform.nil? || vendor.friendly_name.blank?
        vendor.update!(is_cloud_platform: default_vendor.is_cloud_platform, friendly_name: default_vendor.friendly_name)
      end
    end
  end
end
