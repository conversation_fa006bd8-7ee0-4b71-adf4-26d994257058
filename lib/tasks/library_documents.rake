namespace :library_documents do
  desc "Update library documents with public urls"
  task update_library_documents_to_public: :environment do
    LibraryDocument.find_each do |library_document|
      target_attachment = library_document.attached_file
      folder_name = "LibraryDocuments"
      if target_attachment.present? && target_attachment.blob.present?
        folder_path = File.join(Dir.home, folder_name, library_document.id.to_s)
        FileUtils.mkdir_p(folder_path)
        file_path = File.join(folder_path, target_attachment.blob.filename.to_s)

        File.binwrite(file_path, target_attachment.download)
      end
    end

    directory_path = File.join(Dir.home, "LibraryDocuments")

    Dir.foreach(directory_path) do |folder_name|
      folder_path = File.join(directory_path, folder_name)
      next unless File.directory?(folder_path)

      library_document_id = folder_name
      library_document = LibraryDocument.find_by(id: library_document_id)

      if library_document.present?
        attached_file = Dir.glob(File.join(folder_path, "*")).first

        if attached_file.present?
          filename = File.basename(attached_file)
          content_type = Mime::Type.lookup_by_extension(File.extname(attached_file)[1..]).to_s
          old_updated_at = library_document.updated_at
          library_document.attached_file.attach(io: File.open(attached_file), filename: filename, content_type: content_type)
          library_document.update_columns(updated_at: old_updated_at)
        end
      end
    end

    LibraryDocument.find_each do |library_document|
      library_document_blob = library_document.attached_file&.blob
      library_document_blob.update_columns(service_name: "amazon_public") if library_document_blob.present?
    end
  end
end
