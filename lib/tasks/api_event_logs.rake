namespace :event_logs do
  desc "It will delete 10 days old api event logs."
  task delete_api_logs: :environment do
    DeleteApiEventLogsWorker.perform_async('delete_api_logs')
  end

  desc "It will delete 15 days old plaid event logs."
  task delete_plaid_logs: :environment do
    DeleteApiEventLogsWorker.perform_async('delete_plaid_logs')
  end

  desc "It will delete 30 day old asset discovery event logs."
  task delete_asset_discovery_logs: :environment do
    DeleteApiEventLogsWorker.perform_async('delete_asset_discovery_logs')
  end

  desc "It will delete 1 month old help ticket emails logs"
  task delete_help_ticket_emails_logs: :environment do
    DeleteApiEventLogsWorker.perform_async('delete_help_ticket_emails_logs')
  end
end
