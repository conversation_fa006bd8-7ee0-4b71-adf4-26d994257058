namespace :faq do
  desc "Add uncategorized id for nil categories in faq"
  task update_faq_category: :environment do
    Company.find_each do |com|
      com.helpdesk_faqs.where(category_id: nil).each do |faq|
        faq.update( category_id: com.categories.find_by(name:"Uncategorized").id )
      end
    end
  end

  desc "Add workspace_id in faqs and snippets"
  task populate_workspace_id: :environment do
    Company.find_each do |com|
      com.helpdesk_faqs.where(workspace_id: nil).update_all(workspace_id: com.default_workspace.id)
      com.snippets.where(workspace_id: nil).update_all(workspace_id: com.default_workspace.id)
    end
  end

  desc "update faq company categories to workspace categories"
  task update_faq_categories_to_workspace: :environment do
    HelpdeskFaq.where.not(category_id: nil).find_each do |faq|
      workspace_category = faq.workspace.categories.find_or_create_by(name: faq.category.name)
      faq.update_column(:category_id, workspace_category.id)
    end
  end

  desc "make those faqs public whose workspace setting has faq public access enabled"
  task make_faqs_public: :environment do
    faq_default_setting_id = DefaultHelpdeskSetting.find_by(setting_type: "allow_faq_page_to_logged_out_users").id
    Workspace.includes(:helpdesk_settings).where(helpdesk_settings: { default_helpdesk_setting_id: faq_default_setting_id, enabled: true }).find_each do |workspace|
      workspace.helpdesk_faqs.update_all(public: true)
    end
  end
end
