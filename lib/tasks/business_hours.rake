namespace :business_hours do
  desc "update business hours as per the new addition of attributes"
  task populate_new_attributes_for_business_hours: :environment do
    BusinessHour.find_each do |business_hour|
      start_time = business_hour.start_time
      end_time = business_hour.end_time
      business_days = business_hour.business_days

      business_hour.update_columns(
        schedule: [
          {'day' => 'Monday', 'start_time' => start_time, 'end_time' => end_time, 'active' => business_days.include?('Monday')},
          {'day' => 'Tuesday', 'start_time' => start_time, 'end_time' => end_time, 'active' => business_days.include?('Tuesday')},
          {'day' => 'Wednesday', 'start_time' => start_time, 'end_time' => end_time, 'active' => business_days.include?('Wednesday')},
          {'day' => 'Thursday', 'start_time' => start_time, 'end_time' => end_time, 'active' => business_days.include?('Thursday')},
          {'day' => 'Friday', 'start_time' => start_time, 'end_time' => end_time, 'active' => business_days.include?('Friday')},
          {'day' => 'Saturday', 'start_time' => start_time, 'end_time' => end_time, 'active' => business_days.include?('Saturday')},
          {'day' => 'Sunday', 'start_time' => start_time, 'end_time' => end_time, 'active' => business_days.include?('Sunday')}
        ],
        timezone: business_hour.company.timezone
      )
    end
  end

  desc "Move BusinessHours from Company level to Workspace level"
  task business_hour_to_workspaces: :environment do
    def default_schedule
      days_of_week = %w[Monday Tuesday Wednesday Thursday Friday Saturday Sunday]
  
      days_of_week.map do |day|
        {
          'day' => day,
          'start_time' => '09:00',
          'end_time' => '17:00',
          'active' => !%w[Saturday Sunday].include?(day)
        }
      end
    end

    puts "Starting BusinessHour migration..."

    total_workspaces = Workspace.count
    created_count = 0
    skipped_count = 0
    error_count = 0
    processed = 0
    skipped_details = []
    error_details = []

    Workspace.includes(:business_hour, company: :business_hours).find_each do |workspace|
      processed += 1
      puts "-----------------------------------"
      puts "Processing Workspace ##{workspace.id} (#{processed}/#{total_workspaces})"

      if workspace.business_hour.present?
        puts " x Workspace already has a BusinessHour, skipping..."
        skipped_count += 1
        skipped_details << { workspace_id: workspace.id, company_id: workspace.company_id }
        next
      end

      default_bh = workspace.company.business_hours.find_by(workspace_id: nil)
      if default_bh.nil?
        puts " x Company ##{workspace.company_id} has no default BusinessHour, skipping..."
        skipped_count += 1
        next
      end
      begin
        new_bh = workspace.create_business_hour!(
          company_id: workspace.company_id || nil,
          schedule: default_bh.schedule || default_schedule,
          description: default_bh.description || "",
          timezone: default_bh.timezone || 'Etc/UTC',
          holidays: default_bh.holidays || []
        )

        puts " + Created BusinessHour ##{new_bh.id} for Workspace ##{workspace.id}"
        created_count += 1
      rescue => e
        puts " ! Failed to create BusinessHour for Workspace ##{workspace.id}: #{e.message}"
        error_details << {
          workspace_id: workspace.id,
          company_id: workspace.company_id,
          error: e.message
        }
        error_count += 1
      end
    end

    puts "\n✅ BusinessHour migration complete!"
    puts "Total workspaces processed: #{processed}"
    puts "Created BusinessHours: #{created_count}"
    puts "Skipped (already had BH or no default BH): #{skipped_count}"
    puts "Errors during creation: #{error_count}"

    if error_details.any?
      puts "\n❌ Workspaces with errors:"
      puts error_details
    end
    if skipped_details.any?
      puts "\n Workspaces skipped:"
      puts skipped_details
    end
  end
end
