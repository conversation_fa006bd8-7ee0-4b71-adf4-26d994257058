namespace :expanded_privileges do
  desc "Populate expanded privileges"
  task populate: :environment do
    CompanyUser.find_each do |company_user|
      puts company_user.id
      if company_user.contributor
        ExpandedPrivileges::Populate.new(company_user.contributor).call
      end
    end
  end

  desc "Delete help ticket expanded privileges where workspace_id is nil"
  task delete_expanded_privileges: :environment do
    ExpandedPrivilege.where(name:"HelpTicket", workspace_id:nil).destroy_all
  end
end
