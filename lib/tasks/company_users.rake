namespace :company_users do
  desc "create system user for all companies"
  task create_system_users: :environment do
    User.super_admins.each do |super_admin|
      Company.find_each do |company|
        SystemUser.create!(user_id: super_admin.id, company_id: company.id, granted_access_at: Time.now, helpdesk_agent: true)
      end
    end
  end

  desc "create default mailers"
  task create_default_mailers: :environment do
    CompanyUser.find_each do |cu|
      DefaultMailer.find_each do |mailer|
        cu.company_user_mailers.find_or_create_by(company_user_id: cu.id, default_mailer_id: mailer.id)
      end
    end
  end

  desc 'set is_admin true for admin company users'
  task set_is_admin_true_for_admin_company_users: :environment do
    gp_con_ids = Group.where(name: 'Admins').pluck(:contributor_id)
    gp_con_ids.each do |gp_con_id|
      cu_con_ids =  Contributor.find_by(id: gp_con_id)&.contributor_ids_only_users
      CompanyUser.where(contributor_id: cu_con_ids).update_all(is_admin: true) if cu_con_ids.present?
    end
  end

  desc "nullified assignment information on archiving of company users"
  task nullify_orphan_assignments: :environment do
    AssignmentInformation.where('managed_by_contributor_id is NOT NULL')
      .left_outer_joins(:manager)
      .where(contributors: {id: nil})
      .update_all(managed_by_contributor_id: nil)

    AssignmentInformation.where('used_by_contributor_id is NOT NULL')
      .left_outer_joins(:user)
      .where(contributors: {id: nil})
      .update_all(used_by_contributor_id: nil)
  end

  desc "create new contributor for company users with nullified contributor id"
  task create_contributors: :environment do
    CompanyUser.joins(:user).where(contributor_id: nil, users: {archived: false, super_admin: false}).find_each do |user|
      user.create_contributor
    end
  end

  desc "remove contributors for super admins"
  task remove_super_admin_contributors: :environment do
    CompanyUser.joins(:user).where(users: {super_admin: true}).where.not(contributor_id: nil).find_each do |user|
      user.contributor.destroy!
    end
  end

  desc "enabled self mfa for admins if self mfa settings enabled"
  task self_mfa_enabled_for_admins: :environment do
    Company.find_each do |company|
      mfa_setting = company.mfa_setting
      if mfa_setting && mfa_setting.mfa_enabled && mfa_setting.enabled_for_self_user
        company.admins.company_users.update_all(self_mfa_enabled: true, mfa_enabled: true)
      end
    end
  end

  desc "setting invitation_due_at nil for all uninvited company users"
  task update_company_users: :environment do
    CompanyUser.where(invite_token: nil, granted_access_at: nil).where.not(invitation_due_at: nil).update_all(invitation_due_at: nil)
  end

  desc "populate missing custom form in company users"
  task populate_missing_custom_form: :environment do
    CompanyUser.where(custom_form: nil).find_each do |cu|
      default_form = cu.company.custom_forms.find_by(company_module: "company_user", default: true)
      cu.update_column(:custom_form_id, default_form.id)
    end
  end

  desc 'Set duplicate avatar in company user'
  task set_duplicate_avatar_in_company_user: :environment do
    batch_size = 1000
    batch_index = 0
    user_errors = []
    CompanyUser.find_in_batches(batch_size: batch_size) do |batch|
      batch_index += 1
      puts "Processing batch #{batch_index} with #{batch.size} records"
      batch.each do |user|
        begin
          avatar_url = user.avatar_thumb
          if avatar_url.present?
            user.update_column(:avatar_url, avatar_url)
            puts "Company User Updated #{user.id}"
          end
        rescue => e
          Rails.logger.warn "Issue updating Company User with ID: #{user.id}. Error message: #{e.message}"
          user_errors << { company_user_id: user.id, error: e.message }
        end
      end
    end
    puts "Errors: #{user_errors}"
  end

  desc "Destroy source and target linkable links for archived CompanyUsers"
  task destroy_archived_users_linkable_links: :environment do
    puts "Starting to destroy source and target linkable links for archived CompanyUsers"

    CompanyUser.where.not(archived_at: nil).find_each do |company_user|
      if company_user.linkable
        company_user.linkable.source_linkable_links.destroy_all
        company_user.linkable.target_linkable_links.destroy_all

        puts "Destroyed source links and target links for CompanyUser##{company_user.id}"
      else
        puts "No Linkable found for CompanyUser##{company_user.id}"
      end
    end

    puts "Finished! Destroyed Linkables for archived CompanyUsers."
  end

  desc "Update vendor contacts for all archived users"
  task update_vendor_contacts_for_archived_users: :environment do
    puts "Starting to update all vendor contacts for archived CompanyUsers"

    CompanyUser.where.not(archived_at: nil).find_each do |user|
      begin
        user.remove_company_user_vendor_contacts
        puts "Vendor contacts updated for CompanyUser ID: #{user.id}"
      rescue StandardError => e
        puts "Error updating vendor contacts for CompanyUser ID: #{user.id} - #{e.message}"
      end
    end

    puts "Finished! Vendor contact update process for all archived CompanyUsers completed successfully."
  end

end
