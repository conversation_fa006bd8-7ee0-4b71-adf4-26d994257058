namespace :contracts do
  desc "Send contract alert emails"
  task alert_emails: :environment do
    ContractAlertEmailsWorker.perform_async
  end

  desc "It will create has_many relationships for contracts and locations"
  task add_records_to_contracts_locations_table: :environment do
    Contract.all.each do |contract|
      begin
        if contract.location_id.present?
          ContractLocation.create(contract_id: contract.id, location_id: contract.location_id)
        end
      rescue => e
        ActiveRecord::Base.connection.execute 'ROLLBACK'
        Rails.logger.warn e.message
        next
      end
    end
  end

  desc "It will create has_many relationships for contracts and company users"
  task add_records_to_contracts_contacts_table: :environment do
    Contract.find_each.each do |contract|
      begin
        if ActiveRecord::Base.connection.column_exists?(:contracts, :primary_company_user_id) && contract.primary_company_user_id.present?
          ContractContact.find_or_create_by(contract_id: contract.id, company_user_id: contract.primary_company_user_id)
        end
        if ActiveRecord::Base.connection.column_exists?(:contracts, :secondary_company_user_id) && contract.secondary_company_user_id.present?
          ContractContact.find_or_create_by(contract_id: contract.id, company_user_id: contract.secondary_company_user_id)
        end
      rescue => e
        ActiveRecord::Base.connection.execute 'ROLLBACK'
        Rails.logger.warn e.message
        next
      end
    end
  end

  desc "It will create has_many relationships for contracts and company users"
  task create_contract_value_amounts: :environment do
    Contract.find_each.each do |contract|
      begin
        total_value = contract.total_value
        if total_value.present? && total_value > 0
          contract.update(contract_value_amount: total_value)
        end
      rescue => e
        Rails.logger.warn e.message
        next
      end
    end
  end

  desc "It will update contract's category to Uncategorized if category_id is 0"
  task update_category: :environment do
    default_category_id = DefaultCategory.find_by(name: 'Uncategorized').id
    Company.find_each do |company|
      uncategorized = company.categories.find_by(name: 'Uncategorized')
      if uncategorized.present?
        contracts = company.contracts.where(category_id: default_category_id)
        contracts.update_all(category_id: uncategorized.id)
      end
    end
  end

  desc 'It will add contract preference for all the old companies'
  task create_contract_preferences_for_old_companies: :environment do
    Company.find_each do |company|
      company.create_contract_preference!(preference: Contract::DEFAULT_SELECTED_COLUMNS) unless company.contract_preference
    end
  end

  desc "It will update the contract's end date having month-to-month contract terms"
  task update_contract_term: :environment do
    Contract.where(contract_type: 'open_ended').where.not(end_date: nil).update_all(end_date: nil)
  end
end
