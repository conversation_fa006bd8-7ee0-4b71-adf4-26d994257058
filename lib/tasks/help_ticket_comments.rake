require Rails.root.join('app', 'helpers', 'email_response_helper')

namespace :help_ticket_comment do
  include EmailResponseHelper

  desc "It will move data from help ticket activities to comments"
  task create_help_ticket_comments: :environment do
    HelpTicketActivity.where(activity_type: "note").find_each do |activity|
      if activity.data["note_body"].present? && !activity.help_ticket_comment
        comment = HelpTicketComment.create(
          comment_body: activity.data["note_body"],
          private_user_flag: activity.private_user_flag,
          private_company_user_ids: activity.private_user_ids,
          help_ticket_id: activity.help_ticket_id,
          help_ticket_activity_id: activity.id,
          commenter_id: activity.owner_id,
          created_at: activity.created_at
        )

        time_entry = TimeSpent.find_by(help_ticket_activity_id: activity.id)
        time_entry.update(help_ticket_comment_id: comment.id) if time_entry
      end
    end
  end

  desc "Converts comments from company users to contributors"
  task convert_to_contributors: :environment do
    sql = "UPDATE help_ticket_comments SET private_flag = private_user_flag"
    ActiveRecord::Base.connection.execute sql

    sql = """
    UPDATE
      help_ticket_comments
    SET
      contributor_id = cu.contributor_id
    FROM
      company_users cu
    WHERE
      cu.id = help_ticket_comments.commenter_id
    """
    ActiveRecord::Base.connection.execute sql

    HelpTicketComment.where(private_user_flag: true).find_each do |comment|
      if comment.private_contributor_ids.blank? && comment.private_company_user_ids.present?
        contributor_ids = CompanyUser.where(id: comment.private_company_user_ids)
                            .map {|company_user| company_user.contributor_id }.compact
        comment.update_columns(private_contributor_ids: contributor_ids)
      end
    end
  end

  desc "Delete default comments from time spents"
  task delete_time_spent_default_comments: :environment do
    HelpTicketComment.where(comment_body: 'Spent time on this ticket.').destroy_all
  end

  desc 'It will truncate the comments which are greater than 50000 characters'
  task truncate_lengthy_comments: :environment do
    HelpTicketComment.where("length(comment_body) > 50000").find_each do |htc|
      puts htc.id
      comment_body = htc.comment_body
      if htc.source == 'email'
        comment_body = Nokogiri::HTML(comment_body)
        comment_body = remove_replies(comment_body, 20).to_html
        if comment_body.length >= 50000
          comment_body = Nokogiri::HTML(comment_body[0..49999]).to_html
        end
      else
        comment_body = comment_body[0..49999]
      end
      htc.update_columns(comment_body: comment_body)
    end
  end

  desc 'Remove duplicate ids from private_contributor_ids column'
  task remove_duplicate_private_contributor_ids: :environment do
    HelpTicketComment.where(private_flag: true).find_each do |comment|
      puts "iterating comment #{comment.id}"
      ids = comment.private_contributor_ids
      unique_ids = ids.uniq
      if ids.size > unique_ids.size
        puts "--- updating comment #{comment.id} ---"
        comment.update_column(:private_contributor_ids, unique_ids)
      end
    end
  end
end
