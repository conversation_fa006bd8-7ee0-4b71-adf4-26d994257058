namespace :time_spent do
  desc "Fix time spent created by"
  task set_company_user_id: :environment do
    TimeSpent.where(company_user_id: nil).includes(:help_ticket_comment).where.not(help_ticket_comments: { contributor_id: nil }).find_each do |ts|
      ts.update_columns(company_user_id: CompanyUser.find_by(contributor_id: ts.help_ticket_comment.contributor_id)&.id)
    end

    TimeSpent.where(company_user_id: nil).includes(:help_ticket_activity).where.not(help_ticket_activities: { owner_id: nil }).find_each do |ts|
      ts.update_columns(company_user_id: ts.help_ticket_activity.owner_id)
    end
  end
end
