namespace :agent_location do
  desc "Populate manager contributor from company user"
  task update_managed_by_contributor_id: :environment do
    AgentLocation.where.not(company_user_id: nil).find_each do |agent|
      managed_by_contributor_id = agent.company_user&.contributor&.id
      agent.update_columns(managed_by_contributor_id: managed_by_contributor_id)
    end
  end

  desc "Delete agent locations inactive for the past 45 days"
  task delete_inactive_agent_locations: :environment do
    DeleteInactiveAgentLocationsWorker.perform_async
  end

  desc "Delete agent locations inactive for the past 45 days with last active null value"
  task delete_inactive_agent_locations_with_nil_last_active: :environment do
    agent_locations = AgentLocation.where("updated_at <= ? AND last_active IS NULL", 45.days.ago)
    ManagedAsset.unscoped.where(agent_location: agent_locations).update_all(agent_location_id: nil)
    agent_locations.delete_all
  end
end
