namespace :privileges do
  desc "Delete privileges where workspace_id is nil"
  task delete_privilege: :environment do
    Privilege.where(name: 'HelpTicket').where(workspace_id: nil).find_each do |p|
      p.destroy
    end
  end

  desc "Update scoped permissions to write all for all agents"
  task update_agents_permissions: :environment do
    Group.where.not(workspace_id: nil).find_each do |group|
      group.contributor.privileges&.where(name: 'HelpTicket')&.update_all(permission_type: 'write')
    end
  end
end
