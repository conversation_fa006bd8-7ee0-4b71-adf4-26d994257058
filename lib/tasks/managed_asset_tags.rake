namespace :managed_asset_tags do
  desc "It will add the company ID to managed asset tagss"
  task add_company_id_to_managed_asset_tags: :environment do
    ActiveRecord::Base.transaction do
      ManagedAssetTag.all.each do |tag|
        tag.update(company_id: tag&.managed_asset&.company_id)
      end
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
    end
  end
end