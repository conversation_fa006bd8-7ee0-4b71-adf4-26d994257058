namespace :helpdesk_settings do
  desc "Remove helpdesk_custom_form settings"
  task remove_custom_helpdesk_email: :environment do
    default = DefaultHelpdeskSetting.find_by(setting_type: "custom_helpdesk_email")
    default.helpdesk_settings.destroy_all!
    default.destroy!
  end

  desc 'Add enable_self_action_notification settings for old companies'
  task add_self_action_notification_settings: :environment do
    default = DefaultHelpdeskSetting.find_by(setting_type: 'allow_self_action_notification')

    Workspace.find_each do |workspace|
      HelpdeskSetting.create!(company: workspace.company, workspace: workspace, default_helpdesk_setting_id: default.id, enabled: true)
    end
  end

  desc 'Add allow_impacted_users_see_tickets settings for old companies'
  task allow_impacted_users_see_tickets_setting: :environment do
    default = DefaultHelpdeskSetting.find_by(setting_type: 'allow_impacted_users_see_tickets')

    Workspace.find_each do |workspace|
      HelpdeskSetting.create!(company: workspace.company, workspace: workspace, default_helpdesk_setting_id: default.id, enabled: false)
    end
  end

  desc "create settings for Comment attachment preview"
  task add_comment_attachemnt_preview_settings_in_help_desk: :environment do
    default = DefaultHelpdeskSetting.find_by(setting_type: "comment_attachment_preview")

    Workspace.find_each do |workspace|
      HelpdeskSetting.create!(company: workspace.company, workspace: workspace, default_helpdesk_setting_id: default.id, enabled: true)
    end
  end

  desc "create setting to only allow last response via email to be included as ticket comment"
  task only_include_last_response_in_ticket_comment_settings_in_help_desk: :environment do
    default = DefaultHelpdeskSetting.find_by(setting_type: "only_include_last_email_response_to_ticket_comment")

    Workspace.find_each do |workspace|
      HelpdeskSetting.find_or_create_by!(company: workspace.company, workspace: workspace, default_helpdesk_setting_id: default.id)
    end
  end

  desc "create setting to allow customization of help ticket number"
  task add_custom_ticket_number_company_setting: :environment do
    default = DefaultHelpdeskSetting.find_by(setting_type: "customize_ticket_number_for_help_desk")

    Workspace.find_each do |workspace|
      HelpdeskSetting.find_or_create_by!(company: workspace.company, workspace: workspace, default_helpdesk_setting_id: default.id, options: { custom_prefix: "GEN", custom_ticket_number: 1 })
    end
  end

  desc "create setting to disable adding email body attachments for help ticket comments"
  task add_disable_email_attachments_for_ticket_comments_setting: :environment do
    default = DefaultHelpdeskSetting.find_by(setting_type: 'disable_adding_email_body_attachments_for_help_ticket_comments')

    Workspace.find_each do |workspace|
      HelpdeskSetting.find_or_create_by!(company_id: workspace.company_id, workspace: workspace, default_helpdesk_setting_id: default.id, enabled: false)
    end
  end

  desc "create setting to set assigned to based on first agent to respond to a ticket"
  task add_set_assigned_to_first_agent_respond_to_ticket_setting: :environment do
    default = DefaultHelpdeskSetting.find_by(setting_type: 'set_assigned_to_first_agent_respond_to_ticket')

    Workspace.find_each do |workspace|
      HelpdeskSetting.find_or_create_by!(company_id: workspace.company_id, workspace: workspace, default_helpdesk_setting_id: default.id, enabled: false)
    end
  end

  desc 'Add allow_the_attachment_links_in_email setting in helpdesk settings and default helpdesk settings'
  task add_allow_the_attachment_links_in_email_setting: :environment do
    default_setting = DefaultHelpdeskSetting.find_by(setting_type: 'allow_the_attachment_links_in_email')
    raise 'Default helpdesk setting not found' if default_setting.nil?

    Workspace.find_each do |workspace|
      setting = HelpdeskSetting.find_or_initialize_by(
        company: workspace.company,
        workspace: workspace,
        default_helpdesk_setting: default_setting
      )
      if setting.new_record?
        setting.enabled = true
        setting.save!
      end
    end
  end

  desc 'Add missing self_action_notification setting in workspaces'
  task add_missing_self_action_notification_settings: :environment do
    default = DefaultHelpdeskSetting.find_by(setting_type: 'allow_self_action_notification')

    Workspace.find_each do |ws|
      new_setting = HelpdeskSetting.find_or_initialize_by(company_id: ws.company_id, workspace_id: ws.id, default_helpdesk_setting_id: default.id)
      if new_setting.new_record?
        new_setting.enabled = true
        new_setting.save!
      end
    end
  end

  desc 'Add disable_automated_tasks_for_status_field_resolution_comment settings for old companies'
  task add_disable_automatedt_tasks_for_status_field_resolution_comment_setting: :environment do
    default = DefaultHelpdeskSetting.find_by(setting_type: 'disable_automated_tasks_for_status_field_resolution_comment')

    Workspace.find_each do |workspace|
      HelpdeskSetting.find_or_create_by(company: workspace.company, workspace: workspace, default_helpdesk_setting_id: default.id, enabled: false)
    end
  end

  desc 'Update selected option for enabled self_action_notification settings'
  task update_self_action_notification_settings_options: :environment do
    default = DefaultHelpdeskSetting.find_by(setting_type: 'allow_self_action_notification')

    HelpdeskSetting.where(default_helpdesk_setting_id: default.id, enabled: true)
                   .update_all(selected_option: "admins_agents, other_users")
  end

  desc 'Update selected option value where nil for self_action_notification settings'
  task update_self_action_notification_settings_selected_option: :environment do
    default = DefaultHelpdeskSetting.find_by(setting_type: 'allow_self_action_notification')

    HelpdeskSetting.where(default_helpdesk_setting_id: default.id, selected_option: nil)
                   .update_all(selected_option: "")
  end

  desc 'Add default help desk agents group for automatic assignmment settings'
  task add_default_assignee_group: :environment do
    default = DefaultHelpdeskSetting.find_by(setting_type: 'set_assigned_to_first_agent_respond_to_ticket')
    default_groups_contibutor_ids = {}
    default_help_desk_groups = Group.where(name: "Help Desk Agents", default: true)

    default_help_desk_groups.find_each do |group|
      default_groups_contibutor_ids[group.company_id] = group.contributor_id
    end

    HelpdeskSetting.where(default_helpdesk_setting_id: default.id).find_each do |setting|
      begin
        default_group_id = default_groups_contibutor_ids[setting.company_id]
        setting.update_column(:options, { "assignee_groups" => [default_group_id] })
      rescue => e
        Rails.logger.warn e.message
        next
      end
    end
  end

  desc 'Add workspace agents group for automatic assignmment settings'
  task add_workspace_assignee_group: :environment do
    default = DefaultHelpdeskSetting.find_by(setting_type: 'set_assigned_to_first_agent_respond_to_ticket')
    workspaces = Workspace.includes(:helpdesk_settings, :groups).all
    workspaces.find_each do |workspace|
      begin
        setting = workspace.helpdesk_settings.find { |s| s.default_helpdesk_setting_id == default.id }
        if setting
          assignee_id = workspace.workspace_agents&.contributor_id  
          setting.update_column(:options, { "assignee_groups" => [assignee_id] })
        end
      rescue => e
        Rails.logger.warn e.message
        next
      end
    end
  end

  desc "create setting to enable modern design for open ticket"
  task add_modern_design_setting_in_help_desk: :environment do
    default = DefaultHelpdeskSetting.find_by(setting_type: "select_new_ui_for_help_center")

    Workspace.find_each do |workspace|
      HelpdeskSetting.create!(company: workspace.company, workspace: workspace, default_helpdesk_setting_id: default.id, enabled: false)
    end
  end

  desc "create setting to select hyperlinks color for open ticket"
  task add_hyperlinks_color_setting_in_help_desk: :environment do
    default = DefaultHelpdeskSetting.find_by(setting_type: "hyperlinks_color_for_help_center")

    Workspace.find_each do |workspace|
      HelpdeskSetting.create!(company: workspace.company, workspace: workspace, default_helpdesk_setting_id: default.id, enabled: false)
    end
  end

  desc "create setting to make articles public"
  task add_public_article_setting_in_help_desk: :environment do
    default = DefaultHelpdeskSetting.find_by(setting_type: "allow_public_articles_to_logged_out_users")

    Workspace.find_each do |workspace|
      HelpdeskSetting.create!(company: workspace.company, workspace: workspace, default_helpdesk_setting_id: default.id, enabled: false)
    end
  end

  desc "create setting to hide custom_forms from public view"
  task add_hide_all_public_custom_forms_setting_in_help_desk: :environment do
    default = DefaultHelpdeskSetting.find_by(setting_type: "allow_public_custom_forms_to_logged_out_users")

    Workspace.find_each do |workspace|
      HelpdeskSetting.create!(company: workspace.company, workspace: workspace, default_helpdesk_setting_id: default.id, enabled: true)
    end
  end

  desc "create setting to include workspace in help center"
  task add_include_workspace_in_help_center_setting_in_help_desk: :environment do
    default = DefaultHelpdeskSetting.find_by(setting_type: "include_workspace_in_help_center")
    Workspace.find_each do |workspace|
      HelpdeskSetting.create!(company: workspace.company, workspace: workspace, default_helpdesk_setting_id: default.id, enabled: true)
    end
  end

  desc "update default help desk settings followers"
  task update_default_helpdesk_settings_followers: :environment do
    old_setting = DefaultHelpdeskSetting.find_by(setting_type: "allow_making_email_recipients_ticket_impacted_users")
    if old_setting
      new_setting = DefaultHelpdeskSetting.find_by(setting_type: "allow_making_email_recipients_ticket_followers")
      HelpdeskSetting.where(default_helpdesk_setting_id: old_setting.id).update_all(default_helpdesk_setting_id: new_setting.id)
      old_setting.destroy!
    end
  end

  desc 'Add allow_followers_see_tickets settings for old companies'
  task allow_followers_see_tickets_setting: :environment do
    old_setting = DefaultHelpdeskSetting.find_by(setting_type: "allow_impacted_users_see_tickets")
    if old_setting
      new_setting = DefaultHelpdeskSetting.find_by(setting_type: "allow_followers_see_tickets")
      HelpdeskSetting.where(default_helpdesk_setting_id: old_setting.id).update_all(default_helpdesk_setting_id: new_setting.id)
      old_setting.destroy!
    end
  end

  desc "update default helpdesk_mailer impacted_user to followers"
  task update_default_helpdesk_mailer_impacted_user_to_followers: :environment do
    old_setting = DefaultMailer.find_by(event: "impacted_user_changed")
    if old_setting
      new_setting = DefaultMailer.find_by(event: "follower_changed")
      CompanyMailer.where(default_mailer_id: old_setting.id).update_all(default_mailer_id: new_setting.id)
      CompanyUserMailer.where(default_mailer_id: old_setting.id).update_all(default_mailer_id: new_setting.id)
      old_setting.destroy!
    end
  end

  desc 'Add custom logo and favicon settings for the old companies'
  task add_custom_logo_and_favicon_settings_for_old_companies: :environment do
    logo_default = DefaultHelpdeskSetting.find_by(setting_type: 'custom_logo_for_help_center')
    favicon_default = DefaultHelpdeskSetting.find_by(setting_type: 'custom_favicon_for_help_center')

    Workspace.find_each do |workspace|
      HelpdeskSetting.find_or_create_by(company: workspace.company, workspace: workspace, default_helpdesk_setting_id: logo_default.id)
      HelpdeskSetting.find_or_create_by(company: workspace.company, workspace: workspace, default_helpdesk_setting_id: favicon_default.id)
      puts "HelpDesk Setting Created successfully for workspace #{workspace.id}"
    end
  end

  desc 'Add email comment followers settings for the old companies'
  task add_email_comment_followers_settings_for_old_companies: :environment do
    comment_settings = DefaultHelpdeskSetting.find_by(setting_type: 'allow_making_email_comment_recipients_ticket_followers')

    Workspace.find_each do |workspace|
      HelpdeskSetting.find_or_create_by(company: workspace.company, workspace: workspace, default_helpdesk_setting_id: comment_settings.id)
      puts "HelpDesk Setting Created successfully for workspace #{workspace.id}"
    end
  end

  desc 'Add expand new comment section in help ticket'
  task expand_add_new_comment_section: :environment do
    default = DefaultHelpdeskSetting.find_by(setting_type: 'expand_add_new_comment_section')

    Workspace.find_each do |workspace|
      HelpdeskSetting.find_or_create_by(company: workspace.company, workspace: workspace, default_helpdesk_setting_id: default.id, enabled: true)
      puts "HelpDesk Setting Created successfully for workspace #{workspace.id}"
    end
  end

  desc "reorder and update default helpdesk settings"
  task reorder_rename_help_center_settings: :environment do
    settings_data = {
      "allow_followers_see_tickets" => {
        order: 0
      },
      "disable_automated_tasks_for_status_field_resolution_comment" => {
        order: 1
      },
      "allow_self_action_notification" => {
        order: 2
      },
      "set_assigned_to_first_agent_respond_to_ticket" => {
        order: 3
      },
      "customize_ticket_number_for_help_desk" => {
        order: 4
      },
      "select_new_ui_for_help_center" => {
        title: "Use modern Help Center design",
        description: "Enable the modern design for your Help Center.",
        order: 5
      },
      "bg_color_for_help_center" => {
        title: "Background color for Help Center",
        description: "Choose the background color for your Help Center.",
        order: 6
      },
      "hyperlinks_color_for_help_center" => {
        title: "Hyperlinks color for Help Center",
        description: "Choose the hyperlinks color for your Help Center.",
        order: 7
      },
      "allow_faq_page_to_logged_out_users" => {
        order: 8
      },
      "allow_public_articles_to_logged_out_users" => {
        order: 9
      },
      "allow_the_attachment_links_in_email" => {
        order: 10
      },
      "allow_incoming_requests" => {
        order: 11
      },
      "allow_non_user_to_get_update_about_their_tickets" => {
        order: 12
      },
      "allow_making_email_recipients_ticket_followers" => {
        order: 13
      },
      "only_include_last_email_response_to_ticket_comment" => {
        order: 14
      },
      "disable_adding_email_body_attachments_for_help_ticket_comments" => {
        order: 15
      },
      "response_to_closed_ticket" => {
        order: 16
      },
    }
    DefaultHelpdeskSetting.where(setting_type: settings_data.keys).find_each do |setting|
      setting_data = settings_data[setting.setting_type]
      setting.update(setting_data)
    end
  end

  desc "delete settings for help ticket modern view"
  task delete_settings_for_help_ticket_modern_view: :environment do
    HelpdeskSetting.where(default_helpdesk_setting: DefaultHelpdeskSetting.find_by(setting_type: "enable_modern_help_ticket_view")).destroy_all
  end
end
