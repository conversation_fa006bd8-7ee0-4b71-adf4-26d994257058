require Rails.root.join('app', 'models', 'concerns', 'form_icon_and_color')

namespace :custom_forms do
  include FormIconAndColor

  desc "add custom forms to companies"
  task add_forms: :environment do
    template = CustomFormTemplate.includes(:custom_form_field_templates).find_by(form_name: "Base Ticket")
    raise "Missing base ticket form template" unless template
    Company.find_each do |company|
      company.create_default_custom_base_form(template)
    end
  end

  desc "add location custom forms to companies"
  task add_location_forms: :environment do
    template = CustomFormTemplate.includes(:custom_form_field_templates).find_by(form_name: "Base Location")
    raise "Missing base location form template" unless template
    Company.find_each do |company|
      company.create_default_location_custom_base_form(template)
    end
  end

  desc "Add priority field to custom form"
  task add_priority: :environment do
    Company.find_each do |com|
      com.custom_forms.where(company_module: "helpdesk").find_each do |form|
        priority = form.custom_form_fields.find{ |fd| fd.field_attribute_type == "priority" && fd.name == "priority"  }
        unless priority
          field = form.custom_form_fields.create(
            field_attribute_type: "priority",
            order_position: nil,
            label: "Priority",
            options: CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS, # Use default colors in priority.rb
            default_value: "low",
            name: "priority"
          )
          FieldPosition.create(custom_form_field_id: field.id, position: "right")
        end
      end
    end
  end

  desc "Update priority flag colors"
  task update_priority_flag_colors: :environment do
    priority_fields = CustomFormField.where(field_attribute_type: "priority", options: "{\"low\":\"gold\",\"medium\":\"orange\",\"high\":\"red\"}")
    priority_fields.update_all(options: CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS) # Use default colors in priority.rb
  end

  desc "Set options in status field"
  task set_options_in_status: :environment do
    CustomFormFieldTemplate.where(field_attribute_type: "status").update_all(options: "[\"Open\", \"In Progress\", \"Closed\"]")
  end

  desc "Delete unused custon form attachments"
  task delete_unused_custom_form_attachment: :environment do
    CustomFormAttachment.where(custom_form_value_id: nil).destroy_all
  end

  desc "Set options in priority field"
  task set_options_in_priority: :environment do
    CustomFormFieldTemplate.where(field_attribute_type: "priority").update_all(options: CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS)
  end

  desc "Update list options structure from string to json array in db"
  task update_list_option_structure: :environment do
    CustomFormField.where(field_attribute_type: "list") do |f|
      f.update_columns(options: f.split(",").to_json)
    end
  end

  desc "Set priority field to low if it is not set"
  task set_priority_to_low: :environment do
    tickets = HelpTicket.where.not(id: CustomFormValue.joins(:custom_form_field).where(custom_form_fields: {name: "priority"}).pluck(:module_id))
    tickets.find_each do |t|
      if t.custom_form.present?
        field = t.custom_form.custom_form_fields.find_by(name: "priority", field_attribute_type: "priority")
        t.custom_form_values.create(
          custom_form_field_id: field.id,
          value_str: "low",
          custom_form_id: t.custom_form_id,
          help_ticket_id: t.id,
          company_id: t.company_id
        )
      end
    end
  end

  desc "Set field default value"
  task set_field_default_value: :environment do
    CustomFormFieldTemplate.where(name: "priority", default_value: nil, field_attribute_type: "priority").find_each do |f|
      f.update_columns(default_value: "low")
    end
    CustomFormFieldTemplate.where(name: "status", default_value: nil, field_attribute_type: "status").find_each do |f|
      f.update_columns(default_value: "Open")
    end
  end

  desc "Set default form"
  task set_default_form: :environment do
    Company.find_each do |com|
      if com.custom_forms.active.find_by(default: true).nil?
        com.custom_forms.active.first.update(default: true)
      end
    end
  end

  desc "Set HelpDesk default form"
  task set_helpdesk_default_form: :environment do
    Company.find_each do |com|
      if com.custom_forms.active.find_by(default: true, company_module: "helpdesk").nil?
        com.custom_forms.active.where(company_module: "helpdesk").first.update(default: true)
      end
    end
  end

  desc "Add status field to custom form"
  task add_status: :environment do
    Company.find_each do |com|
      com.custom_forms.find_each do |form|
        status = form.custom_form_fields.find{ |fd| fd.field_attribute_type == "status" && fd.name == "status"  }
        unless status
          field = form.custom_form_fields.create(
            field_attribute_type: "status",
            order_position: nil,
            label: "Status",
            options: "[\"Open\",\"In Progress\",\"Closed\"]",
            default_value: "Open"
          )
          FieldPosition.create(custom_form_field_id: field.id, position: "right")
        end
      end
    end
  end

  desc "Set status field to open if it is not set"
  task set_status_to_open: :environment do
    tickets = HelpTicket.where.not(id: CustomFormValue.joins(:custom_form_field).where(custom_form_fields: {name: "status"}).pluck(:help_ticket_id))
    tickets.find_each do |t|
      if t.custom_form.present?
        field = t.custom_form.custom_form_fields.find_by(name: "status", field_attribute_type: "status")
        t.custom_form_values.create(
          custom_form_field_id: field.id,
          value_str: "Open",
          custom_form_id: t.custom_form_id,
          help_ticket_id: t.id,
          company_id: t.company_id
        )
      end
    end
  end

  desc "Order form fields"
  task update_field_positions: :environment do
    Company.find_each do |com|
      com.custom_forms.find_each do |form|
        fields = form.custom_form_fields.where.not(name: ["subject", "priority", "status"])
        p_field = form.custom_form_fields.find_by(name: "priority", field_attribute_type: "priority")
        s_field = form.custom_form_fields.find_by(name: "status", field_attribute_type: "status")

        if p_field.order_position != 1
          p_field.update_columns(order_position: 1)
          fields.update_all('order_position = order_position + 1')
        end

        if s_field.order_position != 2
          s_field.update_columns(order_position: 2)
          fields.update_all('order_position = order_position + 1')
        end
      end
    end
  end

  desc "Updates custom forms address field to external-user type"
  task :update_form_address_field, [:subdomain] => :environment do |t, args|
    com = Company.find_by(subdomain: args[:subdomain])
    if com&.help_tickets
      com.help_tickets.find_each do |ticket|
        user_contributor_id = nil
        new_user = nil
        creator_field_value = ticket.custom_form_values.includes(:custom_form_field).find { |value| value.custom_form_field.name == "created_by" }
        email_address = HelpTicketFacade.new(ticket).get_custom_form_value("email_address")
        new_user = User.find_by(email: email_address) if email_address
        user_contributor_id = new_user.company_users.where(company_id: com.id).first&.contributor_id if new_user
        if user_contributor_id
          creator_field_value.update_columns(value_str: nil, value_int: user_contributor_id) 
        elsif email_address
          creator_field_value.update_columns(value_int: nil, value_str: email_address) 
        end
      end
    end
    if com&.custom_forms
      com.custom_forms.find_each do |form|
        created_by = form.custom_form_fields.find{ |fd| fd.name == "created_by"  }
        email_address_field = form.custom_form_fields.find{ |fd| fd.name == "email_address" }
        created_by.update_columns(private: false) if created_by
        email_address_field.destroy! if email_address_field && created_by
      end
    end
  end

  desc "Order form order"
  task add_order: :environment do
    Company.find_each do |com|
      i = 0
      com.custom_forms.find_each do |f|
        f.update(order: i)
        i += 1
      end
    end
  end

  desc "Adds missing permissions for everyone to custom form fields"
  task add_everyone_permission: :environment do 
    CustomForm.includes(:company).find_in_batches do |forms|
      forms.each do |form|
        company = form.company
        if company
          everyone = company.groups.find_by(name: 'Everyone', default: true)
          if everyone
            form.custom_form_fields.each do |field|
              if field.custom_form_field_permissions.where(can_edit: true).blank?
                field.custom_form_field_permissions.create!(contributor_id: everyone.contributor_id, can_edit: true)
              end
            end
          end
        end
      end
    end
  end

  desc "Adds admins to custom form fields permissions"
  task add_admin_permission_to_field: :environment do 
    CustomForm.includes(:company).find_in_batches do |forms|
      forms.each do |form|
        company = form.company
        admin_group = company.admins
        if admin_group
          form.custom_form_fields.each do |field|
            field.custom_form_field_permissions.find_or_create_by!(contributor_id: admin_group.contributor_id, can_edit: true)
          end
        end
      end
    end
  end

  desc "add staff custom forms to companies"
  task add_staff_forms: :environment do
    template = CustomFormTemplate.find_by(form_name: "Teammates")
    raise "Missing teammates form template" unless template
    Company.find_each do |company|
      company.create_staff_custom_forms([template.form_name])
    end
  end

  desc "Remove spaces after comma in status option"
  task remove_spaces_status_options: :environment do
    CustomForm.where(company_module: 'helpdesk').each do |cf|
      if cf.form_name == 'Base Ticket'
        status_field = cf.custom_form_fields.find_by(field_attribute_type: 'status')
        if status_field
          status_field.options = status_field.options.gsub(/, /, ',')
          status_field.save!
        end
      end
    end
  end

  desc "Adds helpdesk custom form"
  task adds_helpdesk_custom_form: :environment do
    CustomForm.helpdesk.includes(:company).find_in_batches do |forms|
      forms.each do |form|
        puts form.id
        company = form.company
        form.helpdesk_custom_form ||= begin
          helpdesk = HelpdeskCustomForm.new(custom_form: form,
                                            default: form.default,
                                            collect_closing_survey: form.collect_closing_survey,
                                            show_in_open_portal: form.show_in_open_portal
                                      )
          helpdesk
        end
        form.save!
      end
    end
  end

  desc "Adds helpdesk custom email"
  task adds_helpdesk_custom_email: :environment do
    Company.find_each do |company|
      form = company.custom_forms.helpdesk.find_by(default: true) || company.custom_forms.helpdesk.order(:created_at).first
      helpdesk = form.helpdesk_custom_form 
      custom_email = company.helpdesk_custom_emails.first

      if !custom_email || !custom_email.helpdesk_custom_form
        custom_email ||= HelpdeskCustomEmail.new(company: company,
                                                 email: "helpdesk@#{company.subdomain}.#{Rails.application.credentials.root_domain}",
                                                 verified: true)
        helpdesk.helpdesk_custom_email = custom_email
        helpdesk.save!
      end
    end
  end

  desc "Remove first space after comma in assigned to sort list"
  task remove_space_assigned_to_sort: :environment do
    CustomForm.where(company_module: 'helpdesk').each do |cf|
      if cf.form_name == 'Base Ticket'
        assigned_to_field = cf.custom_form_fields.find_by(field_attribute_type: 'people_list', name: 'assigned_to')
        if assigned_to_field
          assigned_to_field.sort_list = assigned_to_field.sort_list.sub(/\s+/, '')
          assigned_to_field.save!
        end
      end
    end
  end

  desc "Replace followers nil options with empty array"
  task replace_nil_options_with_empty_array: :environment do
    CustomForm.where(company_module: 'helpdesk').each do |cf|
      imapcted_users_field = cf.custom_form_fields.find_by(field_attribute_type: 'people_list', name: 'followers')
      if imapcted_users_field && imapcted_users_field.options.nil?
        imapcted_users_field.options = '[]'
        imapcted_users_field.save!
      end
    end
  end

  desc "Add / in locations custom form State Province label"
  task add_slash_in_state_province: :environment do
    CustomForm.where(company_module: 'location').each do |cf|
      state_province_field = cf.custom_form_fields.find_by(field_attribute_type: 'list', label: 'State Province', name: 'state')
      if state_province_field
        state_province_field.update_columns(label: 'State/Province')
      end
    end
  end

  desc "Delete double state prvoince field in locations"
  task delete_state_province_double_fields: :environment do
    CustomForm.where(company_module: 'location').each do |cf|
      state_province_field = cf.custom_form_fields.find_by(field_attribute_type: 'list', label: 'State Province', name: 'state')
      if state_province_field
        state_province_field.destroy!
      end
    end
  end

  desc "add default status in helpdesk custom forms"
  task add_default_status_in_helpdesk_cf: :environment do
    HelpdeskCustomForm.where(default: nil).each do |form|
      form.default = CustomForm.find_by(id: form.custom_form_id).default
      form.save!
    end
  end

  desc "Add description field to custom form"
  task add_description: :environment do
    Company.find_each do |com|
      com.custom_forms.where(company_module: "helpdesk").find_each do |form|
        description = form.custom_form_fields.find_by(field_attribute_type: 'rich_text', name: "description")
        unless description
          position = form.custom_form_fields.length
          field = form.custom_form_fields.create!(
            field_attribute_type: "rich_text",
            order_position: position,
            label: "Description",
            name: "description"
          )
          FieldPosition.create(custom_form_field_id: field.id, position: "left")
        end
      end
    end
  end

  desc "Set missing company users default forms"
  task set_company_user_default_form: :environment do
    Company.all.find_each do |company|
      company_users_forms = company.custom_forms.active.where(company_module: "company_user")
      if company_users_forms.count > 0 && company_users_forms.where(default: true).count == 0
        base_user_form = company_users_forms.find_by(form_name: "Teammates")
        if base_user_form.present?
          base_user_form.update_columns(default: true)
        else
          company_users_forms.first.update_columns(default: true)
        end
      end
    end
  end

  desc "Set missing locations default forms"
  task set_location_default_form: :environment do
    Company.all.find_each do |company|
      location_forms = company.custom_forms.active.where(company_module: "location")
      if location_forms.count > 0 && location_forms.where(default: true).count == 0
        base_location_form = location_forms.find_by(form_name: "Base Location")
        if base_location_form.present?
          base_location_form.update_columns(default: true)
        else
          location_forms.first.update_columns(default: true)
        end
      end
    end
  end

  desc "Fix helpdesk custom data"
  task fix_helpdesk_custom_data: :environment do
    forms = []
    HelpdeskCustomForm.where(default: nil).each do |form|
      form.default = CustomForm.find_by(id: form.custom_form_id).default
      forms << form unless form.save
    end

    forms.each do |f|
      if f.email.include?("gogenuity.com")
        f.email = f.custom_form.default_email
        f.save!
      end
    end
  end

  desc "Rename company module Base User form to Teammates"
  task rename_base_user_to_teammates: :environment do
    CustomForm.where(company_module: 'company_user', form_name: 'Base User').update_all(
      form_name: 'Teammates',
      icon: 'https://nulodgic-static-assets.s3.amazonaws.com/images/buddy.png',
      color: generate_random_color,
      description: 'For members of your company.'
    )
  end

  desc "Set random icons, form colors and description for Company User forms"
  task set_people_forms_icons_colors_descriptions: :environment do
    CustomForm.where(company_module: 'company_user').where.not(form_name: 'Teammates').find_each do |custom_form|
      custom_form.update_columns(
        icon: generate_random_icon,
        color: generate_random_color,
        description: "For #{custom_form.form_name.downcase.gsub('copy','').strip.pluralize} of your company."
      )
    end
  end

  desc "Create people Partners and Contractors custom forms"
  task create_people_partners_and_contractors_forms: :environment do
    CreateCompanyPeopleFormsWorker.perform_async()
  end

  desc "Set icons, colors and description for MSP Templates Company User forms"
  task set_people_template_frms_icons_colors_descriptions: :environment do
    custom_forms = Msp::Templates::CustomForm.where(company_module: "company_user", icon: nil,  color: nil, description:nil)
    custom_forms.find_each do |custom_form|
      begin
        custom_form.update_columns(
          icon: "https://s3.amazonaws.com/nulodgic-static-assets/images/people_defaults/default24_thumbnail.png",
          color: generate_random_color,
          description: "A handy form for your company."
        )
      rescue => e
        puts "faulty_form: #{custom_form.id} , exception: #{e.message}"
        next
      end
    end
  end

  desc 'Update custom form fields with duplicate names'
  task update_fields: :environment do
    CustomForm.where(company_module: 'helpdesk').find_each do |custom_form|
      field_names_count = Hash.new { |hash, key| hash[key] = { count: 0, suffix: 1 } }

      custom_form.custom_form_fields.order(:created_at).each do |field|
        lowercase_name = field.name.downcase

        if field_names_count[lowercase_name][:count] > 0
          updated_name = "#{lowercase_name}_#{field_names_count[lowercase_name][:suffix] + 1}"
          field.update_columns(name: updated_name)

          field_names_count[lowercase_name][:suffix] += 1
        else
          field_names_count[lowercase_name][:count] += 1
        end
      end
    end
  end

  desc "Rename impacted_users field to followers and make audience guests"
  task rename_impacted_users_field_to_followers_and_make_audience_guests: :environment do
    CustomForm.where(company_module: 'helpdesk').find_each do |cf|
      impacted_users_field = cf.custom_form_fields.find_by(field_attribute_type: 'people_list', name: 'impacted_users')    
      if impacted_users_field 
        impacted_users_field.update_columns(name: 'followers', audience: 'guests')
      end
    end
  end
end
