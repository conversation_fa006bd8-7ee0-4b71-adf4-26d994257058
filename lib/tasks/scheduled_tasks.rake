namespace :scheduled_tasks do
  desc 'Send scheduled tasks'
  task send_scheduled_tasks: :environment do
    ScheduledTasksWorker.perform_async()
  end

  desc 'Convert scheduled_tasks to utc scheduled tasks'
  task convert_scheduled_tasks_to_utc: :environment do
    faulty_tasks =[]
    puts "Executing"

    ScheduledTask.where
                 .not(scheduled_at: nil)
                 .where('scheduled_at >= ?', Date.today)
                 .find_each do |task|
      puts "-----------------------"    
      puts "Executing"
    
      begin
        puts "Updating task #{task.id}"
        date = task.scheduled_at
        hours, minutes, zone = DateUtils.parse_time_zone_and_start_time(task.task_started_at['time_zone'], task.task_started_at['start_time'])
        utc_scheduled_at = zone.local(date.year, date.month, date.day, hours, minutes).utc.to_date
    
        task.update_column(:scheduled_at, utc_scheduled_at)
      rescue => e
        puts "Faulty task found! #{e.message}"
        faulty_tasks << task.id
      end
    end
  end
end
