namespace :audits do
  desc "removes location_id changes from assignment_information"
  task remove_location_id: :environment do
    Audited::Audit.where(auditable_type: 'AssignmentInformation').find_each do |audit|
      location_ids = audit.audited_changes.delete("location_id")

      if location_ids && audit.action == 'update'
        asset = audit.associated
        new_audit = Audited::Audit.new(
          action: "update",
          associated_id: asset.hardware_detail.id,
          associated_type: asset.hardware_detail_type,
          auditable_id: asset.id,
          auditable_type: "ManagedAsset",
          audited_changes: {"location_id" => location_ids},
          comment: nil,
          remote_address: audit.remote_address,
          request_uuid: audit.request_uuid,
          user_id: audit.user_id,
          user_type: audit.user_type,
          username: nil,
          version: audit.version
        )
        # new_audit.save!
        new_audit.created_at = audit.created_at
        new_audit.save!
      end
      audit.save!
    end
  end

  desc "delete audits for vendors, telecom services, groups, locations, help tickets & company users"
  task delete_unnecessary_audits: :environment do
    types_to_delete = ["Vendor", "CompanyUser", "TelecomService", "Location", "Group", "HelpTicket"]
    Audited::Audit.where(associated_type: types_to_delete).destroy_all
  end

  desc "delete audits for assets system_up_time changes"
  task delete_system_up_time_history_from_assets: :environment do
    Audited::Audit.where(auditable_type: "ManagedAsset",
                         associated_type: "ComputerDetail",
                         action: "update")
                  .where("audited_changes ilike ?" ,"%system_up_time%")
                  .find_each do |audit|
                    audit.audited_changes.delete("system_up_time")
                    audit.update_columns(audited_changes: audit.audited_changes)
                  end
  end
end
