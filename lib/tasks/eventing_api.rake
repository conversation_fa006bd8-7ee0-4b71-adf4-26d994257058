namespace :eventing_api do
  desc "TODO"
  task create_users: :environment do
    CompanyUser.where.not(company_users: {granted_access_at: nil}).each do |u|
      Events::CreateRemoteUserWorker.perform_async({ id: u.id }.as_json)
    end
  end

  desc "check for non-existent users in the eventing API"
  task check_users: :environment do
    CompanyUser.where.not(company_users: {granted_access_at: nil}).each do |u|
      Events::CheckRemoteUserWorker.perform_async(id: u.id)
    end
  end

  desc "first admin of company will get reward points of previously created entities (contract, managed assets, help_tickets, etc.)"
  task create_events: :environment do
    Contract.unscoped.all.each do |contract|
      if company = contract.company
        admin_user = company.user_roles.admin.first.company_users.first
        contract.created_by = admin_user
        contract.event_type = "created"
        contract.award_points_to_user
      end
    end

    ManagedAsset.all.each do |manages_asset|
      if company = manages_asset.company
        admin_user = company.user_roles.admin.first.company_users.first
        manages_asset.created_by = admin_user
        manages_asset.event_type = "created"
        manages_asset.award_points_to_user
      end
    end

    HelpTicket.unscoped.all.each do |help_ticket|
      if company = help_ticket.company
        admin_user = company.user_roles.admin.first.company_users.first
        help_ticket.created_by = admin_user
        help_ticket.event_type = "created"
        help_ticket.award_points_to_user
      end
    end

    CompanyUser.unscoped.all.each do |company_user|
      if company = company_user.company
        admin_user = company.user_roles.admin.first.company_users.first
        company_user.created_by = admin_user
        company_user.event_type = "created"
        if admin_user.present?
          company_user.award_points_to_user
        end
      end
    end

    Vendor.unscoped.all.each do |vendor|
      if company = vendor.company
        admin_user = company.user_roles.admin.first.company_users.first
        vendor.created_by = admin_user
        vendor.event_type = "created"
        vendor.award_points_to_user
      end
    end

    TelecomService.unscoped.all.each do |telecom_service|
      if company = telecom_service.company
        admin_user = company.user_roles.admin.first.company_users.first
        telecom_service.created_by = admin_user
        telecom_service.event_type = "created"
        telecom_service.award_points_to_user
      end
    end
  end
end
