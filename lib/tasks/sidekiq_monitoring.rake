namespace :sidekiq_monitoring do
  desc "Send a Slack alert for high-latency queues"
  task slack_alert_for_high_latency_queues: :environment do
    if Rails.env.production?
      high_latency_queues = []
      critical_high_latency_queues = []
      early_warning_queues = []

      Sidekiq::Stats.new.queues.each do |queue, _|
        next if ['integrations', 'long_integrations'].include?(queue)
        process_queues(queue, high_latency_queues, critical_high_latency_queues)
      end

      if early_warning_queues.any?
        alert = "Warning: Please check sidekiq queues"
        slack_configuration.chat_postMessage(channel: "#dev-support", text: alert)
      end

      if high_latency_queues.any?
        alert = "High latency detected in queues: #{high_latency_queues.join(", ")}"
        slack_configuration.chat_postMessage(channel: "#dev-support", text: alert)
      end

      if critical_high_latency_queues.any?
        alert = "High latency detected in critical/automated_tasks queues: #{critical_high_latency_queues.join(", ")}"
        slack_configuration.chat_postMessage(channel: "#support", text: alert)
      end
    end
  end
end

private

def slack_configuration
  return @client if @client.present?

  Slack.configure do |config|
    config.token = Rails.application.credentials.slack[:api_token]
  end
  @client = ::Slack::Web::Client.new
  @client.auth_test
  @client
end

def process_queues(queue, high_latency_queues, critical_high_latency_queues)
  seconds_in_an_hour = 3600
  seconds_in_three_minutes = 180
  seconds_in_five_minutes = 300
  seconds_in_a_minute = 60
  seconds_in_two_minutes = 120
  # critical_queues = %w[automated_tasks critical]

  queue_latency = Sidekiq::Queue.new(queue).latency
  latency_in_minutes = (queue_latency / seconds_in_a_minute).round(1)
  if queue == "critical" && queue_latency / seconds_in_two_minutes >= 1
    early_warning_queues << "#{queue} (latency: #{latency_in_minutes} minutes)"
  end
  if queue == "critical" && queue_latency / seconds_in_five_minutes >= 1
    critical_high_latency_queues << "#{queue} (latency: #{latency_in_minutes} minutes)"
  end
  if queue == "automated_tasks" && queue_latency / seconds_in_three_minutes >= 1
    critical_high_latency_queues << "#{queue} (latency: #{latency_in_minutes} minutes)"
  end
  if queue == "at_actions" && queue_latency / seconds_in_three_minutes >= 1
    critical_high_latency_queues << "#{queue} (latency: #{latency_in_minutes} minutes)"
  end
  high_latency_queues << "#{queue} (latency: #{latency_in_minutes} minutes)" if queue_latency / seconds_in_an_hour >= 1
end
