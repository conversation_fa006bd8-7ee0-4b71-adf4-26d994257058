namespace :email_format do
  desc 'download and re-upload header and footer images to public s3 service for last two weeks'
  task download_and_reupload_email_format_images: :environment do
    six_weeks_ago = 6.weeks.ago
    email_formats = EmailFormat.where("created_at >= :six_weeks_ago OR updated_at >= :six_weeks_ago", six_weeks_ago: six_weeks_ago)

    #downloads the images
    email_formats.each do |email_format|
      attachments = [
        { attachment: email_format.header_image, folder: "HeaderImages" },
        { attachment: email_format.footer_image, folder: "FooterImages" }
      ]

      attachments.each do |attachment_info|
        attachment = attachment_info[:attachment]
        folder_name = attachment_info[:folder]
        if attachment.present?
          blob = attachment.blob
          folder_path = File.join(Dir.home, folder_name, email_format.id.to_s)
          FileUtils.mkdir_p(folder_path)

          file_path = File.join(folder_path, blob.filename.to_s)
          File.binwrite(file_path, attachment.download)
        end
      end
    end

    #re-uploads/updates the images
    def process_images(directory_path, attachment_type)
      Dir.foreach(directory_path) do |folder_name|
        folder_path = File.join(directory_path, folder_name)
        next unless File.directory?(folder_path)

        email_format_id = folder_name.to_i
        email_format = EmailFormat.find_by(id: email_format_id)

        if email_format.present?
          image_file = Dir.glob(File.join(folder_path, "*")).first

          if image_file.present?
            filename = File.basename(image_file)
            content_type = Mime::Type.lookup_by_extension(File.extname(image_file)[1..]).to_s
            attachment = attachment_type === :header ? email_format.header_image : email_format.footer_image
            attachment.attach(io: File.open(image_file), filename: filename, content_type: content_type)
            email_format.save
          end
        end
      end
    end

    header_directory_path = File.join(Dir.home, "HeaderImages")
    footer_directory_path = File.join(Dir.home, "FooterImages")

    process_images(header_directory_path, :header)
    process_images(footer_directory_path, :footer)

    EmailFormat.find_each do |email_format|
      header_image_blob = email_format.header_image.blob
      footer_image_blob = email_format.footer_image.blob

      header_image_blob.update_columns(service_name: "amazon_public") if header_image_blob.present?
      footer_image_blob.update_columns(service_name: "amazon_public") if footer_image_blob.present?
    end
  end

  desc 'enable all existing email formats'
  task enable_existing_email_formats: :environment do
    EmailFormat.update_all(enabled: true)
  end

  desc "delete email formats which are in disabled state"
  task delete_disabled_email_formats: :environment do
    EmailFormat.where(enabled: false).destroy_all
  end
end
