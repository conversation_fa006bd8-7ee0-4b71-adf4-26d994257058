namespace :categories do
  desc "add uncategorizes to categories"
  task add_uncategorized: :environment do
    # c = Category.find_or_initialize_by({ name: "Uncategorized" })
    # c.id = 0
    # c.save!
  end

  desc "set company categories from default categories"
  task create_company_categories: :environment do
    ActiveRecord::Base.transaction do
      Company.all.each do |company|
        company.create_categories
        old_categories = Category.where(company_id: nil)
        belongs_to_associations = Category.reflect_on_all_associations(:has_many)

        belongs_to_associations.each do |association|
          categorized_records = company.send(association.name).where.not(category_id: nil)
          categorized_records.find_each do |record|
            old_category = old_categories.find_by(id: record.category_id)
            if old_category.present?
              new_category = company.categories.find_by(name: old_category.name)
              record.update(category_id: new_category.id)
            end
          end
        end
      end
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
    end
  end

  desc "Update company categories from IT to IT and Security"
  task update_company_it_category: :environment do
    ActiveRecord::Base.transaction do
      Company.joins(:categories).where("categories.name = ?", "IT and Security").each do |it_sec_comp|
        it_category = it_sec_comp.categories.find_by(name: "IT")
        it_sec_category = it_sec_comp.categories.find_by(name: "IT and Security")
        if it_category 
          it_category.contracts.each do |contract|
            contract.category_id = it_sec_category.id
            contract.save!
          end
          it_category.general_services.each do |general_service|
            general_service.category_id = it_sec_category.id
            general_service.save!
          end
          it_category.vendors.each do |vendor|
            vendor.category_id = it_sec_category.id
            vendor.save!
          end
          it_category.products.each do |product|
            product.category_id = it_sec_category.id
            product.save!
          end

          it_category.destroy
        end
      end
      Category.where(name: "IT", module_type: 'company').each do |category|
        category.update!(name: "IT and Security")
      end
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
    end
  end

  desc "adds categories in workspaces"
  task add_categories_in_workspaces: :environment do
    Category.update_all(module_type: 'company')
    Workspace.find_each do |workspace|
      categories = []
      categories = workspace.company.categories.pluck(:name).map do |cat_name|
        {
          name: cat_name,
          company_id: workspace.company_id,
          workspace_id: workspace.id,
          module_type: 'helpdesk',
        }
      end
      Category.insert_all(categories)
    end
  end
end
