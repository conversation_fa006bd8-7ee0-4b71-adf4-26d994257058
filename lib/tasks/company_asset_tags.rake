namespace :company_asset_tags do
  desc "It will default tags to company asset tags"
  task add_default_tags_to_company_asset_tags: :environment do
    default_tags = ["team", "essers", "mobile", "inventory", "district funds"]
    asset_tags = []
    Company.all.find_each do |company|
      default_tags.each do |tag|
        asset_tags << { name: tag.downcase, company_id: company.id }
      end
    end
    CompanyAssetTag.upsert_all(asset_tags, unique_by: [:name, :company_id])
  end

  desc "Add managed asset tags to company asset tags"
  task add_managed_asset_tags_to_company_asset_tags: :environment do
    asset_tags = []
    Company.all.find_each do |company|
      managed_asset_tags = company.managed_asset_tags.pluck(:tag).map(&:downcase).uniq
      already_exist_company_asset_tags = company.asset_tags.pluck(:name)
      new_tags = managed_asset_tags - already_exist_company_asset_tags
      new_tags.each do |tag|
        asset_tags << { name: tag, company_id: company.id }
      end
    end
    CompanyAssetTag.upsert_all(asset_tags, unique_by: [:name, :company_id]) if asset_tags.present?
  end

  desc "Associate company asset tags with managed asset"
  task associate_company_asset_tags_with_managed_asset: :environment do
    Company.all.find_each do |company|
      company_asset_tags = company.asset_tags.pluck(:name, :id).to_h
      company.managed_asset_tags.find_each do |tag|
        tag.update(company_asset_tag_id: company_asset_tags[tag.tag])
      end
    end
  end

  desc "Update the audits for company asset tags"
  task update_audit_for_company_asset_tags: :environment do
    managed_assets_tags_lookup = ManagedAssetTag.joins(:company_asset_tag).pluck('managed_asset_tags.id', 'company_asset_tags.id').to_h
    Audited::Audit.where(auditable_type: "ManagedAssetTag").find_in_batches(batch_size: 1000) do |audits|
      updates = audits.map do |audit|
        audit.audited_changes.merge("company_asset_tag_id" => managed_assets_tags_lookup[audit.auditable_id])
        audit.attributes
      end
      Audited::Audit.upsert_all(updates)
    end
  end
end
