namespace :groups do
  desc "Converts roles over to groups"
  task convert_roles: :environment do
    Company.find_each do |company|
      company.user_roles.find_each do |user_role|
        if user_role.name != "User"
          group_name = user_role.name.pluralize
          group = company.groups.find_or_create_by(name: group_name)
          if group.contributor.privileges.blank?
            user_role.user_role_privileges.each do |p|
              group.contributor.privileges.find_or_create_by(name: p.privilege_name, permission_type: p.permission_type)
            end
            user_role.company_users.find_each do |company_user|
              group.group_members.find_or_create_by(contributor: company_user.contributor) if company_user.contributor.present?
            end
          end
          group.save!
        end
      end
    end
  end

  desc "Remove duplicate members from admins group"
  task remove_duplicate_members: :environment do
    sample_company_users_id = Company.find_by(is_sample_company: true).company_users.pluck(:id)

    Company.where.not(is_sample_company: true).each do |company|
      admin_group = company.groups.find_by(name: "<PERSON><PERSON>")
      if admin_group.present?
        admin_group.group_members.joins(contributor: :company_user).where(company_users: { id: sample_company_users_id }).destroy_all
      end
    end
  end

  desc 'Remove privileges where workspace does not exist'
  task remove_privileges_without_workspace: :environment do
    Privilege.where(name: 'HelpTicket', workspace_id: nil).destroy_all
  end
end
