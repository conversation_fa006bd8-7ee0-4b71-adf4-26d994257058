namespace :assets do
  desc "set company asset type from default asset types"
  task create_company_asset_type: :environment do
    ActiveRecord::Base.transaction do
      Company.all.find_each do |company|
        AssetType.all.find_each do |default_asset_type|
          asset_type_exist = company.asset_types.exists?(default_asset_type.name)
          company.asset_types.find_or_create_by(default_asset_type.attributes.except("id", "created_at")) if !asset_type_exist
        end
      end
    end
  end

  desc "Update asset type data according to company asset type table"
  task update_asset_types: :environment do
    ActiveRecord::Base.transaction do
      Company.find_each do |company|
        company.managed_assets.find_each do |mng_asset|
          mng_asset_type = AssetType.find_by_id(mng_asset.asset_type_id)
          if mng_asset_type
            com_asset_type = company.asset_types.find_by_name(mng_asset_type.name)
            mng_asset.update_columns(company_asset_type_id: com_asset_type.id)
          end
        end
      end
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
    end
  end

  desc "Update assets status if it is nil"
  task update_assets_status: :environment do
    ManagedAsset.where(status: nil).update_all(status: 0)
  end

  desc "deleting duplicate 'Windows' asset type"
  task delete_duplicate_windows_asset_type: :environment do
    
    Company.find_each do |company|
      window_asset_type = company.asset_types.where(name: "Windows")
      if window_asset_type.count > 1
        company.managed_assets.where(company_asset_type_id: window_asset_type[1].id)
               .update_all(company_asset_type_id: window_asset_type[0].id)
        window_asset_type[1].destroy
      end
    end
  end 

  desc "Update managed assets if model is nil"
  task set_managed_asset_default_model: :environment do
    ManagedAsset.unscoped.where(model: nil).update_all(model: '')
  end

  desc "Update all managed asset whose cost doesn't exist"
  task update_managed_asset_cost: :environment do
    ManagedAsset.left_outer_joins(:cost).where(costs: { id: nil }).each do |ma|
      ma.create_cost(
        cost: 0.0
      )
    end
  end

  desc "Update managed asset cost whose values are nil"
  task update_managed_asset_cost: :environment do
    Cost.where(cost: nil).each do |c|
      c.cost = 0.0
      c.save_without_auditing
    end
  end

  desc "Update managed asset manufacturer column whose values are nil"
  task update_managed_asset_manufacturer_column: :environment do
    ManagedAsset.unscoped.where(manufacturer: nil).each do |asset|
      asset.manufacturer = ''
      asset.save_without_auditing
    end
  end

  desc "Assining hardwared detail of managed assets to switch type discovered assets"
  task assign_switch_hardware_detail: :environment do
    DiscoveredAsset.where(asset_type: "Switch").where.not(managed_asset_id: nil).find_each do |da|
      if da.managed_asset.hardware_detail.class.name == "NetworkingDeviceDetail"
        da.hardware_detail = da.managed_asset.hardware_detail
        da.save!
      end
    end
  end

  desc "Adding new discovered hardware detail to discovered asset that does not have discovered hardware detail"
  task add_new_dis_hardware_detail: :environment do
    dis_valid_keys = [
      "available_memory", "black_toner_left_percent", "black_toner_name", "cyan_toner_left_percent", 
      "cyan_toner_name", "device_id", "disk_encryption", "disk_free_space", "dns", "domain_name", "gateway",
      "harddisk", "hardware_version", "hostname", "magenta_toner_left_percent", "magenta_toner_name", "memory",
      "network", "personalities_installed", "ports", "processor", "processor_architecture", "processor_cores",
      "processor_logical_cores", "product_number", "public_ip", "remote_ip_address", "screen_size", "service_id",
      "ssids", "total_page_count", "total_pcl_five_page_count", "total_pcl_six_page_count",
      "total_post_script_page_count", "used_memory", "wan_ips", "yellow_toner_left_percent", "yellow_toner_name"
    ]
    
    DiscoveredAsset.all.find_each do |da|
      any_detail_present = false
        dis_valid_keys.each do |key|
          if da[key].present?
            any_detail_present = true
            break
          end
        end
  
      if da.discovered_assets_hardware_detail.blank? && any_detail_present
        hardware_detail = DiscoveredAssetsHardwareDetail.new(discovered_asset_id: da.id)
        valid_keys = hardware_detail.attributes.keys - ["id", "created_at", "updated_at", "discovered_asset_id"]
        valid_keys.each do |key|
          if key === "hard_drive"
            hardware_detail[key] = da["harddisk"]
          elsif key === "processor"
            hardware_detail[key] = da["processor_name"]
          else
            hardware_detail[key] = da[key]
          end
        end
        hardware_detail.save!
      end
    end
  end
end
