namespace :assignment_information do
  desc "Convert the user and manger to contributor form company user"
  task convert_user_manager_to_contributor: :environment do
    AssignmentInformation.find_each do |assignment_information|
      assignment_information.update_columns(used_by_id: assignment_information.user&.contributor_id,
                                            managed_by_id: assignment_information.manager&.contributor_id);
    end
  end

  desc "Update null departments to empty string "
  task update_departments: :environment do
    AssignmentInformation.where(department: nil).update_all(department: '')
  end

  desc "Remove alreday destroyed managed by and used by users"
  task update_used_by_and_managed_by: :environment do
    AssignmentInformation.where.not(used_by_contributor_id: nil).each do |asi|
      cu = CompanyUser.find_by(archived_at: nil, contributor_id: asi.used_by_contributor_id)
      gu = Group.find_by(contributor_id: asi.used_by_contributor_id)
      if cu.blank? && gu.blank?
        asi.update_column(:used_by_contributor_id, nil)
      end
    end

    AssignmentInformation.where.not(managed_by_contributor_id: nil).each do |asi|
      cu = CompanyUser.find_by(archived_at: nil, contributor_id: asi.managed_by_contributor_id)
      gu = Group.find_by(contributor_id: asi.managed_by_contributor_id)
      if cu.blank? && gu.blank?
        asi.update_column(:managed_by_contributor_id, nil)
      end
    end
  end
end
