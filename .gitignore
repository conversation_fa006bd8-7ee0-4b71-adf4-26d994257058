# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

#Ignoring the Webstorm files
.idea

# Ignore bundler config.
/.bundle

# Ignore all logfiles and tempfiles.
/log/*
/screenshots/*
/tmp/*
!/log/.keep
!/tmp/.keep
dump.rdb
*.log
.vscode/*

# Ignore Byebug command history file.
.byebug_history

# Ignore system and editor files
.DS_Store
/.vscode

# Don't include sensitive files
.env
.env.local
.env.test
.env.old
.env.backup

# Elastic Beanstalk Files
.elasticbeanstalk/*
!.elasticbeanstalk/*.cfg.yml
!.elasticbeanstalk/*.global.yml


/public/packs-test
/node_modules
yarn-debug.log*
.yarn-integrity
coverage

*.key
/config/master.key

/config/credentials/development.key

/config/credentials/test.key

/config/credentials/staging.key

/public/packs
/node_modules
/yarn-error.log
yarn-debug.log*
.yarn-integrity

/config/credentials/production.key
storage/

.nvmrc
