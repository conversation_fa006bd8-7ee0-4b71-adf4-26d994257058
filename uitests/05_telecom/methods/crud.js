import common from '../../common/helpers/common.js'
import { Selector } from 'testcafe'
import telecom from '../helpers/telecom_variables.js'
import location from '../../14_locations/helpers/location_variables.js'

const telecomFilterStatus = Selector('#tc-telecom-filterStatus');
const telecomVendor = Selector('[data-tc-multi-select-vendor]');
const companylocation = Selector('[data-tc-location]');
const locationCategory = companylocation.find('option');

export async function telecomFormValidation(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/telecom_services`)
}

export async function createTelecom(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/telecom_services/new`)
  await t.click('[data-tc-telecom-voice]')
  await t.typeText("[data-tc-telecom-name]", telecom.telecomParams.name)
  await t.pressKey('shift+tab')
  await t.pressKey('enter')
  await t.click('[data-tc-service-type-select]')
  await t.click('[data-tc-service-type-option]')
  await t.click('[data-tc-currency]')
  await t.typeText('[data-tc-currency]', `${telecom.telecomParams.cost}`)
  await t.typeText('form textarea#general_service_description', telecom.telecomParams.notes)
  await t.click('[data-tc-save-form-btn="Save Telecom Provider"]')
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}

export async function archiveTelecom(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/telecom_services`)
  await t.typeText('input[data-tc-telecom-search]', telecom.telecomParams.name)
  await t.click(Selector('.box__inner').withText(telecom.telecomParams.name))
  await t.click(Selector("#tc-archive-telecom-btn"))
  await t.click(Selector("#tc-telecom-archive-modal-btn"))
  await t.expect(Selector('.notification-content').innerText).eql('Service was successfully archived.')
}

export async function unArchiveTelecom(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/telecom_services`)
  await t.typeText('input[data-tc-telecom-search]', telecom.telecomParams.name)
  await t
    .click(telecomFilterStatus)
  await t.click(Selector("#tc-telecom-filterStatus .dropdown-menu a:nth-child(3)"))
  await t.click(Selector('.box__inner').withText(telecom.telecomParams.name))
  await t.click(Selector("#tc-unarchive-telecom-btn"))
  await t.click(Selector("#tc-telecom-un-archive-modal-btn"))
  await t.expect(Selector('.notification-content').innerText).eql('Service was successfully unarchived.')
}

export async function deleteTelecom(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/telecom_services`)
  await t.typeText('input[data-tc-telecom-search]', telecom.telecomParams.name)
  await t.click(Selector('.box__inner').withText(telecom.telecomParams.name))
  await t.click(Selector("#tc-archive-telecom-btn"))
  await t.click(Selector("#tc-telecom-archive-modal-btn"))
  await t.expect(Selector('.notification-content').innerText).eql('Service was successfully archived.')
  await t.typeText('input[data-tc-telecom-search]', telecom.telecomParams.name)
  await t
    .click(telecomFilterStatus)
  await t.click(Selector("#tc-telecom-filterStatus .dropdown-menu a:nth-child(3)"))
  await t.click(Selector('.box__inner').withText(telecom.telecomParams.name))
  await t.click(Selector("#tc-delete-telecom-btn"))
  await t.click(Selector("#tc-delete-telecom-modal-btn"))
  await t.expect(Selector(".notification-content").innerText).eql("Service was successfully removed.")
}
export async function createPhoneNumber(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/telecom_providers/phone_numbers`)
  await t.click('[data-tc-add-phone-number]')
  await t.typeText('[data-tc-phone-number] #VuePhoneNumberInput_phone_number', telecom.phoneNumberParams.phoneNumber)
  await t.click('[data-tc-multi-select-vendor]')
  await t.typeText(telecomVendor, telecom.phoneNumberParams.vendorName)
  await t.click(Selector('[data-tc-multi-select-vendor] .multiselect__option span').withText(telecom.phoneNumberParams.vendorName))
  await t.click(companylocation)
  await t.click(locationCategory.withText(location.location.name))
  await t.click('[data-tc-friendly-name]')
  await t.typeText('[data-tc-friendly-name]', telecom.phoneNumberParams.friendlyName)
  await t.typeText("#notes", telecom.telecomParams.notes)
  await t.click('[data-tc-save-form-btn="Save Ip Address"]')
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}
export async function phoneNumberCheck(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/telecom_providers/phone_numbers`)
  await t.click('[data-tc-add-phone-number]')
  await t.click('[data-tc-save-form-btn="Save Ip Address"]')
  await t.expect(Selector('.notification-title').innerText).eql('Error')
}
export async function updatePhoneNumber(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/telecom_providers/phone_numbers`)
  await t.hover('[data-tc-edit-notes]')
  await t.click('[data-tc-edit-number]')
  await t.click('[data-tc-add-phone-number]')
  await t.selectText('[data-tc-phone-number] #VuePhoneNumberInput_phone_number').pressKey("delete");
  await t.typeText('[data-tc-phone-number] #VuePhoneNumberInput_phone_number', telecom.phoneNumberParams.phoneNumber2)
  await t.click('[data-tc-multi-select-vendor]')
  await t.typeText(telecomVendor, telecom.phoneNumberParams.vendorName2)
  await t.click(Selector('[data-tc-multi-select-vendor] .multiselect__option span').withText(telecom.phoneNumberParams.vendorName2))
  await t.selectText('[data-tc-friendly-name]')
  await t.typeText('[data-tc-friendly-name]', telecom.phoneNumberParams.friendlyName2)
  await t.selectText("#notes").pressKey("delete");
  await t.typeText("#notes", telecom.telecomParams.notes2)
  await t.click('[data-tc-save-form-btn="Save Ip Address"]')
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}
export async function deletePhoneNumber(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/telecom_providers/phone_numbers`)
  await t.hover('[data-tc-edit-notes]')
  await t.click('[data-tc-delete-number]')
  await t.click('[data-tc-cancel]')
  await t.hover('[data-tc-edit-notes]')
  await t.click('[data-tc-delete-number]')
  await t.click('[data-tc-delete-num]')
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}
