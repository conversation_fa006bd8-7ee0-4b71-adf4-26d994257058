import common from '../../common/helpers/common.js'

var telecomParams =
{
  contractType      : "Abcd",
  accountNumber     : common.faker.finance.account(),
  priorityType      : "Primary",
  serviceType       : "voice",
  vendorName        : common.faker.lorem.words(),
  name              : common.faker.lorem.words(),
  location          : "UL",
  cost              : common.faker.random.number() ,
  notes             : common.faker.lorem.paragraph(),
  notes2             : common.faker.lorem.paragraph(),
  accountNumber     : common.faker.random.number() ,

}
var updateTelecomParams =
{
  contractType      : "Abcd",
  accountNumber     : common.faker.finance.account(),
  priorityType      : "Primary",
  serviceType       : "voice",
  vendorName        : common.faker.lorem.words(),
  name              : "telecom1",
  location          : "HQ",
  cost              : common.faker.random.number() ,
  notes             : common.faker.lorem.paragraph(),
  accountNumber     : common.faker.random.number() ,

}
var phoneNumberParams = 
{
  phoneNumber       : "**********",
  phoneNumber2       : "**********",
  friendlyName      : "Tele",
  friendlyName2      : "TelePhone",
  vendorName        : "123Net",
  vendorName2        : "8x8",
}

export default {
  telecomParams,
  updateTelecomParams,
  phoneNumberParams
}
