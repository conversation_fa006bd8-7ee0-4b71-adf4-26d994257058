import { telecomFormValidation , createTelecom , archiveTelecom , unArchiveTelecom , deleteTelecom, createPhoneNumber, phoneNumberCheck, updatePhoneNumber, deletePhoneNumber } from './methods/crud.js'
import { createVendor } from '../04_vendors/methods/crud.js'
import { createLocation, deleteLocation } from '../14_locations/methods/locations.js'


fixture('Usecase: Telecom Service')


test('Create Location', async t =>{
  await createLocation(t);
})
test('Phone Number Validation', async t =>{
  await phoneNumberCheck(t);
})
test('Create Phone Number', async t => {
  await createPhoneNumber(t);
})
test('Update Phone Number', async t => {
  await updatePhoneNumber(t);
})
test('Delete Phone Number', async t => {
  await deletePhoneNumber(t);
})
test('Delete Location', async t => {
  await deleteLocation(t);
})

