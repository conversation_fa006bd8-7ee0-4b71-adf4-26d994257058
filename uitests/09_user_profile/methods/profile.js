import common from '../../common/helpers/common.js'
import { Selector, Role, ClientFunction } from 'testcafe'
import faker from 'faker'

export async function visitProfile(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/profile`)
  await t.expect(Selector('[data-tc-btn-profile]').innerText).eql("Profile")
  await t.expect(Selector('[data-tc-btn-referrals]').innerText).eql("Referrals")
  await t.expect(Selector('[data-tc-information]').exists).ok()

  await t.expect(Selector('[data-tc="department"]').innerText).eql("Department")
  await t.expect(Selector('[data-tc="location"]').innerText).eql("Location")
  await t.expect(Selector('[data-tc="mobile_phone"]').innerText).eql("Mobile Phone")
  await t.expect(Selector('[data-tc="work_phone"]').innerText).eql("Work Phone")
  await t.expect(Selector('[data-tc="supervisor"]').innerText).eql("Supervisor")

  if (await Selector('[data-tc-basic-information]').visible) {
    await t.expect(Selector('[data-tc-basic-information]').innerText).eql('Basic Information')
    await t.expect(Selector('[data-tc-label-locked]').innerText).eql('User Status')
    await t.expect(Selector('[data-tc-value-locked]').innerText).eql('Unlocked')
    await t.expect(Selector('[data-tc-label-sso-login]').innerText).eql('SSO Login')
    await t.expect(Selector('[data-tc-value-sso-login]').innerText).eql('False')
  } else {
    await t.expect(Selector('[data-tc-assignments]').innerText).eql('Assignment Information')
  }

  await t
    .hover('[data-tc-btn-referrals]')
    .click('[data-tc-btn-referrals]')
  await t
    .hover('.multiselect__tags')
    .click('.multiselect__tags')
    .typeText('[data-tc-multi-select-email]', (`${faker.lorem.word()}@${faker.lorem.word()}.com`)
    .toLowerCase())
    .pressKey("enter")
  await t
    .hover('.multiselect__tags')
    .click('.multiselect__tags')
    .typeText('[data-tc-multi-select-email]', (`${faker.lorem.word()}@${faker.lorem.word()}.com`)
    .toLowerCase())
    .pressKey("enter")
  await t
    .hover('[data-tc-btn-send-invites]')
    .click('[data-tc-btn-send-invites]')
  // await t.expect(Selector('.notification-content').innerText).eql('Successfully sent 2 referral email(s)')

  await t.expect(Selector('[data-tc-btn-copy]').innerText).eql('Copy')
  await t.click('[data-tc-btn-copy]')
  await t.expect(Selector('[data-tc-btn-copy]').innerText).eql('Copied...')
}

export async function editProfile(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/profile`)
  await t.click('a.edit-delete-btn.edit.mr-4.text-secondary')

  await t.selectText(Selector('[data-tc="first_name"] div input')).pressKey("delete")
  await t.typeText(Selector('[data-tc="first_name"] div input'), common.user.firstName)
  await t.selectText(Selector('[data-tc="last_name"] div input')).pressKey("delete")
  await t.typeText(Selector('[data-tc="last_name"] div input'), common.user.lastName)

  await t.selectText(Selector('[data-tc="title"] div input')).pressKey("delete")
  await t.typeText(Selector('[data-tc="title"] div input'), 'title')
  await t.selectText(Selector('[data-tc="department"] div input')).pressKey("delete")
  await t.typeText(Selector('[data-tc="department"] div input'), 'department')

  const title = await Selector('[data-tc="title"] div input').value
  const department = await Selector('[data-tc="department"] div input').value

  await t.click(Selector('[data-tc-create="staff member"]'))

  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/profile`)
  await t.expect(Selector('[data-text-tc="title"]').innerText).eql(title)
  await t.expect(Selector('[data-text-tc="department"]').innerText).eql(department)
}