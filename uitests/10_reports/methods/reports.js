import common from '../../common/helpers/common.js'
import { Selector } from 'testcafe'

export async function downloadHelpdeskCsvReports(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/reports`)
  await t.click(Selector("[data-tc-checkmark]").withText("Help Desk"))
  await t.click('[data-tc-continue]')
  await t.click('[data-tc-column-select="Help Ticket Number"')
  await t.click('[data-tc-column-select="Comment Count"')
  await t.click('[data-tc-move-to-selected-btn]')
  await t.click('[data-tc-continue]')
  await t.click('[data-tc-continue]')
}

export async function downloadHelpdeskExcelReports(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/reports`)
  await t.click(Selector("[data-tc-checkmark]").withText("Help Desk"))
  await t.click('[data-tc-continue]')
  await t.click('[data-tc-column-select="Help Ticket Number"')
  await t.click('[data-tc-column-select="Comment Count"')
  await t.click('[data-tc-move-to-selected-btn]')
  await t.click('[data-tc-continue]')
  await t.click('[data-tc-continue]')
  await t.wait(4000)
  await t.click('[data-tc-excel]')
}

export async function downloadHelpdeskpdfReports(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/reports`)
  await t.click(Selector("[data-tc-checkmark]").withText("Help Desk"))
  await t.click('[data-tc-continue]')
  await t.click('[data-tc-column-select="Help Ticket Number"')
  await t.click('[data-tc-column-select="Comment Count"')
  await t.click('[data-tc-move-to-selected-btn]')
  await t.click('[data-tc-continue]')
  await t.click('[data-tc-continue]')
  await t.click('[data-tc-pdf]')
}

export async function downloadHelpdeskJsonAndHtmlReports(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/reports`)
  await t.click(Selector("[data-tc-checkmark]").withText("Help Desk"))
  await t.click('[data-tc-continue]')
  await t.click('[data-tc-column-select="Help Ticket Number"')
  await t.click('[data-tc-column-select="Comment Count"')
  await t.click('[data-tc-move-to-selected-btn]')
  await t.click('[data-tc-continue]')
  await t.click('[data-tc-continue]')
  await t.click('[data-tc-json]')
  await t.click('[data-tc-html]')
}

export async function downloadContractsCsvAndExcelReports(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/reports`)
  await t.click(Selector("[data-tc-checkmark]").withText("Contracts"))
  await t.click(Selector("[data-tc-select-format]").withText("Tabular"))
  await t.click('[data-tc-continue]')
  await t.click('[data-tc-continue]')
  await t.click('[data-tc-continue]')
  await t.click('[data-tc-csv]')
  await t.click('[data-tc-excel]')
}

export async function downloadContractspdfReports(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/reports`)
  await t.click(Selector("[data-tc-checkmark]").withText("Contracts"))
  await t.click(Selector("[data-tc-select-format]").withText("Tabular"))
  await t.click('[data-tc-continue]')
  await t.click('[data-tc-continue]')
  await t.click('[data-tc-continue]')
  await t.click('[data-tc-pdf]')
}

export async function downloadContractsJsonAndHtmlReports(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/reports`)
  await t.click(Selector("[data-tc-checkmark]").withText("Contracts"))
  await t.click(Selector("[data-tc-select-format]").withText("Tabular"))
  await t.click('[data-tc-continue]')
  await t.click('[data-tc-continue]')
  await t.click('[data-tc-continue]')
  await t.click('[data-tc-json]')
  await t.click('[data-tc-html]')
}
