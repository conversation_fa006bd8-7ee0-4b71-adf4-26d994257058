import { downloadHelpdeskCsvReports, downloadHelpdeskExcelReports, downloadHelpdeskpdfReports, downloadHelpdeskJsonAndHtmlReports, downloadContractsCsvAndExcelReports, downloadContractspdfReports, downloadContractsJsonAndHtmlReports } from './methods/reports.js'

fixture('Usecase: Reports')

test('Download helpdesk csv reports', async t => {
  await downloadHelpdeskCsvReports(t);
})

test('Download helpdesk excel reports', async t => {
  await downloadHelpdeskExcelReports(t);
})

test('Download helpdesk pdf report', async t => {
  await downloadHelpdeskpdfReports(t);
})

test('Download helpdesk json and html reports', async t => {
  await downloadHelpdeskJsonAndHtmlReports(t);
})

test('Download contract csv and excel reports', async t => {
  await downloadContractsCsvAndExcelReports(t);
})

test('Download contract pdf report', async t => {
  await downloadContractspdfReports(t);
})

test('Download contract json and html reports', async t => {
  await downloadContractsJsonAndHtmlReports(t);
})
