import { createStaff, editStaff, searchStaff, archiveStaffFromIndexPage, archiveStaffFromShowPage,readStaff } from './methods/crud.js';
import { createLocation, editLocation, deleteLocation } from '../14_locations/methods/locations.js';

fixture('Usecase: Staff')

test('Create new Staff', async t => {
  await createStaff(t);
})
test('Read Staff', async t => {
  await readStaff(t);
})
 test('Edit Staff', async t => {
   await editStaff(t);
})
 test('Search Staff', async t => {
  await searchStaff(t);
})
test('Delete Staff from Index Page', async t => {
  await archiveStaffFromIndexPage(t);
})
// test('Delete Staff from Show Page', async t => {
//   await archiveStaffFromShowPage(t);
// })