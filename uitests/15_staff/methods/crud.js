import common from '../../common/helpers/common.js';
import { Selector } from 'testcafe';
import staffInfo from '../helpers/staff_variables.js';

const companyUser = Selector('[data-tc-user-full-name]')
const fname = staffInfo.staff.fname
const lname = staffInfo.staff.lname
const fullName = `${fname} ${lname}`
const department = Selector('[data-text-tc="department"]')
const title = Selector('[data-text-tc="title"]')
const email = Selector('[data-tc-email]')

async function visitStaff(t) {
  await t.click(Selector('[data-tc-staff]'))
  await t.click(Selector('[data-tc-your-staff]'))
}

export async function createStaff(t) {
  await t.useRole(common.userLogin)
  await t.wait(1000)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/company/users`)
  await t.wait(1000)
  await t
    .hover('[data-tc-add-staff-btn]')
    .click('[data-tc-add-staff-btn]')
  await t.typeText(Selector('[data-tc="first_name"]'), staffInfo.staff.fname)
  await t.typeText(Selector('[data-tc="last_name"]'), staffInfo.staff.lname)
  await t.typeText(Selector('[data-tc="email"]'), staffInfo.staff.email)
  await t.typeText(Selector('[data-tc="title"]'), staffInfo.staff.title)
  await t.typeText(Selector('[data-tc="department"]'), staffInfo.staff.dept)
  await t
    .hover(Selector('[data-tc="supervisor"] .multiselect'))
    .click(Selector('[data-tc="supervisor"] .multiselect'))
    .pressKey('enter')
  await t.click('[data-tc-attachment-image]')
  await t
    .setFilesToUpload('.drop__file-input', '../../common/files/image2.png')
  await t.click(Selector('[data-tc-create="staff member"]'))
}

export async function readStaff(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/company/overview`)
  await visitStaff(t)
  await t.wait(1000)
  await t
    .hover(companyUser.withText(fullName))
    .click(companyUser.withText(fullName))
  await t.expect(companyUser.withText(fullName).exists).ok()
  await t.expect(email.innerText).eql(staffInfo.staff.email.toLowerCase())
  await t.expect(department.withText(staffInfo.staff.dept).exists).ok()
  await t.expect(title.withText(staffInfo.staff.title).exists).ok()
}

export async function editStaff(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/company/overview`)
  await visitStaff(t)
  await t.wait(1000)
  await t
    .hover(companyUser.withText(fullName))
    .click(companyUser.withText(fullName))
  await t
    .hover('[data-tc-edit-usr-btn]')
    .click('[data-tc-edit-usr-btn]')
  await t.click('[data-tc-back]')
  await t
    .hover(companyUser.withText(fullName))
    .click(companyUser.withText(fullName))
  await t
    .hover('[data-tc-edit-usr-btn]')
    .click('[data-tc-edit-usr-btn]')
  await t.selectText(Selector('[data-tc="first_name"] div input'))
  await t.typeText(Selector('[data-tc="first_name"] div input'), staffInfo.staff.fname)
  await t.selectText(Selector('[data-tc="last_name"] div input'))
  await t.typeText(Selector('[data-tc="last_name"] div input'), staffInfo.staff.lname)
  await t.selectText(Selector('[data-tc="title"] div input'))
  await t.typeText(Selector('[data-tc="title"] div input'), staffInfo.staff.title)
  await t.selectText(Selector('[data-tc="department"] div input'))
  await t.typeText(Selector('[data-tc="department"] div input'), staffInfo.staff.dept)
  await t
    .hover(Selector('[data-tc="supervisor"] .remove-link'))
    .click(Selector('[data-tc="supervisor"] .remove-link'))
  await t
    .hover(Selector('[data-tc="supervisor"] .multiselect'))
    .click(Selector('[data-tc="supervisor"] .multiselect'))
    .pressKey('enter')
  await t.wait(2000)
  await t
    .hover('[data-tc-remove]')
    .click('[data-tc-remove]')
  await t
    .hover('[data-tc-attachment-image]')
    .click('[data-tc-attachment-image]')
  await t
    .setFilesToUpload('.drop__file-input', '../../common/files/Avatar.png')
  await t.click(Selector('[data-tc-create="staff member"]'))
  await t.expect(Selector(".notification-title").innerText).eql("Success")
}

export async function searchStaff(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/company/overview`)
  await visitStaff(t)
  await t.wait(1000)
  await t.click('[data-tc-company-user-search]')
  await t.typeText('[data-tc-company-user-search]', fname)
  await t.expect(Selector(companyUser.withText(fname)).exists).ok()
  await t.selectText('[data-tc-company-user-search]')
  await t.typeText('[data-tc-company-user-search]', lname)
  await t.expect(Selector(companyUser.withText(lname)).exists).ok()
  await t.selectText('[data-tc-company-user-search]')
  await t.typeText('[data-tc-company-user-search]', staffInfo.staff.word)
  await t.expect(Selector('div').child('h4').withText("No users found, try changing filtering criteria.").exists).ok()
}

export async function archiveStaffFromIndexPage(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/company/overview`)
  await visitStaff(t)
  await t.wait(1000)
  await t
    .hover(`[data-tc-archive-user="${fullName}"]`)
    .wait(1500)
    .click(`[data-tc-archive-user="${fullName}"]`)
  await t
    .hover(`[data-tc-cancel-archive="${fullName}"]`)
    .click(`[data-tc-cancel-archive="${fullName}"]`)
  await t
    .hover(`[data-tc-archive-user="${fullName}"]`)
    .wait(1500)
    .click(`[data-tc-archive-user="${fullName}"]`)
  await t
    .hover(`[data-tc-archive-user-modal-btn="${fullName}"]`)
    .click(`[data-tc-archive-user-modal-btn="${fullName}"]`)
  await t.expect(Selector(".notification-title").innerText).eql("Success")
}
