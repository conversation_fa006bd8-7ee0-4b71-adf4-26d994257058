import common from '../helpers/common.js'
import { Role, Selector, ClientFunction } from 'testcafe'

export async function deleteCompany(t)
{
  const field = Selector('div#search_status_sidebar_section');
  await t.navigateTo(`${common.testEnv.domain}/users/sign_in`)
  if (await Selector('.visible-lg form input[name="username"]').visible) {
    await t.typeText('.visible-lg form input[name="username"]', common.admin.email)
    await t.typeText('.visible-lg form input[name="password"]', common.admin.password)
    await t.click('.visible-lg form input[name="signInSubmitButton"]')
  } else {
    await t.typeText('.visible-sm form input[name="username"]', common.admin.email)
    await t.typeText('.visible-sm form input[name="password"]', common.admin.password)
    await t.click('.visible-sm form input[name="signInSubmitButton"]')
  }
  await t.expect(common.getPageUrl()).contains('admin')
  await t.typeText('#q_name_or_subdomain_contains', common.user.company.name)
  await t.click('.filter_form input[type="submit"]')
  await t.click("#index_table_companies tbody tr:nth-child(1) a.delete_link")
  await t.typeText("#dialog_confirm input[name='company name']", common.user.company.name)
  await t.click(Selector('button.ui-button.ui-corner-all.ui-widget').withText('OK'));
  // Because of the menuing/size of the window, this might not appear.
  // await t.click("#logout")
  await t.navigateTo("/users/sign_out");
}
