import common from '../helpers/common.js'
import { deleteCognitoUser } from './delete_cognito_user.js'
import { Role, Selector, t } from 'testcafe'

export async function signUpCompany(t)
{
  deleteCognitoUser(t, common.user.email)
  await t.navigateTo(`${common.testEnv.domain}/users/sign_up`)
  await t.expect(common.getPageUrl()).contains('/about_you')
  await t.typeText('form input[data-tc-first-name]', common.user.firstName)
  await t.typeText('form input[data-tc-last-name]', common.user.lastName)
  await t.typeText('form input[data-tc-email]', common.user.email, { paste: true })
  await t.typeText('form input[data-tc-password]', common.user.password)
  await t.click('form button[data-tc-save-form-btn="Finish"]')
  await t.expect(common.getPageUrl()).contains('/company_info')
  await t.typeText('form input[data-tc-company-name]', common.user.company.name)
  await t.typeText('form input[data-tc-subdomain]', common.user.company.subdomain)
  await t.typeText('form input[data-tc-subdomain]', common.user.company.subdomain)
  await t.selectText('form input[data-tc-subdomain]').pressKey("delete");
  await t.typeText('form input[data-tc-subdomain]', common.user.company.subdomain)
  await t.click('form button[data-tc-save-form-btn="Finish"]')
  await t.expect(common.getPageUrl()).contains('/welcome')
  await t.wait(30000) // Wait for the 'prepping your account' screen
  await t.expect(common.getPageUrl()).contains('dashboard')
  if (await Selector('[data-tc-info-modal-next]').exists) {
    await t.click('[data-tc-info-modal-next]')
    await t.click('[data-tc-info-modal-gotIt1]')
    await t.click('[data-tc-info-modal-gotIt2]')
  }
  await t.click('[data-tc-main-menu-toggle]')
  await t.click('.nulodgicon-log-out')
}
