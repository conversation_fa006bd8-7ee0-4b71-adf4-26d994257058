import dotenv from 'dotenv'
require('dotenv').config({path:'../../config/settings/env.development'})
import { ClientFunction } from 'testcafe'
import faker from 'faker'
import { Role, Selector } from 'testcafe'

const getPageUrl = ClientFunction(() => {
  return window.location.href.toString();
})

// load env variables
dotenv.config()

var testEnv =
{
  domain : process.env.DOMAIN_WITH_PORT
}

var company =
{
  name : faker.company.companyName(),
  subdomain : faker.internet.domainName(),
  phone : '2015550123',
  address : 'green bay',
  city : 'LA',
  zip : '12345'
}

var company2 =
{
  name : faker.company.companyName(),
  subdomain : faker.internet.domainName(),
}

var firstName = faker.name.firstName();
var lastName = faker.name.lastName();

var user =
{
  name : `${firstName} ${lastName}`,
  firstName: firstName,
  lastName: lastName,
  email : (`${faker.lorem.word()}@company.com`).toLowerCase(),
  password : faker.internet.password(),
  code : null,
  token : null,
  company :
  {
    name : faker.company.companyName(),
    subdomain : faker.name.firstName(),
    phone : '2015550123',
    address : 'green bay',
    city : 'LA',
    zip : '12345'
  }
}

var registeredUser =
{
  email : process.env.TEST_USER_EMAIL,
  password : process.env.TEST_USER_PASSWORD,
  code : null,
  token : null,
  company :
  {
    name : process.env.TEST_USER_COMPANY,
    subdomain : process.env.TEST_USER_COMPANY_DOMAIN,
    phone : '2015550123',
    address : 'green bay',
    city : 'LA',
    zip : '12345'
  }
}

var admin =
{
  email : process.env.ADMIN_EMAIL,
  password : process.env.ADMIN_PASSWORD
}

const getElementWithDataId = Selector ((dataId, options) => {
  if (options === undefined) {
    return document.querySelector ('[data-id='+dataId+']')
  } else {
    return document.querySelectorAll ('[data-id='+dataId+'] '+options)
  }
});

const userLogin = Role(`${testEnv.domain}/users/sign_in`, async t =>
{
  if (await Selector('.visible-lg form input[name="username"]').visible) {
    await t.typeText('.visible-lg form input[name="username"]', user.email)
    await t.typeText('.visible-lg form input[name="password"]', user.password)
    await t.click('.visible-lg form input[name="signInSubmitButton"]')
  } else {
    await t.typeText('.visible-sm form input[name="username"]', user.email)
    await t.typeText('.visible-sm form input[name="password"]', user.password)
    await t.click('.visible-sm form input[name="signInSubmitButton"]')
  }
  await t.expect(getPageUrl()).contains('dashboard')
  await t.navigateTo(`http://${user.company.subdomain}.lvh.me:3000/module_walkthrough_bypass`)
  await t.expect(getPageUrl()).contains('dashboard')
});

export default {
  faker,
  testEnv,
  getPageUrl,
  user,
  admin,
  company,
  company2,
  registeredUser,
  userLogin,
  getElementWithDataId
}
