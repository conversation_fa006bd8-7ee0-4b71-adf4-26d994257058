import { createResponses, updateResponses, deleteResponses } from './methods/canned_responses.js'
import { createArticle, editArticle, deleteArticle } from './methods/articles'
import { createWorkSpace, searchWorkSpace, switchNewWorkapaceAddTicket, setNewWorkspacefilter, updateWorkSpace, deleteWorkSpace } from './methods/workspaces.js'

fixture('Usecase: Helpdesk')

test('Add Responses', async t => {
  await createResponses(t);
})

test('Edit Responses', async t => {
  await updateResponses(t);
})
test('Delete Responses', async t => {
  await deleteResponses(t);
})

test('Add Articles', async t => {
  await createArticle(t);
})

test('Edit Article', async t => {
  await editArticle(t);
})
test('Delete Article', async t => {
  await deleteArticle(t);
})

test('Create Workspace', async t => {
  await createWorkSpace(t);
})

test('Search Workspace', async t => {
  await searchWorkSpace(t);
})

test('Switch Workspace and add ticket', async t => {
  await switchNewWorkapaceAddTicket(t);
})

test('Check new ticket in workspace', async t => {
  await setNewWorkspacefilter(t);
})

test('Update Workspace', async t => {
  await updateWorkSpace(t);
})

test('Delete Workspace', async t => {
  await deleteWorkSpace(t);
})
