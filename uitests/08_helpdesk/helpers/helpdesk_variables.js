import common from '../../common/helpers/common.js'

var helpTicketParams =
{
  subject       : 'Help Ticket',
  description   : common.faker.lorem.sentence(),
  comment       : common.faker.lorem.sentence(),
}

var workSpaceparams =
{
  name          : 'New Workspace',
  updatedName   : 'Workspace name update',
}

var helpTicket2Params =
{
  subject       : common.faker.lorem.words(),
  description   : common.faker.lorem.sentence(),
  comment       : common.faker.lorem.sentence(),
  number        : common.faker.lorem.words(),
  textField     : common.faker.lorem.words(),
  textArea      : common.faker.lorem.words(),
  listValue     : 'One',
  phone         : '2015550123',
}

var customAttributeName = {
  number : common.faker.random.alphaNumeric(20),
  number2 : common.faker.random.alphaNumeric(15),
  textField : common.faker.lorem.words(5).substring(0, 15),
  textArea : common.faker.lorem.words(5).substring(0, 15),
  list : common.faker.lorem.words(5).substring(0, 15),
  phone : common.faker.lorem.words(5).substring(0, 15),
  date : common.faker.lorem.words(5).substring(0, 15),
}

var updateHelpTicketParams =
{
  subject       : common.faker.lorem.words(),
  description   : common.faker.lorem.sentence(),
  comment   : common.faker.lorem.sentence(),
}

var documentParams =
{
  name: common.faker.lorem.words(),
}

var editDocumentParams =
{
  name: common.faker.lorem.words(),
}

var timeSpentParams =
{
  timeSpent: "4h 20m"
}

var response =
{
  title       : common.faker.lorem.words(),
  textReplace : common.faker.lorem.words(),
}
const article =
{
  title    : common.faker.lorem.sentence(),
  body      : common.faker.lorem.sentence(),
}
var document =
{
  name        : common.faker.lorem.word(),
}
export default {
  helpTicketParams,
  updateHelpTicketParams,
  customAttributeName,
  helpTicket2Params,
  documentParams,
  editDocumentParams,
  timeSpentParams,
  response,
  article,
  document,
  workSpaceparams,
}
