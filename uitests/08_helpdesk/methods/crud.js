import common from '../../common/helpers/common.js'
import { Selector, Role, ClientFunction } from 'testcafe'
import helpdesk from '../helpers/helpdesk_variables.js'

var openSelectorBefore;
var openSelectorAfter;
var closedSelectorAfter;
var closedSelectorBefore;

export async function createHelpTicket(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/dashboard`)
  await t.wait(6000)
  await t.click(Selector('[data-tc-tickets]'))
  await t.click(Selector('[data-tc-add-module-dropdown]'))
  await t.click(Selector('[data-tc-base-ticket]'))
  await t.expect(common.getPageUrl()).contains('new');
  await t.typeText(Selector('[data-tc-text]'), helpdesk.helpTicketParams.subject)
  await t
    .click(Selector('[data-tc="assigned_to"]'))
    .click(Selector('[data-tc="assigned_to"] .item-name').withText(common.user.name))
  await t.typeText('trix-editor',`${ helpdesk.helpTicketParams.description }`)
  await t.setFilesToUpload( '.drop__file-input', '../../common/files/doc.odt' )
  await t.expect(Selector("[data-tc='created_by'] div[id='Created By'] .nulodgicon-trash-b").exists).ok()
  await t.expect(Selector('[data-tc-text]').value).eql(helpdesk.helpTicketParams.subject)
  await t.click(Selector('[data-tc-create-ticket]'))
  await t.expect(common.getPageUrl()).contains('general');
  await t.expect(Selector('[data-tc-ticket-subject]').innerText).eql(helpdesk.helpTicketParams.subject)
}

export async function updateHelpTicketStatus(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/dashboard`)
  var openBefore = (await Selector('[data-tc-open-tickets]'))
  var closedBefore = (await Selector('[data-closed-tickets]'))
  await t.click(Selector('[data-tc-tickets]'))
  await t.typeText(Selector('[data-tc-ticket-search]'), helpdesk.helpTicketParams.subject)
  await t.click(Selector('[data-tc-ticket-subject]').withText(helpdesk.helpTicketParams.subject))
  await t.click(Selector('[data-tc-ticket-status-dropdown]'))
  await t.click(Selector('[data-tc-status-options] a.dropdown-item').withText('In Progress'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
  await t.click(Selector('[data-tc-ticket-status-dropdown]'))
  await t.click(Selector('[data-tc-status-options] a.dropdown-item').withText('Closed'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/dashboard`)
  var openAfter = (await Selector('[data-tc-open-tickets]'))
  var closedAfter = (await Selector('[data-closed-tickets]'))
  await t.expect((parseInt(openBefore.innerText) - 1)).eql(parseInt(openAfter.innerText))
  await t.expect((parseInt(closedBefore.innerText) + 1)).eql(parseInt(closedAfter.innerText))
  await t.click(Selector('[data-tc-tickets]'))
  await t.click(Selector('[data-tc-filter-menu-button]'))
  await t.click(Selector('[data-tc-status-filter]'))
  await t.click(Selector('[data-tc-status-filter] a.dropdown-item').withText("Closed"))
  await t.click(Selector('[data-tc-ticket-status-dropdown]'))
  await t.click(Selector('[data-tc-status-options] a.dropdown-item').withText('In Progress'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}

export async function updateHelpTicketPriority(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/dashboard`)
  var openBefore = (await Selector('[data-tc-open-tickets]'))
  var closedBefore = (await Selector('[data-closed-tickets]'))
  await t.click(Selector('[data-tc-tickets]'))
  await t.typeText(Selector('[data-tc-ticket-search]'), helpdesk.helpTicketParams.subject)
  await t.click(Selector('[data-tc-ticket-subject]').withText(helpdesk.helpTicketParams.subject))
  await t.click(Selector('[data-tc-ticket-priority-dropdown]'))
  await t.click(Selector('[data-tc-priority-options] a.dropdown-item').withText('Medium'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
  await t.click(Selector('[data-tc-ticket-priority-dropdown]'))
  await t.click(Selector('[data-tc-priority-options] a.dropdown-item').withText('High'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/dashboard`)
  var openAfter = (await Selector('[data-tc-open-tickets]'))
  var closedAfter = (await Selector('[data-closed-tickets]'))
  await t.expect((parseInt(openBefore.innerText) - 1)).eql(parseInt(openAfter.innerText))
  await t.expect((parseInt(closedBefore.innerText) + 1)).eql(parseInt(closedAfter.innerText))
  await t.click(Selector('[data-tc-tickets]'))
  await t.click(Selector('[data-tc-filter-menu-button]'))
  await t.click(Selector('[data-tc-priority-filter]'))
  await t.click(Selector('[data-tc-priority-filter] a.dropdown-item').withText("High"))
  await t.click(Selector('[data-tc-ticket-priority-dropdown]'))
  await t.click(Selector('[data-tc-priority-options] a.dropdown-item').withText('Low'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}

export async function commentHelpTicket(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/dashboard`)
  await t.click(Selector('[data-tc-tickets]'))
  await t.typeText(Selector('[data-tc-ticket-search]'), helpdesk.helpTicketParams.subject)
  await t.click(Selector('[data-tc-ticket-subject]').withText(helpdesk.helpTicketParams.subject))
  await t.click(Selector('[data-tc-add-new-comment]'))
  await t.typeText('trix-editor', helpdesk.helpTicketParams.comment)
  await t.click(Selector('[data-tc-add-comment]'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
  await t.click(Selector('[data-tc-history]'))
  var paramsComment = helpdesk.helpTicketParams.comment.replace(/\s+/g, "")
  var activityComment = (await Selector('[data-tc-comment-activity]').innerText).replace(/\s+/g, "")
  await t.expect(activityComment).contains(paramsComment)
}

export async function addTask(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/dashboard`)
  await t.click(Selector('[data-tc-tickets]'))
  await t.typeText(Selector('[data-tc-ticket-search]'), helpdesk.helpTicketParams.subject)
  await t.click(Selector('[data-tc-ticket-subject]').withText(helpdesk.helpTicketParams.subject))
  await t.click(Selector('[data-tc-tasks]'))
  await t
    .hover(Selector('[data-tc-add-task]'))
    .click(Selector('[data-tc-add-task]'))
  await t
    .hover(Selector('[data-tc-task-description-input]'))
    .typeText(Selector('[data-tc-task-description-input]'), helpdesk.helpTicketParams.comment)
  await t.click(Selector('div[data-tc-task-due-at]'))
  await t.click(Selector('.cell.cur-month.today'))
  await t.click(Selector('[data-tc-save-form-btn="Save task"]'))
  var descriptionText = (await Selector('[data-tc-task-description]').innerText)
  await t.expect(descriptionText).contains(helpdesk.helpTicketParams.comment)
}

export async function addTimeSpent(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/dashboard`)
  await t.click(Selector('[data-tc-tickets]'))
  await t.typeText(Selector('[data-tc-ticket-search]'), helpdesk.helpTicketParams.subject)
  await t.click(Selector('[data-tc-ticket-subject]').withText(helpdesk.helpTicketParams.subject))
  await t.click(Selector('[data-tc-time-spent]'))
  await t.selectText(Selector('[data-tc-time-spent-entry]'))
    .pressKey('delete');
  await t.typeText(Selector('[data-tc-time-spent-entry]'), '2h')
  await t.click(Selector('[data-tc-add-comment]'))
  await t
    .selectText(Selector('[data-tc-time-spent-entry]'))
    .pressKey('delete')
  await t.typeText(Selector('[data-tc-time-spent-entry]'), helpdesk.timeSpentParams.timeSpent)
  await t.click(Selector('div[data-tc-time-spent-date]'))
  await t.click(Selector('.cell.cur-month.today'))
  await t.click(Selector('.mx-datepicker-btn-confirm'))
  await t.typeText('trix-editor', helpdesk.helpTicketParams.comment)
  await t
    .hover(Selector('[data-tc-add-comment]'))
    .click(Selector('[data-tc-add-comment]'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
  await t.click(Selector('[data-tc-history]'))
  var paramsTimeSpent = helpdesk.timeSpentParams.timeSpent.replace(/\s+/g, "")
  var activityComment = (await Selector('[data-tc-comment-activity]').innerText).replace(/\s+/g, "")
  await t.expect(activityComment).contains(paramsTimeSpent)
}

export async function archiveHelpTicket(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/dashboard`)
  await t.click(Selector('[data-tc-tickets]'))
  await t.typeText(Selector('[data-tc-ticket-search]'), helpdesk.helpTicketParams.subject)
  await t.click(Selector('[data-tc-ticket-subject]').withText(helpdesk.helpTicketParams.subject))
  await t
    .hover(Selector('[data-tc-archive]'))
    .click(Selector('[data-tc-archive]'))
  await t.click(Selector('[data-tc-modal-archive-btn]'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}

export async function unArchiveHelpTicket(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/dashboard`)
  await t.click(Selector('[data-tc-tickets]'))
  await t.typeText(Selector('[data-tc-ticket-search]'), helpdesk.helpTicketParams.subject)
  await t.click(Selector('[data-tc-filter-menu-button]'))
  await t.click(Selector('[data-tc-status-filter]'))
  await t.click(Selector('[data-tc-status-filter] a.dropdown-item').withText("Archived"))
  await t.click(Selector('[data-tc-ticket-subject]').withText(helpdesk.helpTicketParams.subject))
  await t
    .hover(Selector('[data-tc-unarchive]'))
    .click(Selector('[data-tc-unarchive]'))
  await t.click(Selector('[data-tc-modal-unarchive-btn]'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}

export async function deleteHelpTicket(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/dashboard`)
  await t.click(Selector('[data-tc-tickets]'))
  await t.typeText(Selector('[data-tc-ticket-search]'), helpdesk.helpTicketParams.subject)
  await t.click(Selector('[data-tc-ticket-subject]').withText(helpdesk.helpTicketParams.subject))
  await t
    .hover(Selector('[data-tc-archive]'))
    .click(Selector('[data-tc-archive]'))
  await t.click(Selector('[data-tc-modal-archive-btn]'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/dashboard`)
  await t.click(Selector('[data-tc-tickets]'))
  await t.typeText(Selector('[data-tc-ticket-search]'), helpdesk.helpTicketParams.subject)
  await t.click(Selector('[data-tc-filter-menu-button]'))
  await t.click(Selector('[data-tc-status-filter]'))
  await t.click(Selector('[data-tc-status-filter] a.dropdown-item').withText("Archived"))
  await t.click(Selector('[data-tc-ticket-subject]').withText(helpdesk.helpTicketParams.subject))
  await t
    .hover(Selector('[data-tc-delete]'))
    .click(Selector('[data-tc-delete]'))
  await t.click(Selector('[data-tc-modal-delete-btn]'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}

export async function attachDocToHelpTicket(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/dashboard`)
  await t.click(Selector('[data-tc-tickets]'))
  await t.typeText(Selector('[data-tc-ticket-search]'), helpdesk.helpTicketParams.subject)
  await t.click(Selector('[data-tc-ticket-subject]').withText(helpdesk.helpTicketParams.subject))
  await t
    .hover(Selector('[data-tc-attachments]'))
    .click(Selector('[data-tc-attachments]'))
  await t
    .hover(Selector('[data-tc-attach-document]'))
    .click(Selector('[data-tc-attach-document]'))
  await t
   .hover(Selector('[data-tc-document-submit]'))
   .click(Selector('[data-tc-document-submit]'))
  await t.setFilesToUpload( '.drop__file-input', '../../common/files/doc2.odt' )
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}

export async function createDocument(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/documents`)
  await t.click(Selector('[data-tc-add-document]'))
  await t.setFilesToUpload( '.drop__file-input', '../../common/files/doc.odt' )
  await t
    .hover(Selector('[data-tc-document-name]'))
    .typeText(Selector('[data-tc-document-name]'), helpdesk.documentParams.name)
  await t.click(Selector('[data-tc-save-form-btn="Save"]'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}

export async function editDocument(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/documents`)
  await t.typeText(Selector('[data-tc-search-document]'), helpdesk.documentParams.name)
  await t.click(Selector('[data-tc-document-name]').withText(helpdesk.documentParams.name))
  await t.click(Selector('[data-tc-document-name]').withText(helpdesk.documentParams.name))
  await t
    .hover(Selector('[data-tc-name]'))
    .typeText(Selector('[data-tc-name]'), helpdesk.editDocumentParams.name)
  await t
    .hover(Selector('[data-tc-save-form-btn="Save document"]'))
    .click(Selector('[data-tc-save-form-btn="Save document"]'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}

export async function deleteDocument(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/documents`)
  await t.typeText(Selector('[data-tc-search-document]'), helpdesk.documentParams.name)
  await t
    .hover(Selector('[data-tc-delete]'))
    .click(Selector('[data-tc-delete]'))
  await t
    .hover(Selector('[data-tc-delete-document-modal-btn]'))
    .doubleClick(Selector('[data-tc-delete-document-modal-btn]'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}
