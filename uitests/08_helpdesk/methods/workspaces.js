import common from '../../common/helpers/common.js'
import { Selector, Role, ClientFunction } from 'testcafe'
import helpdesk from '../helpers/helpdesk_variables.js'

export async function createWorkSpace(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/workspaces`)
  await t.wait(6000)
  await t.click(Selector('[data-tc-add-workspace]'))
  await t.click(Selector('[data-tc-workspace-name]'))
  await t.typeText('input[data-tc-workspace-name]', helpdesk.workSpaceparams.name)
  await t.click(Selector('[data-tc-add-impacted-user]'))
  await t
    .click(Selector('[data-tc-multi-user-field]'))
    .click(Selector('[data-tc-multi-user-field] .multiselect__content .multiselect__element:nth-child(2)'))
  await t
    .click(Selector('[data-tc-select-custom-form]'))
    .click(Selector('[data-tc-select-custom-form] .multiselect__content .multiselect__element:nth-child(2)'))
  await t.click(Selector('[data-tc-save-form-btn="Save Workspace"]'))
  await t.wait(3000)
}

export async function searchWorkSpace(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/workspaces`)
  await t.wait(6000)
  await t.typeText('[data-tc-workspace-search]', helpdesk.workSpaceparams.name)
  await t.expect(Selector('[data-tc-workspace-name]').innerText).eql(helpdesk.workSpaceparams.name)
}

export async function switchNewWorkapaceAddTicket(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/workspaces`)
  await t.wait(3000)
  await t.click(Selector('[data-tc-workspace-drawer-name]'))
  await t.click(Selector('[data-workspace]:nth-child(2)'))
  await t.wait(3000)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/dashboard`)
  await t.click(Selector('[data-tc-tickets]'))
  await t.click(Selector('[data-tc-add="helpdesk"]'))
  await t.expect(common.getPageUrl()).contains('new');
  await t.typeText(Selector('[data-tc-text]'), helpdesk.helpTicketParams.subject)
  await t
    .click(Selector('[data-tc="assigned_to"]'))
    .click(Selector('[data-tc="assigned_to"] .item-name').withText(common.user.name))
  await t.typeText('trix-editor',`${ helpdesk.helpTicketParams.description }`)
  await t.setFilesToUpload( '.drop__file-input', '../../common/files/doc.odt' )
  await t.expect(Selector("[data-tc='created_by'] div[id='Created By'] .nulodgicon-trash-b").exists).ok()
  await t.expect(Selector('[data-tc-text]').value).eql(helpdesk.helpTicketParams.subject)
  await t.click(Selector('[data-tc-create="ticket"]'))
  await t.expect(Selector('[data-tc-ticket-subject]').innerText).eql(helpdesk.helpTicketParams.subject)
}

export async function setNewWorkspacefilter(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/dashboard`)
  await t.click(Selector('[data-tc-workspace-drawer-name]'))
  await t.click(Selector('[data-workspace]:nth-child(2)'))
  await t.click(Selector('[data-tc-tickets]'))
  await t.expect(Selector('[data-tc-ticket-subject]').innerText).eql(helpdesk.helpTicketParams.subject)
}

export async function updateWorkSpace(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/workspaces`)
  await t.wait(6000)
  await t.click(Selector(`[data-tc-workspace='${helpdesk.workSpaceparams.name}']`))
  await t.selectText(Selector('[data-tc-update-name]'))
  await t.typeText('input[data-tc-update-name]',  helpdesk.workSpaceparams.updatedName)
  await t.click(Selector('div.row:nth-child(2) [data-tc-select-impacted-user] .remove-company-user'))
  await t.click(Selector('[data-tc-save-form-btn="Save Workspace"]'))
  await t.wait(3000)
}

export async function deleteWorkSpace(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/workspaces`)
  await t.wait(6000)
  await t.click(Selector(`[data-tc-workspace='${helpdesk.workSpaceparams.updatedName}']`))
  await t.click(Selector('[data-tc-delete-modal]'))
  await t.click(Selector('[data-tc-delete-field]'))
  await t.typeText('input[data-tc-delete-field]', 'DELETE')
  await t.click(Selector('[data-tc-delete-workspace'))
  await t.wait(3000)
}
