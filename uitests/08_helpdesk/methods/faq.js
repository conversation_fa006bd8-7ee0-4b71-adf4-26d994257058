import common from '../../common/helpers/common.js'
import { Selector, Role, ClientFunction } from 'testcafe'
import helpdesk from '../helpers/helpdesk_variables.js'

const selectCategory = Selector('[data-tc-category-id]');
const option = selectCategory.find('option');

export async function createFaq(t) {
	await t.useRole(common.userLogin)
	await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/faqs`)
	await t.click('[data-tc-add-faq]')
	await t.click(selectCategory)
	await t.click(option.withText('Facilities'))
	await t.click('[data-tc-add-category]')
	await t.typeText('input[data-tc-new-category]', helpdesk.faq.category)
	await t
		.hover('[data-tc-save-form-btn="Save Category"]')
		.click('[data-tc-save-form-btn="Save Category"]')
	await t.typeText(Selector('trix-editor').nth(0), `${helpdesk.faq.question}`)
	await t.typeText(Selector('trix-editor').nth(1), `${helpdesk.faq.answer}`)
	await t.click('[data-tc-save-form-btn="Submit"]')
	await t.expect(Selector(".notification-title").innerText).eql("Success")
}

export async function searchFaq(t) {
	await t.useRole(common.userLogin)
	await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/faqs`)
	await t.typeText('input[data-tc-faq-search]',helpdesk.faq.question)
	await t.selectText('[data-tc-faq-search]')
	await t.typeText('input[data-tc-faq-search]',helpdesk.faq.answer)
	await t.selectText('[data-tc-faq-search]')
	await t.typeText('input[data-tc-faq-search]',helpdesk.faq.random)
	await t.expect(Selector('[data-tc-no-faq]').exists).ok()
}
export async function editFaq(t) {
	await t.useRole(common.userLogin)
	await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/faqs`)
	await t
		.hover('[data-tc-edit-faq]')
		.click('[data-tc-edit-faq]')
	await t.click('[data-tc-faq-edit]')
	await t.click('[data-tc-back-faq]')
	await t
		.hover('[data-tc-edit-faq]')
		.click('[data-tc-edit-faq]')
	await t.click('[data-tc-faq-edit]')
	await t.click(selectCategory)
	await t.click(option.withText('HR'))
	await t.click('[data-tc-add-category]')
	await t.typeText('input[data-tc-new-category]', helpdesk.faq.category)
	await t
		.hover('[data-tc-save-form-btn="Save Category"]')
		.click('[data-tc-save-form-btn="Save Category"]')
	await t.typeText(Selector('trix-editor').nth(0), `${helpdesk.faq.question}`)
	await t.typeText(Selector('trix-editor').nth(1), `${helpdesk.faq.answer}`)
	await t.click('[data-tc-save-form-btn="Submit"]')
	await t.expect(Selector(".notification-title").innerText).eql("Success")
}

export async function deleteFaq(t) {
	await t.useRole(common.userLogin)
	await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/faqs`)
	await t
		.hover('[data-tc-edit-faq]')
		.click('[data-tc-delete-faq]')
	await t
		.hover('[data-tc-keepit]')
		.click('[data-tc-keepit]')
	await t
		.hover('[data-tc-edit-faq]')
		.click('[data-tc-delete-faq]')
	await t
		.hover('[data-tc-faq-delete]')
		.click('[data-tc-faq-delete]')
	await t.expect(Selector('[data-tc-faq-search]').exists).ok()
}
