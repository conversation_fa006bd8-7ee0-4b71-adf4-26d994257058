// eslint-disable-next-line import/no-extraneous-dependencies
import { Selector } from 'testcafe'
import common from '../../common/helpers/common'
import helpdesk from '../helpers/helpdesk_variables'

const selectCategory = Selector('[data-tc-article-category-id]');
// eslint-disable-next-line no-unused-vars
const option = selectCategory.find('option');
// eslint-disable-next-line no-unused-vars
const selectOption = Selector('.multiselect__content .multiselect__element span');

export async function createArticle(t) {
	await t.useRole(common.userLogin)
	await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/articles`)
	await t.click('[data-tc-add-article]')
	await t.typeText('input[data-tc-article-title]', helpdesk.article.title)
	await t.click(Selector('[data-tc-article-category-select]'))
  await t
    .click(Selector('[data-tc-article-category]'))
    .click(Selector('[data-tc-article-category] .multiselect__content .multiselect__element:nth-child(3)'))
	await t.typeText('trix-editor', `${helpdesk.article.textReplace}`)
	await t.click('[data-tc-article-save]')
	await t.expect(Selector(".notification-title").innerText).eql("Success")
}

export async function editArticle(t) {
	await t.useRole(common.userLogin)
	await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/articles`)
	await t.click('[data-tc-edit-article]')
	await t.typeText('input[data-tc-article-title]', helpdesk.article.title)
	await t.click(Selector('[data-tc-article-category-select]'))
	await t
		.click(Selector('[data-tc-article-category]'))
		.click(Selector('[data-tc-article-category] .multiselect__content .multiselect__element:nth-child(4)'))
	await t.typeText('trix-editor', `${helpdesk.article.textReplace}`)
	await t.click('[data-tc-article-save]')
	await t.expect(Selector(".notification-title").innerText).eql("Success")
}

export async function deleteArticle(t) {
	await t.useRole(common.userLogin)
	await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/articles`)
	await t.click('[data-tc-delete-article]')
	await t.expect(Selector('[data-tc-article-search]').exists).ok()
}
