import common from '../../common/helpers/common.js'
import { Selector, Role, ClientFunction } from 'testcafe'
import helpdesk from '../helpers/helpdesk_variables.js'

export async function createResponses(t) {
	await t.useRole(common.userLogin)
	await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/responses`)
	await t.click('[data-tc-add-response]')
	await t.typeText('input[data-tc-title]', helpdesk.response.title)
	await t.typeText('trix-editor', `${helpdesk.response.textReplace}`)
	await t.click('[data-tc-save-form-btn="Save Response"]')
	await t.expect(Selector(".notification-title").innerText).eql("Success")
}

export async function updateResponses(t) {
	await t.useRole(common.userLogin)
	await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/responses`)
	await t
		.hover('[data-tc-response-box]')
		.click('[data-tc-response-box]')
	await t.typeText('input[data-tc-title]', helpdesk.response.title)
	await t.typeText('trix-editor', `${helpdesk.response.textReplace}`)
	await t.click('[data-tc-save-form-btn="Save Response"]')
	await t.expect(Selector(".notification-title").innerText).eql("Success")
}

export async function deleteResponses(t) {
	await t.useRole(common.userLogin)
	await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/responses`)
	await t
		.hover('[data-tc-response-delete]')
		.click('[data-tc-response-delete]')
	await t
		.hover('[data-tc-response-cancel]')
		.click('[data-tc-response-cancel]')
	await t
		.hover('[data-tc-response-delete]')
		.click('[data-tc-response-delete]')
		.hover('[data-tc-response-delete-button]')
		.click('[data-tc-response-delete-button]')
	await t.expect(Selector(".notification-title").innerText).eql("Success")
}
