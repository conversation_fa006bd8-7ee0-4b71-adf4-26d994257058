import common from '../../common/helpers/common.js'
var assetParams =
{
  name          : 'Old iPhone',
  assetType     : "Phone",
  impact        : 'High',
  manufacturer  : common.faker.company.companyName(),
  model         : common.faker.lorem.slug(),
  notes         : common.faker.lorem.sentence(),
  userBy        : common.faker.name.findName(),
  managedBy     : common.faker.name.findName(),
  location      : "HQ",
  department    : common.faker.name.jobArea(),
}

var assetParams2 =
{
  name          : "Dell XPS 123",
  assetType     : "Laptop",
  assetTag      : common.faker.hacker.noun(),
  impact        : 'High',
  assetCost     : common.faker.commerce.price(),
  assetSalvage  : common.faker.commerce.price(),
  processor     : "Intel Core Duo",
  memory        : "1TB",
  hardDrive     : "500GB",
  serialNum     : common.faker.random.number(),
  modelNum      : "2022",
  assetMac      : "A1:B3:C4:D8:EE:F9",
  assetIp       : "**************",
  assetDepre    : "Declining Balance, 5 years (Declining Balance over 5 years)",
  tag           : common.faker.hacker.noun(),
  productNum    : common.faker.random.number(),
  manufacturer  : common.faker.company.companyName(),
  model         : common.faker.lorem.slug(),
  notes         : common.faker.lorem.sentence(),
  userBy        : common.faker.name.findName(),
  managedBy     : common.faker.name.findName(),
  location      : "HQ",
  department    : common.faker.name.jobArea(),
}

var updateAssetParams =
{
  name          : "Killer Workstation",
  assetType     : "Desktop",
  impact        : 'Low',
  manufacturer  : common.faker.company.companyName(),
  model         : common.faker.lorem.slug(),
  notes         : common.faker.lorem.sentence(),
  userBy        : common.faker.name.findName(),
  managedBy     : common.faker.name.findName(),
  location      : "HQ",
  department    : common.faker.name.jobArea(),
}

var primaryAsset =
{
  name          : "Desktop 1",
  assetType     : "Desktop",
}

var secondaryAsset =
{
  name          : "Desktop 2",
  assetType     : "Desktop",
}

var softwareParams =
{
  name          : "AoE2",
  softwareType  : "Utility",
  productKey    : common.faker.random.uuid(),
  installDate   : "",
}

var locationParams =
{
  name            : common.faker.lorem.words(),
  country         : "United States",
  address         : common.faker.address.streetAddress(),
  addressLine2    : common.faker.address.streetName(),
  city            : common.faker.address.city(),
  state           : "CA",
  zip             : common.faker.address.zipCode(),
}

var assetTypeParams =
{
  name            : "Aabcd",
}
var assetDepreciation =
{
  name            : common.faker.lorem.word(),
  usefulLife      : String(common.faker.random.number()),
  description     : common.faker.lorem.sentence(),
}
var assetLocation =
{
  name            : common.faker.lorem.words(),
  country         : "United States",
  address         : common.faker.address.streetAddress(),
  addressLine2    : common.faker.address.streetName(),
  city            : common.faker.address.city(),
  state           : "CA",
  zip             : common.faker.address.zipCode(),
}
export default {
  assetParams,
  updateAssetParams,
  softwareParams,
  locationParams,
  assetTypeParams,
  assetParams2,
  assetLocation,
  assetDepreciation,
  primaryAsset,
  secondaryAsset
}
