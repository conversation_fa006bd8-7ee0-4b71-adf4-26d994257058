import common from '../../common/helpers/common.js';
import { Selector } from 'testcafe';
import asset from '../helpers/asset_variables.js';

const assetSelect = Selector('[data-tc-managed-asset-type-id]');
const assetOption = assetSelect.find('span');
const primaryAsset = asset.primaryAsset;
const secondaryAsset = asset.secondaryAsset;


export async function mergeAsset(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/new`)
  await t.typeText('[data-tc-managed-asset-name]', primaryAsset.name)
  await t
    .click(assetSelect)
    .click(assetOption.withText(primaryAsset.assetType))
  await t.click('[data-tc-save-form-btn="Save assets"]')
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/new`)
  await t.typeText('[data-tc-managed-asset-name]', secondaryAsset.name)
  await t
    .click(assetSelect)
    .click(assetOption.withText(secondaryAsset.assetType))
  await t.click('[data-tc-save-form-btn="Save assets"]')
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/assets`)
  await t.click(Selector("[data-tc-asset-name]").withText(primaryAsset.name), { modifiers: { ctrl: true } })
  await t
    .expect(Selector("[data-tc-merge-show-btn]").exists)
    .notOk()
  await t.click(Selector("[data-tc-asset-name]").withText(secondaryAsset.name), { modifiers: { ctrl: true } })
  await t
    .expect(Selector("[data-tc-merge-show-btn]").exists)
    .ok()
  await t.click('[data-tc-merge-show-btn]')
  await t.click(`[data-tc-merge-asset-primary][value='${primaryAsset.name}']`)
  await t.click('[data-tc-merge-save-btn]')
  await t.click('[data-tc-merge-confirm-btn]')
  await t
    .expect(Selector(".notification-title").innerText)
    .eql("Success")
  await t
    .expect(Selector(".notification-content").innerText)
    .eql("Assets merged successfully!")
}

export async function checkMerged(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/assets`)
  await t
    .hover(Selector(`[data-tc-asset-box-link='${primaryAsset.name}']`))
    .click(Selector(`[data-tc-asset-box-link='${primaryAsset.name}']`))
  await t
    .expect(Selector("[data-tc-source='Merged']").exists)
    .ok()
  await t.click('[data-tc-asset-sources]')
  await t
    .expect(Selector("[data-tc-source-primary]").exists)
    .ok()
  await t
    .expect(Selector(`[data-tc-sourced-asset='${primaryAsset.name}']`).exists)
    .ok()
  await t.click(`[data-tc-view-asset='${primaryAsset.name}']`)
  await t
    .expect(Selector("[data-tc-merged-icon]").exists)
    .ok()
}
