import common from '../../common/helpers/common.js'
import { Selector, Role, ClientFunction } from 'testcafe'
import asset from '../helpers/asset_variables.js'

const assetBoxName = Selector('[data-tc-asset-name]');
const listViewAssetBoxName = Selector('[data-tc-asset-name--row]');
const assetListHeading = Selector('[data-tc-asset-list-heading]');
const activeFilterName = Selector('.filter-name');
const dissmissibleFilterMenu = Selector('.dissmissible-menu');

export async function resultsPerPage(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/assets`)
  await t.click('[data-tc-filters-per-page]')
  await t.click('[data-tc-filters-per-page] > option:nth-child(1)')
  await t.expect(assetBoxName.count).lte(25)
  await t.click('[data-tc-filters-per-page]')
  await t.click('[data-tc-filters-per-page] > option:nth-child(2)')
  await t.expect(assetBoxName.count).lte(50)
  await t.click('[data-tc-filters-per-page]')
  await t.click('[data-tc-filters-per-page] > option:nth-child(3)')
  await t.expect(assetBoxName.count).lte(100)
}

export async function pagination(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/assets`)
  if (await assetBoxName.count >= 25)
  {
    await t.click('.pagination .next-item')
    await t.click('.pagination .prev-item')
  }
}

export async function locationFilter(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/assets`)
  await t.click('[data-tc-filter-menu-button]')
  await t.click('[data-tc-filter-location]')
  await t.expect(Selector('#filterLocation div.dropdown-filter a.dropdown-item').withText(asset.assetParams.location).exists).ok()
}

export async function activeFilter(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/assets`)
  await t.expect(activeFilterName.withText('Active').exists).ok()
  await t.click('[data-tc-filter-menu-button]')
  await t.expect(dissmissibleFilterMenu.visible).ok()

  await t.click(Selector('[data-tc-filter-status]'))
  await t.click(Selector('[data-tc-filter-status] .dropdown-menu a:nth-child(3)'))
  await t.expect(activeFilterName.withText('Active').exists).notOk()
  await t.expect(activeFilterName.withText('Archived').exists).ok()
  await t.expect(assetBoxName.count).eql(0)

  await t.click(activeFilterName.withText('Archived').sibling('[data-tc-active-filter-close]'))
  await t.expect(activeFilterName.withText('Archived').exists).notOk()
  await t.expect(assetBoxName.count).gt(0)

  await t.click(Selector('[data-tc-filter-menu-button]'))
  await t.expect(Selector('[data-tc-filter-status] .dropdown-toggle').withText('Status: All').exists).ok()
  await t.click('[data-tc-dismissible-container-close-button] i')
  await t.expect(dissmissibleFilterMenu.visible).notOk()
}

export async function assetListCount(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/assets`)

  await t.expect(assetListHeading.innerText).eql("Assets (3)")

  await t.typeText('[data-tc-search-asset]', asset.updateAssetParams.name)
  await t.wait(4000)
  await t.expect(assetListHeading.innerText).eql("Assets (1)")
}

export async function gridListView(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/assets`)

  await t.expect(listViewAssetBoxName.count).eql(0)
  await t.expect(assetBoxName.count).gt(0)

  await t.click(Selector('[data-tc-list-toggle]'))

  await t.expect(assetBoxName.count).eql(0)
  await t.expect(listViewAssetBoxName.count).gt(1)
}
