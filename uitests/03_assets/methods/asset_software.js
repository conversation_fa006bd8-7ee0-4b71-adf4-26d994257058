import common from '../../common/helpers/common.js'
import asset from '../helpers/asset_variables.js'
import { Selector, Role, ClientFunction } from 'testcafe'

export async function softwareFormValidation(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/assets`)
  await t.typeText('[data-tc-search-asset]', asset.updateAssetParams.name)
  await t.wait(2000)
  await t.click(Selector('.box .box__inner .row .col .my-0 > span').withText(asset.updateAssetParams.name))
  await t.wait(2000)
  await t.click("[data-tc-software-link]")
  await t.wait(2000)
  await t.click("[data-tc-add-software]")
  await t.click(Selector("button").withText("Save Software"))
  await t.expect(Selector(".notification-content").innerText).eql("Please correct the highlighted errors before submitting.")
  await t.typeText('form input[name="asset_software[name]"]', asset.softwareParams.name)
  await t.click(Selector("button").withText("Save Software"))
  await t.expect(Selector(".notification-content").innerText).eql("Please correct the highlighted errors before submitting.")
  await t.typeText('form input[name="asset_software[product_key]"]', asset.softwareParams.productKey)
  await t.click(Selector("button").withText("Save Software"))
  await t.wait(2000)
}

export async function createSoftware(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/assets`)
  await t.typeText('[data-tc-search-asset]', asset.updateAssetParams.name)
  await t.wait(2000)
  await t.click(Selector('.box .box__inner .row .col .my-0 > span').withText(asset.updateAssetParams.name))
  await t.wait(2000)
  await t.click("[data-tc-software-link]")
  await t.wait(2000)
  await t.click("[data-tc-add-software]")
  await t.click(Selector("button").withText("Save Software"))
  await t.typeText('form input[name="asset_software[name]"]', asset.softwareParams.name)
  await t.typeText('form input[name="asset_software[product_key]"]', asset.softwareParams.productKey)
  await t.click(Selector("button").withText("Save Software"))
  await t.wait(2000)
}
