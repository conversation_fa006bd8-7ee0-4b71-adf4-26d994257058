import common from '../../common/helpers/common.js'
import asset from '../helpers/asset_variables.js'
import { Selector } from 'testcafe'

const assetSelect = Selector('[data-tc-managed-asset-type-id]');
const assetOption = assetSelect.find('span');


export async function createAssetType(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/settings/asset_types`)
  await t.typeText('[data-tc-asset-type-name]', asset.assetTypeParams.name)
  await t.click('[data-tc-save-form-btn="+ Add Custom Type"]')
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}

export async function createAssetTypeValidation(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/settings/asset_types`)
  await t.click('[data-tc-save-form-btn="+ Add Custom Type"]')
  await t.expect(Selector('[data-tc-asset-type-error]').innerText).eql("Asset type cannot be empty")
  await t.typeText('[data-tc-asset-type-name]', asset.assetTypeParams.name)
  await t.click('[data-tc-save-form-btn="+ Add Custom Type"]')
}

export async function removeCustomAssetType(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/settings/asset_types`)
  await t.expect(Selector('[data-tc-asset-type-remove-btn]').exists).ok()
  await t.click(Selector('[data-tc-asset-type-remove-btn]'))
}

export async function removeAndAddDefaultAssetType(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/settings/asset_types`)
  await t.click(Selector('[data-tc-asset-type-remove-btn]'))
  await t.click(Selector('[data-tc-add-default-asset-type-btn]'))
}

export async function createAssetWithCustomAssetType(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/settings/asset_types`)
  await t.typeText('[data-tc-asset-type-name]', asset.assetTypeParams.name)
  await t.click('[data-tc-save-form-btn="+ Add Custom Type"]')
  await t.expect(Selector('.notification-title').innerText).eql('Success')
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/new`)
  await t.click('[data-tc-save-form-btn="Save assets"]')
  await t.expect(Selector(".notification-content").innerText).eql("Please correct the highlighted errors before submitting.")
  await t.typeText('[data-tc-managed-asset-name]', asset.assetParams.name)
  await t.click('[data-tc-save-form-btn="Save assets"]')
  await t.expect(Selector(".notification-content").innerText).eql("Please correct the highlighted errors before submitting.")
  await t
    .click(assetSelect)
    .click(assetOption.withText("Aabcd"))
  await t.expect(Selector(".notification-content").innerText).eql("Please correct the highlighted errors before submitting.")
  await t.typeText('clearfix, .type-selector, input [data-tc-high]', asset.assetParams.impact)
  await t.typeText('form input[data-tc-managed-asset-manufacturer]', asset.assetParams.manufacturer)
  await t.typeText('form input[data-tc-managed-asset-model]', asset.assetParams.model)
  await t.typeText('form textarea[data-tc-managed-asset-description]', asset.assetParams.notes)
  await t.typeText('form input[data-tc-asset-department]', asset.assetParams.department)
  await t.click('[data-tc-save-form-btn="Save assets"]')
  await t.expect(Selector(".notification-content").innerText).eql("Asset was successfully created")
}

export async function removeCustomAssetTypeHavingAssets(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/settings/asset_types`)
  await t.click(Selector('[data-tc-current-asset-types] [data-tc-asset-type-remove-btn]'))
  await t.click(Selector('[data-tc-change-asset-types]'))
  await t.click(Selector('[data-tc-asset-type-options]').withText("Other"))
  await t.click(Selector('[data-tc-change-asset-type-submit-btn]'))
  await t.click(Selector('[data-tc-current-asset-types] [data-tc-asset-type-remove-btn]'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}
