import common from '../../common/helpers/common.js'
import { Selector } from 'testcafe'

const defaultActiveEditableColumns = Selector('[data-tc-list-view-column-deactivate-btn]');
const defaultInactiveEditableColumns = Selector('[data-tc-list-view-column-activate-btn]');


export async function activateAndDeactivateListViewColumn(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/settings/list_view_columns`)

  await t.click(Selector('[data-tc-list-view-column-deactivate-btn="Source"]'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')

  await t.click(Selector('[data-tc-list-view-column-activate-btn="Source"]'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')

}
