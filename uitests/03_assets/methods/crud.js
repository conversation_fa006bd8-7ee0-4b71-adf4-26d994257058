import common from '../../common/helpers/common.js'
import { Selector } from 'testcafe'
import asset from '../helpers/asset_variables.js'

const assetSelect = Selector('[data-tc-managed-asset-type-id]');
const assetOption = assetSelect.find('span');
const assetDep = Selector('[data-tc-dep-drop-down]');
const assetDepOption = assetDep.find('span')
const assetLocation = Selector('[data-tc-managed-asset-location-id]');
const assetLocationOption = assetLocation.find('span');
const assetFilterStatus = Selector('[data-tc-filter-status]');
const assetName = Selector('[data-tc-asset-name]');
const cost = asset.assetParams2.assetCost.toString();
const salvage = asset.assetParams2.assetSalvage.toString();
const usedBy = Selector('[data-tc-used-by]');
const usedByUser = usedBy.find('span');
const managedBy = Selector('[data-tc-managed-by]');
const managedByUser = managedBy.find('span');
const countryField = Selector('[data-tc="country_name"]');;
const countryFieldOption = countryField.find('span');
const stateField = Selector('[data-tc="state"]');
const stateFieldOption = stateField.find('span');
const serialNumber = asset.assetParams2.serialNum.toString();
const productNumber = asset.assetParams2.productNum.toString();

export async function assetFormValidation(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/new`)
  await t.click('[data-tc-save-form-btn="Save assets"]')
  await t.expect(Selector(".notification-content").innerText).eql("Please correct the highlighted errors before submitting.")
  await t.typeText('[data-tc-managed-asset-name]', asset.assetParams.name)
  await t.click('[data-tc-save-form-btn="Save assets"]')
  await t.expect(Selector(".notification-content").innerText).eql("Please correct the highlighted errors before submitting.")
  await t
    .click(assetSelect)
    .click(assetOption.withText(asset.assetParams2.assetType))
  await t.typeText('clearfix, .type-selector, input [data-tc-high]', asset.assetParams.impact)
  await t.typeText('form input[data-tc-managed-asset-manufacturer]', asset.assetParams.manufacturer)
  await t.typeText('form input[data-tc-managed-asset-model]', asset.assetParams.model)
  await t.typeText('form textarea[data-tc-managed-asset-description]', asset.assetParams.notes)
  await t.typeText('form input[data-tc-asset-department]', asset.assetParams.department)
  await t.click('[data-tc-save-form-btn="Save assets"]')
  await t.expect(Selector(".notification-content").innerText).eql("Asset was successfully created")
}

export async function createAsset(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/new`)
  await t.click('[data-tc-save-form-btn="Save assets"]')
  await t.expect(Selector(".notification-content").innerText).eql("Please correct the highlighted errors before submitting.")
  await t.typeText('[data-tc-managed-asset-name]', asset.assetParams2.name)
  await t.click('[data-tc-save-form-btn="Save assets"]')
  await t.expect(Selector(".notification-content").innerText).eql("Please correct the highlighted errors before submitting.")
  await t
    .click(assetSelect)
    .click(assetOption.withText(asset.assetParams2.assetType))
  await t.typeText('[data-tc-asset-tag]', asset.assetParams2.assetTag)
  await t.typeText('clearfix, .type-selector, input [data-tc-high]', asset.assetParams.impact)
  await t.typeText('[data-tc-cost] #pretty_transaction_amount', cost)
  await t.typeText('[data-tc-salvage] #pretty_transaction_amount', salvage)
  await t
    .hover(assetDep)
    .click(assetDep)
  await t
    .hover(assetDepOption.withText("Declining Balance, 5 years (Declining Balance over 5 years)"))
    .click(assetDepOption.withText("Declining Balance, 5 years (Declining Balance over 5 years)"))
  await t.typeText('[data-tc-processor]', asset.assetParams2.processor)
  await t.typeText('[data-tc-memory]', asset.assetParams2.memory)
  await t.typeText('[data-tc-hard-drive]', asset.assetParams2.hardDrive)
  await t.typeText('[data-tc-serial-number]', serialNumber)
  await t.typeText('[data-tc-model]', asset.assetParams2.modelNum)
  await t.typeText('form input[data-tc-managed-asset-manufacturer]', asset.assetParams.manufacturer)
  await t.typeText('[data-tc-mac-address]', asset.assetParams2.assetMac)
  await t.typeText('[data-tc-ip-address]', asset.assetParams2.assetIp)
  await t.typeText('form input[data-tc-managed-asset-model]', asset.assetParams.model)
  await t.click('[data-tc-tag] .multiselect__tags')
  await t.typeText('[data-tc-tag] .multiselect__tags', asset.assetParams2.tag).pressKey("enter")
  await t.typeText('form textarea[data-tc-managed-asset-description]', asset.assetParams.notes)
  await t
    .hover(usedBy)
    .click(usedBy)
  await t.typeText(usedBy, common.user.name)
  await t
    .click(usedByUser.withText(common.user.name))
  await t
    .hover(managedBy)
    .click(managedBy)
  await t.typeText(managedBy, common.user.name)
  await t
    .click(managedByUser.withText(common.user.name))
  await t
    .hover('[data-tc-location-form]')
    .click('[data-tc-location-form]')

  await t.typeText(Selector('[data-tc="name"]'), asset.assetParams.location)
  await t.typeText(Selector('[data-tc="address"]'), asset.locationParams.address)
  await t.typeText(Selector('[data-tc="city"]'), asset.locationParams.city)
  await t.typeText(Selector('[data-tc="zip"]'), asset.locationParams.zip)
  await t.typeText(Selector('[data-tc="address_2"]'), asset.locationParams.addressLine2)
  await t
    .hover(stateField)
    .click(stateField)
    .hover(stateFieldOption.withText("CO"))
    .click(stateFieldOption.withText("CO"))
  await t
    .hover(countryField)
    .click(countryField)
    .hover(countryFieldOption.withText("United States"))
    .click(countryFieldOption.withText("United States"))

  await t.typeText(Selector('[data-tc="notes"]'), "First location")
  await t.click(Selector('[data-tc-create="location"]'))
  await t
    .click(assetLocation)
    .click(assetLocationOption.withText(asset.assetParams.location))
  await t.typeText('form input[data-tc-asset-department]', asset.assetParams.department)
  await t.typeText('[data-tc-product-number]', productNumber)
  await t.click('[data-tc-acquisition-date] #acquisition_date')
  await t.click('[data-tc-acquisition-date] .mx-icon-last-month')
  await t.click(Selector('[data-tc-acquisition-date] td.cell.cur-month').withText('1'))
  await t.click('[data-tc-warranty-date] ')
  await t.click('[data-tc-warranty-date] #expiration_date')
  await t.click('[data-tc-warranty-date] .mx-icon-next-year')
  await t.click('[data-tc-warranty-date] .mx-icon-next-year')
  await t.click(Selector('[data-tc-warranty-date] td.cell.cur-month').withText('28'))
  await t.click('[data-tc-installation-date] #installation_date')
  await t.click(Selector('[data-tc-installation-date] td.cell.cur-month').withText('28'))
  await t.click('[data-tc-add-alert-date-btn]')
  await t.click('[data-tc-remove-alert]')
  await t.click('[data-tc-add-alert-date-btn]')
  await t.click('[data-tc-alert] #alert_date_0')
  await t.click('[data-tc-alert] .mx-icon-last-month')
  await t.click(Selector('[data-tc-alert] td.cell.cur-month').withText('1'))
  await t.expect(Selector(".notification-title").innerText).eql("Error")
  await t.click('[data-tc-alert] .mx-icon-next-year')
  await t.click(Selector('[data-tc-alert] td.cell.cur-month').withText('28'))
  await t.typeText('[data-tc-asset-alert-notes]', asset.assetParams2.notes)
  await t.click('[data-tc-attachment]')
  await t
    .setFilesToUpload('[data-tc-attachment] .drop__file-input', '../../common/files/assets.jpg')
  await t.click('[data-tc-attachment-image="managed_assets"]')
  await t
    .setFilesToUpload('[data-tc-attachment-image="managed_assets"] .drop__file-input', '../../common/files/image.png')
  await t.click('[data-tc-remove]')
  await t.click('[data-tc-attachment-image="managed_assets"]')
  await t
    .setFilesToUpload('[data-tc-attachment-image="managed_assets"] .drop__file-input', '../../common/files/assets.jpg')
  await t.click('[data-tc-save-form-btn="Save assets"]')
  await t.expect(Selector(".notification-content").innerText).eql("Asset was successfully created")
}

export async function updateAsset(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/assets`)
  await t.typeText('[data-tc-search-asset]', asset.assetParams.name)
  await t.click(Selector("[data-tc-asset-name]").withText(asset.assetParams.name))
  await t.click(Selector("[data-tc-edit-asset]"))
  await t.selectText('[data-tc-managed-asset-name]').pressKey("delete");
  await t.typeText('[data-tc-managed-asset-name]', asset.updateAssetParams.name)
  await t
    .click(assetSelect)
    .click(assetOption.withText(asset.updateAssetParams.assetType))
  await t.typeText('clearfix, .type-selector, input [data-tc-low]', asset.updateAssetParams.impact)
  await t.selectText('form input[data-tc-managed-asset-manufacturer]').pressKey("delete");
  await t.typeText('form input[data-tc-managed-asset-manufacturer]', asset.updateAssetParams.manufacturer)
  await t.selectText('form input[data-tc-managed-asset-model]').pressKey("delete");
  await t.typeText('form input[data-tc-managed-asset-model]', asset.updateAssetParams.model)
  await t.selectText('form textarea[data-tc-managed-asset-description]').pressKey("delete");
  await t.typeText('form textarea[data-tc-managed-asset-description]', asset.updateAssetParams.notes)
  await t.selectText('[data-tc-asset-department]').pressKey("delete");
  await t.typeText('[data-tc-asset-department]', asset.updateAssetParams.department)
  await t.click('[data-tc-save-form-btn="Save assets"]')
  await t.expect(Selector(".notification-content").innerText).eql("Asset was successfully updated")
  await t.click(Selector('p > a').withText("set it here."))
  await t.selectText('[data-tc-asset-department]').pressKey("delete");
  await t.typeText('[data-tc-asset-department]', asset.updateAssetParams.department)
  await t.click('[data-tc-save-form-btn="Save"]')
  await t.expect(Selector(".notification-content").innerText).eql("Asset was successfully updated")
}

export async function archiveAsset(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/assets`)
  await t.typeText('[data-tc-search-asset]', asset.updateAssetParams.name)
  await t.click(Selector("[data-tc-asset-name]").withText(asset.updateAssetParams.name))
  await t.click(Selector("[data-tc-archive-asset-btn]"))
  await t.click(Selector("[data-tc-modal-asset-archive-btn]"))
  await t.click('[data-tc-filter-menu-button]')
  await t.click(assetFilterStatus)
  await t.click(Selector("[data-tc-filter-status] .dropdown-menu a:nth-child(3)"))
  await t.click(Selector("[data-tc-asset-name]").withText(asset.updateAssetParams.name))
  await t.expect(Selector("[data-tc-status-archived]").innerText).eql("ARCHIVED")
}

export async function unArchiveAsset(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/assets`)
  await t.typeText('[data-tc-search-asset]', asset.updateAssetParams.name)
  await t.click('[data-tc-filter-menu-button]')
  await t
    .click(assetFilterStatus)
  await t
    .hover(Selector("[data-tc-filter-status] .dropdown-menu a:nth-child(3)"))
    .click(Selector("[data-tc-filter-status] .dropdown-menu a:nth-child(3)"))
  await t
    .hover(Selector(`[data-tc-asset-box-link='${asset.updateAssetParams.name}']`))
    .click(Selector(`[data-tc-asset-box-link='${asset.updateAssetParams.name}']`))
  await t.expect(common.getPageUrl()).match(/managed_assets\/\d+/)
  await t
    .hover(Selector("[data-tc-unarchive-asset-btn]"))
    .click(Selector("[data-tc-unarchive-asset-btn]"))
  await t
    .hover('[data-tc-filter-menu-button]')
    .click('[data-tc-filter-menu-button]')
  await t.click(assetFilterStatus)
  await t.click(Selector("[data-tc-filter-status] .dropdown-menu a:nth-child(2)"))
  await t.typeText('[data-tc-search-asset]', asset.updateAssetParams.name)
  await t.expect(assetName.withText(asset.updateAssetParams.name).exists).ok()
}

export async function deleteAsset(t) {
  await t.useRole(common.userLogin)
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/assets`)
  await t.click(Selector("[data-tc-asset-name]").withText(asset.updateAssetParams.name))
  await t.click(Selector("[data-tc-archive-asset-btn]"))
  await t.click(Selector("[data-tc-modal-asset-archive-btn]"))
  await t.typeText('[data-tc-search-asset]', asset.updateAssetParams.name)
  await t.click('[data-tc-filter-menu-button]')
  await t
    .click(assetFilterStatus)
  await t
    .hover(Selector("[data-tc-filter-status] .dropdown-menu a:nth-child(3)"))
    .click(Selector("[data-tc-filter-status] .dropdown-menu a:nth-child(3)"))
  await t
    .hover(Selector(`[data-tc-asset-box-link='${asset.updateAssetParams.name}']`))
    .click(Selector(`[data-tc-asset-box-link='${asset.updateAssetParams.name}']`))
  await t
    .hover(Selector("[data-tc-delete-asset-btn]"))
    .click(Selector("[data-tc-delete-asset-btn]"))
  await t
    .hover(Selector("[data-tc-modal-delete-asset-btn]"))
    .click(Selector("[data-tc-modal-delete-asset-btn]"))
  await t.expect(assetName.withText(asset.updateAssetParams.name).exists).notOk()
}
