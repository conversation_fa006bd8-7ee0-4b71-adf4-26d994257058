import common from '../../common/helpers/common.js'
import asset from '../helpers/asset_variables.js'
import { Selector, Role, ClientFunction } from 'testcafe'

const countryField = Selector('[data-tc="country_name"]');
const countryFieldOption = countryField.find('span');

export async function createLocation(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/new`)
  await t.click('[data-tc-location-form]')
  await t.typeText(Selector('[data-tc="name"]'), asset.assetLocation.name)
  await t.typeText(Selector('[data-tc="address"]'), asset.assetLocation.address)
  await t.typeText(Selector('[data-tc="city"]'), asset.assetLocation.city)
  await t.typeText(Selector('[data-tc="zip"]'), asset.assetLocation.zip)
  await t.typeText(Selector('[data-tc="address_2"]'), asset.assetLocation.addressLine2)
  await t
    .hover(countryField)
    .click(countryField)
    .hover(countryFieldOption.withText("United States"))
    .click(countryFieldOption.withText("United States"))

  await t.click(Selector('[data-tc-create="location"]'))
}
