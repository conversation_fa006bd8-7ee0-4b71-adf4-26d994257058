import {Selector} from 'testcafe';
import common from '../../common/helpers/common.js'
import asset from '../helpers/asset_variables.js'

export async function createAssetDepreciation(t) {
	await t.useRole(common.userLogin)
	await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/settings`)
	await t.click('[data-tc-depreciation]')
	await t.click('[data-tc-add-new]')
	await t.typeText('input[data-tc-name]', asset.assetDepreciation.name)
	await t
		.hover('[data-tc-depreciation-type]')
		.click('[data-tc-depreciation-type]')
	await t.typeText('input[data-tc-usefullife]', asset.assetDepreciation.usefulLife)
	await t.typeText('[data-tc-description]', asset.assetDepreciation.description)
	await t.click('[data-tc-save-form-btn="Save"]')
	await t.expect(Selector(".notification-title").innerText).eql("Success")
}

export async function updateAssetDepreciation(t){
	await t.useRole(common.userLogin)
	await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/settings`)
	await t.click('[data-tc-depreciation]')
	await t
		.hover('[data-tc-row]')
		.click('[data-tc-edit]')
	await t.typeText('input[data-tc-name]', asset.assetDepreciation.name)
	await t
		.hover('[data-tc-depreciation-type]')
		.click('[data-tc-depreciation-type]')
	await t.typeText('input[data-tc-usefullife]', asset.assetDepreciation.usefulLife)
	await t.click('[data-tc-save-form-btn="Update"]')
	// await t.expect(Selector(".notification-title").innerText).eql("Success")

}
export async function deleteAssetDepreciation(t){
	await t.useRole(common.userLogin)
	await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/managed_assets/settings`)
	await t.click('[data-tc-depreciation]')
	await t
		.hover('[data-tc-row]')
		.click('[data-tc-delete]')
		.hover('[data-tc-cdelete]')
		.click('[data-tc-cancel]')
		.hover('[data-tc-row]')
		.click('[data-tc-delete]')
		.hover('[data-tc-cdelete]')
		.click('[data-tc-cdelete]')
	await t.expect(Selector(".notification-title").innerText).eql("Success")
}
