import { assetFormValidation, createAsset, updateAsset, deleteAsset, archiveAsset, unArchiveAsset } from './methods/crud.js'
import { softwareFormValidation, createSoftware } from './methods/asset_software.js'
import { createAssetType, createAssetTypeValidation, removeCustomAssetType, removeAndAddDefaultAssetType, createAssetWithCustomAssetType, removeCustomAssetTypeHavingAssets } from './methods/asset_type.js'
import { activateAndDeactivateListViewColumn } from './methods/list_view_columns.js'
import { resultsPerPage, pagination, locationFilter, activeFilter, gridListView, assetListCount } from './methods/filters.js'
import { createAssetDepreciation, updateAssetDepreciation, deleteAssetDepreciation} from './methods/asset_depreciation.js'
import { createLocation } from './methods/location.js'
import { mergeAsset, checkMerged } from './methods/asset_merge.js';

fixture('Usecase: Asset')

test('Asset create form validation', async t => {
  await assetFormValidation(t);
})

test('Create', async t => {
  await createAsset(t);
})

test('Merge assets', async t => {
  await mergeAsset(t);
})

test('Check if assets merged', async t => {
  await checkMerged(t);
})

test('Update', async t => {
  await updateAsset(t);
})

test('Software create form validation', async t => {
  await softwareFormValidation(t);
})

test('Create Software', async t => {
  await createSoftware(t);
})

test('Create Location', async t => {
  await createLocation(t);
})

test('Location filter', async t => {
  await locationFilter(t);
})

test('Asset list count', async t => {
  await assetListCount(t);
})

test('Active filter', async t => {
  await activeFilter(t);
})

test('Results Per Page', async t => {
  await resultsPerPage(t);
})

test('Pagination', async t => {
  await pagination(t);
})

test('gridListView', async t => {
  await gridListView(t);
})

test('Archive', async t => {
  await archiveAsset(t);
})

test('unArchive', async t => {
  await unArchiveAsset(t);
})

test('Delete', async t => {
  await deleteAsset(t);
})

test('Create asset type', async t => {
  await createAssetType(t);
})

test('Create asset type validation', async t => {
  await createAssetTypeValidation(t);
})

test('Remove custom asset type', async t => {
  await removeCustomAssetType(t);
})

test('Remove and add default asset type', async t => {
  await removeAndAddDefaultAssetType(t);
})

test('Create asset with custom Asset type', async t => {
  await createAssetWithCustomAssetType(t);
})

test('Remove custom Asset type have associated assets', async t => {
  await removeCustomAssetTypeHavingAssets(t);
})

test('Activate and deactivate list view column', async t => {
  await activateAndDeactivateListViewColumn(t);
})

test('Create Asset Depreciation',async t=>{
  await createAssetDepreciation(t);
})

test('Update Asset Depreciation', async t=>{
  await updateAssetDepreciation(t);
})

test('Delete Asset Depreciation', async t=>{
  await deleteAssetDepreciation(t);
})
