import common from '../../common/helpers/common.js'
import { Selector } from 'testcafe'
import customForm from '../helpers/custom_form_variables.js'

export async function createBlankStateForm(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/settings/custom_forms`)
  await t.click(Selector('[data-tc-blank-state]'))
  await t.expect(common.getPageUrl()).contains('new?templateId')
  await t
    .hover(Selector('[data-tc-edit-custom-form]'))
    .click(Selector('[data-tc-edit-custom-form]'))
  await t.typeText(Selector('[data-tc-form-name]'), customForm.customFormParams.formName)
  await t.click('[data-tc-save-form-preferences]')
  await t.click('[data-tc-save-form-btn="Create form"]')
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}

export async function dragDropAndEditField(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/settings/custom_forms`)
  await t.click(Selector('[data-tc-blank-state]'))
  await t.dragToElement(Selector('[data-tc-asset-list]'), '#custom_form')
  await t.typeText(Selector('[data-tc-edit-label-name="Asset List"]'), customForm.assetListEditParams.label)
  await t.typeText(Selector(`[data-tc-edit-field="Asset List${customForm.assetListEditParams.label}"] [data-tc-edit-note]`), customForm.assetListEditParams.note)
  await t.click(Selector(`[data-tc-edit-field="Asset List${customForm.assetListEditParams.label}"] [data-tc-edit-required]`))
  await t.expect(Selector(`[data-tc-live-preview="Asset List${customForm.assetListEditParams.label}"] [data-tc-label-text]`).innerText).eql(`Asset List`+customForm.assetListEditParams.label)
  await t.expect(Selector(`[data-tc-live-preview="Asset List${customForm.assetListEditParams.label}"] [data-tc-note]`).innerText).eql(customForm.assetListEditParams.note)
  await t.click(`[data-tc-save-edit="Asset List${customForm.assetListEditParams.label}"]`)
}

export async function removeField(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/settings/custom_forms`)
  await t.click(Selector('[data-tc-blank-state]'))
  await t.dragToElement(Selector('[data-tc-asset-list]'), '#custom_form')
  await t.click('[data-tc-save-edit="Asset List"]')
  await t
    .hover(Selector('[data-tc-assetlist-delete]'))
    .click(Selector('[data-tc-assetlist-delete]'))
}
