import common from '../../common/helpers/common.js'
import { Selector, Role, ClientFunction } from 'testcafe'
import automatedTask from '../helpers/auto_task_variables.js'
import {
  anySubjectDetail,
  anyTicketDetail,
  newAutomatedTask,
  assignPriorityToAnyForm,
  setStatusToAnyForm,
  addCommentAction,
  directAssignmentToAnyForm,
  subjectWithTextDetail,
  subjectWithoutTextDetail,
  cleanupTasks,
  sendEmailAction
} from './crud.js'

export async function anyTicketSendAnEmail(t) {
  await newAutomatedTask(t)
  await anyTicketDetail(t)
  await sendEmailAction(t)
}

export async function editAnyTicketSendAnEmail(t) {
  let subject = 'Test Subject'
  let body = "Test test test test"
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/automated_tasks`)
  await t.expect(Selector('[data-tc-event-clause]').innerText).eql('any ticket')
  await t.expect(Selector('[data-tc-event-trigger]').innerText).eql('is created')
  await t.click(Selector('[data-tc-event-action]').withText('send an email'))

  await t.click(Selector('[data-tc-clone]'))
  let targetOption = Selector('[data-tc-target-option]')
  await t.click(targetOption.withText("All Admins"))
  await t.typeText(Selector('[data-tc-subject]'), subject)
  await t.typeText(Selector('trix-editor'), body)
  await t.click(Selector('[data-tc-submit]'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}

export async function deleteAnyTicketSendAnEmail(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/automated_tasks`)
  await cleanupTasks(t)
}

export async function anyTicketAssignPriority(t, priority = 'Medium') {
  await newAutomatedTask(t)
  await anyTicketDetail(t)
  await assignPriorityToAnyForm(t)
  await cleanupTasks(t)
}

export async function anyTicketSetStatus(t) {
  await newAutomatedTask(t)
  await anyTicketDetail(t)
  await setStatusToAnyForm(t)
  await cleanupTasks(t)
}

export async function anyTicketAddComment(t) {
  await newAutomatedTask(t)
  await anyTicketDetail(t)
  await addCommentAction(t)
  await cleanupTasks(t)
}

export async function anyTicketAssignAgent(t) {
  await newAutomatedTask(t)
  await anyTicketDetail(t)
  await directAssignmentToAnyForm(t)
  await cleanupTasks(t)
}
