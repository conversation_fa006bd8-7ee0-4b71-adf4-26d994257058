import common from '../../common/helpers/common.js'
import { Selector, Role, ClientFunction } from 'testcafe'
import automatedTask from '../helpers/auto_task_variables.js'
import {
  cleanupTasks,
} from './crud.js'

const events = ["ticket_created",
                "attachment_added",
                "note_added",
                "status_changed",
                "priority_changed",
                "assigned_to_me",
                "assigned_to_anyone"]
const eventTasks = {
  "ticket_created": "",
  "attachment_added": "",
  "note_added": "",
  "status_changed": "",
  "priority_changed": "",
  "assigned_to_me": "",
  "assigned_to_anyone": "",
}

export async function emailSettingsWithAutomatedTasks(t) {
  await t.useRole(common.userLogin)

  let event;
  let task;
  let toggle;
  for (let idx = 0; idx < events.length; idx++) {
    event = events[idx]

    await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/settings/email_notifications`)
    toggle = Selector(`[data-tc-email-toggle=${event}]`)
    await t.expect(toggle.find(`.slide-toggle`)).ok()
    await t.expect(toggle.find(".slide-toggle.slide-toggle--active")).ok()
    await t.click(toggle)
    await t.expect(Selector('.notification-title').innerText).eql('Success')
    await t.expect(toggle.find(`.slide-toggle`)).ok()
    await t.expect(toggle.find(".slide-toggle.slide-toggle--active")).ok()

    await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/automated_tasks`)
    task = Selector(`[data-tc-task-company-mailer=${event}]`)
    await t.expect(task.visible).ok()
    await t.expect(task.find(".slide-toggle")).ok()
    await t.click(task.find(".slide-toggle"))
    await t.expect(task.find(".slide-toggle.off")).ok()

    await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/settings/email_notifications`)
    toggle = Selector(`[data-tc-email-toggle=${event}]`)
    await t.expect(toggle.find(`.slide-toggle`)).ok()
    await t.expect(toggle.find(".slide-toggle.slide-toggle--active")).ok()
    await t.click(toggle)
    await t.expect(Selector('.notification-title').innerText).eql('Success')
    await t.expect(toggle.find(".slide-toggle.slide-toggle--active")).ok()

    await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/automated_tasks`)
    task = Selector(`[data-tc-task-company-mailer=${event}]`)
    await t.expect(task.visible).ok()
    await t.expect(task.find(".slide-toggle")).ok()
    await t.expect(task.find(".slide-toggle.off")).ok()
    await t.click(task.find(".slide-toggle"))
    await t.expect(task.find(".slide-toggle.off")).ok()
  }
}

export async function emailSettingsWithoutAutomatedTasks(t) {
  await t.useRole(common.userLogin)
  await cleanupTasks(t)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/automated_tasks`)
  await t.expect(Selector(`[data-tc-task]`)).ok()

  let event;
  let task;
  let toggle;
  for (let idx = 0; idx < events.length; idx++) {
    event = events[idx]

    await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/settings/email_notifications`)
    toggle = Selector(`[data-tc-email-toggle=${event}]`)
    await t.expect(toggle.find(`.slide-toggle`).exists).ok()
    await t.expect(toggle.find(".slide-toggle.slide-toggle--active")).ok()
    await t.click(toggle)
    await t.expect(Selector('.notification-title').innerText).eql('Success')
    await t.expect(toggle.find(`.slide-toggle`).exists).ok()
    await t.expect(toggle.find(".slide-toggle.slide-toggle--active").expect).notOk()

    await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/automated_tasks`)
    task = Selector(`[data-tc-task-company-mailer=${event}]`)
    await t.expect(task.visible).ok()
    await t.expect(task.find(".slide-toggle.off").expect).notOk()
    await t.click(task.find(".slide-toggle"))
    await t.expect(task.find(".slide-toggle").exists).ok()
    await t.expect(task.find(".slide-toggle.off").exists).notOk()

    await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/settings/email_notifications`)
    toggle = Selector(`[data-tc-email-toggle=${event}]`)
    await t.expect(toggle.find(`.slide-toggle`).exists).ok()
    await t.expect(toggle.find(".slide-toggle.slide-toggle--active")).ok()
    await t.click(toggle)
    await t.expect(Selector('.notification-title').innerText).eql('Success')
    await t.expect(toggle.find(`.slide-toggle`).exists).ok()
    await t.expect(toggle.find(".slide-toggle.slide-toggle--active").expect).notOk()

    await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/automated_tasks`)
    task = Selector(`[data-tc-task-company-mailer=${event}]`)
    await t.expect(task.visible).ok()
    await t.expect(task.find(".slide-toggle.off").expect).notOk()
    await t.click(task.find(".slide-toggle"))
    await t.expect(task.find(".slide-toggle").exists).ok()
    await t.expect(task.find(".slide-toggle.off").exists).notOk()
  }
}
