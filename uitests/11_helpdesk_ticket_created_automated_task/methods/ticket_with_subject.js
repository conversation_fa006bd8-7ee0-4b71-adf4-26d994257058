import common from '../../common/helpers/common.js'
import { Selector, Role, ClientFunction } from 'testcafe'
import automatedTask from '../helpers/auto_task_variables.js'
import {
  anySubjectDetail,
  anyTicketDetail,
  newAutomatedTask,
  assignPriorityToItGeneralRequest,
  setStatusToItGeneralRequest,
  addCommentAction,
  directAssignmentToItGeneralRequest,
  subjectWithTextDetail,
  subjectWithoutTextDetail,
  cleanupTasks,
  sendEmailAction
} from './crud.js'


// Any Subject
export async function ticketWithSubjectAnySubjectSendEmail(t) {
  await newAutomatedTask(t)
  await anySubjectDetail(t)
  await sendEmailAction(t)
  await cleanupTasks(t)
}

export async function ticketWithSubjectAnySubjectAssignPriority(t) {
  await newAutomatedTask(t)
  await anySubjectDetail(t)
  await assignPriorityToItGeneralRequest(t)
  await cleanupTasks(t)
}

export async function ticketWithSubjectAnySubjectSetStatus(t) {
  await newAutomatedTask(t)
  await anySubjectDetail(t)
  await setStatusToItGeneralRequest(t, 'In Progress')
  await cleanupTasks(t)
}


export async function ticketWithSubjectAnySubjectAddComment(t) {
  await newAutomatedTask(t)
  await anySubjectDetail(t)
  await addCommentAction(t, automatedTask.comment)
  await cleanupTasks(t)
}

export async function ticketWithSubjectAnySubjectAssignAgent(t) {
  await newAutomatedTask(t)
  await anySubjectDetail(t)
  await directAssignmentToItGeneralRequest(t)
  await cleanupTasks(t)
}

//Subject contain text
export async function ticketWithSubjectSubjectContainsTextSendEmail(t) {
  await newAutomatedTask(t)
  await subjectWithTextDetail(t, automatedTask.subjectContainsText)
  await sendEmailAction(t)
  await cleanupTasks(t)
}

export async function ticketWithSubjectSubjectContainsTextAssignPriority(t) {
  await newAutomatedTask(t)
  await subjectWithTextDetail(t)
  await assignPriorityToItGeneralRequest(t)
  await cleanupTasks(t)
}

export async function ticketWithSubjectSubjectContainsTextSetStatus(t) {
  await newAutomatedTask(t)
  await subjectWithTextDetail(t)
  await setStatusToItGeneralRequest(t, "In Progress")
  await cleanupTasks(t)
}

export async function ticketWithSubjectSubjectContainsTextAddComment(t) {
  await newAutomatedTask(t)
  await subjectWithTextDetail(t)
  await addCommentAction(t)
  await cleanupTasks(t)
}

export async function ticketWithSubjectSubjectContainsTextAssignAgent(t) {
  await newAutomatedTask(t)
  await subjectWithTextDetail(t)
  await directAssignmentToItGeneralRequest(t)
  await cleanupTasks(t)
}

// Subject does not contain text
export async function ticketWithSubjectSubjectDoesNotContainTextSendEmail(t) {
  await newAutomatedTask(t)
  await subjectWithoutTextDetail(t)
  await sendEmailAction(t)
  await cleanupTasks(t)
}

export async function ticketWithSubjectSubjectDoesNotContainTextAssignPriority(t) {
  await newAutomatedTask(t)
  await subjectWithoutTextDetail(t)
  await assignPriorityToItGeneralRequest(t)
  await cleanupTasks(t)
}

export async function ticketWithSubjectSubjectDoesNotContainTextSetStatus(t) {
  await newAutomatedTask(t)
  await subjectWithoutTextDetail(t)
  await setStatusToItGeneralRequest(t)
  await cleanupTasks(t)
}

export async function ticketWithSubjectSubjectDoesNotContainTextAddComment(t) {
  await newAutomatedTask(t)
  await subjectWithoutTextDetail(t)
  await addCommentAction(t)
  await cleanupTasks(t)
}

export async function ticketWithSubjectSubjectDoesNotContainTextAssignAgent(t) {
  await newAutomatedTask(t)
  await subjectWithoutTextDetail(t)
  await directAssignmentToItGeneralRequest(t)
  await cleanupTasks(t)
}
