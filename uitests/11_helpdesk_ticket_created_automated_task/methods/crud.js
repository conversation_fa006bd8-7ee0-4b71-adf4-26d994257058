import common from '../../common/helpers/common.js'
import { Selector, Role, ClientFunction } from 'testcafe'
import automatedTask from '../helpers/auto_task_variables.js'

let selectOption = Selector('.multiselect__content .multiselect__element span');

export async function anySubjectDetail(t) {
  await t.click(Selector('[data-tc-event-select]'))
  await t.click(selectOption.withText('a ticket is created'))
  await t.click(Selector('[data-tc-event-detail]'))
  await t.click(selectOption.withText('a ticket with a form field'))

  await t.expect(Selector('[data-tc-form-select]').innerText).eql('Any Form')  

  await t
    .click(Selector('[data-tc-field-select]'))
    .click(Selector('[data-tc-field-select] .multiselect__element').withText(/subject/))

  await t.click(Selector('[data-tc-any-value]'))
  await t.click(Selector('[data-tc-submit]'))
}


export async function anyTicketDetail(t) {
  await t.click(Selector('[data-tc-event-select]'))
  await t.click(selectOption.withText('a ticket is created'))
  await t.click(Selector('[data-tc-event-detail]'))
  await t.click(selectOption.withText('any ticket'))
  await t.click(Selector('[data-tc-submit]'))
}


export async function newAutomatedTask(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/automated_tasks`)
  await t.click(Selector('[data-tc-add-new-task]'))
  await t.click(Selector('[data-tc-event-name]'))
  await t.typeText(Selector('[data-tc-event-name]'), automatedTask.eventName.name)
}

export async function assignPriorityToAnyForm(t, priority = 'Medium') {
  await t.click(Selector('[data-tc-action-select]'))
  await t.click(selectOption.withText('set a form field'))

  await t.expect(Selector('[data-tc-form-select]').innerText).eql('Any Form')  
  await t
    .click(Selector('[data-tc-field-select]'))
    .click(Selector('[data-tc-field-select].multiselect .multiselect__element').withText('priority'))

  let prioritySelect = Selector('[data-tc-priority-select]')
  let priorityOption = Selector('[data-tc-priority-select-option]')
  await t.click(prioritySelect)
  await t.click(priorityOption.withText(priority))
  await t.click(Selector('[data-tc-submit]'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}


export async function assignPriorityToItGeneralRequest(t, priority = 'Medium') {
  await t.click(Selector('[data-tc-action-select]'))
  await t.click(selectOption.withText('set a form field'))

  await t
    .hover(Selector('[data-tc-form-select]'))
    .click(Selector('[data-tc-form-select]'))
    .click(Selector('[data-tc-form-select].multiselect .multiselect__element').withText("IT General Request"))

  await t
    .click(Selector('[data-tc-field-select]'))
    .click(Selector('[data-tc-field-select].multiselect .multiselect__element').withText('priority'))

  let prioritySelect = Selector('[data-tc-priority-select]')
  let priorityOption = Selector("[data-tc-priority-select-option]")
  await t.click(prioritySelect)
  await t.click(priorityOption.withText(priority))
  await t.click(Selector('[data-tc-submit]'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}

export async function setStatusToAnyForm(t, status = 'Open') {
  await t.click(Selector('[data-tc-action-select]'))
  await t.click(selectOption.withText('set a form field'))

  await t.expect(Selector('[data-tc-form-select]').innerText).eql('Any Form')  

  await t
    .click(Selector('[data-tc-field-select]'))
    .click(Selector('[data-tc-field-select].multiselect .multiselect__element').withText('status'))

  let statusSelect = Selector('[data-tc-ticket-status-dropdown]')
  let statusOption = Selector('[data-tc-status-option]')
  await t.click(statusSelect)
  await t.click(statusOption.withText(status))

  await t.click(Selector('[data-tc-submit]'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}


export async function setStatusToItGeneralRequest(t, status = 'Open') {
  await t.click(Selector('[data-tc-action-select]'))
  await t.click(selectOption.withText('set a form field'))

  await t
    .hover(Selector('[data-tc-form-select]'))
    .click(Selector('[data-tc-form-select]'))
    .click(Selector('[data-tc-form-select].multiselect .multiselect__element').withText("IT General Request")) 

  await t
    .click(Selector('[data-tc-field-select]'))
    .click(Selector('[data-tc-field-select].multiselect .multiselect__element').withText('status'))

  let statusSelect = Selector('[data-tc-ticket-status-dropdown]')
  let statusOption = Selector("[data-tc-status-option]")
  await t.click(statusSelect)
  await t.click(statusOption.withText(status))

  await t.click(Selector('[data-tc-submit]'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}


export async function addCommentAction(t, comment = automatedTask.comment) {
  await t.click(Selector('[data-tc-action-select]'))
  await t.click(selectOption.withText('add a comment'))
  await t.typeText(Selector('trix-editor'), comment)
  await t.click(Selector('[data-tc-submit]'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}

export async function directAssignmentToItGeneralRequest(t) {
  await t.click(Selector('[data-tc-action-select]'))
  await t.click(selectOption.withText('set a form field'))

  await t
    .hover(Selector('[data-tc-form-select]'))
    .click(Selector('[data-tc-form-select]'))
    .click(Selector('[data-tc-form-select].multiselect .multiselect__element').withText("IT General Request"))

  await t
    .click(Selector('[data-tc-field-select]'))
    .click(Selector('[data-tc-field-select].multiselect .multiselect__element').withText('assigned to'))

  await t
    .click(Selector('[data-tc-routing-select]'))
    .click(Selector("[data-tc-routing-select].form-control option[value='Direct Assignment']"))

  await t.click(Selector('[data-tc-select-impacted-user] .multiselect'))
  await t.wait(2000)
  await t.pressKey('down')
  await t.pressKey('enter')
  await t.click(Selector('[data-tc-submit]'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}

export async function directAssignmentToAnyForm(t) {
  await t.click(Selector('[data-tc-action-select]'))
  await t.click(selectOption.withText('set a form field'))

  await t.expect(Selector('[data-tc-form-select]').innerText).eql('Any Form')  

  await t
    .click(Selector('[data-tc-field-select]'))
    .click(Selector('[data-tc-field-select].multiselect .multiselect__element').withText('assigned to'))

  await t
    .click(Selector('[data-tc-routing-select]'))
    .click(Selector("[data-tc-routing-select].form-control option[value='Direct Assignment']"))

  await t.click(Selector('[data-tc-select-impacted-user] .multiselect'))
  await t.wait(2000)
  await t.pressKey('down')
  await t.pressKey('enter')
  await t.click(Selector('[data-tc-submit]'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}


export async function subjectWithTextDetail(t, text = automatedTask.subjectContainsText) {
  await t.click(Selector('[data-tc-event-select]'))
  await t.click(selectOption.withText('a ticket is created'))

  await t.click(Selector('[data-tc-event-detail]'))
  await t.click(selectOption.withText('a ticket with a form field'))

  await t
    .hover(Selector('[data-tc-form-select]'))
    .click(Selector('[data-tc-form-select]'))
    .click(Selector('[data-tc-form-select].multiselect .multiselect__element').withText("IT General Request"))

  await t
    .click(Selector('[data-tc-field-select]'))
    .click(Selector('[data-tc-field-select] .multiselect__element').withText(/subject/))

  await t.click(Selector('[data-tc-task-value]'))

  await t.typeText(Selector('[data-tc-task-value]'), text)
  await t.click(Selector('[data-tc-any-value]'))
  await t.click(Selector('[data-tc-submit]'))
}


export async function subjectWithoutTextDetail(t, text = automatedTask.subjectDoesNotContainText) {
  await t.click(Selector('[data-tc-event-select]'))
  await t.click(selectOption.withText('a ticket is created'))
  await t.click(Selector('[data-tc-event-detail]'))
  await t.click(selectOption.withText('a ticket with a form field'))

  await t
    .hover(Selector('[data-tc-form-select]'))
    .click(Selector('[data-tc-form-select]'))
    .click(Selector('[data-tc-form-select].multiselect .multiselect__element').withText("IT General Request"))

  await t
    .click(Selector('[data-tc-field-select]'))
    .click(Selector('[data-tc-field-select] .multiselect__element').withText(/subject/))

    await t.typeText(Selector('[data-tc-task-value]'), text)
    await t.click(Selector('[data-tc-negate-value]'))
    await t.click(Selector('[data-tc-submit]'))
}


export async function cleanupTasks(t) {
  let taskItems
  let taskCount
  let modal
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/automated_tasks`)
  taskItems = await Selector('[data-tc-delete]')
  taskCount = await taskItems.count
  while (taskCount > 0) {
    await t
      .hover(taskItems.nth(0).child('.nulodgicon-trash-b'))
      .click(taskItems.nth(0).child('.nulodgicon-trash-b'))
    modal = await Selector('.sweet-modal.is-visible')
    await t
      .hover(modal.find('[data-tc-i-am-sure]'))
      .click(modal.find('[data-tc-i-am-sure]'))
    await t.expect(Selector('.notification-title').innerText).eql('Success')
    await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/help_tickets/automated_tasks`)
    taskItems = await Selector('[data-tc-delete]')
    taskCount = await taskItems.count
  }
}


export async function sendEmailAction(t, subject = automatedTask.emailVar.subject, body = automatedTask.emailVar.body) {
  await t.click(Selector('[data-tc-action-select]'))
  await t.click(selectOption.withText('send an email'))
  await t.click('[data-tc-submit]')

  let targetOption = Selector('[data-tc-target-option]')
  await t.click(targetOption.withText("Agent Assigned"))
  await t.typeText(Selector('[data-tc-subject]'), subject)
  await t.typeText(Selector('trix-editor'), body)
  await t.click(Selector('[data-tc-submit]'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}
