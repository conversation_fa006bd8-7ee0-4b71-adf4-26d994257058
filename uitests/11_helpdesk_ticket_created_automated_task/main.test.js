import {
  emailSettingsWithAutomatedTasks,
  emailSettingsWithoutAutomatedTasks,
} from './methods/email_notifications.js'

import {
  anyTicketSendAnEmail,
  editAnyTicketSendAnEmail,
  deleteAnyTicketSendAnEmail,
  anyTicketAssignPriority,
  anyTicketSetStatus,
  anyTicketAddComment,
  anyTicketAssignAgent,
} from './methods/any_ticket.js'

import {
  ticketWithSubjectAnySubjectSendEmail,
  ticketWithSubjectAnySubjectAssignAgent,
  ticketWithSubjectSubjectContainsTextAssignAgent,
  ticketWithSubjectAnySubjectAssignPriority,
  ticketWithSubjectAnySubjectSetStatus,
  ticketWithSubjectAnySubjectAddComment,
  ticketWithSubjectSubjectContainsTextSendEmail,
  ticketWithSubjectSubjectContainsTextAssignPriority,
  ticketWithSubjectSubjectContainsTextSetStatus,
  ticketWithSubjectSubjectContainsTextAddComment,
  ticketWithSubjectSubjectDoesNotContainTextAssignAgent,
  ticketWithSubjectSubjectDoesNotContainTextSendEmail,
  ticketWithSubjectSubjectDoesNotContainTextAssignPriority,
  ticketWithSubjectSubjectDoesNotContainTextSetStatus,
  ticketWithSubjectSubjectDoesNotContainTextAddComment,
} from './methods/ticket_with_subject.js'

fixture('Usecase: Automated Tasks')

test('Link email settings to automated tasks', async t => {
  await emailSettingsWithAutomatedTasks(t);
})

test('Creates a task from email settings', async t => {
  await emailSettingsWithoutAutomatedTasks(t);
})

//Any Ticket
test('Create a ticket => Any Ticket => Send An Email', async t => {
  await anyTicketSendAnEmail(t);
})

test('Edit => Create a ticket => Any Ticket => Send An Email', async t => {
  await editAnyTicketSendAnEmail(t);
})

test('Delete => Create a ticket => Any Ticket => Send An Email', async t => {
  await deleteAnyTicketSendAnEmail(t);
})

test('Create a ticket => Any Ticket => Assign A Priority', async t => {
  await anyTicketAssignPriority(t);
})

test('Create a ticket => Any Ticket => Set Status For A Status', async t => {
  await anyTicketSetStatus(t);
})

test('Create a ticket => Any Ticket => Add A comment', async t => {
  await anyTicketAddComment(t);
})

test('Create a ticket => Any Ticket => Assign An Agent', async t => {
  await anyTicketAssignAgent(t);
})

//Any Subject
test('Create a ticket => Any Subject =>  Send An email', async t => {
  await ticketWithSubjectAnySubjectSendEmail(t);
})

test('Create a ticket => Any Subject =>  Assign Priority', async t => {
  await ticketWithSubjectAnySubjectAssignPriority(t);
})

test('Create a ticket => Any Subject =>  Set Status', async t => {
  await ticketWithSubjectAnySubjectSetStatus(t);
})

test('Create a ticket => Any Subject =>  Add Comment', async t => {
  await ticketWithSubjectAnySubjectAddComment(t);
})

test('Create a ticket => Any Subject =>  Assign An Agent', async t => {
  await ticketWithSubjectAnySubjectAssignAgent(t);
})

//Subject Contains Text
test('Create a ticket => Subject Contains Text =>  Send An Email', async t => {
  await ticketWithSubjectSubjectContainsTextSendEmail(t);
})

test('Create a ticket => Subject Contains Text =>  Assign A Priority', async t => {
  await ticketWithSubjectSubjectContainsTextAssignPriority(t);
})

test('Create a ticket => Subject Contains Text =>  Set Status For A Ticket', async t => {
  await ticketWithSubjectSubjectContainsTextSetStatus(t);
})

test('Create a ticket => Subject Contains Text =>  Add A Comment', async t => {
  await ticketWithSubjectSubjectContainsTextAddComment(t);
})

test('Create a ticket => Subject Contains Text =>  Assign An Agent', async t => {
  await ticketWithSubjectSubjectContainsTextAssignAgent(t);
})

//Subject Does Not Contain Text
test('Create a ticket => Subject Does Not Contain Text =>  Send An Email', async t => {
  await ticketWithSubjectSubjectDoesNotContainTextSendEmail(t);
})

test('Create a ticket => Subject Does Not Contain Text =>  Assign A Priority', async t => {
  await ticketWithSubjectSubjectDoesNotContainTextAssignPriority(t);
})

test('Create a ticket => Subject Does Not Contain Text =>  Set Status For A Ticket', async t => {
  await ticketWithSubjectSubjectDoesNotContainTextSetStatus(t);
})

test('Create a ticket => Subject Does Not Contain Text =>  Add A Comment', async t => {
  await ticketWithSubjectSubjectDoesNotContainTextAddComment(t);
})

test('Create a ticket => Subject Does Not Contain Text =>  Assign An Agent', async t => {
  await ticketWithSubjectSubjectDoesNotContainTextAssignAgent(t);
})
