import common from '../../common/helpers/common.js'

var emailVar =
{
  recepient   : common.faker.internet.email().toLowerCase(),
  subject       : common.faker.lorem.sentence(),
  body       : common.faker.lorem.sentence(),
}

var updatedEmailVar =
{
  recepient   : common.faker.internet.email().toLowerCase(),
  subject       : common.faker.lorem.sentence(),
  body       : common.faker.lorem.sentence(),
}

var comment = common.faker.lorem.sentence()
var subjectContainsText = common.faker.lorem.word()
var subjectEqualText = common.faker.lorem.word()
var subjectDoesNotEqualText = common.faker.lorem.word()
var subjectDoesNotContainText = common.faker.lorem.word()

var ticket =
{
  subject: common.faker.lorem.words(),
  subject1: common.faker.lorem.words(),
  subject2: common.faker.lorem.words(),
  subject3: common.faker.lorem.words(),
}

var eventName = 
{
  name: 'When any ticket perform action send an email'
}

export default {
  emailVar,
  updatedEmailVar,
  comment,
  subjectContainsText,
  subjectEqualText,
  subjectDoesNotEqualText,
  ticket,
  subjectDoesNotContainText,
  eventName,
}
