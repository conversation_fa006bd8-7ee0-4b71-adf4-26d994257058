import { createFixedTermContract, createOpenEndedContract, contractFormValidation, updateContract, archiveContract, unarchiveContract, deleteContract } from './methods/crud.js'
import { createLocation } from './methods/location.js'

fixture('Usecase: Contract')

test('Create Location form contract module', async t => {
  await createLocation(t);
})

test('Contract form validation', async t => {
  await contractFormValidation(t);
})

test('Create open ended contract', async t => {
  await createOpenEndedContract(t);
})

test('Create fixed term contract', async t => {
  await createFixedTermContract(t);
})

test('Update contract', async t => {
  await updateContract(t);
})

test('Archive contract', async t => {
  await archiveContract(t);
})

test('Unarchive contract', async t => {
  await unarchiveContract(t);
})

test('Delete contract', async t => {
  await archiveContract(t);
  await deleteContract(t);
})
