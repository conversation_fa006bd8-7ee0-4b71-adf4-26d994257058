import common from '../../common/helpers/common.js'

var contractParams =
{
  name          : common.faker.lorem.words(),
  monthlyName   : common.faker.lorem.words() + " Monthly",
  contractType  : "Cloud",
  impact        : 'High',
  manufacturer  : common.faker.company.companyName(),
  model         : common.faker.lorem.slug(),
  notes         : common.faker.lorem.sentence(),
  userBy        : common.faker.name.findName(),
  managedBy     : common.faker.name.findName(),
  location      : common.faker.lorem.words(),
  department    : common.faker.name.jobArea(),
  transactionAmount: "234567",
  vendor : {
    name: common.faker.lorem.words()
  }
}

var locationParams =
{
  name            : common.faker.lorem.word(),
  country         : common.faker.address.country(),
  address         : common.faker.address.streetAddress(),
  addressLine2    : common.faker.address.streetName(),
  city            : common.faker.address.city(),
  state           : common.faker.address.state(),
  zip             : common.faker.address.zipCode(),
}

export default {
  contractParams,
  locationParams,
}
