import common from '../../common/helpers/common.js'
import { Selector } from 'testcafe'
import contract from '../helpers/contract_variables.js'
import faker from 'faker'

const selectCategory = Selector('[data-tc-category-id]');
const updatedContractName = faker.random.words(2);
const categoryOptions = selectCategory.find('option');

export async function contractFormValidation(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/contracts/new`)
  await t.click('[data-tc-save-form-btn="Save Contract"]')
  await t.expect(Selector('.notification-content').innerText).eql('Please correct the highlighted errors before submitting.')
  await t.click('[data-tc-vendor-field]')
    .typeText('[data-tc-multi-select-vendor]', contract.contractParams.vendor.name)
    .pressKey("enter")
  await t.click('[data-tc-save-form-btn="Save Contract"]')
  await t.expect(Selector('.notification-content').innerText).eql('Please correct the highlighted errors before submitting.')
  await t.typeText('[data-tc-contract-name]', contract.contractParams.name)
  await t.click('[data-tc-save-form-btn="Save Contract"]')
  await t.expect(Selector('.notification-content').innerText).eql('Please correct the highlighted errors before submitting.')
  await t.click('[data-tc-fixed-term]')
  await t.click('[data-tc-save-form-btn="Save Contract"]')
  await t.click('[data-tc-by-monthly-cost]')
  await t.typeText('[data-tc-currency]', contract.contractParams.transactionAmount)
  await t.expect(Selector('[data-tc-end-date-error]').innerText).eql('The End Date field is required')
  await t.click('[data-tc-end-date]')
  await t.click('[data-tc-end-date] .mx-icon-next-year')
  await t.click(Selector('[data-tc-end-date] td.cell.cur-month').withText('27'))
  await t.click('[data-tc-save-form-btn="Save Contract"]')
  await t.expect(Selector('[data-tc-start-date-error]').innerText).eql('The Start Date field is required')
  await t.click('[data-tc-start-date]')
  await t.click(Selector('[data-tc-start-date] td.cell.cur-month').withText('27'))
  await t.click('[data-tc-save-form-btn="Save Contract"]')
  await t.expect(Selector('.notification-content').innerText).eql('Sorry, there was an error saving this contract. Alert dates should have at least one alert before the end date')

  await t.click('[data-tc-alert-date-div]')
  await t.click('[data-tc-alert-date-div] .mx-icon-next-year')
  await t.click(Selector('[data-tc-alert-date-div] td.cell.cur-month').withText('28'))
  await t.expect(Selector('.notification-content').innerText).eql('Alert date should be between start date and end date.')
  await t.click('[data-tc-alert-date]')
  await t.click('[data-tc-alert-date] .mx-icon-last-year')
  await t.click(Selector('[data-tc-alert-date-div] td.cell.cur-month').withText('1'))
  await t.expect(Selector('.notification-content').innerText).eql('Alert date should be between start date and end date.')
}

export async function createFixedTermContract(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/contracts/new`)
  await t.click('.multiselect__tags')
    .typeText('[data-tc-multi-select-vendor]', contract.contractParams.vendor.name)
    .pressKey("enter")
  await t.typeText('[data-tc-contract-name]', contract.contractParams.name)
  await t.click('[data-tc-fixed-term]')
  await t.click('[data-tc-start-date]')
  await t.click(Selector('[data-tc-start-date] td.cell.cur-month').withText('27'))

  await t.click('[data-tc-end-date]')

  for (let i = 0; i < 12; i++) {
    await t.click('[data-tc-end-date] .mx-icon-next-year')
  }

  await t.click(Selector('[data-tc-end-date] td.cell.cur-month').withText('27'))
  await t.click('[data-tc-end-date]')
  await t.click('[data-tc-end-date] .mx-icon-next-year')
  await t.click(Selector('[data-tc-end-date] td.cell.cur-month').withText('27'))

  var strStartDate = (await Selector('input[name="contract[start_date]"]').value)
  var numbers = strStartDate.match(/\d+/g);
  var startDate = new Date(numbers[0], numbers[1]-1,  numbers[2]);

  var strEndDate = (await Selector('input[name="contract[end_date]"]').value)
  var numbers = strEndDate.match(/\d+/g);
  var endDate = new Date(numbers[0], numbers[1]-1,  numbers[2]);

  var strAlertDate0 = (await Selector('input[name="contract[alert_dates_attributes][0][date]"]').value)
  var numbers = strAlertDate0.match(/\d+/g);
  var alertDate0 = new Date(numbers[0], numbers[1]-1,  numbers[2]);

  var strAlertDate1 = (await Selector('input[name="contract[alert_dates_attributes][1][date]"]').value)
  var numbers = strAlertDate1.match(/\d+/g);
  var alertDate1 = new Date(numbers[0], numbers[1]-1,  numbers[2]);

  var noticePeriod = (await Selector('#contract_notice_period').value)

  var DifferenceInTime0 = endDate.getTime() - alertDate0.getTime();
  var alertDays0 = Math.floor(DifferenceInTime0 / (1000 * 3600 * 24));

  var DifferenceInTime1 = endDate.getTime() - alertDate1.getTime();
  var alertDays1 = Math.floor(DifferenceInTime1 / (1000 * 3600 * 24));

  await t.expect(noticePeriod).eql("60 days")
  await t.expect(alertDays0).eql(120)
  await t.expect(alertDays1).eql(90)

  await t.click('.remove-alert')
  await t.click('[data-tc-add-alert-date-btn]')

  await t.click('input[name="contract[alert_dates_attributes][1][date]"]')
  await t.click(Selector('[data-tc-alert-date] .mx-icon-last-year').nth(1))
  await t.click(Selector('[data-tc-alert-date] td.cell.cur-month').withText('27').nth(1))
  await t.expect(Selector('.notification-title').innerText).eql('Error')
  await t.click('[data-tc-add-alert-date-btn]')
  await t.click('input[name="contract[alert_dates_attributes][1][date]"]')

  await t.click(Selector('[data-tc-alert-date] .mx-icon-next-year').nth(1))
  await t.click(Selector('[data-tc-alert-date] .mx-icon-next-year').nth(1))
  await t.click(Selector('[data-tc-alert-date] td.cell.cur-month').withText('27').nth(1))
  // await t.expect(Selector('.notification-title').innerText).eql('Error')

  await t.click('input[name="contract[alert_dates_attributes][1][date]"]')
  await t.click(Selector('[data-tc-alert-date] .mx-icon-next-month').nth(1))
  await t.click(Selector('[data-tc-alert-date] .mx-icon-next-month').nth(1))
  await t.click(Selector('[data-tc-alert-date] td.cell.cur-month').withText('27').nth(1))

  await t.click('[data-tc-by-monthly-cost]')
  await t.typeText('[data-tc-currency]', contract.contractParams.transactionAmount)
  await t
    .click(selectCategory)
    .click(categoryOptions.withText(contract.contractParams.contractType))
  await t.pressKey('tab')
  await t.pressKey('tab')
  await t.pressKey('down')
  await t.pressKey('down')
  await t.pressKey('enter')
  await t.typeText("[data-tc-contract-notes]", contract.contractParams.notes)
  await t.click('[data-tc-save-form-btn="Save Contract"]')
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}

export async function createOpenEndedContract(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/contracts/new`)
  await t.click('.multiselect__tags')
    .typeText('[data-tc-multi-select-vendor]', contract.contractParams.vendor.name)
    .pressKey("enter")
  await t.typeText('[data-tc-contract-name]', contract.contractParams.monthlyName)
  await t.click('[data-tc-open-ended]')
  await t.click('[data-tc-start-date]')
  await t.click(Selector('[data-tc-start-date] td.cell.cur-month').withText('27'))
  await t.typeText('[data-tc-currency]', contract.contractParams.transactionAmount)
  await t
    .click(selectCategory)
    .click(categoryOptions.withText(contract.contractParams.contractType))
  await t.pressKey('tab')
  await t.pressKey('tab')
  await t.pressKey('down')
  await t.pressKey('down')
  await t.pressKey('enter')
  await t.typeText("[data-tc-contract-notes]", contract.contractParams.notes)
  await t.click('[data-tc-save-form-btn="Save Contract"]')
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}

export async function updateContract(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/contracts/all`)
  await t.typeText('[data-tc-search-contract-field]', contract.contractParams.name)
  await t.click(Selector('a.box.box--with-hover div.box__inner h6').withText(contract.contractParams.name))
  await t.click('a.text-secondary.mr-3.edit-delete-btn.edit')
  await t.selectText('[data-tc-contract-name]').pressKey('delete')
  await t.typeText('[data-tc-contract-name]', updatedContractName)
  
  await t.click(Selector(`[data-tc-remove-alert='0']`))
  
  await t.click('input[name="contract[alert_dates_attributes][1][date]"]')
  await t.click(Selector('[data-tc-alert-date] .mx-icon-last-month'))
  await t.click(Selector('[data-tc-alert-date] .mx-icon-last-month'))
  await t.click(Selector('[data-tc-alert-date] td.cell.cur-month').withText('27'))

  await t.click('[data-tc-save-form-btn="Save Contract"]')
  contract.contractParams.name = updatedContractName
  await t.expect(Selector('[data-tc-contract-headline]').innerText).eql(updatedContractName)
}

export async function archiveContract(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/contracts/all`)
  await t.typeText('[data-tc-search-contract-field]', contract.contractParams.name)
  await t.click(Selector('a.box.box--with-hover div.box__inner h6').withText(contract.contractParams.name))
  await t.click('a.text-secondary.edit-delete-btn.archive')
  await t.click('.sweet-buttons button.btn.btn-link.text-danger:nth-child(2)')
  await t.expect(Selector(".notification-content").innerText).eql("Contract was successfully archived.")
}

export async function unarchiveContract(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/contracts/all`)
  await t.click(Selector('[data-tc-filter-menu-button]'))
  await t.click(Selector('a.dropdown-toggle.btn.btn-text.text-secondary.btn-sm span').withText('Status:'))
  await t.click(Selector('a.dropdown-item').withText('Archived'))
  await t.click('a.box.box--with-hover')
  await t.click('.text-secondary.edit-delete-btn.unarchive')
  await t.click(Selector('button.btn.btn-link.text-success').withText('Unarchive'))
  await t.expect(Selector(".notification-content").innerText).eql("Contract was successfully unarchived.")
}

export async function deleteContract(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/contracts/all`)
  await t.click(Selector('[data-tc-filter-menu-button]'))
  await t.click(Selector('a.dropdown-toggle.btn.btn-text.text-secondary.btn-sm span').withText('Status:'))
  await t.click('a.dropdown-item:nth-child(7)')
  await t.click('a.box.box--with-hover')
  await t.click('a.text-secondary.edit-delete-btn.delete.ml-3')
  await t.click('button.btn.btn-link.text-danger')
  await t.expect(Selector(".notification-content").innerText).eql("Contract was successfully removed.")
}
