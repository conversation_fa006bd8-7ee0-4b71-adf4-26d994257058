import common from '../../common/helpers/common.js'
import { Selector, Role, ClientFunction } from 'testcafe'
import contract from '../helpers/contract_variables.js'

const countryField = Selector('[data-tc="country_name"]');
const countryFieldOption = countryField.find('span');

export async function createLocation(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/contracts/new`)
  await t.click('[data-tc-add-new-location]')
  await t.typeText(Selector('[data-tc="name"]'), contract.locationParams.name)
  await t.typeText(Selector('[data-tc="address"]'), contract.locationParams.address)
  await t.typeText(Selector('[data-tc="city"]'), contract.locationParams.city)
  await t.typeText(Selector('[data-tc="zip"]'), contract.locationParams.zip)
  await t.typeText(Selector('[data-tc="address_2"]'), contract.locationParams.addressLine2)
  await t
    .hover(countryField)
    .click(countryField)
    .hover(countryFieldOption.withText("United States"))
    .click(countryFieldOption.withText("United States"))

  await t.click(Selector('[data-tc-create="location"]'))
}
