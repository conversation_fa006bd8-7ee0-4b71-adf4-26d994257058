import common from '../../common/helpers/common.js'

var fakeCompanyName = common.faker.company.companyName(0)

var editCompanyParams =
{
  companyName   : fakeCompanyName,
  subdomain     : fakeCompanyName.toLowerCase().split(' ').join('-'),
  companyUrl    : 'http://'+fakeCompanyName.toLowerCase().split(' ').join('')+'.com',
  mainContact   : '<EMAIL>',
  mainPhoneNumber: common.faker.random.number({min:1000000000, max:9999999999})
}

var companyParams =
{
  firstName     : common.faker.name.firstName(),
  lastName      : common.faker.name.lastName(),
  email         : common.faker.internet.email(),
  mobilePhone   : "2015550123",
  workPhone     : "2015550123",
  extension     : "1234",
  title         : common.faker.lorem.words(),
  department    : common.faker.name.jobArea(),
  location      : "HQ",
  timeZone      : "Kabul",
  supervisor    : "testcafe",
}

var updatedCompanyParams =
{
  firstName     : common.faker.name.firstName(),
  lastName      : common.faker.name.lastName(),
  email         : common.faker.internet.email(),
  mobilePhone   : "2015550123",
  workPhone     : "2015550123",
  extension     : "5678",
  title         : common.faker.lorem.words(),
  department    : common.faker.name.jobArea(),
  location      : "HQ",
  timeZone      : "Paris",
  supervisor    : "",
}

var locationParams =
{
  name            : common.faker.lorem.word(),
  country         : common.faker.address.country(),
  address         : common.faker.address.streetAddress(),
  addressLine2    : common.faker.address.streetName(),
  city            : common.faker.address.city(),
  state           : common.faker.address.state(),
  zip             : common.faker.address.zipCode().substring(0, 5),
  notes           : common.faker.address.streetAddress(),
  phoneNumber     : "2015550123",
}

export default {
  editCompanyParams,
  companyParams,
  updatedCompanyParams,
  locationParams,
}
