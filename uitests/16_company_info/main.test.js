import { editCompanyProfile, createNewParentCompany, createCompanyUser, archiveCompanyUser, inviteCompanyUser, createCompanyLocation, unarchiveCompanyUser } from './methods/crud.js'
import { addNewCategory, removeCategory, addAdmin } from './methods/company_settings.js'

fixture('Usecase: Company Information')

test('Edit company profile', async t => {
  await editCompanyProfile(t);
})

test('Create company user', async t => {
  await createCompanyUser(t);
})

test('Archive User', async t => {
  await archiveCompanyUser(t)
})

test('Unarchive User', async t => {
  await unarchiveCompanyUser(t)
})

test('Invite User', async t => {
  await inviteCompanyUser(t)
})

test('Create company location', async t => {
  await createCompanyLocation(t)
})

test('Add new category', async t => {
  await addNewCategory(t);
})

test('Remove category', async t => {
  await removeCategory(t);
})

test('Create new parent company', async t => {
  await createNewParentCompany(t);
})
