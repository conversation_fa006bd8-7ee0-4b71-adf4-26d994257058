import common from '../../common/helpers/common.js'
import { Selector, Role, ClientFunction } from 'testcafe'
import company from '../helpers/company_user_variables.js'
import faker from 'faker'

const categoryName = faker.name.findName(0)

export async function addNewCategory(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/company/categories`)

  await t.typeText('input[data-tc-new-category-name]', categoryName)
  await t.click('button[data-tc-save-form-btn="Save Category"]')
  await t.expect(Selector('.notification-content').innerText).eql(`Successfully added ${categoryName} to your categories`)
}

export async function removeCategory(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/company/categories`)
  const category = await Selector('[data-tc-category-name]').innerText
  await t.click('[data-tc-category-name] .genuicon-minus-circle')

  await t.expect(Selector('.notification-content').innerText).eql(`Successfully removed ${category} from your categories`)
}
