import common from '../../common/helpers/common.js'
import { Selector } from 'testcafe'
import company from '../helpers/company_user_variables.js'

const countryField = Selector('[data-tc="country_name"]');;
const countryFieldOption = countryField.find('span');
const stateField = Selector('[data-tc="state"]');
const stateFieldOption = stateField.find('span');

export async function editCompanyProfile(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/company/overview`)
  await t.click('[data-tc-company-edit]')
  await t.selectText('input[data-tc-name]').pressKey('delete')
  await t.typeText('input[data-tc-name]', company.editCompanyParams.companyName)
  await t.selectText('input[name="subdomain"]').pressKey('delete')
  await t.typeText('input[name="subdomain"]', company.editCompanyParams.subdomain)
  await t.selectText('input[name="url"]').pressKey('delete')
  await t.typeText('input[name="url"]', company.editCompanyParams.companyUrl)
  await t.selectText('input[data-tc-email]').pressKey('delete')
  await t.typeText('input[data-tc-email]', company.editCompanyParams.mainContact)
  await t.selectText('input[id="phoneNumber_phone_number"]').pressKey('delete')
  await t.typeText('input[id="phoneNumber_phone_number"]', (`2015550123`))
  const phoneNumber = await Selector('input[id="phoneNumber_phone_number"]').value
  await t.click('[data-tc-timezone]')
  await t.click('[data-tc-timezone] option[value="Etc/UTC"]')
  const timezone = 'UTC'
  await t.click('[data-tc-fiscal-month]')
  await t.click('option[value="11"]')
  await t.click('[data-tc-day]')
  await t.click(Selector('option[value="03"]').withExactText('03'))
  await t.click('[data-tc-save-form-btn="Save company"]')
  await t.click('[data-tc-btn-confirm-subdomain-change]')
  common.user.company.name = company.editCompanyParams.companyName
  common.user.company.subdomain = company.editCompanyParams.subdomain

  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/company/overview`)
  await t.expect(Selector('[data-tc-company-name]').innerText).eql(company.editCompanyParams.companyName)
  await t.expect(Selector('[data-tc-subdomain]').innerText).contains(company.editCompanyParams.subdomain, 'String contains subdomain')
  await t.expect(Selector('[data-tc-main-contact]').innerText).eql(company.editCompanyParams.mainContact)
  await t.expect(Selector('[data-tc-main-phone-number]').innerText).eql(phoneNumber)
  await t.expect(Selector('[data-tc-timezone]').innerText).eql(timezone)
}

export async function createNewParentCompany(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/company/new`)
  await t.typeText('input[data-tc-name]', common.company2.name)
  await t.typeText('form input[data-tc-subdomain]', common.company2.subdomain)

  await t.wait(2000)
  await t.click('[data-tc-save-form-btn="Save"]')
  await t.wait(1000)
  await t.click('[data-tc-confirm-save-btn]')

}

export async function createCompanyUser(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/company/users`)
  await t.click('[data-tc-add-staff-btn]')
  await t.typeText(Selector('[data-tc="first_name"]'), company.companyParams.firstName)
  await t.typeText(Selector('[data-tc="last_name"]'), company.companyParams.lastName)
  await t.typeText(Selector('[data-tc="email"]'), company.companyParams.email)
  await t.typeText(Selector('[data-tc="title"]'), company.companyParams.title)
  await t.typeText(Selector('[data-tc="department"]'), company.companyParams.department)
  await t.click(Selector('[data-tc-create="staff member"]'))
}

export async function archiveCompanyUser(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/company/users`)
  await t.typeText('input[data-tc-company-user-search]', company.companyParams.firstName)
  await t.click(Selector('[data-tc-user]').withText(company.companyParams.firstName))
  await t
    .hover(Selector('[data-tc-archive-user-btn]'))
    .click(Selector('[data-tc-archive-user-btn]'))
  if (await Selector('[data-tc-archive-user-modal-btn]').visible) {
    await t.click(Selector('[data-tc-archive-user-modal-btn]'))
    await t.expect(Selector('.notification-title').innerText).eql('Success')
    await t.expect(Selector('.notification-content').innerText).eql("User successfully archived")
  } else {
    await t.click(Selector('[data-tc-archive-user-btn]'))
    await t.click(Selector('[data-tc-archive-user-modal-btn]'))
    await t.expect(Selector('.notification-title').innerText).eql('Success')
    await t.expect(Selector('.notification-content').innerText).eql("User successfully archived")
  }
}

export async function unarchiveCompanyUser(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/company/users`)
  await t.click(Selector('[data-tc-company-user-status-id]'))
  await t.click(Selector('[data-tc-company-user-status-id] div a').withText('Archived'))
  await t.typeText('input[data-tc-company-user-search]', company.companyParams.firstName)
  await t.click(Selector('[data-tc-user]').withText(company.companyParams.firstName))
  await t
    .hover(Selector('[data-tc-un-archive-user-btn]'))
    .click(Selector('[data-tc-un-archive-user-btn]'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}

export async function inviteCompanyUser(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/company/users`)
  await t.typeText('input[data-tc-company-user-search]', company.companyParams.firstName)
  await t.click(Selector('[data-tc-user-invite-id]'))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
  await t.expect(Selector('.notification-content').innerText).eql('User invited successfully')
}

export async function createCompanyLocation(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/company/locations`)
  await t.click(Selector("[data-tc-add='location']"))
  await t.expect(common.getPageUrl()).contains('new')

  await t.typeText(Selector('[data-tc="name"]'), company.locationParams.name)
  await t.typeText(Selector('[data-tc="address"]'), company.locationParams.address)
  await t.typeText(Selector('[data-tc="city"]'), company.locationParams.city)
  await t.typeText(Selector('[data-tc="zip"]'), company.locationParams.zip)
  await t.typeText(Selector('[data-tc="address_2"]'), company.locationParams.addressLine2)
  await t
    .hover(stateField)
    .click(stateField)
    .hover(stateFieldOption.withText("CO"))
    .click(stateFieldOption.withText("CO"))
  await t
    .hover(countryField)
    .click(countryField)
    .hover(countryFieldOption.withText("United States"))
    .click(countryFieldOption.withText("United States"))

  await t.click(Selector('[data-tc-create="location"]'))
}
