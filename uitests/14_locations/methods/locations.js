import common from '../../common/helpers/common.js'
import { Selector, Role, ClientFunction } from 'testcafe'
import loca from '../helpers/location_variables.js'

const countryField = Selector('[data-tc="country_name"]');;
const countryFieldOption = countryField.find('span');
const stateField = Selector('[data-tc="state"]');
const stateFieldOption = stateField.find('span');

export async function createLocation(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/company/locations`)
  await t.click(Selector("[data-tc-add='location']"))
  await t.expect(common.getPageUrl()).contains('new')

  await t.typeText(Selector('[data-tc="name"]'), loca.location.name)
  await t.typeText(Selector('[data-tc="address"]'), loca.location.address1)
  await t.typeText(Selector('[data-tc="city"]'), loca.location.city)
  await t.typeText(Selector('[data-tc="zip"]'), loca.location.zip)
  await t.typeText(Selector('[data-tc="address_2"]'), loca.location.address2)
  await t
    .hover(stateField)
    .click(stateField)
    .hover(stateFieldOption.withText("CO"))
    .click(stateFieldOption.withText("CO"))
  await t
    .hover(countryField)
    .click(countryField)
    .hover(countryFieldOption.withText("United States"))
    .click(countryFieldOption.withText("United States"))

  await t.typeText(Selector('[data-tc="notes"]'), loca.location.note)
  await t.hover('[data-tc="location_avatar"]')
  await t.click('[data-tc="location_avatar"]')
  await t
    .setFilesToUpload('.drop__file-input', '../../common/files/image2.png')
  await t.click(Selector('[data-tc-create="location"]'))
}

export async function editLocation(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/company/locations`)
  await t
    .hover('[data-tc-loc-item]')
    .click('[data-tc-loc-item]')
  await t
    .hover('[data-tc-location-edit]')
    .click('[data-tc-location-edit]')
  await t.selectText('[data-tc-location-name]')
  await t.typeText('[data-tc-location-name]', loca.location.name)
  await t.selectText('[data-tc-location-address]')
  await t.typeText('[data-tc-location-address]',loca.location.address1)
  await t.selectText('[data-tc-location-address-two]')
  await t.typeText('[data-tc-location-address-two]',loca.location.address2)
  await t.selectText('[data-tc-location-city]')
  await t.typeText('[data-tc-location-city]',loca.location.city)
  await t.selectText('[data-tc-location-state]')
  await t.typeText('[data-tc-location-state]',loca.location.state)
  await t.selectText('[data-tc-location-zip]')
  await t.typeText('[data-tc-location-zip]',loca.location.zip)
  await t
    .hover(countryField)
    .click(countryField)
    .hover(countryFieldOption.withText("Argentina"))
    .click(countryFieldOption.withText("Argentina"))
  await t.selectText('#locationPhone_phone_number')
  await t.typeText('#locationPhone_phone_number', "02456 54-6544")
  await t.typeText('[data-tc-location-notes]',loca.location.note)
  await t
    .hover('[data-tc-attachment-image]')
    .click('[data-tc-attachment-image]')
  await t
    .setFilesToUpload('.drop__file-input', '../../common/files/image.png')
  await t
    .hover('[data-tc-save-location-btn]')
    .click('[data-tc-save-location-btn]')
  await t.expect(Selector('[data-tc-loc-item]').exists).ok()
}

export async function deleteLocation(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/company/locations`)
  await t
    .hover('[data-tc-loc-item]')
    .click('[data-tc-loc-item]')
  await t
    .hover('[data-tc-location-delete]')
    .click('[data-tc-location-delete]')
  await t
    .hover('[data-tc-location-cancel]')
    .click('[data-tc-location-cancel]')
  await t
    .hover('[data-tc-location-delete]')
    .click('[data-tc-location-delete]')
  await t
    .hover('[data-tc-location-cdelete]')
    .click('[data-tc-location-cdelete]')
  await t.expect(Selector(".notification-title").innerText).eql("Success")
}
