import common from '../../common/helpers/common.js'
import dotenv from 'dotenv'

dotenv.config()

var vendorParams =
{
  name              : common.faker.lorem.word(),
  url               : common.faker.internet.url(),
  category          : 'HR',
  businessUnit      : common.faker.random.word(),
  shortDescription  : common.faker.lorem.paragraph(),
  accountNumber     : common.faker.finance.account(),
  address           : common.faker.address.streetName(),
  city              : common.faker.address.city(),
  state             : common.faker.address.state(),
  zip               : common.faker.address.zipCode(),
  phoneNumber       : "**********",
  alertPercent      : common.faker.random.number(),
  paymentMethod     : common.faker.random.word(),
  paymentTerm      : common.faker.random.word(),
}

var updateVendorParams =
{
  name              : common.faker.lorem.word(),
  url               : common.faker.internet.url(),
  category          : 'HR',
  shortDescription  : common.faker.lorem.paragraph(),
  accountNumber     : common.faker.finance.account(),
}

var vendorParams2 =
{
  name              : common.faker.lorem.words(),
  url               : common.faker.internet.url(),
  category          : 'HR',
  shortDescription  : common.faker.lorem.paragraph(),
  accountNumber     : common.faker.finance.account(),
}

var transactionParams =
{
  amount            : common.faker.random.number() * 2,
}

var managerParams =
{
  name              : common.faker.lorem.words(),
  firstName         : common.faker.lorem.words(),
  lastName          : common.faker.lorem.words(),
  email             : common.faker.internet.email(),
}

var productParams =
{
  name              : common.faker.lorem.word(),
  total_licenses    : common.faker.random.number() * 2,
}

var updateProductParams =
{
  name              : common.faker.lorem.word(),
  total_licenses    : common.faker.random.number() * 2,
}

var contactParams =
{
  firstName         : common.faker.name.firstName(),
  lastName          : common.faker.name.lastName(),
  email              : common.faker.internet.email(),
  department         : "Sales",
  phoneNumber       : "**********",
}

var oneLoginParams =
{
  clientId         : process.env.ONE_LOGIN_TEST_CLIENT_ID,
  clientSecret     : process.env.ONE_LOGIN_TEST_CLIENT_SECRET,
}

var oktaParams =
{
  organizationName  : process.env.OKTA_TEST_ORGANIZATION,
  token             : process.env.OKTA_TEST_TOKEN,
}

var contractParams =
{
  name          : common.faker.lorem.words(),
  contractType  : "Cloud",
  impact        : 'High',
  manufacturer  : common.faker.company.companyName(),
  model         : common.faker.lorem.slug(),
  notes         : common.faker.lorem.sentence(),
  userBy        : common.faker.name.findName(),
  managedBy     : common.faker.name.findName(),
  location      : common.faker.lorem.words(),
  department    : common.faker.name.jobArea(),
  transactionAmount: "234567",
  vendor : {
    name: common.faker.lorem.words()
  }
}

export default {
  vendorParams,
  vendorParams2,
  updateVendorParams,
  transactionParams,
  productParams,
  updateProductParams,
  contactParams,
  oneLoginParams,
  oktaParams,
  managerParams,
  contractParams
}
