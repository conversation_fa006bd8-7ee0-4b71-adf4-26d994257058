import common from '../../common/helpers/common.js'
import { Selector, Role, ClientFunction } from 'testcafe'
import vendor from '../helpers/vendor_variables.js'

const categorySelect = Selector('[data-tc-category-id]');
const categoryOption = categorySelect.find('option');

export async function createVendorFromDashboard(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/vendors`)
  await t.click('[data-tc-add-vendor]')
  await t
    .typeText("[data-tc-name]", vendor.vendorParams2.name)
    .typeText("[data-tc-vendor-url]", vendor.vendorParams2.url)
    .click(categorySelect)
    .click(categoryOption.withText('IT'))
    .typeText("[data-tc-vendor-description]", vendor.vendorParams2.shortDescription)
    .typeText("[data-tc-vendor-account-number]", vendor.vendorParams2.accountNumber)
    .click("[type='submit']")
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}

