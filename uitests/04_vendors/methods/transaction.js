import common from '../../common/helpers/common.js'
import { Selector, Role, ClientFunction } from 'testcafe'
import vendor from '../helpers/vendor_variables.js'

export async function createVendorTransaction(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/vendors/all`)
  await t.typeText('input[data-tc-search-vendor]', vendor.vendorParams.name)
  await t.click(Selector('[data-tc-vendor-name]').withText(vendor.vendorParams.name))
  await t.click('[data-tc-add-transaction]')
  await t.typeText('[data-tc-currency]', `${vendor.transactionParams.amount}`)
  await t.click('[data-tc-transaction-date]')
  await t.click('tr > td.today')
  await t.click(Selector('.mx-datepicker-btn-confirm'))
  await t.click('[data-tc-save-form-btn="Save transaction"]')
  await t.expect(Selector('[data-tc-transaction-name]')
    .withText(vendor.vendorParams.name).innerText)
    .eql(vendor.vendorParams.name)
}

export async function splitTransaction(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/vendors/all`)
  await t.typeText('input[data-tc-search-vendor]', vendor.vendorParams.name)
  await t.click(Selector('[data-tc-vendor-name]').withText(vendor.vendorParams.name))
  await t.hover('i.nulodgicon-dot-3').click('.genuicon-split.h6.not-as-small')
  await t.selectText('#split-amount-1+input').pressKey("delete");
  await t.typeText('#split-amount-1+input', `${vendor.transactionParams.amount/2}`)
  await t.typeText('#split-amount-2+input', `${vendor.transactionParams.amount/2}`)
  await t.click('div.mr-5.bottom-container')
  await t.click('[data-tc-save-split-transaction]')
  await t.expect(Selector('[data-tc-transaction-amount]')
    .withText(`$ ${(vendor.transactionParams.amount/2)
    .toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,')}`).innerText)
    .eql(`$ ${(vendor.transactionParams.amount/2)
    .toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,')}`)
}

export async function unlinkTransaction(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/vendors/all`)
  await t.typeText('input[data-tc-search-vendor]', vendor.vendorParams.name)
  await t.click(Selector('[data-tc-vendor-name]').withText(vendor.vendorParams.name))
  await t.hover('[data-tc-transtions-list]')
  if (await Selector('.nulodgicon-unlink.h6.mb-0.clickable.mr-1').visible) {
    await t.click('.nulodgicon-unlink.h6.mb-0.clickable.mr-1')
  } else {
    await t.hover(Selector('[data-tc-vendor-name]'))
    await t.hover('[data-tc-transtions-list]')
      .click('.nulodgicon-unlink.h6.mb-0.clickable.mr-1')
  }
  await t.click('[data-tc-unlink-transaction]')
  await t.expect(Selector('.notification-content').innerText).eql('Transactions were successfully unlinked')
}

