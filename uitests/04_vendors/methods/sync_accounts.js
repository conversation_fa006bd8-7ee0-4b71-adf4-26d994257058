import common from '../../common/helpers/common.js'
import { Selector, Role, ClientFunction } from 'testcafe'
import vendor from '../helpers/vendor_variables.js'

const flashMessage = Selector('.notification-title');
const timeout = { timeout: 4500000};

export async function syncOneLogin(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/vendors/sync_accounts`)
  await t.wait(5000)
  await t.hover('[data-tc-one-login-info]')
  await t.click('[data-tc-one-login-info] [data-tc-sync-btn]')
  await t.typeText('[data-tc-client-id]', vendor.oneLoginParams.clientId)
  await t.typeText('[data-tc-client-secret]', vendor.oneLoginParams.clientSecret)
  await t.click('[data-tc-save-form-btn="Sync OneLogin"]')
  await t.expect(Selector('.notification-title').innerText).eql('Success')
  await t.wait(8000)
  await t.expect(flashMessage.innerText).eql('Success',timeout);
}

export async function deleteOneLogin(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/vendors/sync_accounts`)
  await t.wait(5000)
  await t.hover('[data-tc-one-login-info]')
  await t.click('[data-tc-one-login-info] [data-tc-sync-btn]')
  await t.click('[delete-integration-modal] [data-tc-delete-integration-icon]')
  await t.click('[delete-integration-modal] [data-tc-delete-integration]')
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}

export async function syncOkta(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/vendors/sync_accounts`)
  await t.wait(5000)
  await t.hover('[data-tc-okta-info]')
  await t.click('[data-tc-okta-info] [data-tc-sync-btn]')
  await t.typeText('[data-tc-okta-modal] [data-tc-organization-name]', vendor.oktaParams.organizationName)
  await t.typeText('[data-tc-okta-modal] [data-tc-token]', vendor.oktaParams.token)
  await t.click('[data-tc-okta-modal] [data-tc-save-form-btn="Submit"]')
  await t.expect(Selector('.notification-title').innerText).eql('Success')
  await t.wait(8000)
  await t.expect(flashMessage.innerText).eql('Success',timeout);
}

export async function deleteOkta(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/vendors/sync_accounts`)
  await t.wait(5000)
  await t.hover('[data-tc-okta-info]')
  await t.click('[data-tc-okta-info] [data-tc-sync-btn]')
  await t.click('[delete-integration-modal] [data-tc-delete-integration-icon]')
  await t.click('[delete-integration-modal] [data-tc-delete-integration]')
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}


