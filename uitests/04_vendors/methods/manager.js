import common from '../../common/helpers/common.js'
import { Selector, Role, ClientFunction } from 'testcafe'
import vendor from '../helpers/vendor_variables.js'

export async function createVendorManager(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/vendors/all`)
  await t.typeText('input[data-tc-search-vendor]', vendor.vendorParams.name)
  await t.click(Selector('[data-tc-vendor-name]').withText(vendor.vendorParams.name))
  await t.click("[data-tc-vendor-manager]")
  await t.click("[data-tc-assign-manager]")
  await t.click('[data-tc-manager-name]')
  await t.typeText('[data-tc-manager-name]', vendor.managerParams.name)
  await t.pressKey('enter')
  await t.typeText('[data-tc="first_name"]', vendor.managerParams.firstName)
  await t.typeText('[data-tc="last_name"]', vendor.managerParams.lastName)
  await t.typeText('[data-tc="email"]', vendor.managerParams.email)
  await t.click('[data-tc-create="staff member"]')
  await t.expect(Selector('.notification-title').innerText).eql('Success')

}

export async function unassignVendorManager(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/vendors/all`)
  await t.typeText('input[data-tc-search-vendor]', vendor.vendorParams.name)
  await t.click(Selector('[data-tc-vendor-name]').withText(vendor.vendorParams.name))
  await t.click("[data-tc-vendor-manager]")
  await t.click("[data-tc-unassign-manager]")
  await t.click('[data-tc-manager-unassign-btn]')
}


