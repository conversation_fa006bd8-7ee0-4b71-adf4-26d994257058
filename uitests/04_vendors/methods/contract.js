import common from '../../common/helpers/common.js'
import { Selector, Role, ClientFunction } from 'testcafe'
import vendor from '../helpers/vendor_variables.js'

const selectCategory = Selector('[data-tc-category-id]');
const categoryOptions = selectCategory.find('option');

export async function createVendorContract(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/vendors/all`)
  await t.typeText('input[data-tc-search-vendor]', vendor.vendorParams.name)
  await t.click(Selector('[data-tc-vendor-name]').withText(vendor.vendorParams.name))
  await t.click("[data-tc-vendor-contracts]")
  await t.click("[data-tc-add-contract]")
  await t.click('[data-tc-contract-name]')
  await t.typeText('[data-tc-contract-name]', vendor.contractParams.name)
  await t.pressKey('enter')
  await t.click('[data-tc-fixed-term]')
  await t.click('[data-tc-start-date]')
  await t.click(Selector('td.cell.cur-month').withText('27'))
  await t.click('[data-tc-end-date]')
  await t.click('[data-tc-end-date] .mx-icon-next-year')
  await t.click(Selector('[data-tc-end-date] td.cell.cur-month').withText('27'))
  await t.click('[data-tc-add-alert-date-btn]')
  await t.click('[data-tc-alert-date-div] .alert-section.mt-2') // .mt-2 ensures we are clicking a secondary alert
  await t.click('[data-tc-alert-date-div] .alert-section.mt-2 .mx-icon-next-year')
  await t.click(Selector('[data-tc-alert-date-div] .alert-section.mt-2 td.cell.cur-month').withText('10'))
  await t.click('[data-tc-by-monthly-cost]')
  await t.typeText('[data-tc-currency]', vendor.contractParams.transactionAmount)
  await t
    .click(selectCategory)
    .click(categoryOptions.withText(vendor.contractParams.contractType))
  await t.pressKey('tab')
  await t.pressKey('tab')
  await t.pressKey('down')
  await t.pressKey('down')
  await t.pressKey('enter')
  await t.typeText("[data-tc-contract-notes]", vendor.contractParams.notes)
  await t.click('[data-tc-save-form-btn="Save Contract"]')
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}
