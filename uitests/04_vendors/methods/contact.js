import common from '../../common/helpers/common.js'
import { Selector, Role, ClientFunction } from 'testcafe'
import vendor from '../helpers/vendor_variables.js'

const departmentSelect = Selector('[data-tc-department]');
const departmentOption = departmentSelect.find('option');

export async function createVendorContact(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/vendors/all`)
  await t.typeText('input[data-tc-search-vendor]', vendor.vendorParams.name)
  await t.click(Selector('[data-tc-vendor-name]').withText(vendor.vendorParams.name))
  await t.click("[data-tc-vendor-basic-information]")
  await t.click("[data-tc-vendor-contacts]")
  await t.click("[data-tc-add-contact]")
  await t.typeText("[data-tc-first-name]", vendor.contactParams.firstName)
  await t.typeText("[data-tc-last-name]", vendor.contactParams.lastName)
  await t.typeText("[data-tc-email]", vendor.contactParams.email)
  await t.typeText("#phone_number_phone_number", vendor.contactParams.phoneNumber)
  await t.typeText("[data-tc-department]", vendor.contactParams.department)
  await t.click("[data-tc-save-form-btn='Save contact']")
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}

export async function unassignVendorContact(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/vendors/all`)
  await t.typeText('input[data-tc-search-vendor]', vendor.vendorParams.name)
  await t.click(Selector('[data-tc-vendor-name]').withText(vendor.vendorParams.name))
  await t.click("[data-tc-vendor-basic-information]")
  await t.click("[data-tc-vendor-contacts]")
  await t.click(Selector('[data-tc-contact-name]').withText(vendor.contactParams.firstName))
  await t.click('[data-tc-unassign-contact]')
  await t.click('[data-tc-unassign-btn]')
}


