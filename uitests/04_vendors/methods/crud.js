import common from '../../common/helpers/common.js'
import { Selector, Role, ClientFunction } from 'testcafe'
import vendor from '../helpers/vendor_variables.js'

const categorySelect = Selector('[data-tc-category-id]');
const categoryOption = categorySelect.find('option');
const vendorFilterMenu = Selector('[data-tc-filter-menu-button]');
const vendorFilterStatus = Selector('[data-tc-filter-status]');
const vendorTag = Selector('[data-tc-vendor-tag]');
const vendorDefaultTags = vendorTag.find('span');
const vendorCountry = Selector('[data-tc-vendor-country]');
const vendorCountryDetails = vendorCountry.find('option');
const alertPercent = vendor.vendorParams.alertPercent.toString();

export async function vendorFormValidation(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/vendors/new`)
  await t.click("[type='submit']")
  await t.expect(Selector(".notification-content").innerText).eql("Please correct the highlighted errors before submitting.")
  await t
    .typeText("[data-tc-name]", vendor.vendorParams.name)
    .click("[type='submit']")
  await t.expect(Selector('.notification-title').innerText).eql('Error')
}

export async function createVendor(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/vendors/new`)
  await t
    .typeText("[data-tc-name]", vendor.vendorParams.name)
    .typeText("[data-tc-vendor-url]", vendor.vendorParams.url)
    .click(categorySelect)
    .click(categoryOption.withText('IT'))
    .typeText('[data-tc-vendor-business-unit]', vendor.vendorParams.businessUnit)
    .typeText("[data-tc-vendor-description]", vendor.vendorParams.shortDescription)
    .typeText("[data-tc-vendor-account-number]", vendor.vendorParams.accountNumber)
    .click(vendorTag)
    .click(vendorDefaultTags.withText('Business Service'))
    .typeText('[data-tc-vendor-address]',vendor.vendorParams.address)
    .typeText('[data-tc-vendor-city]',vendor.vendorParams.city)
    .typeText('[data-tc-vendor-state]', vendor.vendorParams.state)
    .click(vendorCountry)
    .click(vendorCountryDetails.withText('United States'))
    .typeText('[data-tc-vendor-zip]', vendor.vendorParams.zip)
    .typeText('[data-tc-vendor-phone] #vendor_phone_phone_number', vendor.vendorParams.phoneNumber)
    .click('[data-tc-vendor-alert]')
    .click('[data-tc-remove-alert]')
    .click('[data-tc-vendor-alert]')
    .typeText('[data-tc-alert-percent]', alertPercent)
    .typeText('[data-tc-vendor-billing-requirements]', vendor.vendorParams.paymentMethod)
    .typeText('[data-tc-vendor-payment-terms]', vendor.vendorParams.paymentTerm)
    .click("button[type='submit']")
  await t.expect(Selector('[data-tc-vendor-name]').innerText).eql(vendor.vendorParams.name)
}

export async function updateVendor(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/vendors/all`)
  await t.typeText('input[data-tc-search-vendor]', vendor.vendorParams.name)
  await t.click(Selector('[data-tc-vendor-name]').withText(vendor.vendorParams.name))
  await t.click(Selector("[data-tc-edit-vendor]"))
  await t.selectText('[data-tc-name]').pressKey("delete");
  await t.selectText('[data-tc-vendor-url]').pressKey("delete");
  await t.selectText('[data-tc-vendor-description]').pressKey("delete");
  await t.selectText('[data-tc-vendor-account-number]').pressKey("delete");
  await t
    .typeText("[data-tc-name]", vendor.updateVendorParams.name)
    .typeText("[data-tc-vendor-url]", vendor.updateVendorParams.url)
    .click(categorySelect)
    .click(categoryOption.withText('HR'))
    .typeText("[data-tc-vendor-description]", vendor.updateVendorParams.shortDescription)
    .typeText("[data-tc-vendor-account-number]", vendor.updateVendorParams.accountNumber)
    .click("[type='submit']")
  await t.expect(Selector('[data-tc-vendor-name]').innerText).eql(vendor.updateVendorParams.name)
}

export async function archiveVendor(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/vendors/all`)
  await t.typeText('input[data-tc-search-vendor]', vendor.updateVendorParams.name)
  await t.click(Selector('[data-tc-vendor-name]').withText(vendor.updateVendorParams.name))
  await t.click(Selector("[data-tc-archive-vendor]"))
  await t.click(Selector("[data-tc-vendor-archive-modal-btn]"))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}

export async function unArchiveVendor(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/vendors/all`)
  await t.typeText('input[data-tc-search-vendor]', vendor.updateVendorParams.name)
  await t.click(vendorFilterMenu)
  await t.click(vendorFilterStatus)
  await t.click(Selector("[data-tc-filter-status] .dropdown-menu a:nth-child(3)"))
  await t.click(Selector('[data-tc-vendor-name]').withText(vendor.updateVendorParams.name))
  await t.click(Selector("[data-tc-unarchive-vendor]"))
  await t.click(Selector("[data-tc-vendor-unarchive-modal-btn]"))
  await t.expect(Selector('.notification-title').innerText).eql('Success')
}

export async function deleteVendor(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/vendors/all`)
  await t.click(Selector('[data-tc-vendor-name]').withText(vendor.updateVendorParams.name))
  await t.click(Selector("[data-tc-archive-vendor]"))
  await t.click(Selector("[data-tc-vendor-archive-modal-btn]"))
  await t.typeText('input[data-tc-search-vendor]', vendor.updateVendorParams.name)
  await t.click(vendorFilterMenu)
  await t.click(vendorFilterStatus)
  await t.click(Selector("[data-tc-filter-status] .dropdown-menu a:nth-child(3)"))
  await t.click(Selector('[data-tc-vendor-name]').withText(vendor.updateVendorParams.name))
  await t.click(Selector("[data-tc-delete-vendor]"))
  await t.click(Selector("[data-tc-vendor-delete-modal-btn]"))
  await t.expect(Selector('.notification-title').innerText).eql('Vendor was successfully removed.')
}
