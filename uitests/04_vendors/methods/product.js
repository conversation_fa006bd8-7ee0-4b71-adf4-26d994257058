import common from '../../common/helpers/common.js'
import { Selector, Role, ClientFunction } from 'testcafe'
import vendor from '../helpers/vendor_variables.js'

const categorySelect = Selector('[data-tc-category-id]');
const categoryOption = categorySelect.find('option');

export async function createVendorProduct(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/vendors/all`)
  await t.typeText('input[data-tc-search-vendor]', vendor.vendorParams.name)
  await t.click(Selector('[data-tc-vendor-name]').withText(vendor.vendorParams.name))
  await t.click('[data-tc-products]')
  await t.click('[data-tc-add-product]')
  await t.click('[data-tc-add-default-product]')
  await t.wait(2000)
  await t.click('[data-tc-add-product]')
  await t.typeText('[data-tc-name]', vendor.productParams.name)
  await t.typeText('[data-tc-total-licenses]', `${vendor.productParams.total_licenses}`)
  await t.typeText('[data-tc-consumed-licenses]', `${vendor.productParams.total_licenses - vendor.productParams.total_licenses/2}`)
  await t.click(categorySelect)
  await t.click(categoryOption.withText('IT'))
  await t.click('[data-tc-save-product]')
  await t.expect(Selector('.tc-product-name').innerText).eql(vendor.productParams.name)
}

export async function assignProduct(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/vendors/all`)
  await t.typeText('input[data-tc-search-vendor]', vendor.vendorParams.name)
  await t.click(Selector('[data-tc-vendor-name]').withText(vendor.vendorParams.name))
  await t.hover('i.nulodgicon-dot-3').click('.genuicon-pricetags.h6.not-as-small')
  await t.click("[data-tc-assign-product]")
  await t.expect(Selector('[data-tc-transaction-product-name]')
    .withText(vendor.vendorParams.name).innerText)
    .eql(vendor.vendorParams.name)
}

export async function editProduct(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/vendors/all`)
  await t.typeText('input[data-tc-search-vendor]', vendor.vendorParams.name)
  await t.click(Selector('[data-tc-vendor-name]').withText(vendor.vendorParams.name))
  await t.click('[data-tc-products]')
  await t.click('.box.box--with-hover')
  await t.click('.nulodgicon-edit')
  await t.selectText('[data-tc-name]').pressKey("delete")
  await t.typeText('[data-tc-name]', vendor.productParams.name)
  await t.typeText('[data-tc-total-licenses]', `${vendor.updateProductParams.total_licenses}`)
  await t.typeText('[data-tc-consumed-licenses]', `${vendor.updateProductParams.total_licenses - vendor.updateProductParams.total_licenses/2}`)
  await t.click(categorySelect)
  await t.click(categoryOption.withText('IT'))
  await t.click('[data-tc-save-product]')
}

export async function deleteProduct(t) {
  await t.useRole(common.userLogin)
  await t.navigateTo(`http://${common.user.company.subdomain}.lvh.me:3000/vendors/all`)
  await t.typeText('input[data-tc-search-vendor]', vendor.vendorParams.name)
  await t.click(Selector('[data-tc-vendor-name]').withText(vendor.vendorParams.name))
  await t.click('[data-tc-products]')
  await t.click(Selector('[data-tc-product]').withText(vendor.vendorParams.name))
  await t.click('.nulodgicon-trash-b')
  await t.click('[data-tc-delete-product]')
  await t.expect(Selector('.notification-content').innerText).eql('Successfully deleted product')
}

