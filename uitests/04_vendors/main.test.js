import { vendorFormValidation, createVendor, updateVendor, deleteVendor, archiveVendor, unArchiveVendor } from './methods/crud.js'
import { createVendorTransaction, splitTransaction, unlinkTransaction } from './methods/transaction.js'
import { createVendorProduct, editProduct, deleteProduct, assignProduct } from './methods/product.js'
import { createVendorContact, unassignVendorContact } from './methods/contact.js'
import { createVendorManager, unassignVendorManager } from './methods/manager.js'
import { createVendorContract } from './methods/contract.js'

fixture('Usecase: Vendors')

test('Create', async t => {
  await createVendor(t);
})

test('Vendor create form validation', async t => {
  await vendorFormValidation(t);
})

test('Create vendor product', async t => {
  await createVendorProduct(t);
})

test('Edit vendor product', async t => {
  await editProduct(t);
})

test('Create vendor transaction', async t => {
  await createVendorTransaction(t);
})

test('Split transactions', async t => {
  await splitTransaction(t);
})

test('Assign product', async t => {
  await assignProduct(t);
})

test('Unlink transaction', async t => {
  await unlinkTransaction(t);
})

test('Create vendor contact', async t => {
  await createVendorContact(t);
})

test('Unassign vendor contact', async t => {
  await unassignVendorContact(t);
})

test('Create vendor contract', async t => {
  await createVendorContract(t);
})

test('Create vendor manager', async t => {
  await createVendorManager(t);
})

test('Unassign vendor manager', async t => {
  await unassignVendorManager(t);
})

test('Delete vendor product', async t => {
  await deleteProduct(t);
})

test('Update vendor', async t => {
  await updateVendor(t);
})

test('Archive vendor', async t => {
  await archiveVendor(t);
})

test('unArchive vendor', async t => {
  await unArchiveVendor(t);
})

test('Delete vendor', async t => {
  await deleteVendor(t);
})
