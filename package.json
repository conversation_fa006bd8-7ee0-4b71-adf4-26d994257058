{"name": "nulodgic-app", "author": "Genuity", "version": "1.0.0", "license": "UNLICENSED", "private": true, "dependencies": {"@babel/polyfill": "^7.4.4", "@bugsnag/js": "^7.20.0", "@bugsnag/plugin-vue": "^7.19.0", "@chenfengyuan/vue-qrcode": "^1.0.2", "@codevise/activeadmin-searchable_select": "^1.8.0", "apexcharts": "^4.5.0", "@rails/actioncable": "^8.0.200", "axios": "^0.18.0", "bad-words": "^3.0.4", "chart.js": "^2.7.3", "chartjs-plugin-datalabels": "^0.7.0", "color-convert": "^2.0.1", "countup.js": "^1.9.3", "dompurify": "^3.0.8", "dotenv": "^8.2.0", "faker": "^4.1.0", "file-saver": "^2.0.5", "fiscal-year": "^1.0.0", "font-color-contrast": "^11.1.0", "html2canvas": "^1.4.1", "js-cookie": "^2.2.0", "jspdf": "^2.1.1", "libphonenumber-js": "^1.9.48", "linkify-html": "^4.0.2", "linkifyjs": "^4.0.2", "lodash": "^4.17.4", "lz-string": "^1.5.0", "mammoth": "^1.8.0", "marked": "^15.0.12", "mixpanel-browser": "^2.41.0", "moment-timezone": "^0.5.23", "papaparse": "5.4.1", "phones": "^2.0.1", "platform": "^1.3.5", "pluralize": "^7.0.0", "posthog-js": "^1.205.1", "print-js": "^1.6.0", "pusher-js": "^4.2.1", "sweet-modal-vue": "^2.0.0", "testcafe": "^1.8.2", "title-case": "^3.0.2", "to-camel-case": "^1.0.0", "trix": "^1.2.4", "uuid": "^8.3.2", "v-click-outside": "2.1.5", "v-clipboard": "^2.2.3", "v-markdown-editor": "^1.2.6", "v-tooltip": "^2.1.3", "vee-validate": "^2.2.7", "vue": "2.7.16", "vue-apexcharts": "^1.7.0", "vue-avatar": "^2.3.3", "vue-chartjs": "^3.4.2", "vue-countup-v2": "^1.0.3", "vue-grid-layout": "^2.4.0", "vue-loader": "^15.11.1", "vue-moment": "^4.0.0", "vue-multiselect": "^2.1.9", "vue-native-color-picker": "^1.2.0", "vue-notification": "^1.3.7", "vue-observe-visibility": "^1.0.0", "vue-phone-number-input": "^1.1.12", "vue-pusher": "^1.1.0", "vue-recaptcha": "^1.3.0", "vue-router": "^3.6.5", "vue-safe-html": "^3.0.1", "vue-share-it": "^1.1.8", "vue-slider-component": "^3.2.24", "vue-spinner": "^1.0.3", "vue-tel-input": "5.15.0", "vue-template-compiler": "2.6.14", "vue2-datepicker": "^2.12.0", "vue2-daterange-picker": "^0.6.8", "vue2-dropzone": "^3.2.0", "vue2-teleport": "^1.1.4", "vue2-timepicker": "^1.1.6", "vuedraggable": "^2.17.0", "vuejs-paginate": "https://github.com/j642d4/vuejs-paginate", "vuetrend": "^0.3.4", "vuex": "^3.6.2", "vuex-persistedstate": "^3.2.1", "xlsx": "0.18.5"}, "devDependencies": {"@babel/core": "^7.4.4", "@babel/plugin-proposal-private-methods": "^7.17.12", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-transform-shorthand-properties": "7.2.0", "@rails/webpacker": "^5.0.0", "@vue/eslint-config-airbnb": "^6.0.0", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-standard": "^6.0.0", "babel-eslint": "^10.0.1", "caniuse-lite": "^1.0.30000697", "core-js": "^2.5.6", "css-loader": "^2.1.1", "deep-is": "^0.1.3", "eslint": "7.32.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-friendly-formatter": "^4.0.1", "eslint-loader": "^4.0.2", "eslint-plugin-html": "^7.1.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^5.0.0", "eslint-plugin-react": "^7.12.4", "eslint-plugin-react-hooks": "^4.3.0", "eslint-plugin-standard": "^5.0.0", "eslint-plugin-vue": "^7.0.0", "eslint-plugin-vuejs-accessibility": "^1.1.0", "extract-text-webpack-plugin": "^4.0.0-beta.0", "husky": "^8.0.0", "npm": "^6.0.0", "prettier": "^2.7.1", "sass": "^1.80.3", "sass-loader": "^10.1.1", "style-loader": "^0.19.0", "url-loader": "^1.0.1", "vue-html-loader": "^1.2.4", "webpack": "^4.30.0", "webpack-cli": "^3.1.1", "webpack-dev-server": "^3.11.0", "webpack-merge": "^4.1.2", "webpack-sources": "^3.2.3"}, "scripts": {"start": "DOMAIN=lvh.me bundle exec rails server -p 3000 > log/testcafe.log", "stop": "sudo kill -9 $(cat tmp/pids/server.pid)", "lint": "eslint ./app --ext js,vue", "lint:fix": "NODE_ENV=production eslint ./app --ext js,vue --fix", "lint:changed": "NODE_ENV=production eslint -c .eslintrc.js $(git diff --name-only --diff-filter=ACMRTUXB master | grep  -E \"(.js$|.vue$|.tsx$)\")", "lint:changed:fix": "NODE_ENV=production eslint --fix -c .eslintrc.js $(git diff --name-only --diff-filter=ACMRTUXB master | grep  -E \"(.js$|.vue$|.tsx$)\")", "test": "testcafe  --app-init-delay 50000 --skip-uncaught-errors --skip-js-errors --dev --selector-timeout 20000 --assertion-timeout 20000 --speed 1 'chrome:headless' uitests/01_sign_up/*.test.js uitests/02_sign_in/*.test.js uitests/08_helpdesk/*.test.js uitests/11_helpdesk_ticket_created_automated_task/*.test.js uitests/98_sign_out/*.test.js uitests/99_delete_company/*.test.js --app \"npm start >/dev/null 2>&1\"", "smoketest:dev": "testcafe chrome --env=dev --skip-uncaught-errors --skip-js-errors uitests2/smoke_tests/sign_up/* uitests2/smoke_tests/company_settings/* uitests2/smoke_tests/prerequest_data/main.test.js uitests2/smoke_tests/assets/* uitests2/smoke_tests/help_desk/* uitests2/smoke_tests/contracts/* uitests2/smoke_tests/telecom/* uitests2/smoke_tests/vendor/* uitests2/smoke_tests/deleting_company/*", "smoketest:staging": "testcafe chrome --env=staging --skip-uncaught-errors --skip-js-errors uitests2/smoke_tests/sign_up/* uitests2/smoke_tests/company_settings/* uitests2/smoke_tests/prerequest_data/main.test.js uitests2/smoke_tests/assets/* uitests2/smoke_tests/help_desk/* uitests2/smoke_tests/contracts/* uitests2/smoke_tests/telecom/* uitests2/smoke_tests/vendor/* uitests2/smoke_tests/deleting_company/*", "smoketest:staging:helpdesk": "testcafe chrome --env=staging --skip-uncaught-errors --skip-js-errors uitests2/smoke_tests/prerequest_data/main.test.js uitests2/smoke_tests/help_agent_smoke/*", "smoketest:prod": "testcafe chrome --env=prod --skip-uncaught-errors --skip-js-errors uitests2/smoke_tests/sign_up/* uitests2/smoke_tests/company_settings/* uitests2/smoke_tests/prerequest_data/main.test.js uitests2/smoke_tests/assets/* uitests2/smoke_tests/contracts/* uitests2/smoke_tests/telecom/* uitests2/smoke_tests/vendor/* uitests2/smoke_tests/help_desk/*", "regressiontest:dev": "testcafe chrome --env=dev --skip-uncaught-errors --skip-js-errors uitests2/smoke_tests/sign_up/* uitests2/regression_tests/company_settings/* uitests2/smoke_tests/prerequest_data/regression.test.js uitests2/regression_tests/help_desk/* uitests2/regression_tests/assets/* uitests2/smoke_tests/deleting_company/*", "regressiontest:staging": "testcafe chrome --env=staging --skip-uncaught-errors --skip-js-errors uitests2/smoke_tests/sign_up/* uitests2/regression_tests/company_settings/* uitests2/smoke_tests/prerequest_data/regression.test.js uitests2/regression_tests/help_desk/*  uitests2/regression_tests/assets/* uitests2/smoke_tests/deleting_company/*", "prepare": "if [ \"$HUSKY\" != \"0\" ]; then husky install; fi"}}