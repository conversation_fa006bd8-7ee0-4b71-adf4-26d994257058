
# Genuity

Nulodgic's IT Help Desk solution is a cloud-based SaaS offering, allowing support teams the ability to identify problems, prioritize them for resolution, and spot potential problem trends that may cause outages in the future. IT Help Desk makes it easy to manage tickets and respond to requests. Our automated capabilities substantially reduce the time it takes for support to respond. Our collaboration tools and UI are designed to reduce time spent handling tickets all in one place. Avoid the collection of features you don't need and do it all from one platform efficiently.

## Project Setup - Docker
# Docker Desktop Setup Guide

This guide provides step-by-step instructions to set up Docker Desktop on Linux, Windows, Mac (Intel), and Mac (Silicon).

## Prerequisites

- An account with [Docker Hub](https://hub.docker.com/).
- Administrative access to your machine.

## Table of Contents

- [Linux Setup](https://chatgpt.com/c/53e51a41-b228-4b94-828b-7303b8e15015#linux-setup)
- [Windows Setup](https://chatgpt.com/c/53e51a41-b228-4b94-828b-7303b8e15015#windows-setup)
- [Mac Setup (Intel)](https://chatgpt.com/c/53e51a41-b228-4b94-828b-7303b8e15015#mac-setup-intel)
- [Mac Setup (Silicon)](https://chatgpt.com/c/53e51a41-b228-4b94-828b-7303b8e15015#mac-setup-silicon)
- [Post-Installation Steps](https://chatgpt.com/c/53e51a41-b228-4b94-828b-7303b8e15015#post-installation-steps)
- [Troubleshooting](https://chatgpt.com/c/53e51a41-b228-4b94-828b-7303b8e15015#troubleshooting)

## Linux Setup

### Supported Distributions

- Ubuntu
- Fedora
- Debian
- CentOS

### Docker Ubuntu Installation Steps

1. **Update your package index:**
   ```bash
   sudo apt-get update

2. **Install required packages:**
    ```bash
    sudo apt-get install \
        ca-certificates \
        curl \
        gnupg \
        lsb-release

3. **Add Docker’s official GPG key**
    ```bash
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

4. **Set up the stable repository:**
    ```bash
    echo \
     "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu \
     $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

5. **Install Docker Engine:**
    ```bash
    sudo apt-get update
    sudo apt-get install docker-ce docker-ce-cli containerd.io
    ```

6. **Start Docker:**
    ```bash
    sudo systemctl start docker
    sudo systemctl enable docker
    ```

# Useful command

1. **Run Docker Project:**
    Go inside project directory and run following command
    ```bash
    docker compose up
    ```

2. **Stop Docker Project:**
    Go inside project directory and run following command
    ```bash
    docker compose down
    ```

3. **Check the Status of Running Docker Containers:**
    ```bash
    docker compose ps -a
    ```

4. **LList Docker Volumes:**
    ```bash
    docker volume ls
    ```
5. **Delete Docker Volume:**
    ```bash
    docker compose down
    docker volume rm <volume_name>
    ```

6. **Start a Sidekiq Worker:**
    If you need to start a Sidekiq worker, first locate the compose.yaml file in the project directory and search for the specific worker you want to start. For example, to start the Sidekiq worker named genuity-sidekiq-import, open a new terminal and run the following command: 
    ```bash
    docker compose up --scale genuity-sidekiq-import=1  
    ```

    To start all workers at once, find the compose.yaml file and locate the x-sidekiq-service section. Within this section, look for the scale key (approximately line 29) and update its value from 0 to 1.
    ```bash
    docker compose down
    docker compose up
    ```

7. **Restore PostgreSQL Backup:**
    Place the dump file in the project directory where compose.yaml is located. Then, within the psql service section (around line 40), uncomment the .:/app line."
    ```bash
    docker compose exec psql pg_restore -U <username> -d <database_name> --no-owner /app/<filename>.dump
    ```


## Project Setup - Machine
Prerequisites require for project
- Ruby version 3.0.4
- Postgres version 12 or 13
- Bundler version 2.2.33
- Node version 20.18.0
- Yarn version 1.22.18
- Install Redis
### Project Clone
Clone project in your root directory "projects" (git clone https://github.com/j642d4/nulodgic.git). In the cloned project directory run following commands.

```
bundle install
```
```
yarn
```

### Database Setup
For setting up the database, copy the ENV and decryption keys of local, test enviornments inside project directory. Then run the following commands in sequence.
```
rails db:create
```
```
pg_restore -d env_db_name -1 downloaded_dump_path
```
```
rails db:migrate
```
```
rails db:seed
```
### Test Cases
Use following commands for running the test cases.
- For backend test cases
```
bundle exec rspec
```
- For frontend end-to-end test cases
```
npm test
```
- For rubocop
```
bundle exec rubocop
```
### Starting Project
For starting project locally use the following commands.

- Starting rails server
```
rails s
```
- Worker
```
foreman start -f Procfile.sidekiq
```
- Webpacker
```
./bin/webpack-dev-server
```
