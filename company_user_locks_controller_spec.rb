require 'rails_helper'
require 'sidekiq/testing'
include CompanyUserHelper
include <PERSON>ginHelper

describe CompanyUserLocksController, type: :controller do
  create_company_and_user

  before do
    login_user
    company_user.activities.destroy_all
  end

  describe "#update" do
    it "will revoke user access" do
      company_user.update(granted_access_at: Time.now)
      put :update, params: { id: company_user.id }

      activity = company_user.activities.find_by(activity_type: 'access')
      expect(activity).to be_present
      expect(activity.data['current_value']).to eq("revoked")
    end
  end
end
