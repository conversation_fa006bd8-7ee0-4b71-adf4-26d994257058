FROM alpine:3.20

# Build arguments
ARG USER_NAME=genuity
ARG USER_HOME=/home/<USER>
ARG ASDF_DIR=$USER_HOME/.asdf
ARG ENV=development

# Set environment variables using build arguments
ENV USER_NAME=$USER_NAME \
    USER_HOME=$USER_HOME \
    ASDF_DIR=$ASDF_DIR \
    PATH=$ASDF_DIR/bin:$ASDF_DIR/shims:$PATH \
    APP_DIR=/app \
    ENV=$ENV

# Install dependencies
RUN apk update && \
    apk upgrade && \
    apk add --no-cache \
    curl \
    make \
    ca-certificates \
    build-base \
    imagemagick \
    imagemagick-dev \
    git \
    bash \
    pkgconfig \
    zlib-dev \
    libxml2-dev \
    libxslt-dev \
    tzdata \
    yaml-dev \
    openssl-dev \
    libffi-dev \
    readline-dev \
    bzip2 \
    bzip2-dev \
    less \
    postgresql-dev \
    postgresql-client \
    coreutils \
    tar \
    sqlite-dev \
    libc6-compat \
    shared-mime-info \
    gnupg && \
    rm -rf /var/cache/apk/*

# Add non-root user
RUN adduser -D -h $USER_HOME -s /bin/bash $USER_NAME

# Create app directory and set ownership to the non-root user
RUN mkdir -p ${APP_DIR} && chown -R $USER_NAME:$USER_NAME ${APP_DIR}

# Switch to non-root user
USER $USER_NAME
WORKDIR $APP_DIR

# Install asdf for the non-root user
RUN git clone https://github.com/asdf-vm/asdf.git $ASDF_DIR --branch v0.14.0 && \
    echo -e "\n. $ASDF_DIR/asdf.sh" >> $USER_HOME/.bashrc

# Install plugins and versions
RUN asdf plugin-add python https://github.com/danhper/asdf-python.git && \
    asdf plugin-add ruby https://github.com/asdf-vm/asdf-ruby.git && \
    asdf plugin-add nodejs https://github.com/asdf-vm/asdf-nodejs.git && \
    asdf plugin-add yarn && \
    asdf install python 2.7.18 && \
    asdf global python 2.7.18 && \
    asdf install ruby 3.2.0 && \
    asdf global ruby 3.2.0 && \
    asdf install nodejs 20.18.0 && \
    asdf global nodejs 20.18.0 && \
    asdf install yarn latest && \
    asdf global yarn latest

# Copy application code
ADD --chown=$USER_NAME:$USER_NAME . ${APP_DIR}

# Install Ruby dependencies
RUN gem install shared-mime-info && \
    gem install bundler && \
    bundle install

# Set up node_modules in home directory and configure Yarn
RUN mkdir -p ~/node_modules && \
    yarn config set global-folder ~/node_modules && \
    export PATH=$HOME/node_modules/bin:$PATH

ENV HUSKY=0

# Install Node.js dependencies and build assets
RUN NODE_OPTIONS="--max_old_space_size=8192" yarn add -D webpack-cli@4.8.0 -y && \
    yes |  NODE_OPTIONS="--max-old-space-size=8192" RAILS_ENV=$ENV bin/rails assets:precompile && \
    rm -rf config/credentials/*.key

RUN mkdir -p tmp/pids/

