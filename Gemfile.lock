GIT
  remote: https://github.com/j642d4/oktakit_v2
  revision: ec4dac1e442340c36cc6d85b6e1baaf08999b240
  specs:
    oktakit (0.2.0)
      sawyer (~> 0.8.1)

GIT
  remote: https://github.com/waynerobinson/xeroizer
  revision: 825166913a1ded3858fb1c478b94aaee99cea8d4
  specs:
    xeroizer (3.0.1)
      activesupport
      builder (>= 2.1.2)
      i18n
      nokogiri
      oauth (>= 0.4.5)
      oauth2 (>= 1.4.0)
      tzinfo

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.2.0)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    active_record_doctor (1.9.0)
      activerecord (>= 4.2.0)
    active_storage_validations (1.0.3)
      activejob (>= 5.2.0)
      activemodel (>= 5.2.0)
      activestorage (>= 5.2.0)
      activesupport (>= 5.2.0)
    activeadmin (2.12.0)
      arbre (~> 1.2, >= 1.2.1)
      formtastic (>= 3.1, < 5.0)
      formtastic_i18n (~> 0.4)
      inherited_resources (~> 1.7)
      jquery-rails (~> 4.2)
      kaminari (~> 1.0, >= 1.2.1)
      railties (>= 6.0, < 7.1)
      ransack (>= 2.1.1, < 4)
    activeadmin-searchable_select (1.8.0)
      activeadmin (>= 1.x, < 4)
      jquery-rails (>= 3.0, < 5)
      select2-rails (~> 4.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
    activerecord-import (1.8.1)
      activerecord (>= 4.2)
    activerecord-session_store (2.0.0)
      actionpack (>= *******)
      activerecord (>= *******)
      multi_json (~> 1.11, >= 1.11.2)
      rack (>= 2.0.8, < 3)
      railties (>= *******)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    addressable (2.8.5)
      public_suffix (>= 2.0.2, < 6.0)
    arbre (1.5.0)
      activesupport (>= 3.0.0, < 7.1)
      ruby2_keywords (>= 0.0.2, < 1.0)
    ast (2.4.2)
    audited (5.0.2)
      activerecord (>= 5.0, < 7.1)
    autoprefixer-rails (********)
      execjs (~> 2)
    aws-eventstream (1.2.0)
    aws-partitions (1.586.0)
    aws-sdk (3.1.0)
      aws-sdk-resources (~> 3)
    aws-sdk-accessanalyzer (1.29.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-account (1.6.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-acm (1.51.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-acmpca (1.48.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-alexaforbusiness (1.56.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-amplify (1.41.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-amplifybackend (1.17.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-amplifyuibuilder (1.5.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-apigateway (1.76.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-apigatewaymanagementapi (1.30.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-apigatewayv2 (1.42.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-appconfig (1.25.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-appconfigdata (1.5.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-appflow (1.26.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-appintegrationsservice (1.13.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-applicationautoscaling (1.62.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-applicationcostprofiler (1.9.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-applicationdiscoveryservice (1.44.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-applicationinsights (1.30.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-appmesh (1.45.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-appregistry (1.15.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-apprunner (1.13.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-appstream (1.65.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-appsync (1.52.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-athena (1.53.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-auditmanager (1.24.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-augmentedairuntime (1.22.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-autoscaling (1.79.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-autoscalingplans (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-backup (1.44.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-backupgateway (1.3.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-batch (1.61.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-billingconductor (1.0.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-braket (1.19.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-budgets (1.49.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-chime (1.67.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-chimesdkidentity (1.9.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-chimesdkmediapipelines (1.0.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-chimesdkmeetings (1.10.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-chimesdkmessaging (1.10.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloud9 (1.45.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudcontrolapi (1.7.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-clouddirectory (1.41.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudformation (1.68.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudfront (1.64.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudhsm (1.39.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudhsmv2 (1.42.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudsearch (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudsearchdomain (1.33.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudtrail (1.49.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudwatch (1.64.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudwatchevents (1.57.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudwatchevidently (1.5.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudwatchlogs (1.52.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudwatchrum (1.4.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-codeartifact (1.19.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-codebuild (1.88.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-codecommit (1.51.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-codedeploy (1.49.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-codeguruprofiler (1.24.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-codegurureviewer (1.31.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-codepipeline (1.53.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-codestar (1.38.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-codestarconnections (1.24.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-codestarnotifications (1.19.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cognitoidentity (1.40.1)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cognitoidentityprovider (1.65.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cognitosync (1.36.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-comprehend (1.60.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-comprehendmedical (1.36.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-computeoptimizer (1.32.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-configservice (1.77.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-connect (1.71.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-connectcontactlens (1.11.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-connectparticipant (1.22.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-connectwisdomservice (1.7.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-core (3.130.2)
      aws-eventstream (~> 1, >= 1.0.2)
      aws-partitions (~> 1, >= 1.525.0)
      aws-sigv4 (~> 1.1)
      jmespath (~> 1.0)
    aws-sdk-costandusagereportservice (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-costexplorer (1.76.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-customerprofiles (1.20.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-databasemigrationservice (1.67.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-dataexchange (1.26.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-datapipeline (1.36.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-datasync (1.46.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-dax (1.39.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-detective (1.28.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-devicefarm (1.51.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-devopsguru (1.23.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-directconnect (1.54.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-directoryservice (1.49.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-dlm (1.50.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-docdb (1.42.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-drs (1.4.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-dynamodb (1.74.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-dynamodbstreams (1.38.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ebs (1.26.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ec2 (1.312.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ec2instanceconnect (1.24.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ecr (1.56.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ecrpublic (1.12.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ecs (1.99.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-efs (1.54.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-eks (1.74.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-elasticache (1.77.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-elasticbeanstalk (1.51.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-elasticinference (1.21.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-elasticloadbalancing (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-elasticloadbalancingv2 (1.77.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-elasticsearchservice (1.65.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-elastictranscoder (1.38.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-emr (1.59.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-emrcontainers (1.14.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-eventbridge (1.38.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-finspace (1.11.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-finspacedata (1.14.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-firehose (1.48.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-fis (1.13.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-fms (1.49.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-forecastqueryservice (1.21.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-forecastservice (1.33.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-frauddetector (1.32.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-fsx (1.55.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-gamelift (1.57.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-gamesparks (1.0.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-glacier (1.46.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-globalaccelerator (1.39.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-glue (1.111.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-gluedatabrew (1.22.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-greengrass (1.49.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-greengrassv2 (1.17.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-groundstation (1.27.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-guardduty (1.57.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-health (1.47.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-healthlake (1.13.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-honeycode (1.17.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iam (1.68.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-identitystore (1.15.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-imagebuilder (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-importexport (1.35.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv2 (~> 1.0)
    aws-sdk-inspector (1.43.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-inspector2 (1.4.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iot (1.89.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iot1clickdevicesservice (1.37.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iot1clickprojects (1.37.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iotanalytics (1.49.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iotdataplane (1.39.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iotdeviceadvisor (1.14.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iotevents (1.33.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ioteventsdata (1.26.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iotfleethub (1.11.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iotjobsdataplane (1.36.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iotsecuretunneling (1.21.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iotsitewise (1.41.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iotthingsgraph (1.23.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iottwinmaker (1.5.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iotwireless (1.23.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ivs (1.20.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ivschat (1.0.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-kafka (1.49.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-kafkaconnect (1.7.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-kendra (1.50.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-keyspaces (1.2.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-kinesis (1.41.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-kinesisanalytics (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-kinesisanalyticsv2 (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-kinesisvideo (1.42.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-kinesisvideoarchivedmedia (1.44.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-kinesisvideomedia (1.37.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-kinesisvideosignalingchannels (1.19.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-kms (1.56.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-lakeformation (1.26.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-lambda (1.83.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-lambdapreview (1.35.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-lex (1.45.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-lexmodelbuildingservice (1.57.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-lexmodelsv2 (1.23.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-lexruntimev2 (1.15.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-licensemanager (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-lightsail (1.66.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-locationservice (1.22.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-lookoutequipment (1.11.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-lookoutforvision (1.14.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-lookoutmetrics (1.16.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-machinelearning (1.37.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-macie (1.38.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-macie2 (1.45.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-managedblockchain (1.32.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-managedgrafana (1.7.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-marketplacecatalog (1.21.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-marketplacecommerceanalytics (1.41.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-marketplaceentitlementservice (1.35.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-marketplacemetering (1.44.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mediaconnect (1.44.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mediaconvert (1.89.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-medialive (1.86.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mediapackage (1.53.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mediapackagevod (1.36.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mediastore (1.41.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mediastoredata (1.38.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mediatailor (1.55.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-memorydb (1.8.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mgn (1.13.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-migrationhub (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-migrationhubconfig (1.20.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-migrationhubrefactorspaces (1.5.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-migrationhubstrategyrecommendations (1.4.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mobile (1.35.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mq (1.47.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mturk (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mwaa (1.15.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-neptune (1.45.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-networkfirewall (1.17.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-networkmanager (1.22.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-nimblestudio (1.13.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-opensearchservice (1.10.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-opsworks (1.41.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-opsworkscm (1.52.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-organizations (1.70.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-outposts (1.31.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-panorama (1.7.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-personalize (1.41.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-personalizeevents (1.27.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-personalizeruntime (1.32.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-pi (1.39.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-pinpoint (1.67.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-pinpointemail (1.35.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-pinpointsmsvoice (1.32.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-pinpointsmsvoicev2 (1.0.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-polly (1.55.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-pricing (1.38.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-prometheusservice (1.14.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-proton (1.15.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-qldb (1.25.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-qldbsession (1.22.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-quicksight (1.64.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ram (1.39.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-rds (1.146.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-rdsdataservice (1.35.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-recyclebin (1.5.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-redshift (1.82.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-redshiftdataapiservice (1.19.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-rekognition (1.67.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-resiliencehub (1.4.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-resourcegroups (1.45.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-resourcegroupstaggingapi (1.47.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-resources (3.130.0)
      aws-sdk-accessanalyzer (~> 1)
      aws-sdk-account (~> 1)
      aws-sdk-acm (~> 1)
      aws-sdk-acmpca (~> 1)
      aws-sdk-alexaforbusiness (~> 1)
      aws-sdk-amplify (~> 1)
      aws-sdk-amplifybackend (~> 1)
      aws-sdk-amplifyuibuilder (~> 1)
      aws-sdk-apigateway (~> 1)
      aws-sdk-apigatewaymanagementapi (~> 1)
      aws-sdk-apigatewayv2 (~> 1)
      aws-sdk-appconfig (~> 1)
      aws-sdk-appconfigdata (~> 1)
      aws-sdk-appflow (~> 1)
      aws-sdk-appintegrationsservice (~> 1)
      aws-sdk-applicationautoscaling (~> 1)
      aws-sdk-applicationcostprofiler (~> 1)
      aws-sdk-applicationdiscoveryservice (~> 1)
      aws-sdk-applicationinsights (~> 1)
      aws-sdk-appmesh (~> 1)
      aws-sdk-appregistry (~> 1)
      aws-sdk-apprunner (~> 1)
      aws-sdk-appstream (~> 1)
      aws-sdk-appsync (~> 1)
      aws-sdk-athena (~> 1)
      aws-sdk-auditmanager (~> 1)
      aws-sdk-augmentedairuntime (~> 1)
      aws-sdk-autoscaling (~> 1)
      aws-sdk-autoscalingplans (~> 1)
      aws-sdk-backup (~> 1)
      aws-sdk-backupgateway (~> 1)
      aws-sdk-batch (~> 1)
      aws-sdk-billingconductor (~> 1)
      aws-sdk-braket (~> 1)
      aws-sdk-budgets (~> 1)
      aws-sdk-chime (~> 1)
      aws-sdk-chimesdkidentity (~> 1)
      aws-sdk-chimesdkmediapipelines (~> 1)
      aws-sdk-chimesdkmeetings (~> 1)
      aws-sdk-chimesdkmessaging (~> 1)
      aws-sdk-cloud9 (~> 1)
      aws-sdk-cloudcontrolapi (~> 1)
      aws-sdk-clouddirectory (~> 1)
      aws-sdk-cloudformation (~> 1)
      aws-sdk-cloudfront (~> 1)
      aws-sdk-cloudhsm (~> 1)
      aws-sdk-cloudhsmv2 (~> 1)
      aws-sdk-cloudsearch (~> 1)
      aws-sdk-cloudsearchdomain (~> 1)
      aws-sdk-cloudtrail (~> 1)
      aws-sdk-cloudwatch (~> 1)
      aws-sdk-cloudwatchevents (~> 1)
      aws-sdk-cloudwatchevidently (~> 1)
      aws-sdk-cloudwatchlogs (~> 1)
      aws-sdk-cloudwatchrum (~> 1)
      aws-sdk-codeartifact (~> 1)
      aws-sdk-codebuild (~> 1)
      aws-sdk-codecommit (~> 1)
      aws-sdk-codedeploy (~> 1)
      aws-sdk-codeguruprofiler (~> 1)
      aws-sdk-codegurureviewer (~> 1)
      aws-sdk-codepipeline (~> 1)
      aws-sdk-codestar (~> 1)
      aws-sdk-codestarconnections (~> 1)
      aws-sdk-codestarnotifications (~> 1)
      aws-sdk-cognitoidentity (~> 1)
      aws-sdk-cognitoidentityprovider (~> 1)
      aws-sdk-cognitosync (~> 1)
      aws-sdk-comprehend (~> 1)
      aws-sdk-comprehendmedical (~> 1)
      aws-sdk-computeoptimizer (~> 1)
      aws-sdk-configservice (~> 1)
      aws-sdk-connect (~> 1)
      aws-sdk-connectcontactlens (~> 1)
      aws-sdk-connectparticipant (~> 1)
      aws-sdk-connectwisdomservice (~> 1)
      aws-sdk-costandusagereportservice (~> 1)
      aws-sdk-costexplorer (~> 1)
      aws-sdk-customerprofiles (~> 1)
      aws-sdk-databasemigrationservice (~> 1)
      aws-sdk-dataexchange (~> 1)
      aws-sdk-datapipeline (~> 1)
      aws-sdk-datasync (~> 1)
      aws-sdk-dax (~> 1)
      aws-sdk-detective (~> 1)
      aws-sdk-devicefarm (~> 1)
      aws-sdk-devopsguru (~> 1)
      aws-sdk-directconnect (~> 1)
      aws-sdk-directoryservice (~> 1)
      aws-sdk-dlm (~> 1)
      aws-sdk-docdb (~> 1)
      aws-sdk-drs (~> 1)
      aws-sdk-dynamodb (~> 1)
      aws-sdk-dynamodbstreams (~> 1)
      aws-sdk-ebs (~> 1)
      aws-sdk-ec2 (~> 1)
      aws-sdk-ec2instanceconnect (~> 1)
      aws-sdk-ecr (~> 1)
      aws-sdk-ecrpublic (~> 1)
      aws-sdk-ecs (~> 1)
      aws-sdk-efs (~> 1)
      aws-sdk-eks (~> 1)
      aws-sdk-elasticache (~> 1)
      aws-sdk-elasticbeanstalk (~> 1)
      aws-sdk-elasticinference (~> 1)
      aws-sdk-elasticloadbalancing (~> 1)
      aws-sdk-elasticloadbalancingv2 (~> 1)
      aws-sdk-elasticsearchservice (~> 1)
      aws-sdk-elastictranscoder (~> 1)
      aws-sdk-emr (~> 1)
      aws-sdk-emrcontainers (~> 1)
      aws-sdk-eventbridge (~> 1)
      aws-sdk-finspace (~> 1)
      aws-sdk-finspacedata (~> 1)
      aws-sdk-firehose (~> 1)
      aws-sdk-fis (~> 1)
      aws-sdk-fms (~> 1)
      aws-sdk-forecastqueryservice (~> 1)
      aws-sdk-forecastservice (~> 1)
      aws-sdk-frauddetector (~> 1)
      aws-sdk-fsx (~> 1)
      aws-sdk-gamelift (~> 1)
      aws-sdk-gamesparks (~> 1)
      aws-sdk-glacier (~> 1)
      aws-sdk-globalaccelerator (~> 1)
      aws-sdk-glue (~> 1)
      aws-sdk-gluedatabrew (~> 1)
      aws-sdk-greengrass (~> 1)
      aws-sdk-greengrassv2 (~> 1)
      aws-sdk-groundstation (~> 1)
      aws-sdk-guardduty (~> 1)
      aws-sdk-health (~> 1)
      aws-sdk-healthlake (~> 1)
      aws-sdk-honeycode (~> 1)
      aws-sdk-iam (~> 1)
      aws-sdk-identitystore (~> 1)
      aws-sdk-imagebuilder (~> 1)
      aws-sdk-importexport (~> 1)
      aws-sdk-inspector (~> 1)
      aws-sdk-inspector2 (~> 1)
      aws-sdk-iot (~> 1)
      aws-sdk-iot1clickdevicesservice (~> 1)
      aws-sdk-iot1clickprojects (~> 1)
      aws-sdk-iotanalytics (~> 1)
      aws-sdk-iotdataplane (~> 1)
      aws-sdk-iotdeviceadvisor (~> 1)
      aws-sdk-iotevents (~> 1)
      aws-sdk-ioteventsdata (~> 1)
      aws-sdk-iotfleethub (~> 1)
      aws-sdk-iotjobsdataplane (~> 1)
      aws-sdk-iotsecuretunneling (~> 1)
      aws-sdk-iotsitewise (~> 1)
      aws-sdk-iotthingsgraph (~> 1)
      aws-sdk-iottwinmaker (~> 1)
      aws-sdk-iotwireless (~> 1)
      aws-sdk-ivs (~> 1)
      aws-sdk-ivschat (~> 1)
      aws-sdk-kafka (~> 1)
      aws-sdk-kafkaconnect (~> 1)
      aws-sdk-kendra (~> 1)
      aws-sdk-keyspaces (~> 1)
      aws-sdk-kinesis (~> 1)
      aws-sdk-kinesisanalytics (~> 1)
      aws-sdk-kinesisanalyticsv2 (~> 1)
      aws-sdk-kinesisvideo (~> 1)
      aws-sdk-kinesisvideoarchivedmedia (~> 1)
      aws-sdk-kinesisvideomedia (~> 1)
      aws-sdk-kinesisvideosignalingchannels (~> 1)
      aws-sdk-kms (~> 1)
      aws-sdk-lakeformation (~> 1)
      aws-sdk-lambda (~> 1)
      aws-sdk-lambdapreview (~> 1)
      aws-sdk-lex (~> 1)
      aws-sdk-lexmodelbuildingservice (~> 1)
      aws-sdk-lexmodelsv2 (~> 1)
      aws-sdk-lexruntimev2 (~> 1)
      aws-sdk-licensemanager (~> 1)
      aws-sdk-lightsail (~> 1)
      aws-sdk-locationservice (~> 1)
      aws-sdk-lookoutequipment (~> 1)
      aws-sdk-lookoutforvision (~> 1)
      aws-sdk-lookoutmetrics (~> 1)
      aws-sdk-machinelearning (~> 1)
      aws-sdk-macie (~> 1)
      aws-sdk-macie2 (~> 1)
      aws-sdk-managedblockchain (~> 1)
      aws-sdk-managedgrafana (~> 1)
      aws-sdk-marketplacecatalog (~> 1)
      aws-sdk-marketplacecommerceanalytics (~> 1)
      aws-sdk-marketplaceentitlementservice (~> 1)
      aws-sdk-marketplacemetering (~> 1)
      aws-sdk-mediaconnect (~> 1)
      aws-sdk-mediaconvert (~> 1)
      aws-sdk-medialive (~> 1)
      aws-sdk-mediapackage (~> 1)
      aws-sdk-mediapackagevod (~> 1)
      aws-sdk-mediastore (~> 1)
      aws-sdk-mediastoredata (~> 1)
      aws-sdk-mediatailor (~> 1)
      aws-sdk-memorydb (~> 1)
      aws-sdk-mgn (~> 1)
      aws-sdk-migrationhub (~> 1)
      aws-sdk-migrationhubconfig (~> 1)
      aws-sdk-migrationhubrefactorspaces (~> 1)
      aws-sdk-migrationhubstrategyrecommendations (~> 1)
      aws-sdk-mobile (~> 1)
      aws-sdk-mq (~> 1)
      aws-sdk-mturk (~> 1)
      aws-sdk-mwaa (~> 1)
      aws-sdk-neptune (~> 1)
      aws-sdk-networkfirewall (~> 1)
      aws-sdk-networkmanager (~> 1)
      aws-sdk-nimblestudio (~> 1)
      aws-sdk-opensearchservice (~> 1)
      aws-sdk-opsworks (~> 1)
      aws-sdk-opsworkscm (~> 1)
      aws-sdk-organizations (~> 1)
      aws-sdk-outposts (~> 1)
      aws-sdk-panorama (~> 1)
      aws-sdk-personalize (~> 1)
      aws-sdk-personalizeevents (~> 1)
      aws-sdk-personalizeruntime (~> 1)
      aws-sdk-pi (~> 1)
      aws-sdk-pinpoint (~> 1)
      aws-sdk-pinpointemail (~> 1)
      aws-sdk-pinpointsmsvoice (~> 1)
      aws-sdk-pinpointsmsvoicev2 (~> 1)
      aws-sdk-polly (~> 1)
      aws-sdk-pricing (~> 1)
      aws-sdk-prometheusservice (~> 1)
      aws-sdk-proton (~> 1)
      aws-sdk-qldb (~> 1)
      aws-sdk-qldbsession (~> 1)
      aws-sdk-quicksight (~> 1)
      aws-sdk-ram (~> 1)
      aws-sdk-rds (~> 1)
      aws-sdk-rdsdataservice (~> 1)
      aws-sdk-recyclebin (~> 1)
      aws-sdk-redshift (~> 1)
      aws-sdk-redshiftdataapiservice (~> 1)
      aws-sdk-rekognition (~> 1)
      aws-sdk-resiliencehub (~> 1)
      aws-sdk-resourcegroups (~> 1)
      aws-sdk-resourcegroupstaggingapi (~> 1)
      aws-sdk-robomaker (~> 1)
      aws-sdk-route53 (~> 1)
      aws-sdk-route53domains (~> 1)
      aws-sdk-route53recoverycluster (~> 1)
      aws-sdk-route53recoverycontrolconfig (~> 1)
      aws-sdk-route53recoveryreadiness (~> 1)
      aws-sdk-route53resolver (~> 1)
      aws-sdk-s3 (~> 1)
      aws-sdk-s3control (~> 1)
      aws-sdk-s3outposts (~> 1)
      aws-sdk-sagemaker (~> 1)
      aws-sdk-sagemakeredgemanager (~> 1)
      aws-sdk-sagemakerfeaturestoreruntime (~> 1)
      aws-sdk-sagemakerruntime (~> 1)
      aws-sdk-savingsplans (~> 1)
      aws-sdk-schemas (~> 1)
      aws-sdk-secretsmanager (~> 1)
      aws-sdk-securityhub (~> 1)
      aws-sdk-serverlessapplicationrepository (~> 1)
      aws-sdk-servicecatalog (~> 1)
      aws-sdk-servicediscovery (~> 1)
      aws-sdk-servicequotas (~> 1)
      aws-sdk-ses (~> 1)
      aws-sdk-sesv2 (~> 1)
      aws-sdk-shield (~> 1)
      aws-sdk-signer (~> 1)
      aws-sdk-simpledb (~> 1)
      aws-sdk-sms (~> 1)
      aws-sdk-snowball (~> 1)
      aws-sdk-snowdevicemanagement (~> 1)
      aws-sdk-sns (~> 1)
      aws-sdk-sqs (~> 1)
      aws-sdk-ssm (~> 1)
      aws-sdk-ssmcontacts (~> 1)
      aws-sdk-ssmincidents (~> 1)
      aws-sdk-ssoadmin (~> 1)
      aws-sdk-ssooidc (~> 1)
      aws-sdk-states (~> 1)
      aws-sdk-storagegateway (~> 1)
      aws-sdk-support (~> 1)
      aws-sdk-swf (~> 1)
      aws-sdk-synthetics (~> 1)
      aws-sdk-textract (~> 1)
      aws-sdk-timestreamquery (~> 1)
      aws-sdk-timestreamwrite (~> 1)
      aws-sdk-transcribeservice (~> 1)
      aws-sdk-transcribestreamingservice (~> 1)
      aws-sdk-transfer (~> 1)
      aws-sdk-translate (~> 1)
      aws-sdk-voiceid (~> 1)
      aws-sdk-waf (~> 1)
      aws-sdk-wafregional (~> 1)
      aws-sdk-wafv2 (~> 1)
      aws-sdk-wellarchitected (~> 1)
      aws-sdk-workdocs (~> 1)
      aws-sdk-worklink (~> 1)
      aws-sdk-workmail (~> 1)
      aws-sdk-workmailmessageflow (~> 1)
      aws-sdk-workspaces (~> 1)
      aws-sdk-workspacesweb (~> 1)
      aws-sdk-xray (~> 1)
    aws-sdk-robomaker (1.51.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-route53 (1.62.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-route53domains (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-route53recoverycluster (1.11.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-route53recoverycontrolconfig (1.10.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-route53recoveryreadiness (1.10.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-route53resolver (1.37.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-s3 (1.114.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.4)
    aws-sdk-s3control (1.50.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-s3outposts (1.13.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-sagemaker (1.124.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-sagemakeredgemanager (1.11.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-sagemakerfeaturestoreruntime (1.12.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-sagemakerruntime (1.42.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-savingsplans (1.26.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-schemas (1.23.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-secretsmanager (1.60.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-securityhub (1.65.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-serverlessapplicationrepository (1.43.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-servicecatalog (1.70.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-servicediscovery (1.46.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-servicequotas (1.23.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ses (1.47.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-sesv2 (1.27.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-shield (1.48.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-signer (1.38.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-simpledb (1.35.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv2 (~> 1.0)
    aws-sdk-sms (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-snowball (1.49.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-snowdevicemanagement (1.7.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-sns (1.53.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-sqs (1.51.1)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ssm (1.137.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ssmcontacts (1.13.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ssmincidents (1.13.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ssoadmin (1.16.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ssooidc (1.19.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-states (1.48.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-storagegateway (1.68.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-support (1.41.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-swf (1.36.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-synthetics (1.27.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-textract (1.38.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-timestreamquery (1.16.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-timestreamwrite (1.14.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-transcribeservice (1.74.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-transcribestreamingservice (1.42.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-transfer (1.53.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-translate (1.44.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-voiceid (1.6.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-waf (1.47.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-wafregional (1.48.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-wafv2 (1.39.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-wellarchitected (1.15.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-workdocs (1.39.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-worklink (1.33.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-workmail (1.49.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-workmailmessageflow (1.21.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-workspaces (1.67.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-workspacesweb (1.3.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-xray (1.47.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sigv2 (1.1.0)
    aws-sigv4 (1.5.0)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.2.0)
    bcrypt (3.1.20)
    better_errors (2.9.1)
      coderay (>= 1.0.0)
      erubi (>= 1.0.0)
      rack (>= 0.9.0)
    bindex (0.8.1)
    binding_of_caller (1.0.0)
      debug_inspector (>= 0.0.1)
    bootstrap (4.1.3)
      autoprefixer-rails (>= 6.0.3)
      popper_js (>= 1.12.9, < 2)
      sass (>= 3.5.2)
    bugsnag (6.24.2)
      concurrent-ruby (~> 1.0)
    builder (3.3.0)
    bullet (7.0.1)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    byebug (11.1.3)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    carrierwave (2.2.2)
      activemodel (>= 5.0.0)
      activesupport (>= 5.0.0)
      addressable (~> 2.6)
      image_processing (~> 1.1)
      marcel (~> 1.0.0)
      mini_mime (>= 0.1.3)
      ssrf_filter (~> 1.0)
    choice (0.2.0)
    chronic (0.10.2)
    clearbit (0.3.3)
      nestful (~> 1.1.0)
    climate_control (0.2.0)
    coderay (1.1.3)
    concurrent-ruby (1.2.3)
    connection_pool (2.4.1)
    countries (4.2.3)
      i18n_data (~> 0.16.0)
      sixarm_ruby_unaccent (~> 1.1)
    crack (0.4.5)
      rexml
    crass (1.0.6)
    creek (2.6.3)
      nokogiri (>= 1.10.0)
      rubyzip (>= 1.0.0)
    css_parser (1.11.0)
      addressable
    dalli (2.7.6)
    database_cleaner (2.0.1)
      database_cleaner-active_record (~> 2.0.0)
    database_cleaner-active_record (2.0.1)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.3.4)
    debug_inspector (1.1.0)
    declarative (0.0.20)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise_invitable (2.0.9)
      actionmailer (>= 5.0)
      devise (>= 4.6)
    diff-lcs (1.5.0)
    digest (3.1.1)
    docile (1.4.0)
    domain_name (0.5.20190701)
      unf (>= 0.0.5, < 1.0.0)
    dotenv (2.7.6)
    dotenv-rails (2.7.6)
      dotenv (= 2.7.6)
      railties (>= 3.2)
    email_reply_parser (0.5.10)
    erubi (1.13.0)
    et-orbi (1.2.11)
      tzinfo
    excon (0.92.3)
    execjs (2.8.1)
    factory_bot (6.2.1)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.2.0)
      factory_bot (~> 6.2.0)
      railties (>= 5.0.0)
    faker (2.20.0)
      i18n (>= 1.8.11, < 2)
    faraday (0.17.5)
      multipart-post (>= 1.2, < 3)
    faraday-detailed_logger (2.5.0)
      faraday (>= 0.16, < 3)
    faraday_middleware (0.14.0)
      faraday (>= 0.7.4, < 1.0)
    fcm (0.0.6)
      httparty (~> 0.10, >= 0.10.0)
    ffi (1.15.5)
    fix-db-schema-conflicts (3.1.0)
      rubocop (>= 0.38.0)
    flamegraph (0.9.5)
    fog-aws (3.13.0)
      fog-core (~> 2.1)
      fog-json (~> 1.1)
      fog-xml (~> 0.1)
    fog-core (2.2.4)
      builder
      excon (~> 0.71)
      formatador (~> 0.2)
      mime-types
    fog-json (1.2.0)
      fog-core
      multi_json (~> 1.10)
    fog-xml (0.1.4)
      fog-core
      nokogiri (>= 1.5.11, < 2.0.0)
    foreman (0.87.2)
    formadmin (0.2.1)
      activeadmin
    formatador (0.3.0)
    formtastic (4.0.0)
      actionpack (>= 5.2.0)
    formtastic_i18n (0.7.0)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    fuzzy_match (2.1.0)
    gems (1.2.0)
    geocoder (1.7.5)
    gli (2.21.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-api-client (0.53.0)
      google-apis-core (~> 0.1)
      google-apis-generator (~> 0.1)
    google-apis-core (0.4.2)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (>= 0.16.2, < 2.a)
      httpclient (>= 2.8.1, < 3.a)
      mini_mime (~> 1.0)
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
      rexml
      webrick
    google-apis-discovery_v1 (0.8.0)
      google-apis-core (>= 0.4, < 2.a)
    google-apis-generator (0.4.1)
      activesupport (>= 5.0)
      gems (~> 1.2)
      google-apis-core (>= 0.4, < 2.a)
      google-apis-discovery_v1 (~> 0.5)
      thor (>= 0.20, < 2.a)
    google-protobuf (3.25.2)
    googleapis-common-protos-types (1.11.0)
      google-protobuf (~> 3.18)
    googleauth (1.1.3)
      faraday (>= 0.17.3, < 3.a)
      jwt (>= 1.4, < 3.0)
      memoist (~> 0.16)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    grpc (1.60.0)
      google-protobuf (~> 3.25)
      googleapis-common-protos-types (~> 1.0)
    has_scope (0.8.0)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
    hashdiff (1.0.1)
    hashie (5.0.0)
    htmlentities (4.3.4)
    http-accept (1.7.0)
    http-cookie (1.0.4)
      domain_name (~> 0.5)
    httparty (0.20.0)
      mime-types (~> 3.0)
      multi_xml (>= 0.5.2)
    httpclient (2.8.3)
    i18n (1.14.6)
      concurrent-ruby (~> 1.0)
    i18n_data (0.16.0)
      simple_po_parser (~> 1.1)
    image_processing (1.12.2)
      mini_magick (>= 4.9.5, < 5)
      ruby-vips (>= 2.0.17, < 3)
    inherited_resources (1.13.1)
      actionpack (>= 5.2, < 7.1)
      has_scope (~> 0.6)
      railties (>= 5.2, < 7.1)
      responders (>= 2, < 4)
    intercom (4.2.1)
    intercom-rails (1.0.6)
      activesupport (> 4.0)
      jwt (~> 2.0)
    io-console (0.7.2)
    ipaddress (0.8.3)
    irb (1.13.1)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.1)
    jquery-rails (4.4.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    json (2.5.1)
    jwt (2.3.0)
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    launchy (2.5.2)
      addressable (~> 2.8)
    letter_opener (1.8.1)
      launchy (>= 2.2, < 3)
    listen (3.0.8)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
    logger (1.6.1)
    loofah (2.22.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    malloc_trim (0.1.0)
    marcel (1.0.2)
    matrix (0.4.2)
    memoist (0.16.2)
    method_source (1.1.0)
    mime-types (3.4.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2022.0105)
    mimemagic (0.3.10)
      nokogiri (~> 1)
      rake
    mini_magick (4.11.0)
    mini_mime (1.1.5)
    mini_portile2 (2.8.7)
    minitest (5.25.1)
    mixpanel-ruby (2.2.2)
    multi_json (1.15.0)
    multi_xml (0.6.0)
    multipart-post (2.1.1)
    mustermann (1.1.1)
      ruby2_keywords (~> 0.0.1)
    nestful (1.1.4)
    net-imap (0.4.16)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.0)
      net-protocol
    net-ssh (6.1.0)
    netrc (0.11.0)
    newrelic-infinite_tracing (9.15.0)
      google-protobuf (< 4.0)
      grpc (~> 1.34)
      newrelic_rpm (= 9.15.0)
    newrelic_rpm (9.15.0)
    nio4r (2.5.9)
    no_cache_control (1.0.0)
    nokogiri (1.16.7)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    oauth (0.5.10)
    oauth2 (2.0.9)
      faraday (>= 0.17.3, < 3.0)
      jwt (>= 1.0, < 3.0)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    oj (3.15.1)
    olive_branch (4.0.1)
      multi_json
      rails (>= 4.0)
    omniauth (2.1.0)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    onelogin (1.6.0)
      httparty (>= 0.13.7)
      nokogiri (>= *******)
    orm_adapter (0.5.0)
    os (1.1.4)
    paperclip (6.0.0)
      activemodel (>= 4.2.0)
      activesupport (>= 4.2.0)
      mime-types
      mimemagic (~> 0.3.0)
      terrapin (~> 0.6.0)
    parallel (1.22.1)
    paranoia (2.6.0)
      activerecord (>= 5.1, < 7.1)
    parser (*******)
      ast (~> 2.4.1)
    pg (1.5.9)
    pg_search (2.3.6)
      activerecord (>= 5.2)
      activesupport (>= 5.2)
    pkg-config (1.5.5)
    plaid (12.0.0)
      faraday
      faraday_middleware
      hashie (>= 3.4.3)
    popper_js (1.16.0)
    premailer (1.16.0)
      addressable
      css_parser (>= 1.6.0)
      htmlentities (>= 4.0.0)
    premailer-rails (1.11.1)
      actionmailer (>= 3)
      premailer (~> 1.7, >= 1.7.9)
    prometheus-client (4.2.3)
      base64
    pry (0.14.1)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-rails (0.3.9)
      pry (>= 0.10.4)
    psych (3.3.4)
    public_suffix (5.0.3)
    puma (6.4.2)
      nio4r (~> 2.0)
    puma-metrics (1.4.0)
      prometheus-client (>= 0.10)
      puma (>= 6.0)
    puppeteer-ruby (0.45.3)
      concurrent-ruby (>= 1.1, < 1.3)
      mime-types (>= 3.0)
      websocket-driver (>= 0.6.0)
    pusher (1.3.3)
      httpclient (~> 2.7)
      multi_json (~> 1.0)
      pusher-signature (~> 0.1.8)
    pusher-signature (0.1.8)
    qbo_api (2.0.2)
      faraday
      faraday-detailed_logger
      faraday_middleware
      nokogiri
    quickchart (1.2.1)
    raabro (1.4.0)
    racc (1.8.1)
    rack (2.2.9)
    rack-attack (6.6.1)
      rack (>= 1.0, < 3)
    rack-cors (1.1.1)
      rack (>= 2.0.0)
    rack-mini-profiler (3.0.0)
      rack (>= 1.2.0)
    rack-protection (2.2.0)
      rack
    rack-proxy (0.7.7)
      rack
    rack-test (2.1.0)
      rack (>= 1.3)
    rack-utf8_sanitizer (1.10.1)
      rack (>= 1.0, < 4.0)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-erd (1.6.1)
      activerecord (>= 4.2)
      activesupport (>= 4.2)
      choice (~> 0.2.0)
      ruby-graphviz (~> 1.2)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rainbow (3.1.1)
    rake (13.2.1)
    ransack (3.1.0)
      activerecord (>= 6.0.4)
      activesupport (>= 6.0.4)
      i18n
    rb-fsevent (0.11.1)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    rdoc (6.3.4.1)
    recaptcha (5.19.0)
    redcarpet (3.5.1)
    redis (4.8.1)
    redis-client (0.22.2)
      connection_pool
    regexp_parser (2.8.2)
    reline (0.5.7)
      io-console (~> 0.5)
    representable (3.1.1)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    retriable (3.1.2)
    rexml (3.2.6)
    rmagick (5.3.0)
      pkg-config (~> 1.4)
    roo (2.7.1)
      nokogiri (~> 1)
      rubyzip (~> 1.1, < 2.0.0)
    rotp (6.2.2)
    rspec-core (3.11.0)
      rspec-support (~> 3.11.0)
    rspec-expectations (3.11.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.11.0)
    rspec-mocks (3.11.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.11.0)
    rspec-rails (5.1.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      railties (>= 5.2)
      rspec-core (~> 3.10)
      rspec-expectations (~> 3.10)
      rspec-mocks (~> 3.10)
      rspec-support (~> 3.10)
    rspec-support (3.11.0)
    rubocop (1.29.0)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.17.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 1.4.0, < 3.0)
    rubocop-ast (1.17.0)
      parser (>= *******)
    rubocop-rails (2.15.2)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.7.0, < 2.0)
    ruby-graphviz (1.2.5)
      rexml
    ruby-progressbar (1.11.0)
    ruby-vips (2.1.4)
      ffi (~> 1.12)
    ruby2_keywords (0.0.5)
    rubyXL (3.3.29)
      nokogiri (>= 1.4.4)
      rubyzip (>= 1.1.6)
    rubyzip (1.3.0)
    rufus-scheduler (3.9.2)
      fugit (~> 1.1, >= 1.11.1)
    sanitize (6.0.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    sass (3.7.4)
      sass-listen (~> 4.0.0)
    sass-listen (4.0.0)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
    sass-rails (5.1.0)
      railties (>= 5.2.0)
      sass (~> 3.1)
      sprockets (>= 2.8, < 4.0)
      sprockets-rails (>= 2.0, < 4.0)
      tilt (>= 1.1, < 3)
    sawyer (0.8.2)
      addressable (>= 2.3.5)
      faraday (> 0.8, < 2.0)
    scout_apm (5.3.5)
      parser
    select2-rails (4.0.13)
    selenium-webdriver (4.10.0)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    semantic_range (3.0.0)
    shared-mime-info (0.2.5)
    shoulda-callback-matchers (1.1.4)
      activesupport (>= 3)
    shoulda-matchers (5.1.0)
      activesupport (>= 5.2.0)
    sidekiq (7.3.5)
      connection_pool (>= 2.3.0)
      logger
      rack (>= 2.2.4)
      redis-client (>= 0.22.2)
    sidekiq-limit_fetch (4.4.1)
      sidekiq (>= 6)
    sidekiq-scheduler (5.0.6)
      rufus-scheduler (~> 3.2)
      sidekiq (>= 6, < 8)
      tilt (>= 1.4.0, < 3)
    sidekiq-unique-jobs (8.0.10)
      concurrent-ruby (~> 1.0, >= 1.0.5)
      sidekiq (>= 7.0.0, < 8.0.0)
      thor (>= 1.0, < 3.0)
    signet (0.16.1)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.0)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simple_command (1.0.1)
    simple_po_parser (1.1.6)
    simplecov (0.21.2)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.12.3)
    simplecov_json_formatter (0.1.4)
    simpleidn (0.2.1)
      unf (~> 0.1.4)
    sinatra (2.2.0)
      mustermann (~> 1.0)
      rack (~> 2.2)
      rack-protection (= 2.2.0)
      tilt (~> 2.0)
    sixarm_ruby_unaccent (1.2.0)
    slack-ruby-client (0.14.6)
      activesupport
      faraday (>= 0.9)
      faraday_middleware
      gli
      hashie
      websocket-driver
    slack_mrkdwn (2.2.1)
      redcarpet (>= 3.5.1)
    slim (4.1.0)
      temple (>= 0.7.6, < 0.9)
      tilt (>= 2.0.6, < 2.1)
    slim-rails (3.4.0)
      actionpack (>= 3.1)
      railties (>= 3.1)
      slim (>= 3.0, < 5.0)
    snaky_hash (2.0.1)
      hashie
      version_gem (~> 1.1, >= 1.1.1)
    spring (3.0.0)
    sprockets (3.7.5)
      base64
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    ssrf_filter (1.0.7)
    stackprof (0.2.19)
    string-similarity (2.1.0)
    stripe (5.26.0)
    temple (0.8.2)
    terrapin (0.6.0)
      climate_control (>= 0.0.3, < 1.0)
    thor (1.3.2)
    tilt (2.0.10)
    timecop (0.9.5)
    timeout (0.4.1)
    traceroute (0.8.1)
      rails (>= 3.0.0)
    trailblazer-option (0.1.2)
    truemail (3.0.0)
      simpleidn (~> 0.2.1)
    turbolinks (5.2.1)
      turbolinks-source (~> 5.2)
    turbolinks-source (5.2.0)
    twilio-ruby (5.5.1)
      faraday (~> 0.9)
      jwt (>= 1.5, <= 2.5)
      nokogiri (>= 1.6, < 2.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uber (0.1.0)
    uglifier (4.2.1)
      execjs (>= 0.3.0, < 3)
    unf (0.1.4)
      unf_ext
    unf_ext (*******)
    unicode-display_width (2.1.0)
    unifi (0.1.0)
      httparty (~> 0)
    uniform_notifier (1.16.0)
    vanilla-ujs (1.3.0)
      railties (>= 4.2.0)
    version_gem (1.1.4)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.2.0)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webdrivers (5.3.1)
      nokogiri (~> 1.6)
      rubyzip (>= 1.3.0)
      selenium-webdriver (~> 4.0, < 4.11)
    webmock (3.14.0)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webpacker (5.0.0)
      activesupport (>= 5.2)
      rack-proxy (>= 0.6.1)
      railties (>= 5.2)
      semantic_range (>= 2.3.0)
    webrick (1.7.0)
    websocket (1.2.10)
    websocket-driver (0.7.5)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    whenever (1.0.0)
      chronic (>= 0.6.3)
    wicked_pdf (2.6.2)
      activesupport
    will_paginate (3.3.1)
    wkhtmltopdf-binary (0.12.4)
    working_hours (1.4.1)
      activesupport (>= 3.2)
      tzinfo
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.6.18)

PLATFORMS
  ruby

DEPENDENCIES
  active_record_doctor
  active_storage_validations
  activeadmin
  activeadmin-searchable_select
  activerecord-import (~> 1.8, >= 1.8.1)
  activerecord-session_store (>= 1.1.3)
  audited
  aws-sdk (~> 3)
  better_errors
  binding_of_caller
  bootstrap (~> 4.1.3)
  bugsnag
  bullet
  byebug
  capybara (~> 3.40)
  carrierwave
  clearbit
  countries (~> 4.1)
  creek (~> 2.6, >= 2.6.3)
  dalli (~> 2.7, >= 2.7.6)
  database_cleaner
  devise (~> 4.9, >= 4.9.4)
  devise_invitable (~> 2.0, >= 2.0.9)
  digest (~> 3.1)
  dotenv-rails
  email_reply_parser
  factory_bot_rails
  faker
  fcm
  fix-db-schema-conflicts
  flamegraph
  fog-aws
  foreman (~> 0.87.2)
  formadmin
  fuzzy_match
  geocoder
  google-api-client
  googleauth
  grpc (~> 1.60)
  htmlentities (~> 4.3, >= 4.3.4)
  intercom (= 4.2.1)
  intercom-rails (= 1.0.6)
  ipaddress
  irb (~> 1.4, >= 1.4.1)
  jbuilder (~> 2.13)
  json (= 2.5.1)
  jwt
  letter_opener
  listen (~> 3.0.5)
  malloc_trim
  mini_magick
  mini_portile2 (~> 2.8, >= 2.8.7)
  mixpanel-ruby (~> 2.2)
  net-ssh
  newrelic-infinite_tracing (~> 9.15)
  newrelic_rpm (~> 9.15)
  nio4r (~> 2.5.9)
  no_cache_control (~> 1.0)
  nokogiri
  oauth2 (~> 2.0, >= 2.0.9)
  oj (~> 3.15.1)
  oktakit!
  olive_branch
  omniauth
  onelogin
  paperclip (~> 6.0.0)
  paranoia (~> 2.2)
  pg (~> 1.5, >= 1.5.9)
  pg_search
  plaid (~> 12.0.0)
  premailer-rails
  pry-rails
  psych (< 4)
  puma (= 6.4.2)
  puma-metrics
  puppeteer-ruby
  pusher (~> 1.3.1)
  qbo_api
  quickchart
  rack-attack
  rack-cors
  rack-mini-profiler
  rack-utf8_sanitizer
  rails (~> 7.0.2)
  rails-controller-testing
  rails-erd
  recaptcha (~> 5.19)
  redis (~> 4.0)
  rest-client
  rmagick
  roo (~> 2.7.0)
  rotp
  rspec-rails
  rubocop-rails
  rubyXL (= 3.3.29)
  rubyzip
  sanitize
  sass-rails (~> 5)
  scout_apm
  selenium-webdriver
  shared-mime-info
  shoulda-callback-matchers (~> 1.1.1)
  shoulda-matchers
  sidekiq (~> 7.3, >= 7.3.5)
  sidekiq-limit_fetch (~> 4.4, >= 4.4.1)
  sidekiq-scheduler (~> 5.0, >= 5.0.6)
  sidekiq-unique-jobs (~> 8.0, >= 8.0.10)
  signet
  simple_command
  simplecov
  sinatra
  slack-ruby-client
  slack_mrkdwn
  slim-rails
  spring (~> 3.0.0)
  stackprof
  string-similarity
  stripe (~> 5.26.0)
  timecop
  traceroute
  truemail
  turbolinks (~> 5)
  twilio-ruby (~> 5.5.0)
  tzinfo-data
  uglifier (~> 4.2, >= 4.2.1)
  unifi
  vanilla-ujs
  web-console (>= 3.3.0)
  webdrivers
  webmock
  webpacker (= 5.0.0)
  whenever
  wicked_pdf
  will_paginate (~> 3.3.1)
  wkhtmltopdf-binary (= 0.12.4)
  working_hours
  xeroizer!

BUNDLED WITH
   2.5.4
