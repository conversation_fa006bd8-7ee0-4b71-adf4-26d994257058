x-sidekiq-service: &sidekiq-service
  working_dir: /app
  environment:
    DOCKER: true
  build:
    context: .
    dockerfile: Dockerfile-dev
  volumes:
    - .:/app
    - bundle_gems:/home/<USER>/.asdf/installs/ruby/3.2.0/lib/ruby/gems/3.2.0/gems
    - node_modules:/app/node_modules
  networks:
    - genuity
  depends_on:
    psql:
      condition: service_healthy
    redis:
      condition: service_healthy
  scale: 0
  
services:
  psql:
    image: postgres:16
    container_name: psql
    environment:
      DOCKER: true
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: nulodgic_development
    volumes:
      - .:/app
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - genuity
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G

  redis:
    image: redis:latest
    container_name: redis
    environment:
      DOCKER: true
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - genuity
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M

  genuity-webapp: &genuity-core
    container_name: genuity-webapp
    build:
      context: .
      dockerfile: Dockerfile-dev
      args:
        precompileassets: 'not'
    user: "genuity:genuity"
    init: true
    working_dir: /app
    command: >
      sh -c "
        bundle install && \
        ./bin/rails db:prepare && \
        rm -f tmp/pids/server.pid && \
        ./bin/rails s -b 0.0.0.0"
    volumes:
      - .:/app
      - bundle_gems:/home/<USER>/.asdf/installs/ruby/3.2.0/lib/ruby/gems/3.2.0/gems
      - node_modules:/app/node_modules
      - /tmp/.X11-unix:/tmp/.X11-unix
    environment:
      DOCKER: true
      LETTER_OPENER: 'web'
      LAUNCHY_DRY_RUN: true
      BROWSER: /dev/null
      DISPLAY: ":99"
    depends_on:
      psql:
        condition: service_healthy
      redis:
        condition: service_healthy
      genuity-webpacker:
        condition: service_healthy
    ports:
      - 3000:3000
    networks:
      - genuity
    stdin_open: true
    tty: true
    #scale: 0

  genuity-webpacker: 
    container_name: genuity-webpacker
    build:
      context: .
      dockerfile: Dockerfile-dev
    user: "genuity:genuity"
    init: true
    working_dir: /app
    command: >
      sh -c "
        yarn install && \
        node_modules/.bin/webpack-dev-server --config config/webpack/development.js"
    volumes:
      - .:/app
      - bundle_gems:/home/<USER>/.asdf/installs/ruby/3.2.0/lib/ruby/gems/3.2.0/gems
      - node_modules:/app/node_modules
    environment:
      DOCKER: true
      NODE_ENV: development
      RAILS_ENV: development
      WEBPACKER_DEV_SERVER_HOST: 0.0.0.0
    healthcheck:
      test: ["CMD", "curl", "--connect-timeout", "2", "-s", "-o", "/dev/null", "-f", "http://localhost:3035/sockjs-node/info"]
      interval: 30s
      timeout: 10s
      retries: 10
    depends_on:
      psql:
        condition: service_healthy
      redis:
        condition: service_healthy
    ports:
      - 3035:3035
    networks:
      - genuity
    #scale: 0

  genuity-sidekiq-assets:
    <<: *sidekiq-service
    container_name: sidekiq-assets
    command: >
      sh -c "
          bundle install && \
        ./bin/bundle exec sidekiq -C config/sidekiq_assets.yml"

  genuity-sidekiq-at-actions-2:
    <<: *sidekiq-service
    container_name: sidekiq-at-actions-2
    command: >
      sh -c "
          bundle install && \
        ./bin/bundle exec sidekiq -C config/sidekiq_at_actions_2.yml"
  
  genuity-sidekiq-at-actions:
    <<: *sidekiq-service
    container_name: sidekiq-at-actions
    command: >
      sh -c "
          bundle install && \
        ./bin/bundle exec sidekiq -C config/sidekiq_at_actions.yml"

  genuity-sidekiq-automated-tasks-2:
    <<: *sidekiq-service
    container_name: automated-tasks-2
    command: >
      sh -c "
          bundle install && \
        ./bin/bundle exec sidekiq -C config/sidekiq_automated_tasks_2.yml"

  genuity-sidekiq-automated-tasks:
    <<: *sidekiq-service
    container_name: automated-tasks
    command: >
      sh -c "
          bundle install && \
        ./bin/bundle exec sidekiq -C config/sidekiq_automated_tasks.yml"

  genuity-sidekiq-critical-2:
    <<: *sidekiq-service
    container_name: sidekiq-critical-2
    command: >
      sh -c "
          bundle install && \
        ./bin/bundle exec sidekiq -C config/sidekiq_critical_2.yml"

  genuity-sidekiq-critical:
    <<: *sidekiq-service
    container_name: sidekiq-critical
    command: >
      sh -c "
          bundle install && \
        ./bin/bundle exec sidekiq -C config/sidekiq_critical.yml"

  genuity-sidekiq-default:
    <<: *sidekiq-service
    container_name: sidekiq-default
    command: >
      sh -c "
          bundle install && \
        ./bin/bundle exec sidekiq -C config/sidekiq_default.yml"

  genuity-sidekiq-email:
    <<: *sidekiq-service
    container_name: sidekiq-email
    command: >
      sh -c "
          bundle install && \
        ./bin/bundle exec sidekiq -C config/sidekiq_email.yml"

  genuity-sidekiq-field-permission:
    <<: *sidekiq-service
    container_name: sidekiq-field-permission
    command: >
      sh -c "
          bundle install && \
        ./bin/bundle exec sidekiq -C config/sidekiq_field_permissions.yml"

  genuity-sidekiq-import:
    <<: *sidekiq-service
    container_name: sidekiq-import
    command: >
      sh -c "
          bundle install && \
        ./bin/bundle exec sidekiq -C config/sidekiq_import.yml"

  genuity-sidekiq-integrations:
    <<: *sidekiq-service
    container_name: sidekiq-integrations
    command: >
      sh -c "
          bundle install && \
        ./bin/bundle exec sidekiq -C config/sidekiq_integrations.yml"
    
networks:
  genuity:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
  node_modules:
  bundle_gems:
