<!DOCTYPE html>
<html lang="en">
<head>
  <style>
    html { height: 100%; }
    body {
      margin: 0;
      height: 100%;
      box-sizing: border-box;
      overflow: hidden;
      font-family: 'Lato', sans-serif;
    }
    #btn {
      font-size: 16px;
      color: #fff;
      background-color: #0D6EFD;
      width: 100%;
      height: 100%;
      cursor: pointer;
      border: 1px solid transparent;
      border-radius: 2px;
      font-family: 'Lato', sans-serif;
    }
    #btn:hover { 
      background-color: #025ce2;
    }
  </style>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OneDrive File Picker</title>
</head>
<body>
  <button id="btn">Sign in with Microsoft</button>
  <script src="https://js.live.net/v7.2/OneDrive.js"></script>
  <script>
    let origin = null;
    let cid = null;

    window.onload = function() {
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.get('open') === 'true') {
        origin = urlParams.get('origin');
        cid = urlParams.get('cid');
      }
    };

    document.getElementById('btn').addEventListener("click", runScript);

    async function runScript() {
      postMsg('opened', {});
      const options = {
        clientId: cid,
        action: 'download',
        advanced: {
          filter: '.docx',
          scopes: 'Files.Read.All',
          navigation: {
            sourceTypes: 'OneDrive'
          }
        },
        success: function(files) {
          postMsg('success', files);
        },
        error: function(error) {
          postMsg('error', error);
        },
      };

      try {
        window.OneDrive.open(options);
      } catch (e) {
        postMsg('windowError', error);
      }
    }
    function postMsg(actionType, event) {
      window.parent.postMessage({ type: 'oneDrive', actionType, event, origin }, '*');
    }
  </script>
</body>
</html>
