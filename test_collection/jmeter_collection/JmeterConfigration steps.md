################################### Steps to Execute JMX Scripts in JMeter ###################################

Install JMeter

Step 1: Update Package List Open your terminal and update the package list to ensure you have the latest information about available packages:
Command: sudo apt update 

Step 2: Use the following command to install JMeter:
Command: sudo apt install jmeter
This command will download and install the Apache JMeter package from the official Ubuntu repositories.

Step 3: Verify Installation After the installation is complete, you can verify the JMeter installation by running the following command:

Command: jmeter \--version
This command should display the version information of J<PERSON>eter, indicating that the installation was successful.

Step 4: Run JMeter You can start JMeter by simply typing:

Command: jmeter
This will launch the JMeter GUI, and you can start creating and running your test plans.

3\. Open JMeter UI and navigate to the \"File\" option at the top-left
corner (a dropdown will appear). 
4. Select \"Open\" from the dropdown.
5. Choose the desired JMeter JMX file. 6. The file will be loaded into JMeter.
7. Now, select \"bzm - Random CSV Data Set Config.\" 
8. Remove the existing file name.
9. Create a CSV file with the following 5 columns: 
   a. Production URL 
   b. Email 
   c. Password 
   d. CID (Company ID) 
   e. WID (Workspace ID) 
10. Populate the CSV file with relevant data.
11. Browse the created CSV file in the \"filename\" field of \"bzm - Random CSV Data Set Config.\"

Note:

JMX files store the test plan configuration, so importing a JMX file
essentially brings in the entire test plan structure, including thread
groups, samplers, listeners, and other elements. Ensure that any
external files (e.g., data files, CSV files) referenced in the test plan
are in the correct locations.

################################### Steps to install Influx DB on ubuntu linux on Ubuntu 22.04.3 jammy ###################################

Step 1: Update Package List Open your terminal and update the package
list to make sure you have the latest information about available
packages:

Command: sudo apt update

Step 2: Install InfluxDB Now, you can install InfluxDB using the
following

Command: sudo apt install influxdb

Step 3: Start InfluxDB Service After the installation is complete, start
the InfluxDB service:

Command: sudo service influxdb start

Step 4: Enable InfluxDB to Start on Boot To ensure that InfluxDB starts
automatically when the system boots up, run the following command:

Command: sudo systemctl enable influxdb

Step 5: Verify InfluxDB Installation You can verify the installation by
checking the InfluxDB service status:

Command: sudo service influxdb status

This should show that InfluxDB is active and running.

Step 6: Access InfluxDB Shell You can access the InfluxDB shell by
typing:

Command: influx

This will open the InfluxDB shell where you can interact with the
database.

Step 7: Create a Database Inside the InfluxDB shell, you can create a
new database. For example, let\'s create a database named \"jmeter\":

CREATE DATABASE mydatabase

Now you have InfluxDB installed and running on your Ubuntu system.

################################### Configure InfluxDB with JMeter ###################################

Step 1: Install JMeter InfluxDB Listener Plugin JMeter doesn\'t have
native support for InfluxDB, so you\'ll need to use a third-party
listener. One popular choice is the \"Backend Listener\" that is already
being used in jmeter test suite for GoGenuity this plugin is provided by
the InfluxDB Backend Listener Plugin.

Download the plugin from JMeter Plugins Manager by following the steps
given below:

Step 1: Download JMeter Plugins Manager Visit:
https://github.com/undera/jmeter-plugins-manager. 
Download the latest
release JAR file. It is usually named jmeter-plugins-manager-x.x.jar,
where \"x.x\" is the version number. Step 2: Place JAR File in JMeter\'s
lib/ext Directory Locate your JMeter installation directory. Navigate to
the lib/ext directory inside the JMeter installation directory. Copy the
downloaded jmeter-plugins-manager-x.x.jar file into the lib/ext
directory. Step 3: Restart JMeter Now, restart JMeter to complete the
installation process.

Step 4: Open Plugins Manager in JMeter Once JMeter is open, go to
\"Options\" in the top menu. Select \"Plugins Manager.\" 
Step 5: Configure InfluxDB Backend Listener in JMeter 
       a. Open JMeter. 
       b. Install \"Jmeter Listener pack\" using plugin manager. 
       c. Restart JMeter. Add a Test Plan: Right-click on the Test Plan \> Add \> Threads (Users) \> Thread Group.

Add Sampler(s) as needed.

Add the InfluxDB Backend Listener: Right-click on the Thread Group \>
Add \> Listener \> Backend Listener.

1\. Set the Backend Listener implementation to
org.apache.jmeter.visualizers.backend.influxdb.InfluxdbBackendListenerClient.

Configure the following parameters in backend listner:

2\. InfluxDB URL: http://localhost:8086/write?db=jmeterdb (Adjust the
URL based on your InfluxDB setup after \"=\" sign your db name will
come, that you created in influx db).

3\. Application: Enter a name for your application. (Optional)

4\. Measurement: Enter the table name that should be present inside
table name of influx db.

5\. summaryOnly: a. When set to true: Only summary results are sent to
the backend. Detailed metrics for each individual sample (request) are
not sent. Useful when you are primarily interested in high-level metrics
and don\'t need detailed information on every request.

b. When set to false (default):

Both detailed metrics and summary results are sent to the backend. This
includes information about each sample, such as response time, latency,
and other details. Useful for more in-depth analysis and debugging,
especially when you need information on each request.

6\. Keep rest of the values as default.

Step 6: Run your JMeter Test Run your JMeter test as you normally would.
The Backend Listener will send the results to InfluxDB.

Step 7: Verify Data in InfluxDB

Commands to check data in InfluxDB

1\. Open Terminal. 2. This command will start the influx DB. \> influx
(As now we are in database terminal so we will be using SQL) 3. This
command will show user all databases available in Influx DB \> SHOW
DATABASES 4. This command will show the coulmn values that are fetched
from jmeter in influxdb \> SHOW FIELD KEYS FROM \"jmeter\"

That\'s it! Now you have configured JMeter to send test results to
InfluxDB. Adjust the settings based on your specific requirements and
InfluxDB configuration.

################################### Grafana Configuration with InfluxDB ###################################

Step 1: Access Grafana Web Interface Grafana\'s default web interface
runs on port 3000. Open your web browser and navigate to
http://localhost:3000. The default login credentials are usually:

Username: admin Password: admin You will be prompted to change the
password upon the first login.

Step 2: Add InfluxDB as a Data Source in Grafana. 
1. Log in to Grafana.
2. In the Grafana web interface, navigate to \"Settings\" (gear icon on
the left sidebar) and select \"Data Sources.\" 
3. Click on \"Add your
first data source.\" 
4. Choose \"InfluxDB\" from the list of available
data sources. 
5. Configure the InfluxDB settings: 
  a. Name: Give your data source a name. 
  b. HTTP: Set to http://localhost:8086 (or adjust based on your InfluxDB setup). 
InfluxDB Details: 
  c. Database: Select the database you created in InfluxDB. 
  d. User: Set to your InfluxDB username. 
  e. Password: Set to your InfluxDB password.

Step 3: Import Grafana dashboard \"JMeter Dashboard (3.2 and up)\" (It
can be easily imported from grafana build in dashboards)

There are two ways to import grafana dashboard

1\. Import the Dashboard into Grafana 
    a. Open the Grafana web interface.
    b. n the left sidebar, click on the \"+\" icon and select \"Dashboard.\"
    c. Click on \"Import Dashboard.\" 
    d. Paste the previously copied JSON configuration into the \"Or paste JSON\" field. e. Click on \"Load\" to
       populate the dashboard preview. 
    f. Configure the settings as needed,including selecting the data source. 
    e. Click on \"Import\" to import the dashboard.

2\. Importing a Dashboard via File 
    a. If you have the dashboard configuration saved in a JSON file, you can import it using the following steps: 
    b. Open the Grafana web interface. 
    c. In the left sidebar, click on the \"+\" icon and select \"Dashboard.\" 
    d. Click on \"Import Dashboard.\" 
    e. Click on \"Upload .json File\" and select your saved JSON file. 
    f. Configure the settings as needed, including selecting the data source. 
    g. Click on \"Import\" to import the dashboard.
