{"info": {"_postman_id": "8b66a341-f872-4daf-bf6c-847cd2d12c71", "name": "Probe", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "30765253"}, "item": [{"name": "UploadInstallerInfo()", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"company_guid\":\"7e38674c-87fd-4c8d-95b2-5920c09e2d5c\",\"company_user_guid\":\"06252343-1b06-4522-a904-9d9c80e6f968\",\"mac_addresses\":[\"A4:97:B1:AA:C1:B7\"],\"sec_mac_addresses\":[\"00:15:5D:CE:DB:9C\",\"A6:97:B1:AA:C1:B7\",\"B6:97:B1:AA:C1:B7\",\"A4:97:B1:AA:C1:B7\",\"96:21:20:52:41:53\",\"9A:6C:20:52:41:53\",\"9E:D6:20:52:41:53\"],\"mac_add_details\":[{\"macAddress\":\"00:15:5D:CE:DB:9C\",\"machType\":\"Hyper-V Virtual Ethernet Adapter\",\"networkName\":\"vEthernet (WSL)\",\"networkType\":\"Ethernet\",\"hasGateway\":false},{\"macAddress\":\"A6:97:B1:AA:C1:B7\",\"machType\":\"Microsoft Wi-Fi Direct Virtual Adapter\",\"networkName\":\"Local Area Connection* 1\",\"networkType\":\"Wireless80211\",\"hasGateway\":false},{\"macAddress\":\"B6:97:B1:AA:C1:B7\",\"machType\":\"Microsoft Wi-Fi Direct Virtual Adapter #2\",\"networkName\":\"Local Area Connection* 10\",\"networkType\":\"Wireless80211\",\"hasGateway\":false},{\"macAddress\":\"A4:97:B1:AA:C1:B7\",\"machType\":\"Qualcomm QCA61x4A 802.11ac Wireless Adapter\",\"networkName\":\"Wi-Fi\",\"networkType\":\"Wireless80211\",\"hasGateway\":true},{\"macAddress\":\"96:21:20:52:41:53\",\"machType\":\"WAN Miniport (IP)\",\"networkName\":\"[00000009] WAN Miniport (IP)\",\"networkType\":null,\"hasGateway\":false},{\"macAddress\":\"9A:6C:20:52:41:53\",\"machType\":\"WAN Miniport (IPv6)\",\"networkName\":\"[00000010] WAN Miniport (IPv6)\",\"networkType\":null,\"hasGateway\":false},{\"macAddress\":\"9E:D6:20:52:41:53\",\"machType\":\"WAN Miniport (Network Monitor)\",\"networkName\":\"[00000011] WAN Miniport (Network Monitor)\",\"networkType\":null,\"hasGateway\":false}],\"computer_name\":\"DESKTOP-R83LFQL\",\"pc_serial_no\":\"DL75T93\",\"system_uuid\":\"4C4C4544-004C-3710-8035-C4C04F543933\",\"ip_address\":\"************\",\"app_version\":\"*******\"}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "secure.gogenuity-staging.com/api/v3/probe_locations.json", "host": ["secure", "gogenuity-staging", "com"], "path": ["api", "v3", "probe_locations.json"]}}, "response": []}, {"name": "update_available()", "request": {"method": "GET", "header": [], "url": {"raw": "assets-api.gogenuity-staging.com/api/v1/app_versions.json?company_guid=7e38674c-87fd-4c8d-95b2-5920c09e2d5c&app_version=*******&source=network_discovery", "host": ["assets-api", "gogenuity-staging", "com"], "path": ["api", "v1", "app_versions.json"], "query": [{"key": "company_guid", "value": "7e38674c-87fd-4c8d-95b2-5920c09e2d5c"}, {"key": "app_version", "value": "*******"}, {"key": "source", "value": "network_discovery"}]}}, "response": []}, {"name": "getConfigsFromRemote()", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"company_guid\":\"7e38674c-87fd-4c8d-95b2-5920c09e2d5c\",\"company_user_guid\":\"06252343-1b06-4522-a904-9d9c80e6f968\",\"mac_addresses\":[\"A4:97:B1:AA:C1:B7\"],\"sec_mac_addresses\":[\"00:15:5D:CE:DB:9C\",\"A6:97:B1:AA:C1:B7\",\"B6:97:B1:AA:C1:B7\",\"A4:97:B1:AA:C1:B7\",\"96:21:20:52:41:53\",\"9A:6C:20:52:41:53\",\"9E:D6:20:52:41:53\"],\"mac_add_details\":[{\"macAddress\":\"00:15:5D:CE:DB:9C\",\"machType\":\"Hyper-V Virtual Ethernet Adapter\",\"networkName\":\"vEthernet (WSL)\",\"networkType\":\"Ethernet\",\"hasGateway\":false},{\"macAddress\":\"A6:97:B1:AA:C1:B7\",\"machType\":\"Microsoft Wi-Fi Direct Virtual Adapter\",\"networkName\":\"Local Area Connection* 1\",\"networkType\":\"Wireless80211\",\"hasGateway\":false},{\"macAddress\":\"B6:97:B1:AA:C1:B7\",\"machType\":\"Microsoft Wi-Fi Direct Virtual Adapter #2\",\"networkName\":\"Local Area Connection* 10\",\"networkType\":\"Wireless80211\",\"hasGateway\":false},{\"macAddress\":\"A4:97:B1:AA:C1:B7\",\"machType\":\"Qualcomm QCA61x4A 802.11ac Wireless Adapter\",\"networkName\":\"Wi-Fi\",\"networkType\":\"Wireless80211\",\"hasGateway\":true},{\"macAddress\":\"96:21:20:52:41:53\",\"machType\":\"WAN Miniport (IP)\",\"networkName\":\"[00000009] WAN Miniport (IP)\",\"networkType\":null,\"hasGateway\":false},{\"macAddress\":\"9A:6C:20:52:41:53\",\"machType\":\"WAN Miniport (IPv6)\",\"networkName\":\"[00000010] WAN Miniport (IPv6)\",\"networkType\":null,\"hasGateway\":false},{\"macAddress\":\"9E:D6:20:52:41:53\",\"machType\":\"WAN Miniport (Network Monitor)\",\"networkName\":\"[00000011] WAN Miniport (Network Monitor)\",\"networkType\":null,\"hasGateway\":false}],\"computer_name\":\"DESKTOP-R83LFQL\",\"pc_serial_no\":\"20S1B63\",\"system_uuid\":\"4C4C4544-004C-3710-8035-C4C04F543933\",\"ip_address\":\"************\",\"app_version\":\"*******\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "assets-api.gogenuity-staging.com/api/v1/get_probe_configs.json", "host": ["assets-api", "gogenuity-staging", "com"], "path": ["api", "v1", "get_probe_configs.json"]}}, "response": []}, {"name": "RemoteConfigUpdate()", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"company_guid\":\"7e38674c-87fd-4c8d-95b2-5920c09e2d5c\",\"company_user_guid\":\"06252343-1b06-4522-a904-9d9c80e6f968\",\"mac_addresses\":[\"A4:97:B1:AA:C1:B7\"],\"sec_mac_addresses\":[\"00:15:5D:CE:DB:9C\",\"A6:97:B1:AA:C1:B7\",\"B6:97:B1:AA:C1:B7\",\"A4:97:B1:AA:C1:B7\",\"96:21:20:52:41:53\",\"9A:6C:20:52:41:53\",\"9E:D6:20:52:41:53\"],\"mac_add_details\":[{\"macAddress\":\"00:15:5D:CE:DB:9C\",\"machType\":\"Hyper-V Virtual Ethernet Adapter\",\"networkName\":\"vEthernet (WSL)\",\"networkType\":\"Ethernet\",\"hasGateway\":false},{\"macAddress\":\"A6:97:B1:AA:C1:B7\",\"machType\":\"Microsoft Wi-Fi Direct Virtual Adapter\",\"networkName\":\"Local Area Connection* 1\",\"networkType\":\"Wireless80211\",\"hasGateway\":false},{\"macAddress\":\"B6:97:B1:AA:C1:B7\",\"machType\":\"Microsoft Wi-Fi Direct Virtual Adapter #2\",\"networkName\":\"Local Area Connection* 10\",\"networkType\":\"Wireless80211\",\"hasGateway\":false},{\"macAddress\":\"A4:97:B1:AA:C1:B7\",\"machType\":\"Qualcomm QCA61x4A 802.11ac Wireless Adapter\",\"networkName\":\"Wi-Fi\",\"networkType\":\"Wireless80211\",\"hasGateway\":true},{\"macAddress\":\"96:21:20:52:41:53\",\"machType\":\"WAN Miniport (IP)\",\"networkName\":\"[00000009] WAN Miniport (IP)\",\"networkType\":null,\"hasGateway\":false},{\"macAddress\":\"9A:6C:20:52:41:53\",\"machType\":\"WAN Miniport (IPv6)\",\"networkName\":\"[00000010] WAN Miniport (IPv6)\",\"networkType\":null,\"hasGateway\":false},{\"macAddress\":\"9E:D6:20:52:41:53\",\"machType\":\"WAN Miniport (Network Monitor)\",\"networkName\":\"[00000011] WAN Miniport (Network Monitor)\",\"networkType\":null,\"hasGateway\":false}],\"computer_name\":\"DESKTOP-R83LFQL\",\"pc_serial_no\":\"DL75T93\",\"ip_address\":\"************\",\"app_version\":\"*******\",\"probe_config\":{\"ProbeName\":null,\"ScheduleConfig\":{\"ScheduleEnabled\":false,\"ScheduledDays\":[]},\"IpRange\":null,\"WmiConfigs\":null,\"SshConfigs\":null,\"SnmpConfigs\":null},\"system_uuid\":\"4C4C4544-004C-3710-8035-C4C04F543933\",\"schedule_config\":{\"ScheduleEnabled\":false,\"ScheduledDays\":[]},\"snmp_config\":null,\"ssh_config\":null,\"wmi_config\":null,\"ip_config\":null,\"probe_name\":\"new asset\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "assets-api.gogenuity-staging.com/api/v1/probe_configs.json", "host": ["assets-api", "gogenuity-staging", "com"], "path": ["api", "v1", "probe_configs.json"]}}, "response": []}, {"name": "UploadAssets()", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"discovered_assets\": [\n        {\n            \"managedAsset\": {\n                \"Id\": \"********-0000-0000-0000-************\",\n                \"companyGuid\": \"7e38674c-87fd-4c8d-95b2-5920c09e2d5c\",\n                \"isLocalSystem\": true,\n                \"displayName\": \"DESKTOP-R83LFQL\",\n                \"formattedDisplayName\": \"DESKTOP-R83LFQL\",\n                \"macAddresses\": [\n                    \"A4:97:B1:AA:C1:B7\"\n                ],\n                \"secMacAddresses\": [\n                    \"00:15:5D:CE:DB:9C\",\n                    \"A6:97:B1:AA:C1:B7\",\n                    \"B6:97:B1:AA:C1:B7\",\n                    \"A4:97:B1:AA:C1:B7\",\n                    \"96:21:20:52:41:53\",\n                    \"9A:6C:20:52:41:53\",\n                    \"9E:D6:20:52:41:53\"\n                ],\n                \"macAddressesInfo\": [\n                    {\n                        \"macAddress\": \"00:15:5D:CE:DB:9C\",\n                        \"machType\": \"Hyper-V Virtual Ethernet Adapter\",\n                        \"networkName\": \"vEthernet (WSL)\",\n                        \"networkType\": \"Ethernet\",\n                        \"hasGateway\": false\n                    },\n                    {\n                        \"macAddress\": \"A6:97:B1:AA:C1:B7\",\n                        \"machType\": \"Microsoft Wi-Fi Direct Virtual Adapter\",\n                        \"networkName\": \"Local Area Connection* 1\",\n                        \"networkType\": \"Wireless80211\",\n                        \"hasGateway\": false\n                    },\n                    {\n                        \"macAddress\": \"B6:97:B1:AA:C1:B7\",\n                        \"machType\": \"Microsoft Wi-Fi Direct Virtual Adapter #2\",\n                        \"networkName\": \"Local Area Connection* 10\",\n                        \"networkType\": \"Wireless80211\",\n                        \"hasGateway\": false\n                    },\n                    {\n                        \"macAddress\": \"A4:97:B1:AA:C1:B7\",\n                        \"machType\": \"Qualcomm QCA61x4A 802.11ac Wireless Adapter\",\n                        \"networkName\": \"Wi-Fi\",\n                        \"networkType\": \"Wireless80211\",\n                        \"hasGateway\": true\n                    },\n                    {\n                        \"macAddress\": \"96:21:20:52:41:53\",\n                        \"machType\": \"WAN Miniport (IP)\",\n                        \"networkName\": \"[00000009] WAN Miniport (IP)\",\n                        \"networkType\": null,\n                        \"hasGateway\": false\n                    },\n                    {\n                        \"macAddress\": \"9A:6C:20:52:41:53\",\n                        \"machType\": \"WAN Miniport (IPv6)\",\n                        \"networkName\": \"[00000010] WAN Miniport (IPv6)\",\n                        \"networkType\": null,\n                        \"hasGateway\": false\n                    },\n                    {\n                        \"macAddress\": \"9E:D6:20:52:41:53\",\n                        \"machType\": \"WAN Miniport (Network Monitor)\",\n                        \"networkName\": \"[00000011] WAN Miniport (Network Monitor)\",\n                        \"networkType\": null,\n                        \"hasGateway\": false\n                    }\n                ],\n                \"diskDetail\": null,\n                \"applications\": [\n                    {\n                        \"displayName\": \"AnyDesk\",\n                        \"version\": \"ad 7.1.7\",\n                        \"installDate\": null,\n                        \"installSource\": null,\n                        \"uninstallString\": \"\\\"C:\\\\Program Files (x86)\\\\AnyDesk\\\\AnyDesk.exe\\\" --uninstall\",\n                        \"installLocation\": \"\\\"C:\\\\Program Files\\\\AnyDesk\\\"\"\n                    },\n                    {\n                        \"displayName\": \"Visual Studio Community 2022\",\n                        \"version\": \"17.3.4\",\n                        \"installDate\": \"2022-09-26\",\n                        \"installSource\": null,\n                        \"uninstallString\": \"\\\"C:\\\\Program Files (x86)\\\\Microsoft Visual Studio\\\\Installer\\\\setup.exe\\\" uninstall --installPath \\\"C:\\\\Program Files\\\\Microsoft Visual Studio\\\\2022\\\\Community\\\"\",\n                        \"installLocation\": \"C:\\\\Program Files\\\\Microsoft Visual Studio\\\\2022\\\\Community\"\n                    },\n                    {\n                        \"displayName\": \"Google Chrome\",\n                        \"version\": \"108.0.5359.125\",\n                        \"installDate\": \"2022-12-16\",\n                        \"installSource\": null,\n                        \"uninstallString\": \"\\\"C:\\\\Program Files\\\\Google\\\\Chrome\\\\Application\\\\108.0.5359.125\\\\Installer\\\\setup.exe\\\" --uninstall --channel=stable --system-level --verbose-logging\",\n                        \"installLocation\": \"C:\\\\Program Files\\\\Google\\\\Chrome\\\\Application\"\n                    },\n                    {\n                        \"displayName\": \"Microsoft Edge\",\n                        \"version\": \"108.0.1462.54\",\n                        \"installDate\": \"2022-12-20\",\n                        \"installSource\": null,\n                        \"uninstallString\": \"\\\"C:\\\\Program Files (x86)\\\\Microsoft\\\\Edge\\\\Application\\\\108.0.1462.54\\\\Installer\\\\setup.exe\\\" --uninstall --msedge --channel=stable --system-level --verbose-logging\",\n                        \"installLocation\": \"C:\\\\Program Files\\\\Microsoft\\\\Edge\\\\Application\"\n                    },\n                    {\n                        \"displayName\": \"Microsoft Edge Update\",\n                        \"version\": \"1.3.171.37\",\n                        \"installDate\": null,\n                        \"installSource\": null,\n                        \"uninstallString\": null,\n                        \"installLocation\": null\n                    },\n                    {\n                        \"displayName\": \"Microsoft Edge WebView2 Runtime\",\n                        \"version\": \"108.0.1462.54\",\n                        \"installDate\": \"2022-12-20\",\n                        \"installSource\": null,\n                        \"uninstallString\": \"\\\"C:\\\\Program Files (x86)\\\\Microsoft\\\\EdgeWebView\\\\Application\\\\108.0.1462.54\\\\Installer\\\\setup.exe\\\" --uninstall --msedgewebview --system-level --verbose-logging\",\n                        \"installLocation\": \"C:\\\\Program Files\\\\Microsoft\\\\EdgeWebView\\\\Application\"\n                    },\n                    {\n                        \"displayName\": \"Npcap OEM\",\n                        \"version\": \"1.60\",\n                        \"installDate\": null,\n                        \"installSource\": null,\n                        \"uninstallString\": \"\\\"C:\\\\Program Files\\\\Npcap\\\\uninstall.exe\\\"\",\n                        \"installLocation\": \"C:\\\\Program Files\\\\Npcap\"\n                    },\n                    {\n                        \"displayName\": \"PuTTY release 0.77\",\n                        \"version\": \"0.77.0.0\",\n                        \"installDate\": \"2022-10-26\",\n                        \"installSource\": \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\\",\n                        \"uninstallString\": \"MsiExec.exe /X{2B9F1234-D8ED-45C0-B865-511405F4734F}\",\n                        \"installLocation\": \"\"\n                    },\n                    {\n                        \"displayName\": \"Microsoft Visual C++ 2015-2022 Redistributable - 14.32.31332\",\n                        \"version\": \"14.32.31332.0\",\n                        \"installDate\": null,\n                        \"installSource\": null,\n                        \"uninstallString\": \"\\\"C:\\\\ProgramData\\\\Package Cache\\\\{3746f21b-c990-4045-bb33-1cf98cff7a68}\\\\VC_redist.x64.exe\\\"  /uninstall\",\n                        \"installLocation\": null\n                    },\n                    {\n                        \"displayName\": \"Windows Software Development Kit - Windows 10.0.19041.685\",\n                        \"version\": \"10.1.19041.685\",\n                        \"installDate\": null,\n                        \"installSource\": null,\n                        \"uninstallString\": \"\\\"C:\\\\ProgramData\\\\Package Cache\\\\{4591faf1-a2db-4a3d-bfda-aa5a4ebb1587}\\\\winsdksetup.exe\\\"  /uninstall\",\n                        \"installLocation\": null\n                    },\n                    {\n                        \"displayName\": \"Microsoft .NET Framework 4.6.2 SDK\",\n                        \"version\": \"4.6.01590\",\n                        \"installDate\": \"2022-09-28\",\n                        \"installSource\": \"C:\\\\ProgramData\\\\Package Cache\\\\{5F01B3C4-9BEC-465D-9C68-BB97D381FFAD}v4.6.01590\\\\packages\\\\netfxsdk\\\\\",\n                        \"uninstallString\": \"MsiExec.exe /X{5F01B3C4-9BEC-465D-9C68-BB97D381FFAD}\",\n                        \"installLocation\": \"\"\n                    },\n                    {\n                        \"displayName\": \"Microsoft .NET Runtime - 6.0.9\",\n                        \"version\": \"6.0.9.31619\",\n                        \"installDate\": null,\n                        \"installSource\": null,\n                        \"uninstallString\": \"\\\"C:\\\\ProgramData\\\\Package Cache\\\\{67950e91-8f8f-4d75-9252-7cca68ccdacc}\\\\dotnet-runtime-6.0.9-win-x64.exe\\\"  /uninstall\",\n                        \"installLocation\": null\n                    },\n                    {\n                        \"displayName\": \"WiX Toolset v3.11.2.4516\",\n                        \"version\": \"3.11.2.4516\",\n                        \"installDate\": null,\n                        \"installSource\": null,\n                        \"uninstallString\": \"\\\"C:\\\\ProgramData\\\\Package Cache\\\\{7a940384-ee12-4443-8aa3-2ab82df2372a}\\\\WiX311.exe\\\"  /uninstall\",\n                        \"installLocation\": null\n                    },\n                    {\n                        \"displayName\": \"Network Discovery\",\n                        \"version\": \"2.6.6.7\",\n                        \"installDate\": \"2022-12-20\",\n                        \"installSource\": \"C:\\\\Users\\\\<USER>\\\\Projects\\\\NetworkDiscoveryForCustomizeBuild\\\\DiscoveryTool\\\\NetworkDiscovery.Setup\\\\bin\\\\Release\\\\en-us\\\\\",\n                        \"uninstallString\": \"MsiExec.exe /I{8E0DE4B5-C715-47CE-9216-BED244292DEB}\",\n                        \"installLocation\": \"\"\n                    },\n                    {\n                        \"displayName\": \"Microsoft .NET Framework 4.6.2 Targeting Pack\",\n                        \"version\": \"4.6.01590\",\n                        \"installDate\": \"2022-09-28\",\n                        \"installSource\": \"C:\\\\ProgramData\\\\Package Cache\\\\{A18D4C2A-07A8-40E4-9797-DD324E6EA4FC}v4.6.01590\\\\packages\\\\netfxmtpack\\\\\",\n                        \"uninstallString\": \"MsiExec.exe /X{A18D4C2A-07A8-40E4-9797-DD324E6EA4FC}\",\n                        \"installLocation\": \"\"\n                    },\n                    {\n                        \"displayName\": \"Windows Agent\",\n                        \"version\": \"1.1.3.7\",\n                        \"installDate\": \"2022-10-28\",\n                        \"installSource\": \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\\",\n                        \"uninstallString\": \"MsiExec.exe /I{B97B5DA9-E1B6-4447-A6CC-C3EA7649EFB3}\",\n                        \"installLocation\": \"\"\n                    },\n                    {\n                        \"displayName\": \"Microsoft .NET Framework 4.6.2 Targeting Pack (ENU)\",\n                        \"version\": \"4.6.01590\",\n                        \"installDate\": \"2022-09-28\",\n                        \"installSource\": \"C:\\\\ProgramData\\\\Package Cache\\\\{C80951BD-6904-474F-BBC5-03A6C777F37C}v4.6.01590\\\\packages\\\\netfxmtpacklp\\\\enu\\\\\",\n                        \"uninstallString\": \"MsiExec.exe /X{C80951BD-6904-474F-BBC5-03A6C777F37C}\",\n                        \"installLocation\": \"\"\n                    },\n                    {\n                        \"displayName\": \"Windows SDK AddOn\",\n                        \"version\": \"10.1.0.0\",\n                        \"installDate\": \"2022-09-26\",\n                        \"installSource\": \"C:\\\\ProgramData\\\\Package Cache\\\\{E18618EC-D9DB-4BCE-B382-85ADA2CBB340}v10.1.0.0\\\\Redistributable\\\\10.1.0.0\\\\\",\n                        \"uninstallString\": \"MsiExec.exe /I{E18618EC-D9DB-4BCE-B382-85ADA2CBB340}\",\n                        \"installLocation\": \"\"\n                    },\n                    {\n                        \"displayName\": \"Git\",\n                        \"version\": \"2.37.3\",\n                        \"installDate\": \"2022-09-28\",\n                        \"installSource\": null,\n                        \"uninstallString\": \"\\\"C:\\\\Program Files\\\\Git\\\\unins000.exe\\\"\",\n                        \"installLocation\": \"C:\\\\Program Files\\\\Git\\\\\"\n                    },\n                    {\n                        \"displayName\": \"Microsoft Azure Compute Emulator - v2.9.7\",\n                        \"version\": \"2.9.8999.43\",\n                        \"installDate\": \"9/26-/2-022\",\n                        \"installSource\": \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\\\\\{04CA054C-2F40-44B0-8610-8D51EC9444FE}\\\\\",\n                        \"uninstallString\": \"msiexec /x{04ca054c-2f40-44b0-8610-8d51ec9444fe}\",\n                        \"installLocation\": \"\"\n                    },\n                    {\n                        \"displayName\": \"Microsoft Visual Studio 2010 Tools for Office Runtime\",\n                        \"version\": \"10.0.50903\",\n                        \"installDate\": null,\n                        \"installSource\": null,\n                        \"uninstallString\": \"c:\\\\Program Files\\\\Common Files\\\\Microsoft Shared\\\\VSTO\\\\10.0\\\\Microsoft Visual Studio 2010 Tools for Office Runtime (x64)\\\\install.exe\",\n                        \"installLocation\": \"c:\\\\Program Files\\\\Common Files\\\\Microsoft Shared\\\\VSTO\\\\10.0\\\\\"\n                    },\n                    {\n                        \"displayName\": \"Notepad++ (64-bit)\",\n                        \"version\": \"8.4.6\",\n                        \"installDate\": null,\n                        \"installSource\": null,\n                        \"uninstallString\": \"\\\"C:\\\\Program Files\\\\Notepad++\\\\uninstall.exe\\\"\",\n                        \"installLocation\": null\n                    },\n                    {\n                        \"displayName\": \"WinRAR 6.11\",\n                        \"version\": \"6.11.0\",\n                        \"installDate\": null,\n                        \"installSource\": null,\n                        \"uninstallString\": \"C:\\\\Program Files\\\\WinRAR\\\\uninstall.exe\",\n                        \"installLocation\": \"C:\\\\Program Files\\\\WinRAR\\\\\"\n                    },\n                    {\n                        \"displayName\": \"Microsoft .NET SDK 6.0.401 from Visual Studio\",\n                        \"version\": \"6.4.122.42113\",\n                        \"installDate\": \"2022-09-26\",\n                        \"installSource\": \"C:\\\\ProgramData\\\\Microsoft\\\\VisualStudio\\\\Packages\\\\Microsoft.NetCore.SdkPlaceholder.6.0.401-servicing.22421.13,version=17.3.32901.90,machinearch=x64\\\\\",\n                        \"uninstallString\": \"MsiExec.exe /X{0ADF0BD6-C434-42C7-8AC2-AE7177C28504}\",\n                        \"installLocation\": \"\"\n                    },\n                    {\n                        \"displayName\": \"Microsoft Web Deploy 4.0\",\n                        \"version\": \"10.0.5923\",\n                        \"installDate\": \"2022-09-26\",\n                        \"installSource\": \"C:\\\\ProgramData\\\\Microsoft\\\\VisualStudio\\\\Packages\\\\Microsoft.VisualStudio.WebDeploy.Msi,version=17.3.32708.82,chip=x64\\\\\",\n                        \"uninstallString\": \"MsiExec.exe /I{0C85743B-48E7-4948-96D6-C3BB90246418}\",\n                        \"installLocation\": \"\"\n                    },\n                    {\n                        \"displayName\": \"Microsoft SQL Server 2019 LocalDB \",\n                        \"version\": \"15.0.4153.1\",\n                        \"installDate\": \"2022-09-26\",\n                        \"installSource\": \"C:\\\\ProgramData\\\\Microsoft\\\\VisualStudio\\\\Packages\\\\sqllocaldb2019,version=17.0.62207.4110,chip=x64,language=en-US\\\\\",\n                        \"uninstallString\": \"MsiExec.exe /I{36E492B8-CB83-4DA5-A5D2-D99A8E8228A1}\",\n                        \"installLocation\": \"\"\n                    },\n                    {\n                        \"displayName\": \"Windows Subsystem for Linux Update\",\n                        \"version\": \"5.10.16\",\n                        \"installDate\": \"2022-09-27\",\n                        \"installSource\": \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\\",\n                        \"uninstallString\": \"MsiExec.exe /X{36EF257E-21D5-44F7-8451-07923A8C465E}\",\n                        \"installLocation\": \"\"\n                    },\n                    {\n                        \"displayName\": \"Microsoft Azure PowerShell - April 2018\",\n                        \"version\": \"5.7.0.18831\",\n                        \"installDate\": \"2022-09-26\",\n                        \"installSource\": \"C:\\\\ProgramData\\\\Microsoft\\\\VisualStudio\\\\Packages\\\\Microsoft.Azure.PowerShellNew,version=17.3.32708.82,chip=x64\\\\\",\n                        \"uninstallString\": \"MsiExec.exe /X{3BA7CAA9-97BA-4528-B7E1-B640910BB149}\",\n                        \"installLocation\": \"\"\n                    },\n                    {\n                        \"displayName\": \"DB Browser for SQLite\",\n                        \"version\": \"3.12.2\",\n                        \"installDate\": \"2022-10-18\",\n                        \"installSource\": \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\\",\n                        \"uninstallString\": \"MsiExec.exe /I{5211034D-495B-4A5E-9B8D-8961BBB2B9E2}\",\n                        \"installLocation\": \"C:\\\\Program Files\\\\DB Browser for SQLite\\\\\"\n                    },\n                    {\n                        \"displayName\": \"IIS 10.0 Express\",\n                        \"version\": \"10.0.06614\",\n                        \"installDate\": \"2022-09-26\",\n                        \"installSource\": \"C:\\\\ProgramData\\\\Microsoft\\\\VisualStudio\\\\Packages\\\\Microsoft.VisualStudio.IISExpress.Msi,version=17.3.32708.82,machinearch=x64\\\\\",\n                        \"uninstallString\": \"MsiExec.exe /X{56070933-B0D1-493F-8C12-4F7E83CA3071}\",\n                        \"installLocation\": \"\"\n                    },\n                    {\n                        \"displayName\": \"Microsoft System CLR Types for SQL Server 2019\",\n                        \"version\": \"15.0.2000.5\",\n                        \"installDate\": \"2022-09-26\",\n                        \"installSource\": \"C:\\\\ProgramData\\\\Microsoft\\\\VisualStudio\\\\Packages\\\\sqlsysclrtypes,version=17.0.62207.4110,chip=x64,language=en-US\\\\\",\n                        \"uninstallString\": \"MsiExec.exe /I{5BC7E9EB-13E8-45DB-8A60-F2481FEB4595}\",\n                        \"installLocation\": \"\"\n                    },\n                    {\n                        \"displayName\": \"Windows PC Health Check\",\n                        \"version\": \"3.6.2204.08001\",\n                        \"installDate\": \"2022-09-19\",\n                        \"installSource\": \"C:\\\\WINDOWS\\\\SoftwareDistribution\\\\Download\\\\8c65a163c8c24c7e95c0237bfc859210\\\\img\\\\\",\n                        \"uninstallString\": \"MsiExec.exe /X{6798C408-2636-448C-8AC6-F4E341102D27}\",\n                        \"installLocation\": \"\"\n                    },\n                    {\n                        \"displayName\": \"Microsoft Visual Studio Installer\",\n                        \"version\": \"3.4.2244.14676\",\n                        \"installDate\": \"2022-09-26\",\n                        \"installSource\": null,\n                        \"uninstallString\": \"\\\"C:\\\\Program Files (x86)\\\\Microsoft Visual Studio\\\\Installer\\\\setup.exe\\\" /uninstall\",\n                        \"installLocation\": \"\\\"C:\\\\Program Files\\\\Microsoft Visual Studio\\\\Installer\\\"\"\n                    },\n                    {\n                        \"displayName\": \"Microsoft SQL Server Compact 4.0 SP1 ENU\",\n                        \"version\": \"4.0.8876.1\",\n                        \"installDate\": \"2022-10-18\",\n                        \"installSource\": \"C:\\\\Program Files (x86)\\\\Microsoft SQL Server Compact Edition\\\\Installer\\\\\",\n                        \"uninstallString\": \"MsiExec.exe /X{78909610-D229-459C-A936-25D92283D3FD}\",\n                        \"installLocation\": \"C:\\\\Program Files\\\\Microsoft SQL Server Compact Edition\\\\\"\n                    },\n                    {\n                        \"displayName\": \"Microsoft Update Health Tools\",\n                        \"version\": \"3.68.0.0\",\n                        \"installDate\": \"2022-12-05\",\n                        \"installSource\": \"C:\\\\WINDOWS\\\\TEMP\\\\2F73FDBE-F968-4BD4-A5BE-B8C98F2945E7\\\\\",\n                        \"uninstallString\": \"MsiExec.exe /X{80F1AF52-7AC0-42A3-9AF0-689BFB271D1D}\",\n                        \"installLocation\": \"\"\n                    },\n                    {\n                        \"displayName\": \"Microsoft ODBC Driver 17 for SQL Server\",\n                        \"version\": \"********\",\n                        \"installDate\": \"2022-09-26\",\n                        \"installSource\": \"C:\\\\ProgramData\\\\Microsoft\\\\VisualStudio\\\\Packages\\\\msodbcsql,version=17.0.62207.4110,chip=x64,language=en-US\\\\\",\n                        \"uninstallString\": \"MsiExec.exe /I{853997DA-6FCB-4FB9-918E-E0FF881FAF65}\",\n                        \"installLocation\": \"\"\n                    },\n                    {\n                        \"displayName\": \"Microsoft Azure Authoring Tools - v2.9.7\",\n                        \"version\": \"2.9.8999.45\",\n                        \"installDate\": \"2022-09-26\",\n                        \"installSource\": \"C:\\\\ProgramData\\\\Microsoft\\\\VisualStudio\\\\Packages\\\\Microsoft.Azure.AuthoringTools.Msi,version=2.9.8999.46,chip=x64\\\\\",\n                        \"uninstallString\": \"MsiExec.exe /I{90462BD2-DF5B-449C-A401-FCC1DC264E4E}\",\n                        \"installLocation\": \"\"\n                    },\n                    {\n                        \"displayName\": \"Microsoft Azure Libraries for .NET – v2.9\",\n                        \"version\": \"3.0.0127.060\",\n                        \"installDate\": \"2022-09-26\",\n                        \"installSource\": \"C:\\\\ProgramData\\\\Microsoft\\\\VisualStudio\\\\Packages\\\\Microsoft.Azure.ClientLibs.Msi,version=**********,chip=x64\\\\\",\n                        \"uninstallString\": \"MsiExec.exe /I{C5C91AA6-3E83-430E-8B7A-6B790083F28D}\",\n                        \"installLocation\": \"\"\n                    },\n                    {\n                        \"displayName\": \"GitHub Desktop\",\n                        \"version\": \"3.1.2\",\n                        \"installDate\": \"2022-11-14\",\n                        \"installSource\": null,\n                        \"uninstallString\": \"\\\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\GitHubDesktop\\\\Update.exe\\\" --uninstall\",\n                        \"installLocation\": \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\GitHubDesktop\"\n                    },\n                    {\n                        \"displayName\": \"Microsoft OneDrive\",\n                        \"version\": \"22.238.1114.0002\",\n                        \"installDate\": null,\n                        \"installSource\": null,\n                        \"uninstallString\": \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Microsoft\\\\OneDrive\\\\22.238.1114.0002\\\\OneDriveSetup.exe  /uninstall \",\n                        \"installLocation\": null\n                    },\n                    {\n                        \"displayName\": \"Slack\",\n                        \"version\": \"4.29.149\",\n                        \"installDate\": \"2022-11-15\",\n                        \"installSource\": null,\n                        \"uninstallString\": \"\\\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\slack\\\\Update.exe\\\" --uninstall\",\n                        \"installLocation\": \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\slack\"\n                    },\n                    {\n                        \"displayName\": \"Zoom\",\n                        \"version\": \"5.12.0 (8964)\",\n                        \"installDate\": null,\n                        \"installSource\": null,\n                        \"uninstallString\": \"\\\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\Zoom\\\\uninstall\\\\Installer.exe\\\" /uninstall\",\n                        \"installLocation\": \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\Zoom\\\\bin\"\n                    },\n                    {\n                        \"displayName\": \"Microsoft Visual Studio Code (User)\",\n                        \"version\": \"1.73.1\",\n                        \"installDate\": \"2022-11-10\",\n                        \"installSource\": null,\n                        \"uninstallString\": \"\\\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Programs\\\\Microsoft VS Code\\\\unins000.exe\\\"\",\n                        \"installLocation\": \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Programs\\\\Microsoft VS Code\\\\\"\n                    }\n                ],\n                \"ipAddress\": \"**************\",\n                \"networks\": [\n                    \"************\",\n                    \"**************\"\n                ],\n                \"deviceType\": \"Laptop\",\n                \"protocol\": null,\n                \"os\": \"Windows\",\n                \"manufacturer\": \"Dell Inc.\",\n                \"formattedManufacturer\": \"Dell Inc.\",\n                \"pcSerialNo\": \"20S1B63\",\n                \"osName\": \"Microsoft Windows 10 Pro\",\n                \"osVersion\": \"10.0.19045\",\n                \"osSerialNo\": \"00330-80000-00000-AA863\",\n                \"firmware\": null,\n                \"memory\": \"24 GB\",\n                \"diskSpace\": \"477 GB\",\n                \"processorCores\": \"4 Cores\",\n                \"processorLogicalCores\": \"8 Cores\",\n                \"processorArchitecture\": \"x64\",\n                \"processorName\": \"11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz\",\n                \"domainName\": \"WORKGROUP\",\n                \"username\": null,\n                \"systemUuid\": \"4C4C4544-0030-5310-8031-B2C04F423633\",\n                \"password\": null,\n                \"community\": null,\n                \"isMacDuplicate\": false,\n                \"ssh\": true,\n                \"wmi\": true,\n                \"snmp\": true,\n                \"optionalDetails\": \"\",\n                \"xml\": \"\",\n                \"newAssetType\": \"\",\n                \"model\": \"Inspiron 5502\",\n                \"systemUpTime\": \"52 Hours\",\n                \"osBuildNo\": \"19045\",\n                \"deviceId\": \"8F41D00A-23DC-40C0-9156-B21E23B44486\",\n                \"diskEncryption\": \"Off\",\n                \"diskFreeSpace\": \"106GB\",\n                \"hardwareVersion\": \"1.18.0\",\n                \"userAccountsInfo\": [\n                    {\n                        \"fullName\": \"\",\n                        \"name\": \"ccops\",\n                        \"lastLogin\": null\n                    }\n                ],\n                \"remoteIpAddress\": \"**************\",\n                \"hostname\": \"DESKTOP-LCG7AN0.WORKGROUP\",\n                \"HardwareDetail\": null\n            }\n        },\n        {\n            \"managedAsset\": {\n                \"Id\": \"********-0000-0000-0000-************\",\n                \"companyGuid\": \"7e38674c-87fd-4c8d-95b2-5920c09e2d5c\",\n                \"isLocalSystem\": false,\n                \"displayName\": \"************\",\n                \"formattedDisplayName\": \"************\",\n                \"macAddresses\": [\n                    \"24:91:BB:8F:4C:C8\"\n                ],\n                \"secMacAddresses\": [\n                    \"24:91:BB:8F:4C:C8\"\n                ],\n                \"macAddressesInfo\": [],\n                \"diskDetail\": null,\n                \"applications\": null,\n                \"ipAddress\": \"************\",\n                \"networks\": null,\n                \"deviceType\": null,\n                \"protocol\": null,\n                \"os\": \"Linux/Unix/Android\",\n                \"manufacturer\": \"HUAWEI TECHNOLOGIES CO.,LTD\",\n                \"formattedManufacturer\": \"HUAWEI TECHNOLOGIES ...\",\n                \"pcSerialNo\": null,\n                \"osName\": \"Linux/Unix/Android\",\n                \"osVersion\": null,\n                \"osSerialNo\": null,\n                \"firmware\": null,\n                \"memory\": null,\n                \"diskSpace\": null,\n                \"processorCores\": null,\n                \"processorLogicalCores\": null,\n                \"processorArchitecture\": null,\n                \"processorName\": null,\n                \"domainName\": null,\n                \"username\": null,\n                \"systemUuid\": null,\n                \"password\": null,\n                \"community\": null,\n                \"isMacDuplicate\": false,\n                \"ssh\": false,\n                \"wmi\": false,\n                \"snmp\": false,\n                \"optionalDetails\": \"\",\n                \"xml\": \"\",\n                \"newAssetType\": \"\",\n                \"model\": null,\n                \"systemUpTime\": null,\n                \"osBuildNo\": null,\n                \"deviceId\": null,\n                \"diskEncryption\": null,\n                \"diskFreeSpace\": null,\n                \"hardwareVersion\": null,\n                \"userAccountsInfo\": null,\n                \"remoteIpAddress\": null,\n                \"hostname\": \"************.\",\n                \"HardwareDetail\": null\n            }\n        },\n        {\n            \"managedAsset\": {\n                \"Id\": \"********-0000-0000-0000-************\",\n                \"companyGuid\": \"7e38674c-87fd-4c8d-95b2-5920c09e2d5c\",\n                \"isLocalSystem\": false,\n                \"displayName\": \"*************\",\n                \"formattedDisplayName\": \"*************\",\n                \"macAddresses\": [\n                    \"4A:3A:1D:FF:57:39\"\n                ],\n                \"secMacAddresses\": [\n                    \"4A:3A:1D:FF:57:39\"\n                ],\n                \"macAddressesInfo\": [],\n                \"diskDetail\": null,\n                \"applications\": null,\n                \"ipAddress\": \"*************\",\n                \"networks\": null,\n                \"deviceType\": null,\n                \"protocol\": null,\n                \"os\": \"Linux/Unix/Android\",\n                \"manufacturer\": null,\n                \"formattedManufacturer\": null,\n                \"pcSerialNo\": null,\n                \"osName\": \"Linux/Unix/Android\",\n                \"osVersion\": null,\n                \"osSerialNo\": null,\n                \"firmware\": null,\n                \"memory\": null,\n                \"diskSpace\": null,\n                \"processorCores\": null,\n                \"processorLogicalCores\": null,\n                \"processorArchitecture\": null,\n                \"processorName\": null,\n                \"domainName\": null,\n                \"username\": null,\n                \"systemUuid\": null,\n                \"password\": null,\n                \"community\": null,\n                \"isMacDuplicate\": false,\n                \"ssh\": false,\n                \"wmi\": false,\n                \"snmp\": false,\n                \"optionalDetails\": \"\",\n                \"xml\": \"\",\n                \"newAssetType\": \"\",\n                \"model\": null,\n                \"systemUpTime\": null,\n                \"osBuildNo\": null,\n                \"deviceId\": null,\n                \"diskEncryption\": null,\n                \"diskFreeSpace\": null,\n                \"hardwareVersion\": null,\n                \"userAccountsInfo\": null,\n                \"remoteIpAddress\": null,\n                \"hostname\": \"*************.\",\n                \"HardwareDetail\": null\n            }\n        },\n        {\n            \"managedAsset\": {\n                \"Id\": \"********-0000-0000-0000-************\",\n                \"companyGuid\": \"7e38674c-87fd-4c8d-95b2-5920c09e2d5c\",\n                \"isLocalSystem\": false,\n                \"displayName\": \"DESKTOP-CEGO6V7.localle\",\n                \"formattedDisplayName\": \"DESKTOP-CEGO6V7.loca...\",\n                \"macAddresses\": [\n                    \"F4:46:37:2E:DD:35\"\n                ],\n                \"secMacAddresses\": [\n                    \"F4:46:37:2E:DD:35\"\n                ],\n                \"macAddressesInfo\": [],\n                \"diskDetail\": null,\n                \"applications\": null,\n                \"ipAddress\": \"**************\",\n                \"networks\": null,\n                \"deviceType\": null,\n                \"protocol\": null,\n                \"os\": \"Windows\",\n                \"manufacturer\": \"Intel Corporate\",\n                \"formattedManufacturer\": \"Intel Corporate\",\n                \"pcSerialNo\": null,\n                \"osName\": \"Windows\",\n                \"osVersion\": null,\n                \"osSerialNo\": null,\n                \"firmware\": null,\n                \"memory\": null,\n                \"diskSpace\": null,\n                \"processorCores\": null,\n                \"processorLogicalCores\": null,\n                \"processorArchitecture\": null,\n                \"processorName\": null,\n                \"domainName\": null,\n                \"username\": null,\n                \"systemUuid\": null,\n                \"password\": null,\n                \"community\": null,\n                \"isMacDuplicate\": false,\n                \"ssh\": false,\n                \"wmi\": false,\n                \"snmp\": false,\n                \"optionalDetails\": \"\",\n                \"xml\": \"\",\n                \"newAssetType\": \"\",\n                \"model\": null,\n                \"systemUpTime\": null,\n                \"osBuildNo\": null,\n                \"deviceId\": null,\n                \"diskEncryption\": null,\n                \"diskFreeSpace\": null,\n                \"hardwareVersion\": null,\n                \"userAccountsInfo\": null,\n                \"remoteIpAddress\": null,\n                \"hostname\": \"DESKTOP-CEGO6V7.local.\",\n                \"HardwareDetail\": null\n            }\n        },\n        {\n            \"managedAsset\": {\n                \"Id\": \"********-0000-0000-0000-************\",\n                \"companyGuid\": \"7e38674c-87fd-4c8d-95b2-5920c09e2d5c\",\n                \"isLocalSystem\": false,\n                \"displayName\": \"MAC-95E523\",\n                \"formattedDisplayName\": \"MAC-95E523\",\n                \"macAddresses\": [\n                    \"9C:3E:53:95:E5:23\"\n                ],\n                \"secMacAddresses\": [\n                    \"9C:3E:53:95:E5:23\"\n                ],\n                \"macAddressesInfo\": [],\n                \"diskDetail\": null,\n                \"applications\": null,\n                \"ipAddress\": \"**************\",\n                \"networks\": null,\n                \"deviceType\": \"Apple device\",\n                \"protocol\": null,\n                \"os\": \"Linux/Unix/Android\",\n                \"manufacturer\": \"Apple, Inc.\",\n                \"formattedManufacturer\": \"Apple, Inc.\",\n                \"pcSerialNo\": null,\n                \"osName\": \"Linux/Unix/Android\",\n                \"osVersion\": null,\n                \"osSerialNo\": null,\n                \"firmware\": null,\n                \"memory\": null,\n                \"diskSpace\": null,\n                \"processorCores\": null,\n                \"processorLogicalCores\": null,\n                \"processorArchitecture\": null,\n                \"processorName\": null,\n                \"domainName\": null,\n                \"username\": null,\n                \"systemUuid\": null,\n                \"password\": null,\n                \"community\": null,\n                \"isMacDuplicate\": false,\n                \"ssh\": false,\n                \"wmi\": false,\n                \"snmp\": false,\n                \"optionalDetails\": \"\",\n                \"xml\": \"\",\n                \"newAssetType\": \"\",\n                \"model\": null,\n                \"systemUpTime\": null,\n                \"osBuildNo\": null,\n                \"deviceId\": null,\n                \"diskEncryption\": null,\n                \"diskFreeSpace\": null,\n                \"hardwareVersion\": null,\n                \"userAccountsInfo\": null,\n                \"remoteIpAddress\": null,\n                \"hostname\": \"MAC-95E523.\",\n                \"HardwareDetail\": null\n            }\n        },\n        {\n            \"managedAsset\": {\n                \"Id\": \"********-0000-0000-0000-************\",\n                \"companyGuid\": \"7e38674c-87fd-4c8d-95b2-5920c09e2d5c\",\n                \"isLocalSystem\": false,\n                \"displayName\": \"**************\",\n                \"formattedDisplayName\": \"**************\",\n                \"macAddresses\": [\n                    \"DC:B7:2E:52:8E:08\"\n                ],\n                \"secMacAddresses\": [\n                    \"DC:B7:2E:52:8E:08\"\n                ],\n                \"macAddressesInfo\": [],\n                \"diskDetail\": null,\n                \"applications\": null,\n                \"ipAddress\": \"**************\",\n                \"networks\": null,\n                \"deviceType\": null,\n                \"protocol\": null,\n                \"os\": \"Linux/Unix/Android\",\n                \"manufacturer\": \"Xiaomi Communications Co Ltd\",\n                \"formattedManufacturer\": \"Xiaomi Communication...\",\n                \"pcSerialNo\": null,\n                \"osName\": \"Linux/Unix/Android\",\n                \"osVersion\": null,\n                \"osSerialNo\": null,\n                \"firmware\": null,\n                \"memory\": null,\n                \"diskSpace\": null,\n                \"processorCores\": null,\n                \"processorLogicalCores\": null,\n                \"processorArchitecture\": null,\n                \"processorName\": null,\n                \"domainName\": null,\n                \"username\": null,\n                \"systemUuid\": null,\n                \"password\": null,\n                \"community\": null,\n                \"isMacDuplicate\": false,\n                \"ssh\": false,\n                \"wmi\": false,\n                \"snmp\": false,\n                \"optionalDetails\": \"\",\n                \"xml\": \"\",\n                \"newAssetType\": \"\",\n                \"model\": null,\n                \"systemUpTime\": null,\n                \"osBuildNo\": null,\n                \"deviceId\": null,\n                \"diskEncryption\": null,\n                \"diskFreeSpace\": null,\n                \"hardwareVersion\": null,\n                \"userAccountsInfo\": null,\n                \"remoteIpAddress\": null,\n                \"hostname\": \"**************.\",\n                \"HardwareDetail\": null\n            }\n        },\n        {\n            \"managedAsset\": {\n                \"Id\": \"********-0000-0000-0000-************\",\n                \"companyGuid\": \"7e38674c-87fd-4c8d-95b2-5920c09e2d5c\",\n                \"isLocalSystem\": false,\n                \"displayName\": \"************48\",\n                \"formattedDisplayName\": \"************48\",\n                \"macAddresses\": [\n                    \"A2:0B:62:33:2A:C5\"\n                ],\n                \"secMacAddresses\": [\n                    \"A2:0B:62:33:2A:C5\"\n                ],\n                \"macAddressesInfo\": [],\n                \"diskDetail\": null,\n                \"applications\": null,\n                \"ipAddress\": \"************48\",\n                \"networks\": null,\n                \"deviceType\": null,\n                \"protocol\": null,\n                \"os\": \"Linux/Unix/Android\",\n                \"manufacturer\": null,\n                \"formattedManufacturer\": null,\n                \"pcSerialNo\": null,\n                \"osName\": \"Linux/Unix/Android\",\n                \"osVersion\": null,\n                \"osSerialNo\": null,\n                \"firmware\": null,\n                \"memory\": null,\n                \"diskSpace\": null,\n                \"processorCores\": null,\n                \"processorLogicalCores\": null,\n                \"processorArchitecture\": null,\n                \"processorName\": null,\n                \"domainName\": null,\n                \"username\": null,\n                \"systemUuid\": null,\n                \"password\": null,\n                \"community\": null,\n                \"isMacDuplicate\": false,\n                \"ssh\": false,\n                \"wmi\": false,\n                \"snmp\": false,\n                \"optionalDetails\": \"\",\n                \"xml\": \"\",\n                \"newAssetType\": \"\",\n                \"model\": null,\n                \"systemUpTime\": null,\n                \"osBuildNo\": null,\n                \"deviceId\": null,\n                \"diskEncryption\": null,\n                \"diskFreeSpace\": null,\n                \"hardwareVersion\": null,\n                \"userAccountsInfo\": null,\n                \"remoteIpAddress\": null,\n                \"hostname\": \"************48.\",\n                \"HardwareDetail\": null\n            }\n        }\n    ],\n    \"company_guid\": \"7e38674c-87fd-4c8d-95b2-5920c09e2d5c\",\n    \"mac_addresses\": [\n        \"D4:1B:81:14:0B:1B\"\n    ],\n    \"sec_mac_addresses\": [\n        \"00:15:5D:CE:DB:9C\",\n        \"A6:97:B1:AA:C1:B7\",\n        \"B6:97:B1:AA:C1:B7\",\n        \"A4:97:B1:AA:C1:B7\",\n        \"96:21:20:52:41:53\",\n        \"9A:6C:20:52:41:53\",\n        \"9E:D6:20:52:41:53\"\n    ],\n    \"mac_add_details\": [\n        {\n            \"macAddress\": \"00:15:5D:CE:DB:9C\",\n            \"machType\": \"Hyper-V Virtual Ethernet Adapter\",\n            \"networkName\": \"vEthernet (WSL)\",\n            \"networkType\": \"Ethernet\",\n            \"hasGateway\": false\n        },\n        {\n            \"macAddress\": \"A6:97:B1:AA:C1:B7\",\n            \"machType\": \"Microsoft Wi-Fi Direct Virtual Adapter\",\n            \"networkName\": \"Local Area Connection* 1\",\n            \"networkType\": \"Wireless80211\",\n            \"hasGateway\": false\n        },\n        {\n            \"macAddress\": \"B6:97:B1:AA:C1:B7\",\n            \"machType\": \"Microsoft Wi-Fi Direct Virtual Adapter #2\",\n            \"networkName\": \"Local Area Connection* 10\",\n            \"networkType\": \"Wireless80211\",\n            \"hasGateway\": false\n        },\n        {\n            \"macAddress\": \"A4:97:B1:AA:C1:B7\",\n            \"machType\": \"Qualcomm QCA61x4A 802.11ac Wireless Adapter\",\n            \"networkName\": \"Wi-Fi\",\n            \"networkType\": \"Wireless80211\",\n            \"hasGateway\": true\n        },\n        {\n            \"macAddress\": \"96:21:20:52:41:53\",\n            \"machType\": \"WAN Miniport (IP)\",\n            \"networkName\": \"[00000009] WAN Miniport (IP)\",\n            \"networkType\": null,\n            \"hasGateway\": false\n        },\n        {\n            \"macAddress\": \"9A:6C:20:52:41:53\",\n            \"machType\": \"WAN Miniport (IPv6)\",\n            \"networkName\": \"[00000010] WAN Miniport (IPv6)\",\n            \"networkType\": null,\n            \"hasGateway\": false\n        },\n        {\n            \"macAddress\": \"D4:1B:81:14:0B:1B\",\n            \"machType\": \"WAN Miniport (Network Monitor)\",\n            \"networkName\": \"[00000011] WAN Miniport (Network Monitor)\",\n            \"networkType\": null,\n            \"hasGateway\": false\n        }\n    ],\n    \"computer_name\": \"My postman system\",\n    \"pc_serial_no\": \"DL75T93\",\n    \"system_uuid\": \"4C4C4544-004C-3710-8035-C4C04F543933\",\n    \"ip_address\": \"************\",\n    \"app_version\": \"*******\",\n    \"bg_mode\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://secure.gogenuity-staging.com/api/v3/discovered_assets.json", "protocol": "https", "host": ["secure", "gogenuity-staging", "com"], "path": ["api", "v3", "discovered_assets.json"]}}, "response": []}, {"name": "UploadLogFile()", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "company_guid", "value": "7e38674c-87fd-4c8d-95b2-5920c09e2d5c", "type": "text"}, {"key": "computer_name", "value": "DESKTOP-R83LFQL", "type": "text"}, {"key": "mac_addresses", "value": "A4:97:B1:AA:C1:B7", "type": "text", "disabled": true}, {"key": "pc_serial_no", "value": "DL75T93", "type": "text"}, {"key": "system_uuid", "value": "4C4C4544-004C-3710-8035-C4C04F543933", "type": "text"}, {"key": "source", "value": "probe", "type": "text"}]}, "url": {"raw": "assets-api.gogenuity-staging.com/api/v1/asset_discovery_logs.json", "host": ["assets-api", "gogenuity-staging", "com"], "path": ["api", "v1", "asset_discovery_logs.json"]}}, "response": []}]}