{"info": {"_postman_id": "f52dcdba-6970-462d-b4ce-27acf040a33e", "name": "Agent", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "30765253"}, "item": [{"name": "update_available_req()", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "https://assets-api.gogenuity-staging.com/api/v1/app_versions?company_guid=7e38674c-87fd-4c8d-95b2-5920c09e2d5c&company_user_guid=06252343-1b06-4522-a904-9d9c80e6f968&app_version=*******&source=agent", "protocol": "https", "host": ["assets-api", "gogenuity-staging", "com"], "path": ["api", "v1", "app_versions"], "query": [{"key": "company_guid", "value": "7e38674c-87fd-4c8d-95b2-5920c09e2d5c"}, {"key": "company_user_guid", "value": "06252343-1b06-4522-a904-9d9c80e6f968"}, {"key": "app_version", "value": "*******"}, {"key": "source", "value": "agent"}]}}, "response": []}, {"name": "PostAssetData()", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "[\n    {\n        \"app_version\": \"*******\",\n        \"bg_mode\": false,\n        \"managedAsset\": {\n            \"companyUserGuid\": \"06252343-1b06-4522-a904-9d9c80e6f968\",\n            \"companyGuid\": \"7e38674c-87fd-4c8d-95b2-5920c09e2d5c\",\n            \"source\": \"agent\",\n            \"macAddresses\": [\n                \"D4:1B:81:14:0B:1B\"\n            ],\n            \"secMacAddresses\": [\n                \"00:15:5D:87:30:7F\",\n                \"A6:97:B1:AA:C1:B7\",\n                \"B6:97:B1:AA:C1:B7\",\n                \"A4:97:B1:AA:C1:B7\",\n                \"4A:92:20:52:41:53\",\n                \"50:8A:20:52:41:53\",\n                \"5E:6F:20:52:41:53\"\n            ],\n            \"macAddressesInfo\": [\n                {\n                    \"macAddress\": \"D4:1B:81:14:0B:1B\",\n                    \"machType\": \"Hyper-V Virtual Ethernet Adapter\",\n                    \"networkName\": \"vEthernet (WSL)\",\n                    \"networkType\": \"Ethernet\",\n                    \"hasGateway\": false\n                },\n                {\n                    \"macAddress\": \"D4:1B:81:14:0B:1B\",\n                    \"machType\": \"Microsoft Wi-Fi Direct Virtual Adapter\",\n                    \"networkName\": \"Local Area Connection* 1\",\n                    \"networkType\": \"Wireless80211\",\n                    \"hasGateway\": false\n                },\n                {\n                    \"macAddress\": \"BD4:1B:81:14:0B:1B\",\n                    \"machType\": \"Microsoft Wi-Fi Direct Virtual Adapter #2\",\n                    \"networkName\": \"Local Area Connection* 10\",\n                    \"networkType\": \"Wireless80211\",\n                    \"hasGateway\": false\n                },\n                {\n                    \"macAddress\": \"D4:1B:81:14:0B:1B\",\n                    \"machType\": \"Qualcomm QCA61x4A 802.11ac Wireless Adapter\",\n                    \"networkName\": \"Wi-Fi\",\n                    \"networkType\": \"Wireless80211\",\n                    \"hasGateway\": true\n                },\n                {\n                    \"macAddress\": \"D4:1B:81:14:0B:1B\",\n                    \"machType\": \"WAN Miniport (IP)\",\n                    \"networkName\": \"[00000009] WAN Miniport (IP)\",\n                    \"networkType\": null,\n                    \"hasGateway\": false\n                },\n                {\n                    \"macAddress\": \"D4:1B:81:14:0B:1B\",\n                    \"machType\": \"WAN Miniport (IPv6)\",\n                    \"networkName\": \"[00000010] WAN Miniport (IPv6)\",\n                    \"networkType\": null,\n                    \"hasGateway\": false\n                },\n                {\n                    \"macAddress\": \"D4:1B:81:14:0B:1B\",\n                    \"machType\": \"WAN Miniport (Network Monitor)\",\n                    \"networkName\": \"[00000011] WAN Miniport (Network Monitor)\",\n                    \"networkType\": null,\n                    \"hasGateway\": false\n                }\n            ],\n            \"applications\": [\n                {\n                    \"displayName\": \"AnyDesk\",\n                    \"version\": \"ad 7.1.7\",\n                    \"installDate\": null,\n                    \"installSource\": null,\n                    \"uninstallString\": \"\\\"C:\\\\Program Files (x86)\\\\AnyDesk\\\\AnyDesk.exe\\\" --uninstall\",\n                    \"installLocation\": \"\\\"C:\\\\Program Files\\\\AnyDesk\\\"\"\n                },\n                {\n                    \"displayName\": \"Visual Studio Community 2022\",\n                    \"version\": \"17.3.4\",\n                    \"installDate\": \"2022-09-26\",\n                    \"installSource\": null,\n                    \"uninstallString\": \"\\\"C:\\\\Program Files (x86)\\\\Microsoft Visual Studio\\\\Installer\\\\setup.exe\\\" uninstall --installPath \\\"C:\\\\Program Files\\\\Microsoft Visual Studio\\\\2022\\\\Community\\\"\",\n                    \"installLocation\": \"C:\\\\Program Files\\\\Microsoft Visual Studio\\\\2022\\\\Community\"\n                },\n                {\n                    \"displayName\": \"Google Chrome\",\n                    \"version\": \"108.0.5359.125\",\n                    \"installDate\": \"2022-12-16\",\n                    \"installSource\": null,\n                    \"uninstallString\": \"\\\"C:\\\\Program Files\\\\Google\\\\Chrome\\\\Application\\\\108.0.5359.125\\\\Installer\\\\setup.exe\\\" --uninstall --channel=stable --system-level --verbose-logging\",\n                    \"installLocation\": \"C:\\\\Program Files\\\\Google\\\\Chrome\\\\Application\"\n                },\n                {\n                    \"displayName\": \"Microsoft Edge\",\n                    \"version\": \"108.0.1462.54\",\n                    \"installDate\": \"2022-12-20\",\n                    \"installSource\": null,\n                    \"uninstallString\": \"\\\"C:\\\\Program Files (x86)\\\\Microsoft\\\\Edge\\\\Application\\\\108.0.1462.54\\\\Installer\\\\setup.exe\\\" --uninstall --msedge --channel=stable --system-level --verbose-logging\",\n                    \"installLocation\": \"C:\\\\Program Files\\\\Microsoft\\\\Edge\\\\Application\"\n                },\n                {\n                    \"displayName\": \"Microsoft Edge Update\",\n                    \"version\": \"1.3.171.37\",\n                    \"installDate\": null,\n                    \"installSource\": null,\n                    \"uninstallString\": null,\n                    \"installLocation\": null\n                },\n                {\n                    \"displayName\": \"Microsoft Edge WebView2 Runtime\",\n                    \"version\": \"108.0.1462.54\",\n                    \"installDate\": \"2022-12-20\",\n                    \"installSource\": null,\n                    \"uninstallString\": \"\\\"C:\\\\Program Files (x86)\\\\Microsoft\\\\EdgeWebView\\\\Application\\\\108.0.1462.54\\\\Installer\\\\setup.exe\\\" --uninstall --msedgewebview --system-level --verbose-logging\",\n                    \"installLocation\": \"C:\\\\Program Files\\\\Microsoft\\\\EdgeWebView\\\\Application\"\n                },\n                {\n                    \"displayName\": \"Npcap OEM\",\n                    \"version\": \"1.60\",\n                    \"installDate\": null,\n                    \"installSource\": null,\n                    \"uninstallString\": \"\\\"C:\\\\Program Files\\\\Npcap\\\\uninstall.exe\\\"\",\n                    \"installLocation\": \"C:\\\\Program Files\\\\Npcap\"\n                },\n                {\n                    \"displayName\": \"Network Discovery\",\n                    \"version\": \"2.6.6.8\",\n                    \"installDate\": \"2022-12-26\",\n                    \"installSource\": \"C:\\\\Users\\\\<USER>\\\\Projects\\\\sandbox\\\\NetworkDiscoveryCustomizeBuild\\\\DiscoveryTool\\\\NetworkDiscovery.Setup\\\\bin\\\\Release\\\\en-us\\\\\",\n                    \"uninstallString\": \"MsiExec.exe /I{007D4B3B-889A-4E19-94D4-11D84C908443}\",\n                    \"installLocation\": \"\"\n                },\n                {\n                    \"displayName\": \"PuTTY release 0.77\",\n                    \"version\": \"0.77.0.0\",\n                    \"installDate\": \"2022-10-26\",\n                    \"installSource\": \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\\",\n                    \"uninstallString\": \"MsiExec.exe /X{2B9F1234-D8ED-45C0-B865-511405F4734F}\",\n                    \"installLocation\": \"\"\n                },\n                {\n                    \"displayName\": \"Microsoft Visual C++ 2015-2022 Redistributable - 14.32.31332\",\n                    \"version\": \"14.32.31332.0\",\n                    \"installDate\": null,\n                    \"installSource\": null,\n                    \"uninstallString\": \"\\\"C:\\\\ProgramData\\\\Package Cache\\\\{3746f21b-c990-4045-bb33-1cf98cff7a68}\\\\VC_redist.x64.exe\\\"  /uninstall\",\n                    \"installLocation\": null\n                },\n                {\n                    \"displayName\": \"Windows Software Development Kit - Windows 10.0.19041.685\",\n                    \"version\": \"10.1.19041.685\",\n                    \"installDate\": null,\n                    \"installSource\": null,\n                    \"uninstallString\": \"\\\"C:\\\\ProgramData\\\\Package Cache\\\\{4591faf1-a2db-4a3d-bfda-aa5a4ebb1587}\\\\winsdksetup.exe\\\"  /uninstall\",\n                    \"installLocation\": null\n                },\n                {\n                    \"displayName\": \"Microsoft .NET Framework 4.6.2 SDK\",\n                    \"version\": \"4.6.01590\",\n                    \"installDate\": \"2022-09-28\",\n                    \"installSource\": \"C:\\\\ProgramData\\\\Package Cache\\\\{5F01B3C4-9BEC-465D-9C68-BB97D381FFAD}v4.6.01590\\\\packages\\\\netfxsdk\\\\\",\n                    \"uninstallString\": \"MsiExec.exe /X{5F01B3C4-9BEC-465D-9C68-BB97D381FFAD}\",\n                    \"installLocation\": \"\"\n                },\n                {\n                    \"displayName\": \"Microsoft .NET Runtime - 6.0.9\",\n                    \"version\": \"6.0.9.31619\",\n                    \"installDate\": null,\n                    \"installSource\": null,\n                    \"uninstallString\": \"\\\"C:\\\\ProgramData\\\\Package Cache\\\\{67950e91-8f8f-4d75-9252-7cca68ccdacc}\\\\dotnet-runtime-6.0.9-win-x64.exe\\\"  /uninstall\",\n                    \"installLocation\": null\n                },\n                {\n                    \"displayName\": \"WiX Toolset v3.11.2.4516\",\n                    \"version\": \"3.11.2.4516\",\n                    \"installDate\": null,\n                    \"installSource\": null,\n                    \"uninstallString\": \"\\\"C:\\\\ProgramData\\\\Package Cache\\\\{7a940384-ee12-4443-8aa3-2ab82df2372a}\\\\WiX311.exe\\\"  /uninstall\",\n                    \"installLocation\": null\n                },\n                {\n                    \"displayName\": \"Microsoft .NET Framework 4.6.2 Targeting Pack\",\n                    \"version\": \"4.6.01590\",\n                    \"installDate\": \"2022-09-28\",\n                    \"installSource\": \"C:\\\\ProgramData\\\\Package Cache\\\\{A18D4C2A-07A8-40E4-9797-DD324E6EA4FC}v4.6.01590\\\\packages\\\\netfxmtpack\\\\\",\n                    \"uninstallString\": \"MsiExec.exe /X{A18D4C2A-07A8-40E4-9797-DD324E6EA4FC}\",\n                    \"installLocation\": \"\"\n                },\n                {\n                    \"displayName\": \"Windows Agent\",\n                    \"version\": \"1.1.3.7\",\n                    \"installDate\": \"2022-10-28\",\n                    \"installSource\": \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\\",\n                    \"uninstallString\": \"MsiExec.exe /I{B97B5DA9-E1B6-4447-A6CC-C3EA7649EFB3}\",\n                    \"installLocation\": \"\"\n                },\n                {\n                    \"displayName\": \"Microsoft .NET Framework 4.6.2 Targeting Pack (ENU)\",\n                    \"version\": \"4.6.01590\",\n                    \"installDate\": \"2022-09-28\",\n                    \"installSource\": \"C:\\\\ProgramData\\\\Package Cache\\\\{C80951BD-6904-474F-BBC5-03A6C777F37C}v4.6.01590\\\\packages\\\\netfxmtpacklp\\\\enu\\\\\",\n                    \"uninstallString\": \"MsiExec.exe /X{C80951BD-6904-474F-BBC5-03A6C777F37C}\",\n                    \"installLocation\": \"\"\n                },\n                {\n                    \"displayName\": \"Windows SDK AddOn\",\n                    \"version\": \"10.1.0.0\",\n                    \"installDate\": \"2022-09-26\",\n                    \"installSource\": \"C:\\\\ProgramData\\\\Package Cache\\\\{E18618EC-D9DB-4BCE-B382-85ADA2CBB340}v10.1.0.0\\\\Redistributable\\\\10.1.0.0\\\\\",\n                    \"uninstallString\": \"MsiExec.exe /I{E18618EC-D9DB-4BCE-B382-85ADA2CBB340}\",\n                    \"installLocation\": \"\"\n                },\n                {\n                    \"displayName\": \"Git\",\n                    \"version\": \"2.37.3\",\n                    \"installDate\": \"2022-09-28\",\n                    \"installSource\": null,\n                    \"uninstallString\": \"\\\"C:\\\\Program Files\\\\Git\\\\unins000.exe\\\"\",\n                    \"installLocation\": \"C:\\\\Program Files\\\\Git\\\\\"\n                },\n                {\n                    \"displayName\": \"Microsoft Azure Compute Emulator - v2.9.7\",\n                    \"version\": \"2.9.8999.43\",\n                    \"installDate\": \"9/26-/2-022\",\n                    \"installSource\": \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\\\\\{04CA054C-2F40-44B0-8610-8D51EC9444FE}\\\\\",\n                    \"uninstallString\": \"msiexec /x{04ca054c-2f40-44b0-8610-8d51ec9444fe}\",\n                    \"installLocation\": \"\"\n                },\n                {\n                    \"displayName\": \"Microsoft Visual Studio 2010 Tools for Office Runtime\",\n                    \"version\": \"10.0.50903\",\n                    \"installDate\": null,\n                    \"installSource\": null,\n                    \"uninstallString\": \"c:\\\\Program Files\\\\Common Files\\\\Microsoft Shared\\\\VSTO\\\\10.0\\\\Microsoft Visual Studio 2010 Tools for Office Runtime (x64)\\\\install.exe\",\n                    \"installLocation\": \"c:\\\\Program Files\\\\Common Files\\\\Microsoft Shared\\\\VSTO\\\\10.0\\\\\"\n                },\n                {\n                    \"displayName\": \"Notepad++ (64-bit)\",\n                    \"version\": \"8.4.6\",\n                    \"installDate\": null,\n                    \"installSource\": null,\n                    \"uninstallString\": \"\\\"C:\\\\Program Files\\\\Notepad++\\\\uninstall.exe\\\"\",\n                    \"installLocation\": null\n                },\n                {\n                    \"displayName\": \"WinRAR 6.11\",\n                    \"version\": \"6.11.0\",\n                    \"installDate\": null,\n                    \"installSource\": null,\n                    \"uninstallString\": \"C:\\\\Program Files\\\\WinRAR\\\\uninstall.exe\",\n                    \"installLocation\": \"C:\\\\Program Files\\\\WinRAR\\\\\"\n                },\n                {\n                    \"displayName\": \"Microsoft .NET SDK 6.0.401 from Visual Studio\",\n                    \"version\": \"6.4.122.42113\",\n                    \"installDate\": \"2022-09-26\",\n                    \"installSource\": \"C:\\\\ProgramData\\\\Microsoft\\\\VisualStudio\\\\Packages\\\\Microsoft.NetCore.SdkPlaceholder.6.0.401-servicing.22421.13,version=17.3.32901.90,machinearch=x64\\\\\",\n                    \"uninstallString\": \"MsiExec.exe /X{0ADF0BD6-C434-42C7-8AC2-AE7177C28504}\",\n                    \"installLocation\": \"\"\n                },\n                {\n                    \"displayName\": \"Microsoft Web Deploy 4.0\",\n                    \"version\": \"10.0.5923\",\n                    \"installDate\": \"2022-09-26\",\n                    \"installSource\": \"C:\\\\ProgramData\\\\Microsoft\\\\VisualStudio\\\\Packages\\\\Microsoft.VisualStudio.WebDeploy.Msi,version=17.3.32708.82,chip=x64\\\\\",\n                    \"uninstallString\": \"MsiExec.exe /I{0C85743B-48E7-4948-96D6-C3BB90246418}\",\n                    \"installLocation\": \"\"\n                },\n                {\n                    \"displayName\": \"Microsoft SQL Server 2019 LocalDB \",\n                    \"version\": \"15.0.4153.1\",\n                    \"installDate\": \"2022-09-26\",\n                    \"installSource\": \"C:\\\\ProgramData\\\\Microsoft\\\\VisualStudio\\\\Packages\\\\sqllocaldb2019,version=17.0.62207.4110,chip=x64,language=en-US\\\\\",\n                    \"uninstallString\": \"MsiExec.exe /I{36E492B8-CB83-4DA5-A5D2-D99A8E8228A1}\",\n                    \"installLocation\": \"\"\n                },\n                {\n                    \"displayName\": \"Windows Subsystem for Linux Update\",\n                    \"version\": \"5.10.16\",\n                    \"installDate\": \"2022-09-27\",\n                    \"installSource\": \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\\",\n                    \"uninstallString\": \"MsiExec.exe /X{36EF257E-21D5-44F7-8451-07923A8C465E}\",\n                    \"installLocation\": \"\"\n                },\n                {\n                    \"displayName\": \"Microsoft Azure PowerShell - April 2018\",\n                    \"version\": \"5.7.0.18831\",\n                    \"installDate\": \"2022-09-26\",\n                    \"installSource\": \"C:\\\\ProgramData\\\\Microsoft\\\\VisualStudio\\\\Packages\\\\Microsoft.Azure.PowerShellNew,version=17.3.32708.82,chip=x64\\\\\",\n                    \"uninstallString\": \"MsiExec.exe /X{3BA7CAA9-97BA-4528-B7E1-B640910BB149}\",\n                    \"installLocation\": \"\"\n                },\n                {\n                    \"displayName\": \"DB Browser for SQLite\",\n                    \"version\": \"3.12.2\",\n                    \"installDate\": \"2022-10-18\",\n                    \"installSource\": \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\\",\n                    \"uninstallString\": \"MsiExec.exe /I{5211034D-495B-4A5E-9B8D-8961BBB2B9E2}\",\n                    \"installLocation\": \"C:\\\\Program Files\\\\DB Browser for SQLite\\\\\"\n                },\n                {\n                    \"displayName\": \"IIS 10.0 Express\",\n                    \"version\": \"10.0.06614\",\n                    \"installDate\": \"2022-09-26\",\n                    \"installSource\": \"C:\\\\ProgramData\\\\Microsoft\\\\VisualStudio\\\\Packages\\\\Microsoft.VisualStudio.IISExpress.Msi,version=17.3.32708.82,machinearch=x64\\\\\",\n                    \"uninstallString\": \"MsiExec.exe /X{56070933-B0D1-493F-8C12-4F7E83CA3071}\",\n                    \"installLocation\": \"\"\n                },\n                {\n                    \"displayName\": \"Microsoft System CLR Types for SQL Server 2019\",\n                    \"version\": \"15.0.2000.5\",\n                    \"installDate\": \"2022-09-26\",\n                    \"installSource\": \"C:\\\\ProgramData\\\\Microsoft\\\\VisualStudio\\\\Packages\\\\sqlsysclrtypes,version=17.0.62207.4110,chip=x64,language=en-US\\\\\",\n                    \"uninstallString\": \"MsiExec.exe /I{5BC7E9EB-13E8-45DB-8A60-F2481FEB4595}\",\n                    \"installLocation\": \"\"\n                },\n                {\n                    \"displayName\": \"Windows PC Health Check\",\n                    \"version\": \"3.6.2204.08001\",\n                    \"installDate\": \"2022-09-19\",\n                    \"installSource\": \"C:\\\\WINDOWS\\\\SoftwareDistribution\\\\Download\\\\8c65a163c8c24c7e95c0237bfc859210\\\\img\\\\\",\n                    \"uninstallString\": \"MsiExec.exe /X{6798C408-2636-448C-8AC6-F4E341102D27}\",\n                    \"installLocation\": \"\"\n                },\n                {\n                    \"displayName\": \"Microsoft Visual Studio Installer\",\n                    \"version\": \"3.4.2244.14676\",\n                    \"installDate\": \"2022-09-26\",\n                    \"installSource\": null,\n                    \"uninstallString\": \"\\\"C:\\\\Program Files (x86)\\\\Microsoft Visual Studio\\\\Installer\\\\setup.exe\\\" /uninstall\",\n                    \"installLocation\": \"\\\"C:\\\\Program Files\\\\Microsoft Visual Studio\\\\Installer\\\"\"\n                },\n                {\n                    \"displayName\": \"Microsoft SQL Server Compact 4.0 SP1 ENU\",\n                    \"version\": \"4.0.8876.1\",\n                    \"installDate\": \"2022-10-18\",\n                    \"installSource\": \"C:\\\\Program Files (x86)\\\\Microsoft SQL Server Compact Edition\\\\Installer\\\\\",\n                    \"uninstallString\": \"MsiExec.exe /X{78909610-D229-459C-A936-25D92283D3FD}\",\n                    \"installLocation\": \"C:\\\\Program Files\\\\Microsoft SQL Server Compact Edition\\\\\"\n                },\n                {\n                    \"displayName\": \"Microsoft Update Health Tools\",\n                    \"version\": \"3.68.0.0\",\n                    \"installDate\": \"2022-12-05\",\n                    \"installSource\": \"C:\\\\WINDOWS\\\\TEMP\\\\2F73FDBE-F968-4BD4-A5BE-B8C98F2945E7\\\\\",\n                    \"uninstallString\": \"MsiExec.exe /X{80F1AF52-7AC0-42A3-9AF0-689BFB271D1D}\",\n                    \"installLocation\": \"\"\n                },\n                {\n                    \"displayName\": \"Microsoft ODBC Driver 17 for SQL Server\",\n                    \"version\": \"********\\u0000\\u0000\\u0000\\u0000\\u0000\",\n                    \"installDate\": \"2022-09-26\",\n                    \"installSource\": \"C:\\\\ProgramData\\\\Microsoft\\\\VisualStudio\\\\Packages\\\\msodbcsql,version=17.0.62207.4110,chip=x64,language=en-US\\\\\",\n                    \"uninstallString\": \"MsiExec.exe /I{853997DA-6FCB-4FB9-918E-E0FF881FAF65}\",\n                    \"installLocation\": \"\"\n                },\n                {\n                    \"displayName\": \"Microsoft Azure Authoring Tools - v2.9.7\",\n                    \"version\": \"2.9.8999.45\",\n                    \"installDate\": \"2022-09-26\",\n                    \"installSource\": \"C:\\\\ProgramData\\\\Microsoft\\\\VisualStudio\\\\Packages\\\\Microsoft.Azure.AuthoringTools.Msi,version=2.9.8999.46,chip=x64\\\\\",\n                    \"uninstallString\": \"MsiExec.exe /I{90462BD2-DF5B-449C-A401-FCC1DC264E4E}\",\n                    \"installLocation\": \"\"\n                },\n                {\n                    \"displayName\": \"Microsoft Azure Libraries for .NET – v2.9\",\n                    \"version\": \"3.0.0127.060\",\n                    \"installDate\": \"2022-09-26\",\n                    \"installSource\": \"C:\\\\ProgramData\\\\Microsoft\\\\VisualStudio\\\\Packages\\\\Microsoft.Azure.ClientLibs.Msi,version=**********,chip=x64\\\\\",\n                    \"uninstallString\": \"MsiExec.exe /I{C5C91AA6-3E83-430E-8B7A-6B790083F28D}\",\n                    \"installLocation\": \"\"\n                },\n                {\n                    \"displayName\": \"GitHub Desktop\",\n                    \"version\": \"3.1.2\",\n                    \"installDate\": \"2022-11-14\",\n                    \"installSource\": null,\n                    \"uninstallString\": \"\\\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\GitHubDesktop\\\\Update.exe\\\" --uninstall\",\n                    \"installLocation\": \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\GitHubDesktop\"\n                },\n                {\n                    \"displayName\": \"Microsoft OneDrive\",\n                    \"version\": \"22.238.1114.0002\",\n                    \"installDate\": null,\n                    \"installSource\": null,\n                    \"uninstallString\": \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Microsoft\\\\OneDrive\\\\22.238.1114.0002\\\\OneDriveSetup.exe  /uninstall \",\n                    \"installLocation\": null\n                },\n                {\n                    \"displayName\": \"Slack\",\n                    \"version\": \"4.29.149\",\n                    \"installDate\": \"2022-11-15\",\n                    \"installSource\": null,\n                    \"uninstallString\": \"\\\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\slack\\\\Update.exe\\\" --uninstall\",\n                    \"installLocation\": \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\slack\"\n                },\n                {\n                    \"displayName\": \"Zoom\",\n                    \"version\": \"5.12.2 (9281)\",\n                    \"installDate\": null,\n                    \"installSource\": null,\n                    \"uninstallString\": \"\\\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\Zoom\\\\uninstall\\\\Installer.exe\\\" /uninstall\",\n                    \"installLocation\": \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\Zoom\\\\bin\"\n                },\n                {\n                    \"displayName\": \"Microsoft Visual Studio Code (User)\",\n                    \"version\": \"1.73.1\",\n                    \"installDate\": \"2022-11-10\",\n                    \"installSource\": null,\n                    \"uninstallString\": \"\\\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Programs\\\\Microsoft VS Code\\\\unins000.exe\\\"\",\n                    \"installLocation\": \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Programs\\\\Microsoft VS Code\\\\\"\n                }\n            ],\n            \"userAccountsInfo\": [],\n            \"domainName\": \"WORKGROUP\",\n            \"processorName\": \"11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz\",\n            \"processorCores\": \"4 Cores\",\n            \"processorLogicalCores\": \"8 Cores\",\n            \"processorArchitecture\": \"x64\",\n            \"memory\": \"203 GB\",\n            \"os\": \"Windows\",\n            \"osName\": \"Microsoft Windows 10 Pro\",\n            \"osVersion\": \"10.0.19045\",\n            \"osSerialNo\": \"00330-80000-00000-AA863\",\n            \"osBuildNo\": \"19045\",\n            \"systemUpTime\": \"52 Hours\",\n            \"ipAddress\": \"**************\",\n            \"remoteIpAddress\": \"************\",\n            \"windowsInstallationDate\": \"2022/08/29-15:08:13\",\n            \"diskEncryption\": \"Off\",\n            \"diskSpace\": \"477 GB\",\n            \"diskFreeSpace\": \"105 GB\",\n            \"screenSize\": \"15.3\",\n            \"manufacturer\": \"Dell Inc.\",\n            \"model\": \"Inspiron 5502\",\n            \"displayName\": \"DESKTOP-R83LFQL\",\n            \"pcSerialNo\": \"DL75T93\",\n            \"assetType\": \"Laptop\",\n            \"systemUuid\": \"4C4C4544-0030-5310-8031-B2C04F423633\",\n            \"deviceId\": \"8F41D00A-23DC-40C0-9156-B21E23B44486\",\n            \"appId\": \"aa5f77db-7c41-4074-8587-9a134cc5717c\",\n            \"hardwareVersion\": \"1.18.0\",\n            \"hostname\": \"DESKTOP-LCG7AN0.WORKGROUP\"\n        }\n    }\n]", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://secure.gogenuity-staging.com/api/v3/managed_assets.json", "protocol": "https", "host": ["secure", "gogenuity-staging", "com"], "path": ["api", "v3", "managed_assets.json"]}}, "response": []}, {"name": "UploadLogFile()", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "company_guid", "value": "7e38674c-87fd-4c8d-95b2-5920c09e2d5c", "type": "text"}, {"key": "source", "value": "agent", "type": "text"}, {"key": "computer_name", "value": "DESKTOP-R83LFQL", "type": "text"}, {"key": "mac_addresses", "value": "[\"D4:1B:81:14:0B:1B\"]", "type": "text", "disabled": true}, {"key": "pc_serial_no", "value": "DL75T93", "type": "text"}, {"key": "app_id", "value": "aa5f77db-7c41-4074-8587-9a134cc5717c", "type": "text"}, {"key": "file", "type": "file", "src": "/C:/Users/<USER>/Downloads/LatestLog_638355624720663177.zip"}]}, "url": {"raw": "https://assets-api.gogenuity-staging.com/api/v1/asset_discovery_logs.json", "protocol": "https", "host": ["assets-api", "gogenuity-staging", "com"], "path": ["api", "v1", "asset_discovery_logs.json"]}}, "response": []}, {"name": "PostStatus", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{ \n\"company_guid\": \"7e38674c-87fd-4c8d-95b2-5920c09e2d5c\", \n\"source\": \"agent\", \n\"mac_addresses\": \"{\\\"macList\\\":[\\\"D4:1B:81:14:0B:1B\\\"]}\", \n\"computer_name\": \"DESKTOP-R83LFQL\", \n\"ip_address\": \"**************\", \n\"app_id\":\"aa5f77db-7c41-4074-8587-9a134cc5717c\" ,\n\"app_version\": \"*******\",\n\"pc_serial_no\": \"DL75T93\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://assets-api.gogenuity-staging.com/api/v1/create_agent_location.json", "protocol": "https", "host": ["assets-api", "gogenuity-staging", "com"], "path": ["api", "v1", "create_agent_location.json"]}}, "response": []}]}