{"info": {"_postman_id": "3e2f02c1-633d-4a19-b43d-1735a82dcf53", "name": "WindowsActiveDirectory", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "********"}, "item": [{"name": "https://e250-***********-83.ngrok.io/api/v3/discovered_users.json", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"company_guid\": \"7e38674c-87fd-4c8d-95b2-5920c09e2d5c\",\n    \"data\": \n        [\n            {\n                \"user_account_control\": false,\n                \"email\" : \"<EMAIL>\"\n            } \n        ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "secure.gogenuity-staging.com/api/v3/discovered_users.json?company_guid=7e38674c-87fd-4c8d-95b2-5920c09e2d5c", "host": ["secure", "gogenuity-staging", "com"], "path": ["api", "v3", "discovered_users.json"], "query": [{"key": "data", "value": "{email: \"<EMAIL>\"}", "disabled": true}, {"key": "company_guid", "value": "7e38674c-87fd-4c8d-95b2-5920c09e2d5c"}]}, "description": "Create discovered user"}, "response": []}]}