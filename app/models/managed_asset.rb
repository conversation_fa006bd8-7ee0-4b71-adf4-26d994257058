class ManagedAsset < ActiveRecord::Base
  include Rewards
  include Events
  include PgSearch::Model
  include Manufacturers
  include Linkables
  include DeleteFormValue
  include ManagedAssets::Constants
  include AuditSource
  include IgnoreExistingAttrWithLowerPrecedence
  include DiscoveredManagedAssetFinder
  include TrackAttributesSources
  include UniquelyIdentified
  include OffPeakHoursHelper
  include SanitizesIpAddress
  include QuickViewFilterUpdate

  belongs_to  :creator, class_name: 'Contributor', foreign_key: 'creator_id'
  belongs_to  :asset_type, class_name: "CompanyAssetType", foreign_key: "company_asset_type_id"
  belongs_to  :custom_status, class_name: 'CompanyAssetStatus', foreign_key: 'company_asset_status_id'
  belongs_to  :location, optional: true
  belongs_to  :company
  belongs_to  :vendor, optional: true
  belongs_to  :product
  belongs_to  :agent_location, optional: true, dependent: :destroy
  belongs_to  :hardware_detail, polymorphic: true, dependent: :destroy
  has_one     :discovered_asset, dependent: :destroy
  has_one     :cost, dependent: :destroy
  has_one     :depreciation, through: :cost
  has_one     :assignment_information, dependent: :destroy
  has_many    :asset_usage_histories, dependent: :destroy
  has_one     :google_project, class_name: "Integrations::GoogleAssets::Project", dependent: :nullify
  has_one     :attributes_description, as: :entity, dependent: :destroy
  has_many    :asset_softwares, dependent: :destroy
  has_many    :asset_user_accounts, dependent: :destroy
  has_many    :managed_asset_tags, dependent: :destroy
  has_many    :help_tickets
  has_many    :alert_dates, as: :alertable, dependent: :destroy
  has_many    :managed_asset_attachments, dependent: :destroy
  has_many    :asset_sources, dependent: :destroy
  has_many    :cloud_asset_attributes, dependent: :destroy
  has_many    :merge_sources, as: :mergeable, dependent: :destroy
  has_many    :merged_selected_attributes, as: :source, dependent: :destroy
  has_many    :system_details, dependent: :destroy
  has_many    :smart_alerts, dependent: :destroy

  accepts_nested_attributes_for :cost
  accepts_nested_attributes_for :assignment_information
  accepts_nested_attributes_for :hardware_detail
  accepts_nested_attributes_for :asset_softwares
  accepts_nested_attributes_for :asset_user_accounts
  accepts_nested_attributes_for :alert_dates, allow_destroy: true, reject_if: :all_blank
  accepts_nested_attributes_for :managed_asset_attachments, allow_destroy: true, reject_if: :all_blank
  accepts_nested_attributes_for :smart_alerts

  before_validation :set_default_serial_number, if: Proc.new { |asset| asset.machine_serial_number.nil? }
  before_validation :sanitize_ip_address, if: Proc.new { |asset| asset.ip_address.present? && asset.ip_address.include?('%') }
  before_validation :set_default_model, if: Proc.new { |asset| asset.model.nil? }
  before_validation :remove_serial_number_empty_spaces, if: Proc.new { |asset| asset.machine_serial_number.present? }
  #`before_save` doesn't accept `:on` as an argument, so this callback was actually running on all save events, not just on update.
  before_save :set_event_type, :set_users_notification_status, :capitalize_mac_address, :check_asset_description
  #before_save :assign_default_manufacturer, on: :create, if: Proc.new { |asset| asset.manufacturer.nil? }
  before_create :assign_default_manufacturer, if: Proc.new { |asset| asset.manufacturer.nil? }
  before_create :create_guid
  before_create :assign_status, if: Proc.new { |asset| asset.custom_status.nil? }
  before_commit :set_created_by
  after_commit :create_intercom_event, :award_points_to_user, :update_discovered_assets_status, on: :create, if: Proc.new { |u| !u.skip_callbacks }
  after_commit :fetch_warranty_info, on: [:create, :update], if: Proc.new { |u| !u.skip_callbacks }
  after_commit :create_asset_source, on: [:create, :update], if: Proc.new { |u| !u.skip_callbacks }
  after_commit :delete_audits, on: :destroy
  after_commit :update_combined_asset, on: :update, if: Proc.new { |u| u.primary_asset && !u.skip_callbacks }
  after_commit :update_combined_asset_attribute, on: :update, if: Proc.new { |u| specified_updated_attr.present? && !u.skip_callbacks }
  after_create :assign_asset_id_to_hardware_detail, if: :hardware_detail
  after_commit :create_smart_alert, on: [:create]
  after_update :check_smart_alert_threshold
  after_update :update_cost_and_depreciation, if: Proc.new { saved_change_to_company_asset_type_id? }
  after_update :check_quick_view_filter, if: :saved_change_to_name?
  after_commit -> { check_quick_view_filter(true) }, on: :destroy

  enum source: [:manually_added, :uploaded, :agent, :selfonboarding, :probe, :meraki, :ubiquiti, :aws, :azure, :google, :merged_assets, :ms_intune, :azure_ad_devices, :kaseya, :kandji, :jamf_pro, :google_workspace]
  enum status: [:in_use, :ready_to_use, :needs_attention, :unusable]
  enum impact: [:Low, :Medium, :High]

  default_scope { where( merged: false ) }
  scope :computers, -> { joins(:asset_type).where("company_asset_types.name IN (?)", ["Desktop", "Laptop", "Server"]) }
  scope :phones, -> { joins(:asset_type).where("company_asset_types.name IN (?)", ["Phone", "Phone System"]) }
  scope :mobiles, -> { joins(:asset_type).where("company_asset_types.name IN (?)", ["Mobile", "Tablet"]) }
  scope :in_warranty, -> { where('warranty_expiration > ?', 3.months.from_now) }
  scope :expiring_warranty, -> { where('warranty_expiration > ? AND warranty_expiration <= ?', Date.today, 3.months.from_now ) }
  scope :expired_warranty, -> { where('warranty_expiration <= ?', Date.today) }
  scope :no_warranty, -> { where('warranty_expiration is NULL') }
  scope :owned_by, ->(contributor_id) { where('creator_id = ? ', contributor_id) }
  scope :archived, -> { where(archived: true) }
  scope :unarchived, -> { where.not(archived: true) }
  scope :active, -> { unarchived }
  scope :with_manufacturer_and_machine_serial_number, -> { where.not("machine_serial_number IS NULL OR manufacturer IS NULL OR machine_serial_number = '' OR manufacturer = ''") }

  validates_presence_of :company
  validates_presence_of :name, message: "is required"
  validates_presence_of :asset_type, message: "is required"
  validates_presence_of :description, message: "is required"
  validates_format_of :ip_address, :with => /(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])|(\/)([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])/i, if: -> { ip_address.present? }
  validates_uniqueness_of :machine_serial_number, scope: [:company, :merged], case_sensitive: false, :allow_blank => true, unless: -> { merged }
  validates_associated :assignment_information
  validate :validate_mac_addresses, on: :create, if: -> { machine_serial_number.blank? && mac_addresses.present? }
  validate :validate_updated_mac_addresses, on: :update, if: -> { machine_serial_number.blank? && mac_addresses.present? }

  attr_accessor :image_url, :image_filename, :image_data, :warranty_fetched_from_worker, :skip_callbacks

  audited except: AUDITED_EXCEPT_LIST, on: :update, associated_with: :hardware_detail
  has_associated_audits

  has_one_attached :image do |attachable|
    attachable.variant :thumb, resize_to_limit: [100, 100]
    attachable.variant :medium, resize_to_limit: [640, 640]
  end
  validates :image, content_type: ['image/png', 'image/jpeg', 'image/jpg'],
                    size: { less_than: 10.megabytes, message: 'File size is too large' }

  def validate_mac_addresses
    assets = self.company.managed_assets.where(mac_addresses: self.mac_addresses.map(&:upcase))
    if assets.count > 0
      errors.add :base, 'mac addresses has already been taken'
    end

    if Rails.env.production? && assets.count > 0
      Bugsnag.notify("Mac addresses has already been taken - on create - Company id: #{self.company_id}, Assets: #{assets.pluck(:id)}, Self: #{self.id}")
    end
  end

  def validate_updated_mac_addresses
    combined_assets_ids = []
    # loop through all the assets which are combined. e-g, A combined with B, B combined with C and so on
    current_asset = self
    while current_asset
      combined_assets_ids << current_asset.id
      current_asset = current_asset.combined_asset
    end

    exclude_ids = combined_assets_ids | self.merge_sources&.pluck(:source_id)
    exclude_ids = exclude_ids.compact
    # Exclude itself and combined asset from the list.
    assets = ManagedAsset.unscoped
                 .where(mac_addresses: self.mac_addresses.map(&:upcase))
                 .where(company_id: self.company_id)
                 .where(machine_serial_number: "")
                 .where.not(id: exclude_ids)
    errors.add :base, 'mac addresses has already been taken' if assets.count > 0
    if Rails.env.production? && assets.count > 0
      Bugsnag.notify("Mac addresses has already been taken - on update - Company id: #{self.company_id}, Assets: #{assets.pluck(:id)}, Self: #{self.id}")
    end
  end

  def imported?
    agent? || selfonboarding? || probe?
  end

  def set_default_model
    self.model = ''
  end

  def nuke
    self.assignment_information&.destroy
    self.asset_usage_histories&.destroy
    self.discovered_asset&.destroy
    self.cost&.destroy
    self.managed_asset_tags.destroy_all
    AssetSoftware.where(managed_asset_id: self.id).map {|s| s.destroy }
    AssetUserAccount.where(managed_asset_id: self.id).map {|s| s.destroy }
    self.destroy
  end

  def tags=(values)
    self.managed_asset_tags.destroy_all
    if values.present?
      values.each do |value|
        company_asset_tag = CompanyAssetTag.find_or_create_by(name: value.to_s.downcase, company_id: self.company_id)
        self.managed_asset_tags.new(company_asset_tag_id: company_asset_tag.id)
      end
    end
    self.managed_asset_tags
  end

  def tags
    asset_tags.pluck(:name)
  end

  def as_json(options = {})
    return super if options[:limited]
    super.merge(hardware_detail: hardware_detail&.as_json,
                asset_softwares: asset_softwares&.order(:name).as_json,
                asset_user_accounts: asset_user_accounts&.as_json,
                location: location&.as_json,
                vendor: vendor&.as_json,
                asset_type: asset_type&.name,
                cost: cost&.as_json,
                depreciation: cost&.depreciation&.as_json,
                sources: sources,
                asset_sources_data: updated_asset_sources&.as_json,
                agent_version: self.agent_location&.app_version&.as_json,
                collection_time: self.agent_location&.last_scanned_at&.as_json,
                warranty_status: warranty_status,
                system_details: system_details.pluck(:detail_category))
  end

  def updated_asset_sources
    asset_sources_data = []
    asset_sources.each do |asset_source|
      if asset_source.source == "manually_added"
        asset_source.asset_data&.delete("os")
      end
      asset_sources_data << asset_source
    end
    asset_sources_data
  end

  def assign_status
    self.custom_status = self.company.asset_statuses.find_by(name: 'In Use')
  end

  def to_hash
    hash = {}
    self.attributes.each { |k,v| hash[k] = v }
    hash
  end

  def sources
    srcs = []
    srcs << asset_sources.order("created_at")&.pluck(:source)
    srcs << source
    return srcs.flatten.compact.uniq
  end

  def assign_default_manufacturer
    mac_assets = ["macbook", "ipad", "iphone"]
    mac_assets.each do |mac|
      self.manufacturer = "Apple" if name.downcase.include?(mac)
    end
  end

  def build_hardware_detail(params)
    self.hardware_detail = hardware_detail_type.constantize.new(params) if hardware_detail_type
  end

  def assign_asset_id_to_hardware_detail
    self.hardware_detail.update_columns(managed_asset_id: self.id)
  end

  def mac_addresses_text=(value)
    self.mac_addresses = value&.split&.reject(&:blank?) || []
  end

  def mac_addresses_text
    self.mac_addresses&.to_a&.join("\n")
  end

  def brand=(value)
    self.manufacturer = value
  end

  def notes=(value)
    self.description = value
  end

  def get_asset_info_json
    edited_asset = self.as_json
    if custom_status.present?
      edited_asset['status'] = {
        id: custom_status.id,
        name: custom_status.name,
        icon: custom_status.icon,
      }
    end
    full_os_info = asset_sources.find_by(source: 'agent')&.asset_data
    edited_asset['full_operating_system'] = full_os_info['os_name'] if full_os_info.present?
    edited_asset['discovered_asset_id'] = self.discovered_asset&.id
    edited_asset['managed_asset_attachments'] = managed_asset_attachments.as_json
    edited_asset['name'] = self.name.present? ? self.name : ""
    asset_type_image_url = self.asset_type&.image&.url.to_s
    asset_type_image_file = self.asset_type&.image&.filename.to_s
    if self.image.attached?
      edited_asset['image_file_name'] = self.image.filename.to_s
      edited_asset['image_url'] = self.image.variant(:medium)&.processed&.url.to_s
      edited_asset['image_original_url'] = self.image.url
      edited_asset['image_medium_url'] = self.image.variant(:medium)&.processed&.url.to_s
    elsif asset_type_image_file.present?
      edited_asset['image_medium_url'] = asset_type_image_url
    else
      edited_asset['image_url']       = nil
      edited_asset['image_medium_url'] = nil
    end
    edited_asset['default_image_url'] = asset_type_image_url if asset_type_image_file.present?

    if self.assignment_information.present?
      edited_asset['department_id'] = self.assignment_information.department_id
      if self.assignment_information.user.present?
        used_by = self.assignment_information.user
        user_info = { id: used_by.id,
                      root_id: used_by.root_id,
                      type: used_by.contributor_type,
                      full_name: used_by&.name,
                      first_name: used_by&.first_name,
                      last_name: used_by&.last_name,
                      email: used_by&.email,
                      avatar: used_by.avatar,
                      phone: used_by.phone }
        edited_asset = edited_asset.merge(used_by: user_info)
      end

      if self.assignment_information.manager.present?
        managed_by = self.assignment_information.manager
        manager_info = { id: managed_by.id,
                        root_id: managed_by.root_id,
                        type: managed_by.contributor_type,
                        full_name: managed_by&.name,
                        first_name: managed_by&.first_name,
                        last_name: managed_by&.last_name,
                        email: managed_by&.email,
                        avatar: managed_by.avatar,
                        phone: managed_by.phone }
        edited_asset = edited_asset.merge(managed_by: manager_info)
      end
      edited_asset = edited_asset.merge(
        assignment_information_attributes: self.assignment_information,
        combined_asset: combined_asset_info
      ).as_json
    else
      edited_asset = edited_asset.merge(
        used_by: { first_name: '', last_name: '' }, 
        assignment_information_attributes: self.build_assignment_information,
        combined_asset: combined_asset_info
      ).as_json
    end

    help_tickets = []
    help_tickets = get_help_ticket_ids
    edited_asset = edited_asset.merge(cloud_asset_attributes: self.cloud_asset_attributes).as_json({})
    edited_asset = edited_asset.merge(cloud_location: cloud_location_name).as_json({})
    edited_asset = edited_asset.merge(help_tickets: help_tickets).as_json({})
    edited_asset[:tags] = asset_tags.as_json
    edited_asset[:tags_array] = asset_tags.pluck(:name)
    if self.smart_alerts
      edited_asset[:smart_alert] = self.smart_alerts
    end
    if self.linkable
      edited_asset[:linkable_id] = self.linkable.id
      edited_asset[:universal_links] = self.linkable.source_linkable_links.map { |ll| { id: ll.id, target: ll.target } }
    else
      edited_asset[:universal_links] = []
    end
    edited_asset = edited_asset.merge(cost_attributes: self.cost.present? ? self.cost : self.build_cost).as_json

    asset_type_name = self.asset_type.present? ? self.asset_type.name : "unknown"
    edited_asset = edited_asset.merge(asset_type_name: asset_type_name, warranty_status: self.warranty_status)
    edited_asset[:mac_addresses_text] = self.mac_addresses_text
    edited_asset[:alert_dates] = self.alert_dates.where('date >= ?', Date.today.beginning_of_day).order('date ASC')
    edited_asset[:company_channel_key] = self.company.guid
    edited_asset
  end

  def asset_tags
    @tags ||= self.managed_asset_tags.joins(:company_asset_tag).select('managed_asset_tags.id as id, company_asset_tags.name as name')
  end

  def warranty_status
    if self.warranty_expiration.nil?
      return "no_warranty"
    elsif self.warranty_expiration > 3.months.from_now
      return "in_warranty"
    elsif (self.warranty_expiration > Date.today) && (self.warranty_expiration <= 3.months.from_now)
      return "expiring_warranty"
    elsif self.warranty_expiration <= Date.today
      return "expired_warranty"
    end
  end

  def fetch_warranty_info
    if saved_change_to_id? || (saved_change_to_manufacturer? || saved_change_to_product_number? || saved_change_to_machine_serial_number?)
      if self.source == "uploaded"
        FetchWarrantyExpirationDateWorker.perform_at(next_three_am_chicago_time, self.id)
      else
        FetchWarrantyExpirationDateWorker.perform_async(self.id)
      end
    end
  end

  def self.search_text(query)
    sql = """
      managed_assets.name ilike ? OR
        managed_assets.machine_serial_number ilike ? OR
        managed_assets.ip_address ilike ? OR
        array_to_string(managed_assets.mac_addresses, '||') ilike ? OR
        managed_assets.asset_tag ilike ?
    """
    where(sql, "%#{query}%", "%#{query}%", "%#{query}%", "%#{query}%", "%#{query}%")
  end

  def self.search_results(query)
    sql = """
      name ilike ? OR
        machine_serial_number ilike ?
    """
    sql = """
      to_tsvector('default', name) @@ to_tsquery('default', ?)
        or to_tsvector('default', machine_serial_number) @@ to_tsquery('default', ?)
    """
    #where(sql, "%#{query}%", "%#{query}%")
    where(sql, query, query)
  end

  def get_help_ticket_ids
    help_ticket_ids = CustomFormValue.where(value_int: self.id, module_type: "HelpTicket")
                        .joins(:custom_form_field, :help_ticket)
                        .where(custom_form_fields: {field_attribute_type: "asset_list"}, help_tickets: {archived: false})
                        .pluck(:module_id)
    help_ticket_ids
  end

  def get_asset_data
    data = {
      os: self.operating_system,
      status: self.status,
      ipAddress: self.ip_address,
      manufacturer: self.manufacturer,
      machineSerialNo: self.machine_serial_number,
      macAddresses: self.mac_addresses,
      purchasePrice: self.cost&.purchase_price,
      acquisitionDate: self.acquisition_date,
      warrantyExpiration: self.warranty_expiration,
      location: self.location&.name&.to_s&.strip,
      displayName: self.name,
      assetType: self.asset_type.name,
      tags: self.tags,
    }
    data[:memory] = self.hardware_detail.try(:memory)
    data[:harddisk] = self.hardware_detail.try(:hard_drive)
    data[:processor] = self.hardware_detail.try(:processor)
    data
  end

  def merged_assets
    ids = MergeSource.where(mergeable: self).pluck(:source_id)
    ManagedAsset.unscoped.where(id: ids)
  end

  def combined_asset
    ManagedAsset.unscoped do
      MergeSource.find_by(source_id: self.id)&.mergeable
    end
  end

  def field_type
    CustomForms::FieldType.new(self.class.name).call
  end

  private

  def specified_updated_attr
    @updated_fields ||= self.saved_changes.keys & self.merged_selected_attributes&.pluck(:name)
  end

  def filtered_changed_attributes(record, exclude = [])
    changed_keys = record.saved_changes.keys - exclude
    changed_attributes = record.attributes.slice(*changed_keys)
    changed_attributes.compact_blank!
    changed_attributes
  end

  def update_combined_asset
    if combined_asset.present?
      combined = combined_asset
      combined.assign_attributes(filtered_changed_attributes(self, attributes_to_scrub))
      combined.location_id = combined_asset.location_id if has_manually_updated_location?
      combined.name = combined_asset_name

      combined.cost = self.cost.dup if self.cost
      used_id = self.assignment_information&.used_by_contributor_id || combined.assignment_information&.used_by_contributor_id
      managed_id = self.assignment_information&.managed_by_contributor_id || combined.assignment_information&.managed_by_contributor_id

      unless has_manually_updated_assignment?(combined)
        combined.assignment_information[:managed_by_contributor_id] = managed_id
        combined.assignment_information[:used_by_contributor_id] = used_id
      end

      # Except for the scrub list, update the 'hardware detail' polymorphic association attributes.
      combined.asset_softwares = self.asset_softwares.reduce([]) { |softwares, software| softwares.push(software) }
      combined.asset_user_accounts = self.asset_user_accounts.reduce([]) { |user_accounts, user_account| user_accounts.push(user_account) }
      combined.cloud_asset_attributes =  self.cloud_asset_attributes.reduce([]) { |attrs, attr| attrs.push(attr) }
      if self.hardware_detail && combined.hardware_detail
        combined.hardware_detail.update!(self.hardware_detail.attributes.except(*attributes_to_scrub))
      end

      # Update asset sources for combined asset
      asset_sources.where.not(source: :merged_assets).find_each do |src|
        asset_src = AssetSource.find_by(source: src.source, managed_asset_id: combined_asset.id)
        if asset_src.blank?
          asset_src = AssetSource.new(source: src.source, asset_data: src.asset_data, managed_asset_id: combined_asset.id)
        end
        combined.asset_sources << asset_src
      end

      combined.save!
    end
  end

  def update_combined_asset_attribute
    asset = combined_asset
    specified_updated_attr.each { |key| asset[key] = self[key] }
    asset.save!
  end

  def capitalize_mac_address
    self.mac_addresses = mac_addresses&.compact&.map(&:upcase)
  end

  def check_asset_description
    self.description = '' if self.description.nil?
  end

  def set_default_serial_number
    self.machine_serial_number = ''
  end

  def create_asset_source
    if self.source == 'manually_added'
      managed_asset_source = AssetSource.find_or_initialize_by(managed_asset_id: self.id, source: self.source)
      managed_asset_source.asset_data = get_asset_data
      managed_asset_source.save!
    end
  end

  def delete_audits
    self.own_and_associated_audits.delete_all
  end

  def cloud_location_name
    cloud_asset = ["aws", "azure", "google"] & self.asset_sources.pluck(:source)
    self.discovered_asset&.integration_location&.address if cloud_asset.present?
  end

  def attributes_to_scrub
    %w[id merged deleted_at hardware_detail_id created_at updated_at archived is_combined_asset agent_location_id]
  end

  def combined_asset_info
    combined_asset.present? ? {id: combined_asset.id, name: combined_asset.name} : nil
  end

  def combined_asset_name
    # If the user manually modified the name, it will remain unchanged and will not be updated.
    manually_added_name || self.name || combined_asset.name
  end

  def manually_added_name
    # It returns the name that the user modified.
    combined_asset.name if combined_asset.audits.any? { |audit| audit.audited_changes["name"] && audit.user_id }
  end

  def update_discovered_assets_status
    if self.source == "manually_added" || self.source == "uploaded"
      discovered_asset = find_discovered_asset(company, false, machine_serial_number, mac_addresses, name, ip_address, manufacturer, false)
      if discovered_asset.present?
        discovered_asset.status = :imported
        discovered_asset.managed_asset_id = self.id
        discovered_asset.save!
      end
    end
  end

  def remove_serial_number_empty_spaces
    self.machine_serial_number = self.machine_serial_number.strip
  end

  def has_manually_updated_assignment?(asset)
    asset.assignment_information.audits.any? { |audit| audit.audited_changes["source"] == "manually_added" }
  end

  def has_manually_updated_location?
    combined_asset.audits.where(associated_type: "ComputerDetail").any? {
      |audit| audit.audited_changes["location_id"] && audit.audited_changes["source"] == "manually_added"
    }
  end

  def create_smart_alert
    SmartAlert.find_or_create_by!(managed_asset_id: self.id)
  end

  def check_smart_alert_threshold
    if self.smart_alerts.present?
      first_alert = self.smart_alerts.first
      if first_alert.threshold.present? && first_alert.active && first_alert.recipients.any?
        ManagedAssetEvents::SendEmail.execute_smart_alert(self) if check_disk_space_usage(first_alert.threshold.to_i)
      end
    end
  end

  def check_disk_space_usage(threshold)
    return false unless self.hardware_detail.respond_to?(:disk_free_space) && self.hardware_detail.respond_to?(:hard_drive)

    available_disk_space = self.hardware_detail.disk_free_space.to_f
    total_disk_space = self.hardware_detail.hard_drive.to_f

    return false if total_disk_space.zero?

    used_disk_space_percentage = ((total_disk_space - available_disk_space) / total_disk_space) * 100
    free_space = 100 - used_disk_space_percentage

    free_space < threshold
  end

  def update_cost_and_depreciation
    asset_lifecycle = self.asset_type.asset_lifecycles.first

    if asset_lifecycle.present?
      new_cost_attributes = {
        asset_lifecycle_id: asset_lifecycle.id,
        purchase_price: asset_lifecycle.purchase_price,
        po: asset_lifecycle.po,
        salvage: asset_lifecycle.salvage,
        lifecycle_type: 'by_type',
        replacement_cost: asset_lifecycle.replacement_cost,
        useful_life: asset_lifecycle.useful_life
      }

      self.cost = Cost.new(new_cost_attributes)
    end
    self.cost.save! if self.cost.present?
  end
end
