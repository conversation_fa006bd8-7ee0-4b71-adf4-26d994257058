<template>
  <div class="row mt-5">
    <div class="col-auto pr-5 d-sm-none d-md-block">
      <div ref="sidebarWrapper" class="sidebar-wrapper">
        <div
          ref="sidebarMenu"
          class="sidebar-menu position-static"
          :class="{
            'position-fixed': stickySideBar,
            'sidebar-menu--modal': isModal,
          }"
          :style="isModal ? { top: sideMenuTopValue + 'px' } : null"
        >
          <a
            v-for="item in sectionItems"
            ref="sectionMenuRefs"
            :key="item.id"
            href="#"
            class="side-menu-item"
            :class="{ active: item.active, required: item.required }"
            :data-tc-assets-side-menu="item.title"
            @click.stop.prevent="scrollToElement(item)"
          >
            <i :class="item.icon" />
            {{ item.title }}
          </a>
        </div>
      </div>
    </div>

    <div class="col">
      <form
        id="asset_form"
        ref="assetForm"
        accept-charset="UTF-8"
        enctype="multipart/form-data"
        class="mb-5"
      >
        <div class="box__inner">
          <h5
            id="basic-details"
            :ref="`sectionItems_${Sections.Basic}`"
            class="font-weight-normal scroll-elem"
          >
            Basic Details
          </h5>
          <hr class="mt-0" />
          <div class="row">
            <div class="col-sm-12 col-lg-6">
              <div class="form-group">
                <label for="managed_asset_name"
                  >Name <span class="required"
                /></label>
                <input
                  id="managed_asset_name"
                  v-model="asset.name"
                  v-validate="'required|max:100'"
                  data-tc-managed-asset-name
                  type="text"
                  class="form-control"
                  :class="{ 'is-invalid': errors.has('managed_asset[name]') }"
                  data-vv-as="asset name"
                  name="managed_asset[name]"
                />
                <div
                  v-show="errors.has('managed_asset[name]')"
                  class="form-text text-danger small"
                >
                  {{ errors.first("managed_asset[name]") }}
                </div>
              </div>
            </div>
            <div class="col-sm-12 col-lg-6">
              <div class="form-group">
                <label for="managed_asset_fullname"
                  >Full Name <span class="required"
                /></label>
                <input
                  id="managed_asset_fullname"
                  v-model="asset.fullname"
                  v-validate="'required|max:200'"
                  data-tc-managed-asset-fullname
                  type="text"
                  class="form-control"
                  :class="{
                    'is-invalid': errors.has('managed_asset[fullname]'),
                  }"
                  data-vv-as="full name"
                  name="managed_asset[fullname]"
                />
                <div
                  v-show="errors.has('managed_asset[fullname]')"
                  class="form-text text-danger small"
                >
                  {{ errors.first("managed_asset[fullname]") }}
                </div>
              </div>
            </div>
            <div class="col-sm-12 col-lg-6">
              <div class="form-group">
                <label for="managed_asset_company_asset_type_id"
                  >Asset Type <span class="required"
                /></label>
                <multi-select
                  id="managed_asset_asset_type_id"
                  v-validate="'required'"
                  placeholder="Select an asset type"
                  data-vv-as="asset type"
                  track-by="id"
                  name="managed_asset[company_asset_type_id]"
                  label="name"
                  class="asset-type"
                  deselect-label="Can't remove selected asset type"
                  :class="{
                    'is-invalid': errors.has(
                      'managed_asset[company_asset_type_id]'
                    ),
                  }"
                  :allow-empty="false"
                  :multiple="false"
                  :options="assetTypes"
                  :value="assetValue"
                  data-tc-managed-asset-type-id
                  @input="assignAssetType"
                  @open="getAssetTypes"
                />
                <div
                  v-show="errors.has('managed_asset[company_asset_type_id]')"
                  class="form-text text-danger small"
                >
                  {{ errors.first("managed_asset[company_asset_type_id]") }}
                </div>
              </div>
            </div>
            <div class="col-sm-12 col-lg-6">
              <div class="form-group">
                <label for="managed_asset_asset_tag"
                  >Asset Tag
                  <span class="small text-muted ml-1">(optional)</span></label
                >
                <input
                  id="managed_asset_asset_tag"
                  v-model="asset.assetTag"
                  type="text"
                  class="form-control"
                  :class="{
                    'is-invalid': errors.has('managed_asset[assetTag]'),
                  }"
                  name="managed_asset[assetTag]"
                  data-tc-asset-tag
                />
                <i
                  id="asset-tag-tip"
                  v-tooltip="'This tag will be displayed for asset QR code'"
                  class="genuicon-info-circled h6 text-muted"
                />
                <div
                  v-show="errors.has('managed_asset[assetTag]')"
                  class="form-text text-danger small"
                >
                  {{ errors.first("managed_asset[assetTag]") }}
                </div>
              </div>
            </div>
            <div class="col-sm-12 col-md-6">
              <div class="form-group assets-impact">
                <label for="managed_asset_impact"
                  >Impact
                  <span class="small text-muted ml-1">(optional)</span></label
                >
                <impact-field :asset="asset" />
              </div>
            </div>

            <div class="col-sm-12">
              <div class="form-group">
                <label for="managed_asset_vendor_id">
                  Vendor
                  <span class="small text-muted ml-1">(optional)</span>
                </label>
                <vendor-select
                  id="tc-vendor-field"
                  name="managed_asset[vendor_id]"
                  include-default-vendors
                  :class="{
                    'is-invalid': errors.has('managed_asset[vendor_id]'),
                  }"
                  :value="asset.vendor"
                  @input="setVendor"
                  @adding-new-vendor="createVendor"
                />
              </div>
            </div>

            <div class="col-sm-12">
              <div class="form-group">
                <label for="managed_asset_description"
                  >Notes
                  <span class="small text-muted ml-1">(optional)</span></label
                >
                <textarea
                  id="managed_asset_description"
                  v-model="asset.description"
                  class="form-control"
                  name="managed_asset[description]"
                  data-tc-managed-asset-description
                />
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-12">
              <h5
                id="tech-specs"
                :ref="`sectionItems_${Sections.TechSpecs}`"
                class="mt-3 font-weight-normal scroll-elem"
              >
                Tech Specs
              </h5>
              <hr class="mt-0" />
              <div v-if="!loading">
                <div class="form-group">
                  <div
                    v-if="
                      asset.hardwareDetailType &&
                      asset.hardwareDetailType !== 'OtherDetail'
                    "
                    class="bg-lighter clearfix px-4 pt-4 pb-2"
                  >
                    <p class="text-cyan">
                      {{ assetTypeName }} Hardware Details
                    </p>

                    <hardware-fields
                      :asset="asset"
                      :lock-field="lockField"
                      @change-firmware="changeFirmware"
                      @change-ports="changePorts"
                      @change-sku="changeSku"
                      @change-serial-number="changeSerialNumber"
                      @change-product-id="changeProductId"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div class="col-sm-12">
              <div class="form-group">
                <label
                  >MAC Addresses
                  <span class="small text-muted ml-1">(optional)</span></label
                >
                <textarea
                  id="hardware_detail_mac_address"
                  v-model="asset.macAddressesText"
                  v-validate="'macAddress'"
                  class="form-control"
                  :class="{
                    isvalid: errors.has('managed_asset[mac_addresses_text]'),
                  }"
                  rows="4"
                  name="managed_asset[mac_addresses_text]"
                  data-vv-as="mac address"
                  data-tc-mac-address
                />
                <span
                  v-if="errors.has('managed_asset[mac_addresses_text]')"
                  class="small text-danger"
                  data-tc-message="invalid mac"
                >
                  Please enter the valid MAC addresses</span
                >
              </div>
            </div>
            <div class="col-sm-12 col-md-6">
              <div class="form-group">
                <label data-tc-label="ip address"
                  >IP Address
                  <span class="small text-muted ml-1">(optional)</span></label
                >
                <input
                  id="hardware_detail_ip_address"
                  v-model="asset.ipAddress"
                  v-validate="'ipAddress'"
                  class="form-control"
                  :class="{ isvalid: errors.has('managed_asset[ip_address]') }"
                  name="managed_asset[ip_address]"
                  data-tc-ip-address
                />
                <span
                  v-if="errors.has('managed_asset[ip_address]')"
                  class="small text-danger"
                  data-tc-message="invalid ip"
                >
                  Please enter the valid IP address.
                </span>
              </div>
            </div>
            <div class="col-sm-12 col-md-6">
              <div class="form-group">
                <label for="managed_asset_manufacturer"
                  >Manufacturer
                  <span class="small text-muted ml-1">(optional)</span></label
                >
                <input
                  id="managed_asset_manufacturer"
                  v-model="asset.manufacturer"
                  maxlength="50"
                  class="form-control"
                  name="managed_asset[manufacturer]"
                  type="text"
                  data-tc-managed-asset-manufacturer
                />
              </div>
            </div>
            <div
              v-if="
                asset.hardwareDetailType &&
                asset.hardwareDetailType === 'OtherDetail'
              "
              class="col-sm-12 col-md-6"
            >
              <div class="form-group">
                <label for="managed_asset_sku"
                  >Model Number
                  <span class="small text-muted ml-1">(optional)</span></label
                >
                <input
                  id="managed_asset_sku"
                  v-model="asset.sku"
                  maxlength="100"
                  class="form-control"
                  name="managed_asset[sku]"
                  type="text"
                  placeholder="sku, upc, mpn, cpn, ean, getin etc"
                  data-tc-managed-asset-sku
                />
              </div>
            </div>
            <div class="col-sm-12 col-md-6">
              <div class="form-group">
                <label for="managed_asset_model"
                  >Model
                  <span class="small text-muted ml-1">(optional)</span></label
                >
                <input
                  id="managed_asset_model"
                  v-model="asset.model"
                  maxlength="70"
                  class="form-control"
                  :disabled="lockField && permissionAsset.model"
                  name="managed_asset[model]"
                  type="text"
                  data-tc-managed-asset-model
                />
              </div>
            </div>
            <div class="col-sm-12 col-md-6">
              <div class="form-group control">
                <label for="vendor_default_tags"
                  >Tags
                  <span class="small text-muted ml-1">(optional)</span></label
                >
                <multi-select
                  id="asset_default_tags"
                  class="w-100 mb-1"
                  placeholder="Type in a new tag"
                  tag-placeholder="Add a tag"
                  :allow-empty="true"
                  :multiple="true"
                  :taggable="true"
                  :options="tagOptions"
                  :value="asset.tagsArray"
                  data-tc-tag
                  @remove="removeTag"
                  @select="selectTag"
                  @tag="addNewTag"
                />
              </div>
            </div>
            <div class="col-sm-12 col-md-6">
              <div class="form-group">
                <label for="managed_asset_system_uuid"
                  >System UUID
                  <span class="small text-muted ml-1">(optional)</span></label
                >
                <input
                  id="managed_asset_system_uuid"
                  v-model="asset.systemUuid"
                  maxlength="70"
                  class="form-control"
                  name="managed_asset[system_uuid]"
                  type="text"
                  :disabled="lockField && permissionAsset.systemUuid"
                  data-tc-managed-asset-system-uuid
                />
              </div>
            </div>
            <div class="col-sm-12 col-md-6">
              <div class="form-group">
                <label for="managed_asset_system_up_time"
                  >System Up Time
                  <span class="small text-muted ml-1">(optional)</span></label
                >
                <input
                  id="managed_asset_system_up_time"
                  v-model="asset.systemUpTime"
                  maxlength="70"
                  class="form-control"
                  name="managed_asset[system_up_time]"
                  type="text"
                  placeholder="144 hours"
                  :disabled="lockField && permissionAsset.systemUpTime"
                  data-tc-managed-asset-system-up-time
                />
              </div>
            </div>
          </div>
          <h5
            id="location-and-usage"
            :ref="`sectionItems_${Sections.Location}`"
            class="mt-5 font-weight-normal scroll-elem"
          >
            Location &amp; Usage
          </h5>
          <hr class="mt-0" />

          <location-usage-fields
            :managed-asset="asset"
            :asset-type="assetTypeName"
            @input="updateUsage"
            @open-location-modal="$refs.newLocationModal.open()"
            @open-user-modal="openNewUserModal"
          />
          <div class="row">
            <div class="col-sm-12">
              <h5
                id="cost"
                :ref="`sectionItems_${Sections.Cost}`"
                class="font-weight-normal scroll-elem"
              >
                Cost Depreciation
              </h5>
              <hr class="mt-0" />
            </div>
            <div class="col-sm-12">
              <lifecycle-form
                is-asset-form
                :data="costAttributes"
                @assign-custom-lifecycle="assignCustomLifecycle"
                @un-assign-custom-lifecycle="removeLifecycle"
                @lifecycle-data="updateLifecycleData"
              />
            </div>
          </div>
          <div>
            <h5
              id="warranty-and-acquisition"
              :ref="`sectionItems_${Sections.Warranty}`"
              class="mt-5 font-weight-normal scroll-elem"
            >
              Warranty & Acquisition
            </h5>

            <hr class="mt-0" />

            <warranty-acquisition-fields
              :managed-asset="asset"
              :lock-field="lockField"
              :permission-asset="permissionAsset"
              @input="updateWarrantyFields"
            />
          </div>
          <div class="asset-alert" data-tc-asset-alert-date-div>
            <h5
              id="asset-alert"
              :ref="`sectionItems_${Sections.Alert}`"
              class="mt-5 font-weight-normal scroll-elem"
            >
              Alerts
            </h5>
            <hr class="mt-0" />

            <alert-dates :managed-asset="asset" @asset-alerts="assetAlerts" />
          </div>
          <div>
            <h5
              id="additional-details"
              :ref="`sectionItems_${Sections.AdditionalDetails}`"
              class="mt-3 font-weight-normal scroll-elem"
            >
              Additional Details
            </h5>
            <hr class="mt-0" />

            <universal-links
              :key="linksKey"
              :label-classes="[]"
              :universal-links="universalLinks"
              :source-id="asset.id"
              @remove="removeLink"
              @add="addLink"
            />

            <div class="form-groups my-4">
              <label>
                Attachments
                <span class="small text-muted ml-1">(optional)</span></label
              >
              <attachment-input
                :accept="['images', 'docs']"
                :show-label="true"
                @input="addFile"
              />
            </div>

            <div v-if="files.length" class="form-group attachments-block">
              <div v-for="file in files" :key="file.id">
                <a
                  class="help-ticket-attachment__link float-left"
                  target="_blank"
                >
                  <h6 class="d-inline-block help-ticket__new-file mt-1">
                    <i class="nulodgicon-file-o ml-2" />
                    {{ file.name }}
                  </h6>
                </a>
                <div class="float-right">
                  <a
                    class="ml-4 help-ticket-attachment-delete float-left"
                    @click="deleteFile(file)"
                  >
                    <i class="nulodgicon-trash-b" />
                  </a>
                </div>
              </div>
            </div>
            <div
              v-if="
                asset.managedAssetAttachments &&
                asset.managedAssetAttachments.length
              "
              class="form-group attachments-block"
            >
              <div v-for="file in asset.managedAssetAttachments" :key="file.id">
                <a
                  class="help-ticket-attachment__link float-left"
                  target="_blank"
                >
                  <h6 class="d-inline-block help-ticket__new-file mt-1">
                    <i class="nulodgicon-file-o ml-2" />
                    {{ file.name }}
                  </h6>
                </a>
                <div class="float-right">
                  <a
                    class="ml-4 help-ticket-attachment-delete float-left"
                    @click="removeFile(file)"
                  >
                    <i class="nulodgicon-trash-b" />
                  </a>
                </div>
              </div>
            </div>

            <div class="form-group pb-5">
              <label
                >Asset Image
                <span class="small text-muted ml-1">(optional)</span>
              </label>
              <p class="small">
                Upload image in one of the following formats: .png, .jpg, .jpeg.
                Images cannot be larger than 10mb.
              </p>
              <div class="row">
                <div class="col-lg-4 col-sm-12">
                  <image-input
                    as-file
                    :src="imageSrc"
                    :default-image="checkDefaultImage"
                    :module-prefix="modulePrefix"
                    @input="changeAvatarUrl"
                  />
                </div>
              </div>
            </div>
          </div>

          <div
            class="sticky-btn-holder bg-lighter text-center border-top border-light"
          >
            <div class="col-12 col-md-9 col-lg-10 ml-auto">
              <button
                class="btn btn-link text-secondary mr-3"
                type="button"
                @click.prevent.stop="cancelForm"
              >
                Cancel
              </button>
              <submit-button
                id="save_asset"
                :is-saving="submitted"
                :btn-classes="'px-4'"
                :conditional-class="{
                  'save-asset-btn--fixed': !hasScrolledPastFormEnd,
                }"
                btn-content="Save asset"
                saving-content="Saving assets"
                @submit="validateThenSave"
              />
            </div>
          </div>
        </div>
      </form>
    </div>

    <div class="col-auto pl-5 pl-sm-3 mb-sm-5">
      <tips-and-tricks :tips="tipsAndTricks" />
    </div>
    <sweet-modal
      ref="newLocationModal"
      v-sweet-esc
      blocking
      title="Add a new location"
    >
      <template slot="default">
        <location-custom-form-viewer
          render-from-modal
          @new-location="newLocation"
        />
      </template>
    </sweet-modal>

    <sweet-modal
      ref="newUserModal"
      v-sweet-esc
      title="Add a new teammate"
      blocking
    >
      <template slot="default">
        <company-user-custom-form-viewer
          render-from-modal
          new-form
          @new-user="newUser"
        />
      </template>
    </sweet-modal>

    <sweet-modal
      ref="newAssetTypeWarning"
      v-sweet-esc
      title="Changing asset type?"
    >
      <template slot="default">
        <div>
          <p>
            You might lose some details from this asset. Are you sure you want
            to change the asset type?
          </p>
          <div v-if="showCheckBox">
            <b>Optional Actions</b>
            <div class="not-as-small mt-2">
              <label
                class="mb-0 clickable flex"
                @click.stop.prevent="onRemoveCostValues"
              >
                <input type="checkbox" :checked="removeCostValues" />
                <i class="nulodgicon-checkmark checkbox" />
              </label>
              <span class="ml-2">Remove cost & deprciation values</span>
            </div>
          </div>
        </div>
      </template>
      <button
        slot="button"
        class="btn btn-link text-secondary"
        @click="closeNewAssetTypeWarning"
      >
        Cancel
      </button>
      <button
        slot="button"
        class="btn btn-primary ml-2"
        data-tc-confirm-change-type
        @click="changeDetails"
      >
        Yes, change asset type
      </button>
    </sweet-modal>
    <sweet-modal
      ref="costValuesWarningModal"
      v-sweet-esc
      title="Before proceeding..."
    >
      <template slot="default">
        <p>Do you want to remove cost depreciation values?<br /><br /></p>
        <div class="sweet-buttons sweet-custom-footer pb-0 pt-3 border-top">
          <button
            slot="button"
            class="btn btn-link text-secondary mr-2"
            @click.prevent="closeCostValuesWarningModal"
          >
            Cancel
          </button>
          <button
            slot="button"
            class="btn btn-primary"
            @click.prevent="removeCostDataAndCloseModal"
          >
            Yes, remove cost values
          </button>
        </div>
      </template>
    </sweet-modal>
    <sweet-modal
      ref="fileModal"
      v-sweet-esc
      icon="error"
      title="Sorry, that file is too big to upload"
    />
  </div>
</template>

<script>
import http from "common/http";
import _debounce from "lodash/debounce";
import { SweetModal } from "sweet-modal-vue";
import LocationCustomFormViewer from "components/shared/custom_forms/custom_form_viewer/location_custom_form_viewer.vue";
import CompanyUserCustomFormViewer from "components/shared/custom_forms/custom_form_viewer/company_user_custom_form_viewer.vue";
import universalLinksForm from "mixins/universal_links_form";
import assetImages from "mixins/asset_images";
import assetSpecific from "mixins/asset_specific";
import permissionsHelper from "mixins/permissions_helper";
import { mapGetters, mapMutations, mapActions } from "vuex";
import MultiSelect from "vue-multiselect";
import VendorSelect from "components/shared/vendor_select.vue";
import AlertDates from "components/assets/show_page_elements/alert_dates.vue";
import UniversalLinks from "components/shared/universal_link/universal_links";
import LifecycleForm from "components/assets/lifecycle_form.vue";
import assetAlertUpdateHelper from "mixins/assets/asset_alert_update_helper";
import HardwareFields from "../assets/form_elements/hardware_fields.vue";
import ImpactField from "../assets/form_elements/impact_field.vue";
import LocationUsageFields from "../assets/form_elements/location_usage_fields.vue";
import WarrantyAcquisitionFields from "../assets/form_elements/warranty_acquisition_fields.vue";
import ImageInput from "../shared/image_input.vue";
import AttachmentInput from "../shared/attachment_input.vue";
import sectionItems from "./json/asset_form_items.json";
import SubmitButton from "../shared/submit_button.vue";
import TipsAndTricks from "../shared/tips_and_tricks.vue";

const MARGIN_TO_SCROLL = 60;
const MARGIN_TO_SCROLL_TO_MODAL = -150;
const MARGIN_TO_FIX_MENU = 100;

const Sections = {
  Basic: 0,
  TechSpecs: 1,
  Location: 2,
  Cost: 3,
  Warranty: 4,
  Alert: 5,
  AdditionalDetails: 6,
};

export default {
  $_veeValidate: {
    validator: "new",
  },
  components: {
    MultiSelect,
    SweetModal,
    LocationCustomFormViewer,
    CompanyUserCustomFormViewer,
    HardwareFields,
    ImpactField,
    LocationUsageFields,
    WarrantyAcquisitionFields,
    ImageInput,
    AlertDates,
    AttachmentInput,
    SubmitButton,
    UniversalLinks,
    VendorSelect,
    LifecycleForm,
    TipsAndTricks,
  },
  mixins: [
    assetImages,
    universalLinksForm,
    assetSpecific,
    permissionsHelper,
    assetAlertUpdateHelper,
  ],
  props: {
    tempAsset: {
      type: Object,
      default: null,
    },
    submitted: {
      type: Boolean,
      default: true,
    },
    isNew: {
      type: Boolean,
      default: false,
    },
    isModal: {
      type: Boolean,
      default: false,
    },
    newVendorName: {
      type: String,
      default: null,
    },
    cloneAsset: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      asset: { ...this.tempAsset },
      costAttributes: { ...this.tempAsset.costAttributes },
      nextHardwareDetailType: null,
      nextAssetTypeId: null,
      randomizer: Math.random(),
      loading: true,
      lockField: false,
      permissionAsset: {},
      files: [],
      fileId: 1,
      linksKey: true,
      assetValue: null,
      scrollPosition: null,
      buttonLeftPostition: null,
      scrollHeight: null,
      windowHeight: null,
      stickySideBar: false,
      menuBounds: null,
      sectionItems,
      Sections,
      sideMenuTopValue: 0,
      offset: 0,
      perPage: 100,
      removeCostValues: true,
      tipsAndTricks: [
        {
          title: "What are Build Elements?",
          description:
            "Build Elements are custom-made data (help desk from, automated tasks, etc.) you can use to populate (or repopulate) child companies.",
        },
        {
          title: "What are Saved Elements?",
          description:
            "Saved Builds are a specific grouping of your created elements for quick data population.",
        },
      ],
    };
  },
  computed: {
    ...mapGetters([
      "companyLocations",
      "companyUsers",
      "assetTags",
      "assetTypes",
      "customLifecycles",
      "automaticLifecycles",
    ]),

    hasScrolledPastFormEnd() {
      return this.scrollPosition > this.scrollHeight;
    },
    assetTypeName() {
      if (this.asset.companyAssetTypeId && this.assetTypes.length) {
        const assetType = this.assetTypes.find(
          (type) => type.id === this.asset.companyAssetTypeId
        );
        if (assetType) {
          return assetType.name;
        }
      }
      return "";
    },
    imageSrc() {
      if (this.asset.imageMediumUrl) {
        return this.asset.imageMediumUrl;
      } else if (this.asset.defaultImageUrl) {
        return this.asset.defaultImageUrl;
      }
      return this.assetTypeImageSrc(this.asset);
    },
    universalLinks() {
      return this.asset.universalLinks;
    },
    checkDefaultImage() {
      return !this.asset.imageFileName;
    },
    tagOptions() {
      return this.assetTags.length > 0
        ? this.assetTags.map((tag) => tag.name)
        : [];
    },
    typesLifecycles() {
      return this.automaticLifecycles.filter(
        (lc) => lc.linkedItemType === "CompanyAssetType"
      );
    },
    tagsLifecycles() {
      return this.automaticLifecycles.filter(
        (lc) => lc.linkedItemType === "CompanyAssetTag"
      );
    },
    showCheckBox() {
      return (
        !this.costAttributes?.assetLifecycleId &&
        this.costAttributes?.purchasePrice
      );
    },
  },
  watch: {
    // This should only happen if by some chance the asset isn't loaded in time when it
    // is passed into this component.  In theory...  ;)
    tempAsset() {
      this.asset = { ...this.tempAsset };
      this.linksKey = !this.linksKey;
      this.updateAssetTypes();
    },
    assetTypes() {
      this.updateAssetTypes();
    },
    asset() {
      this.setLockField();
      this.lockPopulatedFields();
    },
    assetValue() {
      if (
        this.assetValue &&
        this.$validator.errors.has("managed_asset[company_asset_type_id]")
      ) {
        this.$validator.errors.remove("managed_asset[company_asset_type_id]");
      }
    },
  },
  created() {
    this.addMacAddressValidation();
    this.addIpAddressValidation();
  },
  destroyed() {
    $SiteScroll
      .getScrollElement()
      .removeEventListener("scroll", this.checkSticky, true);
    $SiteScroll
      .getScrollElement()
      .removeEventListener("scroll", this.addActiveClass, true);
    $SiteScroll
      .getScrollElement()
      .removeEventListener("scroll", this.updateScrollValues, true);
    window.removeEventListener("resize", this.updateButtonPosition);
  },
  methods: {
    ...mapMutations(["setAssetTypes"]),
    ...mapActions("GlobalStore", ["fetchLocations"]),

    updateAssetTypes() {
      if (this.assetTypes && this.assetTypes.length) {
        this.assetValue = this.assetTypes.filter(
          (type) => type.id === this.asset.companyAssetTypeId
        );
      }
    },
    onWorkspaceChange() {
      const { sidebarMenu, sidebarWrapper, sectionMenuRefs } = this.$refs;
      if (sidebarMenu && sidebarWrapper) {
        this.menuBounds = sidebarMenu.getBoundingClientRect();
        sidebarWrapper.style.width = `${this.menuBounds.width}px`;
        requestAnimationFrame(() => {
          this.checkSticky();
        });
      }
      if (sectionItems && sectionMenuRefs) {
        this.sectionItems = this.sectionItems.map((item, index) => ({
          ...item,
          menuRef: sectionMenuRefs[index],
          sectionRef: this.$refs[`sectionItems_${index}`],
        }));
      }

      this.setLockField();
      this.lockPopulatedFields();
      this.getAssetTypes();
      this.$store.dispatch("fetchCompanyAssetTags");
      this.$store.dispatch("fetchLifecycles");
      this.updateButtonPosition();

      $SiteScroll
        .getScrollElement()
        .addEventListener("scroll", this.addActiveClass, true);
      $SiteScroll
        .getScrollElement()
        .addEventListener("scroll", this.checkSticky, true);
      $SiteScroll
        .getScrollElement()
        .addEventListener("scroll", this.updateScrollValues, true);
      window.addEventListener("resize", this.updateButtonPosition);
    },
    clearPhoneNumber(assetType) {
      if (!["Mobile", "Phone"].includes(assetType)) {
        this.asset.assignmentInformationAttributes.phoneNumber = null;
        this.asset.assignmentInformationAttributes.phoneNumberCountryCode =
          null;
        this.asset.assignmentInformationAttributes.phoneNumberCountryCodeNumber =
          null;
      }
    },
    addActiveClass() {
      this.sectionItems.forEach((item) => {
        const rect = item.sectionRef.getBoundingClientRect().y;
        if (rect < MARGIN_TO_FIX_MENU) {
          this.setSideMenuActive(item);
        }
      });
    },

    checkSticky() {
      const { sidebarMenu, sidebarWrapper } = this.$refs;
      if (sidebarMenu && sidebarWrapper) {
        const distanceToTop =
          sidebarWrapper.getBoundingClientRect().top -
          (!this.isModal ? MARGIN_TO_SCROLL : MARGIN_TO_SCROLL_TO_MODAL);
        const scrollPosition = this.isModal
          ? this.$root.currentModal && this.$root.currentModal.$el.scrollTop
          : document.body.scrollTop;
        this.sideMenuTopValue = scrollPosition;
        this.stickySideBar = scrollPosition > distanceToTop;
      }
    },

    setSideMenuActive(item) {
      this.sectionItems = this.sectionItems.map((_item) => ({
        ..._item,
        active: _item.id === item.id,
      }));
    },

    scrollToElement(item) {
      if (!item.sectionRef) {
        return;
      }
      this.setSideMenuActive(item);
      item.sectionRef.scrollIntoView({ block: "start", behavior: "smooth" });
    },

    updateButtonPosition() {
      this.windowHeight = window.innerHeight;
      this.buttonLeftPostition =
        this.$refs.assetForm.getBoundingClientRect().right - 135;
    },

    updateUsage(asset) {
      this.asset.locationId = asset.locationId;
      this.asset.assignmentInformationAttributes.departmentId =
        asset.assignmentInformationAttributes.departmentId;
      this.asset.assignmentInformationAttributes.usedByContributorId =
        asset.assignmentInformationAttributes.usedByContributorId;
      this.asset.assignmentInformationAttributes.managedByContributorId =
        asset.assignmentInformationAttributes.managedByContributorId;
      this.asset.assignmentInformationAttributes.phoneNumber =
        asset.assignmentInformationAttributes.phoneNumber;
      this.asset.assignmentInformationAttributes.phoneNumberCountryCode =
        asset.assignmentInformationAttributes.phoneNumberCountryCode;
      this.asset.assignmentInformationAttributes.phoneNumberCountryCodeNumber =
        asset.assignmentInformationAttributes.phoneNumberCountryCodeNumber;
    },

    assetAlerts(alert) {
      this.asset.alertDates = alert.alertDates;
    },

    updateWarrantyFields(managedAsset) {
      this.asset.machineSerialNumber = managedAsset.machineSerialNumber;
      this.asset.productNumber = managedAsset.productNumber;
      this.asset.warrantyExpiration = managedAsset.warrantyExpiration;
      this.asset.acquisitionDate = managedAsset.acquisitionDate;
      this.asset.installDate = managedAsset.installDate;
    },

    assignAssetType(type) {
      if (type && this.assetValue && this.assetValue.id !== type.id) {
        this.nextAssetTypeId = parseInt(type.id, 10);
        if (this.asset.companyAssetTypeId !== this.nextAssetTypeId) {
          http
            .get(`/asset_fields/${this.nextAssetTypeId}/all_fields.json`)
            .then((res) => {
              this.applyTypeLifecycle(type);
              this.nextHardwareDetailType = res.data.name;
              if (this.asset.hardwareDetailType === null) {
                this.changeDetails();
              } else if (
                this.nextHardwareDetailType !== this.asset.hardwareDetailType ||
                this.cloneAsset
              ) {
                this.$refs.newAssetTypeWarning.open();
              } else {
                this.asset.companyAssetTypeId = this.nextAssetTypeId;
                this.asset.hardwareDetailType = this.nextHardwareDetailType;
                this.clearPhoneNumber(type.assetType);
                if (this.asset.hardwareDetailType !== "OtherDetail") {
                  this.asset.hardwareDetail.id = null;
                }
                this.asset.assetSoftwares.forEach((software) => {
                  const updatedSoftware = { ...software };
                  updatedSoftware.id = null;
                });
                this.assetValue = type;
                this.openCostValuesWarningModal();
              }
            })
            .catch(() => {
              this.emitError(
                "Sorry, there was an error retrieving data for this asset."
              );
            });
        }
      } else {
        this.asset.companyAssetTypeId = null;
        this.assetValue = type;
      }
    },
    applyTypeLifecycle(type) {
      this.updateTypeLifecycleData(type.id);
    },
    updateTypeLifecycleData(typeId) {
      if (!["by_tag", "custom"].includes(this.costAttributes.lifecycleType)) {
        const lifecycle = this.typesLifecycles.find(
          (lc) => lc.assetType.id === typeId
        );
        if (lifecycle) {
          this.lifecycleData(lifecycle, "by_type");
        } else {
          this.removeLifecycle();
        }
      }
    },
    getTagLifecycle(tag) {
      return this.tagsLifecycles.find(
        (lc) => lc.assetTag.name.toLowerCase() === tag.toLowerCase()
      );
    },
    applyTagLifecycle(tag) {
      const lifecycle = this.getTagLifecycle(tag);
      if (lifecycle && this.costAttributes.lifecycleType !== "custom") {
        this.lifecycleData(lifecycle, "by_tag");
      }
    },
    removeTagLifecycle(tag) {
      const tagLifecycle = this.getTagLifecycle(tag);
      const { assetLifecycleId, lifecycleType } = this.costAttributes;
      if (!lifecycleType || !assetLifecycleId) {
        return;
      }
      if (lifecycleType === "by_tag" && assetLifecycleId === tagLifecycle.id) {
        this.removeLifecycle();
      }
    },
    lifecycleData(lifecycle, type) {
      this.costAttributes = {
        ...this.costAttributes,
        purchasePrice: lifecycle.purchasePrice,
        replacementCost: lifecycle.replacementCost,
        usefulLife: lifecycle.usefulLife,
        salvage: lifecycle.salvage,
        approachingEndOfLife: lifecycle.approachingEndOfLife,
        po: lifecycle.po,
        lifecycleType: type,
        assetLifecycleId: lifecycle.id,
      };
    },

    setLockField() {
      if (["probe", "agent", "selfonboarding"].includes(this.asset.source)) {
        this.lockField = true;
      }
    },

    lockPopulatedFields() {
      if (this.lockField) {
        Object.keys(this.asset).forEach((key) => {
          if (
            this.asset[key] == null ||
            this.asset[key].toString().trim() === ""
          ) {
            this.permissionAsset[key] = false;
          } else {
            this.permissionAsset[key] = true;
          }
        });
      } else if (this.asset) {
        Object.keys(this.asset).forEach((key) => {
          this.permissionAsset[key] = true;
        });
      }
    },

    newLocation() {
      this.$refs.newLocationModal.close();
      this.fetchLocations({ offset: this.offset, limit: this.perPage });
    },

    getAssetTypes() {
      http
        .get("/company_asset_types.json")
        .then((res) => {
          this.setAssetTypes(res.data.assetTypes);
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.emitError(`Sorry, there was an error loading asset types.`);
        });
    },

    addLink(link) {
      this.addUniversalLink(this.asset, link);
    },

    removeLink(link) {
      this.removeUniversalLink(this.asset, link);
    },
    changeSerialNumber(value) {
      this.asset.machineSerialNumber = value;
    },
    changeFirmware(value) {
      this.asset.firmware = value;
    },
    changePorts(value) {
      this.asset.hardwareDetail.ports = value;
    },
    changeSku(value) {
      this.asset.sku = value;
    },
    changeProductId(value) {
      this.asset.hardwareDetail.id = value;
    },
    updateLifecycleData(costData) {
      this.costAttributes = {
        ...this.costAttributes,
        ...costData,
      };
    },
    assignCustomLifecycle(lifecycle) {
      if (!lifecycle) {
        return;
      }
      const {
        purchasePrice,
        replacementCost,
        usefulLife,
        approachingEndOfLife,
        salvage,
        po,
      } = lifecycle;
      this.costAttributes = {
        ...this.costAttributes,
        purchasePrice,
        replacementCost,
        usefulLife,
        approachingEndOfLife,
        po,
        salvage,
        lifecycleType: "custom",
        assetLifecycleId: lifecycle.id,
      };
    },
    removeLifecycle() {
      this.costAttributes = {
        ...this.costAttributes,
        lifecycleType: null,
        assetLifecycleId: null,
      };
    },
    validateThenSave() {
      this.$validator.validateAll().then((result) => {
        this.asset.hardwareDetailAttributes = this.asset.hardwareDetail;
        this.asset.assetSoftwaresAttributes = this.asset.assetSoftwares;
        this.isLifecycleValueChanged();
        const form = document.querySelector("#asset_form");
        const formAttachments = new FormData(form);
        if (result) {
          if (this.files) {
            this.files.forEach((file) => {
              formAttachments.append(
                `managed_asset[managed_asset_attachments_attributes][][attached_file]`,
                file,
                file.name
              );
            });
          }

          if (this.asset.universalLinks.length === 0) {
            formAttachments.append(`managed_asset[links_array]`, "");
          } else {
            this.asset.universalLinks.forEach((link, i) => {
              formAttachments.append(
                `managed_asset[links_array][${i}][id]`,
                link.target.id
              );
              formAttachments.append(
                `managed_asset[links_array][${i}][linkable_id]`,
                link.target.linkableId
              );
              formAttachments.append(
                `managed_asset[links_array][${i}][name]`,
                link.target.name
              );
              formAttachments.append(
                `managed_asset[links_array][${i}][linkable_type]`,
                link.target.linkableType
              );
            });
          }

          if (this.asset.managedAssetAttachments.length > 0) {
            const ids = this.asset.managedAssetAttachments.map((a) => a.id);
            formAttachments.append(
              `managed_asset[managed_asset_attachments]`,
              ids
            );
          } else {
            formAttachments.append(
              `managed_asset[managed_asset_attachments]`,
              ""
            );
          }

          this.asset.tagsArray.forEach((tag, i) => {
            formAttachments.append(`managed_asset[tags_array][${i}]`, tag);
          });

          formAttachments.append(`managed_asset[id]`, this.asset.id || "");
          formAttachments.append(
            `managed_asset[company_asset_type_id]`,
            this.asset.companyAssetTypeId || ""
          );
          formAttachments.append(
            `managed_asset[default_vendor_id]`,
            this.asset.default_vendor_id || ""
          );
          formAttachments.append(
            `managed_asset[vendor_id]`,
            this.asset.vendorId || ""
          );
          formAttachments.append(
            `managed_asset[new_vendor_name]`,
            this.newVendorName || ""
          );
          formAttachments.append(
            `managed_asset[product_number]`,
            this.asset.productNumber || ""
          );
          formAttachments.append(
            `managed_asset[hardware_detail_id]`,
            this.asset.hardwareDetailId || ""
          );
          formAttachments.append(
            `managed_asset[hardware_detail_type]`,
            this.asset.hardwareDetailType || ""
          );
          formAttachments.append(
            `managed_asset[asset_tag]`,
            this.checkAssetTag()
          );
          formAttachments.append(
            `managed_asset[machine_serial_number]`,
            this.asset.machineSerialNumber || ""
          );

          if (this.asset.assignmentInformationAttributes) {
            formAttachments.append(
              `managed_asset[assignment_information_attributes][id]`,
              this.asset.assignmentInformationAttributes.id || ""
            );
            formAttachments.append(
              `managed_asset[assignment_information_attributes][managed_by_contributor_id]`,
              this.asset.assignmentInformationAttributes
                .managedByContributorId || ""
            );
            formAttachments.append(
              `managed_asset[assignment_information_attributes][used_by_contributor_id]`,
              this.asset.assignmentInformationAttributes.usedByContributorId ||
                ""
            );
            formAttachments.append(
              `managed_asset[assignment_information_attributes][department_id]`,
              this.asset.assignmentInformationAttributes.departmentId || ""
            );
            formAttachments.append(
              `managed_asset[assignment_information_attributes][phone_number]`,
              this.asset.assignmentInformationAttributes.phoneNumber || ""
            );
            formAttachments.append(
              `managed_asset[assignment_information_attributes][phone_number_country_code]`,
              this.asset.assignmentInformationAttributes
                .phoneNumberCountryCode || ""
            );
            formAttachments.append(
              `managed_asset[assignment_information_attributes][phone_number_country_code_number]`,
              this.asset.assignmentInformationAttributes
                .phoneNumberCountryCodeNumber || ""
            );
            formAttachments.append(
              `managed_asset[assignment_information_attributes][location_id]`,
              this.asset.locationId || ""
            );
            formAttachments.append(
              `managed_asset[location_id]`,
              this.asset.locationId || ""
            );
          }

          if (this.costAttributes) {
            formAttachments.append(
              `managed_asset[cost_attributes][id]`,
              this.costAttributes.id || ""
            );
            formAttachments.append(
              `managed_asset[cost_attributes][salvage]`,
              this.costAttributes.salvage || "0"
            );
            formAttachments.append(
              `managed_asset[cost_attributes][purchase_price]`,
              this.costAttributes.purchasePrice || "0"
            );
            formAttachments.append(
              `managed_asset[cost_attributes][replacement_cost]`,
              this.costAttributes.replacementCost || "0"
            );
            formAttachments.append(
              `managed_asset[cost_attributes][useful_life]`,
              this.costAttributes.usefulLife || null
            );
            formAttachments.append(
              `managed_asset[cost_attributes][approaching_end_of_life]`,
              this.costAttributes.approachingEndOfLife || null
            );
            formAttachments.append(
              `managed_asset[cost_attributes][po]`,
              this.costAttributes.po || ""
            );
            formAttachments.append(
              `managed_asset[cost_attributes][lifecycle_type]`,
              this.costAttributes.lifecycleType || null
            );
            formAttachments.append(
              `managed_asset[cost_attributes][asset_lifecycle_id]`,
              this.costAttributes.assetLifecycleId || null
            );
          }
          if (this.asset.destroyImage) {
            formAttachments.append(
              `managed_asset[destroy_image]`,
              this.asset.destroyImage
            );
          }
          if (this.asset.image) {
            formAttachments.append(`managed_asset[image]`, this.asset.image);
          }

          this.$emit("submit-asset", formAttachments, this.asset.alertDates);
        } else {
          this.emitError(
            "Please correct the highlighted errors before submitting."
          );
        }
      });
    },
    isLifecycleValueChanged() {
      const {
        lifecycleType,
        assetLifecycleId,
        purchasePrice,
        replacementCost,
        usefulLife,
        salvage,
        approachingEndOfLife,
        po,
      } = this.costAttributes;

      if (lifecycleType && assetLifecycleId) {
        const lifecycle = (
          lifecycleType === "custom"
            ? this.customLifecycles
            : this.automaticLifecycles
        ).find((lc) => lc.id === assetLifecycleId);

        if (lifecycle) {
          const lifecycleValues = [
            lifecycle.purchasePrice,
            lifecycle.replacementCost,
            lifecycle.usefulLife,
            lifecycle.salvage,
            lifecycle.approachingEndOfLife,
            lifecycle.po,
          ];

          const costValues = [
            purchasePrice,
            replacementCost,
            usefulLife,
            salvage,
            approachingEndOfLife,
            po,
          ];

          const isValueChanged = costValues.some(
            (value, index) => value !== lifecycleValues[index]
          );

          if (isValueChanged) {
            this.removeLifecycle();
          }
        }
      }
    },
    onRemoveCostValues() {
      this.removeCostValues = !this.removeCostValues;
    },
    openCostValuesWarningModal() {
      if (
        !this.costAttributes?.assetLifecycleId &&
        this.costAttributes?.purchasePrice
      ) {
        this.$refs.costValuesWarningModal.open();
      }
    },
    closeCostValuesWarningModal() {
      this.$refs.costValuesWarningModal.close();
    },
    removeCostData() {
      this.costAttributes = {};
      this.removeCostValues = false;
    },
    removeCostDataAndCloseModal() {
      this.removeCostData();
      this.closeCostValuesWarningModal();
    },
    openNewUserModal() {
      this.$refs.newUserModal.open();
    },

    newUser() {
      this.$refs.newUserModal.close();
      this.$store.dispatch("fetchCompanyUserOptions");
    },

    addAssetSoftware() {
      this.asset.assetSoftwares.push({});
    },

    closeNewAssetTypeWarning() {
      document.getElementById("managed_asset_asset_type_id").value =
        this.asset.companyAssetTypeId;
      this.$refs.newAssetTypeWarning.close();
    },

    cancelForm() {
      this.$router.push({ path: "/assets" });
    },

    changeDetails() {
      this.updateTypeLifecycleData(this.nextAssetTypeId);
      this.asset.companyAssetTypeId = this.nextAssetTypeId;
      this.assetValue = this.assetTypes.find(
        (a) => a.id === this.asset.companyAssetTypeId
      );
      this.asset.hardwareDetailType = this.nextHardwareDetailType;
      this.asset.hardwareDetailId = null;
      if (
        this.asset.hardwareDetailType ||
        this.asset.hardwareDetailType === "Software" ||
        this.assetTypes.find((x) => x[0] === this.nextAssetTypeId)[1] ===
          "Other"
      ) {
        this.asset.hardwareDetail = {};
      }
      this.asset.assetSoftwares = [];
      this.addAssetSoftware();
      this.closeNewAssetTypeWarning();
      this.$forceUpdate();
      this.clearPhoneNumber(this.assetValue.assetType);
      if (this.removeCostValues && !this.costAttributes?.assetLifecycleId) {
        this.removeCostData();
      }
    },

    changeAvatarUrl(url, name, file) {
      const { asset } = this;
      if (file) {
        asset.imageFileName = name;
        asset.imageMediumUrl = url;
        asset.destroyImage = false;
        asset.image = file;
      } else {
        asset.imageFileName = null;
        asset.imageMediumUrl = null;
        asset.destroyImage = true;
        asset.image = null;
      }
    },

    selectTag(tag) {
      this.applyTagLifecycle(tag);
      this.asset.tagsArray.push(tag);
      this.openCostValuesWarningModal();
    },
    removeTag(tag) {
      const idx = this.asset.tagsArray.indexOf(tag);
      if (idx > -1) {
        this.asset.tagsArray.splice(idx, 1);
        this.removeTagLifecycle(tag);
        this.openCostValuesWarningModal();
      }
    },
    addNewTag(newTag) {
      this.applyTagLifecycle(newTag);
      this.asset.tagsArray.push(newTag);
    },
    addFile(file) {
      const alreadyUploaded = this.files.find(({ name }) => name === file.name);
      if (alreadyUploaded) {
        this.emitError(
          "Sorry, you cannot upload the same file multiple times."
        );
      } else if (file.size < 10000000) {
        const myFile = file;
        myFile.id = this.fileId;
        this.fileId += 1;
        this.files.push(myFile);
      } else {
        this.$refs.fileModal.open();
      }
    },
    deleteFile(file) {
      this.files = this.files.filter(({ id }) => id !== file.id);
    },
    removeFile(file) {
      this.asset.managedAssetAttachments.splice(
        this.asset.managedAssetAttachments.indexOf(file),
        1
      );
    },
    updateScrollValues: _debounce(function handleUpdateScroll() {
      if (this.$refs.assetForm) {
        const combinedFormOffsets =
          this.$refs.assetForm.offsetTop + 32 + 18 - 48;
        this.scrollHeight =
          this.$refs.assetForm.scrollHeight +
          combinedFormOffsets -
          this.windowHeight;
        this.scrollPosition = document.querySelector(
          ".simplebar-content-wrapper"
        ).scrollTop;
      }
    }, 15),
    checkAssetTag() {
      if (this.asset.assetTag) {
        return this.asset.assetTag.trim();
      }
      return "";
    },
  },
};
</script>

<style lang="scss" scoped>
.asset-alert {
  :deep(.mx-datepicker) {
    width: 100% !important;
  }
}

.attachments-block {
  overflow: auto;
}

.asset-type.is-invalid {
  :deep(.multiselect__tags) {
    border-color: $color-error;
  }
}

.save-asset-btn--fixed {
  .asset-spinner {
    left: -50px;
    position: absolute;
  }
}

i.nulodgicon-android-bulb {
  color: $pastel-orange-100;
  opacity: 0.5;
}

.sidebar-menu {
  top: 4rem;

  .side-menu-item:hover {
    background-color: $themed-light;
    color: $themed-link;
  }

  .side-menu-item.active {
    background-color: $themed-light;
    color: $themed-link;
  }
}

.sidebar-menu--modal {
  top: 13rem;
}

.scroll-elem {
  scroll-margin-top: 4rem;
}

[type="checkbox"] {
  display: none;
}

:checked ~ .checkbox {
  background-color: $blue;
  border-color: $blue;
}
</style>

<style lang="scss">
.add-first-asset-modal .scroll-elem {
  scroll-margin-top: 1rem;
}

.sticky-btn-holder {
  .btn-link {
    &:hover {
      background-color: #e9ecef !important;
    }
  }
}

#managed_asset_asset_tag {
  padding-right: 37px;
}

#asset-tag-tip {
  position: absolute;
  right: 24px;
  z-index: 10;
  bottom: 28px;
  cursor: pointer;
}
</style>
