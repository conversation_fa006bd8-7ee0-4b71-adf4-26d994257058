FROM alpine:3.20

ARG USER_NAME=genuity
ARG USER_HOME=/home/<USER>
ARG ASDF_DIR=$USER_HOME/.asdf

# Set environment variables using build arguments
ENV USER_NAME=$USER_NAME \
    USER_HOME=$USER_HOME \
    ASDF_DIR=$ASDF_DIR \
    PATH=$ASDF_DIR/bin:$ASDF_DIR/shims:$PATH \
    APP_DIR=/app

# Install dependencies
RUN apk update && \
    apk upgrade && \
    apk add --no-cache \
    curl \
    make \
    ca-certificates \
    build-base \
    imagemagick \
    imagemagick-dev \
    git \
    bash \
    pkgconfig \
    zlib-dev \
    libxml2-dev \
    libxslt-dev \
    tzdata \
    yaml-dev \
    openssl-dev \
    libffi-dev \
    readline-dev \
    bzip2 \
    bzip2-dev \
    less \
    postgresql-dev \
    postgresql-client \
    coreutils \
    tar \
    libc6-compat \
    shared-mime-info \
    chromium \
    xvfb-run \
    x11vnc \
    gnupg && \
    rm -rf /var/cache/apk/* && \
    mkdir -p /usr/src/app

# Add non-root user
RUN adduser -D -h $USER_HOME -s /bin/bash $USER_NAME

# Switch to non-root user
USER $USER_NAME
WORKDIR $APP_DIR

# Install asdf for the non-root user
RUN git clone https://github.com/asdf-vm/asdf.git $ASDF_DIR --branch v0.14.0 && \
    echo -e "\n. $ASDF_DIR/asdf.sh" >> $USER_HOME/.bashrc

# Install plugins and versions
RUN asdf plugin-add python https://github.com/danhper/asdf-python.git && \
    asdf plugin-add ruby https://github.com/asdf-vm/asdf-ruby.git && \
    asdf plugin-add nodejs https://github.com/asdf-vm/asdf-nodejs.git && \
    asdf plugin-add yarn && \
    asdf install python 2.7.18 && \
    asdf global python 2.7.18 && \
    asdf install ruby 3.2.0 && \
    asdf global ruby 3.2.0 && \ 
    asdf install nodejs 20.18.0 && \
    asdf global nodejs 20.18.0 && \
    asdf install yarn latest && \
    asdf global yarn latest

RUN chown -R $USER_NAME:$USER_NAME $APP_DIR

RUN gem install shared-mime-info && \
    gem install bundler

ENV RAILS_ENV=development

ADD --chown=$USER_NAME:$USER_NAME Gemfile Gemfile.lock package.json package-lock.json $APP_DIR

RUN bundle install && \
    yarn install

SHELL ["/bin/bash", "-c"]

# Set the default shell to bash
CMD ["Xvfb", ":99", "-screen", "0", "1280x720x16", "&"]

