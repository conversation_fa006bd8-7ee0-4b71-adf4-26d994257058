#!/bin/bash

# Exit immediately if a command exits with a non-zero status
set -e

# Function to display script usage
usage() {
    echo "Usage: $0 [-d <docker_file>] [-r <repository_name>] [-a <aws_account_id>] [-g <aws_region>] [-e <environment>]"
    echo "  -d: Path to Dockerfile (default: ./Dockerfile)"
    echo "  -r: ECR repository name (default: genuity)"
    echo "  -a: AWS account ID (required)"
    echo "  -g: AWS region (default: us-west-2)"
    echo "  -e: Application environment (default: staging)"
    exit 1
}

# Default values
DOCKERFILE="./Dockerfile"
REPOSITORY_NAME="genuity"
AWS_REGION="us-west-2"
ENVIRONMENT="staging"
AWS_ACCOUNT_ID="************"
MASTER_KEY=""

# Parse command line options
while getopts ":d:r:a:g:e:m:" opt; do
    case $opt in
        d) DOCKERFILE="$OPTARG" ;;
        r) REPOSITORY_NAME="$OPTARG" ;;
        a) AWS_ACCOUNT_ID="$OPTARG" ;;
        g) AWS_REGION="$OPTARG" ;;
        e) ENVIRONMENT="$OPTARG" ;;
        e) MASTER_KEY="$OPTARG" ;;
        \?) echo "Invalid option -$OPTARG" >&2; usage ;;
    esac
done

# Check if AWS account ID is provided
if [ -z "$AWS_ACCOUNT_ID" ]; then
    echo "Error: AWS account ID is required. Use -a option to specify it."
    usage
fi

# Check if we're in a Git repository
if ! git rev-parse --is-inside-work-tree > /dev/null 2>&1; then
    echo "Error: Not a Git repository. Please run this script from within a Git repository."
    exit 1
fi

# Get the current Git branch name
BRANCH_NAME=$(git rev-parse --abbrev-ref HEAD)

# Get the latest commit hash
COMMIT_HASH=$(git rev-parse --short HEAD)

# Create the Docker tag
DOCKER_TAG="${BRANCH_NAME}-${COMMIT_HASH}"

# Construct the full ECR image name
ECR_IMAGE_NAME="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${REPOSITORY_NAME}"

# Build the Docker image
echo "Building Docker image: ${ECR_IMAGE_NAME}:${DOCKER_TAG}"
docker build -t "${ECR_IMAGE_NAME}:${DOCKER_TAG}" -f "$DOCKERFILE" --build-arg ENV="${ENVIRONMENT}" --platform linux/amd64 .

# Tag the image with 'latest' as well
docker tag "${ECR_IMAGE_NAME}:${DOCKER_TAG}" "${ECR_IMAGE_NAME}:latest"

echo "Docker image built and tagged successfully:"
echo "  ${ECR_IMAGE_NAME}:${DOCKER_TAG}"
echo "  ${ECR_IMAGE_NAME}:latest"

# ECR login
echo "Logging into AWS ECR..."
aws ecr get-login-password --region "$AWS_REGION" | docker login --username AWS --password-stdin "${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com"

echo "To push to ECR, run:"
echo "docker push ${ECR_IMAGE_NAME}:${DOCKER_TAG}"
echo "docker push ${ECR_IMAGE_NAME}:latest"

