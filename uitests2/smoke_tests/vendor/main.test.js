import signinpage from '../../shared/signin/signinpage';
import golvar from '../../shared/Globalvariable';
import navbar from '../../shared/Components/navbar';
import addVendor from '../../shared/vendor/add_vendor';
import vendorVar from '../../shared/vendor/vendor_variables';
import editVendor from '../../shared/vendor/edit_vendor';
import deleteVendor from '../../shared/vendor/delete_vendor';
import addProduct from '../../shared/vendor/add_product';
import deleteProduct from '../../shared/vendor/delete_product';
import editProduct from '../../shared/vendor/edit_product';
import addTransaction from '../../shared/vendor/add_transaction';
import editTransaction from '../../shared/vendor/edit_transaction ';
import viewCount from '../../shared/vendor/check_vendor_counter';
import report from '../../shared/help_desk/reports';
import viewAlert from '../../shared/vendor/check_alert_trigger';
import deleteTransactions from '../../shared/vendor/delete_transaction';

const golVari = new golvar();
const logIntoApp = new signinpage();
const naviBar = new navbar();
const addNewVendor = new addVendor();
const vendorVari = new vendorVar();
const editNewVendor = new editVendor();
const delVendor = new deleteVendor();
const addNewProduct = new addProduct();
const delProduct = new deleteProduct();
const editNewProduct = new editProduct();
const addNewTxn = new addTransaction();
const editTxn = new editTransaction();
const checkCounter = new viewCount();
const downloadReports = new report();
const checkTriggers = new viewAlert();
const delTxn = new deleteTransactions();
const editTxnName = vendorVari.editTxnName;
const editVendorName = vendorVari.editVendorName;

const module = 'vendors';

fixture('Smoke test for vendor module')

test('Vendor CRUD', async t => {
  logIntoApp.siginCompanySelect(golVari.emailaddress, golVari.password, golVari.companyname);
  await t.wait(1000);
  addNewVendor.addVendors(vendorVari.vendorName, vendorVari.url, vendorVari.category, vendorVari.bussinessUnit, vendorVari.description, vendorVari.accountNumber, vendorVari.tags, vendorVari.address, vendorVari.city, vendorVari.state, vendorVari.country, vendorVari.zipCode, vendorVari.phoneNumber, vendorVari.alertNumber, vendorVari.billingMethods, vendorVari.billingTerms, golVari.fullName1, golVari.contract);
  await t.wait(3000);
  checkCounter.viewCounts('6');
  await t.wait(1000);
  addNewProduct.addProducts(vendorVari.vendorName, vendorVari.productName, vendorVari.totalLicense, vendorVari.licenseUsed, vendorVari.category, vendorVari.productType, vendorVari.arrivalDate);
  await t.wait(1000);
  addNewTxn.addTransactionswithlastmonth(vendorVari.vendorName, vendorVari.transactionName, vendorVari.previousDayTxn, vendorVari.amount, vendorVari.productName, golVari.locName1);
  await t.wait(1000);
  addNewTxn.addTxnCurrentMonth(vendorVari.vendorName, vendorVari.transactionName1, vendorVari.amount1, vendorVari.productName, golVari.locName1);
  await t.wait(1000);
  checkTriggers.viewAlerts(vendorVari.vendorName, vendorVari.alertBadge, vendorVari.alertNumber, vendorVari.overPrecent);
  await t.wait(1000);
  editTxn.editTransactions(vendorVari.vendorName, vendorVari.transactionName, editTxnName, vendorVari.editTxnDate, vendorVari.editAmount, 'Last 12 months');
  await t.wait(2000);
  delTxn.deleteTransactions(vendorVari.editTxnName);
  await t.wait(1000);
  editNewProduct.editProducts(vendorVari.vendorName, vendorVari.productName, vendorVari.editProductName, vendorVari.editTotalLicense, vendorVari.editLicenseUsed, vendorVari.editCategory, vendorVari.editProductType, vendorVari.editArrivalDate);
  await t.wait(1000);
  delProduct.deleteProducts(vendorVari.vendorName, vendorVari.editProductName);
  await t.wait(1000);
  editNewVendor.editVendors(vendorVari.vendorName, editVendorName, vendorVari.editUrl, vendorVari.editCategory, vendorVari.editBussinessUnit, vendorVari.editDescription, vendorVari.editAccountNumber, vendorVari.editTags, vendorVari.editAddress, vendorVari.editCity, vendorVari.editState, vendorVari.editCountry, vendorVari.editZipCode, vendorVari.editPhoneNumber, vendorVari.editAlertNumber, vendorVari.editBillingMethods, vendorVari.editBillingTerms);
  await t.wait(1000);
  downloadReports.reportsWithGraphical(module, vendorVari.donutChart, vendorVari.totalCost, editVendorName, vendorVari.graphTitle, vendorVari.dollar);
  await t.wait(1000);
  delVendor.deleteVendors(editVendorName);
  await t.wait(1000);
  downloadReports.reportsWithTabular(module, vendorVari.calendarYear);
  await t.wait(1000);
  naviBar.signout();
  await t.wait(1000);
})
