import addAssetType from "../../shared/assets/add_asset_type";
import signinpage from "../../shared/signin/signinpage";
import assetVar from "../../shared/assets/asset_variables";
import navbar from '../../shared/Components/navbar';
import addDepreciation from "../../shared/assets/add_depreciations";
import editDepreciation from "../../shared/assets/edit_depreciations";
import deleteDepreciation from "../../shared/assets/delete_depreciation";
import removeAssetsType from "../../shared/assets/remove_Assets_type";
import assetListColumn from "../../shared/assets/asset_list_view_column";
import addAsset from "../../shared/assets/add_assets";
import deleteAsset from "../../shared/assets/delete_assets";
import assetSoftware from "../../shared/assets/assets_software";
import addlocation from '../../shared/company_settings/add_location';
import deletelocation from "../../shared/company_settings/delete_location";
import companyvar from "../../shared/company_settings/company_variables";
import editAsset from "../../shared/assets/edit_assets";
import helpAgentGolVar from "../../shared/HelpAgentGolbalVar";
import addcontracts from "../../shared/contracts/add_contract";
import contractvar from "../../shared/contracts/contract_variables";
import clonecontracts from "../../shared/contracts/clone_contract";
import editcontracts from "../../shared/contracts/edit_contract";
import deletecontracts from "../../shared/contracts/delete_contract";
import addstaff from "../../shared/company_settings/add_staff";
import deletestaff from "../../shared/company_settings/delete_archive_staff";
import report from "../../shared/help_desk/reports";
import addProvider from "../../shared/telecom/add_provider";
import addPhoneNumbers from "../../shared/telecom/add_phonenumber";
import telecomVar from "../../shared/telecom/telecom_variables";
import addService from "../../shared/telecom/add_service";
import deletePhoneNumbers from "../../shared/telecom/delete_phonenumber";
import editPhoneNumbers from "../../shared/telecom/edit_phonenumber.js";
import addIpAddresses from "../../shared/telecom/add_ipaddress";
import editIpAddresses from "../../shared/telecom/edit_ipaddress";
import deleteIpAddresses from "../../shared/telecom/delete_ipaddress";
import deleteProvider from "../../shared/telecom/delete_provider";
import editService from "../../shared/telecom/edit_service";
import deleteService from "../../shared/telecom/delete_service";
import deleteTicket from '../../shared/help_desk/delete_tickets';
import addTicket from '../../shared/help_desk/add_tickets';
import editTickets from '../../shared/help_desk/edit_tickets';
import helpDeskVar from '../../shared/help_desk/help_desk_variables';
import addgroup from '../../shared/company_settings/add_group';
import deletegroup from '../../shared/company_settings/delete_group';
import { nanoid } from 'nanoid';
import faker from 'faker';
import addWorkSpace from '../../shared/help_desk/add_work_space';
import deletedWorkSpace from '../../shared/help_desk/delete_work_space';
import automatedTask from '../../shared/help_desk/automated_tasks';
import editWorkSpace from '../../shared/help_desk/edit_work_space';
import switchWorkSpace from '../../shared/help_desk/switch_work_space';
import addDocument from '../../shared/help_desk/add_document';
import editDocument from '../../shared/help_desk/edit_document';
import deleteDocument from '../../shared/help_desk/delete_document';
import addFAQ from '../../shared/help_desk/add_faq';
import editFAQ from '../../shared/help_desk/edit_faq';
import deleteFAQ from '../../shared/help_desk/delete_faq';
import addCannedResponse from '../../shared/help_desk/add_canned_response';
import editCannedResponse from '../../shared/help_desk/edit_canned_response';
import deleteCannedResponse from '../../shared/help_desk/delete_canned_response';
import addKnowledgeBase from '../../shared/help_desk/add_knowledge_base';
import editKnowledgeBase from '../../shared/help_desk/edit_knowledge_base';
import deleteKnowledgeBase from '../../shared/help_desk/delete_knowledge_base';
import openPortalTicket from '../../shared/help_desk/open_portal_ticket';
import addCustomForms from '../../shared/company_settings/add_custom_forms';
import indexPageAction from '../../shared/help_desk/ticket_index_page_action';
import addVendor from '../../shared/vendor/add_vendor';
import vendorVar from '../../shared/vendor/vendor_variables';
import editVendor from '../../shared/vendor/edit_vendor';
import deleteVendor from '../../shared/vendor/delete_vendor';
import addProduct from '../../shared/vendor/add_product';
import deleteProduct from '../../shared/vendor/delete_product';
import editProduct from '../../shared/vendor/edit_product';
import addTransaction from '../../shared/vendor/add_transaction';
import editTransaction from '../../shared/vendor/edit_transaction ';
import viewAlert from '../../shared/vendor/check_alert_trigger';
import addCategory from '../../shared/company_settings/add_category';
import removeCurrentCategory from '../../shared/company_settings/remove_category';
import addCustomCategory from '../../shared/company_settings/add_category';
import editgroup from '../../shared/company_settings/edit_group';
import editlocation from '../../shared/company_settings/edit_location';
import editstaff from '../../shared/company_settings/edit_staff';

const addNewGroup = new addgroup();
const newgroupname = 'Test' + nanoid(1);
const editgroupname = 'Test' + nanoid(1);
const editnewgroup = new editgroup();
const deletethegroup = new deletegroup();
const addnewstaff = new addstaff();
const companyVari = new companyvar();
const addnewloc = new addlocation();
const editloc = new editlocation();
const delloc = new deletelocation();
const editnewstaff = new editstaff();
const delstaff = new deletestaff();
const addNewCategory = new addCategory();
const removeCat = new removeCurrentCategory();
const addCustomCat = new addCustomCategory();
const addForm = new addCustomForms();
const golVari = new helpAgentGolVar();
const naviBar = new navbar();
const addCustType = new addAssetType();
const loginIntoApp = new signinpage();
const assetVari = new assetVar();
const addNewDepr = new addDepreciation();
const editDepr = new editDepreciation();
const delDepr = new deleteDepreciation();
const listColumn = new assetListColumn();
const removeAssetType = new removeAssetsType();
const delAsset = new deleteAsset();
const addManagedAssets = new addAsset();
const editManagedAssets = new editAsset();
const softwareCrud = new assetSoftware();
const loginintoapp = new signinpage();
const clonecont = new clonecontracts();
const editcont = new editcontracts();
const delcont = new deletecontracts();
const downloadReports = new report();
const addNewProv = new addProvider();
const addNewSvc = new addService();
const addNewPhonenumber = new addPhoneNumbers();
const telecomVari = new telecomVar();
const deleteAddedPhoneNumber = new deletePhoneNumbers();
const editPnumber = new editPhoneNumbers();
const addNewIpAddress = new addIpAddresses();
const editIpAddress = new editIpAddresses();
const delIpAddress = new deleteIpAddresses();
const delProv = new deleteProvider();
const delSvc = new deleteService();
const editSvc = new editService();
const delGroup = new deletegroup();
const editTic = new editTickets();
const addNewTicket = new addTicket();
const delTicket = new deleteTicket();
const groupName2 = 'Testing' + nanoid(1);
const deskVar = new helpDeskVar();
const AddNewSpace = new addWorkSpace();
const automateTask = new automatedTask();
const delWorkSpace = new deletedWorkSpace();
const editSpace = new editWorkSpace();
const switchNewSpace = new switchWorkSpace();
const addNewDoc = new addDocument();
const editDoc = new editDocument();
const delDoc = new deleteDocument();
const addNewFaq = new addFAQ();
const editNewFaq = new editFAQ();
const delFaq = new deleteFAQ();
const addNewResp = new addCannedResponse();
const editResp = new editCannedResponse();
const delResp = new deleteCannedResponse();
const addArticle = new addKnowledgeBase();
const editArticle = new editKnowledgeBase();
const delArticle = new deleteKnowledgeBase();
const ticketPortal = new openPortalTicket();
const addNewForm = new addCustomForms();
const delUser = new deletestaff();
const contractVari = new contractvar();
const addContract = new addcontracts();
const delContract = new deletecontracts();
const delVendor = new deleteVendor();
const indexPage = new indexPageAction();
const delProvd = new deleteProvider();
const logIntoApp = new signinpage();
const addNewVendor = new addVendor();
const vendorVari = new vendorVar();
const editNewVendor = new editVendor();
const delLoc = new deletelocation();
const addNewProduct = new addProduct();
const delProduct = new deleteProduct();
const editNewProduct = new editProduct();
const addNewTxn = new addTransaction();
const editTxn = new editTransaction();
const checkTriggers = new viewAlert();
const downloadReport = new report();

const category = 'Facilities';
const locName = companyVari.locationname;
const editLocName = companyVari.editlocationname;
const locName1 = companyVari.locationName1;
const module = 'vendors';
const workSpaceName = deskVar.workSpaceName;
const editWorkName = deskVar.editworkName;
const documentName = deskVar.documentName;
const documentName1 = deskVar.documentName1;
const editDocName = deskVar.editDocName;
const ticketSubject = faker.lorem.word(5);
const editSubject = faker.lorem.word(5);
const ticketSubject2 = "Test Ticket" + faker.lorem.word(5);
const ticketSubject3 = 'Ticket open' + nanoid(1);
const ticketSubject4 = 'Ticket portal' + nanoid(2);
const ticketSubject5 = 'Ticket Work' + nanoid(2);
const newQuestion = 'question' + nanoid(1) + '?';
const newAnswer = 'Answer' + nanoid(2);
const editQuestion = 'editquestion' + nanoid(1) + '?';
const editAnswer = 'EditAnswer' + nanoid(2);
const responsetitle = deskVar.newTitle;
const editResponseTitle = deskVar.editTitle;
const staffFstName = 'Staff';
const staffLstName = 'Qa';
const fullName1 = staffFstName + ' ' + staffLstName;
const emailUser = companyVari.email;
const contract = contractVari.contname;
const vendorName = vendorVari.vendorName;
const telecomService = telecomVari.servName;
const newPhoneNumber = telecomVari.phoneNumber;
const editPhoneNumber = telecomVari.editPhoneNumber;
const newIpaddress = telecomVari.ipAddress;
const addSvcName = telecomVari.newSvcName;
const editSvcName = telecomVari.editName;
const editIpAddr = telecomVari.editIpAddress;
const provName = telecomVari.provName;
const sfwName = 'testingSoftware';
const sfwType = 'Application';
const sfwProdNum = '*********';
const sfwInstallDate = '2021-12-20';
const editSfwName = 'edit Software';
const editSfwType = 'Operating System';
const editSfwProdNum = '343434343222';
const editSfwInstallDate = '2020-11-08';
const depreName = assetVari.deprName;
const editDepreName = assetVari.editDeprName;
const assetName1 = assetVari.assetsName1;
const editAssetName = assetVari.editAssetsName1;
const assetName2 = assetVari.assetsName2;
const customAssetType = assetVari.assetType1;
const userEmail = companyVari.email1;
const userEmail1 = companyVari.email2;
const locFormName = 'Location' + nanoid(1);
const userFormName = 'User' + nanoid(1);
const locName2 = 'TLocation' + nanoid(2);
const custStatus1 = deskVar.status1;
const custStatus2 = deskVar.status2;
const custStatus3 = deskVar.status3;
const custPriority1 = deskVar.prority1;
const custPriority2 = deskVar.prority2;
const custPriority3 = deskVar.prority3;

fixture('Smoke ui testcases for help desk agents')

test('All company setting testcases', async t => {
  loginintoapp.siginCompanySelect(golVari.emailaddress, golVari.password)
  await t.wait(2000);
  addNewGroup.addgroup(newgroupname)
  await t.wait(2000);
  addNewGroup.addgroup(groupName2)
  await t.wait(2000);
  editnewgroup.editgroup(editgroupname, newgroupname)
  await t.wait(2000);
  addnewloc.addlocation(locName, companyVari.address, companyVari.city, companyVari.zipcode, companyVari.address, companyVari.pnumber, companyVari.notes)
  await t.wait(2000);
  addnewloc.addlocation(locName2, companyVari.address, companyVari.city, companyVari.zipcode, companyVari.address, companyVari.pnumber, companyVari.notes)
  await t.wait(2000);
  editloc.editlocation(locName, editLocName, companyVari.address, companyVari.city, companyVari.zipcode, companyVari.address, companyVari.pnumber)
  await t.wait(2000);
  addnewstaff.addstaff(companyVari.firstname, companyVari.lastname, userEmail, companyVari.title, companyVari.pnumber, '121', companyVari.department, companyVari.editlocationname)
  await t.wait(2000);
  addnewstaff.addstaff(staffFstName, staffLstName, emailUser, companyVari.title, companyVari.pnumber, '121', companyVari.department, companyVari.editlocationname)
  await t.wait(2000);
  editnewstaff.editstaff(userEmail, companyVari.editfname, companyVari.editlname, companyVari.pnumber)
  await t.wait(2000);
  addNewCategory.addCategory(companyVari.category)
  await t.wait(2000);
  removeCat.removeCurrentCategory(companyVari.category)
  await t.wait(2000);
  removeCat.removeCurrentCategory('IT and Security')
  await t.wait(2000);
  addCustomCat.addCustomCategory('IT and Security')
  await t.wait(2000);
  addForm.addLocCustomform(locFormName, companyVari.option, companyVari.optionName)
  await t.wait(1000);
  addnewloc.addLocThroCustomForm(locFormName, locName1, companyVari.address1, companyVari.city1, companyVari.textArea, companyVari.richText, companyVari.phoneNumber1, companyVari.tag1, companyVari.date1, companyVari.list1, companyVari.number1, companyVari.text1, companyVari.zipCode1)
  await t.wait(1000);
  addForm.addUserCustomform(userFormName, companyVari.option, companyVari.optionName)
  await t.wait(1000);
  addnewstaff.addStaffThroCustomForm(userFormName, companyVari.firstName1, companyVari.lastName1, userEmail1, companyVari.text1, locName1, companyVari.textArea, companyVari.richText, companyVari.phoneNumber1, companyVari.tag2, companyVari.date2, companyVari.list2, companyVari.number1)
  await t.wait(1000);
  // editnewcompany.editcompany(childCompanyName, companyVari.editsubdomain, companyVari.editcompanyphone)
  // await t.wait(1000);
  naviBar.signout()
  await t.wait(2000);
})

test('Assets CRUD', async t => {
  loginIntoApp.siginCompanySelect(golVari.emailaddress, golVari.password)
  await t.wait(1000);
  addManagedAssets.addAssets(assetName1, assetVari.assetsType1, assetVari.assetsTags1, assetVari.assetsNotes1, assetVari.assetsMac1, assetVari.assetsIp1, assetVari.assetsManufacturer1, assetVari.assetsModel1, assetVari.tags1, assetVari.assetsUuid1, assetVari.assetsUpTime1, assetVari.assetsCost1, assetVari.assetsSalvage1, assetVari.assetsDepreType1, assetVari.assetsUsedBy1, assetVari.assetsManagedBy1, editLocName, assetVari.assetsDepartment1, assetVari.assetSerialNumber1, assetVari.assetsProductNum1, '2021-12-08', '2023-11-12', '2021-12-07', assetVari.assetKey1, assetVari.assetsDetials1)
  await t.wait(2000);
  softwareCrud.addSoftware(sfwName, sfwType, sfwProdNum, sfwInstallDate)
  await t.wait(2000);
  editManagedAssets.editAssets(assetName1, editAssetName, assetVari.editAssetsType1, assetVari.editAssetsTags1, assetVari.editAssetsNotes1, assetVari.editAssetsMac1, assetVari.editAssetsIp1, assetVari.editAssetsManufacturer1, assetVari.editAssetsModel1, assetVari.editTags1, assetVari.editAssetsUuid1, assetVari.editAssetsUpTime1, assetVari.editAssetsCost1, assetVari.editAssetsSalvage1, assetVari.editAssetsDepr1, assetVari.editAssetsUsedBy1, assetVari.editAssetsManagedBy1, locName2, assetVari.editAssetsDepartment1, assetVari.editAssetSerialNumber1, assetVari.editAssetsProductNum1, '2021-12-08', '2023-11-12', '2021-12-07', assetVari.assetKey2, assetVari.editAssetsDetials1)
  await t.wait(2000);
  softwareCrud.editSoftware(sfwName, editSfwName, editSfwType, editSfwProdNum, editSfwInstallDate)
  await t.wait(2000);
  softwareCrud.deleteSoftware(editSfwName)
  await t.wait(2000);
  addCustType.addAssetTypes(customAssetType)
  await t.wait(1000);
  addNewDepr.addDepreciations(depreName, assetVari.deprType, assetVari.yearLife, assetVari.description)
  await t.wait(2000);
  addManagedAssets.addAssets(assetName2, customAssetType, assetVari.assetsTags2, assetVari.assetsNotes2, assetVari.assetsMac2, assetVari.assetsIp2, assetVari.assetsManufacturer2, assetVari.assetsModel2, assetVari.tags2, assetVari.assetsUuid2, assetVari.assetsUpTime2, assetVari.assetsCost2, assetVari.assetsSalvage2, depreName, assetVari.assetsUsedBy2, assetVari.assetsManagedBy2, locName2, assetVari.assetsDepartment2, assetVari.assetSerialNumber2, assetVari.assetsProductNum2, '2021-12-08', '2023-11-12', '2021-12-07', assetVari.assetKey2, assetVari.assetsDetials2)
  await t.wait(2000);
  softwareCrud.addSoftware(sfwName, sfwType, sfwProdNum, sfwInstallDate)
  await t.wait(1000);
  softwareCrud.deleteSoftware(sfwName)
  await t.wait(2000);
  removeAssetType.removeCustomTypes(customAssetType)
  await t.wait(2000);
  removeAssetType.removeDefaultTypes(assetVari.defaultTypes1)
  await t.wait(2000);
  addCustType.addDefaultTypes(assetVari.defaultTypes1)
  await t.wait(1000);
  editDepr.editDepreciations(depreName, editDepreName, assetVari.editDeprType, assetVari.editYearLife, assetVari.editDescription)
  await t.wait(1000);
  delDepr.deleteDepreciations(editDepreName)
  await t.wait(1000);
  listColumn.addAssetList(assetVari.assetListColumn1)
  await t.wait(2000);
  listColumn.removeAssetList(assetVari.assetListColumn1)
  await t.wait(2000);
  naviBar.signout()
  await t.wait(1000);
})

test('CRUD operation for help desk and its sub operations', async t => {
  logIntoApp.siginCompanySelect(golVari.emailaddress, golVari.password)
  await t.wait(2000);
  addContract.addcontracts(contract, contractVari.startdate, contractVari.alertdate, contractVari.amount, locName2, fullName1, contractVari.montlyType)
  await t.wait(1000);
  addNewVendor.addVendors(vendorName, vendorVari.url, vendorVari.category, vendorVari.bussinessUnit, vendorVari.description, vendorVari.accountNumber, vendorVari.tags, vendorVari.address, vendorVari.city, vendorVari.state, vendorVari.country, vendorVari.zipCode, vendorVari.phoneNumber, vendorVari.alertNumber, vendorVari.billingMethods, vendorVari.billingTerms, fullName1, locName2)
  await t.wait(3000);
  addNewProv.addProviders(provName, telecomService, editLocName)
  await t.wait(1000);
  addNewSvc.addServices(provName, addSvcName, editLocName)
  await t.wait(1000);
  automateTask.addAutomatedTasks(deskVar.eventName, deskVar.triggerDetail1, deskVar.ticketCreatedTrigger, deskVar.actionAlert, deskVar.recipientAllAgents, deskVar.addTextAlert, deskVar.defaultWorkSpace);
  await t.wait(1000)
  addNewForm.addTicketCustomform(companyVari.ticketFormName, companyVari.option, companyVari.optionName, custStatus1, custStatus2, custStatus3, custPriority1, custPriority2, custPriority3)
  await t.wait(1000);
  addNewTicket.addTicketwithBaseForm(ticketSubject, deskVar.highPriority, deskVar.inProgressStatus, editgroupname, groupName2, editAssetName, locName2, deskVar.notes)
  await t.wait(1000);
  addNewTicket.addTicThroCustomForm(companyVari.ticketFormName, ticketSubject2, 'Help Desk Agents', telecomService, contract, deskVar.highPriority, locName2, vendor, editAssetName, deskVar.openStatus, deskVar.everyOneUser, deskVar.adminUser, companyVari.textArea, companyVari.richText, companyVari.tag1, companyVari.date1, companyVari.phoneNumber1, companyVari.list1, companyVari.text1, companyVari.number1, category, companyVari.notes)
  await t.wait(1000);
  indexPage.updateTicketThroughGridView(ticketSubject2, deskVar.openStatus, deskVar.lowPriority)
  await t.wait(1000);
  editTic.editTicketswithBaseForm(ticketSubject, editgroupname, groupName2, editAssetName, locName2, '', editSubject, deskVar.meduimPriority, deskVar.openStatus, deskVar.everyOneUser, deskVar.adminUser, editAssetName, locName2, '', deskVar.comments, deskVar.notes, deskVar.highPriority, deskVar.dueDate, deskVar.completedDate, deskVar.helpDeskAgent, deskVar.timeSpent, deskVar.date, deskVar.notes)
  await t.wait(1000);
  automateTask.deleteAutomatedTask(deskVar.eventName)
  await t.wait(1000);
  downloadReport.reports(deskVar.HelpTicketModule, deskVar.frameSelection)
  await t.wait(1000);
  delTicket.deleteTickets(editSubject, 'yes')
  await t.wait(1000);
  addNewDoc.addDocuments(documentName)
  await t.wait(1000);
  addArticle.addKnowledgeBases(deskVar.articleTitle, deskVar.articleTags, deskVar.selectCategory, documentName, deskVar.articleText)
  await t.wait(1000);
  addNewDoc.addDocuments(documentName1)
  await t.wait(1000);
  editArticle.editKnowledgeBases(deskVar.articleTitle, deskVar.selectCategory, deskVar.editArticleTitle, deskVar.editArticleTags, deskVar.selectCategory1, documentName1, deskVar.articleText)
  await t.wait(1000);
  delArticle.deleteKnowledgeBases(deskVar.editArticleTitle, deskVar.selectCategory1)
  await t.wait(1000);
  delDoc.deleteDocuments(documentName1)
  await t.wait(1000)
  editDoc.editDocuments(documentName, editDocName)
  await t.wait(1000);
  delDoc.deleteDocuments(editDocName)
  await t.wait(1000);
  addNewFaq.addFAQs(newQuestion, newAnswer, deskVar.selectCategory)
  await t.wait(1000);
  editNewFaq.editFAQs(newQuestion, editQuestion, editAnswer, deskVar.selectCategory1)
  await t.wait(1000);
  delFaq.deleteFAQs(editQuestion, 'yes')
  await t.wait(1000);
  addNewResp.addCannedResponses(responsetitle, deskVar.text)
  await t.wait(1000);
  editResp.editCannedResponses(responsetitle, editResponseTitle, deskVar.text)
  await t.wait(1000);
  delResp.deleteCannedResponses(editResponseTitle)
  await t.wait(1000);
  AddNewSpace.addWorkSpaces(workSpaceName, editgroupname, deskVar.userPerm1, deskVar.userPerm2, deskVar.defaultForm)
  await t.wait(1000);
  switchNewSpace.switchWorkSpaces(workSpaceName)
  await t.wait(1000);
  automateTask.addAutomatedTasks(deskVar.eventName, deskVar.triggerDetail1, deskVar.ticketCreatedTrigger, deskVar.actionAlert, deskVar.recipientAllAgents, deskVar.addTextAlert, workSpaceName);
  await t.wait(1000);
  switchNewSpace.selectWorkSpace(workSpaceName)
  await t.wait(1000);
  addNewTicket.addTicketwithBaseForm(ticketSubject5, deskVar.highPriority, deskVar.inProgressStatus, editgroupname, groupName2, editAssetName, locName2, deskVar.notes)
  await t.wait(1000);
  automateTask.deleteAutomatedTask(deskVar.eventName)
  await t.wait(1000);
  indexPage.updateTicketThroughListView(ticketSubject5, groupName2, deskVar.inProgressStatus, deskVar.meduimPriority, editgroupname)
  await t.wait(1000);
  switchNewSpace.selectWorkSpace(workSpaceName)
  await t.wait(1000);
  downloadReport.reports(deskVar.HelpTicketModule, deskVar.frameSelection)
  await t.wait(1000);
  editSpace.editWorkSpaces(workSpaceName, editWorkName, groupName2, deskVar.userPerm2, deskVar.userPerm1)
  await t.wait(1000);
  ticketPortal.createTicketWithlogin(ticketSubject3, deskVar.highPriority, deskVar.inProgressStatus, editgroupname, deskVar.notes, deskVar.defaultWorkSpace, deskVar.defaultForm)
  await t.wait(1000);
  ticketPortal.createTicketWithoutLogin(golVari.emailaddress, golVari.password, ticketSubject4, deskVar.highPriority, deskVar.inProgressStatus, golVari.guestEmail, deskVar.notes, deskVar.defaultWorkSpace, deskVar.defaultForm)
  await t.wait(1000);
  switchNewSpace.switchWorkSpaces(deskVar.defaultWorkSpace)
  await t.wait(1000);
  delWorkSpace.deleteWorkSpaces(editWorkName)
  await t.wait(1000);
  delContract.deletecontracts(contract)
  await t.wait(1000);
  delVendor.deleteVendors(vendor)
  await t.wait(1000);
  delProvd.deleteProviders(provider)
  await t.wait(2000);
  naviBar.signout()
  await t.wait(1000);
})

test('contracts CRUD', async t => {
  loginintoapp.siginCompanySelect(golVari.emailaddress, golVari.password)
  await t.wait(1000);
  clonecont.clonecontracts(contract)
  await t.wait(1000);
  downloadReports.reportsWithTabular(contractVari.module, contractVari.allTime)
  await t.wait(1000);
  downloadReports.reportsWithGraphical(contractVari.module, contractVari.donutChart, contractVari.spendQtd, contractVari.contname, contractVari.graphTitle, contractVari.dollar)
  await t.wait(1000);
  editcont.editcontracts(contractVari.contname, contractVari.editcontname, contractVari.editstartdate, contractVari.editalertdate, contractVari.editamount)
  await t.wait(1000);
  delcont.deletecontracts(contractVari.contname + " " + '(Copy)')
  await t.wait(1000);
  naviBar.signout()
  await t.wait(1000);
})

test('Telecom CRUD', async t => {
  loginIntoApp.siginCompanySelect(golVari.emailaddress, golVari.password)
  await t.wait(1000);
  addNewProv.addProviders(provName, telecomService, editLocName)
  await t.wait(1000);
  addNewSvc.addServices(provName, addSvcName, editLocName)
  await t.wait(1000);
  addNewPhonenumber.addPhoneNumbers(newPhoneNumber, provName, editLocName, telecomVari.friendlyName, telecomVari.notes)
  await t.wait(2000);
  editPnumber.editPhoneNumbers(newPhoneNumber, editPhoneNumber, telecomVari.editFriendlyName, 'Access One', telecomVari.notes)
  await t.wait(2000);
  deleteAddedPhoneNumber.deletePhoneNumbers(editPhoneNumber)
  await t.wait(1000);
  addNewIpAddress.addIpAddresses(newIpaddress, provName, editLocName, telecomVari.friendlyName, telecomVari.notes)
  await t.wait(1000);
  editIpAddress.editIpAddresses(newIpaddress, editIpAddr, telecomVari.editFriendlyName, 'Access One', telecomVari.editNotes)
  await t.wait(1000);
  delIpAddress.deleteIpAddresses(editIpAddr)
  await t.wait(1000)
  editSvc.editServices(provName, addSvcName, editSvcName, editLocName)
  await t.wait(2000);
  delSvc.deleteServices(provName, editSvcName)
  await t.wait(1000)
  delProv.deleteProviders(provName)
  await t.wait(2000);
  naviBar.signout()
  await t.wait(1000);
})

test('Vendor CRUD', async t => {
  logIntoApp.siginCompanySelect(golVari.emailaddress, golVari.password)
  await t.wait(1000);
  addNewProduct.addProducts(vendorName, vendorVari.productName, vendorVari.totalLicense, vendorVari.licenseUsed, vendorVari.category, vendorVari.productType, vendorVari.arrivalDate)
  await t.wait(1000);
  addNewTxn.addTransactionswithlastmonth(vendorName, vendorVari.transactionName, vendorVari.previousDayTxn, vendorVari.amount, vendorVari.productName, locName2)
  await t.wait(1000);
  addNewTxn.addTxnCurrentMonth(vendorName, vendorVari.transactionName1, vendorVari.amount1, vendorVari.productName, locName2)
  await t.wait(1000);
  checkTriggers.viewAlerts(vendorName, vendorVari.alertBadge, vendorVari.alertNumber, vendorVari.overPrecent);
  await t.wait(1000);
  editTxn.editTransactions(vendorName, vendorVari.transactionName, vendorVari.editTxnName, vendorVari.editTxnDate, vendorVari.editAmount)
  await t.wait(2000);
  downloadReports.reportsWithTabular(module, vendorVari.calendarYear)
  await t.wait(1000);
  downloadReports.reportsWithGraphical(module, vendorVari.donutChart, vendorVari.totalCost, vendorName, vendorVari.graphTitle, vendorVari.dollar)
  await t.wait(1000);
  editNewProduct.editProducts(vendorName, vendorVari.productName, vendorVari.editProductName, vendorVari.editTotalLicense, vendorVari.editLicenseUsed, vendorVari.editCategory, vendorVari.editProductType, vendorVari.editArrivalDate)
  await t.wait(1000);
  delProduct.deleteProducts(vendorName, vendorVari.editProductName)
  await t.wait(1000);
  editNewVendor.editVendors(vendorName, vendorVari.editVendorName, vendorVari.editUrl, vendorVari.editCategory, vendorVari.editBussinessUnit, vendorVari.editDescription, vendorVari.editAccountNumber, vendorVari.editTags, vendorVari.editAddress, vendorVari.editCity, vendorVari.editState, vendorVari.editCountry, vendorVari.editZipCode, vendorVari.editPhoneNumber, vendorVari.editAlertNumber, vendorVari.editBillingMethods, vendorVari.editBillingTerms)
  await t.wait(1000);
  delAsset.deleteAssets(editAssetName)
  await t.wait(1000);
  delVendor.deleteVendors(vendorVari.editVendorName)
  await t.wait(1000);
  delAsset.deleteAssets(assetName2)
  await t.wait(1000);
  delGroup.deletegroup(groupName2)
  await t.wait(1000);
  deletethegroup.deletegroup(editgroupname)
  await t.wait(2000);
  delLoc.deletelocation(editLocName)
  await t.wait(1000);
  delstaff.deletestaff(userEmail)
  await t.wait(2000);
  delUser.deletestaff(emailUser)
  await t.wait(1000);
  delcont.deletecontracts(contractVari.editcontname)
  await t.wait(1000);
  delstaff.deletestaff(userEmail1)
  await t.wait(2000);
  delloc.deletelocation(locName2)
  await t.wait(1000);
  delContract.deletecontracts(contract)
  await t.wait(1000);
  delloc.deletelocation(locName1)
  await t.wait(1000);
  naviBar.signout()
  await t.wait(1000);
})
