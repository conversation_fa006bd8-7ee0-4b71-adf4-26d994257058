import signUp from "../../shared/signup/sign_up";
import golvar from "../../shared/Globalvariable";
import navbar from "../../shared/Components/navbar";

const newCompany = new signUp();
const golVari = new golvar();
const naviBar = new navbar();

fixture('Smoke testcases for Signup module')

test('Signup a new company', async t => {
  newCompany.signUpNewCompany(golVari.firstName, golVari.lastName, golVari.companyemail, golVari.password, golVari.companyname, golVari.subdomain1)
  await t.wait(1000);
  naviBar.signout()
  await t.wait(1000);
})
