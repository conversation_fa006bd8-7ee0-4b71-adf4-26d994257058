import addcontracts from "../../shared/contracts/add_contract";
import golvar from "../../shared/Globalvariable";
import navbar from "../../shared/Components/navbar";
import signinpage from "../../shared/signin/signinpage";
import contractvar from "../../shared/contracts/contract_variables";
import clonecontracts from "../../shared/contracts/clone_contract";
import editcontracts from "../../shared/contracts/edit_contract";
import deletecontracts from "../../shared/contracts/delete_contract";
import report from "../../shared/help_desk/reports";
import viewCount from "../../shared/contracts/check_counter";

const addNewContracts = new addcontracts();
const golVari = new golvar();
const naviBar = new navbar();
const loginIntoApp = new signinpage();
const contractVari = new contractvar();
const cloneContract = new clonecontracts();
const editContract = new editcontracts();
const delContract = new deletecontracts();
const downloadReports = new report();
const verifyCount = new viewCount();

fixture`Smoke testcases for contract module`;

test('contracts CRUD', async t => {
  loginIntoApp.siginCompanySelect(golVari.emailaddress, golVari.password, golVari.companyname);
  await t.wait(1000);
  addNewContracts.addcontracts(contractVari.contname, contractVari.startdate, contractVari.alertdate, contractVari.amount, golVari.locName1, golVari.fullName1, contractVari.montlyType);
  await t.wait(1000);
  verifyCount.checkCounter('2', 'Open contracts', 'Total contracts');
  await t.wait(1000);
  verifyCount.counterCategoryWise(contractVari.category, '2');
  await t.wait(1000);
  cloneContract.clonecontracts(contractVari.contname);
  await t.wait(1000);
  verifyCount.checkCounter('3', 'Open contracts', 'Total contracts');
  await t.wait(1000);
  verifyCount.counterCategoryWise(contractVari.category, '3');
  await t.wait(1000);
  editContract.editcontracts(contractVari.contname, contractVari.editcontname, contractVari.editstartdate, contractVari.editalertdate, contractVari.editamount);
  await t.wait(1000);
  delContract.deletecontracts(contractVari.editcontname);
  await t.wait(1000);
  await t.click(naviBar.contractsbtn);
  await t.wait(1000);
  verifyCount.checkCounter('2', 'Open contracts', 'Total contracts');
  await t.wait(1000);
  verifyCount.counterCategoryWise(contractVari.category, '2');
  await t.wait(1000);
  downloadReports.reportsWithTabular(contractVari.module, contractVari.allTime);
  await t.wait(1000);
  downloadReports.reportsWithGraphicalContract(contractVari.module, contractVari.donutChart, contractVari.spendQtd, golVari.contract, contractVari.graphTitle, contractVari.dollar);
  await t.wait(1000);
  delContract.deletecontracts(`${contractVari.contname} (Copy)`);
  await t.wait(1000);
  naviBar.signout();
  await t.wait(1000);
});
