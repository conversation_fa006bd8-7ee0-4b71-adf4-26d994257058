import signinpage from '../../shared/signin/signinpage'
import golvar from '../../shared/Globalvariable'
import navbar from '../../shared/Components/navbar'


const golvari = new golvar()
const logintoapp = new signinpage()
const signout = new navbar

fixture`Signin Usecases`
test("Sigin with valid credentials", async t => {
  logintoapp.sigin(golvari.companyemail, golvari.password)
  signout.signout()
  await t.wait(1000)
})
