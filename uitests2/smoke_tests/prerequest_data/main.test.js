import signinpage from "../../shared/signin/signinpage";
import assetVar from "../../shared/assets/asset_variables";
import golvar from "../../shared/Globalvariable";
import navbar from '../../shared/Components/navbar';
import addAsset from "../../shared/assets/add_assets";
import addlocation from '../../shared/company_settings/add_location';
import companyvar from "../../shared/company_settings/company_variables";
import addgroup from "../../shared/company_settings/add_group";
import vendorVar from "../../shared/vendor/vendor_variables";
import contractvar from "../../shared/contracts/contract_variables";
import addcontracts from "../../shared/contracts/add_contract";
import addVendor from "../../shared/vendor/add_vendor";
import addProvider from "../../shared/telecom/add_provider";
import addstaff from "../../shared/company_settings/add_staff";
import { Selector } from "testcafe";

const golVari = new golvar();
const naviBar = new navbar();
const loginIntoApp = new signinpage();
const addManagedAssets = new addAsset();
const addLoc = new addlocation();
const addGroup = new addgroup();
const addUser = new addstaff();
const addContract = new addcontracts();
const addNewVendor = new addVendor();
const addProvd = new addProvider();
const assetVari = new assetVar();
const companyVari = new companyvar();
const vendorVari = new vendorVar();
const contractVari = new contractvar();

fixture('Creation of test data')

test('Pre Request Test Data', async t => {
  loginIntoApp.siginCompanySelect(golVari.emailaddress, golVari.password, golVari.companyname);
  await t.wait(1000);
  await t.hover(addManagedAssets.companyViewToggle)
  await t.wait(1000);
  await t.click(addManagedAssets.companyViewToggle)
  await t.wait(2000);
  naviBar.navigatetocompanysetting();
  await t.wait(2000);
  addGroup.addgroup(golVari.groupName1);
  await t.wait(1000);
  addGroup.addgroup(golVari.groupName2);
  await t.wait(1000);
  addLoc.addlocation(golVari.locName1, companyVari.address, companyVari.city, companyVari.zipcode, companyVari.address, companyVari.pnumber, companyVari.notes);
  await t.wait(1000);
  addLoc.addlocation(golVari.locName2, companyVari.address, companyVari.city, companyVari.zipcode, companyVari.address, companyVari.pnumber, companyVari.notes);
  await t.wait(1000);
  addUser.addstaff(golVari.firstName1, golVari.lastName1, golVari.userEmail, companyVari.title, companyVari.pnumber, '121', companyVari.department, golVari.locName1);
  await t.wait(1000);
  addManagedAssets.addAssets(golVari.assetName1, assetVari.assetsType1, assetVari.assetsTags1, assetVari.assetsNotes1, assetVari.assetsMac1, assetVari.assetsIp1, assetVari.assetsManufacturer1, assetVari.assetsModel1, assetVari.tags1, assetVari.assetsUuid1, assetVari.assetsUpTime1, assetVari.assetsCost1, assetVari.assetsSalvage1, assetVari.assetsDepreType1, assetVari.assetsUsedBy1, assetVari.assetsManagedBy1, golVari.locName1, assetVari.assetsDepartment1, assetVari.assetSerialNumber1, assetVari.assetsProductNum1, '2021-12-08', '2023-11-12', '2021-12-07', assetVari.assetKey1, assetVari.assetsDetials1);
  await t.wait(2000);
  addContract.addcontracts(golVari.contract, contractVari.startdate, contractVari.alertdate, contractVari.amount, golVari.locName2, golVari.fullName, contractVari.montlyType);
  await t.wait(1000);
  addNewVendor.addVendors(golVari.vendor, vendorVari.url, vendorVari.category, vendorVari.bussinessUnit, vendorVari.description, vendorVari.accountNumber, vendorVari.tags, vendorVari.address, vendorVari.city, vendorVari.state, vendorVari.country, vendorVari.zipCode, vendorVari.phoneNumber, vendorVari.alertNumber, vendorVari.billingMethods, vendorVari.billingTerms, golVari.fullName, golVari.contract);
  await t.wait(2000);
  addProvd.addProviders(golVari.provider, golVari.telecomService, golVari.locName2);
  await t.wait(1000);
  naviBar.signout();
  await t.wait(1000);
})
