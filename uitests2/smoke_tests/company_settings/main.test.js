import addcompany from '../../shared/company_settings/add_childcompany';
import templatepage from '../../shared/company_settings/template_tab';
import addgroup from '../../shared/company_settings/add_group';
import { nanoid } from 'nanoid';
import faker from 'faker';
import navbar from '../../shared/Components/navbar';
import editgroup from '../../shared/company_settings/edit_group';
import deletegroup from '../../shared/company_settings/delete_group';
import addstaff from '../../shared/company_settings/add_staff';
import companyvar from '../../shared/company_settings/company_variables';
import addlocation from '../../shared/company_settings/add_location';
import editlocation from '../../shared/company_settings/edit_location';
import deletelocation from '../../shared/company_settings/delete_location';
import signinpage from '../../shared/signin/signinpage';
import golvar from '../../shared/Globalvariable';
import editstaff from '../../shared/company_settings/edit_staff';
import deletestaff from '../../shared/company_settings/delete_archive_staff';
import addCategory from '../../shared/company_settings/add_category';
import editcompany from '../../shared/company_settings/edit_company';
import removeCurrentCategory from '../../shared/company_settings/remove_category';
import addCustomCategory from '../../shared/company_settings/add_category';
import copyGroup from '../../shared/company_settings/add_copy_group';
import viewCopiedGroup from '../../shared/company_settings/view_copied_group';
import copyCategory from '../../shared/company_settings/add_copy_category';
import viewCopiedCategory from '../../shared/company_settings/view_copied_category';
import copyDocument from '../../shared/company_settings/add_copy_document';
import viewCopiedDocument from '../../shared/company_settings/view_copied_document';
import copyResponse from '../../shared/company_settings/add_copy_response';
import viewCopiedResponse from '../../shared/company_settings/view_copied_response';
import copyFAQ from '../../shared/company_settings/add_copy_FAQ';
import viewCopiedFaq from '../../shared/company_settings/view_copied_faq';
import addCustomForms from '../../shared/company_settings/add_custom_forms';
import switchCompany from '../../shared/company_settings/switch _company';
import viewCount from '../../shared/company_settings/check_counter';

const addnewcompany = new addcompany();
const template = new templatepage();
const addnewgroup = new addgroup();
const naviBar = new navbar();
const newgroupname = 'Test' + nanoid(1);
const editgroupname = 'Test' + nanoid(1);
const editnewgroup = new editgroup();
const deletethegroup = new deletegroup();
const addnewstaff = new addstaff();
const companyvari = new companyvar();
const addnewloc = new addlocation();
const editloc = new editlocation();
const delloc = new deletelocation();
const loginintoapp = new signinpage();
const golvari = new golvar();
const editnewstaff = new editstaff();
const delstaff = new deletestaff();
const addNewCategory = new addCategory();
const editnewcompany = new editcompany();
const removeCat = new removeCurrentCategory();
const addCustomCat = new addCustomCategory();
const copyNewGroup = new copyGroup();
const viewNewGroup = new viewCopiedGroup();
const copyNewCategory = new copyCategory();
const viewNewCategory = new viewCopiedCategory();
const copyNewDocument = new copyDocument();
const viewCopiedDoc = new viewCopiedDocument();
const copyNewResponse = new copyResponse();
const viewCopiedResp = new viewCopiedResponse();
const copyNewFaq = new copyFAQ();
const viewCopiedQn = new viewCopiedFaq();
const addForm = new addCustomForms();
const switchComp = new switchCompany();
const verifyCount = new viewCount();
const addNewGroup = 'Group' + nanoid(1);
const addCategoryName = 'Category' + nanoid(2);
const addDocumentName = 'Document' + nanoid(2);
const addRespTitle = 'Testing' + nanoid(2);
const question = 'FAQs' + nanoid(2) + '?';
const text = faker.lorem.words();
const category = 'Facilities';
const parentCompany = golvari.companyname;
const childCompanyName = companyvari.companyname;
const childDomain = companyvari.subdomain;
const templateName = `Template ${nanoid(2)}`;
const templateDesc = `This is templates ${faker.lorem.words()}`;

fixture`Company setting usecases`

test('All company setting testcases', async t => {
  loginintoapp.sigin(golvari.emailaddress, golvari.password);
  await t.wait(1000);
  naviBar.navigatetocompanysetting();
  await t.wait(1000);
  template.templateview();
  await t.wait(1000);
  addnewcompany.addchildcompany(childCompanyName, childDomain)
  await t.wait(1000);
  switchComp.switchToCompany(parentCompany)
  await t.wait(1000);
  naviBar.navigatetocompanysetting();
  await t.wait(1000);
  addnewgroup.addgroup(newgroupname);
  await t.wait(1000);
  editnewgroup.editgroup(editgroupname, newgroupname);
  await t.wait(1000);
  addnewloc.addlocation(companyvari.locationname, companyvari.address, companyvari.city, companyvari.zipcode, companyvari.address, companyvari.pnumber, companyvari.notes);
  await t.wait(1000);
  editloc.editlocation(companyvari.locationname, companyvari.editlocationname, companyvari.address, companyvari.city, companyvari.zipcode, companyvari.address, companyvari.pnumber);
  await t.wait(1000);
  addnewstaff.addstaff(companyvari.firstname, companyvari.lastname, companyvari.email, companyvari.title, companyvari.pnumber, '121', companyvari.department, companyvari.editlocationname);
  await t.wait(1000);
  editnewstaff.editstaff(companyvari.email, companyvari.editfname, companyvari.editlname, companyvari.pnumber)
  await t.wait(1000);
  addForm.addLocCustomform(companyvari.locFormName, companyvari.option, companyvari.optionName);
  await t.wait(1000);
  addnewloc.addLocThroCustomForm(companyvari.locFormName, companyvari.locationName1, companyvari.address1, companyvari.city1, companyvari.textArea, companyvari.richText, companyvari.phoneNumber1, companyvari.tag1, companyvari.date1, companyvari.list1, companyvari.number1, companyvari.text1, companyvari.zipCode1); //https://gogenuity.atlassian.net/browse/GN-4168
  await t.wait(1000);
  addForm.addUserCustomform(companyvari.userFormName, companyvari.option, companyvari.optionName);
  await t.wait(1000);
  addnewstaff.addStaffThroCustomForm(companyvari.userFormName, companyvari.firstName1, companyvari.lastName1, companyvari.email1, companyvari.text1, companyvari.locationName1, companyvari.textArea, companyvari.richText, companyvari.phoneNumber1, companyvari.tag2, companyvari.date2, companyvari.list2, companyvari.number1); // https://gogenuity.atlassian.net/browse/GN-4168
  await t.wait(1000);
  addNewCategory.addCategory(companyvari.category);
  await t.wait(1000);
  removeCat.removeCurrentCategory(companyvari.category);
  await t.wait(1000);
  removeCat.removeCurrentCategory('HR');
  await t.wait(1000);
  addCustomCat.addCustomCategory('HR');
  await t.wait(1000);
  verifyCount.checkCounter('4', '3', 'People');
  await t.wait(1000);
  naviBar.navigatetocompanysetting();
  await t.wait(1000);
  delloc.deletelocation(companyvari.editlocationname);
  await t.wait(1000);
  delstaff.deletestaff(companyvari.email);
  await t.wait(1000);
  delloc.deletelocation(companyvari.locationName1);
  await t.wait(1000);
  switchComp.switchToCompany(parentCompany);
  await t.wait(1000);
  naviBar.navigatetocompanysetting();
  await t.wait(1000);
  // template.addNewTemplate(templateName, templateDesc);
  // await t.wait(1000);
  // copyNewGroup.copyGroups(editgroupname, childCompanyName, templateName);
  // await t.wait(1000);
  // viewNewGroup.viewCopiedGroups(childCompanyName, editgroupname);
  // await t.wait(1000);
  // await t.click(naviBar.companySettingTab);
  // await t.wait(1000);
  // copyNewCategory.copyCategoires(addCategoryName, childCompanyName, parentCompany, templateName);
  // await t.wait(1000);
  // viewNewCategory.viewCopiedCategories(childCompanyName, addCategoryName);
  // await t.wait(1000);
  // await t.click(naviBar.companySettingTab);
  // await t.wait(1000);
  // copyNewDocument.copyDocuments(addDocumentName, childCompanyName, parentCompany, templateName);
  // await t.wait(1000);
  // viewCopiedDoc.viewCopiedDocuments(childCompanyName, addDocumentName);
  // await t.wait(1000);
  // naviBar.navigatetocompanysetting();
  // await t.wait(1000);
  // copyNewResponse.copyResponses(addRespTitle, text, childCompanyName, parentCompany, templateName);
  // await t.wait(1000);
  // viewCopiedResp.viewCopiedResponses(childCompanyName, addRespTitle);
  // await t.wait(1000);
  // naviBar.navigatetocompanysetting();
  // await t.wait(1000);
  // copyNewFaq.copyFAQs(category, question, text, childCompanyName, parentCompany, templateName);
  // await t.wait(1000);
  // viewCopiedQn.viewCopiedFaqs(childCompanyName, question);
  // await t.wait(1000);
  // naviBar.navigatetocompanysetting();
  // await t.wait(1000);
  switchComp.switchToCompany(parentCompany);
  await t.wait(1000);
  naviBar.navigatetocompanysetting();
  await t.wait(1000);
  deletethegroup.deletegroup(editgroupname);
  await t.wait(1000);
  switchComp.switchToCompany(childCompanyName);
  await t.wait(1000);
  await t.click(naviBar.companySettingTab);
  await t.wait(1000);
  editnewcompany.editcompany(childCompanyName, companyvari.editsubdomain, companyvari.editcompanyphone);
  await t.wait(1000);
  naviBar.signout();
  await t.wait(1000);
});
