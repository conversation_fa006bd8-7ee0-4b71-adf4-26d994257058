import golvar from "../../shared/Globalvariable";
import companyvar from "../../shared/company_settings/company_variables";
import deleteCompany from "../../shared/company_settings/delete_company";

const companyDel = new deleteCompany();
const golVari = new golvar();
const companyVari = new companyvar();

fixture`Delete the created company`

test('Company Deletion', async t => {
  companyDel.companyDeletion(golVari.companyname);
  await t.wait(1000);
  companyDel.companyDeletion(companyVari.companyname);
  await t.wait(1000);
})
