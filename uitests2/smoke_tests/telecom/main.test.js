import golvar from "../../shared/Globalvariable";
import signinpage from "../../shared/signin/signinpage";
import addProvider from "../../shared/telecom/add_provider";
import addPhoneNumbers from "../../shared/telecom/add_phonenumber";
import navbar from "../../shared/Components/navbar";
import telecomVar from "../../shared/telecom/telecom_variables";
import addService from "../../shared/telecom/add_service";
import deletePhoneNumbers from "../../shared/telecom/delete_phonenumber";
import editPhoneNumbers from "../../shared/telecom/edit_phonenumber.js";
import addIpAddresses from "../../shared/telecom/add_ipaddress";
import editIpAddresses from "../../shared/telecom/edit_ipaddress";
import deleteIpAddresses from "../../shared/telecom/delete_ipaddress";
import deleteProvider from "../../shared/telecom/delete_provider";
import editService from "../../shared/telecom/edit_service";
import deleteService from "../../shared/telecom/delete_service";
import checkCountTelecom from "../../shared/telecom/verify_telecom_count";

const golVari = new golvar();
const loginIntoApp = new signinpage();
const addNewProv = new addProvider();
const addNewSvc = new addService();
const addNewPhonenumber = new addPhoneNumbers();
const naviBar = new navbar();
const telecomVari = new telecomVar();
const deleteAddedPhoneNumber = new deletePhoneNumbers();
const editPnumber = new editPhoneNumbers();
const addNewIpAddress = new addIpAddresses();
const editIpAddress = new editIpAddresses();
const delIpAddress = new deleteIpAddresses();
const delProv = new deleteProvider();
const delSvc = new deleteService();
const editSvc = new editService();
const viewCount = new checkCountTelecom();

const newPhoneNumber = telecomVari.phoneNumber;
const editPhoneNumber = telecomVari.editPhoneNumber;
const newIpaddress = telecomVari.ipAddress;
const addSvcName = telecomVari.newSvcName;
const editSvcName = telecomVari.editName;
const editIpAddr = telecomVari.editIpAddress;
const provName = telecomVari.provName;

fixture('Smoke testcases for Telecom module')

test('Telecom CRUD', async t => {
  loginIntoApp.siginCompanySelect(golVari.emailaddress, golVari.password, golVari.companyname);
  await t.wait(1000);
  addNewProv.addProviders(provName, telecomVari.servName, golVari.locName1);
  await t.wait(1000);
  addNewSvc.addServices(provName, addSvcName, golVari.locName1);
  await t.wait(1000);
  // viewCount.counterCategoryWise(telecomVari.voiceCategory, '2');
  // await t.wait(1000);
  addNewPhonenumber.addPhoneNumbers(newPhoneNumber, provName, golVari.locName1, telecomVari.friendlyName, telecomVari.notes);
  await t.wait(2000);
  viewCount.checkCountPhone('1')
  await t.wait(1000);
  editPnumber.editPhoneNumbers(newPhoneNumber, editPhoneNumber, telecomVari.editFriendlyName, 'Access One', telecomVari.notes);
  await t.wait(2000);
  deleteAddedPhoneNumber.deletePhoneNumbers(editPhoneNumber);
  await t.wait(1000);
  addNewIpAddress.addIpAddresses(newIpaddress, provName, golVari.locName1, telecomVari.friendlyName, telecomVari.notes);
  await t.wait(1000);
  viewCount.checkCountIp('1')
  await t.wait(1000);
  editIpAddress.editIpAddresses(newIpaddress, editIpAddr, telecomVari.editFriendlyName, 'Access One', telecomVari.editNotes);
  await t.wait(1000);
  delIpAddress.deleteIpAddresses(editIpAddr);
  await t.wait(1000)
  editSvc.editServices(provName, addSvcName, editSvcName, golVari.locName2);
  await t.wait(2000);
  delSvc.deleteServices(provName, editSvcName);
  await t.wait(1000);
  delProv.deleteProviders(provName);
  await t.wait(2000);
  // viewCount.counterCategoryWise(telecomVari.voiceCategory, '0')
  // await t.wait(1000);
  naviBar.signout();
  await t.wait(1000);
})
