import { nanoid } from 'nanoid';
import faker from 'faker';
import signinpage from '../../shared/signin/signinpage';
import golvar from '../../shared/Globalvariable';
import navbar from '../../shared/Components/navbar';
import companyvar from '../../shared/company_settings/company_variables';
import deleteTicket from '../../shared/help_desk/delete_tickets';
import addTicket from '../../shared/help_desk/add_tickets';
import editTickets from '../../shared/help_desk/edit_tickets';
import helpDeskVar from '../../shared/help_desk/help_desk_variables';
import addWorkSpace from '../../shared/help_desk/add_work_space';
import deletedWorkSpace from '../../shared/help_desk/delete_work_space';
import automatedTask from '../../shared/help_desk/automated_tasks';
import editWorkSpace from '../../shared/help_desk/edit_work_space';
import switchWorkSpace from '../../shared/help_desk/switch_work_space';
import addDocument from '../../shared/help_desk/add_document';
import editDocument from '../../shared/help_desk/edit_document';
import deleteDocument from '../../shared/help_desk/delete_document';
import addFAQ from '../../shared/help_desk/add_faq';
import editFAQ from '../../shared/help_desk/edit_faq';
import deleteFAQ from '../../shared/help_desk/delete_faq';
import addCannedResponse from '../../shared/help_desk/add_canned_response';
import editCannedResponse from '../../shared/help_desk/edit_canned_response';
import deleteCannedResponse from '../../shared/help_desk/delete_canned_response';
import addKnowledgeBase from '../../shared/help_desk/add_knowledge_base';
import editKnowledgeBase from '../../shared/help_desk/edit_knowledge_base';
import deleteKnowledgeBase from '../../shared/help_desk/delete_knowledge_base';
import openPortalTicket from '../../shared/help_desk/open_portal_ticket';
import viewCount from '../../shared/assets/dashboard_asset_count';
import addCustomForms from '../../shared/company_settings/add_custom_forms';
import indexPageAction from '../../shared/help_desk/ticket_index_page_action';
import checkSettings from '../../shared/help_desk/help_desk_setting';
import ticketFilters from '../../shared/help_desk/index_filters';
import MergeTickets from '../../shared/help_desk/merge_tickets';
import searchTicket from '../../shared/help_desk/search_ticket';
import markCommentResolution from '../../shared/help_desk/mark_comment_resolution';
import moveTicket from '../../shared/help_desk/move_ticket';
import quickView from '../../shared/help_desk/quick_view';
import incomingEmail from '../../shared/help_desk/incoming_email';
import attachmentComment from '../../shared/help_desk/attachment_comment';
import filtersMenu from '../../shared/help_desk/filters_menu';
import addAsset from '../../shared/assets/add_assets';
import sortingTickets from '../../shared/help_desk/sorting_ticket';
import bulkUpdate from '../../shared/help_desk/buk_update';
import viewCategory from '../../shared/company_settings/view_category';
import taskScheduler from '../../shared/help_desk/task_scheduler';
import dashboardOperation from '../../shared/help_desk/helpdesk_dashboard';
import slaPolicies from '../../shared/help_desk/sla_policy_test_case';
import ticketFeedBack from '../../shared/help_desk/feedback_usecases';
import timeSpent from '../../shared/help_desk/time_spent';
import cloneTicketing from '../../shared/help_desk/clone_ticket_ui';
import ticketLogs from '../../shared/help_desk/ticket_logs';

const logIntoApp = new signinpage();
const golVari = new golvar();
const naviBar = new navbar();
const companyVari = new companyvar();
const editTic = new editTickets();
const addNewTicket = new addTicket();
const delTicket = new deleteTicket();
const deskVar = new helpDeskVar();
const AddNewSpace = new addWorkSpace();
const automateTask = new automatedTask();
const delWorkSpace = new deletedWorkSpace();
const editSpace = new editWorkSpace();
const switchNewSpace = new switchWorkSpace();
const addNewDoc = new addDocument();
const editDoc = new editDocument();
const delDoc = new deleteDocument();
const addNewFaq = new addFAQ();
const editNewFaq = new editFAQ();
const delFaq = new deleteFAQ();
const addNewResp = new addCannedResponse();
const editResp = new editCannedResponse();
const delResp = new deleteCannedResponse();
const addArticle = new addKnowledgeBase();
const editArticle = new editKnowledgeBase();
const delArticle = new deleteKnowledgeBase();
const ticketPortal = new openPortalTicket();
const checkTicCount = new viewCount();
const addNewForm = new addCustomForms();
const indexPage = new indexPageAction();
const settingOperations = new checkSettings();
const filters = new ticketFilters();
const mergeTic = new MergeTickets();
const searchingTicket = new searchTicket();
const closeOnComment = new markCommentResolution();
const switchTicketLocation = new moveTicket();
const quickViewFilters = new quickView();
const incomingEmails = new incomingEmail();
const attachmentComments = new attachmentComment();
const filtersHelpdesk = new filtersMenu();
const addAstPage = new addAsset();
const sortOperation = new sortingTickets();
const scheduleTaskOperation = new taskScheduler();
const dashboardHDOperation = new dashboardOperation();
const bulkOperations = new bulkUpdate();
const checkCategory = new viewCategory();
const slaOperation = new slaPolicies();
const provideTicketOpinion = new ticketFeedBack();
const tmSpent = new timeSpent();
const cloneOperation = new cloneTicketing();
const helpLogsCase = new ticketLogs();

const workSpaceName = deskVar.workSpaceName;
const editWorkName = deskVar.editworkName;
const documentName = deskVar.documentName;
const documentName1 = deskVar.documentName1;
const editDocName = deskVar.editDocName;
const ticketSubject = `Zoho tech ${nanoid(2)}`;
const editSubject = `Edit ticket ${nanoid(2)}`;
const ticketSubject2 = `Blocker ticket ${faker.lorem.word(5)}`;
const ticketSubject3 = `Open ticket ${nanoid(1)}`;
const ticketSubject4 = `Ticket portal ${nanoid(2)}`;
const ticketSubject5 = `Need to Fix ${nanoid(2)}`;
const ticketSubject6 = `As a helper ${nanoid(2)}`;
const ticketSubject7 = `As a admin ${nanoid(2)}`;
const ticketSubject8 = `As a staff ${nanoid(2)}`;
const ticketSubject9 = `Implement ${nanoid(2)}`;
const ticketSubject10 = `Merging ticket ${nanoid(1)}`;
const ticketSubject11 = `People ${nanoid(1)}`;
const ticketSubject12 = `Stuck ${nanoid(1)}`;
const ticketSubject13 = `Low Tag ${nanoid(1)}`;
const ticketSubject14 = `Default automated ${nanoid(1)}`;
const ticketSubject15 = `bulk updation${nanoid(1)}`;
const ticketSubject16 = `bulk operation${nanoid(2)}`;
const ticketSubject17 = `move ticket${nanoid(2)}`;
const ticketSubject18 = `move ticket form${nanoid(2)}`;
const ticketSubject19 = `close resolution${nanoid(2)}`;
const ticketSubject20 = `Check good survey ${nanoid(2)}`;
const ticketSubject21 = `Check bad survey ${nanoid(2)}`;
const ticketSubject22 = `Check time entry ${nanoid(2)}`;
const ticketSubject23 = `Add ticket ${nanoid(2)}`;
const newQuestion = `question${nanoid(1)}?`;
const newAnswer = `Answer${nanoid(2)}`;
const editQuestion = `editquestion${nanoid(1)}?`;
const editAnswer = `EditAnswer${nanoid(2)}`;
const responsetitle = deskVar.newTitle;
const descriptionSpace = deskVar.notes;
const editDescriptionSpace = deskVar.editNotes;
const editResponseTitle = deskVar.editTitle;
const category = 'Marketing';
const prefixValue = deskVar.ticPrefix;
const inputCustIndex = deskVar.inputRandomIndex;
const incrementCustIndex = deskVar.viewRandomIndex;
const custStatus1 = deskVar.status1;
const custStatus2 = deskVar.status2;
const custStatus3 = deskVar.status3;
const custPriority1 = deskVar.prority1;
const custPriority2 = deskVar.prority2;
const custPriority3 = deskVar.prority3;
const custFormName = companyVari.ticketFormName;
const formName = custFormName;
const filtersName = 'Asset:Contract:Vendor:Telecom:Location:People:Created by';
const filtersName1 = 'Assigned to:Priority:Status:Assignment:Timeframe:Source:Workspace';
const filtersOptions = `${golVari.assetName1}:${golVari.contract}:${golVari.vendor}:${golVari.telecomService}:${golVari.locName1}:${golVari.groupName1}:${golVari.fullName}`;
const filtersOptions1 = `${golVari.fullName1}:${deskVar.meduimPriority}:${deskVar.inProgressStatus}:${deskVar.assignedText}:${deskVar.currentMonthText}:${deskVar.manuallyAddedText}:${editWorkName}`;
const quickFilterName = `Quick filter${nanoid(2)}`;
const quickEditFilterName = `Edit filter${nanoid(2)}`;
const newCategoryName = `Categories ${nanoid(2)}`;
const articleName1 = deskVar.articleTitle;
const articleName2 = deskVar.articleTitle1;
const editArticleName = deskVar.editArticleTitle;
const taskName = `This task need to be ${deskVar.scheduleTaskName}`;
const editTaskName = `The task is edit now so ${deskVar.scheduleTaskName}`;
const taskDescription = `Testing ${nanoid(10)}`;
const editTaskDescription = `Edit task ${nanoid(10)}`;

fixture('Smoke test for help desk module');

test('CRUD operation for help desk and its sub operations', async t => {
  logIntoApp.siginCompanySelect(golVari.emailaddress, golVari.password, golVari.companyname);
  await t.wait(2000);
  await t.hover(addAstPage.companyViewToggle);
  await t.wait(1000);
  await t.click(addAstPage.companyViewToggle);
  await t.wait(2000);
  automateTask.addAutomatedTasks(deskVar.eventName, deskVar.triggerDetail1, deskVar.ticketCreatedTrigger, deskVar.actionAlert, deskVar.recipientAllAgents, deskVar.addTextAlert, deskVar.defaultWorkSpace);
  await t.wait(1000);
  addNewForm.addTicketCustomform(formName, companyVari.option, companyVari.optionName, custStatus1, custStatus2, custStatus3, custPriority1, custPriority2, custPriority3);
  await t.wait(1000);
  // // filters.viewStatProirFilters(custStatus1, custStatus2, custStatus3, custPriority1, custPriority2, custPriority3)  Revamp of filters
  // // await t.wait(1000);
  // settingOperations.ticCustomIndex(prefixValue, inputCustIndex)
  // await t.wait(1000);
  addNewTicket.addTicketwithBaseForm(ticketSubject, deskVar.highPriority, deskVar.inProgressStatus, golVari.groupName1, golVari.groupName2, golVari.assetName1, golVari.locName1, deskVar.notes);
  await t.wait(1000);
  // addNewTicket.viewTicIndex(prefixValue, incrementCustIndex)
  // await t.wait(1000);
  addNewTicket.addTicketwithBaseForm(ticketSubject6, deskVar.meduimPriority, deskVar.openStatus, golVari.groupName1, golVari.groupName2, golVari.assetName1, golVari.locName1, deskVar.notes);
  await t.wait(1000);
  addNewTicket.addTicketwithBaseForm(ticketSubject7, deskVar.lowPriority, deskVar.openStatus, golVari.groupName1, golVari.groupName2, golVari.assetName1, golVari.locName1, deskVar.notes);
  await t.wait(1000);
  addNewTicket.addTicketwithBaseForm(ticketSubject8, deskVar.highPriority, deskVar.inProgressStatus, golVari.groupName1, golVari.groupName2, golVari.assetName1, golVari.locName2, deskVar.notes);
  await t.wait(1000);
  addNewTicket.addTicketwithBaseForm(ticketSubject9, deskVar.lowPriority, deskVar.closeStatus, golVari.groupName1, golVari.groupName2, golVari.assetName1, golVari.locName1, deskVar.notes);
  await t.wait(1000);
  addNewTicket.addTicketwithBaseForm(ticketSubject10, deskVar.meduimPriority, deskVar.openStatus, golVari.groupName1, golVari.groupName2, golVari.assetName1, golVari.locName2, deskVar.notes);
  await t.wait(1000);
  addNewTicket.addTicThroCustomForm(formName, ticketSubject2, 'Help Desk Agents', golVari.telecomService, golVari.contract, deskVar.highPriority, golVari.locName1, golVari.vendor, golVari.assetName1, deskVar.inProgressStatus, deskVar.everyOneUser, deskVar.adminUser, companyVari.textArea, companyVari.richText, companyVari.tag1, companyVari.date1, companyVari.phoneNumber1, companyVari.list1, companyVari.text1, companyVari.number1, category, companyVari.notes);
  await t.wait(1000);
  addNewTicket.addTicThroCustomForm(formName, ticketSubject11, 'Help Desk Agents', golVari.telecomService, golVari.contract, custPriority3, golVari.locName1, golVari.vendor, golVari.assetName1, custStatus1, deskVar.everyOneUser, deskVar.adminUser, companyVari.textArea, companyVari.richText, companyVari.tag1, companyVari.date1, companyVari.phoneNumber1, companyVari.list1, companyVari.text1, companyVari.number1, category, companyVari.notes);
  await t.wait(1000);
  addNewTicket.addTicThroCustomForm(formName, ticketSubject12, 'Help Desk Agents', golVari.telecomService, golVari.contract, custPriority1, golVari.locName1, golVari.vendor, golVari.assetName1, custStatus2, deskVar.everyOneUser, deskVar.adminUser, companyVari.textArea, companyVari.richText, companyVari.tag1, companyVari.date1, companyVari.phoneNumber1, companyVari.list1, companyVari.text1, companyVari.number1, category, companyVari.notes);
  await t.wait(1000);
  addNewTicket.addTicThroCustomForm(formName, ticketSubject13, 'Help Desk Agents', golVari.telecomService, golVari.contract, custPriority2, golVari.locName1, golVari.vendor, golVari.assetName1, custStatus3, deskVar.everyOneUser, deskVar.adminUser, companyVari.textArea, companyVari.richText, companyVari.tag1, companyVari.date1, companyVari.phoneNumber1, companyVari.list1, companyVari.text1, companyVari.number1, category, companyVari.notes);
  await t.wait(1000);
  helpLogsCase.navigateToTicketLogs();
  await t.wait(500);
  helpLogsCase.createdTicketLogs(ticketSubject);
  await t.wait(500);
  helpLogsCase.createdTicketLogs(ticketSubject6);
  await t.wait(500);
  helpLogsCase.createdTicketLogs(ticketSubject7);
  await t.wait(500);
  helpLogsCase.createdTicketLogs(ticketSubject8);
  await t.wait(500);
  helpLogsCase.createdTicketLogs(ticketSubject9);
  await t.wait(500);
  helpLogsCase.createdTicketLogs(ticketSubject10);
  await t.wait(500);
  helpLogsCase.createdTicketLogs(ticketSubject2);
  await t.wait(500);
  helpLogsCase.createdTicketLogs(ticketSubject11);
  await t.wait(500);
  helpLogsCase.createdTicketLogs(ticketSubject12);
  await t.wait(500);
  helpLogsCase.createdTicketLogs(ticketSubject13);
  await t.wait(500);
  helpLogsCase.deskModuleLogs(golVari.fullName, 'Custom Form', formName, `created by ${golVari.fullName}`);
  await t.wait(500);
  indexPage.viewHDList(); // Sorting check in list view
  await t.wait(1000);
  sortOperation.setItemsInAscendingOrder('Ticket Number');
  await t.wait(1000);
  sortOperation.viewSortedNumberListView('0', '1');
  await t.wait(1000);
  sortOperation.viewSortedNumberListView('1', '2');
  await t.wait(1000);
  sortOperation.viewSortedNumberListView('2', '3');
  await t.wait(1000);
  sortOperation.viewSortedNumberListView('3', '4');
  await t.wait(1000);
  sortOperation.viewSortedNumberListView('4', '6');
  await t.wait(1000);
  sortOperation.viewSortedNumberListView('5', '7');
  await t.wait(1000);
  sortOperation.viewSortedNumberListView('6', '8');
  await t.wait(1000);
  sortOperation.viewSortedNumberListView('7', '9');
  await t.wait(1000);
  sortOperation.viewSortedNumberListView('8', '0');
  await t.wait(1000);
  sortOperation.setItemsInDescendingOrder('Ticket Number');
  await t.wait(1000);
  sortOperation.viewSortedNumberListView('0', '10');
  await t.wait(1000);
  sortOperation.viewSortedNumberListView('1', '9');
  await t.wait(1000);
  sortOperation.viewSortedNumberListView('2', '8');
  await t.wait(1000);
  sortOperation.viewSortedNumberListView('3', '7');
  await t.wait(1000);
  sortOperation.viewSortedNumberListView('4', '6');
  await t.wait(1000);
  sortOperation.viewSortedNumberListView('5', '4');
  await t.wait(1000);
  sortOperation.viewSortedNumberListView('6', '3');
  await t.wait(1000);
  sortOperation.viewSortedNumberListView('7', '2');
  await t.wait(1000);
  sortOperation.viewSortedNumberListView('8', '1');
  await t.wait(1000);
  sortOperation.setItemsInAscendingOrder('Assigned To');
  await t.wait(1000);
  sortOperation.viewSortedUserListView('0', '1', deskVar.adminUser);
  await t.wait(1000);
  sortOperation.viewSortedUserListView('1', '1', deskVar.adminUser);
  await t.wait(1000);
  sortOperation.viewSortedUserListView('2', '1', deskVar.adminUser);
  await t.wait(1000);
  sortOperation.viewSortedUserListView('3', '1', deskVar.adminUser);
  await t.wait(1000);
  sortOperation.viewSortedUserListView('4', '1', golVari.groupName2);
  await t.wait(1000);
  sortOperation.viewSortedUserListView('5', '1', golVari.groupName2);
  await t.wait(1000);
  sortOperation.viewSortedUserListView('6', '1', golVari.groupName2);
  await t.wait(1000);
  sortOperation.viewSortedUserListView('7', '1', golVari.groupName2);
  await t.wait(1000);
  sortOperation.viewSortedUserListView('8', '1', golVari.groupName2);
  await t.wait(1000);
  sortOperation.setItemsInDescendingOrder('Assigned To');
  await t.wait(1000);
  sortOperation.viewSortedUserListView('8', '1', deskVar.adminUser);
  await t.wait(1000);
  sortOperation.viewSortedUserListView('7', '1', deskVar.adminUser);
  await t.wait(1000);
  sortOperation.viewSortedUserListView('6', '1', deskVar.adminUser);
  await t.wait(1000);
  sortOperation.viewSortedUserListView('5', '1', deskVar.adminUser);
  await t.wait(1000);
  sortOperation.viewSortedUserListView('4', '1', golVari.groupName2);
  await t.wait(1000);
  sortOperation.viewSortedUserListView('3', '1', golVari.groupName2);
  await t.wait(1000);
  sortOperation.viewSortedUserListView('2', '1', golVari.groupName2);
  await t.wait(1000);
  sortOperation.viewSortedUserListView('1', '1', golVari.groupName2);
  await t.wait(1000);
  sortOperation.viewSortedUserListView('0', '1', golVari.groupName2);
  await t.wait(1000);
  // sortOperation.setItemsInAscendingOrder('Created By');need to revamp it
  // await t.wait(1000);
  // // sortOperation.viewSortedUserListView('8', '2', `${golVari.fullName}, ${golVari.groupName1}`);
  // // await t.wait(1000);
  // // sortOperation.viewSortedUserListView('7', '2', `${golVari.fullName}, ${golVari.groupName1}`);
  // // await t.wait(1000);
  // // sortOperation.viewSortedUserListView('6', '2', `${golVari.fullName}, ${golVari.groupName1}`);
  // // await t.wait(1000);
  // // sortOperation.viewSortedUserListView('5', '2', `${golVari.fullName}, ${golVari.groupName1}`);
  // // await t.wait(1000);
  // // sortOperation.viewSortedUserListView('4', '2', `${golVari.fullName}, ${golVari.groupName1}`);
  // // await t.wait(1000);
  // // sortOperation.viewSortedUserListView('3', '2', `${golVari.fullName}, ${deskVar.everyOneUser}`);
  // // await t.wait(1000);
  // // sortOperation.viewSortedUserListView('2', '2', `${golVari.fullName}, ${deskVar.everyOneUser}`);
  // // await t.wait(1000);
  // // sortOperation.viewSortedUserListView('1', '2', `${golVari.fullName}, ${deskVar.everyOneUser}`);
  // // await t.wait(1000);
  // // sortOperation.viewSortedUserListView('0', '2', `${golVari.fullName}, ${deskVar.everyOneUser}`);
  // // await t.wait(1000);
  // sortOperation.setItemsInDescendingOrder('Created By');
  // await t.wait(1000);
  // // sortOperation.viewSortedUserListView('0', '2', `${golVari.fullName}, ${golVari.groupName1}`); need to revamp it
  // // await t.wait(1000);
  // // sortOperation.viewSortedUserListView('1', '2', `${golVari.fullName}, ${golVari.groupName1}`);
  // // await t.wait(1000);
  // // sortOperation.viewSortedUserListView('2', '2', `${golVari.fullName}, ${golVari.groupName1}`);
  // // await t.wait(1000);
  // // sortOperation.viewSortedUserListView('3', '2', `${golVari.fullName}, ${golVari.groupName1}`);
  // // await t.wait(1000);
  // // sortOperation.viewSortedUserListView('4', '2', `${golVari.fullName}, ${golVari.groupName1}`);
  // // await t.wait(1000);
  // // sortOperation.viewSortedUserListView('5', '2', `${golVari.fullName}, ${deskVar.everyOneUser}`);
  // // await t.wait(1000);
  // // sortOperation.viewSortedUserListView('6', '2', `${golVari.fullName}, ${deskVar.everyOneUser}`);
  // // await t.wait(1000);
  // // sortOperation.viewSortedUserListView('7', '2', `${golVari.fullName}, ${deskVar.everyOneUser}`);
  // // await t.wait(1000);
  // // sortOperation.viewSortedUserListView('8', '2', `${golVari.fullName}, ${deskVar.everyOneUser}`);
  // // await t.wait(1000);
  sortOperation.setItemsInAscendingOrder('Subject');
  await t.wait(1000);
  sortOperation.viewSortedSubjectView('0', '5', ticketSubject7);
  await t.wait(1000);
  sortOperation.viewSortedSubjectView('1', '5', ticketSubject6);
  await t.wait(1000);
  sortOperation.viewSortedSubjectView('2', '5', ticketSubject8);
  await t.wait(1000);
  sortOperation.viewSortedSubjectView('3', '5', ticketSubject2);
  await t.wait(1000);
  sortOperation.viewSortedSubjectView('4', '5', ticketSubject13);
  await t.wait(1000);
  sortOperation.viewSortedSubjectView('5', '5', ticketSubject10);
  await t.wait(1000);
  sortOperation.viewSortedSubjectView('6', '5', ticketSubject11);
  await t.wait(1000);
  sortOperation.viewSortedSubjectView('7', '5', ticketSubject12);
  await t.wait(1000);
  sortOperation.viewSortedSubjectView('8', '5', ticketSubject);
  await t.wait(1000);
  sortOperation.setItemsInDescendingOrder('Subject');
  await t.wait(1000);
  sortOperation.viewSortedSubjectView('8', '5', ticketSubject7);
  await t.wait(1000);
  sortOperation.viewSortedSubjectView('7', '5', ticketSubject6);
  await t.wait(1000);
  sortOperation.viewSortedSubjectView('6', '5', ticketSubject8);
  await t.wait(1000);
  sortOperation.viewSortedSubjectView('5', '5', ticketSubject2);
  await t.wait(1000);
  sortOperation.viewSortedSubjectView('4', '5', ticketSubject13);
  await t.wait(1000);
  sortOperation.viewSortedSubjectView('3', '5', ticketSubject10);
  await t.wait(1000);
  sortOperation.viewSortedSubjectView('2', '5', ticketSubject11);
  await t.wait(1000);
  sortOperation.viewSortedSubjectView('1', '5', ticketSubject12);
  await t.wait(1000);
  sortOperation.viewSortedSubjectView('0', '5', ticketSubject);
  await t.wait(1000);
  sortOperation.setItemsInAscendingOrder('Status');
  await t.wait(1000);
  sortOperation.viewSortedStatusView('0', '3', deskVar.openStatus);
  await t.wait(1000);
  sortOperation.viewSortedStatusView('1', '3', deskVar.openStatus);
  await t.wait(1000);
  sortOperation.viewSortedStatusView('2', '3', deskVar.openStatus);
  await t.wait(1000);
  sortOperation.viewSortedStatusView('3', '3', deskVar.inProgressStatus);
  await t.wait(1000);
  sortOperation.viewSortedStatusView('4', '3', deskVar.inProgressStatus);
  await t.wait(1000);
  sortOperation.viewSortedStatusView('5', '3', deskVar.inProgressStatus);
  await t.wait(1000);
  // sortOperation.viewSortedStatusView('6', '3', custStatus3)
  // await t.wait(1000);
  // sortOperation.viewSortedStatusView('7', '3', custStatus2)
  // await t.wait(1000);
  // sortOperation.viewSortedStatusView('8', '3', custStatus1)
  // await t.wait(1000);
  sortOperation.setItemsInDescendingOrder('Status');
  await t.wait(1000);
  sortOperation.viewSortedStatusView('8', '3', deskVar.openStatus);
  await t.wait(1000);
  sortOperation.viewSortedStatusView('7', '3', deskVar.openStatus);
  await t.wait(1000);
  sortOperation.viewSortedStatusView('6', '3', deskVar.openStatus);
  await t.wait(1000);
  sortOperation.viewSortedStatusView('5', '3', deskVar.inProgressStatus);
  await t.wait(1000);
  sortOperation.viewSortedStatusView('4', '3', deskVar.inProgressStatus);
  await t.wait(1000);
  sortOperation.viewSortedStatusView('3', '3', deskVar.inProgressStatus);
  await t.wait(1000);
  // sortOperation.viewSortedStatusView('2', '3', custStatus3)
  // await t.wait(1000);
  // sortOperation.viewSortedStatusView('1', '3', custStatus2)
  // await t.wait(1000);
  // sortOperation.viewSortedStatusView('0', '3', custStatus1)
  // await t.wait(1000);
  // sortOperation.setItemsInAscendingOrder('Priority')
  // await t.wait(1000);
  // sortOperation.viewSortedPriortyView('0', '4', deskVar.lowPriority)
  // await t.wait(1000);
  // sortOperation.viewSortedPriortyView('1', '4', deskVar.meduimPriority)
  // await t.wait(1000);
  // sortOperation.viewSortedPriortyView('2', '4', deskVar.meduimPriority)
  // await t.wait(1000);
  // sortOperation.viewSortedPriortyView('3', '4', deskVar.highPriority)
  // await t.wait(1000);
  // sortOperation.viewSortedPriortyView('4', '4', deskVar.highPriority)
  // await t.wait(1000);
  // sortOperation.viewSortedPriortyView('5', '4', deskVar.highPriority)
  // await t.wait(1000);
  // sortOperation.viewSortedPriortyView('6', '4', custPriority1)
  // await t.wait(1000);
  // sortOperation.viewSortedPriortyView('7', '4', custPriority2)
  // await t.wait(1000);
  // sortOperation.viewSortedPriortyView('8', '4', custPriority3)
  // await t.wait(1000);
  // sortOperation.setItemsInDescendingOrder('Priority')
  // await t.wait(1000);
  // sortOperation.viewSortedPriortyView('0', '4', deskVar.lowPriority)
  // await t.wait(1000);
  // sortOperation.viewSortedPriortyView('1', '4', deskVar.meduimPriority)
  // await t.wait(1000);
  // sortOperation.viewSortedPriortyView('2', '4', deskVar.meduimPriority)
  // await t.wait(1000);
  // sortOperation.viewSortedPriortyView('3', '4', deskVar.highPriority)
  // await t.wait(1000);
  // sortOperation.viewSortedPriortyView('4', '4', deskVar.highPriority)
  // await t.wait(1000);
  // sortOperation.viewSortedPriortyView('5', '4', deskVar.highPriority)
  // await t.wait(1000);
  // sortOperation.viewSortedPriortyView('6', '4', custPriority1)
  // await t.wait(1000);
  // sortOperation.viewSortedPriortyView('7', '4', custPriority2)
  // await t.wait(1000);
  // sortOperation.viewSortedPriortyView('8', '4', custPriority3)
  // await t.wait(1000);
  indexPage.viewHDGrid(); // Sorting check in grid view
  await t.wait(1000);
  sortOperation.setItemsInAscendingOrder('Ticket Number');
  await t.wait(1000);
  sortOperation.viewSortedItems('0', ticketSubject);
  await t.wait(1000);
  sortOperation.viewSortedItems('1', ticketSubject6);
  await t.wait(1000);
  sortOperation.viewSortedItems('2', ticketSubject7);
  await t.wait(1000);
  sortOperation.viewSortedItems('3', ticketSubject8);
  await t.wait(1000);
  sortOperation.viewSortedItems('4', ticketSubject10);
  await t.wait(1000);
  sortOperation.viewSortedItems('5', ticketSubject2);
  await t.wait(1000);
  sortOperation.viewSortedItems('6', ticketSubject11);
  await t.wait(1000);
  sortOperation.viewSortedItems('7', ticketSubject12);
  await t.wait(1000);
  sortOperation.viewSortedItems('8', ticketSubject13);
  await t.wait(1000);
  sortOperation.setItemsInDescendingOrder('Ticket Number');
  await t.wait(1000);
  sortOperation.viewSortedItems('0', ticketSubject13);
  await t.wait(1000);
  sortOperation.viewSortedItems('1', ticketSubject12);
  await t.wait(1000);
  sortOperation.viewSortedItems('2', ticketSubject11);
  await t.wait(1000);
  sortOperation.viewSortedItems('3', ticketSubject2);
  await t.wait(1000);
  sortOperation.viewSortedItems('4', ticketSubject10);
  await t.wait(1000);
  sortOperation.viewSortedItems('5', ticketSubject8);
  await t.wait(1000);
  sortOperation.viewSortedItems('6', ticketSubject7);
  await t.wait(1000);
  sortOperation.viewSortedItems('7', ticketSubject6);
  await t.wait(1000);
  sortOperation.viewSortedItems('8', ticketSubject);
  await t.wait(1000);
  sortOperation.setItemsInAscendingOrder('Subject');
  await t.wait(1000);
  sortOperation.viewSortedItems('0', ticketSubject7);
  await t.wait(1000);
  sortOperation.viewSortedItems('1', ticketSubject6);
  await t.wait(1000);
  sortOperation.viewSortedItems('2', ticketSubject8);
  await t.wait(1000);
  sortOperation.viewSortedItems('3', ticketSubject2);
  await t.wait(1000);
  sortOperation.viewSortedItems('4', ticketSubject13);
  await t.wait(1000);
  sortOperation.viewSortedItems('5', ticketSubject10);
  await t.wait(1000);
  sortOperation.viewSortedItems('6', ticketSubject11);
  await t.wait(1000);
  sortOperation.viewSortedItems('7', ticketSubject12);
  await t.wait(1000);
  sortOperation.viewSortedItems('8', ticketSubject);
  await t.wait(1000);
  sortOperation.setItemsInDescendingOrder('Subject');
  await t.wait(1000);
  sortOperation.viewSortedItems('8', ticketSubject7);
  await t.wait(1000);
  sortOperation.viewSortedItems('7', ticketSubject6);
  await t.wait(1000);
  sortOperation.viewSortedItems('6', ticketSubject8);
  await t.wait(1000);
  sortOperation.viewSortedItems('5', ticketSubject2);
  await t.wait(1000);
  sortOperation.viewSortedItems('4', ticketSubject13);
  await t.wait(1000);
  sortOperation.viewSortedItems('3', ticketSubject10);
  await t.wait(1000);
  sortOperation.viewSortedItems('2', ticketSubject11);
  await t.wait(1000);
  sortOperation.viewSortedItems('1', ticketSubject12);
  await t.wait(1000);
  sortOperation.viewSortedItems('0', ticketSubject);
  await t.wait(1000);
  sortOperation.setItemsInAscendingOrder('Assigned To');
  await t.wait(1000);
  sortOperation.viewSortedItems('0', ticketSubject2);
  await t.wait(1000);
  sortOperation.viewSortedItems('1', ticketSubject11);
  await t.wait(1000);
  sortOperation.viewSortedItems('2', ticketSubject12);
  await t.wait(1000);
  sortOperation.viewSortedItems('3', ticketSubject13);
  await t.wait(1000);
  sortOperation.viewSortedItems('4', ticketSubject);
  await t.wait(1000);
  sortOperation.viewSortedItems('5', ticketSubject10);
  await t.wait(1000);
  sortOperation.viewSortedItems('6', ticketSubject6);
  await t.wait(1000);
  sortOperation.viewSortedItems('7', ticketSubject7);
  await t.wait(1000);
  sortOperation.viewSortedItems('8', ticketSubject8);
  await t.wait(1000);
  sortOperation.setItemsInDescendingOrder('Assigned To');
  await t.wait(1000);
  sortOperation.viewSortedItems('8', ticketSubject13);
  await t.wait(1000);
  sortOperation.viewSortedItems('7', ticketSubject12);
  await t.wait(1000);
  sortOperation.viewSortedItems('6', ticketSubject11);
  await t.wait(1000);
  sortOperation.viewSortedItems('5', ticketSubject2);
  await t.wait(1000);
  sortOperation.viewSortedItems('4', ticketSubject10);
  await t.wait(1000);
  sortOperation.viewSortedItems('3', ticketSubject8);
  await t.wait(1000);
  sortOperation.viewSortedItems('2', ticketSubject7);
  await t.wait(1000);
  sortOperation.viewSortedItems('1', ticketSubject6);
  await t.wait(1000);
  sortOperation.viewSortedItems('0', ticketSubject);
  await t.wait(1000);
  // sortOperation.setItemsInAscendingOrder('Created By'); need to revamp it
  // await t.wait(1000);
  // sortOperation.viewSortedItems('0', ticketSubject2);
  // await t.wait(1000);
  // sortOperation.viewSortedItems('1', ticketSubject11);
  // await t.wait(1000);
  // sortOperation.viewSortedItems('2', ticketSubject12);
  // await t.wait(1000);
  // sortOperation.viewSortedItems('3', ticketSubject13);
  // await t.wait(1000);
  // sortOperation.viewSortedItems('4', ticketSubject);
  // await t.wait(1000);
  // sortOperation.viewSortedItems('5', ticketSubject10);
  // await t.wait(1000);
  // sortOperation.viewSortedItems('6', ticketSubject6);
  // await t.wait(1000);
  // sortOperation.viewSortedItems('7', ticketSubject7);
  // await t.wait(1000);
  // sortOperation.viewSortedItems('8', ticketSubject8);
  // await t.wait(1000);
  // sortOperation.setItemsInDescendingOrder('Created By');
  // await t.wait(1000);
  // sortOperation.viewSortedItems('0', ticketSubject);
  // await t.wait(1000);
  // sortOperation.viewSortedItems('1', ticketSubject6);
  // await t.wait(1000);
  // sortOperation.viewSortedItems('2', ticketSubject7);
  // await t.wait(1000);
  // sortOperation.viewSortedItems('3', ticketSubject8);
  // await t.wait(1000);
  // sortOperation.viewSortedItems('4', ticketSubject10);
  // await t.wait(1000);
  // sortOperation.viewSortedItems('5', ticketSubject2);
  // await t.wait(1000);
  // sortOperation.viewSortedItems('6', ticketSubject11);
  // await t.wait(1000);
  // sortOperation.viewSortedItems('7', ticketSubject12);
  // await t.wait(1000);
  // sortOperation.viewSortedItems('8', ticketSubject13);
  // await t.wait(1000);
  sortOperation.setItemsInAscendingOrder('Priority');
  await t.wait(1000);
  sortOperation.viewSortedItems('0', ticketSubject7);
  await t.wait(1000);
  sortOperation.viewSortedItems('1', ticketSubject6);
  await t.wait(1000);
  sortOperation.viewSortedItems('2', ticketSubject10);
  await t.wait(1000);
  sortOperation.viewSortedItems('3', ticketSubject);
  await t.wait(1000);
  sortOperation.viewSortedItems('4', ticketSubject8);
  await t.wait(1000);
  sortOperation.viewSortedItems('5', ticketSubject2);
  await t.wait(1000);
  sortOperation.viewSortedItems('6', ticketSubject12);
  await t.wait(1000);
  sortOperation.viewSortedItems('7', ticketSubject13);
  await t.wait(1000);
  sortOperation.viewSortedItems('8', ticketSubject11);
  await t.wait(1000);
  sortOperation.setItemsInDescendingOrder('Priority');
  await t.wait(1000);
  sortOperation.viewSortedItems('8', ticketSubject7);
  await t.wait(1000);
  sortOperation.viewSortedItems('7', ticketSubject6);
  await t.wait(1000);
  sortOperation.viewSortedItems('6', ticketSubject10);
  await t.wait(1000);
  sortOperation.viewSortedItems('5', ticketSubject);
  await t.wait(1000);
  sortOperation.viewSortedItems('4', ticketSubject8);
  await t.wait(1000);
  sortOperation.viewSortedItems('3', ticketSubject2);
  await t.wait(1000);
  sortOperation.viewSortedItems('2', ticketSubject12);
  await t.wait(1000);
  sortOperation.viewSortedItems('1', ticketSubject13);
  await t.wait(1000);
  sortOperation.viewSortedItems('0', ticketSubject11);
  await t.wait(1000);
  sortOperation.setItemsInAscendingOrder('Status');
  await t.wait(1000);
  sortOperation.viewSortedItems('0', ticketSubject6);
  await t.wait(1000);
  sortOperation.viewSortedItems('1', ticketSubject10);
  await t.wait(1000);
  sortOperation.viewSortedItems('2', ticketSubject7);
  await t.wait(1000);
  sortOperation.viewSortedItems('3', ticketSubject);
  await t.wait(1000);
  sortOperation.viewSortedItems('4', ticketSubject8);
  await t.wait(1000);
  sortOperation.viewSortedItems('5', ticketSubject2);
  await t.wait(1000);
  filters.removeFilter('Active');
  await t.wait(1000);
  filters.applyFilter('Open', 'Status', 'with label');
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10);
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject6);
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject7);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject8);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject2);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject11);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject12);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject13);
  await t.wait(1000);
  filters.removeFilter('Open');
  await t.wait(1000);
  filters.applyFilter('In Progress', 'Status', 'with label');
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject2);
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject);
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject6);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject10);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject11);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject12);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject13);
  await t.wait(1000);
  filters.removeFilter('In Progress');
  await t.wait(1000);
  filters.applyFilter('Closed', 'Status', 'with label');
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject2);
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject9);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject6);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject8);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject10);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject11);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject12);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject13);
  await t.wait(1000);
  filters.removeFilter('Closed');
  await t.wait(1000);
  filters.applyFilter(custStatus1, 'Status', 'with label');
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject6);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject2);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject8);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject10);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject12);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject13);
  await t.wait(1000);
  filters.removeFilter(custStatus1);
  await t.wait(1000);
  filters.applyFilter(custStatus2, 'Status', 'with label');
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject12);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject2);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject6);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject8);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject10);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject11);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject13);
  await t.wait(1000);
  filters.removeFilter(custStatus2);
  await t.wait(1000);
  filters.applyFilter(custStatus3, 'Status', 'with label');
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject13);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject2);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject8);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject6);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject10);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject11);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject12);
  await t.wait(1000);
  filters.removeFilter(custStatus3);
  await t.wait(1000);
  filters.applyFilter(custPriority1, 'Priority', 'with label');
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject12);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject2);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject8);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject10);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject11);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject13);
  await t.wait(1000);
  filters.removeFilter(custPriority1);
  await t.wait(1000);
  filters.applyFilter(custPriority2, 'Priority', 'with label');
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject13);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject2);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject6);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject8);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject10);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject11);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject12);
  await t.wait(1000);
  filters.removeFilter(custPriority2);
  await t.wait(1000);
  filters.applyFilter(custPriority3, 'Priority', 'with label');
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject2);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject8);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject6);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject10);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject12);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject13);
  await t.wait(1000);
  filters.removeFilter(custPriority3);
  await t.wait(1000);
  filters.applyFilter(deskVar.lowPriority, 'Priority', 'with label');
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject2);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject6);
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject7);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject8);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject10);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject11);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject12);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject13);
  await t.wait(1000);
  filters.removeFilter(deskVar.lowPriority);
  await t.wait(1000);
  filters.applyFilter(deskVar.meduimPriority, 'Priority', 'with label');
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject2);
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10);
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject6);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject8);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject11);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject12);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject13);
  await t.wait(1000);
  filters.removeFilter(deskVar.meduimPriority);
  await t.wait(1000);
  filters.applyFilter('Active', 'Status', 'with label');
  await t.wait(1000);
  mergeTic.ticketMerges(ticketSubject8, ticketSubject10);
  await t.wait(1000);
  checkTicCount.viewTicketCounter('8');
  await t.wait(1000);
  checkTicCount.viewCounterDashboard('8', 'tickets');
  await t.wait(1000);
  dashboardHDOperation.navigateToDashboard();
  await t.wait(500);
  dashboardHDOperation.checkTicketByStatus('8:9', '2:3:1', `${custStatus1}:${custStatus2}:${custStatus3}`, '1:1:1');
  await t.wait(1000);
  dashboardHDOperation.checkTicketBySource('8:9');
  await t.wait(500);
  dashboardHDOperation.checkTicketByPriority('4:5', '2:1:1', '2:1:2');
  await t.wait(1000);
  indexPage.updateTicketThroughGridView(ticketSubject6, deskVar.inProgressStatus, deskVar.lowPriority);
  await t.wait(1000);
  filters.removeFilter(deskVar.lowPriority);
  await t.wait(1000);
  editTic.editTicketswithBaseForm(ticketSubject, golVari.groupName1, golVari.groupName2, golVari.assetName1, golVari.locName1, '', editSubject, deskVar.meduimPriority, deskVar.openStatus, deskVar.everyOneUser, deskVar.adminUser, golVari.assetName1, golVari.locName2, '', deskVar.comments, deskVar.notes, deskVar.highPriority, deskVar.dueDate, deskVar.completedDate, deskVar.helpDeskAgent, deskVar.timeSpent, deskVar.date, deskVar.notes);
  await t.wait(1000);
  automateTask.deleteAutomatedTask(deskVar.eventName);
  await t.wait(1000);
  delTicket.deletingTickets(editSubject);
  await t.wait(1000);
  checkTicCount.viewTicketCounter('7');
  await t.wait(1000);
  checkTicCount.viewCounterDashboard('7', 'tickets');
  await t.wait(1000);
  addNewTicket.addTicketWithStatus(ticketSubject20, deskVar.closeStatus);
  await t.wait(1000);
  addNewTicket.addTicketWithStatus(ticketSubject21, deskVar.closeStatus);
  await t.wait(1000);
  addNewTicket.addTicketWithStatus(ticketSubject22, deskVar.inProgressStatus);
  await t.wait(1000);
  const getParentIndex = await indexPage.getTicketIndex(ticketSubject22); //get the ticket index for cloning
  await t.wait(500);
  addNewForm.enableTimeRequiredNSurvey('IT General Request'); //enable it from forms
  await t.wait(1000);
  tmSpent.addTimeEntryModal(ticketSubject22, deskVar.closeFilter, '03:00', '07:07', '4 hrs 7 mins', deskVar.date, deskVar.notes);
  await t.wait(1000);
  filters.clearAllFilters('filter by status');
  await t.wait(1000);
  filters.applyFilter('Closed', 'Status', 'with label');
  await t.wait(1000);
  provideTicketOpinion.provideTicFeedBack(ticketSubject20, 'great', 'dfsdfsdfsdfsdfsdfsdf');
  await t.wait(1000);
  provideTicketOpinion.provideTicFeedBack(ticketSubject21, 'not good', 'ccccfsdfsdf');
  await t.wait(1000);
  cloneOperation.cloneTicket(ticketSubject22, deskVar.closeFilter, deskVar.lowPriority, golVari.fullName, getParentIndex);
  await t.wait(1000);
  filters.removeFilter('Closed');
  await t.wait(1000);
  addNewForm.enableTimeRequiredNSurvey('IT General Request'); // disable it from forms
  await t.wait(1000);
  dashboardHDOperation.checkCustomerSatisfaction('1', '1', '50');
  await t.wait(1000);
  dashboardHDOperation.addTicketFromDashboard(ticketSubject23, deskVar.highPriority, deskVar.inProgressStatus, golVari.fullName, `1:${deskVar.notes}`)
  await t.wait(500);
  quickViewFilters.viewQuickViewFilters(deskVar.myActiveFilter, deskVar.allActiveFilter, deskVar.unassignedFilter, deskVar.closeFilter);
  await t.wait(1000);
  quickViewFilters.applyQuickViewFilters();
  await t.wait(1000);
  quickViewFilters.createQuickViewFilters(quickFilterName, filtersName, filtersOptions);
  await t.wait(1000);
  quickViewFilters.deleteQuickViewFilters(quickFilterName);
  await t.wait(1000);
  closeOnComment.commentResolution(ticketSubject19, deskVar.notes, deskVar.closeStatus);
  await t.wait(2000);
  naviBar.signout();
  await t.wait(1000);
});

test('Resources,SLA Policy,task scheduler and index page operation in help desk', async t => {
  logIntoApp.siginCompanySelect(golVari.emailaddress, golVari.password, golVari.companyname);
  await t.wait(2000);
  await t.hover(addAstPage.companyViewToggle);
  await t.wait(1000);
  await t.click(addAstPage.companyViewToggle);
  await t.wait(2000);
  addArticle.navigateToArticles();
  await t.wait(1000);
  addNewDoc.addDocuments(documentName);
  await t.wait(1000);
  addArticle.addArticlesWithAddedvalues(articleName1, deskVar.articleTags, deskVar.selectCategory, documentName, deskVar.articleText, '1 month', golVari.companyname, golVari.locName1);
  await t.wait(1000);
  addArticle.addArticlesWithCustomvalues(articleName2, deskVar.articleTags1, newCategoryName, documentName, deskVar.articleText1, '3 months', golVari.companyname, 'Read', golVari.groupName1, golVari.locName2);
  await t.wait(1000);
  checkCategory.viewHDCategories(newCategoryName); //check the added category
  await t.wait(2000);
  editArticle.editArticles(articleName1, editArticleName, deskVar.editArticleTags, deskVar.selectCategory1, documentName, deskVar.editArticleText, 'custom', golVari.companyname, '03-04-2025', golVari.locName2);
  await t.wait(1000);
  delArticle.deleteArtilces(editArticleName);
  await t.wait(1000);
  delArticle.deleteArtilces(articleName2);
  await t.wait(1000);
  editDoc.editDocuments(documentName, editDocName);
  await t.wait(1000);
  delDoc.deleteDocuments(editDocName);
  await t.wait(1000);
  addNewFaq.addFAQs(newQuestion, newAnswer, deskVar.selectCategory);
  await t.wait(1000);
  editNewFaq.editFAQs(newQuestion, editQuestion, editAnswer, deskVar.selectCategory1);
  await t.wait(1000);
  delFaq.deleteFAQs(editQuestion, 'yes');
  await t.wait(1000);
  addNewResp.addCannedResponses(responsetitle, deskVar.text);
  await t.wait(1000);
  editResp.editCannedResponses(responsetitle, editResponseTitle, deskVar.text);
  await t.wait(1000);
  delResp.deleteCannedResponses(editResponseTitle);
  await t.wait(1000);
  scheduleTaskOperation.addTaskSchedule(taskName, taskDescription, golVari.fullName, '2027-09-11', '2027-09-19', '03:32', '09:55');
  await t.wait(1000);
  scheduleTaskOperation.editTaskSchedule(taskName, golVari.fullName, editTaskName, editTaskDescription, golVari.fullName1, '2028-11-21', '2028-12-09', '06:00', '10:00');
  await t.wait(1000);
  scheduleTaskOperation.deleteTaskSchedule(editTaskName);
  await t.wait(1000);
  // // slaOperation.navigateToSLA();
  // // await t.wait(500);
  // // slaOperation.addNewPolicy(deskVar.policyName, deskVar.defaultForm, deskVar.notes,
  // //   deskVar.specificStaff, golVari.fullName, 'Low:3;4;12:3;4;20:Calendar Hours',
  // //   'Medium:3;6;54:3;7;20:Calendar Hours', 'High:1;4;10:1;4;40:Calendar Hours', deskVar.firstResponseTarget,
  // //   '12:05', deskVar.resolutionTarget, golVari.fullName1, deskVar.emailSubjectReminder, deskVar.emailSubjectEscalate,
  // //   deskVar.reminderEmailBody, deskVar.escalateEmailBody);
  // // await t.wait(500);
  // // slaOperation.editPolicy(deskVar.policyName, deskVar.editPolicyName, deskVar.defaultForm, deskVar.notes,
  // //   deskVar.specificStaff, golVari.fullName1, 'Low:1;3;12:1;4;20:Business Hours',
  // //   'Medium:1;4;54:1;5;20:Business Hours', 'High:1;3;10:1;6;40:Business Hours', deskVar.resolutionTarget,
  // //   '1:00', deskVar.firstResponseTarget, golVari.fullName, deskVar.editEmailSubjectReminder, deskVar.editEmailSubjectEscalate,
  // //   deskVar.editReminderEmailBody, deskVar.editEmailSubjectEscalate);
  // // await t.wait(500);
  // // slaOperation.deletePolicy(deskVar.editPolicyName);
  // // await t.wait(500);
  // // slaOperation.addNewPolicyWithCustomForm(deskVar.policyName1, formName, deskVar.notes,
  // //   deskVar.specificStaff, golVari.fullName, 'Low:3;4;12:3;4;20:Calendar Hours',
  // //   'Medium:3;6;54:3;7;20:Calendar Hours', 'High:1;4;10:1;4;40:Calendar Hours',
  // //   `${custPriority1}:2;5;10:2;3;40:Calendar Hours`, `${custPriority2}:3;1;10:1;2;40:Calendar Hours`,
  // //   `${custPriority3}:3;1;10:3;1;40:Calendar Hours`, deskVar.firstResponseTarget, '10:15',
  // //   deskVar.resolutionTarget, golVari.fullName1, deskVar.emailSubjectReminder, deskVar.emailSubjectEscalate,
  // //   deskVar.reminderEmailBody, deskVar.escalateEmailBody);
  // // await t.wait(500);
  // // slaOperation.deletePolicy(deskVar.policyName1);
  // // await t.wait(500);
  addNewTicket.addTicketwithBaseForm(ticketSubject5, deskVar.highPriority, deskVar.inProgressStatus, golVari.groupName1, golVari.fullName, golVari.assetName1, golVari.locName1, deskVar.notes);
  await t.wait(1000);
  filtersHelpdesk.viewCommonFilters(deskVar.commonFilterHeaderText, deskVar.ticketsThisMonthText, deskVar.almostDueText, deskVar.recentlyClosedText, deskVar.createdTodayText, deskVar.completedThisWeekText, deskVar.completedThisMonthText, deskVar.closedByDateText, deskVar.createdByMeText, deskVar.assignedToMeText);
  await t.wait(1000);
  filtersHelpdesk.viewPriorityFilters(deskVar.filterByPriorityHeaderText, deskVar.lowText, deskVar.mediumText, deskVar.highText);
  await t.wait(1000);
  filtersHelpdesk.viewStatusFilters(deskVar.filterByStatusHeaderText, deskVar.activeText, deskVar.openText, deskVar.closedText, deskVar.unreadText, deskVar.archivedText, deskVar.inProgressText);
  await t.wait(1000);
  filtersHelpdesk.viewAssignmentFilters(deskVar.filterByAssignmentHeaderText, deskVar.assignedText, deskVar.unAssignedText);
  await t.wait(1000);
  filtersHelpdesk.viewFilterByTimeFrame(deskVar.filterByTimeFrameHeaderText, deskVar.currentMonthText, deskVar.lastMonthText, deskVar.lastSixMonthText, deskVar.lastTwelveMonthText, deskVar.calendarYearText, deskVar.fiscalYearText, deskVar.lastTwoYearsText, deskVar.customDateText);
  await t.wait(1000);
  filtersHelpdesk.viewFilterBySource(deskVar.filterBySourceHeaderText, deskVar.manuallyAddedText, deskVar.emailText, deskVar.slackText, deskVar.autoGeneratedText, deskVar.microSoftTeamsText, deskVar.splitText, deskVar.mobileAppText);
  await t.wait(1000);
  filtersHelpdesk.viewFilterByForm(deskVar.filterByFormText, deskVar.itGeneralText);
  await t.wait(1000);
  filtersHelpdesk.applyCommonFilters(deskVar.ticketsThisMonthText, deskVar.almostDueText, deskVar.recentlyClosedText, deskVar.createdTodayText, deskVar.completedThisWeekText, deskVar.completedThisMonthText);
  await t.wait(1000);
  filtersHelpdesk.applyPriorityFilters(deskVar.appliedLow, deskVar.appliedMedium, deskVar.appliedHigh);
  await t.wait(1000);
  filtersHelpdesk.applyStatusFilters(deskVar.activeStatusPill, deskVar.appliedOpen, deskVar.closedStatusPill, deskVar.appliedUnread, deskVar.appliedArchived, deskVar.appliedInProgress);
  await t.wait(1000);
  filtersHelpdesk.applyAssignmentFilters(deskVar.appliedAssigned, deskVar.appliedUnAssigned);
  await t.wait(1000);
  filtersHelpdesk.applyTimeFrameFilters(deskVar.currentMonthText, deskVar.lastMonthText, deskVar.lastSixMonthText, deskVar.lastTwelveMonthText, deskVar.calendarYearText, deskVar.fiscalYearText, deskVar.lastTwoYearsText);
  await t.wait(1000);
  filtersHelpdesk.applySourceFilters(deskVar.appliedManual, deskVar.appliedEmail, deskVar.appliedSlack, deskVar.appliedAutoGenerated, deskVar.appliedTeams, deskVar.appliedSplit, deskVar.appliedApp);
  await t.wait(1000);
  filtersHelpdesk.applyFormFilters(deskVar.appliedIT);
  await t.wait(1000);
  attachmentComments.addCommentForAttachment(ticketSubject16, deskVar.attachmentAddedText);
  await t.wait(2000);
  incomingEmails.noIncomingEmail(deskVar.noIncomingText);
  await t.wait(2000);
  switchTicketLocation.moveTicketForm(ticketSubject18, formName);
  await t.wait(1000);
  settingOperations.generalSettings();
  await t.wait(1000);
  naviBar.signout();
  await t.wait(1000);
});

test.skip('Default automated task triggered and open portal', async t => {
  logIntoApp.siginCompanySelect(golVari.emailaddress, golVari.password, golVari.companyname);
  await t.wait(2000);
  await t.hover(addAstPage.companyViewToggle);
  await t.wait(1000);
  await t.click(addAstPage.companyViewToggle);
  await t.wait(2000);
  addNewTicket.addTicketwithBaseForm(ticketSubject14, deskVar.meduimPriority, deskVar.openStatus, golVari.groupName1, golVari.groupName2, golVari.assetName1, golVari.locName1, deskVar.notes);
  await t.wait(1000);
  automateTask.viewDefaultAutomatedTask(); // this testcases need to run first before checking automated task
  await t.wait(1000);
  const getTaskId1 = await automateTask.assignedToChangesTask();
  await t.wait(1000);
  const getTaskId2 = await automateTask.notifyPriorityChangesTask();
  await t.wait(1000);
  const getTaskId3 = await automateTask.notifyStatusChangesTask();
  await t.wait(1000);
  const getTaskId4 = await automateTask.notifyTicketCreatedTask();
  await t.wait(1000);
  const getTaskId5 = await automateTask.notifyAttachmentAddedTask();
  await t.wait(1000);
  addNewTicket.navigateToTicShowPage(ticketSubject14);
  await t.wait(1000);
  automateTask.triggerChangePriority(ticketSubject14, deskVar.highPriority);
  await t.wait(1000);
  automateTask.triggerChangeStatus(ticketSubject14, deskVar.inProgressStatus);
  await t.wait(1000);
  automateTask.triggerAttachmentAdded(ticketSubject14);
  await t.wait(1000);
  automateTask.triggerAssignedToChanges(ticketSubject14, golVari.groupName1);
  await t.wait(1000);
  addNewTicket.navigateToTicShowPage(ticketSubject14);
  await t.wait(1000);
  automateTask.verifyTicketCreationHistory(getTaskId4);
  await t.wait(1000);
  automateTask.verifyProrityChangesHistory(getTaskId2);
  await t.wait(1000);
  automateTask.verifyStatusChangeHistory(getTaskId3);
  await t.wait(1000);
  automateTask.verifyAttachmentAddedHistory(getTaskId5);
  await t.wait(1000);
  automateTask.verifyAssigneeChangedHistory(getTaskId1);
  await t.wait(1000);
  delTicket.deletingTickets(ticketSubject14);
  await t.wait(1000);
  filters.removeFilter('Archived');
  await t.wait(1000);
  settingOperations.enableClassicPortal();
  await t.wait(1000);
  ticketPortal.portalClassicWithlogin(ticketSubject3, deskVar.highPriority, deskVar.inProgressStatus, golVari.groupName1, deskVar.notes, deskVar.defaultForm);
  await t.wait(1000);
  ticketPortal.portalClassicWithoutLogin(golVari.emailaddress, golVari.password, ticketSubject4, deskVar.highPriority, deskVar.inProgressStatus, golVari.guestEmail, deskVar.notes, deskVar.defaultForm);
  await t.wait(1000);
  settingOperations.enableModernPortal();
  await t.wait(1000);
  naviBar.signout();
  await t.wait(1000);
});

test('Smoke test cases of HD workspaces and its sub operations', async t => {
  logIntoApp.siginCompanySelect(golVari.emailaddress, golVari.password, golVari.companyname);
  await t.wait(2000);
  await t.hover(addAstPage.companyViewToggle);
  await t.wait(1000);
  await t.click(addAstPage.companyViewToggle);
  await t.wait(2000);
  AddNewSpace.addWorkSpaces(workSpaceName, descriptionSpace, golVari.groupName1, deskVar.userPerm1, deskVar.userPerm2, deskVar.defaultForm);
  await t.wait(1000);
  switchNewSpace.switchWorkSpaces(workSpaceName);
  await t.wait(1000);
  automateTask.addAutomatedTasks(deskVar.eventName, deskVar.triggerDetail1, deskVar.ticketCreatedTrigger, deskVar.actionAlert, deskVar.recipientAllAgents, deskVar.addTextAlert, workSpaceName);
  await t.wait(1000);
  addNewTicket.addTicketwithBaseForm(ticketSubject5, deskVar.highPriority, deskVar.inProgressStatus, golVari.groupName1, golVari.groupName2, golVari.assetName1, golVari.locName1, deskVar.notes);
  await t.wait(1000);
  checkTicCount.viewTicketCounter('1');
  await t.wait(1000);
  automateTask.deleteAutomatedTask(deskVar.eventName);
  await t.wait(1000);
  indexPage.updateTicketThroughListView(ticketSubject5, golVari.groupName2, deskVar.inProgressStatus, deskVar.meduimPriority, golVari.fullName1);
  await t.wait(1000);
  addNewTicket.addTicketwithSubjectOnly(ticketSubject15);
  await t.wait(1000);
  addNewTicket.addTicketwithSubjectOnly(ticketSubject14);
  await t.wait(2000);
  addNewResp.addCannedResponses(responsetitle, deskVar.text);
  await t.wait(1000);
  bulkOperations.bulkStatus(ticketSubject15, ticketSubject14, deskVar.inProgressStatus, deskVar.inProgressStatus);
  await t.wait(2000);
  bulkOperations.bulkCannedResponse(ticketSubject15, ticketSubject14, responsetitle, ticketSubject15);
  await t.wait(2000);
  bulkOperations.bulkArchive(ticketSubject15, ticketSubject14);
  await t.wait(2000);
  bulkOperations.bulkAssignment(ticketSubject15, ticketSubject14, golVari.groupName1);
  await t.wait(2000);
  bulkOperations.deleteBulk(ticketSubject15, ticketSubject14);
  await t.wait(2000);
  switchNewSpace.switchWorkSpaces(deskVar.defaultWorkSpace);
  await t.wait(1000);
  editSpace.editWorkSpaces(workSpaceName, editWorkName, editDescriptionSpace, golVari.groupName2, deskVar.userPerm2, deskVar.userPerm1, formName);
  await t.wait(1000);
  switchNewSpace.switchWorkSpaces(editWorkName);
  await t.wait(1000);
  settingOperations.enableClassicPortal();
  await t.wait(1000);
  ticketPortal.createTicketWithlogin(ticketSubject3, deskVar.highPriority, deskVar.inProgressStatus, golVari.groupName1, deskVar.notes, editWorkName, deskVar.defaultForm);
  await t.wait(1000);
  ticketPortal.createTicketWithoutLogin(golVari.emailaddress, golVari.password, ticketSubject4, deskVar.highPriority, deskVar.inProgressStatus, golVari.guestEmail, deskVar.notes, editWorkName, deskVar.defaultForm);
  await t.wait(1000);
  settingOperations.enableModernPortal();
  await t.wait(1000);
  switchNewSpace.switchWorkSpaces(editWorkName);
  await t.wait(1000);
  switchTicketLocation.moveTicketWorkspace(ticketSubject17, deskVar.defaultWorkSpace, deskVar.defaultForm);
  await t.wait(1000);
  addNewTicket.addTicThroCustomForm(formName, 'testing custom forms', golVari.fullName1, golVari.telecomService, golVari.contract, custPriority2, golVari.locName1, golVari.vendor, golVari.assetName1, custStatus3, deskVar.everyOneUser, deskVar.adminUser, companyVari.textArea, companyVari.richText, companyVari.tag1, companyVari.date1, companyVari.phoneNumber1, companyVari.list1, companyVari.text1, companyVari.number1, category, companyVari.notes);
  await t.wait(1000);
  // slaOperation.navigateToSLA();
  // await t.wait(500);
  // slaOperation.addNewPolicy(deskVar.policyName, deskVar.defaultForm, deskVar.notes,
  //   deskVar.specificStaff, golVari.fullName, 'Low:3;4;12:3;4;20:Calendar Hours',
  //   'Medium:3;6;54:3;7;20:Calendar Hours', 'High:1;4;10:1;4;40:Calendar Hours', deskVar.firstResponseTarget,
  //   '12:05', deskVar.resolutionTarget, golVari.fullName1, deskVar.emailSubjectReminder, deskVar.emailSubjectEscalate,
  //   deskVar.reminderEmailBody, deskVar.escalateEmailBody);
  // await t.wait(500);
  // slaOperation.editPolicy(deskVar.policyName, deskVar.editPolicyName, deskVar.defaultForm, deskVar.notes,
  //   deskVar.specificStaff, golVari.fullName1, 'Low:1;3;12:1;4;20:Business Hours',
  //   'Medium:1;4;54:1;5;20:Business Hours', 'High:1;3;10:1;6;40:Business Hours', deskVar.resolutionTarget,
  //   '1:00', deskVar.firstResponseTarget, golVari.fullName, deskVar.editEmailSubjectReminder, deskVar.editEmailSubjectEscalate,
  //   deskVar.editReminderEmailBody, deskVar.editEmailSubjectEscalate);
  // await t.wait(500);
  // slaOperation.deletePolicy(deskVar.editPolicyName);
  // await t.wait(500);
  // slaOperation.addNewPolicyWithCustomForm(deskVar.policyName1, formName, deskVar.notes,
  //   deskVar.specificStaff, golVari.fullName, 'Low:3;4;12:3;4;20:Calendar Hours',
  //   'Medium:3;6;54:3;7;20:Calendar Hours', 'High:1;4;10:1;4;40:Calendar Hours',
  //   `${custPriority1}:2;5;10:2;3;40:Calendar Hours`, `${custPriority2}:3;1;10:1;2;40:Calendar Hours`,
  //   `${custPriority3}:3;1;10:3;1;40:Calendar Hours`, deskVar.firstResponseTarget, '10:15',
  //   deskVar.resolutionTarget, golVari.fullName1, deskVar.emailSubjectReminder, deskVar.emailSubjectEscalate,
  //   deskVar.reminderEmailBody, deskVar.escalateEmailBody);
  // await t.wait(500);
  // slaOperation.deletePolicy(deskVar.policyName1);
  // await t.wait(500);
  addNewTicket.addTicketwithBaseForm(ticketSubject5, deskVar.highPriority, deskVar.inProgressStatus, golVari.groupName1, golVari.fullName, golVari.assetName1, golVari.locName1, deskVar.notes);
  await t.wait(1000);
  filtersHelpdesk.viewWorkspaceAndFieldFilters(deskVar.filterByFieldHeaderText, deskVar.filterHelpdesk);
  await t.wait(1000);
  filtersHelpdesk.viewCommonFilters(deskVar.commonFilterHeaderText, deskVar.ticketsThisMonthText, deskVar.almostDueText, deskVar.recentlyClosedText, deskVar.createdTodayText, deskVar.completedThisWeekText, deskVar.completedThisMonthText, deskVar.closedByDateText, deskVar.createdByMeText, deskVar.assignedToMeText);
  await t.wait(1000);
  filtersHelpdesk.viewPriorityFilters(deskVar.filterByPriorityHeaderText, deskVar.lowText, deskVar.mediumText, deskVar.highText);
  await t.wait(1000);
  filtersHelpdesk.viewStatusFilters(deskVar.filterByStatusHeaderText, deskVar.activeText, deskVar.openText, deskVar.closedText, deskVar.unreadText, deskVar.archivedText, deskVar.inProgressText);
  await t.wait(1000);
  filtersHelpdesk.viewAssignmentFilters(deskVar.filterByAssignmentHeaderText, deskVar.assignedText, deskVar.unAssignedText);
  await t.wait(1000);
  filtersHelpdesk.viewFilterByTimeFrame(deskVar.filterByTimeFrameHeaderText, deskVar.currentMonthText, deskVar.lastMonthText, deskVar.lastSixMonthText, deskVar.lastTwelveMonthText, deskVar.calendarYearText, deskVar.fiscalYearText, deskVar.lastTwoYearsText, deskVar.customDateText);
  await t.wait(1000);
  filtersHelpdesk.viewFilterBySource(deskVar.filterBySourceHeaderText, deskVar.manuallyAddedText, deskVar.emailText, deskVar.slackText, deskVar.autoGeneratedText, deskVar.microSoftTeamsText, deskVar.splitText, deskVar.mobileAppText);
  await t.wait(1000);
  filtersHelpdesk.viewFilterByForm(deskVar.filterByFormText, deskVar.itGeneralText);
  await t.wait(1000);
  filtersHelpdesk.applyCommonFilters(deskVar.ticketsThisMonthText, deskVar.almostDueText, deskVar.recentlyClosedText, deskVar.createdTodayText, deskVar.completedThisWeekText, deskVar.completedThisMonthText);
  await t.wait(1000);
  filtersHelpdesk.applyPriorityFilters(deskVar.appliedLow, deskVar.appliedMedium, deskVar.appliedHigh);
  await t.wait(1000);
  filtersHelpdesk.applyStatusFilters(deskVar.activeStatusPill, deskVar.appliedOpen, deskVar.closedStatusPill, deskVar.appliedUnread, deskVar.appliedArchived, deskVar.appliedInProgress);
  await t.wait(1000);
  filtersHelpdesk.applyAssignmentFilters(deskVar.appliedAssigned, deskVar.appliedUnAssigned);
  await t.wait(1000);
  filtersHelpdesk.applyTimeFrameFilters(deskVar.currentMonthText, deskVar.lastMonthText, deskVar.lastSixMonthText, deskVar.lastTwelveMonthText, deskVar.calendarYearText, deskVar.fiscalYearText, deskVar.lastTwoYearsText);
  await t.wait(1000);
  filtersHelpdesk.applySourceFilters(deskVar.appliedManual, deskVar.appliedEmail, deskVar.appliedSlack, deskVar.appliedAutoGenerated, deskVar.appliedTeams, deskVar.appliedSplit, deskVar.appliedApp);
  await t.wait(1000);
  filtersHelpdesk.applyFormFilters(deskVar.appliedIT);
  await t.wait(1000);
  attachmentComments.addCommentForAttachment(ticketSubject16, deskVar.attachmentAddedText);
  await t.wait(2000);
  incomingEmails.noIncomingEmail(deskVar.noIncomingText);
  await t.wait(2000);
  quickViewFilters.viewQuickViewFilters(deskVar.myActiveFilter, deskVar.allActiveFilter, deskVar.unassignedFilter, deskVar.closeFilter);
  await t.wait(1000);
  quickViewFilters.applyQuickViewFilters();
  await t.wait(1000);
  quickViewFilters.createQuickViewFilters(quickFilterName, filtersName, filtersOptions);
  await t.wait(1000);
  quickViewFilters.editQuickViewFilters(quickFilterName, quickEditFilterName, filtersName1, filtersOptions1);
  await t.wait(1000);
  quickViewFilters.deleteQuickViewFilters(quickEditFilterName);
  await t.wait(1000);
  switchTicketLocation.moveTicketForm(ticketSubject18, formName);
  await t.wait(1000);
  closeOnComment.commentResolution(ticketSubject19, deskVar.notes, deskVar.closeStatus);
  await t.wait(2000);
  switchNewSpace.switchWorkSpaces(deskVar.defaultWorkSpace);
  await t.wait(1000);
  delWorkSpace.deleteWorkSpaces(editWorkName);
  await t.wait(1000);
  naviBar.signout();
  await t.wait(1000);
});
