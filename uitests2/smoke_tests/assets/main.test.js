import addAssetType from "../../shared/assets/add_asset_type";
import signinpage from "../../shared/signin/signinpage";
import assetVar from "../../shared/assets/asset_variables";
import golvar from "../../shared/Globalvariable";
import navbar from '../../shared/Components/navbar';
import addDepreciation from "../../shared/assets/add_depreciations";
import editDepreciation from "../../shared/assets/edit_depreciations";
import deleteDepreciation from "../../shared/assets/delete_depreciation";
import removeAssetsType from "../../shared/assets/remove_Assets_type";
import assetListColumn from "../../shared/assets/asset_list_view_column";
import addAsset from "../../shared/assets/add_assets";
import deleteAsset from "../../shared/assets/delete_assets";
import assetSoftware from "../../shared/assets/assets_software";
import editAsset from "../../shared/assets/edit_assets";
import viewCount from "../../shared/assets/dashboard_asset_count";
import assetHardware from "../../shared/assets/asset_hardware";
import applyAssetFilters from "../../shared/assets/asset_filter";
import { nanoid } from 'nanoid';
import assetStatus from "../../shared/assets/asset_statuses";
import assetLifeCycle from "../../shared/assets/asset_life_cycles";
import assetIndexPageAction from "../../shared/assets/asset_index_page_action";

const golVari = new golvar();
const naviBar = new navbar();
const addCustType = new addAssetType();
const loginIntoApp = new signinpage();
const assetVari = new assetVar();
const addNewDepr = new addDepreciation();
const editDepr = new editDepreciation();
const delDepr = new deleteDepreciation();
const listColumn = new assetListColumn();
const removeAssetType = new removeAssetsType();
const delAsset = new deleteAsset();
const addManagedAssets = new addAsset();
const editManagedAssets = new editAsset();
const softwareCrud = new assetSoftware();
const checkAstCount = new viewCount();
const hardwareCrud = new assetHardware();
const checkFilter = new applyAssetFilters();
const statusOperation = new assetStatus();
const lifeCycleOperation = new assetLifeCycle();
const indexPageOperation = new assetIndexPageAction();

const sfwName = 'testingSoftware';
const sfwType = 'Application';
const sfwProdNum = '454534535';
const sfwInstallDate = '2021-12-20';
const editSfwName = 'edit Software';
const editSfwType = 'New Software' + nanoid(2);
const editSfwProdNum = '343434343222';
const editSfwInstallDate = '2020-11-08';
const depreName = assetVari.deprName;
const editDepreName = assetVari.editDeprName;
const assetName1 = assetVari.assetsName1;
const editAssetName = assetVari.editAssetsName1;
const assetName2 = assetVari.assetsName2;
const customAssetType = assetVari.assetType1;
const customStatusName = `Status ${assetVari.randomText}`;
const editStatusName = `edit Status ${assetVari.randomText}`;
const lifeCycleName = `Asset Cycle ${nanoid(2)}`;
const lifeCycleName1 = `Deprecaition ${nanoid(2)}`;
const lifeCycleEditName = `Edit Asset Cycle ${nanoid(2)}`;
const assetName3 = `Asset A${nanoid(2)}`;
const assetName4 = `Asset B${nanoid(2)}`;
const assetName5 = `Asset C${nanoid(2)}`;
const assetBulkNames = `${assetName3};${assetName4};${assetName5}`;
const editAssetBulkNames = `Edit AssetA${nanoid(1)};Edit AssetB${nanoid(1)};Edit AssetC${nanoid(1)}`;
const assetSerialNumbers = `${assetVari.assetSerialNumber4};${assetVari.assetSerialNumber5};${assetVari.assetSerialNumber6}`;
const productNumbers = `${assetVari.assetsProductNum2};${assetVari.assetsProductNum1}`;
const ipAddress = '************';
const departments = `departmented${nanoid(2)}`;
const assettags = `Tags${nanoid(2)}`;

fixture('Smoke test for assets module')

test('Assets CRUD', async t => {
  loginIntoApp.siginCompanySelect(golVari.emailaddress, golVari.password, golVari.companyname);
  await t.wait(1000);
  await t.click(addManagedAssets.companyViewToggle);
  await t.wait(1000);
  addManagedAssets.addAssets(assetName1, assetVari.assetsType1, assetVari.assetsTags1, assetVari.assetsNotes1, assetVari.assetsMac1, assetVari.assetsIp1, assetVari.assetsManufacturer1, assetVari.assetsModel1, assetVari.tags1, assetVari.assetsUuid1, assetVari.assetsUpTime1, assetVari.assetsCost1, assetVari.assetsSalvage1, assetVari.assetsDepreType1, assetVari.assetsUsedBy1, assetVari.assetsManagedBy1, golVari.locName1, assetVari.assetsDepartment1, assetVari.assetSerialNumber1, assetVari.assetsProductNum1, '2021-12-08', '2023-11-12', '2021-12-07', assetVari.assetKey1, assetVari.assetsDetials1);
  await t.wait(2000);
  softwareCrud.addSoftware(sfwName, sfwType, sfwProdNum, sfwInstallDate);
  await t.wait(2000);
  listColumn.addViewAssetColumn();
  await t.wait(2000);
  listColumn.viewListAssetData(assetName1, assetVari.assetsType1, 'Manually Added', assetVari.tags1, assetVari.assetsNotes1, golVari.locName1, assetVari.assetsDepartment1, assetVari.assetsModel1, assetVari.assetsMac1, '', assetVari.assetsManufacturer1, assetVari.assetsIp1, assetVari.assetsProductNum1, assetVari.assetSerialNumber1, 'NetworkingDeviceDetail', assetVari.assetsTags1, '', assetVari.assetsUsedBy1, assetVari.assetsManagedBy1, assetVari.assetsCost1);
  await t.wait(2000);
  listColumn.removeViewAllAssetColumn();
  await t.wait(1000);
  editManagedAssets.editAssets(assetName1, editAssetName, assetVari.editAssetsType1, assetVari.editAssetsTags1, assetVari.editAssetsNotes1, assetVari.editAssetsMac1, assetVari.editAssetsIp1, assetVari.editAssetsManufacturer1, assetVari.editAssetsModel1, assetVari.editTags1, assetVari.editAssetsUuid1, assetVari.editAssetsUpTime1, assetVari.editAssetsCost1, assetVari.editAssetsSalvage1, assetVari.editAssetsDepr1, assetVari.editAssetsUsedBy1, assetVari.editAssetsManagedBy1, golVari.locName2, assetVari.editAssetsDepartment1, assetVari.editAssetSerialNumber1, assetVari.editAssetsProductNum1, '2021-12-08', '2023-11-12', '2021-12-07', assetVari.assetKey2, assetVari.editAssetsDetials1);
  await t.wait(2000);
  softwareCrud.editSoftware(sfwName, editSfwName, editSfwType, editSfwProdNum, editSfwInstallDate);
  await t.wait(2000);
  checkAstCount.viewCounts(assetVari.editAssetsType1, '1');
  await t.wait(2000);
  checkAstCount.viewCounterDashboard('2', 'assets');
  await t.wait(1000);
  delAsset.deleteAssets(editAssetName);
  await t.wait(1000);
  checkAstCount.viewCounts(assetVari.editAssetsType1, '0');
  await t.wait(2000);
  checkAstCount.viewCounterDashboard('1', 'assets');
  await t.wait(1000);
  addCustType.addAssetTypes(customAssetType);
  await t.wait(1000);
  addManagedAssets.addAssets(assetName2, customAssetType, assetVari.assetsTags2, assetVari.assetsNotes2, assetVari.assetsMac2, assetVari.assetsIp2, assetVari.assetsManufacturer2, assetVari.assetsModel2, assetVari.tags2, assetVari.assetsUuid2, assetVari.assetsUpTime2, assetVari.assetsCost2, assetVari.assetsSalvage2, depreName, assetVari.assetsUsedBy2, assetVari.assetsManagedBy2, golVari.locName2, assetVari.assetsDepartment2, assetVari.assetSerialNumber2, assetVari.assetsProductNum2, '2021-12-08', '2023-11-12', '2021-12-07', assetVari.assetKey2, assetVari.assetsDetials2);
  await t.wait(2000);
  softwareCrud.addSoftware(sfwName, sfwType, sfwProdNum, sfwInstallDate);
  await t.wait(1000);
  await t.hover(naviBar.assetsbtn);
  await t.wait(1000);
  await t.click(naviBar.assetsbtn);
  await t.wait(1000);
  checkFilter.removeFilter('Active');
  await t.wait(1000);
  checkAstCount.viewCounts(customAssetType, '1');
  await t.wait(2000);
  checkAstCount.viewCounterDashboard('2', 'assets');
  await t.wait(1000);
  lifeCycleOperation.navigateToLifeCycle();
  await t.wait(500);
  lifeCycleOperation.addCustomCycle(lifeCycleName, assetVari.assetsPurchasePrice1, assetVari.assetsreplacementCost1, assetVari.assetsSalvage1, assetVari.randomYears, assetVari.randomMonths);
  await t.wait(500);
  lifeCycleOperation.addCustomCycle(lifeCycleName1, assetVari.assetsPurchasePrice1, assetVari.assetsreplacementCost1, assetVari.assetsSalvage1, assetVari.randomYears, assetVari.randomMonths);
  await t.wait(500);
  lifeCycleOperation.addCustomCycleToAsset(lifeCycleName, assetName2, assetVari.assetsPurchasePrice1, assetVari.assetsreplacementCost1, assetVari.assetsSalvage1, assetVari.randomYears, assetVari.randomMonths);
  await t.wait(500);
  lifeCycleOperation.navigateToLifeCycle();
  await t.wait(500);
  lifeCycleOperation.editCustomCycle(lifeCycleName1, lifeCycleEditName, assetVari.assetsPurchasePrice2, assetVari.assetsreplacementCost2, assetVari.assetsSalvage2, assetVari.randomYears1, assetVari.randomMonths1);
  await t.wait(500);
  // lifeCycleOperation.viewCustomChanges(assetName2, assetVari.assetsPurchasePrice2, assetVari.assetsreplacementCost2, assetVari.assetsSalvage2, assetVari.randomYears1, assetVari.randomMonths1); currently we are not changing the edit values
  // await t.wait(500);
  // lifeCycleOperation.navigateToLifeCycle();
  // await t.wait(500);
  lifeCycleOperation.deleteCustomLifeCycle(lifeCycleEditName);
  await t.wait(500);
  delAsset.deleteAssets(assetName2);
  await t.wait(1000);
  checkAstCount.viewCounts(customAssetType, '0');
  await t.wait(2000);
  removeAssetType.removeCustomTypes(customAssetType);
  await t.wait(2000);
  removeAssetType.removeDefaultTypes(assetVari.defaultTypes1);
  await t.wait(2000);
  addCustType.addDefaultTypes(assetVari.defaultTypes1);
  await t.wait(1000);
  statusOperation.naviToAssetStatus();
  await t.wait(1000);
  statusOperation.addAssetStatus(customStatusName, assetVari.exclamationIcon);
  await t.wait(1000);
  statusOperation.editAssetStatus(customStatusName, editStatusName, assetVari.arrowIcon);
  await t.wait(1000);
  statusOperation.removeAssetStatus(editStatusName);
  await t.wait(1000);
  statusOperation.assetStatusValidate();
  await t.wait(1000);
  addManagedAssets.addAssetWithNameNType(assetName3, assetVari.server);
  await t.wait(1000);
  addManagedAssets.addAssetWithNameNType(assetName4, assetVari.tv);
  await t.wait(1000);
  addManagedAssets.addAssetWithNameNType(assetName5, assetVari.windows);
  await t.wait(1000);
  indexPageOperation.bulkUpdation(assetBulkNames, editAssetBulkNames, assetSerialNumbers, ipAddress, productNumbers, assettags, departments,
    'Huawei', `${golVari.fullName};Admin`, `${golVari.groupName1};${golVari.groupName2}`, assetVari.assetsNotes1, `${assetVari.appleDevice};${assetVari.wap}`);
  await t.wait(2000);
  delAsset.bulk3Deletion(editAssetBulkNames);
  await t.wait(1000);
  naviBar.signout();
  await t.wait(1000);
})
