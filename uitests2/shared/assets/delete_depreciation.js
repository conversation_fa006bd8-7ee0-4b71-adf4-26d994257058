import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import addAssetType from './add_asset_type';
import addDepreciation from './add_depreciations';

const golVari = new golvar();
const naviBar = new navbar();
const addType = new addAssetType();
const addDpr = new addDepreciation();

class deleteDepreciation {

  constructor() {
    this.confirmDelete = Selector('[data-tc-cdelete]');
  }

  async deleteDepreciations(depreName) {
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
      .hover(addType.settingMenu)
      .click(addType.settingMenu)
      .hover(addDpr.depreciationTab)
      .click(addDpr.depreciationTab)
    const listedName = Selector(`[data-tc-listed-dep_name="${depreName}"]`);
    const delBtn = Selector(`[data-tc-delete-depreciations="${depreName}"]`);
    await t.wait(500)
      .hover(listedName)
      .expect(listedName.innerText).contains(depreName)
      .hover(delBtn)
      .click(delBtn)
      .wait(1000)
      .click(this.confirmDelete)
      .wait(500)
      .expect(addDpr.notifyPopUp.innerText).contains('Successfully deleted your depreciation')
      .wait(1000)
  }
} export default deleteDepreciation
