import { Selector, t } from 'testcafe';

class relatedItemOperation {

  constructor() {
    this.relatedItemLabel = Selector('[data-tc-label="Related Items"]');
    this.itemParentData = Selector('[data-tc-items-drop-down]');
    this.dropDownIcon = this.itemParentData.find('.multiselect__select');
    this.inputItem = this.itemParentData.find('input');

  }

  async addRelatedItems(items) {
    const viewItems = Selector(`[data-tc-view-items="${items}"]`);
    await t.wait(1000)
      .expect(this.relatedItemLabel.innerText).contains('Related Items')
      .wait(1000)
      .click(this.dropDownIcon)
      .wait(1000)
      .typeText(this.inputItem, items, { paste: true })
      .wait(3000)
      .pressKey('enter')
      .wait(1000)
      .expect(viewItems.innerText).contains(items)
      .wait(2000)
  }

  async removeRelatedItems(items1) {
    const removeItemsBtn = Selector(`[data-tc-remove-items="${items1}"]`);
    const viewItems = Selector(`[data-tc-view-items="${items1}"]`);
    await t.wait(1000)
      .hover(viewItems)
      .wait(1000)
      .expect(this.relatedItemLabel.innerText).contains('Related Items')
      .wait(1000)
      .click(removeItemsBtn)
      .wait(1000)
      .expect(viewItems.exists).notOk()
      .wait(1000)
  }
} export default relatedItemOperation
