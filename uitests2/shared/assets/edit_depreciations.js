import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import addAssetType from './add_asset_type';
import addDepreciation from './add_depreciations';

const golVari = new golvar();
const naviBar = new navbar();
const addType = new addAssetType();
const addDepr = new addDepreciation();

class editDepreciation {

  constructor() {
    this.updateBtn = Selector('[data-tc-save-form-btn="Update"]');
  }

  async editDepreciations(deprecName, editDeprName, editDeprType, editLifeYear, editdescr) {
    const viewName = Selector(`[data-tc-listed-dep_name="${deprecName}"]`);
    const editIcon = Selector(`[data-tc-edit-depreciation="${deprecName}"]`);
    const editedName = Selector(`[data-tc-listed-dep_name="${editDeprName}"]`);
    const editedYear = Selector(`[data-tc-listed-dep_life="${editLifeYear}"]`);
    const editedDescription = Selector(`[data-tc-listed-dep_descr="${editdescr}"]`);
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
      .hover(addType.settingMenu)
      .click(addType.settingMenu)
      .hover(addDepr.depreciationTab)
      .click(addDepr.depreciationTab)
      .expect(golVari.currentpageurl()).contains('/managed_assets/settings/depreciations')
      .wait(1000)
      .hover(viewName)
      .expect(viewName.innerText).contains(deprecName)
      .wait(1000)
      .hover(editIcon)
      .click(editIcon)
      .wait(1000)
      .hover(addDepr.nameField)
      .typeText(addDepr.nameField, editDeprName, { replace: true })
      .hover(addDepr.selectType.withText(editDeprType))
      .click(addDepr.selectType.withText(editDeprType))
      .wait(1000)
      .hover(addDepr.yearField)
      .typeText(addDepr.yearField, editLifeYear, { replace: true })
      .hover(addDepr.descrField)
      .typeText(addDepr.descrField, editdescr, { replace: true })
      .wait(1000)
      .hover(this.updateBtn)
      .click(this.updateBtn)
      .wait(1000)
      .expect(addDepr.notifyPopUp.innerText).contains('Successfully edited your depreciation')
      .wait(1000)
      .hover(editedName)
      .expect(editedName.innerText).contains(editDeprName)
      .wait(1000)
      .hover(editedYear)
      .expect(editedYear.innerText).contains(editLifeYear)
      .wait(1000)
      .hover(editedDescription)
      .expect(editedDescription.innerText).contains(editdescr)
      .wait(1000)
  }
} export default editDepreciation
