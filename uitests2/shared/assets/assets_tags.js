import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import moment from 'moment-timezone';
import editAsset from './edit_assets';
import deleteAsset from './delete_assets';
import navbar from '../Components/navbar';

const golVari = new golvar();
const naviBar = new navbar();
const delAst = new deleteAsset();

class assetTags {

  constructor() {
    this.addTagBtn = Selector('[data-tc-btn="add tag"]');
    this.viewHeaderTag = Selector('[data-tc-title="tag"] h2');
    this.tagDropDown = Selector('[data-tc-tag-drop-down]');
    this.inputTag = this.tagDropDown.find('input');
    this.closeBtn = Selector('[data-tc-btn="close tag modal"]');
  }

  async addTags(astName, tags) {
    const viewTags = Selector(`[data-tc-view-tags="${tags}"]`);
    await t.hover(delAst.viewsearchedAsset)
      .expect(delAst.viewsearchedAsset.innerText).eql(astName)
      .click(delAst.viewsearchedAsset)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/managed_assets/')
      .wait(1000)
      .click(this.addTagBtn)
      .wait(2000)
      .expect(this.viewHeaderTag.innerText).contains('Add a new tag')
      .wait(1000)
      .click(this.tagDropDown)
      .wait(1000)
      .typeText(this.inputTag, tags, { paste: true })
      .wait(1000)
      .pressKey('enter')
      .wait(1000)
      .click(this.closeBtn)
      .wait(1000)
      .hover(viewTags)
      .expect(viewTags.innerText).eql(tags);
  }

  async removeTags(astName, tags) {
    const viewTag1 = Selector(`[data-tc-view-tags="${tags}"]`);
    const removeTag = Selector(`[data-tc-remove-icon="${tags}"]`);
    await t.hover(delAst.viewsearchedAsset)
      .expect(delAst.viewsearchedAsset.innerText).eql(astName)
      .click(delAst.viewsearchedAsset)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/managed_assets/')
      .wait(1000)
      .expect(viewTag1.innerText).eql(tags)
      .wait(1000)
      .click(removeTag)
      .wait(2000)
      .expect(viewTag1.exists).notOk();
  }
} export default assetTags
