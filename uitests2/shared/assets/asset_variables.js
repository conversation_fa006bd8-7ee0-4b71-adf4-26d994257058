import faker from 'faker';
import { nanoid } from 'nanoid';
import golvar from '../Globalvariable';

const golVari = new golvar();

class assetVar {

  constructor() {
    this.assetType1 = `Routers${faker.company.companySuffix()}`;
    this.assetType2 = `Apple${faker.company.companySuffix()}`;
    this.defaultTypes1 = 'Apple Device';
    this.defaultTypes2 = 'Switch';
    this.assetListColumn1 = 'Model';
    this.deprName = faker.name.lastName();
    this.deprType = 'Straight Line';
    this.yearLife = '10';
    this.description = faker.lorem.paragraphs(1);
    this.editDeprName = faker.name.firstName();
    this.editDeprType = 'Sum Of Years Digits';
    this.editYearLife = '20';
    this.editDescription = faker.lorem.paragraphs(1);
    this.assetsName1 = `Assets ${faker.commerce.product()}`;
    this.assetsType1 = 'Firewall';
    this.assetsTags1 = faker.lorem.word(5);
    this.assetsTags2 = `Testing${ nanoid(2)}`;
    this.assetsTags3 = `Hardware${nanoid(2)}`;
    this.assetsTags4 = `Software${nanoid(2)}`;
    this.assetsTags5 = `QA${nanoid(2)}`;
    this.assetsTags6 = `Laptops${  nanoid(2)}`;
    this.assetsNotes1 = faker.lorem.word(8);
    this.assetsMac1 = faker.internet.mac().toUpperCase();
    this.assetsMac2 = faker.internet.mac().toUpperCase();
    this.assetsMac3 = faker.internet.mac().toUpperCase();
    this.assetsMac4 = faker.internet.mac().toUpperCase();
    this.assetsMac5 = faker.internet.mac().toUpperCase();
    this.assetsMac6 = faker.internet.mac().toUpperCase();
    this.assetsMac7 = faker.internet.mac().toUpperCase();
    this.assetsIp1 = '************';
    this.assetsIp3 = '************';
    this.assetsIp4 = '************';
    this.assetsIp5 = '************';
    this.assetsIp6 = '************';
    this.assetsManufacturer1 = 'Apple';
    this.assetsModel1 = '2015Pa';
    this.tags1 = 'testing assets';
    this.assetsUuid1 = '89bd9d8d-69a6-474e-8f46-7cc8796ed151';
    this.assetsUpTime1 = '22';
    this.assetsCost1 = `${faker.commerce.price()}:${faker.commerce.price()}:4:5`;
    this.assetsSalvage1 = faker.commerce.price();
    this.assetsDepreType1 = 'Straight Line, 5 years';
    this.assetsUsedBy1 = 'Admins';
    this.assetsManagedBy1 = 'Everyone';
    this.assetsDepartment1 = faker.commerce.department();
    this.assetsProductNum1 = 'UYdqZrTqIEJuENl';
    this.assetsType5 = 'Dongle';
    this.assetSerialNumber1 = golVari.randomImei;
    this.assetKey1 = `Keys${generateRandomTags(2)}`;
    this.assetsDetials1 = 'This is only for testing purposes';
    this.assetsName2 = `Assets ${faker.commerce.product()}`;
    this.assetTags2 = faker.lorem.word(5);
    this.assetTags3 = `tags${nanoid(2).toLowerCase()}`;
    this.assetsNotes2 = faker.lorem.word(8);
    this.assetsIp2 = '************';
    this.assetsManufacturer2 = 'Custom';
    this.assetsModel2 = '2016AQ';
    this.tags2 = 'qa assets';
    this.assetsUuid2 = '01b02a36-8204-11ed-a1eb-0242ac120002';
    this.assetsUpTime2 = '44';
    this.assetsCost2 = `${faker.commerce.price()}:${faker.commerce.price()}:6:11`;;
    this.assetsSalvage2 = faker.commerce.price();
    this.assetsUsedBy2 = 'Everyone';
    this.assetsManagedBy2 = 'Admins';
    this.assetsDepartment2 = faker.commerce.department();
    this.assetsProductNum2 = 'CTE7005GY4';
    this.assetSerialNumber1 = faker.internet.mac();
    this.assetSerialNumber3 = faker.internet.mac();
    this.processor = `Gen${nanoid(2)}`;
    this.memory = `${nanoid(2)} GB`;
    this.hardDrive = `${nanoid(1)} GB`;
    this.port = '3';
    this.imei = '323223245242452';
    this.imei1 = '306458822539053';
    this.imei2 = '352911272379738';
    this.imei3 = '526890278245971';
    this.imei4 = '494556064283538';
    this.imei5 = '916026538903491';
    this.imei6 = '449463755841235';
    this.connectionInterface = 'SATA';
    this.assetSerialNumber2 = golVari.randomImei;
    this.assetKey2 = `Key${generateRandomTags(2)}`;
    this.assetsDetials2 = 'An asset is a resource with economic value';
    this.editAssetsName1 = `Edit ${faker.commerce.product()}`;
    this.editAssetsType1 = 'Desktop';
    this.editAssetsTags1 = faker.lorem.word(5);
    this.editAssetsNotes1 = faker.lorem.word(8);
    this.editAssetsMac1 = faker.internet.mac();
    this.editAssetsIp1 = '************';
    this.editAssetsManufacturer1 = 'Customizer';
    this.editAssetsDepr1 = 'Declining Balance, 5 years';
    this.editAssetsModel1 = '2222AQ';
    this.editTags1 = 'qa assets';
    this.editAssetsUuid1 = '88:13:bc:0a:41:ad';
    this.editAssetsUpTime1 = '452';
    this.editAssetsCost1 = `${faker.commerce.price()}:${faker.commerce.price()}:2:7`;
    this.editAssetsSalvage1 = faker.commerce.price();
    this.editAssetsUsedBy1 = 'Everyone';
    this.editAssetsManagedBy1 = 'Admins';
    this.editAssetsDepartment1 = faker.commerce.department();
    this.editAssetsProductNum1 = 'CTE7005GY4';
    this.editAssetSerialNumber1 = golVari.randomImei;
    this.editAssetsDetials1 = 'editing the asset for testing';
    this.appleDevice = 'Apple Device';
    this.desktop = 'Desktop';
    this.dongle = 'Dongle';
    this.fireWall = 'Firewall';
    this.iPad = 'iPad';
    this.iPhone = 'iPhone';
    this.laptop = 'Laptop';
    this.mobile = 'Mobile';
    this.other = 'Other';
    this.unrecognized = 'Unrecognized';
    this.phone = 'Phone';
    this.phoneSystem = 'Phone System';
    this.printer = 'Printer';
    this.router = 'Router';
    this.server = 'Server';
    this.switch = 'Switch';
    this.tablet = 'Tablet';
    this.thinClient = 'Thin Client';
    this.tv = 'Tv';
    this.virtualMachine = 'Virtual Machine';
    this.wap = 'WAP';
    this.windows = 'Windows';
    this.assetsSerialNumber1 = faker.internet.mac();
    this.assetsSerialNumber2 = faker.internet.mac();
    this.assetsSerialNumber3 = faker.internet.mac();
    this.assetsSerialNumber4 = faker.internet.mac();
    this.assetsSerialNumber5 = faker.internet.mac();
    this.assetsSerialNumber6 = faker.internet.mac();
    this.assetsSerialNumber7 = faker.internet.mac();
    this.assetsName3 = faker.commerce.product().toUpperCase();
    this.assetsName4 = faker.commerce.product().toUpperCase();
    this.assetsName5 = faker.commerce.product().toUpperCase();
    this.assetsName6 = faker.commerce.product().toUpperCase();
    this.assetsName7 = faker.commerce.product().toUpperCase();
    this.assetsIp13 = '***********';
    this.assetsPort = '5';
    this.assetsPort2 = '6';
    this.assetsPort3 = '7';
    this.assetsPort4 = '2';
    this.assetsPort5 = '8';
    this.assetsPort6 = '4';
    this.assetsProductNum3 = 'YUdqZrTqIEJuEAl';
    this.assetsProductNum4 = 'ABdqZrTqIEJuEla';
    this.assetsProductNum5 = 'CDdqZrTqIEJuEEE';
    this.assetsProductNum6 = 'FGdqZrTqIEJuEUU';
    this.assetsProductNum7 = 'ILdqZrTqIEJuESD';
    this.connectionInterface1 = 'DATA';
    this.connectionInterface3 = 'RATA';
    this.connectionInterface4 = 'FATA';
    this.connectionInterface5 = 'EATA';
    this.connectionInterface6 = 'QATA';
    this.connectionInterface7 = 'YATA';
    this.memory1 = `${nanoid(2)} MB`;
    this.memory2 = `${nanoid(2)} KB`;
    this.memory3 = `${nanoid(2)} KMB`;
    this.memory4 = `${nanoid(2)} GMB`;
    this.memory5 = `${nanoid(2)} MMB`;
    this.memory6 = `${nanoid(2)} TMB`;
    this.hardDrive1 = `${nanoid(1)} MB`;
    this.hardDrive2 = `${nanoid(1)} kMB`;
    this.hardDrive3 = `${nanoid(1)} GMB`;
    this.hardDrive4 = `${nanoid(1)} MMB`;
    this.hardDrive5 = `${nanoid(1)} TB`;
    this.hardDrive6 = `${nanoid(1)} TTB`;
    this.processor = `Gen${nanoid(2)}`;
    this.processor1 = `Gen1${nanoid(2)}`;
    this.processor2 = `Gen2${nanoid(2)}`;
    this.processor3 = `Gen3${nanoid(2)}`;
    this.processor4 = `Gen4${nanoid(2)}`;
    this.processor5 = `Gen5${nanoid(2)}`;
    this.processor6 = `Gen6${nanoid(2)}`;
    this.tags = `tags ${generateRandomTags(4)}`;
    this.checkMarkIcon = 'genuicon-checkmark';
    this.arrowIcon = 'nulodgicon-arrow-right-b';
    this.exclamationIcon = 'genuicon-exclamation';
    this.crossIcon = '&times;';
    this.randomText = generateRandomTags(2);
    this.randomYears = '4';
    this.randomMonths = '6';
    this.randomYears1 = '8';
    this.randomMonths1 = '10';
    this.assetsPurchasePrice1 = faker.commerce.price(2, 100);
    this.assetsPurchasePrice2 = faker.commerce.price(2, 90);
    this.assetsreplacementCost1 = faker.commerce.price(2, 80);
    this.assetsreplacementCost2 = faker.commerce.price(2, 70);
    this.assetSerialNumber4 = generateSerialNumber();
    this.assetSerialNumber5 = generateSerialNumber();
    this.assetSerialNumber6 = generateSerialNumber();
    this.assetSerialNumber7 = generateSerialNumber1();

    function generateRandomTags(length) {
      const alphabet = 'abcdefghijklmnopqrstuvwxyz';
      let result = '';

      for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * alphabet.length);
        result += alphabet.charAt(randomIndex);
      }

      return result;
    }

    function generateSerialNumber() {
      const prefix = 'LAP-';
      const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
      let serialNumber = prefix;

      for (let i = 0; i < 8; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length);
        serialNumber += characters[randomIndex];
      }

      return serialNumber;
    }

    function generateSerialNumber1() {
      const prefix = 'HP-';
      const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
      let serialNumber = prefix;

      for (let i = 0; i < 8; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length);
        serialNumber += characters[randomIndex];
      }

      return serialNumber;
    }
  }
} export default assetVar;
