import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';

const golVari = new golvar();
const naviBar = new navbar();

class deleteAsset {

  constructor() {
    this.assetTab = Selector('[data-tc-assets="Assets menu"]');
    this.searchField = Selector('[data-tc-search-asset]');
    this.viewsearchedAsset = Selector('[data-tc-view-assets-name]');
    this.archieveBtn = Selector('[data-tc-archive-asset-btn]');
    this.confirmArchieved = Selector('[data-tc-save-form-btn="Archive"]');
    this.filterBtn = Selector('[data-tc-filter-menu-button]');
    this.statusDropDown = Selector('[data-tc-filter="Status"]');
    this.selectStatus = Selector('[data-tc-filter-option="Archived"]');
    this.deleteBtn = Selector('[data-tc-delete-asset-btn]');
    this.confirmDelete = Selector('[data-tc-modal-delete-asset-btn]');
    this.searchDeletedAsset = Selector('[data-tc-not-tracking-assets]');
    this.archiveAction = Selector('[data-tc-action="Archive"]');
    this.deleteAction = Selector('[data-tc-action="Delete"]');
    this.bulkArchive = Selector('[data-tc-bulk-asset-archive-btn]');
    this.bulkDelete = Selector('[data-tc-modal-delete-btn]');
    this.selectAll = Selector('[data-tc-asset="select all"]');
    this.notifyPop = Selector('.notification-title');
    this.selectActiveFilter = Selector('[data-tc-filter-option="Active"]');
  }

  async deleteAssets(assetName) {
    const viewSearchedAst = Selector(`[data-tc-asset-box-link="${assetName}"]`);
    const checkAstName = Selector(`[data-tc-asset-box-link="${assetName}"] [data-tc-asset-name]`);
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
      .hover(this.assetTab)
      .click(this.assetTab)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(1000)
      .hover(this.searchField)
      .typeText(this.searchField, assetName, { paste: true })
      .wait(1000)
      .hover(viewSearchedAst)
      .expect(checkAstName.innerText).contains(assetName)
      .click(viewSearchedAst)
      .wait(1000)
      .hover(this.archieveBtn)
      .click(this.archieveBtn)
      .hover(this.confirmArchieved)
      .click(this.confirmArchieved)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.statusDropDown)
      .click(this.statusDropDown)
      .wait(500)
      .hover(this.selectStatus)
      .click(this.selectStatus)
      .wait(1000)
      .typeText(this.searchField, assetName, { replace: true })
      .wait(1000)
      .hover(viewSearchedAst)
      .expect(checkAstName.innerText).contains(assetName)
      .click(viewSearchedAst)
      .wait(1000)
      .hover(this.deleteBtn)
      .click(this.deleteBtn)
      .wait(1000)
      .hover(this.confirmDelete)
      .click(this.confirmDelete)
      .wait(2000)
  }

  async bulkDeletion(assetName1) {
    const assetCheckBox = Selector(`[data-tc-checkbox="${assetName1}"]`);
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
      .hover(this.assetTab)
      .click(this.assetTab)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(1000)
      .click(assetCheckBox)
      .wait(2000)
      .expect(this.archiveAction.visible).ok()
      .wait(1000)
      .click(this.selectAll)
      .wait(1000)
      .click(this.archiveAction)
      .wait(1000)
      .click(this.bulkArchive)
      .wait(5000)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.statusDropDown)
      .click(this.statusDropDown)
      .wait(500)
      .hover(this.selectStatus)
      .click(this.selectStatus)
      .wait(1000)
      .click(assetCheckBox)
      .wait(2000)
      .click(this.selectAll)
      .wait(1000)
      .click(this.deleteAction)
      .wait(1000)
      .click(this.bulkDelete)
      .wait(5000)
  }

  async bulk3Deletion(assetName) {
    const [assetName1, assetName2, assetName3] = assetName.split(';');
    const assetCheckBox1 = Selector(`[data-tc-checkbox="${assetName1}"]`);
    const assetCheckBox2 = Selector(`[data-tc-checkbox="${assetName2}"]`);
    const assetCheckBox3 = Selector(`[data-tc-checkbox="${assetName3}"]`);
    await t.expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
      .hover(this.assetTab)
      .click(this.assetTab)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(1000)
      .click(assetCheckBox1)
      .wait(500)
      .click(assetCheckBox2)
      .wait(500)
      .click(assetCheckBox3)
      .wait(500)
      .expect(this.archiveAction.visible).ok()
      .wait(500)
      .click(this.archiveAction)
      .wait(1000)
      .click(this.bulkArchive)
      .wait(5000)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.statusDropDown)
      .click(this.statusDropDown)
      .wait(500)
      .hover(this.selectStatus)
      .click(this.selectStatus)
      .wait(1000)
      .click(assetCheckBox1)
      .wait(500)
      .click(assetCheckBox2)
      .wait(500)
      .click(assetCheckBox3)
      .wait(500)
      .click(this.deleteAction)
      .wait(500)
      .click(this.bulkDelete)
      .wait(3000)
      .expect(assetCheckBox1.exists).notOk('Asset 1 should deleted')
      .expect(assetCheckBox2.exists).notOk('Asset 2 should deleted')
      .expect(assetCheckBox3.exists).notOk('Asset 3 should deleted')
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.statusDropDown)
      .click(this.statusDropDown)
      .wait(500)
      .hover(this.selectActiveFilter)
      .click(this.selectActiveFilter);
  }
} export default deleteAsset
