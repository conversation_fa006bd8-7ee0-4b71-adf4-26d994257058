import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import addAsset from './add_assets';

const golVari = new golvar();
const addAstPage = new addAsset();

class assetSoftware {

  constructor() {
    this.softwareSideNav = Selector('[data-tc-software-link]');
    this.addBtn = Selector('[data-tc-add-software]');
    this.nameField = Selector('[data-tc-software-name-field]');
    this.typeDropdown = Selector('[data-tc-software-type]');
    this.productKeyField = Selector('[data-tc-product-key]');
    this.installDate = Selector('[data-tc-title="new software"] [data-tc-install-date]');
    this.editInstallDate = Selector('[data-tc-title="edit software"] [data-tc-install-date]');
    this.saveBtn = Selector('[data-tc-save-form-btn="Save Software"]');
    this.searchField = Selector('[data-tc-search-software-field]');
    this.viewName = Selector('[data-tc-view-software-name]');
    this.viewType = Selector('[data-tc-view-software-type]');
    this.viewKey = Selector('[data-tc-view-product-name]');
    this.viewDate = Selector('[data-tc-view-install-date]');
    this.hoverAction = Selector('[data-tc-hover-action]');
    this.dotedIcon = Selector('[data-tc-hover="doted"]');
    this.editIcon = Selector('[data-tc-edit-software-icon]');
    this.archieveIcon = Selector('[data-tc-archive-software-icon]');
    this.filterStatus = Selector('[data-tc-software-filter-status]');
    this.selectStatus = Selector('[data-tc-filter-option="Archived"]');
    this.deleteIcon = Selector('[data-tc-delete-software-icon]');
    this.notifyPopUp = Selector('.notification-content');
    this.deletedSoftware = Selector('[data-tc-no-tracking]');
    this.viewNameError = Selector('[data-tc-error="The software name field is required"]');
    this.viewTypeError = Selector('[data-tc-error="The software type field is required"]');
    this.softwareSection = Selector('#asset-software');
    this.cancelSoftModal = Selector('[data-tc-cancel="software details"]');
  }

  async addSoftware(softName, softType, softKey, softInstallDate) {
    await t.hover(this.softwareSideNav)
      .click(this.softwareSideNav)
      .expect(this.softwareSection.visible).ok()
      .hover(this.addBtn)
      .click(this.addBtn)
      .typeText(this.nameField, softName, { paste: true })
      .hover(this.typeDropdown)
      .click(this.typeDropdown)
      .typeText(this.typeDropdown, softType, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .hover(this.productKeyField)
      .typeText(this.productKeyField, softKey, { paste: true })
      .hover(this.installDate)
      .click(this.installDate)
      .typeText(this.installDate, softInstallDate, { paste: true })
      .click(this.installDate)
      .wait(500)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(1000)
      .hover(this.searchField)
      .typeText(this.searchField, softName, { replace: true })
      .pressKey('enter')
      .wait(500)
      .hover(this.viewName)
      .expect(this.viewName.innerText).contains(softName)
      .hover(this.viewType)
      .expect(this.viewType.innerText).contains(softType)
      .hover(this.viewKey)
      .expect(this.viewKey.innerText).contains(softKey);
    await t.wait(1000)
  }

  async softwareValidation(softName1, softType1, softKey1, softInstallDate1) {
    await t.wait(1000)
      .hover(this.softwareSideNav)
      .click(this.softwareSideNav)
      .expect(this.softwareSection.visible).ok()
      .hover(this.addBtn)
      .click(this.addBtn)
      .wait(1000)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(1000)
      .hover(this.viewNameError)
      .expect(this.viewNameError.innerText).contains('The software name field is required')
      .wait(500)
      .expect(this.viewTypeError.innerText).contains('The software type field is required')
      .wait(500)
      .hover(this.typeDropdown)
      .click(this.typeDropdown)
      .typeText(this.typeDropdown, softType1, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .hover(this.productKeyField)
      .typeText(this.productKeyField, softKey1, { paste: true })
      .hover(this.installDate)
      .click(this.installDate)
      .typeText(this.installDate, softInstallDate1, { paste: true })
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(1000)
      .hover(this.viewNameError)
      .expect(this.viewNameError.innerText).contains('The software name field is required')
      .wait(1000)
      .typeText(this.nameField, softName1, { paste: true })
      .hover(this.typeDropdown)
      .click(this.typeDropdown)
      .typeText(this.typeDropdown, softType1, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(1000)
      .expect(this.viewTypeError.innerText).contains('The software type field is required')
      .wait(500)
      .click(this.cancelSoftModal)
      .wait(500)
  }

  async editSoftware(swName, editSoftName, editSoftType, editSoftKey, editSoftInstallDate) {
    const viewName = Selector(`[data-tc-soft-name="${swName}"]`);
    const viewEditName = Selector(`[data-tc-soft-name="${editSoftName}"]`);
    await t.wait(1000)
      .hover(this.softwareSideNav)
      .click(this.softwareSideNav)
      .wait(1000)
      .expect(this.softwareSection.visible).ok()
      .wait(1000)
      .hover(this.searchField)
      .typeText(this.searchField, swName, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .expect(viewName.innerText).contains(swName)
      .wait(1000)
      .hover(this.hoverAction)
      .click(this.editIcon)
      .wait(1000)
      .typeText(this.nameField, editSoftName, { replace: true })
      .hover(this.typeDropdown)
      .click(this.typeDropdown)
      .pressKey('delete')
      .typeText(this.typeDropdown, editSoftType, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(this.productKeyField)
      .typeText(this.productKeyField, editSoftKey, { replace: true })
      .hover(this.editInstallDate)
      .click(this.editInstallDate)
      .typeText(this.editInstallDate, editSoftInstallDate, { replace: true })
      .click(this.editInstallDate)
      .wait(1000)
      .click(this.saveBtn)
      .hover(this.searchField)
      .typeText(this.searchField, editSoftName, { replace: true })
      .pressKey('enter')
      .wait(500)
      .hover(viewEditName)
      .expect(viewEditName.innerText).contains(editSoftName)
      .hover(this.viewType)
      .expect(this.viewType.innerText).contains(editSoftType)
      .hover(this.viewKey)
      .expect(this.viewKey.innerText).contains(editSoftKey);
    await t.wait(1000)
  }

  async deleteSoftware(softwName) {
    const viewName = Selector(`[data-tc-soft-name="${softwName}"]`);
    await t.wait(1000)
      .hover(this.softwareSideNav)
      .click(this.softwareSideNav)
      .wait(1000)
      .expect(this.softwareSection.visible).ok()
      .wait(1000)
      .hover(this.searchField)
      .typeText(this.searchField, softwName, { replace: true })
      .pressKey('enter')
      .wait(500)
      .hover(this.hoverAction)
      .wait(500)
      .hover(this.archieveIcon)
      .click(this.archieveIcon)
      .wait(500)
      .expect(this.notifyPopUp.innerText).contains('Successfully archived this software.')
      .wait(1000)
      .hover(this.filterStatus)
      .click(this.filterStatus)
      .wait(500)
      .hover(this.selectStatus)
      .click(this.selectStatus)
      .wait(1000)
      .hover(this.searchField)
      .typeText(this.searchField, softwName, { replace: true })
      .pressKey('enter')
      .wait(500)
      .hover(viewName)
      .hover(this.hoverAction)
      .wait(500)
      .hover(this.deleteIcon)
      .click(this.deleteIcon)
      .expect(this.notifyPopUp.innerText).contains('Successfully deleted this software.')
      .hover(this.searchField)
      .typeText(this.searchField, softwName, { replace: true })
      .pressKey('enter')
      .wait(500)
      .expect(this.deletedSoftware.innerText).contains('Not tracking anything here, yet.')
      .click(addAstPage.backAstLink)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(1000)
  }
} export default assetSoftware
