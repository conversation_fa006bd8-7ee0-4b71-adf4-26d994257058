import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import deleteAsset from './delete_assets';

const golVari = new golvar();
const naviBar = new navbar();
const delAst = new deleteAsset();

class assetHardware {

  constructor() {
    this.processorLabel = Selector('[data-tc-label-field="Processor"]');
    this.inputProcessor = Selector('[data-tc-label-field="Processor"] [data-tc-processor]');
    this.inputMemory = Selector('[data-tc-input-memory]');
    this.inputHardDrive = Selector('[data-tc-hard-drive]');
    this.inputSerialNumber = Selector('[data-tc-serial-number]');
    this.inputProduct = Selector('[data-tc-model]');
    this.inputPorts = Selector('[data-tc-input-ports]');
    this.inputImei = Selector('[data-tc-input-imei]');
    this.inputConnectInterface = Selector('[data-tc-connection-interface]');
    this.systemDetailTitle = Selector('[data-tc-hardware-details]');
    this.editBtn = Selector('[data-tc-edit="hardware"]');
    this.saveBtn = Selector('[data-tc-save="hardware details"]');
    this.procLabel = Selector('[data-tc-label="Processor"]');
    this.portLabel = Selector('[data-tc-label="Ports"]');
    this.noDetails = Selector('[data-tc-no-hardware-details]');
    this.assetDataVar = 'data-tc-asset-data';
  }

  async addHardwareDetails(type1, processor1, memory1, hardDrive1, serialNumber1, productNumber1, ipAddress1, ports1, macAddress1, connectionInterface1, imei) {
    const viewprocessor1 = Selector(`[${this.assetDataVar}="${processor1}"]`);
    const viewmemory1 = Selector(`[${this.assetDataVar}="${memory1}"]`);
    const viewHwDrive = Selector(`[${this.assetDataVar}="${hardDrive1}"]`);
    const viewSerial1 = Selector(`[${this.assetDataVar}="${serialNumber1}"]`);
    const viewProduct = Selector(`[${this.assetDataVar}="${productNumber1}"]`);
    const viewIp = Selector(`[${this.assetDataVar}="${ipAddress1}"]`);
    const viewMac = Selector(`[data-tc-mac="${macAddress1}"]`);
    const viewPorts1 = Selector(`[${this.assetDataVar}="${ports1}"]`);
    const viewConnection = Selector(`[${this.assetDataVar}="${connectionInterface1}"]`);
    const viewImei = Selector(`[${this.assetDataVar}="${imei}"]`);
    await t.wait(1000)
      .hover(this.systemDetailTitle)
      .expect(this.systemDetailTitle.innerText).contains('System Details')
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/managed_assets/')
      .wait(1000);
    if (type1 == "Apple Device" || type1 == "Desktop" || type1 == "Server" || type1 == "Laptop" || type1 == "Thin Client" || type1 == "Virtual Machine" || type1 == "Windows") {
      await t.hover(this.editBtn)
        .click(this.editBtn)
        .wait(1000)
        .hover(this.processorLabel)
        .expect(this.processorLabel.innerText).contains('Processor (optional)')
        .wait(500)
        .click(this.inputProcessor)
        .wait(500)
        .typeText(this.inputProcessor, processor1, { replace: true })
        .wait(500)
        .click(this.inputMemory)
        .wait(500)
        .typeText(this.inputMemory, memory1, { replace: true })
        .wait(500)
        .click(this.inputHardDrive)
        .wait(500)
        .typeText(this.inputHardDrive, hardDrive1, { replace: true })
        .wait(500)
        .click(this.inputSerialNumber)
        .wait(500)
        .typeText(this.inputSerialNumber, serialNumber1, { replace: true })
        .wait(500)
        .click(this.inputProduct)
        .wait(500)
        .typeText(this.inputProduct, productNumber1, { replace: true })
        .wait(1000)
        .click(this.saveBtn)
        .wait(1000)
        .hover(this.procLabel)
        .wait(500)
        .expect(viewprocessor1.innerText).eql(processor1)
        .wait(500)
        .expect(viewmemory1.innerText).eql(memory1)
        .wait(500)
        .expect(viewHwDrive.innerText).eql(hardDrive1)
        .wait(500)
        .expect(viewSerial1.innerText).eql(serialNumber1)
        .wait(500)
        .expect(viewProduct.innerText).eql(productNumber1)
        .wait(500)
        .expect(viewIp.innerText).eql(ipAddress1)
        .wait(500)
        .expect(viewMac.innerText).eql(macAddress1)
        .wait(500);
    }
    else if (type1 == "Firewall" || type1 == "Router" || type1 == "WAP" || type1 == "Switch") {
      await t.hover(this.editBtn)
        .click(this.editBtn)
        .wait(1000)
        .click(this.inputPorts)
        .wait(500)
        .typeText(this.inputPorts, ports1, { replace: true })
        .wait(500)
        .click(this.inputSerialNumber)
        .wait(500)
        .typeText(this.inputSerialNumber, serialNumber1, { replace: true })
        .wait(500)
        .click(this.inputProduct)
        .wait(500)
        .typeText(this.inputProduct, productNumber1, { replace: true })
        .wait(1000)
        .click(this.saveBtn)
        .wait(1000)
        .hover(this.portLabel)
        .wait(500)
        .expect(viewPorts1.innerText).eql(ports1)
        .wait(500)
        .expect(viewSerial1.innerText).eql(serialNumber1)
        .wait(500)
        .expect(viewProduct.innerText).eql(productNumber1)
        .wait(500)
        .expect(viewIp.innerText).eql(ipAddress1)
        .wait(500)
        .expect(viewMac.innerText).eql(macAddress1)
        .wait(500);
    }
    else if (type1 == "Phone" || type1 == "Phone System") {
      await t.hover(this.editBtn)
        .click(this.editBtn)
        .wait(1000)
        .click(this.inputConnectInterface)
        .wait(500)
        .typeText(this.inputConnectInterface, connectionInterface1, { replace: true })
        .wait(500)
        .click(this.inputSerialNumber)
        .wait(500)
        .typeText(this.inputSerialNumber, serialNumber1, { replace: true })
        .wait(500)
        .click(this.inputProduct)
        .wait(500)
        .typeText(this.inputProduct, productNumber1, { replace: true })
        .wait(1000)
        .click(this.saveBtn)
        .wait(1000)
        .expect(viewConnection.innerText).eql(connectionInterface1)
        .wait(500)
        .expect(viewSerial1.innerText).eql(serialNumber1)
        .wait(500)
        .expect(viewProduct.innerText).eql(productNumber1)
        .wait(500)
        .expect(viewIp.innerText).eql(ipAddress1)
        .wait(500)
        .expect(viewMac.innerText).eql(macAddress1)
        .wait(500);
    }
    else if (type1 == "iPad" || type1 == "iPhone" || type1 == "Mobile" || type1 == "Tablet") {
      await t.hover(this.editBtn)
        .click(this.editBtn)
        .wait(1000)
        .click(this.inputImei)
        .wait(500)
        .typeText(this.inputImei, imei, { replace: true })
        .wait(500)
        .click(this.inputSerialNumber)
        .wait(500)
        .typeText(this.inputSerialNumber, serialNumber1, { replace: true })
        .wait(500)
        .click(this.inputProduct)
        .wait(500)
        .typeText(this.inputProduct, productNumber1, { replace: true })
        .wait(1000)
        .click(this.saveBtn)
        .wait(1000)
        .hover(viewImei)
        .wait(500)
        .expect(viewImei.innerText).eql(imei)
        .wait(500)
        .expect(viewSerial1.innerText).eql(serialNumber1)
        .wait(500)
        .expect(viewProduct.innerText).eql(productNumber1)
        .wait(500)
        .expect(viewIp.innerText).eql(ipAddress1)
        .wait(500)
        .expect(viewMac.innerText).eql(macAddress1)
        .wait(500);
    }
    else if (type1 == "Printer") {
      await t.hover(this.editBtn)
        .click(this.editBtn)
        .wait(1000)
        .click(this.inputSerialNumber)
        .wait(500)
        .typeText(this.inputSerialNumber, serialNumber1, { replace: true })
        .wait(500)
        .click(this.inputProduct)
        .wait(500)
        .typeText(this.inputProduct, productNumber1, { replace: true })
        .wait(1000)
        .click(this.saveBtn)
        .wait(1000)
        .hover(viewSerial1)
        .wait(500)
        .expect(viewSerial1.innerText).eql(serialNumber1)
        .wait(500)
        .expect(viewProduct.innerText).eql(productNumber1)
        .wait(500)
        .expect(viewIp.innerText).eql(ipAddress1)
        .wait(500)
        .expect(viewMac.innerText).eql(macAddress1)
        .wait(500);
    }
    else {
      await t
        .expect(this.noDetails.innerText).contains(`This asset type (${type1}) doesn't have hardware details associated with it. You can change the asset type`)
        .wait(500)
    }
  }

  async editHardwareDetails(astName, type1, processor1, memory1, hardDrive1, serialNumber1, productNumber1, ipAddress1, ports1, macAddress1, connectionInterface1, imei) {
    const viewprocessor1 = Selector(`[${this.assetDataVar}="${processor1}"]`);
    const viewmemory1 = viewprocessor1Selector(`[${this.assetDataVar}="${memory1}"]`);
    const viewHwDrive = Selector(`[${this.assetDataVar}="${hardDrive1}"]`);
    const viewSerial1 = Selector(`[${this.assetDataVar}="${serialNumber1}"]`);
    const viewProduct = Selector(`[${this.assetDataVar}="${productNumber1}"]`);
    const viewIp = Selector(`[${this.assetDataVar}="${ipAddress1}"]`);
    const viewMac = Selector(`[data-tc-mac="${macAddress1}"]`);
    const viewPorts1 = Selector(`[${this.assetDataVar}="${ports1}"]`);
    const viewConnection = Selector(`[${this.assetDataVar}="${connectionInterface1}"]`);
    const viewImei = Selector(`[${this.assetDataVar}="${imei}"]`);
    await t.hover(this.systemDetailTitle)
      .expect(this.systemDetailTitle.innerText).contains('System Details')
      .wait(1000);
    if (type1 == "Apple Device" || type1 == "Desktop" || type1 == "Server" || type1 == "Laptop" || type1 == "Thin Client" || type1 == "Virtual Machine" || type1 == "Windows") {
      await t.hover(this.editBtn)
        .click(this.editBtn)
        .wait(1000)
        .hover(this.processorLabel)
        .expect(this.processorLabel.innerText).contains('Processor (optional)')
        .wait(500)
        .click(this.inputProcessor)
        .wait(500)
        .typeText(this.inputProcessor, processor1, { replace: true })
        .wait(500)
        .click(this.inputMemory)
        .wait(500)
        .typeText(this.inputMemory, memory1, { replace: true })
        .wait(500)
        .click(this.inputHardDrive)
        .wait(500)
        .typeText(this.inputHardDrive, hardDrive1, { replace: true })
        .wait(500)
        .click(this.inputSerialNumber)
        .wait(500)
        .typeText(this.inputSerialNumber, serialNumber1, { replace: true })
        .wait(500)
        .click(this.inputProduct)
        .wait(500)
        .typeText(this.inputProduct, productNumber1, { replace: true })
        .wait(1000)
        .click(this.saveBtn)
        .wait(1000)
        .hover(this.procLabel)
        .wait(500)
        .expect(viewprocessor1.innerText).eql(processor1)
        .wait(500)
        .expect(viewmemory1.innerText).eql(memory1)
        .wait(500)
        .expect(viewHwDrive.innerText).eql(hardDrive1)
        .wait(500)
        .expect(viewSerial1.innerText).eql(serialNumber1)
        .wait(500)
        .expect(viewProduct.innerText).eql(productNumber1)
        .wait(500)
        .expect(viewIp.innerText).eql(ipAddress1)
        .wait(500)
        .expect(viewMac.innerText).eql(macAddress1)
        .wait(500);
    }
    else if (type1 == "Firewall" || type1 == "Router" || type1 == "WAP" || type1 == "Switch") {
      await t.hover(this.editBtn)
        .click(this.editBtn)
        .wait(1000)
        .click(this.inputPorts)
        .wait(500)
        .typeText(this.inputPorts, ports1, { replace: true })
        .wait(500)
        .click(this.inputSerialNumber)
        .wait(500)
        .typeText(this.inputSerialNumber, serialNumber1, { replace: true })
        .wait(500)
        .click(this.inputProduct)
        .wait(500)
        .typeText(this.inputProduct, productNumber1, { replace: true })
        .wait(1000)
        .click(this.saveBtn)
        .wait(1000)
        .hover(this.portLabel)
        .wait(500)
        .expect(viewPorts1.innerText).eql(ports1)
        .wait(500)
        .expect(viewSerial1.innerText).eql(serialNumber1)
        .wait(500)
        .expect(viewProduct.innerText).eql(productNumber1)
        .wait(500)
        .expect(viewIp.innerText).eql(ipAddress1)
        .wait(500)
        .expect(viewMac.innerText).eql(macAddress1)
        .wait(500);
    }
    else if (type1 == "Phone" || type1 == "Phone System") {
      await t.hover(this.editBtn)
        .click(this.editBtn)
        .wait(1000)
        .click(this.inputConnectInterface)
        .wait(500)
        .typeText(this.inputConnectInterface, connectionInterface1, { replace: true })
        .wait(500)
        .click(this.inputSerialNumber)
        .wait(500)
        .typeText(this.inputSerialNumber, serialNumber1, { replace: true })
        .wait(500)
        .click(this.inputProduct)
        .wait(500)
        .typeText(this.inputProduct, productNumber1, { replace: true })
        .wait(1000)
        .click(this.saveBtn)
        .wait(1000)
        .expect(viewConnection.innerText).eql(connectionInterface1)
        .wait(500)
        .expect(viewSerial1.innerText).eql(serialNumber1)
        .wait(500)
        .expect(viewProduct.innerText).eql(productNumber1)
        .wait(500)
        .expect(viewIp.innerText).eql(ipAddress1)
        .wait(500)
        .expect(viewMac.innerText).eql(macAddress1)
        .wait(500);
    }
    else if (type1 == "iPad" || type1 == "iPhone" || type1 == "Mobile" || type1 == "Tablet") {
      await t.hover(this.editBtn)
        .click(this.editBtn)
        .wait(1000)
        .click(this.inputImei)
        .wait(500)
        .typeText(this.inputImei, imei, { replace: true })
        .wait(500)
        .click(this.inputSerialNumber)
        .wait(500)
        .typeText(this.inputSerialNumber, serialNumber1, { replace: true })
        .wait(500)
        .click(this.inputProduct)
        .wait(500)
        .typeText(this.inputProduct, productNumber1, { replace: true })
        .wait(1000)
        .click(this.saveBtn)
        .wait(1000)
        .hover(viewImei)
        .wait(500)
        .expect(viewImei.innerText).eql(imei)
        .wait(500)
        .expect(viewSerial1.innerText).eql(serialNumber1)
        .wait(500)
        .expect(viewProduct.innerText).eql(productNumber1)
        .wait(500)
        .expect(viewIp.innerText).eql(ipAddress1)
        .wait(500)
        .expect(viewMac.innerText).eql(macAddress1)
        .wait(500);
    }
    else if (type1 == "Printer") {
      await t.hover(this.editBtn)
        .click(this.editBtn)
        .wait(1000)
        .click(this.inputSerialNumber)
        .wait(500)
        .typeText(this.inputSerialNumber, serialNumber1, { replace: true })
        .wait(500)
        .click(this.inputProduct)
        .wait(500)
        .typeText(this.inputProduct, productNumber1, { replace: true })
        .wait(1000)
        .click(this.saveBtn)
        .wait(1000)
        .hover(viewSerial1)
        .wait(500)
        .expect(viewSerial1.innerText).eql(serialNumber1)
        .wait(500)
        .expect(viewProduct.innerText).eql(productNumber1)
        .wait(500)
        .expect(viewIp.innerText).eql(ipAddress1)
        .wait(500)
        .expect(viewMac.innerText).eql(macAddress1)
        .wait(500);
    }
    else {
      await t
        .expect(this.noDetails.innerText).contains(`This asset type (${type1}) doesn't have hardware details associated with it. You can change the asset type`)
        .wait(500)
    }
  }
} export default assetHardware
