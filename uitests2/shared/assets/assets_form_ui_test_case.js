import { Selector, t } from 'testcafe';

class assetForm {

  constructor() {
    this.assetsName = Selector('[data-tc-managed-asset-name]');
    this.assetsType = Selector('[data-tc-managed-asset-type-id]');
    this.assetTags = Selector('[data-tc-asset-tag]');
    this.assetNotes = Selector('[data-tc-managed-asset-description]');
    this.assetMacAddress = Selector('[data-tc-mac-address]');
    this.assetIpAddress = Selector('[data-tc-ip-address]');
    this.assetManufacturer = Selector('[data-tc-managed-asset-manufacturer]');
    this.assetModel = Selector('[data-tc-managed-asset-model]');
    this.tags = Selector('[data-tc-tag]');
    this.assetSystemUuid = Selector('[data-tc-managed-asset-system-uuid]');
    this.assetSystemUpTime = Selector('[data-tc-managed-asset-system-up-time]');
    this.assetCost = Selector('[data-tc-cost]');
    this.assetSalvage = Selector('[data-tc-salvage]');
    this.assetDepreciaton = Selector('[data-tc-dep-drop-down]');
    this.assetUsedBy = Selector('[data-tc-used-by]');
    this.assetManagedBy = Selector('[data-tc-managed-by]');
    this.assetLocation = Selector('[data-tc-location-field]');
    this.assetDepartment = Selector('[data-tc-asset-department]');
    this.assetSerialNumber = Selector('[data-tc-managed-asset-machine-serial-number]');
    this.assetProductNumber = Selector('[data-tc-product-number]');
    this.assetAquisitionDate = Selector('[data-tc-acquisition-date]');
    this.assetWarrantyDate = Selector('[data-tc-warranty-date]');
    this.assetInstallationDate = Selector('[data-tc-installation-date]');
    this.addAlertBtn = Selector('[data-tc-add-alert-date-btn]');
    this.alertNote = Selector('[data-tc-asset-alert-notes]');
    this.assetAttachment = Selector('[data-tc-attachment]');
    this.inputAttachment = Selector('[data-tc-attachment] [data-tc-uploading-input-attachments]');
    this.assetImage = Selector('[data-tc-attachment-image="managed_assets"]');
    this.inputImages = Selector('[data-tc-attachment-image="managed_assets"] [data-tc-input-file]');
    this.techSideMenu = Selector('[data-tc-assets-side-menu="Tech Specs"]');
    this.costSideMenu = Selector('[data-tc-assets-side-menu="Cost Depreciation"]');
    this.locationSideMenu = Selector('[data-tc-assets-side-menu="Location & Usage"]');
    this.warrantySideMenu = Selector('[data-tc-assets-side-menu="Warranty & Acquisition"]');
    this.additionalSideMenu = Selector('[data-tc-assets-side-menu="Additional Details"]');
    this.changeAstType = Selector('[data-tc-confirm-change-type]');
    this.processorLabel = Selector('[data-tc-label="Processor"]');
    this.inputProcessor = Selector('[data-tc-label="Processor"] [data-tc-processor]');
    this.inputMemory = Selector('[data-tc-memory]');
    this.inputHardDrive = Selector('[data-tc-hard-drive]');
    this.inputSerialNumber = Selector('[data-tc-serial-number]');
    this.inputProduct = Selector('[data-tc-model]');
    this.inputPorts = Selector('[data-tc-input-ports]');
    this.inputImei = Selector('[data-tc-imei]');
    this.inputConnectInterface = Selector('[data-tc-connection-interface]');
    this.inputPurchasePrice =Selector('[data-tc-field-input="purchase_price"]');
    this.inputReplacementPrice = Selector('[data-tc-field-input="replacement_cost"]');
    this.inputSalvageValue = Selector('[data-tc-field-input="salvage"]');
    this.inputUsefulLife = Selector('[data-tc-field="useful life"]');
    this.inputEndLife = Selector('[data-tc-field="approaching life"]');
  }

  async assetForms(name, type, astTags, note, macAddress, ipAddress, manufacturer, model, tags, systemUUID, systemUpTime, cost, salvage, depreciationType, usedBy, managedBy, location, department, serialNumber, productNumber, acquisitionDate, warrantyExpiration, installationDate) {
    const [purchasePrice, replacementCost, usefulLife, endLife] = cost.split(':');
    await t.hover(this.assetsName)
      .typeText(this.assetsName, name, { paste: true })
      .hover(this.assetsType)
      .click(this.assetsType)
      .typeText(this.assetsType, type, { paste: true })
      .pressKey('enter')
      .wait(500)
      .hover(this.assetTags)
      .typeText(this.assetTags, astTags, { paste: true })
      .hover(this.assetNotes)
      .typeText(this.assetNotes, note, { paste: true })
      .hover(this.techSideMenu)
      .click(this.techSideMenu)
      .hover(this.assetMacAddress)
      .typeText(this.assetMacAddress, macAddress, { paste: true })
      .hover(this.assetIpAddress)
      .typeText(this.assetIpAddress, ipAddress, { paste: true })
      .hover(this.assetManufacturer)
      .typeText(this.assetManufacturer, manufacturer, { paste: true })
      .hover(this.assetModel)
      .typeText(this.assetModel, model, { paste: true })
      .hover(this.tags)
      .click(this.tags)
      .typeText(this.tags, tags, { paste: true })
      .pressKey('enter')
      .wait(500)
      .hover(this.assetSystemUuid)
      .typeText(this.assetSystemUuid, systemUUID, { paste: true })
      .hover(this.assetSystemUpTime)
      .typeText(this.assetSystemUpTime, systemUpTime, { paste: true })
      .wait(2000)
      .hover(this.assetUsedBy)
      .click(this.assetUsedBy)
      .typeText(this.assetUsedBy, usedBy, { paste: true })
      .pressKey('enter')
      .wait(50)
      .hover(this.assetManagedBy)
      .click(this.assetManagedBy)
      .typeText(this.assetManagedBy, managedBy, { paste: true })
      .pressKey('enter')
      .wait(500)
      .hover(this.locationSideMenu)
      .click(this.locationSideMenu)
      .hover(this.assetLocation)
      .click(this.assetLocation)
      .typeText(this.assetLocation, location, { paste: true })
      .pressKey('enter')
      .wait(500)
      .hover(this.assetDepartment)
      .typeText(this.assetDepartment, department, { paste: true })
      .typeText(this.inputPurchasePrice, purchasePrice, { paste: true })
      .typeText(this.inputReplacementPrice, replacementCost, { paste: true })
      .typeText(this.inputSalvageValue, salvage, { paste: true })
      .typeText(this.inputUsefulLife, usefulLife, { paste: true })
      .typeText(this.inputEndLife, endLife, { paste: true })
      .hover(this.assetSerialNumber)
      .typeText(this.assetSerialNumber, serialNumber, { paste: true })
      .hover(this.assetProductNumber)
      .typeText(this.assetProductNumber, productNumber, { paste: true })
      .hover(this.warrantySideMenu)
      .click(this.warrantySideMenu)
      .hover(this.additionalSideMenu)
      .click(this.additionalSideMenu)
      .hover(this.assetAttachment)
      .click(this.assetAttachment)
      .setFilesToUpload(this.inputAttachment, '../../shared/assets/inputImage.png')
      .click(this.assetImage)
      .setFilesToUpload(this.inputImages, '../../shared/assets/inputImage.png')
      .wait(500)
  }

  async editAssetForms(editName, editType, editAstTags, editNote, editMacAddress, editIpAddress, editManufacturer, editModel, editTags, editUuid, editUpTime, editCost, editSalvage, editDepreciationType, editUsedBy, editManagedBy, editLocation, editDepartment, editSerialNumber, editproductNumber, acquisitionDate, warrantyExpiration, installationDate) {
    const [purchasePrice, replacementCost, usefulLife, endLife] = editCost.split(':');
    await t.hover(this.assetsName)
      .typeText(this.assetsName, editName, { replace: true })
      .hover(this.assetsType)
      .click(this.assetsType)
      .typeText(this.assetsType, editType, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(this.changeAstType)
      .click(this.changeAstType)
      .wait(2000)
      .hover(this.assetTags)
      .typeText(this.assetTags, editAstTags, { replace: true })
      .wait(500)
      .hover(this.assetNotes)
      .typeText(this.assetNotes, editNote, { replace: true })
      .wait(1000)
      .hover(this.techSideMenu)
      .click(this.techSideMenu)
      .wait(1000)
      .hover(this.assetMacAddress)
      .typeText(this.assetMacAddress, editMacAddress, { replace: true })
      .hover(this.assetIpAddress)
      .wait(1000)
      .typeText(this.assetIpAddress, editIpAddress, { replace: true })
      .hover(this.assetManufacturer)
      .typeText(this.assetManufacturer, editManufacturer, { replace: true })
      .hover(this.assetModel)
      .wait(1000)
      .typeText(this.assetModel, editModel, { replace: true })
      .wait(1000)
      .hover(this.tags)
      .click(this.tags)
      .pressKey('delete')
      .typeText(this.tags, editTags, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(this.assetSystemUuid)
      .typeText(this.assetSystemUuid, editUuid, { replace: true })
      .pressKey('enter')
      .hover(this.assetSystemUpTime)
      .typeText(this.assetSystemUpTime, editUpTime, { replace: true })
      .wait(2000)
      .hover(this.assetUsedBy)
      .click(this.assetUsedBy)
      .pressKey('delete')
      .typeText(this.assetUsedBy, editUsedBy, { replace: true })
      .pressKey('enter')
      .wait(500)
      .hover(this.assetManagedBy)
      .click(this.assetManagedBy)
      .pressKey('delete')
      .typeText(this.assetManagedBy, editManagedBy, { replace: true })
      .pressKey('enter')
      .wait(500)
      .hover(this.locationSideMenu)
      .click(this.locationSideMenu)
      .wait(500)
      .hover(this.assetLocation)
      .click(this.assetLocation)
      .pressKey('delete')
      .typeText(this.assetLocation, editLocation, { replace: true })
      .pressKey('enter')
      .wait(500)
      .hover(this.assetDepartment)
      .typeText(this.assetDepartment, editDepartment, { replace: true })
      .wait(500)
      .typeText(this.inputPurchasePrice, purchasePrice, { replace: true })
      .typeText(this.inputReplacementPrice, replacementCost, { replace: true })
      .typeText(this.inputSalvageValue, editSalvage, { replace: true })
      .typeText(this.inputUsefulLife, usefulLife, { replace: true })
      .typeText(this.inputEndLife, endLife, { replace: true })
      .hover(this.assetSerialNumber)
      .typeText(this.assetSerialNumber, editSerialNumber, { replace: true })
      .hover(this.assetProductNumber)
      .typeText(this.assetProductNumber, editproductNumber, { replace: true })
      .wait(1000)
      .hover(this.warrantySideMenu)
      .click(this.warrantySideMenu)
      .wait(1000)
      .hover(this.additionalSideMenu)
      .click(this.additionalSideMenu)
      .hover(this.assetAttachment)
      .click(this.assetAttachment)
      .setFilesToUpload(this.inputAttachment, '../../shared/assets/inputImage.png')
      .click(this.assetImage)
      .setFilesToUpload(this.inputImages, '../../shared/assets/inputImage.png')
      .wait(1000)
  }
} export default assetForm
