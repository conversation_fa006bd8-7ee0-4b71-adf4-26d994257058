import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import addAssetType from './add_asset_type';

const golVari = new golvar();
const naviBar = new navbar();
const addType = new addAssetType();

class addDepreciation {

  constructor() {
    this.depreciationTab = Selector('[data-tc-links="depreciations"]');
    this.addBtn = Selector('[data-tc-add-new]');
    this.nameField = Selector('[data-tc-name]');
    this.selectType = Selector('[data-tc-depreciation-type]');
    this.yearField = Selector('[data-tc-usefullife]');
    this.descrField = Selector('[data-tc-description]');
    this.saveBtn = Selector('[data-tc-save-form-btn="Save"]');
    this.settingMenu = Selector('[data-tc-asset="settings menu"]');
    this.notifyPopUp = Selector('.notification-content');
  }

  async addDepreciations(deprName, deprType, lifeYear, description) {
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
      .hover(addType.settingMenu)
      .click(addType.settingMenu)
      .wait(1000)
      .hover(this.depreciationTab)
      .click(this.depreciationTab)
      .expect(golVari.currentpageurl()).contains('/managed_assets/settings/depreciations')
      .hover(this.addBtn)
      .click(this.addBtn)
      .wait(1000)
      .hover(this.nameField)
      .typeText(this.nameField, deprName, { paste: true })
      .hover(this.selectType.withText(deprType))
      .click(this.selectType.withText(deprType))
      .wait(1000)
      .hover(this.yearField)
      .typeText(this.yearField, lifeYear, { paste: true })
      .hover(this.descrField)
      .typeText(this.descrField, description, { paste: true })
      .wait(1000)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(500)
      .expect(this.notifyPopUp.innerText).contains('Successfully added a new depreciation')
      .wait(1000);
    const viewName = Selector(`[data-tc-listed-dep_name="${deprName}"]`);
    const viewusefulLife = Selector(`[data-tc-listed-dep_life="${lifeYear}"]`);
    const viewDescription = Selector(`[data-tc-listed-dep_descr="${description}"]`);
    await t.wait(500)
      .hover(viewName)
      .expect(viewName.innerText).contains(deprName)
      .wait(1000)
      .hover(viewusefulLife)
      .expect(viewusefulLife.innerText).contains(lifeYear)
      .wait(1000)
      .hover(viewDescription)
      .expect(viewDescription.innerText).contains(description)
      .wait(1000)
  }
} export default addDepreciation
