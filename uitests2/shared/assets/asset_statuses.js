import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import addAssetType from './add_asset_type';
import addAsset from './add_assets';
import deleteAsset from './delete_assets';

const golVari = new golvar();
const naviBar = new navbar();
const astType = new addAssetType();
const addAstPage = new addAsset();
const delAstPage = new deleteAsset();

class assetStatus {

  constructor() {
    this.defaultText = '(default)';
    this.inputField = 'data-tc-field="custom status"';
    this.iconDropDown = 'data-tc-drop-down="icons"';
    this.newCustomStatusParent = 'data-tc-title="new custom status"';
    this.editStatusPopup = 'data-tc-title="edit status"';
    this.statusLockIcon = 'data-tc="status lock"';
    this.usageStatusBox = 'data-tc-box="usage status"';
    this.statusMenu = Selector('[data-tc-links="statuses"]');
    this.mainHeading = Selector('[data-tc-title="Status Customization"]');
    this.headingText = Selector('[data-tc-paragraph]');
    this.customStatusText = Selector('[data-tc-title="New custom status"]');
    this.activeStatusText = Selector('[data-tc-title="Active statuses"]');
    this.statusName = 'data-tc-status-name';
    this.statusTag = 'data-tc-status-tag';
    this.assetInlineHeader = 'data-tc-asset="header inline area"';
    this.inUseStatusParent = Selector('[data-tc-status="In Use"]');
    this.inUseStatusText = this.inUseStatusParent.find(`[${this.statusName}]`);
    this.inUseStatusTag = this.inUseStatusParent.find(`[${this.statusTag}]`);
    this.inUseLockIcon = this.inUseStatusParent.find(`[${this.statusLockIcon}]`);
    this.rTUseStatusParent = Selector('[data-tc-status="Ready to Use"]');
    this.rTUseStatusText = this.rTUseStatusParent.find(`[${this.statusName}]`);
    this.rTUseStatusTag = this.rTUseStatusParent.find(`[${this.statusTag}]`);
    this.rtLockIcon = this.rTUseStatusParent.find(`[${this.statusLockIcon}]`);
    this.nAUseStatusParent = Selector('[data-tc-status="Needs Attention"]');
    this.nAUseStatusText = this.nAUseStatusParent.find(`[${this.statusName}]`);
    this.nAUseStatusTag = this.nAUseStatusParent.find(`[${this.statusTag}]`);
    this.nAUseLockIcon = this.nAUseStatusParent.find(`[${this.statusLockIcon}]`);
    this.unUsableStatusParent = Selector('[data-tc-status="Unusable"]');
    this.unUsableStatusText = this.unUsableStatusParent.find(`[${this.statusName}]`);
    this.unUsableStatusTag = this.unUsableStatusParent.find(`[${this.statusTag}]`);
    this.unUsableLockIcon = this.unUsableStatusParent.find(`[${this.statusLockIcon}]`);
    this.inputStatusName = Selector(`[${this.newCustomStatusParent}] [${this.inputField}]`);
    this.dropDownStatus = Selector(`[${this.newCustomStatusParent}] [${this.iconDropDown}]`);
    this.editInputStatusName = Selector(`[${this.editStatusPopup}] [${this.inputField}]`);
    this.editDropDownStatus = Selector(`[${this.editStatusPopup}] [${this.iconDropDown}]`);
    this.editStatusTitle = Selector(`[${this.editStatusPopup}] h2`);
    this.addStatusBtn = Selector('[data-tc-save-form-btn="+ Add Custom Status"]');
    this.editStatusBtn = Selector('[data-tc-save-form-btn="Update"]');
    this.notifyPopup = Selector('.notification-content');
    this.statusDropDownIcon = Selector(`[${this.usageStatusBox}] [data-tc-dropdown="asset statuses"]`);
    this.viewStatusInline = Selector(`[${this.assetInlineHeader}] [data-tc-view="asset status"]`);
    this.availabilityFilter = Selector('[data-tc-filter="Availability"]');
    this.statusModifyTitle = Selector('[data-tc-title="modify assets in status"] h2');
    this.doneStatusButton = Selector('[data-tc-button="done status"]');
  }

  async naviToAssetStatus() {
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(1000)
      .hover(astType.settingMenu)
      .click(astType.settingMenu)
      .wait(500)
      .hover(this.statusMenu)
      .click(this.statusMenu)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/managed_assets/settings/statuses');
  }

  async assetStatusValidate() {
    await t.expect(golVari.currentpageurl()).contains('/managed_assets/settings/statuses')
      .hover(this.mainHeading)
      .expect(this.mainHeading.innerText).eql('Status Customization')
      .expect(this.headingText.innerText).eql('Add your own custom statuses to the default list of status.')
      .hover(this.customStatusText)
      .expect(this.customStatusText.innerText).eql('New custom status')
      .hover(this.activeStatusText)
      .expect(this.activeStatusText.innerText).eql('Active statuses')
      .wait(500)
      .hover(this.inUseStatusParent)
      .expect(this.inUseStatusText.innerText).eql('In Use')
      .expect(this.inUseStatusTag.innerText).eql(`${this.defaultText}`)
      .hover(this.rTUseStatusParent)
      .expect(this.rTUseStatusText.innerText).eql('Ready to Use')
      .expect(this.rTUseStatusTag.innerText).eql(`${this.defaultText}`)
      .hover(this.nAUseStatusParent)
      .expect(this.nAUseStatusText.innerText).eql('Needs Attention')
      .expect(this.nAUseStatusTag.innerText).eql(`${this.defaultText}`)
      .hover(this.unUsableStatusParent)
      .expect(this.unUsableStatusText.innerText).eql('Unusable')
      .expect(this.unUsableStatusTag.innerText).eql(`${this.defaultText}`);
  }

  async addAssetStatus(statusName, statusIcon) {
    const selectIcon = this.dropDownStatus.find(`[data-tc-icons="${statusIcon}"]`);
    const newStatusParent = Selector(`[data-tc-status="${statusName}"]`);
    const viewStatusName = newStatusParent.find(`[${this.statusName}]`);
    const viewStatusIcon = newStatusParent.find(`[data-tc-icon="${statusIcon}"]`);
    await t.expect(golVari.currentpageurl()).contains('/managed_assets/settings/statuses')
      .hover(this.inputStatusName)
      .typeText(this.inputStatusName, statusName, { paste: true })
      .hover(this.dropDownStatus)
      .click(this.dropDownStatus)
      .wait(500)
      .click(selectIcon)
      .wait(500)
      .hover(this.addStatusBtn)
      .click(this.addStatusBtn)
      .wait(500)
      .hover(newStatusParent)
      .expect(viewStatusName.innerText).eql(statusName)
      .expect(viewStatusIcon.visible).ok('Check the status icon is visible or not')
      .wait(500);
  }

  async validateNameNType(statusName, statusIcon) {
    const selectIcon = this.dropDownStatus.find(`[data-tc-icons="${statusIcon}"]`);
    await t.expect(golVari.currentpageurl()).contains('/managed_assets/settings/statuses')
      .hover(this.inputStatusName)
      .typeText(this.inputStatusName, statusName, { paste: true })
      .hover(this.dropDownStatus)
      .click(this.dropDownStatus)
      .wait(500)
      .click(selectIcon)
      .wait(500)
      .hover(this.addStatusBtn)
      .click(this.addStatusBtn)
      .wait(500)
      .hover(this.notifyPopup)
      .expect(this.notifyPopup.innerText).contains('Sorry, there was an error adding a new status. Name has already been taken')
      .wait(500);
  }

  async checkDefaultNotEditable() {
    await t.expect(golVari.currentpageurl()).contains('/managed_assets/settings/statuses')
      .hover(this.inUseStatusParent)
      .expect(this.inUseStatusText.innerText).eql('In Use')
      .expect(this.inUseStatusTag.innerText).eql(`${this.defaultText}`)
      .hover(this.inUseLockIcon)
      .expect(this.inUseLockIcon.visible).ok('In use status not editable')
      .hover(this.rTUseStatusParent)
      .expect(this.rTUseStatusText.innerText).eql('Ready to Use')
      .expect(this.rTUseStatusTag.innerText).eql(`${this.defaultText}`)
      .hover(this.rtLockIcon)
      .expect(this.rtLockIcon.visible).ok('Ready to use status not editable')
      .hover(this.nAUseStatusParent)
      .expect(this.nAUseStatusText.innerText).eql('Needs Attention')
      .expect(this.nAUseStatusTag.innerText).eql(`${this.defaultText}`)
      .hover(this.nAUseLockIcon)
      .expect(this.nAUseLockIcon.visible).ok('Need attention status not editable')
      .hover(this.unUsableStatusParent)
      .expect(this.unUsableStatusText.innerText).eql('Unusable')
      .expect(this.unUsableStatusTag.innerText).eql(`${this.defaultText}`)
      .hover(this.unUsableLockIcon)
      .expect(this.unUsableLockIcon.visible).ok('Unusable status not editable');
  }

  async editAssetStatus(statusName, editStatusName, editStatusIcon) {
    const statusParent = Selector(`[data-tc-status="${statusName}"]`);
    const viewStatusName = statusParent.find(`[${this.statusName}]`);
    const editStatusParent = Selector(`[data-tc-status="${editStatusName}"]`);
    const viewEditStatusName = editStatusParent.find(`[${this.statusName}]`);
    const viewEditStatusIcon = editStatusParent.find(`[data-tc-icon="${editStatusIcon}"]`);
    const selectEditIcon = Selector(`[data-tc-icons="${editStatusIcon}"]`);
    const editIcon = statusParent.find(`[${naviBar.editIcon}]`);
    await t.expect(golVari.currentpageurl()).contains('/managed_assets/settings/statuses')
      .hover(this.activeStatusText)
      .expect(this.activeStatusText.innerText).eql('Active statuses')
      .wait(1000)
      .hover(statusParent)
      .expect(viewStatusName.innerText).eql(statusName)
      .wait(1000)
      .hover(editIcon)
      .click(editIcon)
      .wait(500)
      .expect(this.editStatusTitle.innerText).eql('Edit Status')
      .hover(this.editInputStatusName)
      .typeText(this.editInputStatusName, editStatusName, { replace: true })
      .hover(this.editDropDownStatus)
      .click(this.editDropDownStatus)
      .wait(500)
      .click(selectEditIcon)
      .wait(500)
      .hover(this.editStatusBtn)
      .click(this.editStatusBtn)
      .wait(500)
      .hover(editStatusParent)
      .expect(viewEditStatusName.innerText).eql(editStatusName)
      .expect(viewEditStatusIcon.exists).ok('Check the status icon is visible or not')
      .wait(500);
  }

  async applyStatusInAsset(assetName, statusName) {
    const selectAsset = Selector(`[data-tc-asset-box-link="${assetName}"]`);
    const selectStatus = Selector(`[${this.usageStatusBox}] [data-tc-status-option="${statusName}"]`);
    const selectFilter = Selector(`[data-tc-filters-holder="Availability"] [data-tc-filter-option="${statusName}"]`);
    const viewAppliedFilter = Selector(`[data-tc-applied-filter="${statusName}"]`);
    const removeFilter = Selector(`[data-tc-remove-filter="${statusName}"]`);
    const checkRemovedFilter = Selector(`[data-tc-applied-filter="${statusName}"]`);
    await t.hover(addAstPage.assetTab)
      .click(addAstPage.assetTab)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
      .hover(selectAsset)
      .click(selectAsset)
      .wait(1000)
      .expect(addAstPage.viewAstName.innerText).eql(assetName)
      .hover(this.statusDropDownIcon)
      .click(this.statusDropDownIcon)
      .wait(500)
      .click(selectStatus)
      .wait(500)
      .hover(this.statusDropDownIcon)
      .expect(this.statusDropDownIcon.innerText).contains(statusName)
      .hover(this.viewStatusInline)
      .expect(this.viewStatusInline.innerText).contains(statusName)
      .wait(500)
      .hover(addAstPage.backAstLink)
      .click(addAstPage.backAstLink)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .hover(delAstPage.filterBtn)
      .click(delAstPage.filterBtn)
      .wait(500)
      .hover(this.availabilityFilter)
      .click(this.availabilityFilter)
      .wait(500)
      .click(selectFilter)
      .wait(1000)
      .expect(viewAppliedFilter.innerText).contains(statusName)
      .hover(selectAsset)
      .expect(selectAsset.exists).ok('Asset is available')
      .hover(removeFilter)
      .click(removeFilter)
      .wait(1000)
      .expect(checkRemovedFilter.exists).notOk();
  }

  async viewStatusChangesInAsset(assetName, statusName) {
    const selectAsset = Selector(`[data-tc-asset-box-link="${assetName}"]`);
    const selectFilter = Selector(`[data-tc-filters-holder="Availability"] [data-tc-filter-option="${statusName}"]`);
    const viewAppliedFilter = Selector(`[data-tc-applied-filter="${statusName}"]`);
    const removeFilter = Selector(`[data-tc-remove-filter="${statusName}"]`);
    const checkRemovedFilter = Selector(`[data-tc-applied-filter="${statusName}"]`);
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .wait(1000)
      .hover(addAstPage.assetTab)
      .click(addAstPage.assetTab)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
      .hover(delAstPage.filterBtn)
      .click(delAstPage.filterBtn)
      .wait(500)
      .hover(this.availabilityFilter)
      .click(this.availabilityFilter)
      .wait(500)
      .click(selectFilter)
      .wait(1000)
      .expect(viewAppliedFilter.innerText).contains(statusName)
      .hover(selectAsset)
      .expect(selectAsset.exists).ok('Asset is available')
      .click(selectAsset)
      .expect(addAstPage.viewAstName.innerText).eql(assetName)
      .hover(this.statusDropDownIcon)
      .expect(this.statusDropDownIcon.innerText).contains(statusName)
      .hover(this.viewStatusInline)
      .expect(this.viewStatusInline.innerText).contains(statusName)
      .wait(500)
      .hover(addAstPage.backAstLink)
      .click(addAstPage.backAstLink)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .hover(removeFilter)
      .click(removeFilter)
      .wait(1000)
      .expect(checkRemovedFilter.exists).notOk();
  }

  async removeAssetStatus(statusName) {
    const newStatusParent = Selector(`[data-tc-status="${statusName}"]`);
    const viewStatusName = newStatusParent.find(`[${this.statusName}]`);
    const deactivateIcon = newStatusParent.find('[data-tc-icon="deactivate"]');
    await t.expect(golVari.currentpageurl()).contains('/managed_assets/settings/statuses')
      .wait(1000)
      .hover(this.activeStatusText)
      .expect(this.activeStatusText.innerText).eql('Active statuses')
      .wait(500)
      .expect(viewStatusName.innerText).eql(statusName)
      .wait(1000)
      .hover(deactivateIcon)
      .click(deactivateIcon)
      .wait(500)
      .expect(viewStatusName.exists).notOk('Check the status is delete or not')
      .wait(500);
  }

  async removeNChangeStatus(assetName, statusName) {
    const assetRow = Selector(`[data-tc-asset="${assetName}"]`);
    const viewAssetName = assetRow.find('[data-tc-view="asset name row"]');
    const statusDropDown = assetRow.find('[data-tc-drop-down="asset types"]');
    const selectStatus = Selector(`[data-tc-drop-down="asset types"] [data-tc-options="${statusName}"]`);
    const newStatusParent = Selector(`[data-tc-status="${statusName}"]`);
    const viewStatusName = newStatusParent.find(`[${this.statusName}]`);
    const deactivateIcon = newStatusParent.find('[data-tc-icon="deactivate"]');
    await t.expect(golVari.currentpageurl()).contains('/managed_assets/settings/statuses')
      .wait(1000)
      .hover(this.activeStatusText)
      .expect(this.activeStatusText.innerText).eql('Active statuses')
      .wait(500)
      .expect(viewStatusName.innerText).eql(statusName)
      .wait(1000)
      .hover(deactivateIcon)
      .click(deactivateIcon)
      .wait(1000)
      .hover(this.statusModifyTitle)
      .expect(this.statusModifyTitle.innerText).contains('Modify assets')
      .wait(500)
      .hover(viewAssetName)
      .expect(viewAssetName.innerText).eql(assetName)
      .hover(statusDropDown)
      .click(statusDropDown)
      .wait(500)
      .click(selectStatus)
      .wait(500)
      .hover(this.doneStatusButton)
      .click(this.doneStatusButton)
      .wait(500)
      .expect(viewStatusName.exists).notOk('Check the status is delete or not');
  }
} export default assetStatus
