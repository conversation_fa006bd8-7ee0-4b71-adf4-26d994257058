import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import addAssetType from './add_asset_type';
import addAsset from './add_assets';
import deleteAsset from './delete_assets';

const golVari = new golvar();
const naviBar = new navbar();
const addType = new addAssetType();
const addAstPage = new addAsset();
const delAstPage = new deleteAsset();

class assetLifeCycle {

  constructor() {
    this.customSelector = '[data-tc-title="create new lifecycle"]';
    this.editCustomSelector = '[data-tc-title="edit lifecycle"]';
    this.addBtn = '[data-tc-btn="add new life cycle"]';
    this.viewName = '[data-tc-view-name]';
    this.viewPrice = '[data-tc-view-data="purchasePrice"]';
    this.viewCost = '[data-tc-view-data="replacementCost"]';
    this.viewValue = '[data-tc-view-data="salvage"]';
    this.viewUsefulLife = '[data-tc-view-data="usefulLife"]';
    this.viewEndLife = '[data-tc-view-data="approachingEndOfLife"]';
    this.lifeCycleTab = Selector('[data-tc-links="lifecycle_management"]');
    this.customCycleTitle = Selector('[data-tc-title="Custom Lifecycles"]');
    this.addCustomBtn = Selector(`[data-tc-area="custom life cycle"] ${this.addBtn}`);
    this.customNewModalTitle = Selector(`${this.customSelector} h2`);
    this.customNameField = Selector(`${this.customSelector} [data-tc-field="life cycle name"]`);
    this.customPurchasePriceField = Selector(`${this.customSelector} [data-tc-field="purchase price"]`);
    this.customReplacementCostField = Selector(`${this.customSelector} [data-tc-field="replacement cost"]`);
    this.customSalvageValueField = Selector(`${this.customSelector} [data-tc-field="salvage value"]`);
    this.customUsefulLifeField = Selector(`${this.customSelector} [data-tc-field="useful life"]`);
    this.customEndLifeField = Selector(`${this.customSelector} [data-tc-field="approaching life"]`);
    this.customCreateBtn = Selector(`${this.customSelector} [data-tc-save-form-btn="Create Lifecycle"]`);
    this.editBtn = Selector('[data-tc-btn="Edit"]');
    this.editTitle = Selector(`${this.editCustomSelector} h2`);
    this.updateCustomBtn = Selector('[data-tc-save-form-btn="Update Lifecycle"]');
    this.customEditNameField = Selector(`${this.editCustomSelector} [data-tc-field="life cycle name"]`);
    this.customEditPurchasePriceField = Selector(`${this.editCustomSelector} [data-tc-field="purchase price"]`);
    this.customEditReplacementCostField = Selector(`${this.editCustomSelector} [data-tc-field="replacement cost"]`);
    this.customEditSalvageValueField = Selector(`${this.editCustomSelector} [data-tc-field="salvage value"]`);
    this.customEditUsefulLifeField = Selector(`${this.editCustomSelector} [data-tc-field="useful life"]`);
    this.customEditEndLifeField = Selector(`${this.editCustomSelector} [data-tc-field="approaching life"]`);
    this.deleteBtn = Selector('[data-tc-btn="Delete"]');
    this.deleteTitle = Selector('[data-tc-title="delete lifecycle"] h2');
    this.confirmDeleteBtn = Selector('[data-tc-btn="delete lifecycle"]');
    this.confirmUpdate = Selector('[data-tc-btn="delete lifecycle"]').nth(1);
    this.costDeperBox = Selector('[data-tc-box="cost depreciation"]');
    this.viewCostDeperTitle = this.costDeperBox.find('h5');
    this.editCostDeprIcon = Selector('[data-tc-icon="edit cost depreciation"]');
    this.costDeprModal = Selector('[data-tc-title="Cost & Depreciation"]');
    this.viewEditModalTitle = this.costDeprModal.find('h2');
    this.customLifeDropDown = Selector('[data-tc-label="custom life cycle"]');
    this.saveCostBtn = Selector('[data-tc-btn="save cost"]');
    this.notifyPopUp = Selector('.notification-content');
  }

  async navigateToLifeCycle() {
    await t.hover(naviBar.assetsbtn)
      .wait(1000)
      .click(naviBar.assetsbtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
      .hover(addType.settingMenu)
      .click(addType.settingMenu)
      .wait(500)
      .hover(this.lifeCycleTab)
      .click(this.lifeCycleTab)
      .expect(golVari.currentpageurl()).contains('/managed_assets/settings/lifecycle_management');
  }

  async addCustomCycle(name, purchasePrice, replacementCost, salvageValue, usefulLife, endLife) {
    const rowSelector = `[data-tc-row-data="${name}"]`;
    const viewName = Selector(`${rowSelector} ${this.viewName}`);
    const viewPrice = Selector(`${rowSelector} ${this.viewPrice}`);
    const viewCost = Selector(`${rowSelector} ${this.viewCost}`);
    const viewValue = Selector(`${rowSelector} ${this.viewValue}`);
    const viewUsefulLife = Selector(`${rowSelector} ${this.viewUsefulLife}`);
    const viewEndLife = Selector(`${rowSelector} ${this.viewEndLife}`);
    await t.expect(golVari.currentpageurl()).contains('/managed_assets/settings/lifecycle_management')
      .hover(this.customCycleTitle)
      .expect(this.customCycleTitle.innerText).eql('Custom Lifecycles')
      .wait(200)
      .hover(this.addCustomBtn)
      .click(this.addCustomBtn)
      .wait(500)
      .hover(this.customNewModalTitle)
      .expect(this.customNewModalTitle.innerText).eql('Create New Lifecycle')
      .wait(500)
      .typeText(this.customNameField, name, { place: true })
      .typeText(this.customPurchasePriceField, purchasePrice, { place: true })
      .typeText(this.customReplacementCostField, replacementCost, { place: true })
      .typeText(this.customSalvageValueField, salvageValue, { place: true })
      .typeText(this.customUsefulLifeField, usefulLife, { place: true })
      .typeText(this.customEndLifeField, endLife, { place: true })
      .wait(500)
      .hover(this.customCreateBtn)
      .click(this.customCreateBtn)
      .wait(500)
      .hover(viewName)
      .expect(viewName.innerText).eql(name)
      .expect(viewPrice.innerText).eql(`$${purchasePrice}`)
      .expect(viewCost.innerText).eql(`$${replacementCost}`)
      .expect(viewValue.innerText).eql(`$${salvageValue}`)
      .expect(viewUsefulLife.innerText).eql(`${usefulLife} Years`)
      .expect(viewEndLife.innerText).eql(`${endLife} Months`)
      .wait(500);
  }

  async editCustomCycle(name, editName, purchasePrice, replacementCost, salvageValue, usefulLife, endLife) {
    const rowSelector = `[data-tc-row-data="${name}"]`;
    const editRowSelector = `[data-tc-row-data="${editName}"]`;
    const viewName = Selector(`${rowSelector} ${this.viewName}`);
    const dotIcon = Selector(`${rowSelector} [data-tc-icon="ellipsis life cycle"]`);
    const viewEditName = Selector(`${editRowSelector} ${this.viewName}`);
    const viewPrice = Selector(`${editRowSelector} ${this.viewPrice}`);
    const viewCost = Selector(`${editRowSelector} ${this.viewCost}`);
    const viewValue = Selector(`${editRowSelector} ${this.viewValue}`);
    const viewUsefulLife = Selector(`${editRowSelector} ${this.viewUsefulLife}`);
    const viewEndLife = Selector(`${editRowSelector} ${this.viewEndLife}`);
    await t.expect(golVari.currentpageurl()).contains('/managed_assets/settings/lifecycle_management')
      .hover(this.customCycleTitle)
      .expect(this.customCycleTitle.innerText).eql('Custom Lifecycles')
      .wait(200)
      .hover(viewName)
      .expect(viewName.innerText).eql(name)
      .hover(dotIcon)
      .click(dotIcon)
      .wait(500)
      .hover(this.editBtn)
      .click(this.editBtn)
      .wait(500)
      .expect(this.editTitle.innerText).eql('Edit Lifecycle')
      .wait(500)
      .typeText(this.customEditNameField, editName, { replace: true })
      .typeText(this.customEditPurchasePriceField, purchasePrice, { replace: true })
      .typeText(this.customEditReplacementCostField, replacementCost, { replace: true })
      .typeText(this.customEditSalvageValueField, salvageValue, { replace: true })
      .typeText(this.customEditUsefulLifeField, usefulLife, { replace: true })
      .typeText(this.customEditEndLifeField, endLife, { replace: true })
      .wait(500)
      .hover(this.updateCustomBtn)
      .click(this.updateCustomBtn)
      .wait(1000)
      .hover(viewEditName)
      .expect(viewEditName.innerText).eql(editName)
      .expect(viewPrice.innerText).eql(`$${purchasePrice}`)
      .expect(viewCost.innerText).eql(`$${replacementCost}`)
      .expect(viewValue.innerText).eql(`$${salvageValue}`)
      .expect(viewUsefulLife.innerText).eql(`${usefulLife} Years`)
      .expect(viewEndLife.innerText).eql(`${endLife} Months`)
      .wait(500);
  }

  async viewCustomChanges(assetName, purchasePrice, replacementCost, salvageValue, usefulLife, endLife) {
    const asset = Selector(`[data-tc-asset-box-link="${assetName}"]`);
    await t.hover(delAstPage.assetTab)
      .click(delAstPage.assetTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/managed_assets/')
      .hover(asset)
      .click(asset)
      .wait(500)
      .expect(addAstPage.viewAstName.innerText).eql(assetName)
      .hover(this.viewCostDeperTitle)
      .expect(this.viewCostDeperTitle.innerText).eql('Cost & Depreciation')
      .hover(addAstPage.viewPurchasePrice)
      .expect(addAstPage.viewPurchasePrice.innerText).eql(`$${purchasePrice}`)
      .hover(addAstPage.viewReplacementCost)
      .expect(addAstPage.viewReplacementCost.innerText).eql(`$${replacementCost}`)
      .hover(addAstPage.viewSalvage)
      .expect(addAstPage.viewSalvage.innerText).eql(`$${salvageValue}`)
      .hover(addAstPage.viewUsefulLife)
      .expect(addAstPage.viewUsefulLife.innerText).eql(`${usefulLife} years`)
      .hover(addAstPage.viewEndLife)
      .expect(addAstPage.viewEndLife.innerText).eql(`${endLife} months`)
      .wait(500)
      .click(addAstPage.backAstLink);
  }

  async addCustomCycleToAsset(name, assetName, purchasePrice, replacementCost, salvageValue, usefulLife, endLife) {
    const asset = Selector(`[data-tc-asset-box-link="${assetName}"]`);
    const selectLifeCycle = this.customLifeDropDown.find('li').withText(name);
    await t.hover(delAstPage.assetTab)
      .click(delAstPage.assetTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/managed_assets/')
      .hover(asset)
      .click(asset)
      .wait(500)
      .expect(addAstPage.viewAstName.innerText).eql(assetName)
      .hover(this.viewCostDeperTitle)
      .expect(this.viewCostDeperTitle.innerText).eql('Cost & Depreciation')
      .hover(this.editCostDeprIcon)
      .click(this.editCostDeprIcon)
      .wait(500)
      .expect(this.viewEditModalTitle.innerText).eql('Cost & Depreciation')
      .hover(this.customLifeDropDown)
      .click(this.customLifeDropDown)
      .wait(200)
      .click(selectLifeCycle)
      .wait(200)
      .hover(this.saveCostBtn)
      .click(this.saveCostBtn)
      .wait(500)
      .hover(addAstPage.viewPurchasePrice)
      .expect(addAstPage.viewPurchasePrice.innerText).eql(`$${purchasePrice}`)
      .hover(addAstPage.viewReplacementCost)
      .expect(addAstPage.viewReplacementCost.innerText).eql(`$${replacementCost}`)
      .hover(addAstPage.viewSalvage)
      .expect(addAstPage.viewSalvage.innerText).eql(`$${salvageValue}`)
      .hover(addAstPage.viewUsefulLife)
      .expect(addAstPage.viewUsefulLife.innerText).eql(`${usefulLife} years`)
      .hover(addAstPage.viewEndLife)
      .expect(addAstPage.viewEndLife.innerText).eql(`${endLife} months`)
      .wait(500)
      .click(addAstPage.backAstLink);
  }

  async deleteCustomLifeCycle(name) {
    const rowSelector = `[data-tc-row-data="${name}"]`;
    const viewName = Selector(`${rowSelector} ${this.viewName}`);
    const dotIcon = Selector(`${rowSelector} [data-tc-icon="ellipsis life cycle"]`);
    await t.expect(golVari.currentpageurl()).contains('/managed_assets/settings/lifecycle_management')
      .hover(this.customCycleTitle)
      .expect(this.customCycleTitle.innerText).eql('Custom Lifecycles')
      .wait(200)
      .hover(viewName)
      .expect(viewName.innerText).eql(name)
      .wait(500)
      .hover(dotIcon)
      .click(dotIcon)
      .wait(500)
      .hover(this.deleteBtn)
      .click(this.deleteBtn)
      .wait(500)
      .hover(this.deleteTitle)
      .expect(this.deleteTitle.innerText).eql('Delete Lifecycle')
      .hover(this.confirmDeleteBtn)
      .click(this.confirmDeleteBtn)
      .wait(500)
      .expect(viewName.exists).notOk('The life cycle is deleted or not')
  }
} export default assetLifeCycle
