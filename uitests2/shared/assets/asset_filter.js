import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import deleteAsset from './delete_assets';

const golVari = new golvar();
const naviBar = new navbar();
const delAstPage = new deleteAsset();


class applyAssetFilters {

  constructor() {

  }

  async filterAsset(filterType, valueItem) {
    const filterType1 = Selector(`[data-tc-filter="${filterType}"]`);
    const selectFilter = Selector(`[data-tc-filters-holder="${filterType}"] [data-tc-filter-option="${valueItem}"]`);
    const viewAppliedFilter = Selector(`[data-tc-applied-filter="${valueItem}"]`);
    await t.hover(delAstPage.assetTab)
      .click(delAstPage.assetTab)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(2000)
      .hover(delAstPage.filterBtn)
      .click(delAstPage.filterBtn)
      .wait(1000)
      .click(filterType1)
      .wait(1000)
      .click(selectFilter)
      .wait(1000)
      .expect(viewAppliedFilter.innerText).contains(valueItem);
  }

  async removeFilter(filter) {
    const removeFilters = Selector(`[data-tc-remove-filter="${filter}"]`);
    const viewAppliedFilter = Selector(`[data-tc-applied-filter="${filter}"]`);
    await t.hover(delAstPage.assetTab)
      .click(delAstPage.assetTab)
      .wait(1000)
      .click(removeFilters)
      .wait(2000)
      .expect(viewAppliedFilter.exists).notOk();
  }
} export default applyAssetFilters
