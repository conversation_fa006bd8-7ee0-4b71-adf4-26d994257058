import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import addAssetType from './add_asset_type';
import indexPageAction from '../help_desk/ticket_index_page_action';
import deleteAsset from './delete_assets';

const golVari = new golvar();
const naviBar = new navbar();
const astType = new addAssetType();
const indexTicPage = new indexPageAction();
const delAstPage = new deleteAsset();

class assetListColumn {

  constructor() {
    this.titleListView = Selector('[data-tc-title="List View Columns"]');
    this.columnTab = Selector('[data-tc-menu="List-View Columns"]');
    this.settingsTab = Selector('[data-tc-asset="settings menu"]');
    this.settingIcon = Selector('[data-tc-icon="customize table"]');
    this.customTableIcon = Selector('[data-tc-icon="customize drop down"]');
    this.viewTypeActive = Selector('[data-tc-assets-active-column-name="Type"]');
    this.viewAssetNameActive = Selector('[data-tc-assets-active-column-name="Asset Name"]');
    this.viewSourceActive = Selector('[data-tc-assets-active-column-name="Source"]');
    this.viewTagsActive = Selector('[data-tc-assets-active-column-name="Tags"]');
    this.viewWarrantyStatusActive = Selector('[data-tc-assets-active-column-name="Warranty Status"]');
    this.viewNotesActive = Selector('[data-tc-assets-active-column-name="Notes"]');
    this.viewLocationActive = Selector('[data-tc-assets-active-column-name="Location"]');
    this.viewDepartmentActive = Selector('[data-tc-assets-active-column-name="Department"]');
    this.viewModelActive = Selector('[data-tc-assets-active-column-name="Model"]');
    this.viewMacAddressActive = Selector('[data-tc-assets-active-column-name="Mac Addresses"]');
    this.viewStatusActive = Selector('[data-tc-assets-active-column-name="Usage Status"]');
    this.viewManufacturerActive = Selector('[data-tc-assets-active-column-name="Manufacturer"]');
    this.viewIpAddressActive = Selector('[data-tc-assets-active-column-name="IP Address"]');
    this.viewOSActive = Selector('[data-tc-assets-active-column-name="Operating System"]');
    this.viewProductNumberActive = Selector('[data-tc-assets-active-column-name="Product Number"]');
    this.viewSerialNumberActive = Selector('[data-tc-assets-active-column-name="Serial Number"]');
    this.viewMachSerialNumberActive = Selector('[data-tc-assets-active-column-name="Machine Serial Number"]');
    this.viewHwDetailTypeActive = Selector('[data-tc-assets-active-column-name="Hardware Detail type"]');
    this.viewImpactActive = Selector('[data-tc-assets-active-column-name="Impact"]');
    this.viewAstTagActive = Selector('[data-tc-assets-active-column-name="Asset Tag"]');
    this.viewAcquisitionDateActive = Selector('[data-tc-assets-active-column-name="Acquisition Date"]');
    this.viewArchivedActive = Selector('[data-tc-assets-active-column-name="Archived"]');
    this.viewInstallDateActive = Selector('[data-tc-assets-active-column-name="Install Date"]');
    this.viewUsedByActive = Selector('[data-tc-assets-active-column-name="Used By"]');
    this.viewManagedByActive = Selector('[data-tc-assets-active-column-name="Managed By"]');
    this.viewCostActive = Selector('[data-tc-assets-active-column-name="Purchase Price"]');
    this.viewFirmwareActive = Selector('[data-tc-assets-active-column-name="Firmware"]');
    this.viewWarrantyDateActive = Selector('[data-tc-assets-active-column-name="Warranty Date"]');
    this.viewNotesInactive = Selector('[data-tc-assets-inactive-column-name="Notes"]');
    this.viewLocationInactive = Selector('[data-tc-assets-inactive-column-name="Location"]');
    this.viewDepartmentInactive = Selector('[data-tc-assets-inactive-column-name="Department"]');
    this.viewModelInactive = Selector('[data-tc-assets-inactive-column-name="Model"]');
    this.viewMacAddressInactive = Selector('[data-tc-assets-inactive-column-name="Mac Addresses"]');
    this.viewStatusInactive = Selector('[data-tc-assets-inactive-column-name="Usage Status"]');
    this.viewManufacturerInactive = Selector('[data-tc-assets-inactive-column-name="Manufacturer"]');
    this.viewIpAddressInactive = Selector('[data-tc-assets-inactive-column-name="IP Address"]');
    this.viewOSInactive = Selector('[data-tc-assets-inactive-column-name="Operating System"]');
    this.viewProductNumberInactive = Selector('[data-tc-assets-inactive-column-name="Product Number"]');
    this.viewSerialNumberInactive = Selector('[data-tc-assets-inactive-column-name="Serial Number"]');
    this.viewMachSerialNumberInactive = Selector('[data-tc-assets-inactive-column-name="Machine Serial Number"]');
    this.viewHwDetailTypeInactive = Selector('[data-tc-assets-inactive-column-name="Hardware Detail type"]');
    this.viewImpactInactive = Selector('[data-tc-assets-inactive-column-name="Impact"]');
    this.viewAstTagInactive = Selector('[data-tc-assets-inactive-column-name="Asset Tag"]');
    this.viewAcquisitionDateInactive = Selector('[data-tc-assets-inactive-column-name="Acquisition Date"]');
    this.viewArchivedInactive = Selector('[data-tc-assets-inactive-column-name="Archived"]');
    this.viewInstallDateInactive = Selector('[data-tc-assets-inactive-column-name="Install Date"]');
    this.viewUsedByInactive = Selector('[data-tc-assets-inactive-column-name="Used By"]');
    this.viewManagedByInactive = Selector('[data-tc-assets-inactive-column-name="Managed By"]');
    this.viewCostInactive = Selector('[data-tc-assets-inactive-column-name="Purchase Price"]');
    this.viewFirmwareInactive = Selector('[data-tc-assets-inactive-column-name="Firmware"]');
    this.viewWarrantyDateInactive = Selector('[data-tc-assets-inactive-column-name="Warranty Date"]');
    this.addNotesColumn = Selector('[data-tc-list-view-column-activate-btn="Notes"]');
    this.addLocationColumn = Selector('[data-tc-list-view-column-activate-btn="Location"]');
    this.addDepartmentColumn = Selector('[data-tc-list-view-column-activate-btn="Department"]');
    this.addModelColumn = Selector('[data-tc-list-view-column-activate-btn="Model"]');
    this.addMacAddressColumn = Selector('[data-tc-list-view-column-activate-btn="Mac Addresses"]');
    this.addStatusColumn = Selector('[data-tc-list-view-column-activate-btn="Usage Status"]');
    this.addManufacturerColumn = Selector('[data-tc-list-view-column-activate-btn="Manufacturer"]');
    this.addIpAddressColumn = Selector('[data-tc-list-view-column-activate-btn="IP Address"]');
    this.addOSColumn = Selector('[data-tc-list-view-column-activate-btn="Operating System"]');
    this.addProductNumberColumn = Selector('[data-tc-list-view-column-activate-btn="Product Number"]');
    this.addSerialNumberColumn = Selector('[data-tc-list-view-column-activate-btn="Serial Number"]');
    this.addMachSerialNumberColumn = Selector('[data-tc-list-view-column-activate-btn="Machine Serial Number"]');
    this.addHwDetailTypeColumn = Selector('[data-tc-list-view-column-activate-btn="Hardware Detail type"]');
    this.addImpactColumn = Selector('[data-tc-list-view-column-activate-btn="Impact"]');
    this.addAstTagColumn = Selector('[data-tc-list-view-column-activate-btn="Asset Tag"]');
    this.addAcquisitionColumn = Selector('[data-tc-list-view-column-activate-btn="Acquisition Date"]');
    this.addArchivedColumn = Selector('[data-tc-list-view-column-activate-btn="Archived"]');
    this.addInstallDateColumn = Selector('[data-tc-list-view-column-activate-btn="Install Date"]');
    this.addUsedByColumn = Selector('[data-tc-list-view-column-activate-btn="Used By"]');
    this.addManagedByColumn = Selector('[data-tc-list-view-column-activate-btn="Managed By"]');
    this.addCostColumn = Selector('[data-tc-list-view-column-activate-btn="Purchase Price"]');
    this.addFirmwareColumn = Selector('[data-tc-list-view-column-activate-btn="Firmware"]');
    this.addWarrantyDateColumn = Selector('[data-tc-list-view-column-activate-btn="Warranty Date"]');
    this.removeNotesColumn = Selector('[data-tc-list-view-column-deactivate-btn="Notes"]');
    this.removeLocationColumn = Selector('[data-tc-list-view-column-deactivate-btn="Location"]');
    this.removeDepartmentColumn = Selector('[data-tc-list-view-column-deactivate-btn="Department"]');
    this.removeModelColumn = Selector('[data-tc-list-view-column-deactivate-btn="Model"]');
    this.removeMacAddressColumn = Selector('[data-tc-list-view-column-deactivate-btn="Mac Addresses"]');
    this.removeStatusColumn = Selector('[data-tc-list-view-column-deactivate-btn="Usage Status"]');
    this.removeManufacturerColumn = Selector('[data-tc-list-view-column-deactivate-btn="Manufacturer"]');
    this.removeIpAddressColumn = Selector('[data-tc-list-view-column-deactivate-btn="IP Address"]');
    this.removeOSColumn = Selector('[data-tc-list-view-column-deactivate-btn="Operating System"]');
    this.removeProductNumberColumn = Selector('[data-tc-list-view-column-deactivate-btn="Product Number"]');
    this.removeSerialNumberColumn = Selector('[data-tc-list-view-column-deactivate-btn="Serial Number"]');
    this.removeMachSerialNumberColumn = Selector('[data-tc-list-view-column-deactivate-btn="Machine Serial Number"]');
    this.removeHwDetailTypeColumn = Selector('[data-tc-list-view-column-deactivate-btn="Hardware Detail type"]');
    this.removeImpactColumn = Selector('[data-tc-list-view-column-deactivate-btn="Impact"]');
    this.removeAstTagColumn = Selector('[data-tc-list-view-column-deactivate-btn="Asset Tag"]');
    this.removeAcquisitionColumn = Selector('[data-tc-list-view-column-deactivate-btn="Acquisition Date"]');
    this.removeArchivedColumn = Selector('[data-tc-list-view-column-deactivate-btn="Archived"]');
    this.removeInstallDateColumn = Selector('[data-tc-list-view-column-deactivate-btn="Install Date"]');
    this.removeUsedByColumn = Selector('[data-tc-list-view-column-deactivate-btn="Used By"]');
    this.removeManagedByColumn = Selector('[data-tc-list-view-column-deactivate-btn="Managed By"]');
    this.removeCostColumn = Selector('[data-tc-list-view-column-deactivate-btn="Purchase Price"]');
    this.removeFirmwareColumn = Selector('[data-tc-list-view-column-deactivate-btn="Firmware"]');
    this.removeWarrantyDateColumn = Selector('[data-tc-list-view-column-deactivate-btn="Warranty Date"]');
    this.previewType = Selector('[data-tc-preview-active-column="Type"]');
    this.previewAssetName = Selector('[data-tc-preview-active-column="Asset Name"]');
    this.previewSource = Selector('[data-tc-preview-active-column="Source"]');
    this.previewTags = Selector('[data-tc-preview-active-column="Tags"]');
    this.previewWarrantyStatus = Selector('[data-tc-preview-active-column="Warranty Status"]');
    this.previewNotes = Selector('[data-tc-preview-active-column="Notes"]');
    this.previewLocation = Selector('[data-tc-preview-active-column="Location"]');
    this.previewDepartment = Selector('[data-tc-preview-active-column="Department"]');
    this.previewModel = Selector('[data-tc-preview-active-column="Model"]');
    this.previewMacAddress = Selector('[data-tc-preview-active-column="Mac Addresses"]');
    this.previewStatus = Selector('[data-tc-preview-active-column="Usage Status"]');
    this.previewManufacturer = Selector('[data-tc-preview-active-column="Manufacturer"]');
    this.previewIpAddress = Selector('[data-tc-preview-active-column="IP Address"]');
    this.previewOperatingSystem = Selector('[data-tc-preview-active-column="Operating System"]');
    this.previewProductNumber = Selector('[data-tc-preview-active-column="Product Number"]');
    this.previewSerialNumber = Selector('[data-tc-preview-active-column="Serial Number"]');
    this.previewMachSerialNumber = Selector('[data-tc-preview-active-column="Machine Serial Number"]');
    this.previewHwdDetailType = Selector('[data-tc-preview-active-column="Hardware Detail type"]');
    this.previewImpact = Selector('[data-tc-preview-active-column="Impact"]');
    this.previewAssetTag = Selector('[data-tc-preview-active-column="Asset Tag"]');
    this.previewAcquisitionDate = Selector('[data-tc-preview-active-column="Acquisition Date"]');
    this.previewArchived = Selector('[data-tc-preview-active-column="Archived"]');
    this.previewInstallDate = Selector('[data-tc-preview-active-column="Install Date"]');
    this.previewUsedBy = Selector('[data-tc-preview-active-column="Used By"]');
    this.previewManagedBy = Selector('[data-tc-preview-active-column="Managed By"]');
    this.previewCost = Selector('[data-tc-preview-active-column="Purchase Price"]');
    this.previewFirmware = Selector('[data-tc-preview-active-column="Firmware"]');
    this.previewWarrantyDate = Selector('[data-tc-preview-active-column="Warranty Date"]');
    this.assetViewTab = Selector('[data-tc-links="assets_views"]');
    this.tableDataTab = Selector('[data-tc-links="Table Data"]');
  }

  async addAssetList(astColumn) {
    const viewInactiveCol = Selector(`[data-tc-assets-inactive-column-name="${astColumn}"]`);
    const addListIcon = Selector(`[data-tc-list-view-column-activate-btn="${astColumn}"]`);
    const previewCol = Selector(`[data-tc-preview-active-column="${astColumn}"]`);
    const viewActiveColn = Selector(`[data-tc-assets-active-column-name="${astColumn}"]`);
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
      .hover(astType.settingMenu)
      .click(astType.settingMenu)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('managed_assets/settings/asset_types')
      .wait(500)
      .click(this.assetViewTab)
      .wait(500)
      .click(this.tableDataTab)
      .wait(500)
      .hover(viewInactiveCol)
      .wait(1000)
      .hover(addListIcon)
      .click(addListIcon)
      .expect(astType.notifyPopUp.innerText).contains('Asset preferences have been saved successfully')
      .wait(500)
      .hover(previewCol)
      .wait(500)
      .hover(viewActiveColn)
      .wait(1000);
  }

  async removeAssetList(asstColumn) {
    const viewActiveCol = Selector(`[data-tc-assets-active-column-name="${asstColumn}"]`);
    const viewInactiveColn = Selector(`[data-tc-assets-inactive-column-name="${asstColumn}"]`);
    const removeListIcon = Selector(`[data-tc-list-view-column-deactivate-btn="${asstColumn}"]`);
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
      .hover(astType.settingMenu)
      .click(astType.settingMenu)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('managed_assets/settings/asset_types')
      .wait(500)
      .click(this.assetViewTab)
      .wait(500)
      .click(this.tableDataTab)
      .wait(500)
      .hover(viewActiveCol)
      .wait(1000)
      .hover(removeListIcon)
      .click(removeListIcon)
      .expect(astType.notifyPopUp.innerText).contains('Asset preferences have been saved successfully')
      .wait(500)
      .hover(viewInactiveColn)
      .wait(1000);
  }

  async addViewAssetColumn() {
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .wait(1000)
      .hover(delAstPage.assetTab)
      .click(delAstPage.assetTab)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(500)
      .hover(astType.settingMenu)
      .click(astType.settingMenu)
      .wait(1000)
      // .click(indexTicPage.listToggleIcon)
      // .wait(500)
      // .click(this.settingIcon)
      // .wait(500)
      // .click(this.customTableIcon)
      // .wait(500)
      .expect(golVari.currentpageurl()).contains('managed_assets/settings/asset_types')
      .wait(500)
      .click(this.assetViewTab)
      .wait(500)
      .click(this.tableDataTab)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('managed_assets/settings/table_data')
      .expect(this.viewTypeActive.innerText).contains('Type')
      .expect(this.viewAssetNameActive.innerText).contains('Asset Name')
      .expect(this.viewSourceActive.innerText).contains('Source')
      .expect(this.viewTagsActive.innerText).contains('Tags')
      .expect(this.viewWarrantyStatusActive.innerText).contains('Warranty Status')
      .wait(500)
      .hover(this.addNotesColumn)
      .click(this.addNotesColumn)
      .click(this.addLocationColumn)
      .click(this.addDepartmentColumn)
      .wait(500)
      .click(this.addModelColumn)
      .click(this.addMacAddressColumn)
      .wait(500)
      .hover(this.addStatusColumn)
      .click(this.addStatusColumn)
      .click(this.addManufacturerColumn)
      .expect(astType.notifyPopUp.innerText).contains('Asset preferences have been saved successfully')
      .wait(500)
      .click(this.addIpAddressColumn)
      .wait(500)
      .hover(this.addOSColumn)
      .click(this.addOSColumn)
      .click(this.addProductNumberColumn)
      .click(this.addSerialNumberColumn)
      .click(this.addMachSerialNumberColumn)
      .wait(500)
      .click(this.addHwDetailTypeColumn)
      .click(this.addImpactColumn)
      .click(this.addAstTagColumn)
      .click(this.addAcquisitionColumn)
      .click(this.addArchivedColumn)
      .click(this.addInstallDateColumn)
      .click(this.addUsedByColumn)
      .expect(astType.notifyPopUp.innerText).contains('Asset preferences have been saved successfully')
      .wait(500)
      .click(this.addManagedByColumn)
      .click(this.addCostColumn)
      .click(this.addFirmwareColumn)
      .click(this.addWarrantyDateColumn)
      .expect(astType.notifyPopUp.innerText).contains('Asset preferences have been saved successfully')
      .wait(500)
      .hover(this.previewType)
      .expect(this.previewType.visible).ok()
      .expect(this.previewAssetName.visible).ok()
      .expect(this.previewSource.visible).ok()
      .expect(this.previewTags.visible).ok()
      .expect(this.previewWarrantyStatus.visible).ok()
      .expect(this.previewNotes.visible).ok()
      .expect(this.previewLocation.visible).ok()
      .expect(this.previewDepartment.visible).ok()
      .expect(this.previewModel.visible).ok()
      .expect(this.previewMacAddress.visible).ok()
      .hover(this.previewMacAddress)
      .wait(500)
      .hover(this.previewStatus)
      .expect(this.previewStatus.visible).ok()
      .expect(this.previewManufacturer.visible).ok()
      .expect(this.previewIpAddress.visible).ok()
      .expect(this.previewOperatingSystem.visible).ok()
      .expect(this.previewProductNumber.visible).ok()
      .expect(this.previewSerialNumber.visible).ok()
      .expect(this.previewMachSerialNumber.visible).ok()
      .hover(this.previewHwdDetailType)
      .expect(this.previewHwdDetailType.visible).ok()
      .expect(this.previewImpact.visible).ok()
      .expect(this.previewAssetTag.visible).ok()
      .expect(this.previewAcquisitionDate.visible).ok()
      .expect(this.previewArchived.visible).ok()
      .hover(this.previewWarrantyDate)
      .expect(this.previewInstallDate.visible).ok()
      .expect(this.previewUsedBy.visible).ok()
      .expect(this.previewManagedBy.visible).ok()
      .expect(this.previewCost.visible).ok()
      .expect(this.previewFirmware.visible).ok()
      .expect(this.previewWarrantyDate.visible).ok()
      .wait(1000)
      .expect(this.viewNotesActive.innerText).contains('Notes')
      .expect(this.viewLocationActive.innerText).contains('Location')
      .expect(this.viewDepartmentActive.innerText).contains('Department')
      .expect(this.viewModelActive.innerText).contains('Model')
      .hover(this.viewModelActive)
      .expect(this.viewMacAddressActive.innerText).contains('Mac Addresses')
      .expect(this.viewStatusActive.innerText).contains('Usage Status')
      .hover(this.viewManufacturerActive)
      .expect(this.viewManufacturerActive.innerText).contains('Manufacturer')
      .expect(this.viewIpAddressActive.innerText).contains('IP Address')
      .expect(this.viewOSActive.innerText).contains('Operating System')
      .hover(this.viewUsedByActive)
      .wait(500)
      .expect(this.viewProductNumberActive.innerText).contains('Product Number')
      .expect(this.viewSerialNumberActive.innerText).contains('Serial Number')
      .expect(this.viewMachSerialNumberActive.innerText).contains('Machine Serial Number')
      .expect(this.viewHwDetailTypeActive.innerText).contains('Hardware Detail type')
      .expect(this.viewImpactActive.innerText).contains('Impact')
      .expect(this.viewAstTagActive.innerText).contains('Asset Tag')
      .expect(this.viewAcquisitionDateActive.innerText).contains('Acquisition Date')
      .expect(this.viewArchivedActive.innerText).contains('Archived')
      .expect(this.viewInstallDateActive.innerText).contains('Install Date')
      .expect(this.viewUsedByActive.innerText).contains('Used By')
      .expect(this.viewManagedByActive.innerText).contains('Managed By')
      .expect(this.viewCostActive.innerText).contains('Purchase Price')
      .expect(this.viewFirmwareActive.innerText).contains('Firmware')
      .expect(this.viewWarrantyDateActive.innerText).contains('Warranty Date')
      .wait(500);
  }

  async removeViewAllAssetColumn() {
    await t.hover(astType.settingMenu)
      .click(astType.settingMenu)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('managed_assets/settings/asset_types')
      .wait(500)
      .click(this.assetViewTab)
      .wait(500)
      .click(this.tableDataTab)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('managed_assets/settings/table_data')
      .expect(this.viewTypeActive.innerText).contains('Type')
      .expect(this.viewAssetNameActive.innerText).contains('Asset Name')
      .expect(this.viewSourceActive.innerText).contains('Source')
      .expect(this.viewTagsActive.innerText).contains('Tags')
      .expect(this.viewWarrantyStatusActive.innerText).contains('Warranty Status')
      .expect(this.viewNotesActive.innerText).contains('Notes')
      .expect(this.viewLocationActive.innerText).contains('Location')
      .expect(this.viewDepartmentActive.innerText).contains('Department')
      .expect(this.viewModelActive.innerText).contains('Model')
      .hover(this.viewModelActive)
      .expect(this.viewMacAddressActive.innerText).contains('Mac Addresses')
      .expect(this.viewStatusActive.innerText).contains('Usage Status')
      .hover(this.viewManufacturerActive)
      .expect(this.viewManufacturerActive.innerText).contains('Manufacturer')
      .expect(this.viewIpAddressActive.innerText).contains('IP Address')
      .expect(this.viewOSActive.innerText).contains('Operating System')
      .hover(this.viewUsedByActive)
      .wait(1000)
      .expect(this.viewProductNumberActive.innerText).contains('Product Number')
      .expect(this.viewSerialNumberActive.innerText).contains('Serial Number')
      .expect(this.viewMachSerialNumberActive.innerText).contains('Machine Serial Number')
      .expect(this.viewHwDetailTypeActive.innerText).contains('Hardware Detail type')
      .expect(this.viewImpactActive.innerText).contains('Impact')
      .expect(this.viewAstTagActive.innerText).contains('Asset Tag')
      .expect(this.viewAcquisitionDateActive.innerText).contains('Acquisition Date')
      .expect(this.viewArchivedActive.innerText).contains('Archived')
      .expect(this.viewInstallDateActive.innerText).contains('Install Date')
      .expect(this.viewUsedByActive.innerText).contains('Used By')
      .expect(this.viewManagedByActive.innerText).contains('Managed By')
      .expect(this.viewCostActive.innerText).contains('Purchase Price')
      .expect(this.viewFirmwareActive.innerText).contains('Firmware')
      .expect(this.viewWarrantyDateActive.innerText).contains('Warranty Date')
      .wait(1000)
      .click(this.removeNotesColumn)
      .click(this.removeLocationColumn)
      .click(this.removeDepartmentColumn)
      .click(this.removeModelColumn)
      .click(this.removeMacAddressColumn)
      .click(this.removeStatusColumn)
      .click(this.removeManufacturerColumn)
      .expect(astType.notifyPopUp.innerText).contains('Asset preferences have been saved successfully')
      .wait(500)
      .click(this.removeIpAddressColumn)
      .wait(500)
      .click(this.removeOSColumn)
      .click(this.removeProductNumberColumn)
      .click(this.removeSerialNumberColumn)
      .click(this.removeMachSerialNumberColumn)
      .wait(500)
      .click(this.removeHwDetailTypeColumn)
      .click(this.removeImpactColumn)
      .click(this.removeAstTagColumn)
      .click(this.removeAcquisitionColumn)
      .click(this.removeArchivedColumn)
      .click(this.removeInstallDateColumn)
      .click(this.removeUsedByColumn)
      .expect(astType.notifyPopUp.innerText).contains('Asset preferences have been saved successfully')
      .wait(500)
      .click(this.removeManagedByColumn)
      .click(this.removeCostColumn)
      .click(this.removeFirmwareColumn)
      .click(this.removeWarrantyDateColumn)
      .expect(astType.notifyPopUp.innerText).contains('Asset preferences have been saved successfully')
      .wait(500)
      .hover(this.titleListView)
      .wait(500)
      .hover(this.previewType)
      .expect(this.previewType.visible).ok()
      .hover(this.previewAssetName)
      .expect(this.previewAssetName.visible).ok()
      .hover(this.previewSource)
      .expect(this.previewSource.visible).ok()
      .hover(this.previewWarrantyStatus)
      .expect(this.previewTags.visible).ok()
      .expect(this.previewWarrantyStatus.visible).ok()
      .wait(500)
      .expect(this.previewNotes.exists).notOk({ timeout: 500 })
      .expect(this.previewLocation.exists).notOk()
      .expect(this.previewDepartment.exists).notOk()
      .expect(this.previewModel.exists).notOk()
      .expect(this.previewMacAddress.exists).notOk()
      .wait(500)
      .expect(this.previewStatus.exists).notOk({ timeout: 500 })
      .expect(this.previewManufacturer.exists).notOk()
      .expect(this.previewIpAddress.exists).notOk()
      .expect(this.previewOperatingSystem.exists).notOk()
      .expect(this.previewProductNumber.exists).notOk()
      .expect(this.previewSerialNumber.exists).notOk({ timeout: 500 })
      .expect(this.previewMachSerialNumber.exists).notOk()
      .expect(this.previewHwdDetailType.exists).notOk()
      .expect(this.previewImpact.exists).notOk()
      .expect(this.previewAssetTag.exists).notOk({ timeout: 500 })
      .expect(this.previewAcquisitionDate.exists).notOk()
      .expect(this.previewArchived.exists).notOk()
      .expect(this.previewInstallDate.exists).notOk()
      .expect(this.previewUsedBy.exists).notOk()
      .expect(this.previewManagedBy.exists).notOk()
      .expect(this.previewCost.exists).notOk()
      .expect(this.previewFirmware.exists).notOk()
      .expect(this.previewWarrantyDate.exists).notOk()
      .wait(500)
      .expect(this.viewNotesActive.exists).notOk()
      .expect(this.viewLocationActive.exists).notOk()
      .expect(this.viewDepartmentActive.exists).notOk({ timeout: 500 })
      .expect(this.viewModelActive.exists).notOk()
      .expect(this.viewMacAddressActive.exists).notOk()
      .expect(this.viewStatusActive.exists).notOk()
      .expect(this.viewManufacturerActive.exists).notOk()
      .expect(this.viewIpAddressActive.exists).notOk()
      .expect(this.viewOSActive.exists).notOk()
      .expect(this.viewProductNumberActive.exists).notOk()
      .expect(this.viewSerialNumberActive.exists).notOk({ timeout: 500 })
      .expect(this.viewMachSerialNumberActive.exists).notOk()
      .expect(this.viewHwDetailTypeActive.exists).notOk()
      .expect(this.viewImpactActive.exists).notOk()
      .expect(this.viewAstTagActive.exists).notOk()
      .expect(this.viewAcquisitionDateActive.exists).notOk()
      .expect(this.viewArchivedActive.exists).notOk()
      .expect(this.viewInstallDateActive.exists).notOk()
      .expect(this.viewUsedByActive.exists).notOk()
      .expect(this.viewManagedByActive.exists).notOk()
      .expect(this.viewCostActive.exists).notOk()
      .expect(this.viewFirmwareActive.exists).notOk()
      .expect(this.viewWarrantyDateActive.exists).notOk({ timeout: 500 })
      .wait(500)
      .hover(this.viewNotesInactive)
      .expect(this.viewNotesInactive.visible).ok()
      .expect(this.viewLocationInactive.visible).ok()
      .expect(this.viewDepartmentInactive.visible).ok()
      .expect(this.viewModelInactive.visible).ok()
      .expect(this.viewMacAddressInactive.visible).ok()
      .expect(this.viewStatusInactive.visible).ok()
      .expect(this.viewManufacturerInactive.visible).ok()
      .expect(this.viewIpAddressInactive.visible).ok()
      .expect(this.viewOSInactive.visible).ok()
      .hover(this.viewUsedByInactive)
      .expect(this.viewProductNumberInactive.visible).ok()
      .expect(this.viewSerialNumberInactive.visible).ok()
      .expect(this.viewMachSerialNumberInactive.visible).ok()
      .expect(this.viewHwDetailTypeInactive.visible).ok()
      .expect(this.viewImpactInactive.visible).ok()
      .expect(this.viewAstTagInactive.visible).ok()
      .expect(this.viewAcquisitionDateInactive.visible).ok()
      .expect(this.viewArchivedInactive.visible).ok()
      .expect(this.viewInstallDateInactive.visible).ok()
      .expect(this.viewUsedByInactive.visible).ok()
      .expect(this.viewManagedByInactive.visible).ok()
      .expect(this.viewCostInactive.visible).ok()
      .expect(this.viewFirmwareInactive.visible).ok()
      .expect(this.viewWarrantyDateInactive.visible).ok()
      .wait(500);
  }

  async checkDefaultListColumn() {
    await t.hover(delAstPage.assetTab)
      .click(delAstPage.assetTab)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(1000)
      .click(indexTicPage.listToggleIcon)
      .wait(1000)
      .hover(this.previewType)
      .expect(this.previewType.visible).ok()
      .expect(this.previewAssetName.visible).ok()
      .expect(this.previewSource.visible).ok()
      .expect(this.previewTags.visible).ok()
      .expect(this.previewWarrantyStatus.visible).ok()
      .hover(this.previewWarrantyStatus)
      .wait(500)
      .click(indexTicPage.gridToggleIcon)
      .wait(500);
  }

  async viewListAssetData(assetName, type, source, tag, notes, location, department, model, macAddress, status, manufacturer, ipAddress, productNumber, serialNumber, hwDetailType, astTag, archived, usedBy, managedBy, cost) {
    const [purchasePrice] = cost.split(':');
    const convertInt = parseInt(purchasePrice, 10);
    const extractCost = Math.floor(convertInt);
    const costInString = extractCost.toString();
    const assetName1 = Selector(`[data-tc-asset="${assetName}"] [data-tc-asset-name="${assetName}"]`);
    const viewType = Selector(`[data-tc-asset="${assetName}"] [data-tc-type="${type}"]`);
    const viewSource = Selector(`[data-tc-asset="${assetName}"] [data-tc-icon="${source}"]`);
    const viewTags = Selector(`[data-tc-asset="${assetName}"] [data-tc-tags="${tag}"]`);
    const viewNotes = Selector(`[data-tc-asset="${assetName}"] [data-tc-val="${notes}"]`);
    const viewLocation = Selector(`[data-tc-asset="${assetName}"] [data-tc-location="${location}"]`);
    const viewDepartment = Selector(`[data-tc-asset="${assetName}"] [data-tc-val="${department}"]`);
    const viewModel = Selector(`[data-tc-asset="${assetName}"] [data-tc-val="${model}"]`);
    const viewMacAddress = Selector(`[data-tc-asset="${assetName}"] [data-tc-mac-adddress="${macAddress}"]`);
    const viewStatus = Selector(`[data-tc-asset="${assetName}"] [data-tc-val="${notes}"]`);
    const viewManufacturer = Selector(`[data-tc-asset="${assetName}"] [data-tc-val="${manufacturer}"]`);
    const viewIpAddress = Selector(`[data-tc-asset="${assetName}"] [data-tc-val="${ipAddress}"]`);
    const viewProductNumber = Selector(`[data-tc-asset="${assetName}"] [data-tc-val="${productNumber}"]`);
    const viewSerialNumber = Selector(`[data-tc-asset="${assetName}"] [data-tc-machine-serial="${serialNumber}"]`);
    const viewHwDetailType = Selector(`[data-tc-asset="${assetName}"] [data-tc-val="${hwDetailType}"]`);
    const viewAstTag = Selector(`[data-tc-asset="${assetName}"] [data-tc-val="${astTag}"]`);
    const viewArchived = Selector(`[data-tc-asset="${assetName}"] [data-tc-val="${notes}"]`);
    const viewUsedBy = Selector(`[data-tc-asset="${assetName}"] [data-tc-val="${usedBy}"]`);
    const viewManagedBy = Selector(`[data-tc-asset="${assetName}"] [data-tc-val="${managedBy}"]`);
    const viewCost = Selector(`[data-tc-asset="${assetName}"] [data-tc-cost="${costInString}"]`);
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .wait(1000)
      .hover(delAstPage.assetTab)
      .click(delAstPage.assetTab)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(1000)
      .click(indexTicPage.listToggleIcon)
      .wait(1000)
      .hover(this.previewType)
      .expect(this.previewType.visible).ok()
      .expect(this.previewAssetName.visible).ok()
      .expect(this.previewSource.visible).ok()
      .expect(this.previewTags.visible).ok()
      .expect(this.previewWarrantyStatus.visible).ok()
      .expect(this.previewNotes.visible).ok()
      .expect(this.previewLocation.visible).ok()
      .expect(this.previewDepartment.visible).ok()
      .expect(this.previewModel.visible).ok()
      .expect(this.previewMacAddress.visible).ok()
      .hover(this.previewMacAddress)
      .wait(500)
      .hover(this.previewStatus)
      .expect(this.previewStatus.visible).ok()
      .expect(this.previewManufacturer.visible).ok()
      .expect(this.previewIpAddress.visible).ok()
      .expect(this.previewOperatingSystem.visible).ok()
      .expect(this.previewProductNumber.visible).ok()
      .expect(this.previewSerialNumber.visible).ok()
      .expect(this.previewMachSerialNumber.visible).ok()
      .hover(this.previewHwdDetailType)
      .expect(this.previewHwdDetailType.visible).ok()
      .expect(this.previewImpact.visible).ok()
      .expect(this.previewAssetTag.visible).ok()
      .expect(this.previewAcquisitionDate.visible).ok()
      .expect(this.previewArchived.visible).ok()
      .hover(this.previewWarrantyDate)
      .expect(this.previewInstallDate.visible).ok()
      .expect(this.previewUsedBy.visible).ok()
      .expect(this.previewManagedBy.visible).ok()
      .expect(this.previewCost.visible).ok()
      .expect(this.previewFirmware.visible).ok()
      .expect(this.previewWarrantyDate.visible).ok()
      .hover(assetName1)
      .expect(assetName1.innerText).contains(assetName)
      .wait(500)
      .expect(viewType.visible).ok()
      .expect(viewSource.visible).ok()
      .wait(500)
      .expect(viewTags.innerText).eql(tag)
      .wait(500)
      .expect(viewNotes.innerText).eql(notes)
      .expect(viewLocation.innerText).eql(location)
      .expect(viewDepartment.innerText).eql(department)
      .expect(viewModel.innerText).eql(model)
      .expect(viewMacAddress.innerText).eql(macAddress)
      .expect(viewManufacturer.innerText).eql(manufacturer)
      .expect(viewIpAddress.innerText).eql(ipAddress)
      .expect(viewProductNumber.innerText).eql(productNumber)
      .expect(viewSerialNumber.innerText).eql(serialNumber)
      .expect(viewHwDetailType.innerText).eql(hwDetailType)
      .expect(viewAstTag.innerText).eql(astTag)
      .expect(viewUsedBy.innerText).eql(usedBy)
      .expect(viewManagedBy.innerText).eql(managedBy)
      // .expect(viewCost.innerText).eql(`$ ${costInString}`)
      .click(indexTicPage.gridToggleIcon)
      .wait(1000);
  }
} export default assetListColumn
