import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import deleteAsset from './delete_assets';
import addAssetType from './add_asset_type';
import addAsset from './add_assets';

const golVari = new golvar();
const naviBar = new navbar();
const delAstPage = new deleteAsset();
const astTypePage = new addAssetType();
const addAstPage = new addAsset();


class assetAlert {

  constructor() {
    this.assetAlertElement = '[data-tc-title="asset alerts"]';
    this.dateElement = '[data-tc-dates="Set an alert date..."]';
    this.alertBtn = Selector('[data-tc-btn="+ Add alerts"]');
    this.addAlertBtn = Selector('[data-tc-add-alert-date-btn]');
    this.viewAlertModal = Selector(`${this.assetAlertElement} h2`);
    this.viewSmartSection = Selector('[data-tc-section="smart alerts"]');
    this.viewSmartTitle = this.viewSmartSection.find('h6');
    this.viewSmartBox = this.viewSmartSection.find('[data-tc-box="smart alerts"]');
    this.daysElement = Selector(`${this.assetAlertElement} [data-tc-section="mention days"]`);
    this.addCalendarBtn = Selector('[data-tc-btn="calendar alerts"]');
    this.inputAlertField = Selector('[data-tc-field="add alert"]');
    this.inputDate = Selector(`${this.assetAlertElement} [data-tc-dates-input]`);
    this.inputDate1 = Selector('[data-tc-dates="Set an alert date..."]').nth(2);
    this.inputNotes = Selector('[data-tc-title="alert dates"] [data-tc-asset-alert-notes]');
    this.saveBtn = Selector('[data-tc-save-form-btn="Save"]');
    this.removeAlert = Selector('[data-tc-remove-alert]');
    this.manageBtn = Selector('[data-tc-btn="Manage"]');
    this.cancelBtn = Selector('[data-tc-btn="Cancel"]');
    this.closeModal = Selector(`${this.assetAlertElement} .sweet-action-close`);
  }

  async alertsModalView(assetName) {
    await t.expect(golVari.currentpageurl()).contains('/managed_assets/')
      .expect(delAstPage.viewsearchedAsset.innerText).contains(assetName)
      .click(this.alertBtn)
      .wait(500)
      .expect(this.viewAlertModal.innerText).contains('Asset Alerts')
      .expect(this.viewSmartSection.exists).ok('Check the smart alerts section')
      .hover(this.viewSmartSection)
      .expect(this.viewSmartTitle.innerText).eql('Smart Alerts')
      .expect(this.viewSmartBox.exists).ok('Check the smart alerts fields')
      .hover(this.viewSmartBox)
      .expect(this.addCalendarBtn.exists).ok()
      .hover(this.addCalendarBtn)
      .expect(this.inputAlertField.exists).ok()
      .hover(this.inputAlertField)
      .hover(this.daysElement)
      .expect(this.daysElement.innerText).eql(`≤ 5 days\n \n≤ 10 days\n \n> 10 days\nCancel \nSave`)
      .click(this.closeModal);
  }

  async alertWithNoData(assetName) {
    await t.expect(golVari.currentpageurl()).contains('/managed_assets/')
      .expect(delAstPage.viewsearchedAsset.innerText).contains(assetName)
      .wait(500)
      .click(this.alertBtn)
      .wait(1000)
      .expect(this.viewAlertModal.innerText).contains('Asset Alerts')
      .wait(500)
      .hover(this.addCalendarBtn)
      .click(this.addCalendarBtn)
      .wait(500)
      .click(this.saveBtn)
      .wait(500)
      .expect(astTypePage.notifyPopUp.innerText).eql('Alert Date and Recipients cannot be empty.')
      .click(this.closeModal)
      .hover(addAstPage.backAstLink)
      .click(addAstPage.backAstLink);
  }

  async manageAlerts(assetName, dates, notes) {
    const asset = Selector(`[data-tc-asset-box-link="${assetName}"]`);
    const selectNextMonth = Selector('[data-tc-dates="Set an alert date..."] .mx-calendar-header .mx-icon-next-month');
    const selectDate = Selector('[data-tc-dates="Set an alert date..."] .cell.cur-month');
    const calendarOkBtn = Selector('[data-tc-dates="Set an alert date..."] .mx-datepicker-btn.mx-datepicker-btn-confirm');
    await t.hover(delAstPage.assetTab)
      .click(delAstPage.assetTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(3000)
      .hover(asset)
      .wait(1000)
      .click(asset)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/managed_assets/')
      .wait(1000)
      .click(this.alertBtn)
      .wait(1000)
      .expect(this.viewAlertModal.innerText).contains('Asset Alerts')
      .wait(500)
      .hover(this.addCalendarBtn)
      .click(this.addCalendarBtn)
      .wait(500)
      .click(this.inputDate)
      .wait(1000)
      .click(selectNextMonth)
      .wait(1000)
      .click(selectDate.withText(dates))
      .wait(500)
      .click(this.viewAlertModal)
      .wait(500)
      .click(this.inputNotes)
      .wait(500)
      .typeText(this.inputNotes, notes, { paste: true })
      .wait(1000)
      .click(this.saveBtn)
      .wait(1000)
      .expect(astTypePage.notifyPopUp.innerText).contains('Successfully saved alert dates')
  }

  async pastDateAlerts(assetName1) {
    // const todayDate = Selector(`${this.dateElement} .cell cur-month today`);
    // const selectDateValue = await todayDate.innerText;
    // const minusValue = selectDateValue - 1;
    const selectDate = Selector(`${this.dateElement} .cell cur-month`);
    await t.expect(delAstPage.viewsearchedAsset.innerText).contains(assetName1)
      .wait(500)
      .click(this.alertBtn)
      .wait(1000)
      .expect(this.viewAlertModal.innerText).contains('Asset Alerts')
      .wait(500)
      .hover(this.addCalendarBtn)
      .click(this.addCalendarBtn)
      .click(this.inputDate)
      .wait(1000)
      .click(selectDate.withText('1'))
      .wait(500)
      .expect(astTypePage.notifyPopUp.innerText).contains('Alert dates cannot be in the past.')
      .wait(1000)
      .click(this.closeModal);
  }

  async manageMultipleAlerts(assetName, date1, date2, note1, note2) {
    const asset = Selector(`[data-tc-asset-box-link="${assetName}"]`);
    const selectNextMonth = Selector('[data-tc-dates="Set an alert date..."] .mx-calendar-header .mx-icon-next-month');
    const selectNextMonth1 = this.inputDate1.find('.mx-calendar-header .mx-icon-next-month');
    const selectDate = Selector('[data-tc-dates="Set an alert date..."] .cell.cur-month');
    const selectDate1 = this.inputDate1.find('.cell.cur-month');
    const calendarOkBtn = Selector('[data-tc-dates="Set an alert date..."] .mx-datepicker-btn.mx-datepicker-btn-confirm');
    await t.expect(golVari.currentpageurl()).contains('/managed_assets/')
      .expect(delAstPage.viewsearchedAsset.innerText).contains(assetName)
      .wait(500)
      .click(this.alertBtn)
      .wait(1000)
      .expect(this.viewAlertModal.innerText).contains('Alert dates')
      .wait(1000)
      .click(this.addAlertBtn)
      .wait(500)
      .click(this.inputDate)
      .wait(1000)
      .click(selectNextMonth)
      .wait(1000)
      .click(selectDate.withText(date1))
      .wait(1000)
      .click(this.viewAlertModal)
      .wait(1000)
      .click(this.inputNotes)
      .wait(500)
      .typeText(this.inputNotes, note1, { paste: true })
      .wait(1000)
      .click(this.addAlertBtn)
      .wait(500)
      .click(this.inputDate1)
      .wait(1000)
      .click(selectNextMonth1)
      .wait(1000)
      .click(selectDate1.withText(date2))
      .wait(500)
      .click(this.viewAlertModal)
      .wait(500)
      .click(this.inputNotes)
      .wait(500)
      .typeText(this.inputNotes, note2, { paste: true })
      .wait(1000)
      .click(this.saveBtn)
      .wait(1000)
      .expect(astTypePage.notifyPopUp.innerText).contains('Successfully saved alert dates')
      .wait(1000)
  }

  async deleteAlerts(assetName3) {
    const asset = Selector(`[data-tc-asset-box-link="${assetName3}"]`);
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
      .hover(delAstPage.assetTab)
      .click(delAstPage.assetTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(3000)
      .hover(asset)
      .wait(1000)
      .click(asset)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/managed_assets/')
      .wait(1000)
      .click(this.manageBtn)
      .wait(1000)
      .expect(this.viewAlertModal.innerText).contains('Alert dates')
      .wait(1000)
      .click(this.removeAlert)
      .wait(1000)
      .click(this.saveBtn)
      .wait(1000)
      .expect(astTypePage.notifyPopUp.innerText).contains('Successfully saved alert dates')
      .wait(1000)
  }

} export default assetAlert
