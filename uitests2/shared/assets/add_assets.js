import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import assetForm from './assets_form_ui_test_case';

const golVari = new golvar();
const naviBar = new navbar();
const addNewAssets = new assetForm();

class addAsset {

  constructor() {
    this.addBtn = Selector('[data-tc-add-assets-btn]');
    this.assetHeaderPage = Selector('[data-tc-header-form]');
    this.saveBtn = Selector('[data-tc-save-form-btn="Save asset"]');
    this.viewAstName = Selector('[data-tc-view-assets-name]');
    this.viewType = Selector('[data-tc-view="asset type"]');
    this.viewManufacturer = Selector('[data-tc-view-manufacturer]');
    this.viewModel = Selector('[data-tc-view-model]');
    this.viewTag = Selector('[data-tc-view-asset-tag]');
    this.viewUsedBy = Selector('[data-tc-user-name]');
    this.viewManagedBy = Selector('[data-tc-view-managed-user]');
    this.viewDepartment = Selector('[data-tc-label="Department"] [data-tc-value]');
    this.viewLocation = Selector('[data-tc-view-assets-location] [data-tc-location-name]');
    this.viewCost = Selector('[data-tc-view-assets-cost]');
    this.viewPurchasePrice = Selector('[data-tc-view="purchase price"]');
    this.viewReplacementCost = Selector('[data-tc-view="replacement cost"]');
    this.viewSalvage = Selector('[data-tc-view="salvage value"]');
    this.viewUsefulLife = Selector('[data-tc-view="useful life"]');
    this.viewEndLife = Selector('[data-tc-view="approaching end of life"]');
    this.viewDepreciation = Selector('[data-tc-view-assets-depreciation]');
    this.noteSideNav = Selector('[data-tc-notes-link]');
    this.viewNotes = Selector('[data-tc-view-assets-note]');
    this.detailSideNav = Selector('[data-tc-details-link]');
    this.addDetailBtn = Selector('[data-tc-add-detail-btn]');
    this.viewKeyTitle = Selector('[data-tc-view="key"]');
    this.viewValueTitle = Selector('[data-tc-view="value"]');
    this.keyInput = Selector('[data-tc-field="key"]');
    this.detailInput = Selector('[data-tc-field="value"]');
    this.submitDetailBtn = Selector('[data-tc-add-key-btn]');
    this.viewDetails = Selector('[data-tc-view-details]');
    this.sourceSideNav = Selector('[data-tc-asset-sources]');
    this.manuallyAddedTab = Selector('[data-tc-manually-add-link]');
    this.notifyPopUp = Selector('.notification-content');
    this.machineSerialLabel = Selector('[data-tc-view-key="Machine Serial No"]');
    this.backAstLink = Selector('[data-tc-link="back to all assets"]');
    this.noteSection = Selector('#asset-notes');
    this.sourceSection = Selector('#asset-sources');
    this.customDetailSection = Selector('#asset-details');
    this.historySection = Selector('#asset-history');
    this.companyViewToggle = Selector('#my_company_view');
    this.managementViewToggle = Selector('#management_view');
    this.assetTab = Selector('[data-tc-assets="Assets menu"]');
  }

  async addAssets(astName, astType, astTags, astNote, astMacAddress, astIpAddress, astManufacturer, astModel, tags, astSystemUUID, astSystemUpTime, astCost, astSalvage, astDepreciationType, astUsedBy, astManagedBy, astLocation, astDepartment, astSerialNumber, astProductNumber, astAcquisitionDate, astWarrantyExpiration, astInstallationDate, key, details) {
    const viewTags = Selector(`[data-tc-manually-added="${tags}"]`);
    const viewAstLoc = Selector(`[data-tc-manually-added="${astLocation}"]`);
    const viewAstType = Selector(`[data-tc-manually-added="${astType}"]`);
    const viewAstIp = Selector(`[data-tc-manually-added="${astIpAddress}"]`);
    const viewAstName = Selector(`[data-tc-manually-added="${astName}"]`);
    const viewAstManuf = Selector(`[data-tc-manually-added="${astManufacturer}"] span`);
    const viewAstSerialNum = Selector(`[data-tc-manually-added="${astSerialNumber}"] span`);
    const viewAddedKey = Selector(`[data-tc-listed-key="${key}"]`);
    const viewAddedValue = Selector(`[data-tc-listed-value="${details}"]`);
    const viewUsedBy = Selector(`[data-tc-used-by="${astUsedBy}"]`);
    const viewLocTag = Selector(`[data-tc-location="${astLocation}"]`);
    const [purchasePrice, replacementCost, usefulLife, endLife] = astCost.split(':');
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
      .expect(this.addBtn.visible).ok()
      .hover(this.addBtn)
      .click(this.addBtn)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('managed_assets/new')
      .hover(this.assetHeaderPage)
      .expect(this.assetHeaderPage.innerText).contains('New Asset')
      .wait(500);
    addNewAssets.assetForms(astName, astType, astTags, astNote, astMacAddress, astIpAddress, astManufacturer, astModel, tags, astSystemUUID, astSystemUpTime, astCost, astSalvage, astDepreciationType, astUsedBy, astManagedBy, astLocation, astDepartment, astSerialNumber, astProductNumber, astAcquisitionDate, astWarrantyExpiration, astInstallationDate)
    await t.hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(4000)
      .expect(golVari.currentpageurl()).contains('managed_assets')
      .hover(this.viewAstName)
      .expect(this.viewAstName.innerText).contains(astName)
      .wait(500)
      .hover(this.viewManufacturer)
      .expect(this.viewManufacturer.innerText).contains(astManufacturer)
      .hover(this.viewModel)
      .expect(this.viewModel.innerText).contains(astModel)
      .hover(this.viewTag)
      .expect(this.viewTag.innerText).contains(astTags)
      .wait(500)
      .hover(viewUsedBy)
      .expect(viewUsedBy.innerText).contains(astUsedBy)
      .hover(viewLocTag)
      .expect(viewLocTag.innerText).contains(astLocation)
      .hover(this.viewManagedBy)
      .expect(this.viewManagedBy.innerText).contains(astManagedBy)
      .hover(this.viewDepartment)
      .expect(this.viewDepartment.innerText).contains(astDepartment)
      .hover(this.viewPurchasePrice)
      .expect(this.viewPurchasePrice.innerText).eql(`$${purchasePrice}`)
      .hover(this.viewReplacementCost)
      .expect(this.viewReplacementCost.innerText).eql(`$${replacementCost}`)
      .hover(this.viewSalvage)
      .expect(this.viewSalvage.innerText).eql(`$${astSalvage}`)
      .hover(this.viewUsefulLife)
      .expect(this.viewUsefulLife.innerText).eql(`${usefulLife} years`)
      .hover(this.viewEndLife)
      .expect(this.viewEndLife.innerText).eql(`${endLife} months`)
      .wait(500)
      .hover(this.noteSideNav)
      .click(this.noteSideNav)
      .wait(500)
      .expect(this.noteSection.visible).ok()
      .hover(this.viewNotes)
      .expect(this.viewNotes.innerText).contains(astNote)
      .hover(this.detailSideNav)
      .click(this.detailSideNav)
      .expect(this.customDetailSection.visible).ok()
      .hover(this.addDetailBtn)
      .click(this.addDetailBtn)
      .wait(1000)
      .hover(this.viewKeyTitle)
      .expect(this.viewKeyTitle.innerText).contains('Key')
      .wait(1000)
      .hover(this.viewValueTitle)
      .expect(this.viewValueTitle.innerText).contains('Value')
      .wait(1000)
      .hover(this.keyInput)
      .typeText(this.keyInput, key, { paste: true })
      .wait(1000)
      .hover(this.detailInput)
      .typeText(this.detailInput, details, { paste: true })
      .wait(500)
      .click(this.submitDetailBtn)
      .wait(3000)
      .hover(viewAddedKey)
      .expect(viewAddedKey.innerText).contains(key)
      .wait(500)
      .expect(viewAddedValue.innerText).contains(details)
      .wait(2000)
      .hover(this.sourceSideNav)
      .click(this.sourceSideNav)
      .expect(this.sourceSection.visible).ok()
      .hover(this.manuallyAddedTab)
      .click(this.manuallyAddedTab)
      .hover(viewTags)
      .expect(viewTags.innerText).contains(tags)
      .hover(viewAstLoc)
      .expect(viewAstLoc.innerText).contains(astLocation)
      .hover(viewAstType)
      .expect(viewAstType.innerText).contains(astType)
      .hover(viewAstIp)
      .expect(viewAstIp.innerText).contains(astIpAddress)
      .hover(viewAstName)
      .expect(viewAstName.innerText).contains(astName)
      .hover(viewAstManuf)
      .expect(viewAstManuf.innerText).contains(astManufacturer)
      .hover(this.machineSerialLabel)
      .expect(viewAstSerialNum.innerText).contains(astSerialNumber)
      .wait(1000)
  }

  async addAssetWithNameNType(astName1, astType1) {
    await t.hover(this.assetTab)
      .click(this.assetTab)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
      .hover(this.addBtn)
      .click(this.addBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('managed_assets/new')
      .hover(this.assetHeaderPage)
      .expect(this.assetHeaderPage.innerText).contains('New Asset')
      .wait(500)
      .hover(addNewAssets.assetsName)
      .typeText(addNewAssets.assetsName, astName1, { paste: true })
      .hover(addNewAssets.assetsType)
      .click(addNewAssets.assetsType)
      .typeText(addNewAssets.assetsType, astType1, { paste: true })
      .pressKey('enter')
      .wait(500)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('managed_assets')
      .hover(this.viewAstName)
      .expect(this.viewAstName.innerText).contains(astName1)
      .hover(this.viewType)
      .expect(this.viewType.innerText).eql(`• ${astType1}`)
      .hover(this.backAstLink)
      .click(this.backAstLink)
      .wait(1500);
  }

  async navigateToAstShowPage() {
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
      .hover(this.addBtn)
      .click(this.addBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('managed_assets/new')
      .hover(this.assetHeaderPage)
      .expect(this.assetHeaderPage.innerText).contains('New Asset')
      .wait(500)
  }
} export default addAsset
