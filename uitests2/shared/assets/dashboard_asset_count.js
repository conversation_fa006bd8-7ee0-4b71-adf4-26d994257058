import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import deleteAsset from './delete_assets';
import deleteTicket from '../help_desk/delete_tickets';

const golVari = new golvar();
const naviBar = new navbar();
const delAstPage = new deleteAsset();
const delTic = new deleteTicket();

class viewCount {

  async viewCounts(assetType, count) {
    const viewType = Selector(`[data-tc-asset-type="${assetType}"]`);
    const viewAstCount = Selector(`[data-tc-view-count="${assetType}"] strong`);
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(1000)
      .hover(viewType)
      .wait(500)
      .expect(viewAstCount.innerText).contains(count)
      .wait(1000)
  }

  async viewCounterDashboard(value, module) {
    const checkCounts = Selector(`[data-tc-total-${module}] span`);
    await t.wait(2000)
      .hover(naviBar.homeDashboard)
      .click(naviBar.homeDashboard)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('/dashboard')
      .wait(500)
      .hover(checkCounts)
      .expect(checkCounts.innerText).contains(value)
      .wait(500);
  }

  async viewTicketCounter(counts) {
    const checkCounts = Selector(`[data-tc-asset-list-heading] span`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .hover(checkCounts)
      .expect(checkCounts.innerText).contains(`(Showing ${counts})`)
      .wait(1000)
  }
} export default viewCount
