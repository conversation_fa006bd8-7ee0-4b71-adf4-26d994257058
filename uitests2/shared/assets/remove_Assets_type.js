import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import addAssetType from './add_asset_type';

const golVari = new golvar();
const naviBar = new navbar();
const astType = new addAssetType();

class removeAssetsType {
  constructor() {
    this.changeTypeModal = Selector('[data-tc-title="asset type"]');
    this.doneBtn = Selector('[data-tc-change-asset-type-submit-btn]');
  }

  async removeDefaultTypes(activeType) {
    const removeDeftIcon = Selector(`[data-tc-active-type-remove-icon="${activeType}"]`);
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(1000)
      .hover(astType.settingMenu)
      .click(astType.settingMenu)
      .wait(2000)
      .hover(astType.assetTypeTab)
      .click(astType.assetTypeTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('managed_assets/settings/asset_types')
    await t.hover(removeDeftIcon)
      .click(removeDeftIcon)
      .wait(1000)
  }

  async removeCustomTypes(customType) {
    const removeCustIcon = Selector(`[data-tc-active-type-remove-icon="${customType}"]`);
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
      .hover(astType.settingMenu)
      .click(astType.settingMenu)
      .wait(2000)
      .hover(astType.assetTypeTab)
      .click(astType.assetTypeTab)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('managed_assets/settings/asset_types')
      .wait(500)
      .hover(removeCustIcon)
      .click(removeCustIcon)
      .wait(1000)
  }

  async removeNChangeCustomTypes(customType, assetName, changeType) {
    const removeCustIcon = Selector(`[data-tc-active-type-remove-icon="${customType}"]`);
    const asset = Selector(`[data-tc-assets="${assetName}"]`);
    const viewAstName = asset.find('[data-tc-view="asset name row"]');
    const typeDropDown = asset.find('[data-tc-drop-down="asset types"]');
    const selectType = asset.find(`[data-tc-options="${changeType}"]`);
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
      .hover(astType.settingMenu)
      .click(astType.settingMenu)
      .wait(2000)
      .hover(astType.assetTypeTab)
      .click(astType.assetTypeTab)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('managed_assets/settings/asset_types')
      .wait(500)
      .hover(removeCustIcon)
      .click(removeCustIcon)
      .wait(1000)
      .expect(this.changeTypeModal.exists).ok('Check the modal is open')
      .hover(asset)
      .expect(asset.exists).ok('Check the asset is viewing or not')
      .expect(viewAstName.innerText).contains(assetName)
      .hover(typeDropDown)
      .click(typeDropDown)
      .wait(500)
      .click(selectType)
      .wait(500)
      .hover(this.doneBtn)
      .click(this.doneBtn)
      .wait(500)
      .expect(removeCustIcon.exists).notOk();
  }
} export default removeAssetsType
