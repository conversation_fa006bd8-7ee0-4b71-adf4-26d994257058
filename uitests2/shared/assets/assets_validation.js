import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import addAsset from './add_assets';
import deleteAsset from './delete_assets';
import assetForm from './assets_form_ui_test_case';

const golVari = new golvar();
const naviBar = new navbar();
const addAstPage = new addAsset();
const delAstPage = new deleteAsset();
const astFormPage = new assetForm();


class assetValidations {

  constructor() {
    this.viewAstMsg = Selector('[data-tc-not-tracking-assets]');
    this.viewAddNewOne = Selector('[data-tc-link="add new one"]');
    this.discoverOne = Selector('[data-tc-link="discover one"]');
    this.viewIpLabel = Selector('[data-tc-label="ip address"]');
    this.viewIpMsg = Selector('[data-tc-message="invalid ip"]');
    this.viewMacMsg = Selector('[data-tc-message="invalid mac"]');
    this.viewCostMsg = Selector('[data-tc-view-error="cost"]');
    this.viewSalvageMsg = Selector('[data-tc-view-error="salvage"]');
    this.viewDeprMsg = Selector('[data-tc-view-error="depreciation"]');
    this.editNotesBtn = Selector('[data-tc-edit-icon="notes"]');
    this.notesEnableBtn = Selector('[data-tc-enable-btn="true"]');
    this.notesDisableBtn = Selector('[data-tc-disable-btn="true"]');
    this.inputNotes = Selector('[data-tc-input="notes"]');
  }

  async checkNoAsset() {
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
      .hover(delAstPage.assetTab)
      .click(delAstPage.assetTab)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(1000)
      .hover(this.viewAstMsg)
      .wait(500)
      .expect(this.viewAstMsg.innerText).contains('Not tracking anything here, yet.')
      .expect(this.viewAddNewOne.exists).ok()
      .expect(this.discoverOne.exists).ok()
      .wait(1000)
  }

  async addAstWithoutData() {
    await t.hover(addAstPage.assetHeaderPage)
      .expect(addAstPage.assetHeaderPage.innerText).contains('New Asset')
      .wait(1000)
      .hover(addAstPage.saveBtn)
      .click(addAstPage.saveBtn)
      .wait(500)
      .expect(addAstPage.notifyPopUp.innerText).contains('Please correct the highlighted errors before submitting.')
      .wait(1000)
  }

  async AddAstWithinvalidIPMac() {
    await t.hover(addAstPage.assetHeaderPage)
      .expect(addAstPage.assetHeaderPage.innerText).contains('New Asset')
      .wait(1000)
      .hover(astFormPage.assetsName)
      .typeText(astFormPage.assetsName, 'Assets', { paste: true })
      .wait(500)
      .hover(astFormPage.assetsType)
      .click(astFormPage.assetsType)
      .typeText(astFormPage.assetsType, 'Desktop', { paste: true })
      .pressKey('enter')
      .wait(1000)
      .hover(this.viewIpLabel)
      .wait(1000)
      .typeText(astFormPage.assetIpAddress, '**********', { paste: true })
      .wait(500)
      .typeText(astFormPage.assetMacAddress, 'AA-12-C1-E5-CD-31', { paste: true })
      .wait(500)
      .hover(addAstPage.saveBtn)
      .click(addAstPage.saveBtn)
      .wait(500)
      .expect(addAstPage.notifyPopUp.innerText).contains('Please correct the highlighted errors before submitting.')
      .wait(500)
      .expect(this.viewIpMsg.innerText).contains('Please enter the valid IP address.')
      .wait(1000)
    // .expect(this.viewMacMsg.innerText).contains('Please enter the valid MAC addresses')
    // .wait(1000)
  }

  async checkValidationCostSalvageDepriciation() {
    await t.hover(addAstPage.assetHeaderPage)
      .expect(addAstPage.assetHeaderPage.innerText).contains('New Asset')
      .wait(1000)
      .hover(astFormPage.assetsName)
      .typeText(astFormPage.assetsName, 'Assets', { paste: true })
      .wait(500)
      .hover(astFormPage.assetsType)
      .click(astFormPage.assetsType)
      .typeText(astFormPage.assetsType, 'Desktop', { paste: true })
      .pressKey('enter')
      .wait(1000)
      .hover(this.viewIpLabel)
      .wait(1000)
      .typeText(astFormPage.assetIpAddress, '**********', { paste: true })
      .wait(500)
      .typeText(astFormPage.assetMacAddress, 'AA-12-C1-E5-CD-31', { paste: true })
      .wait(500)
      .hover(astFormPage.costSideMenu)
      .click(astFormPage.costSideMenu)
      .wait(1000)
      .hover(astFormPage.assetCost)
      .typeText(astFormPage.assetCost, '-10', { paste: true })
      .wait(500)
      .hover(astFormPage.assetSalvage)
      .typeText(astFormPage.assetSalvage, '-12', { paste: true })
      .wait(500)
      .hover(addAstPage.saveBtn)
      .click(addAstPage.saveBtn)
      .wait(500)
      .expect(addAstPage.notifyPopUp.innerText).contains('Please correct the highlighted errors before submitting.')
      .wait(500)
      .expect(this.viewCostMsg.innerText).contains('The cost field format is invalid')
      .wait(1000)
      .expect(this.viewSalvageMsg.innerText).contains('The salvage field format is invalid')
      .wait(1000)
      .hover(astFormPage.assetCost)
      .typeText(astFormPage.assetCost, '10', { replace: true })
      .wait(500)
      .hover(astFormPage.assetSalvage)
      .typeText(astFormPage.assetSalvage, '12', { replace: true })
      .wait(500)
      .hover(addAstPage.saveBtn)
      .click(addAstPage.saveBtn)
      .wait(500)
      .expect(addAstPage.notifyPopUp.innerText).contains('Please correct the highlighted errors before submitting.')
      .wait(500)
      .expect(this.viewDeprMsg.innerText).contains('The depreciation type field is required')
      .wait(1000)
  }

  async validateNotes() {
    await t.hover(addAstPage.noteSideNav)
      .click(addAstPage.noteSideNav)
      .wait(1000)
      .click(this.editNotesBtn)
      .wait(500)
      .typeText(this.inputNotes, ' ', { replace: true })
      .wait(500)
      .expect(this.notesEnableBtn.exists).ok()
      .expect(this.notesDisableBtn.exists).notOk()
      .click(this.inputNotes)
      .typeText(this.inputNotes, ' ', { replace: true })
      .wait(500)
      .pressKey('backspace')
      .wait(500)
      .expect(this.notesEnableBtn.exists).notOk()
      .expect(this.notesDisableBtn.exists).ok()
      .wait(500)
  }
} export default assetValidations
