import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';


const golVari = new golvar();
const naviBar = new navbar();


class addAssetType {

  constructor() {
    this.settingMenu = Selector('[data-tc-asset="settings menu"]');
    this.nameField = Selector('[data-tc-asset-type-name]');
    this.uploadImage = Selector('[data-tc-attachment-image]');
    this.dropFile = Selector('[data-tc-attachment-image] [data-tc-input-file]');
    this.addTypeBtn = Selector('[data-tc-save-form-btn="+ Add Custom Type"]');
    this.notifyPopUp = Selector('.notification-content');
    this.editModal = Selector('[data-tc-modal="Edit Asset Type"] h2');
    this.defaultNameField = Selector('[data-tc-form="asset type"] [disabled="disabled"]');
    this.removeDefaultTypeIcon = Selector('[data-tc-disable-remove="true"]');
    this.editUploadedImage = Selector('[data-tc-modal="Edit Asset Type"] [data-tc-attachment-image]');
    this.editDropFile = Selector('[data-tc-modal="Edit Asset Type"] [data-tc-attachment-image] [data-tc-input-file]');
    this.updateBtn = Selector('[data-tc-save-form-btn="Update"]');
    this.assetTypeTab = Selector('[data-tc-links="asset_types"]');
  }

  async addAssetTypes(typeName) {
    const addedType = Selector(`[data-tc-asset-ctypes="${typeName}"]`);
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
      .hover(this.settingMenu)
      .click(this.settingMenu)
      .wait(500)
      .hover(this.assetTypeTab)
      .click(this.assetTypeTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('managed_assets/settings/asset_types')
      .typeText(this.nameField, typeName, { paste: true })
      .wait(1000)
      .hover(this.uploadImage)
      .click(this.uploadImage)
      .setFilesToUpload(this.dropFile, '../../shared/assets/inputImage.png')
      .hover(this.addTypeBtn)
      .click(this.addTypeBtn)
      .wait(2000)
      .hover(addedType)
      .expect(addedType.innerText).contains(typeName)
      .wait(1000)
  }

  async editDefaultType(typeName) {
    const editIcon = Selector(`[data-tc-edit-icon="${typeName}"]`);
    await t.expect(golVari.currentpageurl()).contains('managed_assets/settings/asset_types')
      .wait(500)
      .click(editIcon)
      .wait(500)
      .expect(this.editModal.innerText).contains('Edit Asset Type')
      .wait(500)
      .expect(this.defaultNameField.exists).ok()
      .click(this.updateBtn)
      .wait(500)
  }

  async editDefaultImage(typeName) {
    const editIcon = Selector(`[data-tc-edit-icon="${typeName}"]`);
    await t.expect(golVari.currentpageurl()).contains('managed_assets/settings/asset_types')
      .wait(500)
      .click(editIcon)
      .wait(500)
      .expect(this.editModal.innerText).contains('Edit Asset Type')
      .wait(500)
      .hover(this.editUploadedImage)
      .click(this.editUploadedImage)
      .setFilesToUpload(this.editDropFile, '../../shared/assets/inputImage.png')
      .wait(500)
      .click(this.updateBtn)
      .wait(500)
  }

  async viewAllDefaultTypes() {
    const appleDevice = Selector('[data-tc-asset-ctypes="Apple Device"]');
    const desktop = Selector('[data-tc-asset-ctypes="Desktop"]');
    const dongle = Selector('[data-tc-asset-ctypes="Dongle"]');
    const fireWall = Selector('[data-tc-asset-ctypes="Firewall"]');
    const iPad = Selector('[data-tc-asset-ctypes="iPad"]');
    const iPhone = Selector('[data-tc-asset-ctypes="iPhone"]');
    const laptop = Selector('[data-tc-asset-ctypes="Laptop"]');
    const mobile = Selector('[data-tc-asset-ctypes="Mobile"]');
    const other = Selector('[data-tc-asset-ctypes="Other"]');
    const phone = Selector('[data-tc-asset-ctypes="Phone"]');
    const phoneSystem = Selector('[data-tc-asset-ctypes="Phone System"]');
    const printer = Selector('[data-tc-asset-ctypes="Printer"]');
    const router = Selector('[data-tc-asset-ctypes="Router"]');
    const server = Selector('[data-tc-asset-ctypes="Server"]');
    const switchs = Selector('[data-tc-asset-ctypes="Switch"]');
    const tablet = Selector('[data-tc-asset-ctypes="Tablet"]');
    const thinClient = Selector('[data-tc-asset-ctypes="Thin Client"]');
    const tv = Selector('[data-tc-asset-ctypes="Tv"]');
    const virtualMachine = Selector('[data-tc-asset-ctypes="Virtual Machine"]');
    const wap = Selector('[data-tc-asset-ctypes="WAP"]');
    const windows = Selector('[data-tc-asset-ctypes="Windows"]');
    await t.expect(golVari.currentpageurl()).contains('managed_assets/settings/asset_types')
      .wait(500)
      .hover(appleDevice)
      .expect(appleDevice.innerText).contains('Apple Device')
      .hover(desktop)
      .expect(desktop.innerText).contains('Desktop')
      .hover(dongle)
      .expect(dongle.innerText).contains('Dongle')
      .hover(fireWall)
      .expect(fireWall.innerText).contains('Firewall')
      .hover(iPad)
      .expect(iPad.innerText).contains('iPad')
      .hover(iPhone)
      .expect(iPhone.innerText).contains('iPhone')
      .wait(500)
      .hover(laptop)
      .expect(laptop.innerText).contains('Laptop')
      .hover(mobile)
      .expect(mobile.innerText).contains('Mobile')
      .hover(other)
      .expect(other.innerText).contains('Other')
      .hover(phone)
      .expect(phone.innerText).contains('Phone')
      .hover(phoneSystem)
      .expect(phoneSystem.innerText).contains('Phone System')
      .hover(printer)
      .expect(printer.innerText).contains('Printer')
      .hover(router)
      .expect(router.innerText).contains('Router')
      .hover(server)
      .expect(server.innerText).contains('Server')
      .hover(switchs)
      .expect(switchs.innerText).contains('Switch')
      .wait(500)
      .hover(tablet)
      .expect(tablet.innerText).contains('Tablet')
      .hover(thinClient)
      .expect(thinClient.innerText).contains('Thin Client')
      .hover(tv)
      .expect(tv.innerText).contains('Tv')
      .hover(virtualMachine)
      .expect(virtualMachine.innerText).contains('Virtual Machine')
      .hover(wap)
      .expect(wap.innerText).contains('WAP')
      .hover(windows)
      .expect(windows.innerText).contains('Windows')
      .wait(1000)
  }

  async addAssetTypeOnlyName(typeName) {
    const viewType = Selector(`[data-tc-asset-ctypes="${typeName}"]`);
    await t.expect(golVari.currentpageurl()).contains('managed_assets/settings/asset_types')
      .wait(500)
      .typeText(this.nameField, typeName, { paste: true })
      .wait(500)
      .hover(this.addTypeBtn)
      .click(this.addTypeBtn)
      .wait(1000)
      .hover(viewType)
      .expect(viewType.innerText).contains(typeName)
      .wait(1000)
  }

  async addDefaultTypes(defType) {
    const viewDefType = Selector(`[data-tc-inactive-default-type="${defType}"]`);
    const addTypeIcon = Selector(`[data-tc-add-default-types-icon="${defType}"]`);
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(1000)
      .hover(this.settingMenu)
      .click(this.settingMenu)
      .wait(1000)
      .hover(this.assetTypeTab)
      .click(this.assetTypeTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('managed_assets/settings/asset_types')
      .hover(viewDefType)
      .hover(addTypeIcon)
      .click(addTypeIcon)
      .wait(1000)
  }
}
export default addAssetType
