import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import addAsset from './add_assets';
import deleteAsset from './delete_assets';
import assetForm from './assets_form_ui_test_case';
import applyAssetFilters from './asset_filter';
import assetHardware from './asset_hardware';

const golVari = new golvar();
const naviBar = new navbar();
const addAstPage = new addAsset();
const delAstPage = new deleteAsset();
const hardwareAstPage = new assetHardware();
const astFormPage = new assetForm();
const applyingFilter = new applyAssetFilters();


class assetIndexPageAction {

  constructor() {
    this.archiveBadge = Selector('[data-tc-archived-badge]');
    this.unarchiveBtn = Selector('[data-tc-action="Unarchive"]');
    this.confirmUnarchived = Selector('[data-tc-unarchive-asset-btn]');
    this.removeArchiveStatus = Selector('[data-tc-remove-filter="Archived"]');
    this.mergeIcon = Selector('[data-tc-action="Merge"]');
    this.closeMergeBtn = Selector('[data-tc-btn="close merge"]');
    this.assetDropDown = Selector('[data-tc-merged-list]');
    this.mergeBtn = Selector('[data-tc-merge-save-btn]');
    this.confirmMergeBtn = Selector('[data-tc-merge-confirm-btn]');
    this.cancelConfirmBtn = Selector('[data-tc-btn="cancel merge modal"]');
    this.mergeSource = Selector('[data-tc-source="Merged"]');
    this.tagField = Selector('[data-tc-input="Add new Tag"] input');
    this.applyTagsIcon = Selector('[data-tc-action="Apply tags"]');
    this.addTagsBtn = Selector('[data-tc-btn="Add new Tag"]');
    this.saveTagBtn = Selector('[data-tc-save-tag-assignment]');
    this.archiveAstBtn = Selector('[data-tc-modal-asset-archive-btn]');
    this.primaryAstText = Selector('[data-tc-title="primary asset selection"]');
    this.listingAstText = Selector('[data-tc-text="associated merge assets"]');
    this.unmergeLink = Selector('[data-tc-link="unmerge"]');
    this.unmergeBtn = Selector('[data-tc-btn="unmerge"]');
    this.closeUnmergeBtn = Selector('[data-tc-btn="cancel unmerge"]');
    this.unmergeModalText = Selector('[data-tc-text="unmerge asset"]');
    this.bulkUpdateBtn = Selector('[data-tc-action="Bulk update"]');
    this.bulkUpdateTitle = Selector('[data-tc-title="bulk update"]');
    this.rowBulkNameField = '[data-tc-fields="Name"]';
    this.rowBulkMachineNumberField = '[data-tc-fields="MachineSerialNumber"]';
    this.rowBulkIpAddressField = '[data-tc-main-input="Ip Address"]';
    this.rowBulkProductNumberField = Selector('[data-tc-main-input="Product Number"]');
    this.rowBulkAssetTagField = Selector('[data-tc-main-input="Asset Tag"]');
    this.rowBulkDepartmentField = Selector('[data-tc-main-input="Department"]');
    this.rowBulkModelField = Selector('[data-tc-main-input="Model"]');
    this.rowBulkNoteField = Selector('[data-tc-main-input="Description"]');
    this.rowBulkUsedByDropDown = Selector('[data-tc-main-drop-down="Used By"]');
    this.rowBulkManagedByDropDown = Selector('[data-tc-main-drop-down="Managed By"]');
    this.rowBulkAssetTypeDropDown = Selector('[data-tc-main-drop-down="Asset Type"]');
    this.applyIpIcon = Selector('[data-tc-apply-icon="Ip Address"]');
    this.applyProductIcon = Selector('[data-tc-apply-icon="Product Number"]');
    this.applyAssetTagIcon = Selector('[data-tc-apply-icon="Asset Tag"]');
    this.applyDepartmentIcon = Selector('[data-tc-apply-icon="Department"]');
    this.applyModelIcon = Selector('[data-tc-apply-icon="Model"]');
    this.applyNoteIcon = Selector('[data-tc-apply-icon="Description"]');
    this.changeTypeTitle = Selector('[data-tc-title="changing asset type"] h2');
    this.yesChangeBtn = Selector('[data-tc-btn="yes, change asset type"]');
    this.updateAllBtn = Selector('[data-tc-btn="bulk update all"]');
    this.cancelUpdateBtn = Selector('[data-tc-btn="bulk cancel changes"]');
    this.bulkUpdateAgainBtn = Selector('[data-tc-btn="bulk update again"]');
    this.bulkNameHeader = Selector('[data-tc-field-header="Name"]');
    this.bulkSerialHeader = Selector('[data-tc-field-header="Machine Serial Number"]');
    this.bulkIpHeader = Selector('[data-tc-field-header="Ip Address"]');
    this.bulkProductHeader = Selector('[data-tc-field-header="Product Number"]');
    this.bulktagHeader = Selector('[data-tc-field-header="Asset Tag"]');
    this.bulkDepartmentHeader = Selector('[data-tc-field-header="Department"]');
    this.bulkAcquisitionHeader = Selector('[data-tc-field-header="Acquisition Date"]');
    this.bulkWarrantyHeader = Selector('[data-tc-field-header="Warranty Expiration"]');
    this.bulkInstallHeader = Selector('[data-tc-field-header="Install Date"]');
    this.bulkModelHeader = Selector('[data-tc-field-header="Model"]');
    this.bulkUsedByHeader = Selector('[data-tc-field-header="Used By"]');
    this.bulkManagedByHeader = Selector('[data-tc-field-header="Managed By"]');
    this.bulkNotesHeader = Selector('[data-tc-field-header="Notes"]');
    this.bulkAssetTypeHeader = Selector('[data-tc-field-header="Asset Type"]');
  }

  async navigateToAstIndexPage() {
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
      .hover(delAstPage.assetTab)
      .click(delAstPage.assetTab)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(1000)
  }

  async archiveAsset(astName) {
    const checkBox = Selector(`[data-tc-checkbox="${astName}"]`);
    const viewArchivedBadge = Selector(`[data-tc-asset-box-link="${astName}"] [data-tc-archived-badge]`);
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
      .hover(delAstPage.assetTab)
      .click(delAstPage.assetTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(1000)
      .click(checkBox)
      .wait(500)
      .click(delAstPage.archiveAction)
      .wait(500)
      .expect(this.archiveAstBtn.exists).ok()
      .click(this.archiveAstBtn)
      .wait(500)
      .expect(addAstPage.notifyPopUp.innerText).contains('Successfully archived this asset.')
      .wait(500)
      .hover(delAstPage.filterBtn)
      .click(delAstPage.filterBtn)
      .wait(500)
      .hover(delAstPage.statusDropDown)
      .click(delAstPage.statusDropDown)
      .wait(500)
      .hover(delAstPage.selectStatus)
      .click(delAstPage.selectStatus)
      .wait(1000)
      .expect(viewArchivedBadge.innerText).eql('Archived')
      .expect(viewArchivedBadge.visible).ok()
      .wait(500)
      .hover(this.removeArchiveStatus)
      .click(this.removeArchiveStatus)
      .wait(500)
      .expect(this.removeArchiveStatus.exists).notOk('Check the assets are removed');
  }

  async unarchiveAsset(astName1) {
    const checkBox1 = Selector(`[data-tc-checkbox="${astName1}"]`);
    const viewArchivedBadge = Selector(`[data-tc-asset-box-link="${astName1}"] [data-tc-archived-badge]`);
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
      .hover(delAstPage.assetTab)
      .click(delAstPage.assetTab)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(1000)
      .hover(delAstPage.filterBtn)
      .click(delAstPage.filterBtn)
      .wait(500)
      .hover(delAstPage.statusDropDown)
      .click(delAstPage.statusDropDown)
      .wait(500)
      .hover(delAstPage.selectStatus)
      .click(delAstPage.selectStatus)
      .wait(1000)
      .expect(checkBox1.exists).ok('Check asset is available or not')
      .click(checkBox1)
      .wait(500)
      .click(this.unarchiveBtn)
      .wait(500)
      .click(this.confirmUnarchived)
      .wait(500)
      .click(this.removeArchiveStatus)
      .wait(500)
      .typeText(delAstPage.searchField, astName1, { replace: true })
      .wait(500)
      .expect(viewArchivedBadge.exists).notOk()
      .wait(500);
  }

  async mergeAssets(primaryAsset, secondaryAsset, primaryMac, primaryIp, primarySerial) {
    const primaryCheckBox = Selector(`[data-tc-checkbox="${primaryAsset}"]`);
    const secondaryCheckBox = Selector(`[data-tc-checkbox="${secondaryAsset}"]`);
    const selectPrimaryAsset = Selector(`[data-tc-select-asset="${primaryAsset}"]`);
    const primarySource = Selector(`[data-tc-sourced-asset="${primaryAsset}"]`);
    const secondarySource = Selector(`[data-tc-sourced-asset="${secondaryAsset}"]`);
    const viewPriorMac = Selector(`[data-tc-mac="${primaryMac}"]`);
    const viewPriorIp = Selector(`[data-tc-asset-data="${primaryIp}"]`);
    const viewPriorSerial = Selector(`[data-tc-asset-data="${primarySerial}"]`);
    await t.expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(1000)
      .click(primaryCheckBox)
      .wait(500)
      .click(secondaryCheckBox)
      .wait(500)
      .expect(this.mergeIcon.exists).ok()
      .click(this.mergeIcon)
      .wait(500)
      .expect(this.primaryAstText.innerText).contains('Choose the primary asset that you wish to keep')
      .click(this.assetDropDown)
      .wait(500)
      .click(selectPrimaryAsset)
      .hover(this.mergeBtn)
      .click(this.mergeBtn)
      .wait(500)
      .click(this.confirmMergeBtn)
      .wait(1000)
      .expect(addAstPage.viewAstName.innerText).contains(primaryAsset)
      .wait(500)
      .click(addAstPage.sourceSideNav)
      .wait(500)
      .hover(primarySource)
      .expect(primarySource.innerText).contains(`${primaryAsset} Primary`)
      .hover(secondarySource)
      .expect(secondarySource.innerText).contains(`${secondaryAsset}`)
      .wait(500)
      .hover(addAstPage.viewAstName)
      .expect(addAstPage.viewAstName.innerText).contains(primaryAsset)
      .wait(500)
      .hover(hardwareAstPage.systemDetailTitle)
      .expect(viewPriorMac.innerText).contains(primaryMac)
      .expect(viewPriorIp.innerText).contains(primaryIp)
      .expect(viewPriorSerial.innerText).contains(primarySerial)
  }

  async cancelMergeAssets(primaryAsset1, secondaryAsset1) {
    const primaryCheckBox = Selector(`[data-tc-checkbox="${primaryAsset1}"]`);
    const secondaryCheckBox = Selector(`[data-tc-checkbox="${secondaryAsset1}"]`);
    const selectPrimaryAsset = Selector(`[data-tc-select-asset="${primaryAsset1}"]`);
    await t.expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(1000)
      .click(primaryCheckBox)
      .wait(500)
      .click(secondaryCheckBox)
      .wait(500)
      .expect(this.mergeIcon.exists).ok()
      .click(this.mergeIcon)
      .wait(500)
      .expect(this.primaryAstText.innerText).contains('Choose the primary asset that you wish to keep')
      .click(this.assetDropDown)
      .wait(500)
      .click(selectPrimaryAsset)
      .hover(this.mergeBtn)
      .click(this.mergeBtn)
      .wait(500)
      .click(this.cancelConfirmBtn)
      .wait(500)
      .click(this.closeMergeBtn)
      .wait(500)
      .expect(this.primaryAstText.exists).notOk()
      .hover(primaryCheckBox)
      .expect(primaryCheckBox.exists).ok()
      .hover(secondaryCheckBox)
      .expect(secondaryCheckBox.exists).ok()
      .wait(500)
  }

  async unmergeAssets(primaryAsset2, secondaryAsset2, primaryMac, primaryIp, primarySerial, secondaryMac, secondaryIp, secondarySerial) {
    const primarySource = Selector(`[data-tc-sourced-asset="${primaryAsset2}"]`);
    const viewAsset1 = Selector(`[data-tc-asset-box-link="${primaryAsset2}"]`);
    const viewAsset2 = Selector(`[data-tc-asset-box-link="${secondaryAsset2}"]`);
    const viewPriorMac = Selector(`[data-tc-mac="${primaryMac}"]`);
    const viewPriorIp = Selector(`[data-tc-asset-data="${primaryIp}"]`);
    const viewPriorSerial = Selector(`[data-tc-asset-data="${primarySerial}"]`);
    const viewSecondaryMac = Selector(`[data-tc-mac="${secondaryMac}"]`);
    const viewSecondaryIp = Selector(`[data-tc-asset-data="${secondaryIp}"]`);
    const viewSecondarySerial = Selector(`[data-tc-asset-data="${secondarySerial}"]`);
    await t.expect(addAstPage.viewAstName.innerText).contains(primaryAsset2)
      .wait(500)
      .click(addAstPage.sourceSideNav)
      .wait(500)
      .hover(primarySource)
      .expect(primarySource.innerText).contains(`${primaryAsset2} Primary`)
      .wait(500)
      .hover(this.listingAstText)
      .expect(this.listingAstText.innerText).contains('This asset was created by combining the assets listed below. You may unmerge them by clicking here.')
      .click(this.unmergeLink)
      .wait(500)
      .expect(this.unmergeModalText.innerText).contains('Are you sure you want to unmerge these assets?')
      .hover(this.unmergeBtn)
      .click(this.unmergeBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(500)
      .hover(viewAsset1)
      .expect(viewAsset1.exists).ok()
      .hover(viewAsset2)
      .expect(viewAsset2.exists).ok()
      .wait(500)
      .click(viewAsset1)
      .wait(1000)
      .hover(addAstPage.viewAstName)
      .expect(addAstPage.viewAstName.innerText).contains(primaryAsset2)
      .wait(500)
      .hover(hardwareAstPage.systemDetailTitle)
      .expect(viewPriorMac.innerText).contains(primaryMac)
      .expect(viewPriorIp.innerText).contains(primaryIp)
      .expect(viewPriorSerial.innerText).contains(primarySerial)
      .wait(500)
      .click(addAstPage.backAstLink)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(500)
      .click(viewAsset2)
      .wait(1000)
      .hover(addAstPage.viewAstName)
      .expect(addAstPage.viewAstName.innerText).contains(secondaryAsset2)
      .wait(500)
      .hover(hardwareAstPage.systemDetailTitle)
      .expect(viewSecondaryMac.innerText).contains(secondaryMac)
      .expect(viewSecondaryIp.innerText).contains(secondaryIp)
      .expect(viewSecondarySerial.innerText).contains(secondarySerial)
      .wait(500)
  }

  async cancelUnmergeAssets(primaryAsset2) {
    const primarySource = Selector(`[data-tc-sourced-asset="${primaryAsset2}"]`);
    await t.expect(addAstPage.viewAstName.innerText).contains(primaryAsset2)
      .wait(500)
      .click(addAstPage.sourceSideNav)
      .wait(500)
      .hover(primarySource)
      .expect(primarySource.innerText).contains(`${primaryAsset2} Primary`)
      .wait(500)
      .hover(this.listingAstText)
      .expect(this.listingAstText.innerText).contains('This asset was created by combining the assets listed below. You may unmerge them by clicking here.')
      .click(this.unmergeLink)
      .wait(500)
      .expect(this.unmergeModalText.innerText).contains('Are you sure you want to unmerge these assets?')
      .hover(this.closeUnmergeBtn)
      .click(this.closeUnmergeBtn)
      .wait(500)
      .hover(primarySource)
      .expect(primarySource.innerText).contains(`${primaryAsset2} Primary`)
      .wait(500)
  }

  async mergeAssetsIndividualFields(primaryAsset3, secondaryAsset3, secondaryMac1, secondaryIp1, secondarySerial1) {
    const primaryCheckBox = Selector(`[data-tc-checkbox="${primaryAsset3}"]`);
    const secondaryCheckBox = Selector(`[data-tc-checkbox="${secondaryAsset3}"]`);
    const selectPrimaryAsset = Selector(`[data-tc-select-asset="${primaryAsset3}"]`);
    const selectSecondaryMacRadio = Selector(`[data-tc-radio="${secondaryMac1}"]`);
    const selectSecondaryIpRadio = Selector(`[data-tc-radio="${secondaryIp1}"]`);
    const selectSecondarySerailRadio = Selector(`[data-tc-radio="${secondarySerial1}"]`);
    const viewSecondaryMac = Selector(`[data-tc-mac="${secondaryMac1}"]`);
    const viewSecondaryIp = Selector(`[data-tc-asset-data="${secondaryIp1}"]`);
    const viewSecondarySerial = Selector(`[data-tc-asset-data="${secondarySerial1}"]`);
    const primarySource = Selector(`[data-tc-sourced-asset="${primaryAsset3}"]`);
    const secondarySource = Selector(`[data-tc-sourced-asset="${secondaryAsset3}"]`);
    await t.hover(delAstPage.assetTab)
      .click(delAstPage.assetTab)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .click(primaryCheckBox)
      .wait(500)
      .click(secondaryCheckBox)
      .wait(500)
      .expect(this.mergeIcon.exists).ok()
      .click(this.mergeIcon)
      .wait(500)
      .expect(this.primaryAstText.innerText).contains('Choose the primary asset that you wish to keep')
      .click(this.assetDropDown)
      .wait(500)
      .click(selectPrimaryAsset)
      .wait(500)
      .click(selectSecondaryMacRadio)
      .wait(500)
      .click(selectSecondaryIpRadio)
      .wait(500)
      .click(selectSecondarySerailRadio)
      .wait(500)
      .hover(this.mergeBtn)
      .click(this.mergeBtn)
      .wait(500)
      .click(this.confirmMergeBtn)
      .wait(1000)
      .expect(addAstPage.viewAstName.innerText).contains(primaryAsset3)
      .wait(500)
      .click(addAstPage.sourceSideNav)
      .wait(500)
      .hover(primarySource)
      .expect(primarySource.innerText).contains(`${primaryAsset3} Primary`)
      .hover(secondarySource)
      .expect(secondarySource.innerText).contains(`${secondaryAsset3}`)
      .wait(500)
      .hover(hardwareAstPage.systemDetailTitle)
      .expect(viewSecondaryMac.innerText).contains(secondaryMac1)
      .expect(viewSecondaryIp.innerText).contains(secondaryIp1)
      .expect(viewSecondarySerial.innerText).contains(secondarySerial1)
  }

  async applyTags(assetName, newTags, alreadyAppliedtags) {
    const checkBox = Selector(`[data-tc-checkbox="${assetName}"]`);
    const viewAddedTag = Selector(`[data-tc-labels="${newTags}"]`);
    const viewAppliedTags1 = Selector(`[data-tc-asset-box-link="${assetName}"] [data-tc-view-tag="${alreadyAppliedtags}"]`);
    const viewAppliedTags2 = Selector(`[data-tc-asset-box-link="${assetName}"] [data-tc-view-tag="${newTags}"]`);
    const asset = Selector(`[data-tc-asset-box-link="${assetName}"]`);
    const viewTags1 = Selector(`[data-tc-view-tags="${alreadyAppliedtags}"]`);
    const viewTags2 = Selector(`[data-tc-view-tags="${newTags}"]`);
    await t.hover(delAstPage.assetTab)
      .click(delAstPage.assetTab)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(500)
      .click(checkBox)
      .wait(500)
      .expect(this.applyTagsIcon.visible).ok()
      .click(this.applyTagsIcon)
      .wait(500)
      .click(this.tagField)
      .wait(1000)
      .typeText(this.tagField, newTags, { paste: true })
      .wait(500)
      .click(this.addTagsBtn)
      .wait(500)
      .expect(viewAddedTag.innerText).contains(newTags)
      .doubleClick(viewAddedTag)
      .click(this.saveTagBtn)
      .wait(500)
      .expect(viewAppliedTags1.innerText).contains(alreadyAppliedtags)
      .expect(viewAppliedTags2.innerText).contains(newTags)
      .wait(500)
      .click(asset)
      .wait(1000)
      .expect(viewTags1.innerText).contains(alreadyAppliedtags)
      .expect(viewTags2.innerText).contains(newTags)
      .wait(500)
      .click(addAstPage.backAstLink)
      .wait(1000);
  }

  async removeTags(assetName, unSelectTag) {
    const checkBox = Selector(`[data-tc-checkbox="${assetName}"]`);
    const viewAddedTag = Selector(`[data-tc-labels="${unSelectTag}"]`);
    const viewRemovedTags = Selector(`[data-tc-asset-box-link="${assetName}"] [data-tc-view-tag="${unSelectTag}"]`);
    const asset = Selector(`[data-tc-asset-box-link="${assetName}"]`);
    const viewRemoveTags = Selector(`[data-tc-view-tags="${unSelectTag}"]`);
    await t.hover(delAstPage.assetTab)
      .click(delAstPage.assetTab)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(500)
      .click(checkBox)
      .wait(500)
      .expect(this.applyTagsIcon.visible).ok()
      .click(this.applyTagsIcon)
      .wait(500)
      .expect(viewAddedTag.innerText).contains(unSelectTag)
      .click(viewAddedTag)
      .click(this.saveTagBtn)
      .wait(500)
      .expect(viewRemovedTags.exists).notOk()
      .wait(500)
      .click(asset)
      .wait(1000)
      .expect(viewRemoveTags.exists).notOk()
      .wait(500)
      .click(addAstPage.backAstLink)
      .wait(1000);
  }

  async bulkUpdation(assetsName, editAssetsName, machineSerial, ipAddress, productNumber, assetTag, department,
    model, usedBy, managedBy, notes, assetTypes) {
    const [asset1, asset2, asset3] = assetsName.split(';');
    const [editAsset1, editAsset2, editAsset3] = editAssetsName.split(';');
    const [machineAsset1, machineAsset2, machineAsset3] = machineSerial.split(';');
    const [product1, product2] = productNumber.split(';');
    const [usedBy1, usedBy2] = usedBy.split(';');
    const [managedBy1, managedBy2] = managedBy.split(';');
    const [assetType1, assetType2] = assetTypes.split(';');
    const checkAst1Box = Selector(`[data-tc-checkbox="${asset1}"]`);
    const checkAst2Box = Selector(`[data-tc-checkbox="${asset2}"]`);
    const checkAst3Box = Selector(`[data-tc-checkbox="${asset3}"]`);
    const viewEditAsset1 = Selector(`[data-tc-asset-box-link="${editAsset1}"]`);
    const viewName1 = viewEditAsset1.find('[data-tc-asset-name]');
    const viewAstType1 = viewEditAsset1.find('[data-tc-view="asset type"]');
    const viewEditAsset2 = Selector(`[data-tc-asset-box-link="${editAsset2}"]`);
    const viewName2 = viewEditAsset2.find('[data-tc-asset-name]');
    const viewAstType2 = viewEditAsset2.find('[data-tc-view="asset type"]');
    const viewEditAsset3 = Selector(`[data-tc-asset-box-link="${editAsset3}"]`);
    const viewName3 = viewEditAsset3.find('[data-tc-asset-name]');
    const viewAstType3 = viewEditAsset3.find('[data-tc-view="asset type"]');
    const assetRow1 = Selector(`[data-tc-asset-row="${asset1}"]`);
    const assetRow2 = Selector(`[data-tc-asset-row="${asset2}"]`);
    const assetRow3 = Selector(`[data-tc-asset-row="${asset3}"]`);
    const nameInput1 = assetRow1.find(`${this.rowBulkNameField}`);
    const nameInput2 = assetRow2.find(`${this.rowBulkNameField}`);
    const nameInput3 = assetRow3.find(`${this.rowBulkNameField}`);
    const machineSerialInput1 = assetRow1.find(`${this.rowBulkMachineNumberField}`);
    const machineSerialInput2 = assetRow2.find(`${this.rowBulkMachineNumberField}`);
    const machineSerialInput3 = assetRow3.find(`${this.rowBulkMachineNumberField}`);
    const productNumberInput2 = assetRow2.find('[data-tc-fields="ProductNumber"]');
    const selectUsedBy1 = this.rowBulkUsedByDropDown.find('option').withText(usedBy1);
    const usedByDropDown2 = assetRow2.find('[data-tc-drop-down="UsedByContributorId"]');
    const selectUsedBy2 = usedByDropDown2.find('option').withText(usedBy2);
    const selectManagedBy1 = this.rowBulkManagedByDropDown.find('option').withText(managedBy1);
    const managedByDropDown2 = assetRow2.find('[data-tc-drop-down="ManagedByContributorId"]');
    const selectManagedBy2 = managedByDropDown2.find('option').withText(managedBy2);
    const selectAssetType1 = this.rowBulkAssetTypeDropDown.find('option').withText(assetType1);
    const assetTypeDropDown2 = assetRow2.find('[data-tc-drop-down="AssetTypeId"]');
    const selectAssetType2 = assetTypeDropDown2.find('option').withText(assetType2);
    const viewIpAddress = Selector(`[data-tc-asset-data="${ipAddress}"]`);
    const viewAstSerialNumber1 = Selector(`[data-tc-asset-data="${machineAsset1}"]`);
    const viewAstSerialNumber2 = Selector(`[data-tc-asset-data="${machineAsset2}"]`);
    const viewAstSerialNumber3 = Selector(`[data-tc-asset-data="${machineAsset3}"]`);

    await t.hover(delAstPage.assetTab)
      .click(delAstPage.assetTab)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(1000)
      .click(checkAst1Box)
      .wait(500)
      .click(checkAst2Box)
      .wait(500)
      .click(checkAst3Box)
      .wait(1000)
      .expect(this.bulkUpdateBtn.exists).ok('Check the bulk update is present.')
      .hover(this.bulkUpdateBtn)
      .click(this.bulkUpdateBtn)
      .wait(500)
      .expect(this.bulkUpdateTitle.innerText).eql(`Bulk Update (3)`)
      .expect(assetRow1.exists).ok('check asset 1 is present')
      .expect(assetRow2.exists).ok('check asset 2 is present')
      .expect(assetRow3.exists).ok('check asset 3 is present')
      .wait(500)
      .typeText(nameInput1, editAsset1, { replace: true })
      .typeText(nameInput2, editAsset2, { replace: true })
      .typeText(nameInput3, editAsset3, { replace: true })
      .wait(500)
      .typeText(machineSerialInput1, machineAsset1, { replace: true })
      .typeText(machineSerialInput2, machineAsset2, { replace: true })
      .typeText(machineSerialInput3, machineAsset3, { replace: true })
      .wait(500)
      .typeText(this.rowBulkIpAddressField, ipAddress, { replace: true })
      .click(this.applyIpIcon)
      .wait(100)
      .typeText(this.rowBulkProductNumberField, product1, { replace: true })
      .click(this.applyProductIcon)
      .wait(100)
      .typeText(productNumberInput2, product2, { replace: true })
      .wait(500)
      .typeText(this.rowBulkAssetTagField, assetTag, { replace: true })
      .click(this.applyAssetTagIcon)
      .wait(100)
      .typeText(this.rowBulkDepartmentField, department, { replace: true })
      .click(this.applyDepartmentIcon)
      .wait(500)
      .hover(this.rowBulkModelField)
      .typeText(this.rowBulkModelField, model, { replace: true })
      .click(this.applyModelIcon)
      .wait(500)
      .hover(this.rowBulkNoteField)
      .typeText(this.rowBulkNoteField, notes, { replace: true })
      .click(this.applyNoteIcon)
      .wait(500)
      .hover(this.rowBulkUsedByDropDown)
      .click(this.rowBulkUsedByDropDown)
      .wait(500)
      // .hover(selectUsedBy1)
      // .click(selectUsedBy1)
      // .hover(usedByDropDown2)
      // .click(usedByDropDown2)
      // .hover(selectUsedBy2)
      // .click(selectUsedBy2)
      // .wait(500)
      // .hover(this.rowBulkManagedByDropDown)
      // .click(this.rowBulkManagedByDropDown)
      // .hover(selectManagedBy1)
      // .click(selectManagedBy1)
      // .wait(500)
      // .hover(managedByDropDown2)
      // .click(managedByDropDown2)
      // .hover(selectManagedBy2)
      // .click(selectManagedBy2)
      // .wait(500)
      .hover(this.rowBulkAssetTypeDropDown)
      .click(this.rowBulkAssetTypeDropDown)
      .wait(300)
      .hover(selectAssetType1)
      .click(selectAssetType1)
      .wait(1000)
      .hover(this.changeTypeTitle)
      .expect(this.changeTypeTitle.innerText).eql('Changing asset type?')
      .wait(100)
      .hover(this.yesChangeBtn)
      .click(this.yesChangeBtn)
      .hover(assetTypeDropDown2)
      .click(assetTypeDropDown2)
      .wait(500)
      .hover(selectAssetType2)
      .click(selectAssetType2)
      .wait(1000)
      .hover(this.changeTypeTitle)
      .expect(this.changeTypeTitle.innerText).eql('Changing asset type?')
      .wait(100)
      .hover(this.yesChangeBtn)
      .click(this.yesChangeBtn)
      .wait(500)
      .hover(this.updateAllBtn)
      .click(this.updateAllBtn)
      .wait(1000)
      .expect(addAstPage.notifyPopUp.innerText).eql('Your assets have been updated successfully')
      .wait(500)
      .hover(viewEditAsset1)
      .expect(viewName1.innerText).eql(editAsset1)
      // .expect(viewAstType1.innerText).eql(`• ${assetType1}`)
      .hover(viewEditAsset2)
      .expect(viewName2.innerText).eql(editAsset2)
      // .expect(viewAstType2.innerText).eql(`• ${assetType2}`)
      .hover(viewEditAsset3)
      .expect(viewName3.innerText).eql(editAsset3)
      // .expect(viewAstType3.innerText).eql(`• ${assetType1}`)
      .wait(500)
      .click(viewEditAsset1)
      .wait(1000)
      .expect(addAstPage.viewAstName.innerText).eql(editAsset1)
      .hover(addAstPage.viewType)
      // .expect(addAstPage.viewType.innerText).eql(`• ${assetType1}`)
      .hover(addAstPage.viewTag)
      .expect(addAstPage.viewTag.innerText).contains(assetTag)
      // .hover(addAstPage.viewUsedBy)
      // .expect(addAstPage.viewUsedBy.innerText).contains(usedBy1)
      // .hover(addAstPage.viewManagedBy)
      // .expect(addAstPage.viewManagedBy.innerText).contains(managedBy1)
      .hover(addAstPage.viewModel)
      .expect(addAstPage.viewModel.innerText).contains(model)
      .hover(addAstPage.viewDepartment)
      .expect(addAstPage.viewDepartment.innerText).contains(department)
      .hover(viewIpAddress)
      .expect(viewIpAddress.innerText).contains(ipAddress)
      .hover(viewAstSerialNumber1)
      .expect(viewAstSerialNumber1.innerText).contains(machineAsset1)
      .hover(addAstPage.noteSideNav)
      .click(addAstPage.noteSideNav)
      .wait(500)
      .expect(addAstPage.noteSection.visible).ok()
      .hover(addAstPage.viewNotes)
      .expect(addAstPage.viewNotes.innerText).contains(notes)
      .hover(addAstPage.backAstLink)
      .click(addAstPage.backAstLink)
      .wait(1000)
      .click(viewEditAsset2)
      .wait(1000)
      .expect(addAstPage.viewAstName.innerText).eql(editAsset2)
      .hover(addAstPage.viewType)
      // .expect(addAstPage.viewType.innerText).eql(`• ${assetType2}`)
      .hover(addAstPage.viewTag)
      .expect(addAstPage.viewTag.innerText).contains(assetTag)
      // .hover(addAstPage.viewUsedBy)
      // .expect(addAstPage.viewUsedBy.innerText).contains(usedBy2)
      // .hover(addAstPage.viewManagedBy)
      // .expect(addAstPage.viewManagedBy.innerText).contains(managedBy2)
      .hover(addAstPage.viewModel)
      .expect(addAstPage.viewModel.innerText).contains(model)
      .hover(addAstPage.viewDepartment)
      .expect(addAstPage.viewDepartment.innerText).contains(department)
      .hover(viewIpAddress)
      .expect(viewIpAddress.innerText).contains(ipAddress)
      .hover(viewAstSerialNumber2)
      .expect(viewAstSerialNumber2.innerText).contains(machineAsset2)
      .hover(addAstPage.noteSideNav)
      .click(addAstPage.noteSideNav)
      .wait(500)
      .expect(addAstPage.noteSection.visible).ok()
      .hover(addAstPage.viewNotes)
      .expect(addAstPage.viewNotes.innerText).contains(notes)
      .hover(addAstPage.backAstLink)
      .click(addAstPage.backAstLink)
      .wait(1000)
      .click(viewEditAsset3)
      .wait(1000)
      .expect(addAstPage.viewAstName.innerText).eql(editAsset3)
      .hover(addAstPage.viewType)
      // .expect(addAstPage.viewType.innerText).eql(`• ${assetType1}`)
      .hover(addAstPage.viewTag)
      .expect(addAstPage.viewTag.innerText).contains(assetTag)
      // .hover(addAstPage.viewUsedBy)
      // .expect(addAstPage.viewUsedBy.innerText).contains(usedBy1)
      // .hover(addAstPage.viewManagedBy)
      // .expect(addAstPage.viewManagedBy.innerText).contains(managedBy1)
      .hover(addAstPage.viewModel)
      .expect(addAstPage.viewModel.innerText).contains(model)
      .hover(addAstPage.viewDepartment)
      .expect(addAstPage.viewDepartment.innerText).contains(department)
      .hover(viewIpAddress)
      .expect(viewIpAddress.innerText).contains(ipAddress)
      .hover(viewAstSerialNumber3)
      .expect(viewAstSerialNumber3.innerText).contains(machineAsset3)
      .hover(addAstPage.noteSideNav)
      .click(addAstPage.noteSideNav)
      .wait(500)
      .expect(addAstPage.noteSection.visible).ok()
      .hover(addAstPage.viewNotes)
      .expect(addAstPage.viewNotes.innerText).contains(notes)
      .hover(addAstPage.backAstLink)
      .click(addAstPage.backAstLink);
  }

  async bulkCancelUpdation(assetsName, editAssetsName, machineSerial, assetType, editAssetType) {
    const [asset1, asset2, asset3] = assetsName.split(';');
    const [editAsset1, editAsset2, editAsset3] = editAssetsName.split(';');
    const [machineAsset1, machineAsset2, machineAsset3] = machineSerial.split(';');
    const [assetType1, assetType2, assetType3] = assetType.split(';');
    const checkAst1Box = Selector(`[data-tc-checkbox="${asset1}"]`);
    const checkAst2Box = Selector(`[data-tc-checkbox="${asset2}"]`);
    const checkAst3Box = Selector(`[data-tc-checkbox="${asset3}"]`);
    const viewAsset1 = Selector(`[data-tc-asset-box-link="${asset1}"]`);
    const viewName1 = viewAsset1.find('[data-tc-asset-name]');
    const viewAstType1 = viewAsset1.find('[data-tc-view="asset type"]');
    const viewAsset2 = Selector(`[data-tc-asset-box-link="${asset2}"]`);
    const viewName2 = viewAsset2.find('[data-tc-asset-name]');
    const viewAstType2 = viewAsset2.find('[data-tc-view="asset type"]');
    const viewAsset3 = Selector(`[data-tc-asset-box-link="${asset3}"]`);
    const viewName3 = viewAsset3.find('[data-tc-asset-name]');
    const viewAstType3 = viewAsset3.find('[data-tc-view="asset type"]');
    const assetRow1 = Selector(`[data-tc-asset-row="${asset1}"]`);
    const assetRow2 = Selector(`[data-tc-asset-row="${asset2}"]`);
    const assetRow3 = Selector(`[data-tc-asset-row="${asset3}"]`);
    const machineSerialInput1 = assetRow1.find(`${this.rowBulkMachineNumberField}`);
    const machineSerialInput2 = assetRow2.find(`${this.rowBulkMachineNumberField}`);
    const machineSerialInput3 = assetRow3.find(`${this.rowBulkMachineNumberField}`);
    const selectAssetType = this.rowBulkAssetTypeDropDown.find('option').withText(editAssetType);
    const nameInput1 = assetRow1.find(`${this.rowBulkNameField}`);
    const nameInput2 = assetRow2.find(`${this.rowBulkNameField}`);
    const nameInput3 = assetRow3.find(`${this.rowBulkNameField}`);
    await t.hover(delAstPage.assetTab)
      .click(delAstPage.assetTab)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(1000)
      .click(checkAst1Box)
      .wait(500)
      .click(checkAst2Box)
      .wait(500)
      .click(checkAst3Box)
      .wait(1000)
      .expect(this.bulkUpdateBtn.exists).ok('Check the bulk update is present.')
      .hover(this.bulkUpdateBtn)
      .click(this.bulkUpdateBtn)
      .wait(500)
      .expect(this.bulkUpdateTitle.innerText).eql(`Bulk Update (3)`)
      .expect(assetRow1.exists).ok('check asset 1 is present')
      .expect(assetRow2.exists).ok('check asset 2 is present')
      .expect(assetRow3.exists).ok('check asset 3 is present')
      .wait(500)
      .typeText(nameInput1, editAsset1, { replace: true })
      .typeText(nameInput2, editAsset2, { replace: true })
      .typeText(nameInput3, editAsset3, { replace: true })
      .wait(500)
      .typeText(machineSerialInput1, machineAsset1, { replace: true })
      .typeText(machineSerialInput2, machineAsset2, { replace: true })
      .typeText(machineSerialInput3, machineAsset3, { replace: true })
      .wait(500)
      .hover(this.rowBulkAssetTypeDropDown)
      .click(this.rowBulkAssetTypeDropDown)
      .wait(300)
      .hover(selectAssetType)
      .click(selectAssetType)
      .wait(500)
      .hover(this.changeTypeTitle)
      .expect(this.changeTypeTitle.innerText).eql('Changing asset type?')
      .wait(100)
      .hover(this.yesChangeBtn)
      .click(this.yesChangeBtn)
      .wait(500)
      .hover(this.cancelUpdateBtn)
      .click(this.cancelUpdateBtn)
      .wait(1000)
      .hover(viewAsset1)
      .expect(viewName1.innerText).eql(asset1)
      .expect(viewAstType1.innerText).eql(`• ${assetType1}`)
      .hover(viewAsset2)
      .expect(viewName2.innerText).eql(asset2)
      .expect(viewAstType2.innerText).eql(`• ${assetType2}`)
      .hover(viewAsset3)
      .expect(viewName3.innerText).eql(asset3)
      .expect(viewAstType3.innerText).eql(`• ${assetType3}`);
  }

  async bulkUpdateModelCheck(assetName) {
    const checkAst1Box = Selector(`[data-tc-checkbox="${assetName}"]`);
    const assetRow1 = Selector(`[data-tc-asset-row="${assetName}"]`);
    await t.hover(delAstPage.assetTab)
      .click(delAstPage.assetTab)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .wait(1000)
      .click(checkAst1Box)
      .wait(500)
      .expect(this.bulkUpdateBtn.exists).ok('Check the bulk update is present.')
      .hover(this.bulkUpdateBtn)
      .click(this.bulkUpdateBtn)
      .wait(500)
      .hover(this.bulkUpdateTitle)
      .expect(this.bulkUpdateTitle.innerText).eql(`Bulk Update (1)`)
      .expect(assetRow1.exists).ok('check asset 1 is present')
      .wait(500)
      .hover(this.bulkNameHeader)
      .expect(this.bulkNameHeader.innerText).eql('Name')
      .hover(this.bulkSerialHeader)
      .expect(this.bulkSerialHeader.innerText).eql('Machine Serial Number')
      .hover(this.bulkIpHeader)
      .expect(this.bulkIpHeader.innerText).eql('Ip Address')
      .hover(this.bulkProductHeader)
      .expect(this.bulkProductHeader.innerText).eql('Product Number')
      .hover(this.bulktagHeader)
      .expect(this.bulktagHeader.innerText).eql('Asset Tag')
      .hover(this.bulkDepartmentHeader)
      .expect(this.bulkDepartmentHeader.innerText).eql('Department')
      .hover(this.bulkAcquisitionHeader)
      .expect(this.bulkAcquisitionHeader.innerText).eql('Acquisition Date')
      .hover(this.bulkWarrantyHeader)
      .expect(this.bulkWarrantyHeader.innerText).eql('Warranty Expiration')
      .hover(this.bulkInstallHeader)
      .expect(this.bulkInstallHeader.innerText).eql('Install Date')
      .hover(this.bulkModelHeader)
      .expect(this.bulkModelHeader.innerText).eql('Model')
      .hover(this.bulkUsedByHeader)
      .expect(this.bulkUsedByHeader.innerText).eql('Used By')
      .hover(this.bulkManagedByHeader)
      .expect(this.bulkManagedByHeader.innerText).eql('Managed By')
      .hover(this.bulkNotesHeader)
      .expect(this.bulkNotesHeader.innerText).eql('Notes')
      .hover(this.bulkAssetTypeHeader)
      .expect(this.bulkAssetTypeHeader.innerText).eql('Asset Type')
      .wait(500)
      .hover(this.cancelUpdateBtn)
      .click(this.cancelUpdateBtn)
      .wait(500)
      .click(checkAst1Box)
      .wait(500)
      .expect(this.bulkUpdateAgainBtn.exists).ok('Check button is present')
      .hover(this.bulkUpdateAgainBtn)
      .click(this.bulkUpdateAgainBtn)
      .wait(500)
      .hover(this.bulkUpdateTitle)
      .expect(this.bulkUpdateTitle.innerText).eql(`Bulk Update (1)`)
      .expect(assetRow1.exists).ok('check asset 1 is present')
      .wait(500)
      .hover(this.bulkNameHeader)
      .expect(this.bulkNameHeader.innerText).eql('Name')
      .hover(this.bulkSerialHeader)
      .expect(this.bulkSerialHeader.innerText).eql('Machine Serial Number')
      .hover(this.bulkIpHeader)
      .expect(this.bulkIpHeader.innerText).eql('Ip Address')
      .hover(this.bulkProductHeader)
      .expect(this.bulkProductHeader.innerText).eql('Product Number')
      .hover(this.bulktagHeader)
      .expect(this.bulktagHeader.innerText).eql('Asset Tag')
      .hover(this.bulkDepartmentHeader)
      .expect(this.bulkDepartmentHeader.innerText).eql('Department')
      .hover(this.bulkAcquisitionHeader)
      .expect(this.bulkAcquisitionHeader.innerText).eql('Acquisition Date')
      .hover(this.bulkWarrantyHeader)
      .expect(this.bulkWarrantyHeader.innerText).eql('Warranty Expiration')
      .hover(this.bulkInstallHeader)
      .expect(this.bulkInstallHeader.innerText).eql('Install Date')
      .hover(this.bulkModelHeader)
      .expect(this.bulkModelHeader.innerText).eql('Model')
      .hover(this.bulkUsedByHeader)
      .expect(this.bulkUsedByHeader.innerText).eql('Used By')
      .hover(this.bulkManagedByHeader)
      .expect(this.bulkManagedByHeader.innerText).eql('Managed By')
      .hover(this.bulkNotesHeader)
      .expect(this.bulkNotesHeader.innerText).eql('Notes')
      .hover(this.bulkAssetTypeHeader)
      .expect(this.bulkAssetTypeHeader.innerText).eql('Asset Type')
      .wait(500)
      .hover(this.cancelUpdateBtn)
      .click(this.cancelUpdateBtn);
  }
} export default assetIndexPageAction
