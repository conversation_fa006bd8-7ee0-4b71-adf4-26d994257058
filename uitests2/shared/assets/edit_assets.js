import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import assetForm from './assets_form_ui_test_case';
import addAsset from './add_assets';
import deleteAsset from './delete_assets';

const golVari = new golvar();
const naviBar = new navbar();
const mainAst = new addAsset();
const editAssets = new assetForm();
const delAst = new deleteAsset();

class editAsset {

  constructor() {
    this.assetStatusElement = '[data-tc-dropdown="asset statuses"]';
    this.statusSectionElement = '[data-tc-section="asset usage"]';
    this.editBtn = Selector('[data-tc-edit-asset]');
    this.addAttributeBtn = Selector('[data-tc-add-btn="new attribute"]');
    this.setItLocBtn = Selector('[data-tc-link-btn="location"]');
    this.editUsageIcon = Selector('[data-tc-icon="edit usage status"]');
    this.locModal = Selector('[data-tc-title="Location & Usage"] h2');
    this.usedByDropDown = Selector('[data-tc-used-by] [data-tc-multi-user-field]');
    this.usedByInput = Selector('[data-tc-used-by] input');
    this.managedByDropDown = Selector('[data-tc-managed-by] [data-tc-multi-user-field]');
    this.managedByInput = Selector('[data-tc-managed-by] input');
    this.locationDropDown = Selector('[data-tc-location-field]');
    this.locationInput = this.locationDropDown.find('input');
    this.saveUsageStatus = Selector('[data-tc-save-form-btn="Save Status"]');
    this.setItWartBtn = Selector('[data-tc-link-btn="warranty & acquisition"]');
    this.wartModal = Selector('[data-tc-title="Warranty & Acquisition"] h2');
    this.saveWartBtn = Selector('[data-tc-title="Warranty & Acquisition"] [data-tc-save-form-btn="Save"]');
    this.addCostBtn = Selector('[data-tc-btn="add cost data"]');
    this.costModal = Selector('[data-tc-title="Cost & Depreciation"]');
    this.saveCostBtn = Selector('[data-tc-btn="save cost"]');
    this.usageTitleModel = Selector('[data-tc-title="Usage Status"] h2');
    this.assetStatusLabel = Selector(`${this.statusSectionElement} label`);
    this.assetStatusDropDown = Selector(`${this.statusSectionElement} ${this.assetStatusElement}`);
    this.cancelUsageBtn = Selector('[data-tc-btn="cancel usage modal"]');
  }

  async editAssets(astName, editAstName, editAstType, editAstTags, editAstNote, editAstMacAddress, editAstIpAddress, editAstManufacturer, editAstModel, editTag, editAstSystemUUID, editAstSystemUpTime, editAstCost, editAstSalvage, editAstDepreciationType, editAstUsedBy, editAstManagedBy, editAstLocation, editAstDepartment, editAstSerialNumber, editAstProductNumber, editAstAcquisitionDate, editAstWarrantyExpiration, editAstInstallationDate, editKey, editDetails) {
    const viewTags = Selector(`[data-tc-manually-added="${editTag}"]`);
    const viewAstLoc = Selector(`[data-tc-manually-added="${editAstLocation}"]`);
    const viewAstType = Selector(`[data-tc-manually-added="${editAstType}"]`);
    const viewAstIp = Selector(`[data-tc-manually-added="${editAstIpAddress}"]`);
    const viewAstName = Selector(`[data-tc-manually-added="${editAstName}"]`);
    const viewAstManuf = Selector(`[data-tc-manually-added="${editAstManufacturer}"]`);
    const viewAstSerialNum = Selector(`[data-tc-manually-added="${editAstSerialNumber}"]`);
    const viewAstBox = Selector(`[data-tc-asset-box-link="${astName}"]`);
    const checkAstName = Selector(`[data-tc-asset-box-link="${astName}"] [data-tc-asset-name]`);
    const [purchasePrice, replacementCost, usefulLife, endLife] = editAstCost.split(':');
    await t.hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
      .hover(delAst.assetTab)
      .click(delAst.assetTab)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .hover(delAst.searchField)
      .typeText(delAst.searchField, astName, { paste: true })
      .wait(1000)
      .hover(viewAstBox)
      .expect(checkAstName.innerText).contains(astName)
      .click(viewAstBox)
      .wait(1000)
      .hover(this.editBtn)
      .click(this.editBtn)
      .wait(1000)
      .hover(mainAst.assetHeaderPage)
      .expect(mainAst.assetHeaderPage.innerText).contains('Edit Asset')
      .wait(1000);
    editAssets.editAssetForms(editAstName, editAstType, editAstTags, editAstNote, editAstMacAddress, editAstIpAddress, editAstManufacturer, editAstModel, editTag, editAstSystemUUID, editAstSystemUpTime, editAstCost, editAstSalvage, editAstDepreciationType, editAstUsedBy, editAstManagedBy, editAstLocation, editAstDepartment, editAstSerialNumber, editAstProductNumber, editAstAcquisitionDate, editAstWarrantyExpiration, editAstInstallationDate)
    await t.hover(mainAst.saveBtn)
      .click(mainAst.saveBtn)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('managed_assets')
      .hover(mainAst.viewAstName)
      .expect(mainAst.viewAstName.innerText).contains(editAstName)
      .hover(mainAst.viewManufacturer)
      .expect(mainAst.viewManufacturer.innerText).contains(editAstManufacturer)
      .hover(mainAst.viewModel)
      .expect(mainAst.viewModel.innerText).contains(editAstModel)
      .hover(mainAst.viewTag)
      .expect(mainAst.viewTag.innerText).contains(editAstTags)
      .wait(1000)
      .hover(mainAst.viewUsedBy)
      .expect(mainAst.viewUsedBy.innerText).contains(editAstUsedBy)
      .hover(mainAst.viewManagedBy)
      .expect(mainAst.viewManagedBy.innerText).contains(editAstManagedBy)
      .hover(mainAst.viewLocation)
      .expect(mainAst.viewLocation.innerText).contains(editAstLocation)
      .hover(mainAst.viewDepartment)
      .expect(mainAst.viewDepartment.innerText).contains(editAstDepartment)
      // .hover(mainAst.viewPurchasePrice)
      // .expect(mainAst.viewPurchasePrice.innerText).eql(`$${purchasePrice}`)
      // .hover(mainAst.viewReplacementCost)
      // .expect(mainAst.viewReplacementCost.innerText).eql(`$${replacementCost}`)
      // .hover(mainAst.viewSalvage)
      // .expect(mainAst.viewSalvage.innerText).eql(`$${editAstSalvage}`)
      // .hover(mainAst.viewUsefulLife)
      // .expect(mainAst.viewUsefulLife.innerText).eql(`${usefulLife} years`)
      // .hover(mainAst.viewEndLife)
      // .expect(mainAst.viewEndLife.innerText).eql(`${endLife} months`)
      // .wait(3000)
      .hover(mainAst.noteSideNav)
      .click(mainAst.noteSideNav)
      .wait(1000)
      .expect(mainAst.noteSection.visible).ok()
      .hover(mainAst.viewNotes)
      .expect(mainAst.viewNotes.innerText).contains(editAstNote)
      .wait(1000)
      .hover(mainAst.detailSideNav)
      .click(mainAst.detailSideNav)
      .expect(mainAst.customDetailSection.visible).ok()
      .wait(1000)
      .hover(this.addAttributeBtn)
      .click(this.addAttributeBtn)
      .wait(1000)
      .hover(mainAst.keyInput)
      .typeText(mainAst.keyInput, editKey, { paste: true })
      .wait(1000)
      .hover(mainAst.detailInput)
      .typeText(mainAst.detailInput, editDetails, { paste: true })
      .wait(500)
      .click(mainAst.submitDetailBtn)
      .wait(3000)
      .hover(mainAst.sourceSideNav)
      .click(mainAst.sourceSideNav)
      .expect(mainAst.sourceSection.visible).ok()
      .hover(mainAst.manuallyAddedTab)
      .click(mainAst.manuallyAddedTab)
      .wait(1000)
      .hover(viewTags)
      .expect(viewTags.innerText).contains(editTag)
      .wait(1000)
      .hover(viewAstLoc)
      .expect(viewAstLoc.innerText).contains(editAstLocation)
      .wait(1000)
      .hover(viewAstType)
      .expect(viewAstType.innerText).contains(editAstType)
      .wait(1000)
      .hover(viewAstIp)
      .expect(viewAstIp.innerText).contains(editAstIpAddress)
      .wait(1000)
      .hover(viewAstName)
      .expect(viewAstName.innerText).contains(editAstName)
      .wait(1000)
      .hover(viewAstManuf)
      .expect(viewAstManuf.innerText).contains(editAstManufacturer)
      .wait(1000)
      .hover(viewAstSerialNum)
      .expect(viewAstSerialNum.innerText).contains(editAstSerialNumber)
      .wait(1000)
  }

  async editAssetDetails(asset, usedByUser, manageByUser, location1, department1, machineSerial, productNumber, status) {
    const selectAsset = Selector(`[data-tc-asset-box-link="${asset}"]`);
    this.assetStatusLabel = Selector(`${this.statusSectionElement} label`);
    const selectStatus = Selector(`${this.statusSectionElement} [data-tc-status-option="${status}"]`);
    await t.hover(delAst.assetTab)
      .click(delAst.assetTab)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/managed_assets/assets')
      .hover(selectAsset)
      .click(selectAsset)
      .wait(1000)
      .expect(delAst.viewsearchedAsset.innerText).eql(asset)
      .hover(this.setItLocBtn)
      .click(this.setItLocBtn)
      .wait(500)
      .expect(this.usageTitleModel.innerText).contains('Usage Status')
      .hover(this.cancelUsageBtn)
      .click(this.cancelUsageBtn)
      .wait(500)
      .hover(this.editUsageIcon)
      .click(this.editUsageIcon)
      .wait(500)
      .expect(this.usageTitleModel.innerText).contains('Usage Status')
      .hover(this.assetStatusLabel)
      .expect(this.assetStatusLabel.innerText).contains('Asset Status')
      .hover(this.assetStatusDropDown)
      .click(this.assetStatusDropDown)
      .wait(500)
      .hover(selectStatus)
      .click(selectStatus)
      .wait(500)
      .expect(this.assetStatusDropDown.innerText).eql(` ${status} `)
      .click(this.usedByDropDown)
      .wait(500)
      .typeText(this.usedByInput, usedByUser, { paste: true })
      .wait(500)
      .pressKey('enter')
      .wait(1000)
      .click(this.managedByDropDown)
      .wait(1000)
      .typeText(this.managedByInput, manageByUser, { paste: true })
      .wait(500)
      .pressKey('enter')
      .wait(1000)
      .click(this.locationDropDown)
      .wait(1000)
      .typeText(this.locationInput, location1, { paste: true })
      .wait(500)
      .pressKey('enter')
      .wait(1000)
      .click(editAssets.assetDepartment)
      .typeText(editAssets.assetDepartment, department1, { paste: true })
      .wait(500)
      .click(this.saveUsageStatus)
      .wait(1000)
      .expect(mainAst.notifyPopUp.innerText).contains('Asset was successfully updated')
      .wait(500)
      .hover(this.setItWartBtn)
      .wait(500)
      .click(this.setItWartBtn)
      .wait(1000)
      .expect(this.wartModal.innerText).contains('Warranty & Acquisition')
      .wait(1000)
      .hover(editAssets.assetSerialNumber)
      .typeText(editAssets.assetSerialNumber, machineSerial, { paste: true })
      .wait(500)
      .hover(editAssets.assetProductNumber)
      .typeText(editAssets.assetProductNumber, productNumber, { paste: true })
      .wait(1000)
      .click(this.saveWartBtn)
      .wait(1000)
      .expect(mainAst.notifyPopUp.innerText).contains('Asset was successfully updated')
      .wait(500)
      .expect(mainAst.notifyPopUp.innerText).contains('Asset was successfully updated')
      .wait(1000)
      .hover(mainAst.viewUsedBy)
      .expect(mainAst.viewUsedBy.innerText).contains(usedByUser)
      .hover(mainAst.viewManagedBy)
      .expect(mainAst.viewManagedBy.innerText).contains(manageByUser)
      .hover(mainAst.viewLocation)
      .expect(mainAst.viewLocation.innerText).contains(location1)
      .hover(mainAst.viewDepartment)
      .expect(mainAst.viewDepartment.innerText).contains(department1);
  }
} export default editAsset
