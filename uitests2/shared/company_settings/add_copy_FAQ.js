import { Selector, t } from 'testcafe';
import templatepage from './template_tab';
import copyGroup from './add_copy_group';
import switchCompany from './switch _company';
import addcontracts from '../contracts/add_contract';

const naviTotemplate = new templatepage();
const copiedGroup = new copyGroup();
const companySwitch = new switchCompany();
const contractPage = new addcontracts();

class copyFAQ {

  constructor() {
    this.addFAQIcon = Selector(`[data-tc-plus-icon="FAQ's"]`);
    this.questionField = Selector('[data-tc-input-editor="question"]');
    this.answerField = Selector('[data-tc-input-editor="answer"]');
    this.submitBtn = Selector('[data-tc-save-form-btn="Submit"]');
    this.faqChevronDrop = Selector(`[data-tc-chevron-down="FAQ's"]`);
  }

  async copyFAQs(selectCategory, question, answer, companyName, parentCompany, templateName) {
    const copyQnIcon = Selector(`[data-tc-copy-icon="<div><!--block-->${question}</div>"]`);
    const destinationCompany = Selector(`[data-tc-check-box="${companyName}"]`);
    const viewCompany = Selector(`[data-tc-view-destination-company="${companyName}"]`);
    const parentBox = `[data-tc-template="${templateName}"]`
    const tempBox = Selector(`${parentBox}`);
    const viewName = Selector(`${parentBox} [data-tc-view-name]`);
    companySwitch.switchToCompany(parentCompany)
    await t.wait(1000)
    naviTotemplate.templateview()
    await t
      .hover(tempBox)
      .expect(tempBox.visible).ok()
      .wait(1000)
      .expect(viewName.innerText).contains(templateName)
      .wait(500)
      .click(tempBox)
      .wait(1000)
      .hover(this.addFAQIcon)
      .click(this.addFAQIcon)
      .wait(1000)
      .hover(contractPage.categoryDropDown)
      .wait(1000)
      .click(contractPage.categoryDropDown)
      .wait(500)
      .click(contractPage.selectCategory.withText(selectCategory))
      .wait(1000)
      .typeText(this.questionField, question, { paste: true })
      .wait(500)
      .typeText(this.answerField, answer, { paste: true })
      .wait(1000)
      .click(this.submitBtn)
      .wait(1000)
      .hover(this.faqChevronDrop)
      .click(this.faqChevronDrop)
      .wait(1000)
      .hover(copyQnIcon)
      .click(copyQnIcon)
      .wait(1000)
      .hover(destinationCompany)
      .click(destinationCompany)
      .wait(500)
      .click(copiedGroup.nextBtn)
      .wait(1000)
      .hover(viewCompany)
      .expect(viewCompany.innerText).contains(companyName)
      .wait(1000)
      .click(copiedGroup.nextBtn)
      .wait(3000)
      .click(naviTotemplate.saveTemBtn)
      .wait(1000);
  }
} export default copyFAQ
