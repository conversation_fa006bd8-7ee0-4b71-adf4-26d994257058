import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";

const navibar = new navbar();
const golvari = new golvar();

class removeCategory {

  constructor() {
    this.CatBtn = Selector('[data-tc-categories-tab]');
    this.currentCatHead = Selector('[data-tc-current-categories-header]');
    this.addCatBtn = Selector('[data-tc-btn-add-category]');
    this.addedCatName = Selector('[data-tc-category-name] .align-sub');
    this.notifyPopUp = Selector('.notification-content');
  }

  async removeCurrentCategory(categoryName) {
    const viewCategory = Selector(`[data-tc-view-name="${categoryName}"]`);
    const removeCategoryIcon = Selector(`[data-tc-remove-category-icon="${categoryName}"]`);
    await t.hover(navibar.companySettingTab)
      .click(navibar.companySettingTab)
      .wait(500)
      .hover(this.CatBtn)
      .click(this.CatBtn)
      .expect(golvari.currentpageurl()).contains('/company/categories')
      .hover(this.currentCatHead)
      .expect(this.currentCatHead.innerText).contains('Current Company Categories')
      .hover(viewCategory)
      .hover(removeCategoryIcon)
      .click(removeCategoryIcon)
      .hover(this.notifyPopUp)
      .expect(this.notifyPopUp.innerText).contains(`Successfully removed ${categoryName} from your categories`)
      .wait(1000);
  }
} export default removeCategory
