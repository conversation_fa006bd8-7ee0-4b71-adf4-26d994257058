import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import setUserPermission from './set_permission_user';
import companyvar from './company_variables';
import addAssetType from '../assets/add_asset_type';
import addlocation from './add_location';

const golvari = new golvar();
const navibar = new navbar();
const assetTypePage = new addAssetType();
const addLocPage = new addlocation();

class addstaff {

  constructor() {
    this.staffsidebar = Selector('[data-tc-staff]');
    this.staffbtn = Selector('[data-tc-your-staff]');
    this.addstaffbtn = Selector('[data-tc-add-staff-btn] button');
    this.newstafform = Selector('[data-tc-header-form]');
    this.fnamefield = Selector('[data-tc-text-field="first_name"]');
    this.lnamefield = Selector('[data-tc-text-field="last_name"]');
    this.emailfield = Selector('[data-tc-text-field="email"]');
    this.extensionfield = Selector('[data-tc-text-field="extension"]');
    this.titlefield = Selector('[data-tc-text-field="title"]');
    this.departmentfield = Selector('[data-tc-text-field="department"]');
    this.locationfield = Selector('[data-tc-text-field="location"]');
    this.selectLocation = this.locationfield.find('li');
    this.supervisorField = Selector('[data-tc-text-field="supervisor"]');
    this.selectSupervisor = this.supervisorField.find('li');
    this.phonenumber = Selector('[data-tc-text-field="mobile_phone"]');
    this.createstaffbtn = Selector('[data-tc-save-form-btn="Create teammate"]');
    this.backstaffbtn = Selector('[data-tc-back-staff-btn]');
    this.searchfield = Selector('[data-tc-company-user-search]');
    this.searchedstaff = Selector('[data-tc-user]');
    this.viewfnlname = Selector('[data-tc-user-full-name]');
    this.viewemail = Selector('[data-tc-email]');
    this.viewsupervisor = Selector('[data-tc-selected="supervisor"]');
    this.requirefield = Selector('[data-tc-require-field="email"]');
    this.toggleBtn = Selector('[data-tc-toggle-btn="company_user"]');
    this.emailNav = Selector('[data-tc-require-field="email"]');
    this.staticLabel = Selector('[data-tc-check-label="Static Text"]');
    this.mobileLabel = Selector('[data-tc-check-label="Mobile Phone"]');
    this.locationLabel = Selector('[data-tc-check-label="Location List"]');
    this.locationListField = Selector('[data-tc-text-field="location_list"]');
    this.selectLocList = this.locationListField.find('li');
    this.inputAttachment = Selector('[data-tc-input-file]');
    this.imageParent = Selector('[data-tc-attachment-image]');
    this.inputImages = Selector('[data-tc-attachment-image] [data-tc-input-file]');
  }

  async addstaff(fname, lname, email, title, phonenumber, extension, department, location) {
    const viewLocation = Selector(`[data-tc-view-location="${location}"]`);
    const viewDepartment = Selector(`[data-tc-view-department="${department}"]`);
    const viewSupervisor = Selector('[data-tc-view-supervisor="Admins"]');
    navibar.navigatetoPeoplePage()
    await t.expect(this.addstaffbtn.exists).ok()
      .hover(this.addstaffbtn)
      .click(this.addstaffbtn)
      .wait(1000)
      .expect(this.newstafform.innerText).contains('New Teammate')
      .typeText(this.fnamefield, fname,{ speed: 0.2 })
      .typeText(this.lnamefield, lname, { speed: 0.2 })
      .typeText(this.emailfield, email, { paste: true })
      .typeText(this.titlefield, title, { paste: true })
      .hover(this.requirefield)
      .click(this.requirefield)
      .typeText(this.departmentfield, department, { paste: true })
      .typeText(this.phonenumber, phonenumber, { paste: true })
      .typeText(this.extensionfield, extension, { paste: true })
      .wait(1000)
      .hover(this.locationfield)
      .wait(500)
      .click(this.locationfield)
      .wait(500)
      .hover(this.selectLocation.withText(location))
      .click(this.selectLocation.withText(location))
      .pressKey('enter')
      .wait(500)
      .hover(this.supervisorField)
      .click(this.supervisorField)
      .click(this.selectSupervisor.withText('Admins'))
      .wait(500)
      .setFilesToUpload(this.inputAttachment, '../../shared/assets/inputImage.png')
      .click(this.imageParent)
      .setFilesToUpload(this.inputImages, '../../shared/assets/inputImage.png')
      .wait(500)
      .hover(this.createstaffbtn)
      .click(this.createstaffbtn)
      .wait(1000)
      .click(this.backstaffbtn)
      .expect(golvari.currentpageurl()).contains('/company/users')
      .hover(this.searchfield)
      .click(this.searchfield)
      .typeText(this.searchfield, email, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .hover(this.viewfnlname)
      .hover(this.searchedstaff)
      .click(this.searchedstaff)
      .wait(500)
      .hover(this.viewemail)
      .expect(this.viewemail.innerText).contains(email)
      .wait(500)
      .expect(viewLocation.innerText).contains(location)
      .wait(500)
      .expect(viewDepartment.innerText).contains(department)
      .wait(500)
      .expect(viewSupervisor.innerText).contains('Admins')
      .click(this.backstaffbtn);
  }

  async addStaffThroCustomForm(selectForm, ftName, ltName, userEmail, text, locationName, textArea, richText, phoneNumber, tag, date, list, number) {
    const formOption = Selector(`[data-tc-icon="${selectForm}"]`);
    const viewStaffEmail = Selector(`[data-tc-view-email="${userEmail}"]`);
    await t.hover(this.staffsidebar)
      .click(this.staffsidebar)
      .expect(golvari.currentpageurl()).contains('/company/users')
      .wait(1000)
      .hover(this.toggleBtn)
      .click(this.toggleBtn)
      .wait(1000)
      .hover(formOption)
      .click(formOption)
      .wait(1000)
      .expect(golvari.currentpageurl()).contains('/company/users/new?formId=')
      .expect(addLocPage.viewSelectedLoc.innerText).contains(selectForm)
      .wait(500)
      .typeText(this.fnamefield, ftName, { paste: true })
      .wait(500)
      .typeText(this.lnamefield, ltName, { paste: true })
      .wait(500)
      .typeText(this.emailfield, userEmail, { paste: true })
      .wait(1000)
      .hover(addLocPage.textLabel)
      .wait(1000)
      .typeText(addLocPage.inputText, text, { paste: true })
      .wait(1000)
      .hover(addLocPage.avatarLabel)
      .wait(500)
      .hover(assetTypePage.uploadImage)
      .wait(500)
      .click(assetTypePage.uploadImage)
      .wait(500)
      .setFilesToUpload(assetTypePage.dropFile, '../../shared/company_settings/locationavatar.jpg')
      .wait(500)
      .click(this.emailNav)
      .wait(1000)
      .typeText(addLocPage.inputDate, date, { paste: true })
      .wait(500)
      .pressKey('enter')
      .wait(1000)
      .hover(addLocPage.phoneLabel)
      .wait(500)
      .typeText(addLocPage.inputPhone, phoneNumber, { paste: true })
      .wait(1000)
      .wait(1000)
      .hover(addLocPage.listLabel)
      .wait(500)
      .click(addLocPage.listDropDown)
      .wait(500)
      .click(addLocPage.selectList.withText(list))
      .wait(500)
      .hover(addLocPage.radioBtnLabel)
      .wait(1000)
      .click(addLocPage.checkedRadio)
      .wait(1000)
      .hover(this.locationLabel)
      .wait(1000)
      .hover(this.locationListField)
      .wait(500)
      .click(this.locationListField)
      .wait(1000)
      .hover(this.selectLocList.withText(locationName))
      .click(this.selectLocList.withText(locationName))
      .wait(1000)
      .hover(addLocPage.numberLabel)
      .wait(500)
      .typeText(addLocPage.inputNumber, number, { paste: true })
      .wait(1000)
      .scrollIntoView(addLocPage.checkBoxLabel)
      .wait(500)
      .hover(addLocPage.checkBoxLabel)
      .wait(1000)
      .click(addLocPage.checkedBox)
      .wait(1000)
      .scrollIntoView(addLocPage.tagLabel)
      .wait(500)
      .click(addLocPage.tagField)
      .wait(500)
      .click(addLocPage.selectTag.withText(tag))
      .wait(500)
      .hover(addLocPage.textAreaLabel)
      .wait(500)
      .typeText(addLocPage.inputTextArea, textArea, { paste: true })
      .wait(500)
      .hover(addLocPage.richAreaLabel)
      .wait(1000)
      .typeText(addLocPage.inputRichText, richText, { paste: true })
      .wait(500)
      .scrollIntoView(addLocPage.richAreaLabel)
      .wait(500)
      .hover(this.staticLabel)
      .wait(1000)
      .hover(this.mobileLabel)
      .typeText(this.phonenumber, phoneNumber, { paste: true })
      .wait(1000)
      .hover(this.createstaffbtn)
      .click(this.createstaffbtn)
      .wait(1000)
      .expect(golvari.currentpageurl()).contains('/company/users')
      .wait(1000)
      .click(this.backstaffbtn)
      .wait(1000)
      .expect(golvari.currentpageurl()).contains('/company/users')
      .hover(this.searchfield)
      .click(this.searchfield)
      .wait(500)
      .typeText(this.searchfield, userEmail, { paste: true })
      .pressKey('enter')
      .wait(2000)
      .hover(viewStaffEmail)
      .wait(1000)
      .expect(viewStaffEmail.innerText).contains(userEmail);
  }
} export default addstaff
