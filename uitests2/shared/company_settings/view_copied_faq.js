import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import switchCompany from './switch _company';

const golVari = new golvar();
const naviBar = new navbar();
const switchChildComp = new switchCompany();

class viewCopiedFaq {

  constructor() {
    // this.resourceTab = Selector('[data-tc-resources-tab]');
    this.faqTab = Selector('[data-tc-sub-menu="FAQ"]');
    this.searchQuestion = Selector('[data-tc-faq-search]');
    this.viewDocument = Selector('[data-tc-document-name]');
  }

  async viewCopiedFaqs(selectCompany, question) {
    const viewQn = Selector(`[data-tc-question="<div><!--block-->${question}</div>"]`);
    switchChildComp.switchToCompany(selectCompany)
    await t.wait(1000)
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(naviBar.resourceNavMenu)
      .click(naviBar.resourceNavMenu)
      .expect(golVari.currentpageurl()).contains('/help_tickets/articles')
      .wait(500)
      .click(this.faqTab)
      .expect(golVari.currentpageurl()).contains('/help_tickets/faqs')
      .wait(500)
      .typeText(this.searchQuestion, question, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .expect(viewQn.innerText).contains(question);
  }
} export default viewCopiedFaq
