import { Selector, t } from 'testcafe';
import navbar from '../Components/navbar';
import golvar from '../Globalvariable';

const golVari = new golvar();
const naviBar = new navbar();

class addgroup {

  constructor() {
    this.groupTab = Selector('[data-tc-groups-tab]');
    this.addgroupbtn = Selector('[data-tc-add-group-btn]');
    this.groupInput = Selector('[data-tc-input-group-name]');
    this.helpDeskDropDown = Selector('[data-tc-helpdesk-dropdown]');
    this.selectionHelpPermission = Selector('[data-tc-helpdesk-dropdown] [data-tc-help-desk-permission-items="Write All"]');
    this.memberOption = Selector('[data-tc-multi-user-field]');
    this.searchMember = Selector('[id="company-users"]');
    this.memberSelected = Selector('[data-tc-select-impacted-user]');
    this.saveGroupBtn = Selector('[data-tc-save-form-btn="Save group"]');
    this.searchByName = Selector('[data-tc-company-user-search]');
    this.viewGroup = Selector('[data-tc-group-name]');
    this.viewUserName = Selector('[data-tc-view="user"]');
    this.assetDropDown = Selector('[data-tc-select-permission="Asset Management"]');
    this.selectAstPerm = Selector('[data-tc-select-permission="Asset Management"] [data-tc-help-desk-permission-items="Write All"]');
    this.vendorDropDown = Selector('[data-tc-select-permission="SaaS and Vendor Management"]');
    this.selectVendorPerm = Selector('[data-tc-select-permission="SaaS and Vendor Management"] [data-tc-help-desk-permission-items="Write All"]');
    this.contractDropDown = Selector('[data-tc-select-permission="Contract Management"]');
    this.selectContPerm = Selector('[data-tc-select-permission="Contract Management"] [data-tc-help-desk-permission-items="Write All"]');
    this.telecomDropDown = Selector('[data-tc-select-permission="Telecom Management"]');
    this.selectTelePerm = Selector('[data-tc-select-permission="Telecom Management"] [data-tc-help-desk-permission-items="Write All"]');
    this.networkDropDown = Selector('[data-tc-select-permission="Network Monitoring"]');
    this.selectNetPerm = Selector('[data-tc-select-permission="Network Monitoring"] [data-tc-help-desk-permission-items="Write All"]');
    this.companyDropDown = Selector('[data-tc-select-permission="Company Settings"]');
    this.selectCompanyPerm = Selector('[data-tc-select-permission="Company Settings"] [data-tc-help-desk-permission-items="Write All"]');
    this.viewMemberList = Selector('[data-tc-multi-user-field] ul');
    this.member = this.viewUserName.innerText;
  }

  async addgroup(groupName) {
    const viewGroup = Selector(`[data-tc-group="${groupName}"]`);
    const userElement = Selector('[data-tc-view="user"]');
    const getUserText = await userElement.innerText;
    const selectUser = Selector(`[data-tc-selected-object="${getUserText}"]`);
    const memberSelected = Selector(`[data-tc-contributor="${getUserText}"]`);
    await t.hover(this.groupTab)
      .click(this.groupTab)
      .expect(golVari.currentpageurl()).contains('company/groups')
      .wait(500)
      .hover(this.addgroupbtn)
      .click(this.addgroupbtn)
      .expect(golVari.currentpageurl()).contains('company/groups/new')
      .typeText(this.groupInput, groupName, { paste: true })
      .click(this.assetDropDown)
      .wait(500)
      .click(this.selectAstPerm)
      .wait(500)
      .click(this.vendorDropDown)
      .wait(500)
      .click(this.selectVendorPerm)
      .wait(500)
      .click(this.contractDropDown)
      .wait(500)
      .click(this.selectContPerm)
      .wait(500)
      .click(this.telecomDropDown)
      .wait(500)
      .click(this.selectTelePerm)
      .wait(500)
      .click(this.networkDropDown)
      .wait(500)
      .click(this.selectNetPerm)
      .wait(500)
      .click(this.companyDropDown)
      .wait(500)
      .click(this.selectCompanyPerm)
      .wait(500)
      .click(this.helpDeskDropDown)
      .hover(this.selectionHelpPermission)
      .click(this.selectionHelpPermission)
      .expect(this.helpDeskDropDown.innerText).contains('Write All')
      .click(this.memberOption)
      .hover(this.viewUserName)
      .wait(500)
      .click(selectUser)
      .wait(500)
      .expect(this.viewMemberList.visible).ok()
      .wait(500)
      .expect(memberSelected.innerText).contains(getUserText)
      .hover(this.saveGroupBtn)
      .click(this.saveGroupBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/company/groups')
      .expect(viewGroup.innerText).eql(groupName)
      .wait(500);
  }

  async addgroupWithoutPerms(groupName1) {
    const viewGroup = Selector(`[data-tc-group="${groupName1}"]`);
    const userElement = Selector('[data-tc-view="user"]');
    const getUserText = await userElement.innerText;
    const selectUser = Selector(`[data-tc-selected-object="${getUserText}"]`);
    const memberSelected = Selector(`[data-tc-contributor="${getUserText}"]`);
    naviBar.navigatetocompanysetting()
    await t.hover(this.groupTab)
      .click(this.groupTab)
      .expect(golVari.currentpageurl()).contains('company/groups')
      .wait(1000)
      .hover(this.addgroupbtn)
      .click(this.addgroupbtn)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('company/groups/new')
      .typeText(this.groupInput, groupName1, { paste: true })
      .wait(1000)
      .click(this.memberOption)
      .hover(this.viewUserName)
      .hover(this.searchMember)
      .click(this.searchMember)
      .click(selectUser)
      .wait(500)
      .wait(1000)
      .expect(this.viewMemberList.visible).ok()
      .wait(500)
      .expect(memberSelected.innerText).contains(getUserText)
      .hover(this.saveGroupBtn)
      .click(this.saveGroupBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('company/groups')
      .wait(1000)
      .expect(viewGroup.innerText).contains(groupName1)
      .wait(1000)
  }
} export default addgroup
