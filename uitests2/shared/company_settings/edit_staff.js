import { Selector, t } from 'testcafe'
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";

const navibar = new navbar()
const golvari = new golvar()

class editstaff {

  constructor() {
    this.staffsidebar = Selector('[data-tc-staff]');
    this.editstafform = Selector('[data-tc-header-form]');
    this.searchfield = Selector('[data-tc-company-user-search]');
    this.searchedstaff = Selector('[data-tc-user]');
    this.viewemail = Selector('[data-tc-email]');
    this.editbtn = Selector('[data-tc-edit-usr-btn]');
    this.edituserform = Selector('[data-tc-header-form]');
    this.fnamefield = Selector('[data-tc-text-field="first_name"]');
    this.lnamefield = Selector('[data-tc-text-field="last_name"]');
    this.phonenumberfield = Selector('[data-tc-text-field="mobile_phone"]');
    this.updatestaffbtn = Selector('[data-tc-save-form-btn="Update teammate"]');
    this.backstaffbtn = Selector('[data-tc-back-staff-btn]');
  }

  async editstaff(addedstaffemail, fname, lname, phonenumber) {
    const searchedUser = Selector(`[data-tc-company-user-line-item="${fname} ${lname}"]`);
    await t.wait(1000)
    navibar.navigatetoPeoplePage()
    await t.wait(1000)
      // .expect(golvari.currentpageurl()).contains('/company/users')  //will undo when the logic will fix
      .hover(this.searchfield)
      .click(this.searchfield)
      .typeText(this.searchfield, addedstaffemail, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(this.searchedstaff)
      .click(this.searchedstaff)
      .hover(this.viewemail)
      .expect(this.viewemail.innerText).contains(addedstaffemail)
      .wait(1000)
      .hover(this.editbtn)
      .click(this.editbtn)
      .wait(1000)
      .hover(this.edituserform)
      .expect(this.edituserform.innerText).contains('Edit Teammate')
      .typeText(this.fnamefield, fname, { replace: true })
      .wait(500)
      .typeText(this.lnamefield, lname, { replace: true })
      .wait(500)
      .typeText(this.phonenumberfield, phonenumber, { replace: true })
      .hover(this.updatestaffbtn)
      .click(this.updatestaffbtn)
      .click(this.backstaffbtn)
      .wait(500)
      .expect(golvari.currentpageurl()).contains('/company/users')
      .wait(1000)
      .click(searchedUser)
      .wait(1000)
      .hover(this.viewemail)
      .expect(this.viewemail.innerText).contains(addedstaffemail)
      .click(this.backstaffbtn)
      .wait(500);
  }
} export default editstaff
