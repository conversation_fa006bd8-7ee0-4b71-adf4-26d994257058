import navbar from "../Components/navbar"
import golvar from "../Globalvariable"
import { Selector, t } from 'testcafe'

const naviBar = new navbar();
const golVari = new golvar();

class templatepage {

  constructor() {
    this.templateTab = Selector('[data-tc-template-nav-tab]');
    this.childCompanyTab = Selector('[data-tc-tab="child companies"]');
    this.addBtn = Selector('[data-tc-btn="add template"]');
    this.tempModelTitle = Selector('[data-tc-title="new template"]');
    this.nameField = Selector('[data-tc-field="name"]');
    this.descriptionField = Selector('[data-tc-field="description"]');
    this.createBtn = Selector('[data-tc-btn="create"]');
    this.saveTemBtn = Selector('[data-tc-save-form-btn="Save Template"]');
  }

  async templateview() {
    await t.expect(golVari.currentpageurl()).contains('company/overview')
      .wait(1000)
      .click(this.childCompanyTab)
      .expect(golVari.currentpageurl()).contains('related_companies/child_company_management');
  }

  async addNewTemplate(temName, description) {
    const parentBox = `[data-tc-template="${temName}"]`
    const tempBox = Selector(`${parentBox}`);
    const viewName = Selector(`${parentBox} [data-tc-view-name]`);
    const viewDesciption = Selector(`${parentBox} [data-tc-view-description]`);
    await t.wait(1000)
      .expect(golVari.currentpageurl()).contains('company/overview')
      .wait(1000)
      .click(this.templateTab)
      .expect(golVari.currentpageurl()).contains('related_companies/company_templates')
      .wait(500)
      .hover(this.addBtn)
      .click(this.addBtn)
      .wait(500)
      .expect(this.tempModelTitle.innerText).contains('New Template')
      .wait(500)
      .hover(this.nameField)
      .typeText(this.nameField, temName, { paste: true })
      .hover(this.descriptionField)
      .typeText(this.descriptionField, description, { paste: true })
      .wait(1000)
      .hover(this.createBtn)
      .click(this.createBtn)
      .expect(naviBar.notifyPopup.innerText).contains('Company template successfully created')
      .hover(tempBox)
      .expect(tempBox.visible).ok()
      .wait(500)
      .expect(viewName.innerText).contains(temName)
      .expect(viewDesciption.innerText).contains(description);
  }
} export default templatepage
