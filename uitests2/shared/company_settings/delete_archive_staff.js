import { Selector, t } from 'testcafe';
import navbar from '../Components/navbar';
import golvar from '../Globalvariable';
import deleteAsset from '../assets/delete_assets';

const naviBar = new navbar();
const golVari = new golvar();
const delAstPage = new deleteAsset;

class deletestaff {

  constructor() {
    this.staffSideBar = Selector('[data-tc-staff]');
    this.staffBtn = Selector('[data-tc-your-staff]');
    this.searchField = Selector('[data-tc-company-user-search]');
    this.searchedStaff = Selector('[data-tc-user]');
    this.viewEmail = Selector('[data-tc-email]');
    this.archiveBtn = Selector('[data-tc-archive-user-btn]');
    this.archiveConfirm = Selector('[data-tc-archive-user-modal-btn]');
    this.archiveStatus = Selector('[data-tc-company-user-status-id]');
    this.archive = Selector('[data-tc-company-user-status-id] div a');
    this.deleteBtn = Selector('[data-tc-archive-user-btn]');
    this.confirmDeleteBtn = Selector('[data-tc-delete-staff-member]');
    this.viewMessage = Selector('[data-tc-no-users] h4');
  }

  async deletestaff(staffEmail) {
    naviBar.navigatetoPeoplePage()
    await t.expect(golVari.currentpageurl()).contains('/company/users')
      .wait(500)
      .hover(this.searchField)
      .click(this.searchField)
      .typeText(this.searchField, staffEmail, { paste: true })
      .pressKey('enter')
      .wait(2000)
      .hover(this.searchedStaff)
      .expect(this.searchedStaff.exists).ok()
      .click(this.searchedStaff)
      .wait(1000)
      .hover(this.archiveBtn)
      .click(this.archiveBtn)
      .wait(500)
      .click(this.archiveConfirm)
      .wait(1000)
      .click(delAstPage.filterBtn)
      .wait(500)
      .hover(this.archiveStatus)
      .click(this.archiveStatus)
      .wait(500)
      .click(this.archive.withText('Archived'))
      .wait(1000)
      .hover(this.searchField)
      .click(this.searchField)
      .wait(1000)
      .typeText(this.searchField, staffEmail, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(this.searchedStaff)
      .click(this.searchedStaff)
      .wait(1000)
      .hover(this.viewEmail)
      .expect(this.viewEmail.innerText).contains(staffEmail)
      .hover(this.deleteBtn)
      .wait(1000)
      .click(this.deleteBtn)
      .wait(1000)
      .hover(this.confirmDeleteBtn)
      .wait(1000)
      .click(this.confirmDeleteBtn)
      .wait(2000)
      .hover(this.searchField)
      .click(this.searchField)
      .typeText(this.searchField, staffEmail, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .expect(this.viewMessage.innerText).contains('No archived people, currently.')
      .wait(1000)
  }
} export default deletestaff
