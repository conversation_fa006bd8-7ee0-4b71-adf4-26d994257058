import { Selector, t } from 'testcafe';
import copyGroup from './add_copy_group';
import templatepage from './template_tab';
import switchCompany from './switch _company';

const naviTotemplate = new templatepage();
const copiedGroup = new copyGroup();
const companySwitch = new switchCompany();
const templatePage = new templatepage();

class copyCategory {

  constructor() {
    this.addCatIcon = Selector('[data-tc-plus-icon="Categories"]');
    this.popUpOpen = Selector('[data-tc-label="category_name"]');
    this.nameField = Selector('[data-tc-field="category_name"]');
    this.saveBtn = Selector('[data-tc-save-form-btn="Save Category"]');
    this.catChevronDrop = Selector('[data-tc-chevron-down="Categories"]');
  }

  async copyCategoires(categoryName, companyName, parentCompany, templateName) {
    const viewNewCategory = Selector(`[data-tc-view-added-value="${categoryName}"]`);
    const copyCate = Selector(`[data-tc-copy-icon="${categoryName}"]`);
    const destinationCopy = Selector(`[data-tc-check-box="${companyName}"]`);
    const checkCompany = Selector(`[data-tc-view-destination-company="${companyName}"]`);
    const parentBox = `[data-tc-template="${templateName}"]`
    const tempBox = Selector(`${parentBox}`);
    const viewName = Selector(`${parentBox} [data-tc-view-name]`);
    companySwitch.switchToCompany(parentCompany)
    await t.wait(1000)
    naviTotemplate.templateview()
    await t.wait(1000)
      .hover(tempBox)
      .expect(tempBox.visible).ok()
      .wait(1000)
      .expect(viewName.innerText).contains(templateName)
      .wait(500)
      .click(tempBox)
      .wait(1000)
      .hover(this.addCatIcon)
      .click(this.addCatIcon)
      .wait(1000)
      .expect(this.popUpOpen.visible).ok()
      .wait(500)
      .typeText(this.nameField, categoryName, { paste: true })
      .wait(500)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(1000)
      .hover(this.catChevronDrop)
      .click(this.catChevronDrop)
      .wait(500)
      .expect(viewNewCategory.innerText).contains(categoryName)
      .wait(500)
      .hover(copyCate)
      .click(copyCate)
      .wait(1000)
      .hover(destinationCopy)
      .click(destinationCopy)
      .wait(500)
      .click(copiedGroup.nextBtn)
      .wait(1000)
      .hover(checkCompany)
      .expect(checkCompany.innerText).contains(companyName)
      .wait(1000)
      .click(copiedGroup.nextBtn)
      .wait(3000)
      .click(templatePage.saveTemBtn)
      .wait(500);
  }
} export default copyCategory
