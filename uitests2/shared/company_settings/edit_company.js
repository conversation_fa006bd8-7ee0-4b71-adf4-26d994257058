import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";

const navibar = new navbar()
const golvari = new golvar()

class editcompany {

  constructor() {
    this.overviewtab = Selector('[data-tc-overview-tab]')
    this.editcompanybtn = Selector('[data-tc-company-edit]')
    this.companyname = Selector('[data-tc-name]')
    this.subdomain = Selector('[data-tc-subdomain-field]')
    this.phonenumber = Selector('[data-tc-phone-number-input-field]')
    this.savebtn = Selector('[data-tc-save-form-btn="Save company"]')
    this.viewcompanyname = Selector('[data-tc-company-name]')
    this.viewsubdomainname = Selector('[data-tc-subdomain]')
    this.viewphonenumber = Selector('[data-tc-main-phone-number]')
    this.settingicon = Selector('[data-tc-settings-menu-toggle]')
    this.companysetting = Selector('[data-tc-settings-menu="Company Settings"]')
    this.confirmedit = Selector('[data-tc-btn-confirm-subdomain-change]')
  }

  async editcompany(companyname, subdomain, phonenumber) {
    await t.hover(this.overviewtab)
      .click(this.overviewtab)
      .hover(this.editcompanybtn)
      .click(this.editcompanybtn)
      .expect(golvari.currentpageurl()).contains('/company/edit')
      .typeText(this.companyname, companyname, { replace: true })
      .typeText(this.subdomain, subdomain, { replace: true })
      .typeText(this.phonenumber, phonenumber, { replace: true })
      .wait(1000)
      .hover(this.savebtn)
      .click(this.savebtn)
      .wait(1000)
      .hover(this.confirmedit)
      .click(this.confirmedit)
      .wait(3000)
      .expect(golvari.currentpageurl()).contains('company/overview')
      .wait(2000)
      .expect(this.viewcompanyname.innerText).contains(companyname)
      .expect(this.viewphonenumber.innerText).contains(phonenumber);
  }
} export default editcompany
