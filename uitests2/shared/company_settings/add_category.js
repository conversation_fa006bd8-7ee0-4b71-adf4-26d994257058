import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";

const navibar = new navbar();
const golvari = new golvar();

class addCategory {

  constructor() {
    this.catBtn = Selector('[data-tc-categories-tab]');
    this.addCatField = Selector('[data-tc-new-category-name]');
    this.addCatBtn = Selector('[data-tc-btn-add-category]');
    this.notifyPopUp = Selector('.notification-content');
    this.addedCat = Selector('[data-tc-new-category-name] .align-sub');
  }

  async addCategory(newCategoryName) {
    await t.hover(navibar.companySettingTab)
      .click(navibar.companySettingTab)
      .wait(500)
      .hover(this.catBtn)
      .click(this.catBtn)
      .expect(golvari.currentpageurl()).contains('/company/categories')
      .hover(this.addCatField)
      .typeText(this.addCatField, newCategoryName, { paste: true })
      .hover(this.addCatField)
      .click(this.addCatField)
      .hover(this.addCatField)
      .click(this.addCatField)
      .click(this.addCatBtn)
      .expect(this.notifyPopUp.innerText).eql(`Successfully added ${newCategoryName} to your categories`);
  }

  async addCustomCategory(customCategoryName) {
    const categoryElement = Selector(`[data-tc-categories-name="${customCategoryName}"]`);
    const addIcon = Selector(`[data-tc-add-category-icon="${customCategoryName}"]`);
    await t.hover(navibar.companySettingTab)
      .click(navibar.companySettingTab)
      .wait(500)
      .hover(this.catBtn)
      .click(this.catBtn)
      .expect(golvari.currentpageurl()).contains('/company/categories')
      .hover(categoryElement)
      .expect(categoryElement.innerText).eql(` ${customCategoryName}`)
      .hover(addIcon)
      .click(addIcon)
      .expect(this.notifyPopUp.innerText).eql(`Successfully added ${customCategoryName} to your categories`);
  }

} export default addCategory
