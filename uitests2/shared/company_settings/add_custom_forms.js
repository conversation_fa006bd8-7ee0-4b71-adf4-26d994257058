import { Selector, t } from 'testcafe'
import navbar from '../Components/navbar'
import golvar from '../Globalvariable'
import helpDeskVar from '../help_desk/help_desk_variables'

const golVari = new golvar();
const naviBar = new navbar();
const deskVari = new helpDeskVar();

class addCustomForms {

  constructor() {
    this.customFormsTab = Selector('[data-tc-custom-forms-tab]');
    this.selectLocModule = Selector('[data-tc-select-module="Location"]');
    this.selectUserModule = Selector('[data-tc-select-module="Company User"]');
    this.selectTicketsModule = Selector('[data-tc-select-module="Help Tickets"]');
    this.addHelpForm = Selector(`${golVari.horizontalMenu} [data-tc-add-custom-form]`);
    this.addForm = Selector('[data-tc-add-custom-form]');
    this.attachmentDragField = Selector('[data-tc-attachments]');
    this.categoryDragField = Selector('[data-tc-category]');
    this.staffDragField = Selector('[data-tc-people-list]');
    this.assetDragField = Selector('[data-tc-asset-list]');
    this.contractDragField = Selector('[data-tc-contract-list]');
    this.vendorDragField = Selector('[data-tc-vendor-list]');
    this.telecomDragField = Selector('[data-tc-telecom-list]');
    this.avatarDragField = Selector('[data-tc-avatar]');
    this.textDragField = Selector('[data-tc-text]');
    this.staticTextDragField = Selector('[data-tc-static-text]');
    this.textAreaField = Selector('[data-tc-text-area]');
    this.richTextField = Selector('[data-tc-rich-text]');
    this.numberDragField = Selector('[data-tc-number]');
    this.listDragField = Selector('[data-tc-list]');
    this.dateDragField = Selector('[data-tc-date]');
    this.phoneDragField = Selector('[data-tc-phone]');
    this.tagDragField = Selector('[data-tc-tag]');
    this.radioBtnDragField = Selector('[data-tc-radio-button]');
    this.checkBoxDragField = Selector('[data-tc-checkbox]');
    this.locationDragField = Selector('[data-tc-location-list]');
    this.customizableSection = Selector('[data-tc-drop-element-sections]');
    this.selectListData = Selector('[data-tc-edit-field="List"] [data-tc-select-option]');
    this.selectOption = this.selectListData.find('li');
    this.editCustomName = Selector('[data-tc-edit-icon="Custom Form"]');
    this.formNameField = Selector('[data-tc-form-name]');
    this.setCustomName = Selector('[data-tc-save-form-preferences]');
    this.viewSetName = Selector('[data-tc-edit-custom-form]');
    this.createFormBtn = Selector('[data-tc-save-form-btn="Create Form"]');
    this.selectCustomArea = Selector('[data-tc-drop-element-sections] [data-tc-view-label="Checkbox"]');
    this.prioritySection = Selector('[data-tc-view-label="Priority"]');
    this.attachmentSection = Selector('[data-tc-view-label="Attachments"]');
    this.categorySection = Selector('[data-tc-view-label="Category"]');
    this.textSection = Selector('[data-tc-view-label="Text"]');
    this.statictextSection = Selector('[data-tc-view-label="Static Text"]');
    this.textareaSection = Selector('[data-tc-view-label="Text Area"]');
    this.richtextSection = Selector('[data-tc-view-label="Rich Text"]');
    this.numberSection = Selector('[data-tc-view-label="Number"]');
    this.listSection = Selector('[data-tc-view-label="List"]');
    this.dateSection = Selector('[data-tc-view-label="Date"]');
    this.phoneSection = Selector('[data-tc-view-label="Phone"]');
    this.emailaddressSection = Selector('[data-tc-view-label="Email Address"]');
    this.tagSection = Selector('[data-tc-view-label="Tag"]');
    this.radiobuttonSection = Selector('[data-tc-view-label="Radio Button"]');
    this.checkboxSection = Selector('[data-tc-view-label="Checkbox"]');
    this.stafflistSection = Selector('[data-tc-view-label="People List"]');
    this.assetlistSection = Selector('[data-tc-view-label="Asset List"]');
    this.contractlistSection = Selector('[data-tc-view-label="Contract List"]');
    this.vendorlistSection = Selector('[data-tc-view-label="Vendor List"]');
    this.telecomlistSection = Selector('[data-tc-view-label="Telecom List"]');
    this.locationlistSection = Selector('[data-tc-view-label="Location List"]');
    this.cityLabel = Selector('[data-tc-view-label="City"]');
    this.descLabel = Selector('[data-tc-dragged-form="Description"]');
    this.checkBoxSurvey = Selector('[data-tc-check-box="collect closing survey"]');
    this.surveyText = Selector('[data-tc-label="collect closing survey"]');
    this.checkBoxTimeRequire = Selector('[data-tc-check-box="require time spent"]');
    this.timeRequireLabel = Selector('[data-tc-label="require time spent"]');
    this.saveFormBtn = Selector('[data-tc-save-form-btn="Save Form"]');
  }

  async addLocCustomform(customLocName, selectOptions, optionName) {
    const saveAvatarBtn = Selector('[data-tc-save-edit="Avatar"]');
    const saveTextBtn = Selector('[data-tc-save-edit="Text"]');
    const saveStaticTextBtn = Selector('[data-tc-save-edit="Static Text"]');
    const saveTextAreaBtn = Selector('[data-tc-save-edit="Text Area"]');
    const saveRichTextBtn = Selector('[data-tc-save-edit="Rich Text"]');
    const saveNumberBtn = Selector('[data-tc-save-edit="Number"]');
    const saveListBtn = Selector('[data-tc-save-edit="List"]');
    const saveDateBtn = Selector('[data-tc-save-edit="Date"]');
    const savePhoneBtn = Selector('[data-tc-save-edit="Phone"]');
    const saveTagBtn = Selector('[data-tc-save-edit="Tag"]');
    const saveRadioBtn = Selector('[data-tc-save-edit="Radio Button"]');
    const saveCheckBtn = Selector('[data-tc-save-edit="Checkbox"]');
    const addOptionsField = Selector('[data-tc-edit-field="List"] [data-tc-input-option="Add options"]');
    const addOptionsField1 = Selector('[data-tc-edit-field="Radio Button"] [data-tc-input-option="Add options"]');
    const addOptionsField2 = Selector('[data-tc-edit-field="Checkbox"] [data-tc-input-option="Add options"]');
    const addOptionBtn = Selector('[data-tc-add-btn="Add options"]');
    const addOptionBtn1 = Selector('[data-tc-edit-field="Radio Button"] button');
    const addOptionBtn2 = Selector('[data-tc-edit-field="Checkbox"] button');
    const viewaddedForm = Selector(`[data-tc-form-name="${customLocName}"]`);
    await t.hover(naviBar.companySettingTab)
      .click(naviBar.companySettingTab)
      .wait(500)
      .hover(this.customFormsTab)
      .click(this.customFormsTab)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('/company/custom_forms')
      .hover(this.selectLocModule)
      .click(this.selectLocModule)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/company/custom_forms/locations')
      .hover(this.addForm)
      .click(this.addForm)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('/company/custom_forms/locations/new')
      .click(this.editCustomName)
      .wait(500)
      .typeText(this.formNameField, customLocName, { replace: true })
      .wait(500)
      .click(this.setCustomName)
      .wait(2000)
      .expect(this.viewSetName.innerText).contains(customLocName)
      .hover(this.createFormBtn)
      .wait(1000)
      .dragToElement(this.avatarDragField, this.customizableSection)
      .wait(2000)
      .hover(saveAvatarBtn)
      .click(saveAvatarBtn)
      .wait(2000)
      .dragToElement(this.textDragField, this.customizableSection)
      .wait(2000)
      .hover(saveTextBtn)
      .click(saveTextBtn)
      .wait(2000)
      .dragToElement(this.staticTextDragField, this.customizableSection)
      .wait(2000)
      .hover(saveStaticTextBtn)
      .click(saveStaticTextBtn)
      .wait(2000)
      .dragToElement(this.textAreaField, this.customizableSection)
      .wait(2000)
      .hover(saveTextAreaBtn)
      .click(saveTextAreaBtn)
      .wait(2000)
      .dragToElement(this.richTextField, this.customizableSection)
      .wait(2000)
      .hover(saveRichTextBtn)
      .click(saveRichTextBtn)
      .wait(2000)
      .dragToElement(this.numberDragField, this.customizableSection)
      .wait(2000)
      .hover(saveNumberBtn)
      .click(saveNumberBtn)
      .wait(2000)
      .dragToElement(this.listDragField, this.customizableSection)
      .wait(2000)
      .click(this.selectListData)
      .wait(500)
      .click(this.selectOption.withText(selectOptions))
      .wait(2000)
      .hover(addOptionsField)
      .typeText(addOptionsField, optionName, { paste: true })
      .wait(2000)
      .click(addOptionBtn)
      .wait(500)
      .hover(saveListBtn)
      .click(saveListBtn)
      .wait(2000)
      .dragToElement(this.dateDragField, this.customizableSection)
      .wait(2000)
      .hover(saveDateBtn)
      .click(saveDateBtn)
      .wait(2000)
      .dragToElement(this.phoneDragField, this.customizableSection)
      .wait(2000)
      .hover(savePhoneBtn)
      .click(savePhoneBtn)
      .wait(2000)
      .dragToElement(this.tagDragField, this.customizableSection)
      .wait(2000)
      .hover(saveTagBtn)
      .click(saveTagBtn)
      .wait(2000)
      .dragToElement(this.radioBtnDragField, this.customizableSection)
      .wait(2000)
      .hover(addOptionsField1)
      .typeText(addOptionsField1, optionName, { paste: true })
      .wait(2000)
      .click(addOptionBtn1)
      .wait(500)
      .hover(saveRadioBtn)
      .click(saveRadioBtn)
      .wait(2000)
      .dragToElement(this.checkBoxDragField, this.customizableSection)
      .wait(2000)
      .hover(addOptionsField2)
      .typeText(addOptionsField2, optionName, { paste: true })
      .wait(2000)
      .click(addOptionBtn2)
      .wait(500)
      .hover(saveCheckBtn)
      .click(saveCheckBtn)
      .wait(2000)
      .hover(this.createFormBtn)
      .click(this.createFormBtn)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('company/custom_forms/locations')
      .wait(500)
      .expect(viewaddedForm.innerText).contains(customLocName);
  }

  async addUserCustomform(customUserName, selectOptions, optionName) {
    const saveAvatarBtn = Selector('[data-tc-save-edit="Avatar"]');
    const saveTextBtn = Selector('[data-tc-save-edit="Text"]');
    const saveStaticTextBtn = Selector('[data-tc-save-edit="Static Text"]');
    const saveTextAreaBtn = Selector('[data-tc-save-edit="Text Area"]');
    const saveRichTextBtn = Selector('[data-tc-save-edit="Rich Text"]');
    const saveNumberBtn = Selector('[data-tc-save-edit="Number"]');
    const saveListBtn = Selector('[data-tc-save-edit="List"]');
    const saveDateBtn = Selector('[data-tc-save-edit="Date"]');
    const savePhoneBtn = Selector('[data-tc-save-edit="Phone"]');
    const saveTagBtn = Selector('[data-tc-save-edit="Tag"]');
    const saveRadioBtn = Selector('[data-tc-save-edit="Radio Button"]');
    const saveCheckBtn = Selector('[data-tc-save-edit="Checkbox"]');
    const saveLocBtn = Selector('[data-tc-save-edit="Location List"]');
    const addOptionsField = Selector('[data-tc-edit-field="List"] [data-tc-input-option="Add options"]');
    const addOptionsField1 = Selector('[data-tc-edit-field="Radio Button"] [data-tc-input-option="Add options"]');
    const addOptionsField2 = Selector('[data-tc-edit-field="Checkbox"] [data-tc-input-option="Add options"]');
    const addOptionBtn = Selector('[data-tc-add-btn="Add options"]');
    const addOptionBtn1 = Selector('[data-tc-edit-field="Radio Button"] button');
    const addOptionBtn2 = Selector('[data-tc-edit-field="Checkbox"] button');
    const viewaddedForm = Selector(`[data-tc-form-name="${customUserName}"]`);
    await t.hover(naviBar.companySettingTab)
      .click(naviBar.companySettingTab)
      .wait(500)
      .hover(this.customFormsTab)
      .click(this.customFormsTab)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('/company/custom_forms')
      .hover(this.selectUserModule)
      .click(this.selectUserModule)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/company/custom_forms/company_users')
      .hover(this.addForm)
      .click(this.addForm)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('/company/custom_forms/company_users/new')
      .click(this.editCustomName)
      .wait(500)
      .typeText(this.formNameField, customUserName, { replace: true })
      .wait(500)
      .click(this.setCustomName)
      .wait(2000)
      .expect(this.viewSetName.innerText).contains(customUserName)
      .wait(2000)
      .hover(this.createFormBtn)
      .dragToElement(this.avatarDragField, this.customizableSection)
      .expect(this.customizableSection.with({ visibilityCheck: true }).exists).ok()
      .wait(2000)
      .hover(saveAvatarBtn)
      .click(saveAvatarBtn)
      .wait(2000)
      .dragToElement(this.textDragField, this.customizableSection)
      .wait(2000)
      .hover(saveTextBtn)
      .click(saveTextBtn)
      .wait(2000)
      .dragToElement(this.staticTextDragField, this.customizableSection)
      .wait(2000)
      .hover(saveStaticTextBtn)
      .click(saveStaticTextBtn)
      .wait(2000)
      .dragToElement(this.textAreaField, this.customizableSection)
      .wait(2000)
      .hover(saveTextAreaBtn)
      .click(saveTextAreaBtn)
      .wait(2000)
      .dragToElement(this.richTextField, this.customizableSection)
      .wait(2000)
      .hover(saveRichTextBtn)
      .click(saveRichTextBtn)
      .wait(2000)
      .dragToElement(this.numberDragField, this.customizableSection)
      .wait(2000)
      .hover(saveNumberBtn)
      .click(saveNumberBtn)
      .wait(2000)
      .dragToElement(this.listDragField, this.customizableSection)
      .wait(2000)
      .click(this.selectListData)
      .wait(500)
      .click(this.selectOption.withText(selectOptions))
      .wait(2000)
      .hover(addOptionsField)
      .typeText(addOptionsField, optionName, { paste: true })
      .wait(2000)
      .click(addOptionBtn)
      .wait(500)
      .hover(saveListBtn)
      .click(saveListBtn)
      .wait(2000)
      .dragToElement(this.dateDragField, this.customizableSection)
      .wait(2000)
      .hover(saveDateBtn)
      .click(saveDateBtn)
      .wait(2000)
      .dragToElement(this.phoneDragField, this.customizableSection)
      .wait(2000)
      .hover(savePhoneBtn)
      .click(savePhoneBtn)
      .wait(2000)
      .dragToElement(this.tagDragField, this.customizableSection)
      .wait(2000)
      .hover(saveTagBtn)
      .click(saveTagBtn)
      .wait(2000)
      .dragToElement(this.radioBtnDragField, this.customizableSection)
      .wait(2000)
      .hover(addOptionsField1)
      .typeText(addOptionsField1, optionName, { paste: true })
      .wait(2000)
      .click(addOptionBtn1)
      .wait(500)
      .hover(saveRadioBtn)
      .click(saveRadioBtn)
      .wait(2000)
      .dragToElement(this.checkBoxDragField, this.customizableSection)
      .wait(2000)
      .hover(addOptionsField2)
      .typeText(addOptionsField2, optionName, { paste: true })
      .wait(2000)
      .click(addOptionBtn2)
      .wait(500)
      .hover(saveCheckBtn)
      .click(saveCheckBtn)
      .wait(2000)
      .dragToElement(this.locationDragField, this.customizableSection)
      .wait(2000)
      .hover(saveLocBtn)
      .click(saveLocBtn)
      .wait(2000)
      .hover(this.createFormBtn)
      .click(this.createFormBtn)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('/company/custom_forms/company_users')
      .wait(500)
      .expect(viewaddedForm.innerText).contains(customUserName);
  }

  async addTicketCustomform(customTicName, selectOptions, optionName, status1, status2, status3, prority1, prority2, prority3) {
    const saveProrityBtn = Selector('[data-tc-save-edit="Priority"]');
    const saveStatusBtn = Selector('[data-tc-save-edit="Status"]');
    const saveCategoryBtn = Selector('[data-tc-save-edit="Category"]');
    const saveAttachmentBtn = Selector('[data-tc-save-edit="Attachments"]');
    const saveStaffBtn = Selector('[data-tc-save-edit="People List"]');
    const saveAssetBtn = Selector('[data-tc-save-edit="Asset List"]');
    const saveContractBtn = Selector('[data-tc-save-edit="Contract List"]');
    const saveVendorBtn = Selector('[data-tc-save-edit="Vendor List"]');
    const saveTelecomBtn = Selector('[data-tc-save-edit="Telecom List"]');
    const saveTextBtn = Selector('[data-tc-save-edit="Text"]');
    const saveStaticTextBtn = Selector('[data-tc-save-edit="Static Text"]');
    const saveTextAreaBtn = Selector('[data-tc-save-edit="Text Area"]');
    const saveRichTextBtn = Selector('[data-tc-save-edit="Rich Text"]');
    const saveNumberBtn = Selector('[data-tc-save-edit="Number"]');
    const saveListBtn = Selector('[data-tc-save-edit="List"]');
    const saveDateBtn = Selector('[data-tc-save-edit="Date"]');
    const savePhoneBtn = Selector('[data-tc-save-edit="Phone"]');
    const saveTagBtn = Selector('[data-tc-save-edit="Tag"]');
    const saveRadioBtn = Selector('[data-tc-save-edit="Radio Button"]');
    const saveCheckBtn = Selector('[data-tc-save-edit="Checkbox"]');
    const addOptionsField = Selector('[data-tc-edit-field="List"] [data-tc-input-option="Add options"]');
    const addOptionsField1 = Selector('[data-tc-edit-field="Radio Button"] [data-tc-input-option="Add options"]');
    const addOptionsField2 = Selector('[data-tc-edit-field="Checkbox"] [data-tc-input-option="Add options"]');
    const addOptionBtn = Selector('[data-tc-add-btn="Add options"]');
    const addOptionBtn1 = Selector('[data-tc-edit-field="Radio Button"] button');
    const addOptionBtn2 = Selector('[data-tc-edit-field="Checkbox"] button');
    const viewaddedForm = Selector(`[data-tc-form-name="${customTicName}"]`);
    const saveLocBtn = Selector('[data-tc-save-edit="Location List"]');
    const editProrityIcon = Selector('[data-tc-edit-icon="Priority"]');
    const editStatusIcon = Selector('[data-tc-edit-icon="Status"]');
    const inputOptionStatus = Selector('[data-tc-edit-field="Status"] [data-tc-input-field]');
    const addOptionStatBtn = Selector('[data-tc-edit-field="Status"] [data-tc-add-btn]');
    const inputOptionPrority = Selector('[data-tc-edit-field="Priority"] [data-tc-input-option]');
    const addOptionProirBtn = Selector('[data-tc-edit-field="Priority"] [data-tc-btn="add option"]');
    const viewCustPriority1 = Selector(`[data-tc-option="${prority1}"]`);
    const viewCustPriority2 = Selector(`[data-tc-option="${prority2}"]`);
    const viewCustPriority3 = Selector(`[data-tc-option="${prority3}"]`);
    const viewCustStatus1 = Selector(`[data-tc-option="${status1}"]`);
    const viewCustStatus2 = Selector(`[data-tc-option="${status2}"]`);
    const viewCustStatus3 = Selector(`[data-tc-option="${status3}"]`);
    naviBar.navigatetocompanysetting()
    await t.expect(golVari.currentpageurl()).contains('/company/overview')
      .wait(500)
      .hover(this.customFormsTab)
      .click(this.customFormsTab)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('/company/custom_forms')
      .wait(500)
      .hover(this.selectTicketsModule)
      .click(this.selectTicketsModule)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/settings/custom_forms')
      .wait(2000)
      .hover(this.addHelpForm)
      .click(this.addHelpForm)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/settings/custom_forms/new')
      .wait(500)
      .click(this.editCustomName)
      .wait(500)
      .typeText(this.formNameField, customTicName, { replace: true })
      .wait(500)
      .click(this.setCustomName)
      .wait(2000)
      .expect(this.viewSetName.innerText).contains(customTicName)
      .wait(2000)
      .click(editProrityIcon)
      .wait(1000)
      .click(inputOptionPrority)
      .wait(500)
      .typeText(inputOptionPrority, prority1, { paste: true })
      .wait(2000)
      .click(addOptionProirBtn)
      .wait(1000)
      .click(inputOptionPrority)
      .wait(1000)
      .typeText(inputOptionPrority, prority2, { paste: true })
      .wait(200)
      .click(addOptionProirBtn)
      .wait(1000)
      .click(inputOptionPrority)
      .wait(1000)
      .typeText(inputOptionPrority, prority3, { paste: true })
      .wait(2000)
      .click(addOptionProirBtn)
      .wait(1000)
      .expect(viewCustPriority1.visible).ok()
      .expect(viewCustPriority2.visible).ok()
      .expect(viewCustPriority3.visible).ok()
      .click(saveProrityBtn)
      .wait(500)
      .click(editStatusIcon)
      .wait(1000)
      .click(inputOptionStatus)
      .wait(1000)
      .typeText(inputOptionStatus, status1, { paste: true })
      .click(addOptionStatBtn)
      .wait(1000)
      .click(inputOptionStatus)
      .wait(1000)
      .typeText(inputOptionStatus, status2, { paste: true })
      .click(addOptionStatBtn)
      .wait(1000)
      .click(inputOptionStatus)
      .wait(1000)
      .typeText(inputOptionStatus, status3, { paste: true })
      .click(addOptionStatBtn)
      .wait(1000)
      .expect(viewCustStatus1.visible).ok()
      .expect(viewCustStatus2.visible).ok()
      .expect(viewCustStatus3.visible).ok()
      .click(saveStatusBtn)
      .wait(1000)
      .dragToElement(this.attachmentDragField, this.prioritySection)
      .wait(2000)
      .hover(saveAttachmentBtn)
      .click(saveAttachmentBtn)
      .wait(2000)
      .dragToElement(this.categoryDragField, this.attachmentSection)
      .wait(1000)
      .hover(saveCategoryBtn)
      .click(saveCategoryBtn)
      .wait(2000)
      .dragToElement(this.textDragField, this.categorySection)
      .wait(2000)
      .hover(saveTextBtn)
      .click(saveTextBtn)
      .wait(2000)
      .dragToElement(this.staticTextDragField, this.textSection)
      .wait(2000)
      .hover(saveStaticTextBtn)
      .click(saveStaticTextBtn)
      .wait(2000)
      .dragToElement(this.textAreaField, this.statictextSection)
      .wait(2000)
      .hover(saveTextAreaBtn)
      .click(saveTextAreaBtn)
      .wait(2000)
      .dragToElement(this.richTextField, this.textareaSection)
      .wait(2000)
      .hover(saveRichTextBtn)
      .click(saveRichTextBtn)
      .wait(2000)
      .dragToElement(this.numberDragField, this.richtextSection)
      .wait(2000)
      .hover(saveNumberBtn)
      .click(saveNumberBtn)
      .wait(2000)
      .dragToElement(this.listDragField, this.numberSection)
      .wait(2000)
      .click(this.selectListData)
      .wait(500)
      .click(this.selectOption.withText(selectOptions))
      .wait(2000)
      .hover(addOptionsField)
      .typeText(addOptionsField, optionName, { paste: true })
      .wait(2000)
      .click(addOptionBtn)
      .wait(500)
      .hover(saveListBtn)
      .click(saveListBtn)
      .wait(2000)
      .dragToElement(this.dateDragField, this.listSection)
      .wait(2000)
      .hover(saveDateBtn)
      .click(saveDateBtn)
      .wait(2000)
      .dragToElement(this.phoneDragField, this.dateSection)
      .wait(2000)
      .hover(savePhoneBtn)
      .click(savePhoneBtn)
      .wait(2000)
      .dragToElement(this.tagDragField, this.phoneSection)
      .wait(2000)
      .hover(saveTagBtn)
      .click(saveTagBtn)
      .wait(2000)
      .dragToElement(this.radioBtnDragField, this.tagSection)
      .wait(2000)
      .hover(addOptionsField1)
      .typeText(addOptionsField1, optionName, { paste: true })
      .wait(2000)
      .click(addOptionBtn1)
      .wait(500)
      .hover(saveRadioBtn)
      .click(saveRadioBtn)
      .wait(2000)
      .dragToElement(this.checkBoxDragField, this.radiobuttonSection)
      .wait(2000)
      .hover(addOptionsField2)
      .typeText(addOptionsField2, optionName, { paste: true })
      .wait(2000)
      .click(addOptionBtn2)
      .wait(2000)
      .hover(saveCheckBtn)
      .click(saveCheckBtn)
      .wait(2000)
      .dragToElement(this.staffDragField, this.checkboxSection)
      .wait(2000)
      .hover(saveStaffBtn)
      .click(saveStaffBtn)
      .wait(2000)
      .dragToElement(this.assetDragField, this.stafflistSection)
      .wait(2000)
      .hover(saveAssetBtn)
      .click(saveAssetBtn)
      .wait(2000)
      .dragToElement(this.contractDragField, this.assetlistSection)
      .wait(2000)
      .hover(saveContractBtn)
      .click(saveContractBtn)
      .wait(2000)
      .wait(2000)
      .dragToElement(this.vendorDragField, this.contractlistSection)
      .wait(2000)
      .hover(saveVendorBtn)
      .click(saveVendorBtn)
      .wait(2000)
      .wait(2000)
      .dragToElement(this.telecomDragField, this.vendorlistSection)
      .wait(2000)
      .hover(saveTelecomBtn)
      .click(saveTelecomBtn)
      .wait(2000)
      .dragToElement(this.locationDragField, this.telecomlistSection)
      .wait(2000)
      .hover(saveLocBtn)
      .click(saveLocBtn)
      .wait(2000)
      .hover(this.createFormBtn)
      .click(this.createFormBtn)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/settings/custom_forms')
      .wait(500)
      .expect(viewaddedForm.innerText).contains(customTicName)
      .wait(2000)
  }

  async enableTimeRequiredNSurvey(formName) {
    const selectForm = Selector(`[data-tc-form-name="${formName}"]`);
    naviBar.navigatetocompanysetting()
    await t.expect(golVari.currentpageurl()).contains('/company/overview')
      .wait(500)
      .hover(this.customFormsTab)
      .click(this.customFormsTab)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('/company/custom_forms')
      .wait(500)
      .hover(this.selectTicketsModule)
      .click(this.selectTicketsModule)
      .wait(500)
      .hover(selectForm)
      .click(selectForm)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/settings/custom_forms/')
      .expect(this.viewSetName.innerText).contains(formName)
      .wait(500)
      .hover(this.checkBoxSurvey)
      .click(this.checkBoxSurvey)
      .expect(this.surveyText.innerText).eql('Collect a satisfaction survey at the end of the ticket.')
      .wait(500)
      .hover(this.checkBoxTimeRequire)
      .click(this.checkBoxTimeRequire)
      .expect(this.timeRequireLabel.innerText).eql('Require a time spent entry before closing the ticket.')
      .hover(this.saveFormBtn)
      .click(this.saveFormBtn)
      .wait(2000);
  }
} export default addCustomForms;
