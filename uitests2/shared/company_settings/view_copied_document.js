import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import switchCompany from './switch _company';

const golVari = new golvar();
const naviBar = new navbar();
const switchChildComp = new switchCompany();

class viewCopiedDocument {

  constructor() {
    // this.resourceTab = Selector('[data-tc-resources-tab]');
    this.documentTab = Selector('[data-tc-sub-menu="document"]');
    this.searchDocument = Selector('[data-tc-search-document]');
    this.viewDocument = Selector('[data-tc-document-name]');
  }

  async viewCopiedDocuments(selectCompany, docuName) {
    switchChildComp.switchToCompany(selectCompany)
    await t.wait(1000)
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(naviBar.resourceNavMenu)
      .click(naviBar.resourceNavMenu)
      .expect(golVari.currentpageurl()).contains('/help_tickets/articles')
      .wait(500)
      .click(this.documentTab)
      .expect(golVari.currentpageurl()).contains('/help_tickets/documents')
      .wait(500)
      .typeText(this.searchDocument, docuName, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .expect(this.viewDocument.innerText).contains(docuName)
      .wait(2000)
  }
} export default viewCopiedDocument
