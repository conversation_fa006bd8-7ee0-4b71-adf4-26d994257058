import { Selector, t } from 'testcafe';
import templatepage from './template_tab';
import copyGroup from './add_copy_group';
import switchCompany from './switch _company';
import addCannedResponse from '../help_desk/add_canned_response';

const naviTotemplate = new templatepage();
const copiedGroup = new copyGroup();
const companySwitch = new switchCompany();
const addResponsePage = new addCannedResponse();

class copyResponse {

  constructor() {
    this.addRespIcon = Selector('[data-tc-plus-icon="Responses"]');
    this.respChevronDrop = Selector('[data-tc-chevron-down="Responses"]');
  }

  async copyResponses(title, text, companyName, parentCompany, templateName) {
    const viewResponse = Selector(`[data-tc-view-added-value="${title}"]`);
    const copyRespIcon = Selector(`[data-tc-copy-icon="${title}"]`);
    const destinationCompany = Selector(`[data-tc-check-box="${companyName}"]`);
    const viewCompany = Selector(`[data-tc-view-destination-company="${companyName}"]`);
    const parentBox = `[data-tc-template="${templateName}"]`
    const tempBox = Selector(`${parentBox}`);
    const viewName = Selector(`${parentBox} [data-tc-view-name]`);
    companySwitch.switchToCompany(parentCompany)
    await t.wait(1000)
    naviTotemplate.templateview()
    await t.hover(tempBox)
      .expect(tempBox.visible).ok()
      .wait(1000)
      .expect(viewName.innerText).contains(templateName)
      .wait(500)
      .click(tempBox)
      .wait(1000)
      .hover(this.addRespIcon)
      .click(this.addRespIcon)
      .wait(1000)
      .typeText(addResponsePage.titleField, title, { paste: true })
      .wait(500)
      .typeText(addResponsePage.textToReplaceField, text, { paste: true })
      .wait(500)
      .click(addResponsePage.submitBtn)
      .wait(1000)
      .hover(this.respChevronDrop)
      .click(this.respChevronDrop)
      .wait(1000)
      .expect(viewResponse.innerText).contains(title)
      .wait(1000)
      .hover(copyRespIcon)
      .click(copyRespIcon)
      .wait(1000)
      .hover(destinationCompany)
      .click(destinationCompany)
      .wait(500)
      .click(copiedGroup.nextBtn)
      .wait(1000)
      .hover(viewCompany)
      .expect(viewCompany.innerText).contains(companyName)
      .wait(1000)
      .click(copiedGroup.nextBtn)
      .wait(3000)
      .click(naviTotemplate.saveTemBtn)
      .wait(1000);
  }
} export default copyResponse
