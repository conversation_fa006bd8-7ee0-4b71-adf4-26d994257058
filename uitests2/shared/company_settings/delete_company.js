import { Selector, t } from 'testcafe'
import golvar from '../Globalvariable'
import navbar from '../Components/navbar'
import signinpage from '../signin/signinpage';

const golVari = new golvar();
const naviBar = new navbar();
const logInToApp = new signinpage();

class deleteCompany {

  constructor() {
    this.inputName = Selector('[id="q_name_or_subdomain_contains"]');
    this.logoutBtn = Selector('[id="logout"]');
    this.viewComName = Selector('.odd a')
    this.delBtn = "#index_table_companies tbody tr:nth-child(1) a.delete_link";
    this.nameContain = Selector('[id="search_status_sidebar_section"] li b');
    this.inputName1 = Selector('[name="company name"]');
    this.okBtn = Selector('.ui-dialog-buttonset button');
    this.viewDeleteInitiated = Selector('.flashes div');
  }

  async companyDeletion(companyDelete) {
    logInToApp.sigin(golVari.adminUser, golVari.adminPass)
    await t.wait(1000)
      .expect(golVari.currentpageurl()).contains('/admin')
      .wait(1000)
      .typeText(this.inputName, companyDelete, { paste: true })
      .wait(500)
      .pressKey('enter')
      .wait(1000)
      .hover(this.viewComName)
      .wait(500)
      .expect(this.viewComName.innerText).contains(companyDelete)
      .expect(this.nameContain.innerText).contains(companyDelete)
      .wait(500)
      .click(this.delBtn)
      .wait(1000)
      .typeText(this.inputName1, companyDelete, { paste: true })
      .wait(500)
      .click(this.okBtn)
      .wait(500)
      .hover(this.viewDeleteInitiated)
      .expect(this.viewDeleteInitiated.innerText).contains('Company deletion initiated.')
      .wait(500)
      .hover(this.logoutBtn)
      .click(this.logoutBtn)
      .wait(500)
  }
} export default deleteCompany
