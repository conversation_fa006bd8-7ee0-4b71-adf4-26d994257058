import { nanoid } from 'nanoid'
import faker from 'faker'

const cities = [
  "New York", "Los Angeles", "Chicago", "Houston", "Phoenix",
  "Philadelphia", "San Antonio", "San Diego", "Dallas", "San Jose",
];

class companyvar {

  constructor() {
    this.firstname = 'Tester' + faker.name.firstName();
    this.lastname = faker.name.lastName() + 'qa';
    this.editfname = faker.name.firstName();
    this.editlname = faker.name.lastName();
    this.email = faker.internet.email('genuitytest', `${faker.lorem.word()}`, 'work4humanity.com', { allowSpecialCharacters: true });
    this.email1 = faker.internet.email('genuitytest', `${faker.lorem.word()}`, 'work4humanity.com', { allowSpecialCharacters: true });
    this.email2 = faker.internet.email('genuitytest', `${faker.lorem.word()}`, 'work4humanity.com', { allowSpecialCharacters: true });
    this.pnumber = '8553627737';
    this.workphone = '8225757455';
    this.extension = nanoid(3);
    this.title = nanoid(3);
    this.department = nanoid(4);
    this.locationname = 'Test' + nanoid(2);
    this.editlocationname = 'Test' + nanoid(2);
    this.address = '3809 Leo Street';
    this.city = getRandomCity();
    this.zipcode = '80123';
    this.notes = 'This is for testing purposes';
    this.category = 'Test' + nanoid(1);
    this.companyname = 'Testchild' + faker.internet.domainWord().toLowerCase();
    this.subdomain = 'qasubdomain' + faker.internet.domainWord().toLowerCase();
    this.companyname1 = 'Qachild' + faker.internet.domainWord().toLowerCase();
    this.subdomain1 = 'Qa' + faker.internet.domainWord().toLowerCase();
    this.editsubdomain = 'Testing' + faker.internet.domainWord(5).toLowerCase();
    this.editcompanyphone = '(*************';
    this.readAll = 'Read All';
    this.writeMine = 'Write Mine';
    this.readAllWriteMine = 'Read All / Write Mine';
    this.writeAll = 'Write All';
    this.locFormName = 'Location' + faker.name.lastName();
    this.userFormName = 'User' + faker.name.lastName();
    this.ticketFormName = 'Ticket Form' + faker.name.lastName();
    this.ticketFormName1 = 'Help Ticket Form' + faker.name.lastName();
    this.option = 'Country';
    this.option1 = 'State/Province';
    this.optionName = nanoid(3);
    this.locationName1 = 'Doomfists' + nanoid(2);
    this.address1 = '636 Greenwich St, New York, NY 10014';
    this.city1 = getRandomCity();
    this.zipCode1 = '80032';
    this.phoneNumber1 = '2025550129';
    this.textArea = faker.lorem.word() + nanoid(2);
    this.richText = nanoid(2) + faker.lorem.word();
    this.tag1 = 'Business Service';
    this.date1 = '2023-02-01';
    this.list1 = 'Anguilla';
    this.number1 = '3';
    this.text1 = nanoid(3);
    this.firstName1 = 'Qa' + faker.name.firstName();
    this.lastName1 = faker.name.lastName() + 'test';
    this.email1 = (`${faker.lorem.word()}@work4humanity.com`).toLowerCase();
    this.workPhone1 = '8225757455';
    this.tag2 = 'SaaS';
    this.date2 = '2023-01-01';
    this.list2 = 'Angola';

    function getRandomCity() {
      const randomIndex = Math.floor(Math.random() * cities.length);
      return cities[randomIndex];
    }

  }
} export default companyvar
