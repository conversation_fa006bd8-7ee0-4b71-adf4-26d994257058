import { Selector, t } from 'testcafe';

class setUserPermission {

  constructor() {
    this.assetDropDown = Selector('[data-tc-select-permission="Asset Management"]');
    this.vendorDropDown = Selector('[data-tc-select-permission="SaaS and Vendor Management"]');
    this.contractDropDown = Selector('[data-tc-select-permission="Contract Management"]');
    this.networkDropDown = Selector('[data-tc-select-permission="Network Monitoring"]');
    this.telecomDropDown = Selector('[data-tc-select-permission="Telecom Management"]');
    this.companyDropDown = Selector('[data-tc-select-permission="Company Settings"]');
    this.helpDeskDropDown = Selector('[data-tc-helpdesk-dropdown]');
    this.checkAssetPer = this.assetDropDown.find('span');
    this.checkVendorPer = this.vendorDropDown.find('span');
    this.checkContractPer = this.contractDropDown.find('span');
    this.checkCompanyPer = this.companyDropDown.find('span');
    this.checkTelecomPer = this.telecomDropDown.find('span');
    this.checkNetworkPerm = this.networkDropDown.find('span');
    this.checkHelpDeskPer = this.helpDeskDropDown.find('span');
  }

  async setUserPermissions(assetPermission, vendorPermission, contractPermission, telecomPermission, networkPermission, companyPermission, helpDeskPermission) {
    const selectPermission = Selector('[data-tc-select-permission-helpdesk-groups] a');
    const contractPer = Selector(`[data-tc-select-permission="Contract Management"] [data-tc-select-permission-helpdesk-groups] [data-tc-help-desk-permission-items="${contractPermission}"]`);
    const telecomPer = Selector(`[data-tc-select-permission="Telecom Management"] [data-tc-select-permission-helpdesk-groups] [data-tc-help-desk-permission-items="${telecomPermission}"]`);
    const networkPer = Selector(`[data-tc-select-permission="Network Monitoring"] [data-tc-select-permission-helpdesk-groups] [data-tc-help-desk-permission-items="${networkPermission}"]`);
    const companyPer = Selector(`[data-tc-select-permission="Company Settings"] [data-tc-select-permission-helpdesk-groups] [data-tc-help-desk-permission-items="${companyPermission}"]`);
    const helpDeskPer = Selector(`[data-tc-helpdesk-dropdown] [data-tc-select-permission-helpdesk-groups] [data-tc-help-desk-permission-items="${helpDeskPermission}"]`);
    await t.wait(1000)
      .hover(this.assetDropDown)
      .click(this.assetDropDown)
      .wait(500)
      .hover(selectPermission.withText(assetPermission))
      .click(selectPermission.withText(assetPermission))
      .wait(1000)
      .hover(this.vendorDropDown)
      .click(this.vendorDropDown)
      .wait(500)
      .hover(selectPermission.withText(vendorPermission))
      .click(selectPermission.withText(vendorPermission))
      .wait(1000)
      .hover(this.contractDropDown)
      .click(this.contractDropDown)
      .wait(500)
      .hover(contractPer)
      .click(contractPer)
      .wait(1000)
      .hover(this.telecomDropDown)
      .click(this.telecomDropDown)
      .wait(500)
      .hover(telecomPer)
      .click(telecomPer)
      .wait(1000)
      .hover(this.networkDropDown)
      .click(this.networkDropDown)
      .wait(500)
      .hover(networkPer)
      .click(networkPer)
      .wait(1000)
      .hover(this.companyDropDown)
      .click(this.companyDropDown)
      .wait(500)
      .hover(companyPer)
      .click(companyPer)
      .wait(1000)
      .hover(this.checkAssetPer)
      .expect(this.checkAssetPer.innerText).contains(assetPermission)
      .wait(1000)
      .expect(this.checkVendorPer.innerText).contains(vendorPermission)
      .wait(1000)
      .expect(this.checkContractPer.innerText).contains(contractPermission)
      .wait(1000)
      .expect(this.checkTelecomPer.innerText).contains(telecomPermission)
      .wait(1000)
      .hover(this.checkNetworkPerm)
      .expect(this.checkNetworkPerm.innerText).contains(networkPermission)
      .wait(1000)
      .hover(this.checkCompanyPer)
      .expect(this.checkCompanyPer.innerText).contains(companyPermission)
      .wait(1000)
  }
} export default setUserPermission
