import navbar from "../Components/navbar";
import { Selector, t } from "testcafe";
import golvar from "../Globalvariable";
import addAsset from "../assets/add_assets";

const naviBar = new navbar();
const golVari = new golvar();
const addAstPage = new addAsset();

class viewCount {

  constructor() {
    this.staffCounter = Selector('[data-tc-total-staffs]');
  }

  async checkCounter(value, companyValue, module) {
    const checkModule = Selector(`[data-tc-module="${module}"]`);
    const checkTitleViewManagement = Selector(`[data-tc-module="${module}"] [data-tc-title="${module}"]`);
    const checkCounterViewManagement = Selector(`[data-tc-module="${module}"] [data-tc-total="${value}"]`);
    await t.wait(2000)
      .hover(naviBar.homeDashboard)
      .click(naviBar.homeDashboard)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/dashboard')
      .wait(1000)
      .hover(checkCounterViewManagement)
      .expect(checkCounterViewManagement.innerText).contains(value)
      .hover(checkCounterViewManagement)
      .expect(checkTitleViewManagement.innerText).contains(module)
      .wait(500)
      .click(addAstPage.companyViewToggle)
      .wait(500)
      .expect(this.staffCounter.innerText).contains(companyValue)
      .expect(checkModule.innerText).contains(moduleNameComView)
      .wait(1000)
      .click(addAstPage.managementViewToggle);
  }
} export default viewCount
