import { Selector, t } from 'testcafe'
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";

const navibar = new navbar()
const golvari = new golvar()

class deletelocation {

  constructor() {
    this.deleteicon = Selector('[data-tc-delete-location-icon]')
    this.confirmbtn = Selector('[data-tc-confirm-location-remove-btn]')
    this.searchfield = Selector('[data-tc-locations-search-field]')
    this.locnamesearched = Selector('[data-tc-locations-item-name]')
    this.deletedloc = Selector('[data-tc-location-result]')
    this.locationsideicon = Selector('[data-tc-locations-tab]')
  }

  async deletelocation(locationname) {
    navibar.navigatetocompanysetting()
    await t.hover(this.locationsideicon)
      .click(this.locationsideicon)
      .expect(golvari.currentpageurl()).contains('company/locations')
      //search the location which want to delete
      .wait(1000)
      .hover(this.searchfield)
      .typeText(this.searchfield, locationname, { paste: true })
      .pressKey('enter')
      .wait(2000)
      .expect(this.locnamesearched.innerText).contains(locationname)
      .wait(2000)
      .hover(this.deleteicon)
      .click(this.deleteicon)
      .wait(2000)
      .hover(this.confirmbtn)
      .click(this.confirmbtn)
      .wait(1000)
      //search the location which want to delete
      .wait(1000)
      .hover(this.searchfield)
      .click(this.searchfield)
      .wait(1000)
      .typeText(this.searchfield, locationname, { replace: true })
      .pressKey('enter')
      .wait(2000)
      .hover(this.deletedloc)
      .expect(this.deletedloc.innerText).contains('No results found, try clearing the search box, or adding a new location above.');
  }
} export default deletelocation
