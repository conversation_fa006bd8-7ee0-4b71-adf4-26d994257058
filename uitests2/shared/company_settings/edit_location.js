import { Selector, t } from 'testcafe'
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";

const navibar = new navbar()
const golvari = new golvar()

class editlocation {

  constructor() {
    this.locationsideicon = Selector('[data-tc-locations-tab]')
    this.searchfield = Selector('[data-tc-locations-search-field]')
    this.locnamesearched = Selector('[data-tc-locations-item-name]')
    this.editicon = Selector('[data-tc-edit-location-icon]')
    this.headerform = Selector('[data-tc-header-form]')
    this.editlocbtn = Selector('[data-tc-location-edit]')
    this.name = Selector('[data-tc-text-field="name"]')
    this.addressfield = Selector('[data-tc-text-field="address"]')
    this.cityfield = Selector('[data-tc-text-field="city"]')
    this.zipfield = Selector('[data-tc-text-field="zip"]')
    this.addressline = Selector('[data-tc-text-field="address_2"]')
    this.statefield = Selector('[data-tc-text-field="state"]')
    this.countryfield = Selector('[data-tc-text-field="country_name"]')
    this.phonenumberfield = Selector('[data-tc-text-field="phone_number"]')
    this.updatelocbtn = Selector('[data-tc-save-form-btn="Update location"]')
    this.backlocbtn = Selector('[data-tc-back-locations-btn]')
    this.verilocname = Selector('[data-tc-created-location-name]')
    this.searchedloc = Selector('[data-tc-loc-item]')
    this.veriaddress = Selector('[data-tc-created-location-address]')
  }

  async editlocation(locname, editlocname, editaddress, editcity, editzip, editaddressline, editphonenumber) {
    await t.hover(navibar.companySettingTab)
      .click(navibar.companySettingTab)
      .wait(500)
      .hover(this.locationsideicon)
      .click(this.locationsideicon)
      .expect(golvari.currentpageurl()).contains('company/locations')
    //search the location which want to edit
    await t.hover(this.searchfield)
      .typeText(this.searchfield, locname, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .expect(this.locnamesearched.innerText).contains(locname)
      .wait(1000)
      //edit the location info
      .hover(this.editicon)
      .click(this.editicon)
      .wait(1000)
      .expect(this.headerform.innerText).contains('Edit Location')
      .wait(1000)
      .click(this.name)
      .typeText(this.name, editlocname, { replace: true })
      .wait(1000)
      .click(this.addressfield)
      .typeText(this.addressfield, editaddress, { replace: true })
      .wait(1000)
      .click(this.cityfield)
      .typeText(this.cityfield, editcity, { replace: true })
      .wait(1000)
      .click(this.zipfield)
      .typeText(this.zipfield, editzip, { replace: true })
      .wait(1000)
      .click(this.addressline)
      .typeText(this.addressline, editaddressline, { replace: true })
      .wait(1000)
      .click(this.statefield)
      .typeText(this.statefield, 'CO', { replace: true })
      .pressKey('enter')
      .wait(1000)
      .click(this.countryfield)
      .typeText(this.countryfield, 'Australia', { replace: true })
      .pressKey('enter')
      .wait(1000)
      .click(this.phonenumberfield)
      .typeText(this.phonenumberfield, editphonenumber, { replace: true })
      .wait(500)
      .hover(this.updatelocbtn)
      .click(this.updatelocbtn)
      .wait(1000)
      .click(this.backlocbtn)
      //search and view the edit info of location
      .hover(this.searchfield)
      .typeText(this.searchfield, editlocname, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .expect(this.locnamesearched.innerText).contains(editlocname)
      .wait(1000)
      .hover(this.searchedloc)
      .click(this.searchedloc)
      .wait(1000)
      .hover(this.verilocname)
      .expect(this.verilocname.innerText).contains(editlocname)
      .wait(500)
      .hover(this.veriaddress)
      .expect(this.veriaddress.innerText).contains(editaddress + ',' + ' ' + editcity + ',' + ' ' + editzip)
      .wait(500);
  }
} export default editlocation
