import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import switchCompany from './switch _company';

const golVari = new golvar();
const naviBar = new navbar();
const switchChildComp = new switchCompany();

class viewCopiedResponse {

  constructor() {
    this.resourceTab = Selector('[data-tc-resources-tab]');
    this.cannedTab = Selector('[data-tc-sub-menu="canned response"]');
    this.searchResponse = Selector('[data-tc-search-field="canned response"]');
    this.viewResponse = Selector('[data-test-response-title]');
  }

  async viewCopiedResponses(selectCompany, title) {
    switchChildComp.switchToCompany(selectCompany)
    await t.wait(1000)
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(naviBar.resourceNavMenu)
      .click(naviBar.resourceNavMenu)
      .expect(golVari.currentpageurl()).contains('/help_tickets/articles')
      .wait(500)
      .click(this.cannedTab)
      .expect(golVari.currentpageurl()).contains('/help_tickets/responses')
      .wait(500)
      .typeText(this.searchResponse, title, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .expect(this.viewResponse.innerText).contains(title)
      .wait(1000)
  }
} export default viewCopiedResponse
