import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addCategory from "./add_category";
import deletedWorkSpace from "../help_desk/delete_work_space";
import removeCategory from "./remove_category";

const naviBar = new navbar();
const golVari = new golvar();
const addCatPage = new addCategory();
const removeCatPage = new removeCategory();
const delWorkPage = new deletedWorkSpace();

class viewCategory {

  constructor() {

  }

  async viewHDCategories(categoryName) {
    const viewName = Selector(`[data-tc-view-name="${categoryName}"]`);
    await t.hover(delWorkPage.settingIcon)
      .click(delWorkPage.settingIcon)
      .wait(500)
      .hover(addCatPage.catBtn)
      .click(addCatPage.catBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/categories')
      .wait(500)
      .hover(removeCatPage.currentCatHead)
      .expect(removeCatPage.currentCatHead.innerText).contains('Current Help Desk Categories')
      .wait(500)
      .hover(viewName)
      .expect(viewName.innerText).eql(` ${categoryName}`)
      .wait(500);
  }
} export default viewCategory
