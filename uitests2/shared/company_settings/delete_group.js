import { Selector, t } from 'testcafe'
import golvar from '../Globalvariable'
import navbar from '../Components/navbar'

const golvari = new golvar()
const navbari = new navbar()

class deletegroup {

  constructor() {
    this.deleteicon = Selector('[data-tc-delete-icon-btn]');
    this.deletealert = Selector('[data-tc-view="help ticket grid"] [data-tc-title-before-delete-modal]');
    this.confirmdelbtn = Selector('[data-tc-view="help ticket grid"] [data-tc-ok-delete-group-btn]');
    this.deletedmessage = Selector('[data-tc-no-group-found-message]');
    this.grouptab = Selector('[data-tc-groups-tab]');
    this.searchbygroupnamefield = Selector('[data-tc-company-user-search]');
    this.groupsearchedname = Selector('[data-tc-company-group-card-item]');
    this.notificationalert = Selector('.notification-content');
  }

  async deletegroup(groupname) {
    await t.hover(this.grouptab)
      .click(this.grouptab)
      .expect(golvari.currentpageurl()).contains('/company/groups')
      .wait(1000)
      .hover(this.searchbygroupnamefield)
      .typeText(this.searchbygroupnamefield, groupname, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .hover(this.groupsearchedname)
      .expect(this.groupsearchedname.innerText).contains(groupname)
      .wait(1000)
      .hover(this.deleteicon)
      .click(this.deleteicon)
      .wait(500)
      .expect(this.deletealert.innerText).contains('Are you absolutely sure you want to delete this group?')
      .hover(this.confirmdelbtn)
      .wait(1000)
      .click(this.confirmdelbtn)
      .wait(1000)
      .click(this.grouptab)
      .expect(this.notificationalert.innerText).contains('Group was successfully removed')
      .click(this.searchbygroupnamefield)
      .pressKey('enter')
      .wait(500)
      .expect(this.deletedmessage.innerText).contains('No groups found, try changing filtering criteria.');
  }
} export default deletegroup
