import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";

const golvari = new golvar()
const navibar = new navbar()

class editgroup {

  constructor() {
    this.addstaffmember = Selector('[data-tc-add-impacted-user]')
    this.searchgroupnamefield = Selector('[data-tc-company-user-search]')
    this.groupsearchedname = Selector('[data-tc-group-name]')
    this.grouptab = Selector('[data-tc-groups-tab]')
    this.groupinput = Selector('[data-tc-input-group-name]')
    this.memberoption = Selector('[data-tc-multi-user-field]')
    this.searchmember = Selector('[id="company-users"]')
    this.savegroupbtn = Selector('[data-tc-save-form-btn="Save group"]')
    this.helpdeskdropdown = Selector('[data-tc-helpdesk-dropdown]')
    this.selectionhelppermission = Selector('[data-tc-helpdesk-dropdown] [data-tc-help-desk-permission-items="Read All / Write Mine"]')
  }

  async editgroup(editgroupname, addedgroupname) {
    await t.hover(this.grouptab)
      .click(this.grouptab)
      .expect(golvari.currentpageurl()).contains('company/groups')
      //search the group which want to edit
      .wait(1000)
      .hover(this.searchgroupnamefield)
      .click(this.searchgroupnamefield)
      .typeText(this.searchgroupnamefield, addedgroupname, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .hover(this.groupsearchedname)
      .expect(this.groupsearchedname.innerText).contains(addedgroupname)
      .click(this.groupsearchedname)
      //edit the group name
      .wait(1000)
      .click(this.groupinput)
      .selectText(this.groupinput)
      .pressKey('delete')
      .typeText(this.groupinput, editgroupname, { paste: true })
      .click(this.helpdeskdropdown)
      .click(this.selectionhelppermission)
      .expect(this.helpdeskdropdown.innerText).contains('Read All / Write Mine')
      .hover(this.addstaffmember)
      .click(this.addstaffmember)// add new staff member
      .click(this.memberoption)
      .hover(this.searchmember)
      .click(this.searchmember)
      .wait(1000)  //here issue occur
      .typeText(this.searchmember, 'Admins', { paste: true })
      .pressKey('enter')
      .click(this.savegroupbtn)
      .expect(golvari.currentpageurl()).contains('company/groups')
      //search the group which want to edit
      .wait(1000)
      .hover(this.searchgroupnamefield)
      .typeText(this.searchgroupnamefield, editgroupname, { paste: true })
      .pressKey('enter')
      .hover(this.groupsearchedname)
      .wait(2000)
      .expect(this.groupsearchedname.innerText).contains(editgroupname)
      .click(this.groupsearchedname)
    this.editedname = editgroupname  //this will pass to other search or delete operation
  }
} export default editgroup
