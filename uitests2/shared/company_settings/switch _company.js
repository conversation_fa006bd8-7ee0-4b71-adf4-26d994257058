import { Selector, t } from 'testcafe';
import addcompany from './add_childcompany';

const addChildCompany = new addcompany();

class switchCompany {

  constructor() {
    this.companyDropDown = Selector('[data-dropdown-menu="company_select_menu"]');
    this.dropDownTitle = Selector('[data-tc-title-drop-down="switch company"]');
    this.searchField = Selector('[data-tc-field="search company"]');
  }

  async switchToCompany(companyName) {
    const viewCompanyName = Selector(`[data-company-name="${companyName}"]`);
    const viewText =  Selector(`[data-company-name="${companyName}"] span`).nth(1);
    await t.wait(1000)
      .hover(this.companyDropDown)
      .click(this.companyDropDown)
      .wait(1000)
      .hover(this.dropDownTitle)
      .expect(this.dropDownTitle.innerText).contains('Switch Company')
      .wait(1000)
      // .typeText(this.searchField, companyName, { replace: true })
      // .wait(2000)
      .hover(viewCompanyName)
      .expect(viewText.innerText).contains(companyName)
      .click(viewCompanyName)
      .wait(5000)
      .hover(addChildCompany.childcompanyname)
      // .expect(addChildCompany.childcompanyname.innerText).contains(companyName)
  }
} export default switchCompany
