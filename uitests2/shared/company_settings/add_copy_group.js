import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import templatepage from './template_tab';

const golVari = new golvar();
const templatePage = new templatepage();

class copyGroup {

  constructor() {
    this.addIconBtn = Selector('[data-tc-plus-icon="Groups"]');
    this.checkGroupPopup = Selector('[data-tc-label="group name"]');
    this.nameField = Selector('[data-tc-field="group name"]');
    this.chevronDrop = Selector('[data-tc-chevron-down="Groups"]');
    this.nextBtn = Selector('[data-tc-next-btn="copy destination"]');
    this.templateTab = Selector('[data-tc-template-nav-tab]');
    this.saveBtn = Selector('[data-tc-save-form-btn="Save Group"]');
  }

  async copyGroups(groupName, companyName, templateName) {
    const viewAddedGroup = Selector(`[data-tc-view-added-value="${groupName}"]`);
    const copyIcon = Selector(`[data-tc-copy-icon="${groupName}"]`);
    const destinationCompany = Selector(`[data-tc-check-box="${companyName}"]`);
    const viewCompany = Selector(`[data-tc-view-destination-company="${companyName}"]`);
    const parentBox = `[data-tc-template="${templateName}"]`
    const tempBox = Selector(`${parentBox}`);
    const viewName = Selector(`${parentBox} [data-tc-view-name]`);
    await t.wait(1000)
      .click(this.templateTab)
      .wait(1000)
      .hover(tempBox)
      .expect(tempBox.visible).ok()
      .wait(1000)
      .expect(viewName.innerText).contains(templateName)
      .wait(500)
      .click(tempBox)
      .wait(1000)
      .hover(this.chevronDrop)
      .click(this.chevronDrop)
      .wait(1000)
      .expect(viewAddedGroup.innerText).contains(groupName)
      .wait(1000)
      .hover(copyIcon)
      .click(copyIcon)
      .wait(1000)
      .hover(destinationCompany)
      .click(destinationCompany)
      .wait(500)
      .click(this.nextBtn)
      .wait(1000)
      .hover(viewCompany)
      .expect(viewCompany.innerText).contains(companyName)
      .wait(1000)
      .click(this.nextBtn)
      .wait(3000)
      .click(templatePage.saveTemBtn)
      .wait(1000);
  }
} export default copyGroup
