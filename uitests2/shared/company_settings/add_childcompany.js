import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';

const golvari = new golvar();

class addcompany {

  constructor() {
    this.settingicon = Selector('[data-tc-settings-menu-toggle]');
    this.addcompanybtn = Selector('[data-tc-settings-menu="Add Company"]');
    this.selectcompany = Selector('[data-tc-select-company="parent"]');
    this.companyname = Selector('[data-tc-name]');
    this.subdomain = Selector('[data-tc-subdomain]');
    this.savebtn = Selector('[data-tc-save-form-btn="Create & Continue to Access"]');
    this.confirmbtn = Selector('[data-tc-save-form-btn="Save With Selected Parent Company"]');
    this.childcompanyname = Selector('[data-tc-company-drawer-name]');
    this.emailinput = Selector('.visible-lg form input[name="username"]');
    this.passinput = Selector('.visible-lg form input[name="password"]');
    this.submitbtn = Selector('.visible-lg form input[name="signInSubmitButton"]');
    this.confirmedit = Selector('[data-tc-btn-confirm-subdomain-change]');
    this.createComTitle1 = Selector('[data-tc-title1="create company"]');
    this.createComTitle2 = Selector('[data-tc-title2="create company"]');
    this.prePopulateTitle1 = Selector('[data-tc-title1="pre-populate company"]');
    this.prePopulateTitle2 = Selector('[data-tc-title2="pre-populate company"]');
    this.skipBuildElement = Selector('[data-tc-btn="skip build element"]');
    this.skipAccessGroup = Selector('[data-tc-btn="skip access group"]');
    this.createComTab = Selector('[data-tc-toggle] [data-tc-tab="new company"]');
  }

  async addchildcompany(companyname, subdomain) {
    await t.wait(1000)
      .click(this.settingicon)
      .wait(500)
      .click(this.addcompanybtn)
      .wait(1000)
      .expect(golvari.currentpageurl()).contains('company/new')
      .wait(500)
      .expect(this.createComTitle1.innerText).contains(`When one company just isn't enough`)
      .expect(this.createComTitle2.innerText).contains('Create or Connect a Child Company')
      .wait(500)
      .hover(this.createComTab)
      .expect(this.createComTab.innerText).contains('Create New Company')
      .click(this.createComTab)
      .wait(500)
      .typeText(this.companyname, companyname, { paste: true })
      .wait(1000)
      .typeText(this.subdomain, subdomain, { replace: true })
      .hover(this.savebtn)
      .click(this.savebtn)
      .wait(3000)
      .hover(this.confirmbtn)
      .click(this.confirmbtn)
      .wait(5000)
      .expect(golvari.currentpageurl()).contains(`/company/new?subdomain=${subdomain}&module=access_groups`)
      .wait(500)
      .hover(this.skipAccessGroup)
      .click(this.skipAccessGroup)
      .wait(500)
      .expect(golvari.currentpageurl()).contains(`/company/new?subdomain=${subdomain}&module=company_builds`)
      .wait(500)
      .hover(this.skipBuildElement)
      .click(this.skipBuildElement)
      .wait(8000)
      .hover(this.childcompanyname)
      .expect(this.childcompanyname.innerText).contains(companyname);
  }
}
export default addcompany
