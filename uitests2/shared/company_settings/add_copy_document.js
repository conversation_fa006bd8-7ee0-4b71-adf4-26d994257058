import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import templatepage from './template_tab';
import addgroup from './add_group';
import copyGroup from './add_copy_group';
import switchCompany from './switch _company';

const golVari = new golvar();
const naviTotemplate = new templatepage();
const groupPage = new addgroup();
const copiedGroup = new copyGroup();
const companySwitch = new switchCompany();

class copyDocument {

  constructor() {
    this.addDocIcon = Selector('[data-tc-plus-icon="Documents"]');
    this.checkGroupPopup = Selector('[data-tc-label="group name"]');
    this.uploaddDoc = Selector('[data-tc-attachment]');
    this.dropFile = Selector('[data-tc-uploading-input-attachments]');
    this.documentName = Selector('[data-tc-document-name]');
    this.saveDoc = Selector('[data-tc-save-form-btn="Save"]');
    this.docChevronDrop = Selector('[data-tc-chevron-down="Documents"]');
    this.nextBtn = Selector('[data-tc-next-btn="copy destination"]');
  }

  async copyDocuments(docName, companyName, parentCompany, templateName) {
    const viewDocument = Selector(`[data-tc-view-added-value="${docName}"]`);
    const copyDocIcon = Selector(`[data-tc-copy-icon="${docName}"]`);
    const destinationCompany = Selector(`[data-tc-check-box="${companyName}"]`);
    const viewCompany = Selector(`[data-tc-view-destination-company="${companyName}"]`);
    const parentBox = `[data-tc-template="${templateName}"]`
    const tempBox = Selector(`${parentBox}`);
    const viewName = Selector(`${parentBox} [data-tc-view-name]`);
    companySwitch.switchToCompany(parentCompany)
    await t.wait(1000)
    naviTotemplate.templateview()
    await t.wait(1000)
      .hover(tempBox)
      .expect(tempBox.visible).ok()
      .wait(1000)
      .expect(viewName.innerText).contains(templateName)
      .wait(500)
      .click(tempBox)
      .wait(1000)
      .hover(this.addDocIcon)
      .click(this.addDocIcon)
      .wait(1000)
      .hover(this.uploaddDoc)
      .click(this.uploaddDoc)
      .setFilesToUpload(this.dropFile, '../../shared/assets/inputImage.png')
      .wait(1000)
      .typeText(this.documentName, docName, { paste: true })
      .wait(500)
      .click(this.saveDoc)
      .wait(1000)
      .hover(this.docChevronDrop)
      .click(this.docChevronDrop)
      .wait(500)
      .expect(viewDocument.innerText).contains(docName)
      .wait(1000)
      .hover(copyDocIcon)
      .click(copyDocIcon)
      .wait(1000)
      .hover(destinationCompany)
      .click(destinationCompany)
      .wait(500)
      .click(copiedGroup.nextBtn)
      .wait(1000)
      .hover(viewCompany)
      .expect(viewCompany.innerText).contains(companyName)
      .wait(1000)
      .click(copiedGroup.nextBtn)
      .wait(3000)
      .click(naviTotemplate.saveTemBtn)
      .wait(1000);
  }
} export default copyDocument
