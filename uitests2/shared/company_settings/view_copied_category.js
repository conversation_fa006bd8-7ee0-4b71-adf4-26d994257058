import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import addgroup from './add_group';
import switchCompany from './switch _company';
import addCategory from './add_category';

const golVari = new golvar();
const naviBar = new navbar();
const categoryPage = new addCategory();
const switchChildComp = new switchCompany();

class viewCopiedCategory {

  async viewCopiedCategories(companySwitch, categoryName) {
    const viewCategory = Selector(`[data-tc-view-name="${categoryName}"]`);
    switchChildComp.switchToCompany(companySwitch)
    await t.wait(1000)
    naviBar.navigatetocompanysetting()
    await t.wait(1000)
      .hover(categoryPage.catBtn)
      .click(categoryPage.catBtn)
      .expect(golVari.currentpageurl()).contains('/company/categories')
      .wait(1000)
      .hover(viewCategory)
      .expect(viewCategory.innerText).contains(categoryName)
      .wait(2000)
  }
} export default viewCopiedCategory
