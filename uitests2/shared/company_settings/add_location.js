import { Selector, t } from "testcafe";
import addAssetType from "../assets/add_asset_type";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";

const navibar = new navbar();
const golvari = new golvar();
const assetTypePage = new addAssetType();

class addlocation {

  constructor() {
    this.locationsideicon = Selector('[data-tc-locations-tab]');
    this.addlocationbtn = Selector('[data-tc-add="location"]');
    this.name = Selector('[data-tc-text-field="name"]');
    this.addressfield = Selector('[data-tc-text-field="address"]');
    this.cityfield = Selector('[data-tc-text-field="city"]');
    this.zipfield = Selector('[data-tc-text-field="zip"]');
    this.addressline = Selector('[data-tc-text-field="address_2"]');
    this.statefield = Selector('[data-tc-text-field="state"]');
    this.countryfield = Selector('[data-tc-text-field="country_name"]');
    this.phonenumberfield = Selector('[data-tc-text-field="phone_number"]');
    this.notesfield = Selector('[data-tc-text-field="notes"]');
    this.createlocbtn = Selector('[data-tc-save-form-btn="Create location"]');
    this.backlocbtn = Selector('[data-tc-back-locations-btn]');
    this.searchfield = Selector('[data-tc-locations-search-field]');
    this.locnamesearched = Selector('[data-tc-locations-item-name]');
    this.verilocname = Selector('[data-tc-created-location-name]');
    this.searchedloc = Selector('[data-tc-loc-item]');
    this.veriaddress = Selector('[data-tc-created-location-address]');
    this.toggleBtn = Selector('[data-tc-toggle-btn="location"]');
    this.viewSelectedLoc = Selector('[data-tc-form-drop-down] span');
    this.tagField = Selector('[data-tc-text-field="tag"]');
    this.selectTag = this.tagField.find('span');
    this.inputPhone = Selector('[data-tc-text-field="phone"] input');
    this.inputDate = Selector('[data-tc-text-field="date"] input');
    this.inputTextArea = Selector('[data-tc-text-field="text_area"]');
    this.inputRichText = Selector('[data-tc-trix-editor]');
    this.addressNav = Selector('[data-tc-require-field="address"]');
    this.nameNav = Selector('[data-tc-require-field="name"]');
    this.cityNav = Selector('[data-tc-require-field="city"]');
    this.zipNav = Selector('[data-tc-require-field="zip"]');
    this.textAreaLabel = Selector('[data-tc-check-label="Text Area"]');
    this.richAreaLabel = Selector('[data-tc-check-label="Rich Text"]');
    this.radioBtnLabel = Selector('[data-tc-check-label="Radio Button"]');
    this.checkedRadio = Selector('[data-tc-text-field="radio_button"] input');
    this.checkBoxLabel = Selector('[data-tc-check-label="Checkbox"]');
    this.checkedBox = Selector('[data-tc-text-field="checkbox"] input');
    this.tagLabel = Selector('[data-tc-check-label="Tag"]');
    this.listLabel = Selector('[data-tc-check-label="List"]');
    this.listDropDown = Selector('[data-tc-text-field="list"]');
    this.selectList = this.listDropDown.find('span span');
    this.numberLabel = Selector('[data-tc-check-label="Number"]');
    this.inputNumber = Selector('[data-tc-text-field="number"]');
    this.textLabel = Selector('[data-tc-check-label="Text"]');
    this.inputText = Selector('[data-tc-text-field="text"]');
    this.avatarLabel = Selector('[data-tc-text-field="avatar"]');
    this.phoneLabel = Selector('[data-tc-check-label="Phone"]');
  }

  async addlocation(locname, address, city, zip, addressline, phonenumber, notes) {
    await t.hover(navibar.companySettingTab)
      .click(navibar.companySettingTab)
      .wait(500)
      .hover(this.locationsideicon)
      .click(this.locationsideicon)
      .expect(golvari.currentpageurl()).contains('company/locations')
      .hover(this.addlocationbtn)
      .click(this.addlocationbtn)
      .expect(golvari.currentpageurl()).contains('company/locations/new')
      .wait(1000)
      .hover(this.name)
      .typeText(this.name, locname, { speed: 0.2 })
      .wait(500)
      .typeText(this.addressfield, address, { paste: true })
      .typeText(this.cityfield, city, { speed: 0.2 })
      .typeText(this.zipfield, zip, { paste: true })
      .typeText(this.addressline, addressline, { paste: true })
      .wait(500)
      .hover(this.statefield)
      .click(this.statefield)
      .typeText(this.statefield, 'AA', { paste: true })
      .pressKey('enter')
      .wait(500)
      .click(this.countryfield)
      .typeText(this.countryfield, 'Canada', { paste: true })
      .pressKey('enter')
      .wait(500)
      .typeText(this.phonenumberfield, phonenumber, { paste: true })
      .scroll(this.phonenumberfield, 'center')
      .typeText(this.notesfield, notes, { paste: true })
      .wait(500)
      .hover(this.createlocbtn)
      .click(this.createlocbtn)
      .wait(1000)
      .click(this.backlocbtn)
      .expect(golvari.currentpageurl()).contains('company/locations')
      .wait(1000)
      .typeText(this.searchfield, locname, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .expect(this.locnamesearched.innerText).contains(locname)
    await t.hover(this.searchedloc)
      .click(this.searchedloc)
      .hover(this.verilocname)
      .expect(this.verilocname.innerText).contains(locname)
      .hover(this.veriaddress)
      .expect(this.veriaddress.innerText).contains(address + ',' + ' ' + city + ',' + ' ' + zip)
      .wait(1000)
  }

  async addLocThroCustomForm(selectForm, nameLoc, addressLoc, cityLoc, textArea, richText, phoneLoc, tag, date, list, number, text, zipLoc) {
    const formOption = Selector(`[data-tc-${selectForm}]`);
    await t.hover(navibar.companySettingTab)
      .click(navibar.companySettingTab)
      .wait(500)
      .hover(this.locationsideicon)
      .click(this.locationsideicon)
      .wait(1000)
      .expect(golvari.currentpageurl()).contains('company/locations')
      .wait(500)
      .hover(this.toggleBtn)
      .click(this.toggleBtn)
      .wait(1000)
      .hover(formOption)
      .click(formOption)
      .wait(2000)
      .expect(golvari.currentpageurl()).contains('/company/locations/new?formId=')
      .wait(500)
      .expect(this.viewSelectedLoc.innerText).contains(selectForm)
      .wait(500)
      .typeText(this.name, nameLoc, { paste: true })
      .wait(1000)
      .hover(this.addressfield)
      .wait(500)
      .typeText(this.addressfield, addressLoc, { paste: true })
      .wait(1000)
      .hover(this.cityNav)
      .click(this.cityNav)
      .wait(1000)
      .hover(this.cityfield)
      .wait(500)
      .typeText(this.cityfield, cityLoc, { paste: true })
      .wait(1000)
      .hover(this.textAreaLabel)
      .wait(500)
      .typeText(this.inputTextArea, textArea, { paste: true })
      .wait(1000)
      .hover(this.richAreaLabel)
      .wait(1000)
      .typeText(this.inputRichText, richText, { paste: true })
      .wait(1000)
      .scrollIntoView(this.richAreaLabel)
      .wait(500)
      .hover(this.radioBtnLabel)
      .wait(1000)
      .click(this.checkedRadio)
      .wait(1000)
      .scrollIntoView(this.checkBoxLabel)
      .wait(500)
      .hover(this.checkBoxLabel)
      .wait(1000)
      .click(this.checkedBox)
      .wait(1000)
      .hover(this.phoneLabel)
      .wait(500)
      .typeText(this.inputPhone, phoneLoc, { paste: true })
      .wait(1000)
      .scrollIntoView(this.tagLabel)
      .wait(500)
      .click(this.tagField)
      .wait(500)
      .click(this.selectTag.withText(tag))
      .wait(1000)
      .typeText(this.inputDate, date, { paste: true })
      .wait(500)
      .pressKey('enter')
      .wait(1000)
      .hover(this.listLabel)
      .wait(500)
      .click(this.listDropDown)
      .wait(500)
      .click(this.selectList.withText(list))
      .wait(500)
      .pressKey('enter')
      .wait(1000)
      .hover(this.numberLabel)
      .wait(500)
      .typeText(this.inputNumber, number, { paste: true })
      .wait(1000)
      .hover(this.textLabel)
      .wait(1000)
      .typeText(this.inputText, text, { paste: true })
      .wait(1000)
      .hover(this.avatarLabel)
      .wait(500)
      .hover(assetTypePage.uploadImage)
      .wait(500)
      .click(assetTypePage.uploadImage)
      .wait(500)
      .setFilesToUpload(assetTypePage.dropFile, '../../shared/company_settings/locationavatar.jpg')
      .wait(1000)
      .hover(this.zipNav)
      .click(this.zipNav)
      .wait(1000)
      .hover(this.zipfield)
      .wait(500)
      .typeText(this.zipfield, zipLoc, { paste: true })
      .wait(1000)
      .hover(this.createlocbtn)
      .click(this.createlocbtn)
      .wait(2000)
      .click(this.backlocbtn)
      .wait(500)
      .expect(golvari.currentpageurl()).contains('company/locations')
      .wait(1000)
      .typeText(this.searchfield, nameLoc, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .expect(this.locnamesearched.innerText).contains(nameLoc)
      .wait(1000)
  }
} export default addlocation
