import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import addgroup from './add_group';
import switchCompany from './switch _company';

const golVari = new golvar();
const naviBar = new navbar();
const groupPage = new addgroup();
const switchChildComp = new switchCompany();

class viewCopiedGroup {

  async viewCopiedGroups(companySwitch, groupName) {
    const viewGroup = Selector(`[data-tc-group="${groupName}"]`);
    switchChildComp.switchToCompany(companySwitch)
    await t.wait(1000)
    await t.hover(groupPage.groupTab)
      .click(groupPage.groupTab)
      .expect(golVari.currentpageurl()).contains('company/groups')
      .wait(1000)
      .expect(viewGroup.innerText).contains(groupName)
      .wait(1000)
      .click(viewGroup)
      .wait(1000)
  }
} export default viewCopiedGroup
