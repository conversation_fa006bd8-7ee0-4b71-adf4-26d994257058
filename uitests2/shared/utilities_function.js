import { Selector, t } from 'testcafe';
class utilityFunction {

  async multiselectDropDown(fieldLabel, memberItem) {
    const labelDropDown = Selector(`[data-tc="${fieldLabel}"]`);
    const fieldDropDown = labelDropDown.find('ul');
    const selectItem = fieldDropDown.find('li');
    await t.hover(labelDropDown)
      .click(fieldDropDown)
      .wait(500)
      .click(selectItem.withText(memberItem))
      .wait(500)
  }
} export default utilityFunction
