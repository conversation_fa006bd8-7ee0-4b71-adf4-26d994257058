import faker from 'faker';
import { ClientFunction } from 'testcafe';
import { Selector, t } from 'testcafe';
import { getEnv, getConfig } from '../../config.ts';
import { nanoid } from 'nanoid';
import helpDeskVar from './help_desk/help_desk_variables.js';

const deskVari = new helpDeskVar();
const config = getConfig();
const randomString = randomStrings(3).toLowerCase();

//  Smoke test data //
const email = `genuitytest+smokytest${randomString}@work4humanity.com`;
const email1 = `genuitytest+testteam${randomString}@work4humanity.com`;
const companyName = `Testcomp${faker.internet.domainWord()}`;
const subdomain1 = `${faker.internet.domainWord()}testdomain`;
const fName = `Ab${faker.name.firstName()}`;
const lName = `Test${faker.name.lastName()}`;
const fName1 = `Rol${deskVari.randomAlphabet}`;
const lName1 = `Elie${deskVari.randomAlphabet}`;
const groupA = `Group A${nanoid(2)}`;
const groupB = `Group B${nanoid(2)}`;
const location1 = `Loca 1${deskVari.randomAlphabet}`;
const location2 = `Loca 2${deskVari.randomAlphabet}`;
const asset = `asset${faker.commerce.product()}`;
const contract1 = `contract${faker.commerce.product()}`;
const vendor1 = `vendor${faker.commerce.product()}`;
const provider1 = `provider${faker.commerce.product()}`;
const service = `Service${nanoid(1)}`;

// Regression test data //
const asset1 = `assets${faker.commerce.product()}`;
const contract2 = `Tie Contract${nanoid(1)}`;
const vendor2 = `Vendors${faker.commerce.product()}`;
const provider2 = `providers${faker.commerce.product()}`;
const provider3 = `itprovider${faker.commerce.product()}`;
const service1 = `Services${nanoid(1)}`;
const service2 = `Teleservice${nanoid(1)}`;
const fName2 = `Tor${deskVari.randomAlphabet}`;
const lName2 = `Uzur${deskVari.randomAlphabet}`;
const email2 = `genuitytest+teammate${deskVari.randomNumberVar}@work4humanity.com`;
const fName3 = `Host${deskVari.randomAlphabet}`;
const lName3 = `Tester${deskVari.randomAlphabet}`;
const email3 = `genuitytest+people${deskVari.randomNumberVar}@work4humanity.com`;
const location3 = `denver${deskVari.randomAlphabet}`;
const location4 = `sliver${deskVari.randomAlphabet}`;
const groupC = `Group C${nanoid(2)}`;

class golvar {

  constructor() {
    //signin variables
    this.email_multiplecom = '<EMAIL>';
    this.password = '12345678A';
    this.baseurl = config.siteUrl;
    this.envurl = config.ev;
    this.signupurl = this.baseurl + 'users/sign_up/about_you';
    this.firstName = fName;
    this.lastName = lName;
    this.fullName = fName + ' ' + lName;
    this.companyemail = email;
    this.emailaddress = email;
    this.newpassword = faker.internet.password();
    this.companyname = companyName;
    this.subdomain1 = subdomain1;
    this.membername = Selector('[data-tc-view="user"]').textContent;
    this.adminmember = 'Everyone';
    this.guestEmail = faker.internet.email('genuitytest', `${faker.lorem.word()}`, 'work4humanity.com', { allowSpecialCharacters: true });
    this.adminUser = '<EMAIL>';
    this.adminPass = config.adminPassword;
    this.randomImei = generateRandomImei();
    this.groupName1 = groupA;
    this.groupName2 = groupB;
    this.userEmail = email1;
    this.firstName1 = fName1;
    this.lastName1 = lName1;
    this.fullName1 = fName1 + ' ' + lName1;
    this.locName1 = location1;
    this.locName2 = location2;
    this.assetName1 = asset;
    this.contract = contract1;
    this.vendor = vendor1;
    this.provider = provider1;
    this.telecomService = service;
    //Regression Testing data
    this.groupName3 = groupC;
    this.firstName2 = fName2;
    this.lastName2 = lName2;
    this.fullName2 = fName2 + ' ' + lName2;
    this.email2 = email2;
    this.firstName3 = fName3;
    this.lastName3 = lName3;
    this.fullName3 = fName3 + ' ' + lName3;
    this.email3 = email3;
    this.locName3 = location3;
    this.locName4 = location4;
    this.assetName2 = asset1;
    this.contract1 = contract2;
    this.vendor1 = vendor2;
    this.provider1 = provider2;
    this.provider2 = provider3;
    this.telecomService1 = service1;
    this.telecomService2 = service2;
    this.horizontalMenu = '[data-tc-tab="horizontal"]';
    this.verticalMenu = '[data-tc-tab="vertical"]';
    this.url = `http://${this.subdomain1}.${this.envurl}`

    this.currentpageurl = ClientFunction(() => {
      return window.location.href.toString();
    })

    this.getUrl = ClientFunction(() => {
      return window.location.origin.toString();
    })

    function generateRandomImei() {
      let randomNumber = "";
      for (let i = 0; i < 15; i++) {
        randomNumber += Math.floor(Math.random() * 10);
      }
      return randomNumber;
    }
  }

} export default golvar

function randomStrings(length) {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    result += characters.charAt(randomIndex);
  }
  return result;
}
