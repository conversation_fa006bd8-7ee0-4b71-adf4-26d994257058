import { Selector, t } from "testcafe";
import golvar from "../Globalvariable";
import deleteTicket from "./delete_tickets";
import addTicket from "./add_tickets";
import moveTicket from "./move_ticket";
import editTickets from "./edit_tickets";
import indexPageAction from "./ticket_index_page_action";

const golVari = new golvar();
const delTic = new deleteTicket();
const addTicPage = new addTicket();
const moveTicPage = new moveTicket();
const editTicPage = new editTickets();
const ticIndexPage = new indexPageAction();

class cloneTicketing {

  constructor() {
    this.cloneActionBtn = Selector('[data-tc-action-btn="Clone ticket"]');
    this.cloneTitle = Selector('[data-tc-title="clone ticket"] h2');
    this.modalHeading = Selector('[data-tc-heading="clone ticket"]');
    this.cloneItBtn = Selector('[data-tc-btn="clone it"]');
    this.cloneHistory = Selector('[data-tc-history="ticket clone"]');
    this.getTicketIndex = Selector('[[data-tc-ticket-number]]');
  }

  async cloneTicket(subject, status, priority, createdBy, parentIndex) {
    const ticket = Selector(`[data-tc-ticket="${subject}"]`);
    const viewStatus = Selector(`[data-tc-view-status="${status}"]`);
    const viewPriority = Selector(`[data-tc-view-priority="${priority}"]`);
    const viewCreatedBy = Selector(`[data-tc-field="created_by"] [data-tc-view-selected="${createdBy}"]`);
    await t.hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .expect(ticket.exists).ok('Check the ticket is present or not.')
      .hover(ticket)
      .click(ticket)
      .wait(1000)
      .hover(addTicPage.viewSubject)
      .expect(addTicPage.viewSubject.innerText).contains(subject)
      .wait(500)
      .hover(moveTicPage.moreActionIcon)
      .click(moveTicPage.moreActionIcon)
      .wait(500)
      .hover(this.cloneActionBtn)
      .click(this.cloneActionBtn)
      .wait(1000)
      .hover(this.cloneTitle)
      .expect(this.cloneTitle.innerText).eql('Clone Ticket')
      .hover(this.modalHeading)
      .expect(this.modalHeading.innerText).eql('Are you sure you want to clone this ticket?')
      .hover(this.cloneItBtn)
      .click(this.cloneItBtn)
      .wait(3000)
      .hover(addTicPage.viewSubject)
      .expect(addTicPage.viewSubject.innerText).contains(subject)
      .wait(500)
      .hover(viewStatus)
      .expect(viewStatus.innerText).eql(` ${status}`)
      .hover(viewPriority)
      .expect(viewPriority.innerText).eql(` ${priority}`)
      .hover(viewCreatedBy)
      .expect(viewCreatedBy.innerText).contains(createdBy)
      .wait(500)
      .hover(editTicPage.historyTab)
      .click(editTicPage.historyTab)
      .wait(500)
      .hover(this.cloneHistory)
      .expect(this.cloneHistory.innerText).eql(`Ticket was cloned from Ticket #${parentIndex}`)
      .wait(300)
      .hover(ticIndexPage.redirectBackTic)
      .click(ticIndexPage.redirectBackTic)
      .wait(1000);
  }
} export default cloneTicketing
