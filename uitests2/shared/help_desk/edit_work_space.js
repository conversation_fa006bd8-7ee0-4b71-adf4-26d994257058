import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addWorkSpace from "./add_work_space";
import deletedWorkSpace from "./delete_work_space";
import checkSettings from "./help_desk_setting";
import deleteTicket from "./delete_tickets";

const naviBar = new navbar();
const golVari = new golvar();
const workSpace = new addWorkSpace();
const delSpace = new deletedWorkSpace();
const delTicPage = new deleteTicket();
const helpSettingPage = new checkSettings();

class editWorkSpace {

  constructor() {
    this.sliderToggle = '[data-tc-slide-toggle]';
    this.editNameField = Selector('[data-tc-update-name]');
    this.copyFormBtn = Selector('[data-tc-btn="copy custom form"]');
    this.viewFormModal = Selector('[data-tc-title="Copy New Custom Form"] h2');
    this.addFormBtn = Selector('[data-tc-add-custom-form]');
    this.copyFormDropDown = Selector('[data-tc-multi-custom-form]');
    this.inputCopyForm = this.copyFormDropDown.find('input');
    this.copyBtn = Selector('[data-tc-save-form-btn="Copy"]');
    this.removeDropDown = Selector('[data-tc-remove-icon="member dropdown"]');
    this.editModalTitle = Selector('[data-tc-title="workspaceTitle"] h2');
    this.incomingEmailToggle = Selector(`[data-tc="Allow all incoming emailed requests"] ${this.sliderToggle}`);
    this.unregisterUserToggle = Selector(`[data-tc="Allow unregistered users to receive help ticket updates via email"] ${this.sliderToggle}`);
    this.lastEmailToggle = Selector(`[data-tc="Only include last email response as ticket comment"] ${this.sliderToggle}`);
    this.customizeTicketToggle = Selector(`[data-tc="Customize ticket number"] ${this.sliderToggle}`);
    this.updateBtn = Selector('[data-tc-save-form-btn="Update Workspace"]');
  }

  async editWorkSpaces(name, editName, description, editGroupUser, editPermission1, editPermission2, formName) {
    const viewName = Selector(`[data-tc-value="${name}"]`);
    const dotIcon = Selector(`[data-tc-workspace="${name}"] [data-tc-icon="ellipsis workspace"]`);
    const editIcon = Selector(`[data-tc-workspace="${name}"] [data-tc-icon="edit workspace"]`);
    const staffNewUser = Selector(`[data-tc-selected-object="${editGroupUser}"]`);
    const staffUser1 = Selector(`[data-tc-staff-users="${editGroupUser}"] div`);
    const setPerm2 = staffUser1.find('.multiselect__content li span span');
    const viewRemovedInList = Selector(`[data-tc-selected-object="${editGroupUser}"]`);
    const viewEditName = Selector(`[data-tc-value="${editName}"]`);
    const viewDescription = Selector(`[data-tc-description="${editName}"]`);
    const viewWorkSpace = Selector(`[data-workspace-name="${editName}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .hover(delSpace.workSpaceTab)
      .click(delSpace.workSpaceTab)
      .expect(golVari.currentpageurl()).contains('/help_tickets/workspaces')
      .hover(viewName)
      .expect(viewName.innerText).eql(name)
      .hover(dotIcon)
      .click(dotIcon)
      .wait(300)
      .hover(editIcon)
      .click(editIcon)
      .wait(500)
      .hover(this.editModalTitle)
      .expect(this.editModalTitle.innerText).eql('Edit Workspace')
      .hover(workSpace.nameField)
      .typeText(workSpace.nameField, editName, { replace: true })
      .wait(500)
      .hover(workSpace.descField)
      .typeText(workSpace.descField, description, { replace: true })
      .wait(500)
      .hover(workSpace.colorDropDown)
      .click(workSpace.colorDropDown)
      .wait(400)
      .hover(workSpace.selectColor2)
      .click(workSpace.selectColor2)
      .hover(workSpace.iconDropDown)
      .click(workSpace.iconDropDown)
      .wait(500)
      .click(workSpace.selectIcon2)
      .wait(500)
      .hover(workSpace.addFormBtn)
      .click(workSpace.addFormBtn)
      .wait(500)
      .hover(workSpace.formDropDown)
      .click(workSpace.formDropDown)
      .wait(500)
      .click(workSpace.selectForm.withText(formName))
      .wait(100)
      .hover(workSpace.peopleSubMenu)
      .click(workSpace.peopleSubMenu)
      .wait(300)
      .hover(workSpace.members)
      .click(workSpace.members)
      .click(workSpace.setPerm1.withText(editPermission1))//need to change here
      .wait(1000)
      .click(workSpace.addNewUser)
      .wait(500)
      .hover(workSpace.userDropDown)
      .click(workSpace.userDropDown)
      .wait(1000)
      .hover(staffNewUser)
      .click(staffNewUser)
      .wait(1000)
      .click(staffUser1)
      .wait(1000)
      .click(setPerm2.withText(editPermission2))
      .wait(2000)
      .click(workSpace.addNewUser)
      .wait(500)
      .hover(workSpace.userDropDown)
      .click(workSpace.userDropDown)
      .wait(1000)
      .expect(viewRemovedInList.exist).notOk()
      .wait(500)
      .click(this.removeDropDown)
      .wait(1000)
      .hover(workSpace.settingSubMenu)
      .click(workSpace.settingSubMenu)
      .hover(workSpace.quickSettingLabel)
      .expect(workSpace.quickSettingLabel.innerText).eql('Quick Settings')
      .click(this.incomingEmailToggle)
      .click(this.unregisterUserToggle)
      .click(this.lastEmailToggle)
      .click(this.customizeTicketToggle)
      .wait(500)
      .typeText(helpSettingPage.inputPrefix, 'Testing', { replace: true })
      .wait(500)
      .click(helpSettingPage.inputticNumber)
      // .pressKey('backspace')
      // .pressKey('backspace')
      // .wait(500)
      .typeText(helpSettingPage.inputticNumber, '12', { replace: true })
      .wait(500)
      .hover(this.updateBtn)
      .click(this.updateBtn)
      .wait(3000)
      .hover(delTicPage.ticketTab)
      .click(delTicPage.ticketTab)
      .wait(500)
      .hover(delSpace.workSpaceTab)
      .click(delSpace.workSpaceTab)
      .wait(500)
      .hover(viewEditName)
      .expect(viewEditName.innerText).eql(editName)
      .expect(viewDescription.innerText).eql(description)
      .wait(500)
      .hover(workSpace.spaceDropDown)
      .click(workSpace.spaceDropDown)
      .wait(500)
      .hover(viewWorkSpace)
      .expect(viewWorkSpace.innerText).contains(editName);
  }

  async copyForm(name1, formName) {
    const viewCopiedForm = Selector(`[data-tc-custom-form="${formName}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(delSpace.settingIcon)
      .click(delSpace.settingIcon)
      .wait(1000)
      .hover(delSpace.workSpaceTab)
      .click(delSpace.workSpaceTab)
      .expect(golVari.currentpageurl()).contains('/help_tickets/workspaces')
      .hover(workSpace.searchSpace)
      .typeText(workSpace.searchSpace, name1, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(workSpace.viewWorkSpaceName)
      .expect(workSpace.viewWorkSpaceName.innerText).contains(name1)
      .wait(500)
      .click(workSpace.viewWorkSpaceName)
      .wait(1000)
      .hover(this.copyFormBtn)
      .wait(500)
      .click(this.copyFormBtn)
      .wait(1000)
      .expect(this.viewFormModal.innerText).contains('Copy New Custom Form')
      .wait(500)
      .hover(this.addFormBtn)
      .wait(500)
      .click(this.addFormBtn)
      .wait(1000)
      .click(this.copyFormDropDown)
      .wait(1000)
      .typeText(this.inputCopyForm, formName, { paste: true })
      .wait(500)
      .pressKey('enter')
      .wait(1000)
      .hover(this.copyBtn)
      .wait(500)
      .click(this.copyBtn)
      .wait(1000)
      .hover(viewCopiedForm)
      .wait(500)
      .expect(viewCopiedForm.innerText).contains(formName)
      .wait(1000)
      .hover(workSpace.saveBtn)
      .click(workSpace.saveBtn)
      .wait(2000)
  }

  async removeMember(name2, removeItem) {
    const viewMember = Selector(`[data-tc-member="${removeItem}"]`);
    const removeIcon = Selector(`[data-tc-remove="${removeItem}"]`);
    const viewRemovedInList = Selector(`[data-tc-selected-object="${removeItem}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(delSpace.settingIcon)
      .click(delSpace.settingIcon)
      .wait(1000)
      .hover(delSpace.workSpaceTab)
      .click(delSpace.workSpaceTab)
      .expect(golVari.currentpageurl()).contains('/help_tickets/workspaces')
      .hover(workSpace.searchSpace)
      .typeText(workSpace.searchSpace, name2, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(workSpace.viewWorkSpaceName)
      .expect(workSpace.viewWorkSpaceName.innerText).contains(name2)
      .wait(500)
      .click(workSpace.viewWorkSpaceName)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/workspaces')
      .hover(viewMember)
      .wait(500)
      .click(removeIcon)
      .wait(1000)
      .expect(viewMember.exists).notOk()
      .wait(500)
      .click(workSpace.addNewUser)
      .wait(500)
      .hover(workSpace.userDropDown)
      .click(workSpace.userDropDown)
      .wait(1000)
      .expect(viewRemovedInList.innerText).contains(removeItem)
      .wait(500)
      .click(this.removeDropDown)
      .wait(1000)
      .hover(workSpace.saveBtn)
      .click(workSpace.saveBtn)
      .wait(2000)
      .hover(workSpace.searchSpace)
      .typeText(workSpace.searchSpace, name2, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(workSpace.viewWorkSpaceName)
      .expect(workSpace.viewWorkSpaceName.innerText).contains(name2)
      .wait(500)
      .click(workSpace.viewWorkSpaceName)
      .wait(1000)
      .expect(viewMember.exists).notOk()
      .wait(500)
  }
} export default editWorkSpace
