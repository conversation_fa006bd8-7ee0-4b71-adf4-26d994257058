import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addDocument from "./add_document";

const naviBar = new navbar();
const golVari = new golvar();
const docPage = new addDocument();

class deleteDocument {

  constructor() {
    this.deleteBtn = Selector('[data-tc-delete]');
    this.confirmDelete = Selector('[data-tc-delete-document-modal-btn]');
    this.checkDeletedDoc = Selector('[data-tc-no-result-found]');
  }

  async deleteDocuments(docName) {
    const viewDocName = Selector(`[data-tc-view-document="${docName}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(naviBar.resourceNavMenu)
      .click(naviBar.resourceNavMenu)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/articles')
      .wait(500)
      .hover(docPage.docTab)
      .click(docPage.docTab)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/documents')
      .hover(docPage.searchField)
      .typeText(docPage.searchField, docName, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .expect(viewDocName.innerText).contains(docName)
      .wait(1000)
      .hover(this.deleteBtn)
      .click(this.deleteBtn)
      .wait(2000)
      .hover(this.confirmDelete)
      .click(this.confirmDelete)
      .wait(2000)
      .hover(docPage.searchField)
      .typeText(docPage.searchField, docName, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .expect(this.checkDeletedDoc.innerText).contains('Nothing found, try searching again.')
      .wait(1000)
  }
} export default deleteDocument
