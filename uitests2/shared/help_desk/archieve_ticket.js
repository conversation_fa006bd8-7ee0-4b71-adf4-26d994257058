import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import deleteTicket from "./delete_tickets";
import indexPageAction from "./ticket_index_page_action";

const naviBar = new navbar();
const golVari = new golvar();
const delTicPage = new deleteTicket();
const indexPage = new indexPageAction();

class archiveTicket {

  constructor() {
    this.cancelArchiveBtn = Selector('[data-tc-cancel-archive-btn]');
    this.archieveBadge = Selector('[data-tc-archived-badge]');
    this.cancelUnarchive = Selector('[data-tc-cancel-unarchive-modal]');
    this.viewArchivedFilter = Selector('[data-tc-applied-filter="Archived"]');
    this.removeArchiveFilter = Selector(`[data-tc-remove-multi-filter="Archived"]`);
  }

  async archivedTickets(subject, switcher) {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTicPage.ticketTab)
      .click(delTicPage.ticketTab)
      .wait(1000)
      .hover(delTicPage.searchField)
      .typeText(delTicPage.searchField, subject, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(delTicPage.searchedTicket)
      .expect(delTicPage.searchedTicket.innerText).contains(subject)
      .click(delTicPage.searchedTicket)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .hover(delTicPage.archiveBtn)
      .click(delTicPage.archiveBtn)
      .wait(500);
    if (switcher == 'yes') {
      await t.wait(1000)
        .hover(delTicPage.confirmArchived)
        .click(delTicPage.confirmArchived)
        .wait(1000)
        .expect(golVari.currentpageurl()).contains('/help_tickets')
        .hover(delTicPage.filterIcon)
        .click(delTicPage.filterIcon)
        .wait(500)
        .hover(delTicPage.archiveFilter)
        .click(delTicPage.archiveFilter)
        .wait(1000)
        .hover(delTicPage.searchField)
        .typeText(delTicPage.searchField, subject, { replace: true })
        .pressKey('enter')
        .expect(delTicPage.searchedTicket.innerText).contains(subject)
        .wait(1000)
        .hover(this.archieveBadge)
        .expect(this.archieveBadge.exists).ok()
        .wait(1000)
    }
    else {
      await t.wait(1000)
        .hover(this.cancelArchiveBtn)
        .click(this.cancelArchiveBtn)
        .wait(1000)
        .hover(delTicPage.archiveBtn)
        .expect(delTicPage.archiveBtn.exists).ok()
        .wait(1000)
    }
  }

  async unarchiveTickets(subject1, switchTo) {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTicPage.ticketTab)
      .click(delTicPage.ticketTab)
      .wait(1000)
      // .hover(delTicPage.filterIcon) not used for right now need to uncomment if this test cases run indepedently
      // .click(delTicPage.filterIcon)
      // .wait(500)
      // .hover(delTicPage.archiveFilter)
      // .click(delTicPage.archiveFilter)
      // .wait(1000)
      .hover(delTicPage.searchField)
      .typeText(delTicPage.searchField, subject1, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(delTicPage.searchedTicket)
      .expect(delTicPage.searchedTicket.innerText).contains(subject1)
      .click(delTicPage.searchedTicket)
      .wait(1000)
      .hover(delTicPage.unarchiveBtn)
      .click(delTicPage.unarchiveBtn)
      .wait(1000);
    if (switchTo == 'yes') {
      await t.wait(1000)
        .hover(delTicPage.unarchiveConfirm)
        .click(delTicPage.unarchiveConfirm)
        .wait(2000)
        .hover(delTicPage.searchField)
        .typeText(delTicPage.searchField, subject1, { replace: true })
        .pressKey('enter')
        .wait(3000)
        .expect(this.archieveBadge.exists).notOk()
        .wait(1000)
    }
    else {
      await t.wait(1000)
        .hover(this.cancelUnarchive)
        .click(this.cancelUnarchive)
        .wait(1000)
        .hover(delTicPage.unarchiveBtn)
        .expect(delTicPage.unarchiveBtn.exists).ok()
        .wait(1000)
        .click(indexPage.redirectBackTic)
        .wait(2000)
        .expect(golVari.currentpageurl()).contains('/help_tickets/')
        .typeText(delTicPage.searchField, subject1, { replace: true })
        .pressKey('enter')
        .wait(3000)
        .expect(this.archieveBadge.exists).ok()
        .wait(1000)
    }
  }
} export default archiveTicket
