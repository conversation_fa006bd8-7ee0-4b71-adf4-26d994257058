import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";

const naviBar = new navbar();
const golVari = new golvar();

class attachmentComment {
  constructor() {
    this.addTicketBtn = Selector(`${golVari.horizontalMenu} [data-tc-add="helpdesk"]`);
    this.subjectField = Selector('[data-tc-text-field="subject"] [data-tc-task-value]');
    this.createTicketBtn = Selector('[data-tc-save-form-btn="Create ticket"]');
    this.attachmentsTab = Selector('[data-tc-attachments]');
    this.inputFile = Selector('[data-tc-attachment] [data-tc-uploading-input-attachments]');
    this.commentText = Selector('[data-tc-comment]');
    this.commentsTab = Selector('[data-tc-comments]');
    this.attachment = Selector('[data-tc-attachment]');
  }

  async addCommentForAttachment(subject, AttachmentText) {
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(this.addTicketBtn)
      .click(this.addTicketBtn)
      .wait(1000)
      .hover(this.subjectField)
      .typeText(this.subjectField, subject, { paste: true })
      .wait(2000)
      .hover(this.attachment)
      .click(this.attachment)
      .setFilesToUpload(this.inputFile, '../../shared/help_desk/ticketImage.jpg')
      .wait(1000)
      .hover(this.createTicketBtn)
      .click(this.createTicketBtn)
      .wait(4000)
      .expect(this.commentText.innerText).contains(AttachmentText)
  }
}
export default attachmentComment
