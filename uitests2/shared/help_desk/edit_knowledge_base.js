import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addKnowledgeBase from "./add_knowledge_base";
import editAsset from "../assets/edit_assets";

const naviBar = new navbar();
const golVari = new golvar();
const articlePage = new addKnowledgeBase();
const editAstPage = new editAsset();

class editKnowledgeBase {

  constructor() {
    this.reviewDate = Selector('[data-tc-date="review by article"] [data-tc-dates-input]');
    this.selectTagList = Selector('[data-tc-input="article tags"] li span');
  }

  async editArticles(title, editTitle, tags, selectCategory, selectDocument, bodyText, date, currentCompany, reviewByDate,location) {
    const viewTitleOnModal = Selector(`[data-tc-title="${title}"]`);
    const viewEditTitleOnModal = Selector(`[data-tc-title="${editTitle}"]`);
    const viewSelectDoc = Selector(`[data-tc-listed-document="${selectDocument}"]`);
    const selectDateOption = Selector(`[data-tc-date-option="${date}"]`);
    const articleBox = Selector(`[data-tc-article="${title}"]`);
    const editArticleBox = Selector(`[data-tc-article="${editTitle}"]`);
    const viewaddedTitle = editArticleBox.find(`[data-tc-view-title="${editTitle}"]`);
    const viewBody = editArticleBox.find('p');
    const viewTag = editArticleBox.find(`[data-tc-view-article-tags="${tags}"]`);
    const viewCategory = editArticleBox.find(`[data-tc-view-article-category="${selectCategory}"]`);
    const publicArticle = editArticleBox.find('[data-tc-article-is-public]');
    const removeDocIcon = Selector(`[data-tc-menu-document="${selectDocument}"] [data-tc-icon="delete document"]`);
    const selectLocation = editAstPage.locationDropDown.find('li').withText(location);
    await t.hover(naviBar.resourceNavMenu)
      .click(naviBar.resourceNavMenu)
      .wait(1000)
      .hover(articlePage.articleNavTab)
      .click(articlePage.articleNavTab)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('help_tickets/articles')
      .wait(1000)
      .hover(articleBox)
      .expect(articleBox.exists).ok()
      .wait(500)
      .click(articleBox)
      .wait(1000)
      .hover(viewTitleOnModal)
      .expect(viewTitleOnModal.innerText).contains(title)
      .wait(500)
      .click(articlePage.contentTab)
      .expect(articlePage.contentTab.innerText).contains('Content')
      .wait(500)
      .expect(articlePage.titleLabel.innerText).contains('Title ')
      .typeText(articlePage.titleField, editTitle, { replace: true })
      .wait(500)
      .click(articlePage.tagDropDown)
      .wait(500)
      .typeText(articlePage.inputTags, tags, { replace: true })
      .wait(1000)
      .click(this.selectTagList.withText(tags))
      .wait(500)
      .hover(articlePage.categoryLabel)
      .expect(articlePage.categoryLabel.innerText).contains('Category ')
      .hover(articlePage.categoryDropDown)
      .wait(1000)
      .click(articlePage.categoryDropDown)
      .wait(500)
      .click(articlePage.selectCategory.withText(selectCategory))
      .wait(1000)
      .hover(articlePage.documentLabel)
      .expect(articlePage.documentLabel.innerText).contains('Attach Document')
      .expect(viewSelectDoc.innerText).contains(selectDocument)
      .wait(500)
      .click(removeDocIcon)
      .wait(500)
      .expect(viewSelectDoc.exists).notOk()
      .wait(500)
      .click(editAstPage.locationDropDown)
      .wait(500)
      .click(selectLocation)
      .hover(articlePage.bodyField)
      .typeText(articlePage.bodyField, bodyText, { replace: true })
      .wait(1000)
      .hover(viewEditTitleOnModal)
      .expect(viewEditTitleOnModal.innerText).contains(editTitle)
      .wait(500)
      .click(articlePage.reviewDateTab)
      .expect(articlePage.reviewDateTab.innerText).contains('Review Date')
      .wait(500)
      .hover(articlePage.reviewQuestion)
      .expect(articlePage.reviewQuestion.innerText).contains('When should this be reviewed?')
      .hover(articlePage.reviewAnswer)
      .expect(articlePage.reviewAnswer.innerText).contains(`We will send a reminder to the author of this article on it's review date so that your article does not get outdated.`)
      .wait(500)
      .hover(articlePage.reviewByDropdown)
      .click(articlePage.reviewByDropdown)
      .wait(500)
      .click(selectDateOption)
      .wait(500)
      .hover(this.reviewDate)
      .typeText(this.reviewDate, reviewByDate, { paste: true })
      .wait(500)
      .pressKey('enter')
      .click(articlePage.audienceTab)
      .expect(articlePage.audienceTab.innerText).contains('Audience')
      .wait(500)
      .hover(articlePage.audienceQuestion1)
      .expect(articlePage.audienceQuestion1.innerText).contains('Do you want to make this Article publicly available?')
      .hover(articlePage.audienceAnswer1)
      .expect(articlePage.audienceAnswer1.innerText).contains('Articles can be read outside of the system as well. You get to choose if this article goes public or not.')
      .wait(500)
      .hover(articlePage.makePublicText)
      .expect(articlePage.makePublicText.innerText).contains('Make it Public')
      .hover(articlePage.makePublicToggle)
      .click(articlePage.makePublicToggle)
      .wait(500)
      .expect(articlePage.activeToggleCheck.exists).notOk()
      .hover(articlePage.audienceQuestion2)
      .expect(articlePage.audienceQuestion2.innerText).contains('Who is this article meant for?')
      .hover(articlePage.audienceAnswer2)
      .expect(articlePage.audienceAnswer2.innerText).contains('You get to choose who this article is for. You can make it accessible to everyone in your company or set custom permissions for specific individuals.')
      .wait(500)
      .hover(articlePage.viewArticlePerm)
      .hover(articlePage.saveBtn)
      .click(articlePage.saveBtn)
      .wait(1000)
      .hover(editArticleBox)
      .expect(editArticleBox.exists).ok()
      .expect(viewaddedTitle.innerText).contains(editTitle)
      .expect(viewBody.innerText).contains(bodyText)
      .expect(viewTag.innerText).contains(tags)
      .expect(viewCategory.innerText).contains(selectCategory)
      .expect(publicArticle.exists).notOk()
      .wait(500);
  }
} export default editKnowledgeBase
