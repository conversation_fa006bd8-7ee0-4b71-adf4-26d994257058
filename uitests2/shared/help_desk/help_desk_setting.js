import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import deletedWorkSpace from "./delete_work_space";

const naviBar = new navbar();
const golVari = new golvar();
const delWorkSpace = new deletedWorkSpace();
const toggle = '[data-tc-slide-toggle]';

class checkSettings {

  constructor() {
    this.notifyPopup = Selector('.notification-content');
    this.viewHeader = Selector('[data-tc-header="Access  Settings"]');
    this.faqPageToggle = Selector(`[data-tc="Allow unauthenticated users to access the FAQ page"] ${toggle}`);
    this.impactedUserToggle = Selector(`[data-tc="Allow Impacted users or Followers to view tickets in which they are added"] ${toggle}`);
    this.preventDuplicationToggle = Selector(`[data-tc="Prevent duplicate notifications on ticket close when a comment has been marked for resolution"] ${toggle}`);
    this.setAssignToggle = Selector(`[data-tc="Set assigned to based on the first agent to respond to a ticket"] ${toggle}`);
    this.ticPrefixToggle = Selector(`[data-tc="Customize ticket number"] ${toggle}`);
    this.disableSaveBtn = Selector('[data-tc-disable-save="true"]');
    this.saveBtn = Selector('[data-tc-save-btn="true"]');
    this.inputPrefix = Selector('[data-tc-input="prefix"]');
    this.inputticNumber = Selector('[data-tc-input="ticket number"]');
    this.publicCenterNav = Selector('[data-tc="help center"]');
    this.displaySettingNav = Selector('[data-tc="display settings"]');
    this.selectClassic = Selector('[data-tc="classic design"]');
    this.selectModern = Selector('[data-tc="modern design"]');
    this.designToggleActive = Selector('[data-tc-toggle="help center design"] [data-tc-active-task="true"]');
  }

  async generalSettings() {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delWorkSpace.settingIcon)
      .wait(500)
      .click(delWorkSpace.settingIcon)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/general')
      .expect(this.viewHeader.innerText).contains('Access Settings')
      .wait(500)
      .click(this.impactedUserToggle)
      .expect(this.notifyPopup.innerText).contains('Successfully updated your helpdesk settings')
      .wait(500)
      .click(this.impactedUserToggle)
      .expect(this.notifyPopup.innerText).contains('Successfully updated your helpdesk settings')
      .wait(500)
      .click(this.preventDuplicationToggle)
      .expect(this.notifyPopup.innerText).contains('Successfully updated your helpdesk settings')
      .wait(500)
      .click(this.preventDuplicationToggle)
      .expect(this.notifyPopup.innerText).contains('Successfully updated your helpdesk settings')
      .wait(500)
      .click(this.setAssignToggle)
      .expect(this.notifyPopup.innerText).contains('Successfully updated your helpdesk settings')
      .wait(500)
      .click(this.setAssignToggle)
      .expect(this.notifyPopup.innerText).contains('Successfully updated your helpdesk settings')
      .wait(500)
      .expect(this.disableSaveBtn.exists).ok()
      .click(this.ticPrefixToggle)
      .expect(this.notifyPopup.innerText).contains('Successfully updated your helpdesk settings')
      .wait(500)
      .expect(this.disableSaveBtn.exists).notOk()
      .expect(this.saveBtn.exists).ok()
      .click(this.ticPrefixToggle)
      .expect(this.notifyPopup.innerText).contains('Successfully updated your helpdesk settings')
      .wait(500)
      .expect(this.disableSaveBtn.exists).ok()
      .expect(this.saveBtn.exists).notOk()
      .wait(500);
  }

  async ticCustomIndex(prefix, tickNumber) {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delWorkSpace.settingIcon)
      .wait(500)
      .click(delWorkSpace.settingIcon)
      .wait(1000)
      .click(this.ticPrefixToggle)
      .expect(this.notifyPopup.innerText).contains('Successfully updated your helpdesk settings')
      .wait(500)
      .typeText(this.inputPrefix, prefix, { replace: true })
      .wait(500)
      .click(this.inputticNumber)
      .pressKey('backspace')
      .pressKey('backspace')
      .wait(500)
      .typeText(this.inputticNumber, tickNumber, { replace: true })
      .wait(500)
      .click(this.saveBtn)
      .wait(500)
      .expect(this.notifyPopup.innerText).contains('Successfully applied custom ticket number settings to create new ticket numbers.')
      .wait(500);
  }

  async enableClassicPortal() {
    await t.hover(delWorkSpace.settingIcon)
      .wait(500)
      .click(delWorkSpace.settingIcon)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/settings/general')
      .wait(500)
      .hover(this.publicCenterNav)
      .click(this.publicCenterNav)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/settings/help_center')
      .hover(this.displaySettingNav)
      .click(this.displaySettingNav)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/settings/help_center/display')
      .hover(this.selectClassic)
      .click(this.selectClassic)
      .wait(500)
      .expect(this.designToggleActive.exists).notOk();
  }

  async enableModernPortal() {
    await t.hover(delWorkSpace.settingIcon)
      .wait(500)
      .click(delWorkSpace.settingIcon)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/settings/general')
      .wait(500)
      .hover(this.publicCenterNav)
      .click(this.publicCenterNav)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/settings/help_center')
      .hover(this.displaySettingNav)
      .click(this.displaySettingNav)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/settings/help_center/display')
      .hover(this.selectModern)
      .click(this.selectModern)
      .wait(500)
      .expect(this.designToggleActive.exists).ok('enable toggle');
  }

} export default checkSettings;
