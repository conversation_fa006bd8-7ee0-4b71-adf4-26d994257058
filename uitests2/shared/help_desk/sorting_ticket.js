import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import indexPageAction from "./ticket_index_page_action";
import deleteTicket from "./delete_tickets";

const naviBar = new navbar();
const golVari = new golvar();
const indexPage = new indexPageAction();
const delTic = new deleteTicket();

class sortingTickets {

  constructor() {
    this.sortDropDown = Selector('[data-tc-sort="dropdown"]');
    this.sortPriorty = Selector('[data-tc-sort-option="Priority"] i');
    this.sortStatus = Selector('[data-tc-sort-option="Status"]');
    this.sortAssignedTo = Selector('[data-tc-sort-option="Assigned To"]');
    this.sortCreatedBy = Selector('[data-tc-sort-option="Created By"]');
    this.sortTicNumber = Selector('[data-tc-sort-option="Ticket Number"]');
    this.viewSortTitle = Selector('[data-tc-view-title="sort column"]');
    this.arrowIconUp = Selector('[data-tc-direction-icon="genuicon-sort-up"]');
    this.arrowIconDown = Selector('[data-tc-direction-icon="genuicon-sort-down"]');
    this.ticNumberCell = Selector('[data-tc-ticket-number]');
  }

  async checkPriorityOrder(sortingType) {
    const cellSelector1 = Selector('[data-tc="ticket table"]:nth-child(1) [data-tc-ticket-priority-dropdown] span');
    const cellSelector2 = Selector('[data-tc="ticket table"]:nth-child(2) [data-tc-ticket-priority-dropdown] span');
    const cellSelector3 = Selector('[data-tc="ticket table"]:nth-child(3) [data-tc-ticket-priority-dropdown] span');
    const cellSelector4 = Selector('[data-tc="ticket table"]:nth-child(4) [data-tc-ticket-priority-dropdown] span');
    const cellSelector5 = Selector('[data-tc="ticket table"]:nth-child(5) [data-tc-ticket-priority-dropdown] span');
    const cellSelector6 = Selector('[data-tc="ticket table"]:nth-child(6) [data-tc-ticket-priority-dropdown] span');
    const cellSelector7 = Selector('[data-tc="ticket table"]:nth-child(7) [data-tc-ticket-priority-dropdown] span');
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(indexPage.listToggleIcon)
      .wait(500)
      .click(indexPage.listToggleIcon)
      .wait(1000)
    if (sortingType == 'asc') {
      await t.wait(2000)
        .expect(cellSelector1.innerText).eql('High')
        .wait(500)
        .expect(cellSelector2.innerText).eql('High')
        .wait(500)
        .expect(cellSelector3.innerText).eql('High')
        .wait(500)
        .expect(cellSelector4.innerText).eql('Medium')
        .wait(500)
        .expect(cellSelector5.innerText).eql('Medium')
        .wait(500)
        .expect(cellSelector6.innerText).eql('Low')
        .wait(500)
        .expect(cellSelector7.innerText).eql('Low')
        .wait(1000)
    }
    else {
      await t.wait(2000)
        .click(this.sortDropDown)
        .wait(1000)
        .doubleClick(this.sortPriorty)
        .wait(1000)
        .click(this.sortDropDown)
        .wait(1000)
        .expect(cellSelector1.innerText).eql('Low')
        .wait(500)
        .expect(cellSelector2.innerText).eql('Low')
        .wait(500)
        .expect(cellSelector3.innerText).eql('Medium')
        .wait(500)
        .expect(cellSelector4.innerText).eql('Medium')
        .wait(500)
        .expect(cellSelector5.innerText).eql('High')
        .wait(500)
        .expect(cellSelector6.innerText).eql('High')
        .wait(500)
        .expect(cellSelector7.innerText).eql('High')
        .wait(1000)
        .click(this.sortDropDown)
        .wait(1000)
        .click(this.sortPriorty)
        .wait(1000)
        .click(this.sortDropDown)
        .wait(1000)
    }
  }

  async checkStatusOrder(sortingType) {
    const cellSelector1 = Selector('[data-tc="ticket table"]:nth-child(1) [data-tc-ticket-status-dropdown] span');
    const cellSelector2 = Selector('[data-tc="ticket table"]:nth-child(2) [data-tc-ticket-status-dropdown] span');
    const cellSelector3 = Selector('[data-tc="ticket table"]:nth-child(3) [data-tc-ticket-status-dropdown] span');
    const cellSelector4 = Selector('[data-tc="ticket table"]:nth-child(4) [data-tc-ticket-status-dropdown] span');
    const cellSelector5 = Selector('[data-tc="ticket table"]:nth-child(5) [data-tc-ticket-status-dropdown] span');
    const cellSelector6 = Selector('[data-tc="ticket table"]:nth-child(6) [data-tc-ticket-status-dropdown] span');
    const cellSelector7 = Selector('[data-tc="ticket table"]:nth-child(7) [data-tc-ticket-status-dropdown] span');
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(indexPage.listToggleIcon)
      .wait(500)
      .click(indexPage.listToggleIcon)
      .wait(1000)
    if (sortingType == 'asc') {
      await t.wait(2000)
        .expect(cellSelector1.innerText).eql('Open')
        .wait(500)
        .expect(cellSelector2.innerText).eql('Open')
        .wait(500)
        .expect(cellSelector3.innerText).eql('Open')
        .wait(500)
        .expect(cellSelector4.innerText).eql('In Progress')
        .wait(500)
        .expect(cellSelector5.innerText).eql('In Progress')
        .wait(500)
        .expect(cellSelector6.innerText).eql('Closed')
        .wait(500)
        .expect(cellSelector7.innerText).eql('Closed')
        .wait(1000)
    }
    else {
      await t.wait(2000)
        .click(this.sortDropDown)
        .wait(1000)
        .doubleClick(this.sortStatus)
        .wait(1000)
        .click(this.sortDropDown)
        .wait(1000)
        .expect(cellSelector1.innerText).eql('Closed')
        .wait(500)
        .expect(cellSelector2.innerText).eql('Closed')
        .wait(500)
        .expect(cellSelector3.innerText).eql('In Progress')
        .wait(500)
        .expect(cellSelector4.innerText).eql('In Progress')
        .wait(500)
        .expect(cellSelector5.innerText).eql('Open')
        .wait(500)
        .expect(cellSelector6.innerText).eql('Open')
        .wait(500)
        .expect(cellSelector7.innerText).eql('Open')
        .wait(1000)
        .click(this.sortDropDown)
        .wait(1000)
        .click(this.sortStatus)
        .wait(1000)
        .click(this.sortDropDown)
        .wait(1000)
    }
  }

  async checkTicNumberOrder(sortingType) {
    const cellSelector1 = Selector('[data-tc="ticket table"]:nth-child(1) [data-tc-ticket-number]');
    const cellSelector2 = Selector('[data-tc="ticket table"]:nth-child(2) [data-tc-ticket-number]');
    const cellSelector3 = Selector('[data-tc="ticket table"]:nth-child(3) [data-tc-ticket-number]');
    const cellSelector4 = Selector('[data-tc="ticket table"]:nth-child(4) [data-tc-ticket-number]');
    const cellSelector5 = Selector('[data-tc="ticket table"]:nth-child(5) [data-tc-ticket-number]');
    const cellSelector6 = Selector('[data-tc="ticket table"]:nth-child(6) [data-tc-ticket-number]');
    const cellSelector7 = Selector('[data-tc="ticket table"]:nth-child(7) [data-tc-ticket-number]');
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(indexPage.listToggleIcon)
      .wait(500)
      .click(indexPage.listToggleIcon)
      .wait(1000)
    if (sortingType == 'asc') {
      await t.wait(2000)
        .expect(cellSelector1.innerText).eql('1')
        .wait(500)
        .expect(cellSelector2.innerText).eql('2')
        .wait(500)
        .expect(cellSelector3.innerText).eql('3')
        .wait(500)
        .expect(cellSelector4.innerText).eql('4')
        .wait(500)
        .expect(cellSelector5.innerText).eql('5')
        .wait(500)
        .expect(cellSelector6.innerText).eql('6')
        .wait(500)
        .expect(cellSelector7.innerText).eql('7')
        .wait(1000)
    }
    else {
      await t.wait(2000)
        .click(this.sortDropDown)
        .wait(1000)
        .doubleClick(this.sortTicNumber)
        .wait(1000)
        .click(this.sortDropDown)
        .wait(1000)
        .expect(cellSelector1.innerText).eql('7')
        .wait(500)
        .expect(cellSelector2.innerText).eql('6')
        .wait(500)
        .expect(cellSelector3.innerText).eql('5')
        .wait(500)
        .expect(cellSelector4.innerText).eql('4')
        .wait(500)
        .expect(cellSelector5.innerText).eql('3')
        .wait(500)
        .expect(cellSelector6.innerText).eql('2')
        .wait(500)
        .expect(cellSelector7.innerText).eql('1')
        .wait(1000)
        .click(this.sortDropDown)
        .wait(1000)
        .click(this.sortTicNumber)
        .wait(1000)
        .click(this.sortDropDown)
        .wait(1000)
    }
  }

  async checkAssignedToOrder(sortingType, groupName) {
    const cellSelector1 = Selector('[data-tc="ticket table"]:nth-child(1) [data-tc-listed-assigned-to] span:nth-child(2) span');
    const cellSelector2 = Selector('[data-tc="ticket table"]:nth-child(2) [data-tc-listed-assigned-to] span:nth-child(2) span');
    const cellSelector3 = Selector('[data-tc="ticket table"]:nth-child(3) [data-tc-listed-assigned-to] span:nth-child(2) span');
    const cellSelector4 = Selector('[data-tc="ticket table"]:nth-child(4) [data-tc-listed-assigned-to] span:nth-child(2) span');
    const cellSelector5 = Selector('[data-tc="ticket table"]:nth-child(5) [data-tc-listed-assigned-to] span:nth-child(2) span');
    const cellSelector6 = Selector('[data-tc="ticket table"]:nth-child(6) [data-tc-listed-assigned-to] span:nth-child(2) span');
    const cellSelector7 = Selector('[data-tc="ticket table"]:nth-child(7) [data-tc-listed-assigned-to] span:nth-child(2) span');
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(indexPage.listToggleIcon)
      .wait(500)
      .click(indexPage.listToggleIcon)
      .wait(1000)
    if (sortingType == 'asc') {
      await t.wait(2000)
        .expect(cellSelector1.innerText).eql('Admins')
        .wait(500)
        .expect(cellSelector2.innerText).eql('Admins')
        .wait(500)
        .expect(cellSelector3.innerText).eql(groupName)
        .wait(500)
        .expect(cellSelector4.innerText).eql(groupName)
        .wait(500)
        .expect(cellSelector5.innerText).eql(groupName)
        .wait(500)
        .expect(cellSelector6.innerText).eql(groupName)
        .wait(500)
        .expect(cellSelector7.innerText).eql(groupName)
        .wait(1000)
    }
    else {
      await t.wait(2000)
        .click(this.sortDropDown)
        .wait(1000)
        .doubleClick(this.sortAssignedTo)
        .wait(1000)
        .click(this.sortDropDown)
        .wait(1000)
        .expect(cellSelector1.innerText).eql(groupName)
        .wait(500)
        .expect(cellSelector2.innerText).eql(groupName)
        .wait(500)
        .expect(cellSelector3.innerText).eql(groupName)
        .wait(500)
        .expect(cellSelector4.innerText).eql(groupName)
        .wait(500)
        .expect(cellSelector5.innerText).eql(groupName)
        .wait(500)
        .expect(cellSelector6.innerText).eql('Admins')
        .wait(500)
        .expect(cellSelector7.innerText).eql('Admins')
        .wait(1000)
        .click(this.sortDropDown)
        .wait(1000)
        .click(this.sortAssignedTo)
        .wait(1000)
        .click(this.sortDropDown)
        .wait(1000)
    }
  }

  async checkCreatedByOrder(sortingType, userName, groupName1) {
    const cellSelector1 = Selector(`[data-tc="ticket table"]:nth-child(1) [data-tc-listed-staff-assigned="${userName}"]`);
    const cellSelector2 = Selector(`[data-tc="ticket table"]:nth-child(2) [data-tc-listed-staff-assigned="${userName}"]`);
    const cellSelector3 = Selector(`[data-tc="ticket table"]:nth-child(3) [data-tc-listed-staff-assigned="${userName}"]`);
    const cellSelector4 = Selector(`[data-tc="ticket table"]:nth-child(4) [data-tc-listed-staff-assigned="${userName}"]`);
    const cellSelector5 = Selector(`[data-tc="ticket table"]:nth-child(5) [data-tc-listed-staff-assigned="${userName}"]`);
    const cellSelector6 = Selector(`[data-tc="ticket table"]:nth-child(6) [data-tc-listed-staff-assigned="${userName}"]`);
    const cellSelector7 = Selector(`[data-tc="ticket table"]:nth-child(7) [data-tc-listed-staff-assigned="${userName}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(indexPage.listToggleIcon)
      .wait(500)
      .click(indexPage.listToggleIcon)
      .wait(1000)
    if (sortingType == 'asc') {
      await t.wait(2000)
        .expect(cellSelector1.innerText).eql(`${userName}, ${groupName1}`)
        .wait(500)
        .expect(cellSelector2.innerText).eql(`${userName}, ${groupName1}`)
        .wait(500)
        .expect(cellSelector3.innerText).eql(`${userName}, ${groupName1}`)
        .wait(500)
        .expect(cellSelector4.innerText).eql(`${userName}, ${groupName1}`)
        .wait(500)
        .expect(cellSelector5.innerText).eql(`${userName}, ${groupName1}`)
        .wait(500)
        .expect(cellSelector6.innerText).eql(`${userName}, Everyone`)
        .wait(500)
        .expect(cellSelector7.innerText).eql(`${userName}, Everyone`)
        .wait(1000)
    }
    else {
      await t.wait(2000)
        .click(this.sortDropDown)
        .wait(1000)
        .doubleClick(this.sortCreatedBy)
        .wait(1000)
        .click(this.sortDropDown)
        .wait(1000)
        .expect(cellSelector1.innerText).eql(`${userName}, ${groupName1}`)
        .wait(500)
        .expect(cellSelector2.innerText).eql(`${userName}, ${groupName1}`)
        .wait(500)
        .expect(cellSelector3.innerText).eql(`${userName}, ${groupName1}`)
        .wait(500)
        .expect(cellSelector4.innerText).eql(`${userName}, ${groupName1}`)
        .wait(500)
        .expect(cellSelector5.innerText).eql(`${userName}, ${groupName1}`)
        .wait(500)
        .expect(cellSelector6.innerText).eql(`${userName}, Everyone`)
        .wait(500)
        .expect(cellSelector7.innerText).eql(`${userName}, Everyone`)
        .wait(1000)
        .click(this.sortDropDown)
        .wait(1000)
        .click(this.sortCreatedBy)
        .wait(1000)
        .click(this.sortDropDown)
        .wait(1000)
    }
  }

  async setItemsInAscendingOrder(columnName) {
    const columnSelector = Selector(`[data-tc-sort-option="${columnName}"]`);
    await t.click(this.sortDropDown)
      .wait(1000)
      .hover(columnSelector)
      .click(columnSelector)
      .wait(500)
      .click(this.sortDropDown)
      .wait(500)
      .expect(this.arrowIconUp.visible).ok()
      .expect(this.viewSortTitle.innerText).contains(`Sort: ${columnName}`)
      .wait(1000)
  }

  async setItemsInDescendingOrder(columnName1) {
    const columnSelector = Selector(`[data-tc-sort-option="${columnName1}"]`);
    await t.click(this.sortDropDown)
      .wait(1000)
      .hover(columnSelector)
      .click(columnSelector)
      .wait(500)
      .click(this.sortDropDown)
      .wait(500)
      .expect(this.arrowIconDown.visible).ok()
      .expect(this.viewSortTitle.innerText).contains(`Sort: ${columnName1}`)
      .wait(500)
  }

  async viewSortedItems(index, ticketSubject) {
    var myInteger = parseInt(index);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .hover(delTic.searchedTicket.nth(myInteger))
      .expect(delTic.searchedTicket.nth(myInteger).innerText).contains(ticketSubject)
  }

  async viewSortedNumberListView(index, ticketNumber) {
    var myInteger = parseInt(index);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(500)
      .hover(this.ticNumberCell.nth(myInteger))
      .expect(this.ticNumberCell.nth(myInteger).innerText).contains(ticketNumber)
  }

  async viewSortedUserListView(index, rowPosition, people) {
    var myInteger = parseInt(index);
    const userSelector = Selector('[data-tc="ticket table"]').nth(myInteger).find(`[data-tc-ticket="${rowPosition}"] [data-tc-people-listed]`);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(500)
      .expect(userSelector.innerText).contains(people)
  }

  async viewSortedStatusView(index, rowPosition, status) {
    var myInteger = parseInt(index);
    const statusSelector = Selector('[data-tc="ticket table"]').nth(myInteger).find(`[data-tc-ticket="${rowPosition}"] [data-tc-listed-status]`);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(500)
      .expect(statusSelector.innerText).contains(status)
  }

  async viewSortedPriortyView(index, rowPosition, priority) {
    var myInteger = parseInt(index);
    const prioritySelector = Selector('[data-tc="ticket table"]').nth(myInteger).find(`[data-tc-ticket="${rowPosition}"] [data-tc-listed-priority]`);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(500)
      .expect(prioritySelector.innerText).contains(priority)
  }

  async viewSortedSubjectView(index, rowPosition, subject) {
    var myInteger = parseInt(index);
    const subjectSelector = Selector('[data-tc="ticket table"]').nth(myInteger).find(`[data-tc-ticket="${rowPosition}"] [data-tc-listed-note]`);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(500)
      .expect(subjectSelector.innerText).contains(subject)
  }
} export default sortingTickets
