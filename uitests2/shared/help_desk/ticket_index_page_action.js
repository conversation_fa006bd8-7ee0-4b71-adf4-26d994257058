import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addTicket from "./add_tickets";
import deleteTicket from "./delete_tickets";
import editTickets from "./edit_tickets";

const naviBar = new navbar();
const golVari = new golvar();
const addTic = new addTicket();
const editTic = new editTickets();
const delTic = new deleteTicket();

class indexPageAction {

  constructor() {
    this.gridToggleIcon = Selector('[data-tc-btn="Grid"]');
    this.listToggleIcon = Selector('[data-tc-btn="List"]');
    this.notifyPopup = Selector('.notification-content');
    this.statusDropList = Selector('[data-tc-status-option]');
    this.priorityDropList = Selector('[data-tc-priority-option]');
    this.viewStatusGrid = addTic.statusDropDown.find('span');
    this.dismissFilterContainer = Selector('[data-tc-icon="dismiss"]');
    this.redirectBackTic = Selector('[ data-tc-icon="Back to all tickets"]');
    this.closeActiveStatusFilter = Selector('[data-tc-active-filter-label="Status"] [data-tc-active-filter-close]');
    this.closeActivePriorityFilter = Selector('[data-tc-active-filter-label="Priority"] [data-tc-active-filter-close]');
    this.assignedToDropIcon = Selector('[data-tc-assigned-to-drop-icon]');
    this.gridViewTickets = Selector('[data-tc-view="help ticket grid"]');
    this.listViewTickets = Selector('[data-tc-view-hd-list="true"]');
    this.listMenu = Selector('[data-tc-list-menu="List View"]');
  }

  async getTicketIndex(subject) {
    const ticket = Selector(`[data-tc-ticket="${subject}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .hover(ticket)
      .expect(ticket.exists).ok('Check the ticket is present or not.');
    const indexElement = Selector(`[data-tc-ticket="${subject}"] [data-tc-ticket-index]`);
    const ticketIndex = await indexElement.innerText;
    return ticketIndex;
  }

  async updateTicketThroughGridView(subject1, editStatus, editPriority) {
    const ticket = Selector(`[data-tc-ticket="${subject1}"]`);
    const statusDropIcon = Selector(`[data-tc-ticket="${subject1}"] [${addTic.statusSelector}]`);
    const prorityDropIcon = Selector(`[data-tc-ticket="${subject1}"] [${editTic.prioritySelector}]`);
    const viewEditPriority = Selector(`[data-tc-view-priority="${editPriority}"]`);
    const selectStatusFilter = Selector(`[data-tc-apply-filter="${editStatus}"]`);
    const selectPriorityFilter = Selector(`[data-tc-apply-filter="${editPriority}"]`);
    const viewStatusGrid = Selector(`[data-tc-ticket="${subject1}"] [data-tc-ticket-status-dropdown] span`);

    const viewEditStatus = Selector(`[data-tc-view-status="${editStatus}"]`);
    const viewAppliedStatusFilter = Selector(`[data-tc-applied-filter="${editStatus}"]`);
    const viewAppliedPriorityFilter = Selector(`[data-tc-applied-filter="${editPriority}"]`);
    const removeMultiStatusFilter = Selector(`[data-tc-remove-multi-filter="${editStatus}"]`);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .hover(this.gridToggleIcon)
      .wait(500)
      .click(this.gridToggleIcon)
      .wait(500)
      .hover(statusDropIcon)
      .wait(500)
      .click(statusDropIcon)
      .wait(1000)
      .hover(this.statusDropList.withText(editStatus))
      .click(this.statusDropList.withText(editStatus))
      .wait(2000)
      .expect(this.notifyPopup.innerText).contains('Successfully changed')
      .wait(1000)
      .hover(delTic.filterIcon)
      .wait(1000)
      .click(delTic.filterIcon)
      .wait(1000)
      .hover(selectStatusFilter)
      .wait(1000)
      .click(selectStatusFilter)
      .wait(1000)
      .click(this.dismissFilterContainer)
      .wait(1000)
      .hover(viewAppliedStatusFilter)
      .wait(1000)
      .expect(viewAppliedStatusFilter.innerText).contains(editStatus)
      .wait(500)
      .expect(viewStatusGrid.nth(1).innerText).contains(editStatus)
      .wait(1000)
      .hover(prorityDropIcon)
      .wait(500)
      .click(prorityDropIcon)
      .wait(1000)
      .hover(this.priorityDropList.withText(editPriority))
      .wait(1000)
      .click(this.priorityDropList.withText(editPriority))
      .wait(1000)
      .hover(delTic.filterIcon)
      .wait(1000)
      .click(delTic.filterIcon)
      .wait(1000)
      .hover(selectPriorityFilter)
      .wait(1000)
      .click(selectPriorityFilter)
      .wait(1000)
      .click(this.dismissFilterContainer)
      .wait(1000)
      .hover(viewAppliedPriorityFilter)
      .wait(1000)
      .expect(viewAppliedPriorityFilter.innerText).contains(editPriority)
      .wait(1000)
      .expect(statusDropIcon.innerText).contains(editStatus)
      .wait(1000)
      .click(ticket)
      .wait(1000)
      .hover(viewEditPriority)
      .wait(1000)
      .expect(viewEditPriority.innerText).contains(editPriority)
      .wait(1000)
      .expect(viewEditStatus.innerText).contains(editStatus)
      .wait(1000)
      .hover(this.redirectBackTic)
      .wait(1000)
      .click(this.redirectBackTic)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(this.closeActiveStatusFilter)
      .wait(1000)
      .click(this.closeActiveStatusFilter)
      .wait(1000);
    // .hover(removeMultiStatusFilter)
    // .wait(500)
    // .click(removeMultiStatusFilter)
    // .wait(2000)
    // .hover(this.closeActivePriorityFilter)
    // .wait(1000)
    // .click(this.closeActivePriorityFilter)
    // .wait(1000)
  }

  async updateTicketThroughListView(subject2, assignedTo, editStatus1, editPriority1, editAssignedTo1) {
    const viewTicSubject = Selector(`[data-tc-listed-value="${subject2}"]`);
    const viewEditPriority1 = Selector(`[data-tc-view-priority="${editPriority1}"]`);
    const selectStatusFilter1 = Selector(`[data-tc-apply-filter="${editStatus1}"]`);
    const selectPriorityFilter1 = Selector(`[data-tc-apply-filter="${editPriority1}"]`);
    const viewEditStatus1 = Selector(`[data-tc-view-status="${editStatus1}"]`);
    const viewAppliedPriorityFilter1 = Selector(`[data-tc-applied-filter="${editPriority1}"]`);
    const removeMultiStatusFilter1 = Selector(`[data-tc-remove-multi-filter="${editStatus1}"]`);
    const selectNewAssignedTo = Selector(`[data-tc-selected-object="${editAssignedTo1}"]`);
    const viewSelectedAssignedTo = Selector(`[data-tc-selected-object="${assignedTo}"]`);
    const removeSelectedAssignedTo = Selector(`[data-tc-remove-selected="${assignedTo}"] [data-tc-remove-btn="object"]`);
    const viewNewAssignedTo = Selector(`[data-tc-listed-staff-assigned="${editAssignedTo1}"]`);
    const viewEditStatus = Selector(`[data-tc-view-status="${editStatus1}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(this.listToggleIcon)
      .wait(500)
      .click(this.listToggleIcon)
      .wait(1000)
      .click(this.listMenu)
      .wait(1000)
      .expect(this.listViewTickets.visible).ok()
      .wait(500)
      .hover(delTic.searchField)
      .typeText(delTic.searchField, subject2, { replace: true })
      .wait(3000)
      .hover(viewTicSubject)
      .wait(1000)
      .expect(viewTicSubject.innerText).contains(subject2)
      .wait(1000)
      .hover(addTic.statusDropDown)
      .wait(1000)
      .click(addTic.statusDropDown)
      .wait(1000)
      .hover(this.statusDropList.withText(editStatus1))
      .click(this.statusDropList.withText(editStatus1))
      .wait(2000)
      .expect(this.notifyPopup.innerText).contains('Successfully changed')
      .wait(1000)
      .hover(delTic.filterIcon)
      .wait(1000)
      .click(delTic.filterIcon)
      .wait(1000)
      .hover(selectStatusFilter1)
      .wait(1000)
      .click(selectStatusFilter1)
      .wait(1000)
      .click(this.dismissFilterContainer)
      .wait(1000)
      .hover(viewEditStatus)
      .wait(1000)
      .expect(viewEditStatus.innerText).contains(editStatus1)
      .hover(addTic.statusDropDown)
      .wait(1000)
      .hover(editTic.dropDownPriority)
      .wait(500)
      .click(editTic.dropDownPriority)
      .wait(1000)
      .hover(this.priorityDropList.withText(editPriority1))
      .wait(1000)
      .click(this.priorityDropList.withText(editPriority1))
      .wait(1000)
      .hover(delTic.filterIcon)
      .wait(1000)
      .click(delTic.filterIcon)
      .wait(1000)
      .hover(selectPriorityFilter1)
      .wait(1000)
      .click(selectPriorityFilter1)
      .wait(1000)
      .click(this.dismissFilterContainer)
      .wait(1000)
      .hover(viewAppliedPriorityFilter1)
      .wait(1000)
      .expect(viewAppliedPriorityFilter1.innerText).contains(editPriority1)
      .wait(1000)
      .hover(this.assignedToDropIcon)
      .wait(1000)
      .click(this.assignedToDropIcon)
      .wait(3000)
      .hover(viewSelectedAssignedTo)
      .wait(500)
      .expect(viewSelectedAssignedTo.innerText).contains(assignedTo)
      .wait(1000)
      .hover(removeSelectedAssignedTo)
      .wait(500)
      .click(removeSelectedAssignedTo)
      .wait(2000)
      .expect(this.notifyPopup.innerText).contains('Successfully removed Assigned To')
      .wait(1000)
      .click(this.assignedToDropIcon)
      .wait(3000)
      .hover(selectNewAssignedTo)
      .wait(500)
      .click(selectNewAssignedTo)
      .wait(2000)
      .expect(this.notifyPopup.innerText).contains('Successfully added Assigned To')
      .wait(1000)
      .hover(viewNewAssignedTo)
      .wait(1000)
      .expect(viewNewAssignedTo.innerText).contains(editAssignedTo1)
      .wait(1000)
      .hover(viewTicSubject)
      .wait(1000)
      .expect(addTic.statusDropDown.innerText).contains(editStatus1)
      .wait(1000)
      .click(viewTicSubject)
      .wait(1000)
      .hover(viewEditPriority1)
      .wait(1000)
      .expect(viewEditPriority1.innerText).contains(editPriority1)
      .wait(1000)
      .expect(viewEditStatus1.innerText).contains(editStatus1)
      .wait(1000)
      .hover(this.redirectBackTic)
      .wait(1000)
      .click(this.redirectBackTic)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(this.closeActiveStatusFilter)
      .wait(1000)
      .click(this.closeActiveStatusFilter)
      .wait(1000)
      .hover(removeMultiStatusFilter1)
      .wait(500)
      .click(removeMultiStatusFilter1)
      .wait(2000)
      .hover(this.closeActivePriorityFilter)
      .wait(1000)
      .click(this.closeActivePriorityFilter)
      .wait(1000)
      .hover(this.gridToggleIcon)
      .wait(500)
      .click(this.gridToggleIcon)
      .wait(1000);
  }

  async viewHDGrid() {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(this.gridToggleIcon)
      .wait(500)
      .click(this.gridToggleIcon)
      .wait(1000)
      .expect(this.gridViewTickets.visible).ok()
      .wait(500);
  }

  async viewHDList() {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(this.listToggleIcon)
      .wait(500)
      .click(this.listToggleIcon)
      .wait(1000)
      .click(this.listMenu)
      .wait(1000)
      .expect(this.listViewTickets.visible).ok()
      .wait(500);
  }
} export default indexPageAction;
