import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";

const naviBar = new navbar();
const golVari = new golvar();

class deleteTicket {

  constructor() {
    this.ticketTab = Selector(`${golVari.horizontalMenu} [data-tc-tickets]`);
    this.searchField = Selector('[data-tc-ticket-search]');
    this.searchedTicket = Selector('[data-tc-ticket-subject]');
    this.archiveBtn = Selector('[data-tc-archive]');
    this.confirmArchived = Selector('[data-tc-modal-archive-btn]');
    this.filterIcon = Selector('[data-tc-filter-menu-button]');
    this.archiveFilter = Selector('[data-tc-apply-filter="Archived"]');
    this.deleteBtn = Selector('[data-tc-delete]');
    this.viewDeleteModal = Selector('[data-tc-view-title="delete ticket"]');
    this.confirmDelete = Selector('[data-tc-modal-delete-btn]');
    this.cancelDelete = Selector('[data-tc-cancel-delete-btn]');
    this.viewDelTic = Selector('[data-tc-no-tickets]');
    this.unarchiveBtn = Selector('[data-tc-unarchive]');
    this.unarchiveConfirm = Selector('[data-tc-modal-unarchive-btn]');
    this.removeFilterMenu = Selector('[data-tc-icon="dismiss"]');
  }

  async deleteTickets(subject, toggle) {
    const ticket = Selector(`[data-tc-view-subject="${subject}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(this.ticketTab)
      .click(this.ticketTab)
      .wait(1000)
      .hover(ticket)
      .expect(ticket.innerText).contains(subject)
      .click(ticket)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .hover(this.archiveBtn)
      .click(this.archiveBtn)
      .wait(500)
      .hover(this.confirmArchived)
      .click(this.confirmArchived)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets')
      .hover(this.filterIcon)
      .click(this.filterIcon)
      .wait(500)
      .hover(this.archiveFilter)
      .click(this.archiveFilter)
      .wait(1000)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(ticket)
      .expect(ticket.innerText).contains(subject)
      .click(ticket)
      .wait(2000);
    if (toggle == 'yes') {
      await t.hover(this.deleteBtn)
        .click(this.deleteBtn)
        .hover(this.confirmDelete)
        .click(this.confirmDelete)
        .wait(3000)
        .hover(this.searchField)
        .typeText(this.searchField, subject, { replace: true })
        .pressKey('enter')
        .wait(2000);
    }
    else {
      await t.wait(1000)
        .hover(this.deleteBtn)
        .click(this.deleteBtn)
        .wait(1000)
        .hover(this.cancelDelete)
        .click(this.cancelDelete)
        .wait(1000)
        .expect(this.deleteBtn.exists).ok()
        .wait(1000)
        .hover(this.unarchiveBtn)
        .click(this.unarchiveBtn)
        .wait(1000)
        .hover(this.unarchiveConfirm)
        .click(this.unarchiveConfirm)
        .wait(1000);
    }
  }

  async deletingTickets(subject) {
    const ticket = Selector(`[data-tc-view-subject="${subject}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(this.ticketTab)
      .click(this.ticketTab)
      .wait(1000)
      .hover(ticket)
      .expect(ticket.innerText).contains(subject)
      .click(ticket)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .hover(this.archiveBtn)
      .click(this.archiveBtn)
      .wait(500)
      .hover(this.confirmArchived)
      .click(this.confirmArchived)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets')
      .hover(this.filterIcon)
      .click(this.filterIcon)
      .wait(500)
      .hover(this.archiveFilter)
      .click(this.archiveFilter)
      .wait(1000)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(ticket)
      .expect(ticket.innerText).contains(subject)
      .click(ticket)
      .wait(2000)
      .hover(this.deleteBtn)
      .click(this.deleteBtn)
      .hover(this.confirmDelete)
      .click(this.confirmDelete)
      .wait(3000)
      .expect(ticket.exists).notOk('The ticket is deleted');
  }
} export default deleteTicket;
