import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";

const naviBar = new navbar();
const golVari = new golvar();

class moveTicket {
  constructor() {
    this.addTicketBtn = Selector(`${golVari.horizontalMenu} [data-tc-add="helpdesk"]`);
    this.ticketIndex = Selector('[data-tc-tickets]');
    this.subjectField = Selector('[data-tc-text-field="subject"] [data-tc-task-value]');
    this.createTicketBtn = Selector('[data-tc-save-form-btn="Create ticket"]');
    this.moveTicketbtn = Selector('[data-tc-action-btn="Move ticket"]');
    this.workSpaceBtn = Selector('[data-tc-move-ticket-to-workspace]');
    this.workSpaceDropDown = Selector('[data-tc-workspace-dropdown]');
    this.selectWorkSpace = this.workSpaceDropDown.find('li span span');
    this.formDropDown = Selector('[data-tc-select-custom-form]');
    this.selectForm = this.formDropDown.find('li span span');
    this.submitTicketToMove = Selector('[data-tc-save-form-btn="Move ticket"]');
    this.formBtn = Selector('[data-tc-move-ticket-to-form]');
    this.ticketSearch = Selector('[data-tc-ticket-search]');
    this.commentsTab = Selector('[data-tc-comments]');
    this.moreActionIcon = Selector('[data-tc-more-actions]');
  }

  async moveTicketWorkspace(subject, workspace, form) {
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(this.addTicketBtn)
      .click(this.addTicketBtn)
      .wait(1000)
      .hover(this.subjectField)
      .typeText(this.subjectField, subject, { paste: true })
      .wait(2000)
      .hover(this.createTicketBtn)
      .click(this.createTicketBtn)
      .wait(4000)
      .hover(this.moreActionIcon)
      .click(this.moreActionIcon)
      .wait(500)
      .hover(this.moveTicketbtn)
      .click(this.moveTicketbtn)
      .wait(1000)
      .hover(this.workSpaceBtn)
      .click(this.workSpaceBtn)
      .wait(1000)
      .hover(this.workSpaceDropDown)
      .click(this.workSpaceDropDown)
      .wait(1000)
      .hover(this.selectWorkSpace.withText(workspace))
      .click(this.selectWorkSpace.withText(workspace))
      .wait(1000)
      .hover(this.formDropDown)
      .click(this.formDropDown)
      .wait(1000)
      .hover(this.selectForm.withText(form))
      .click(this.selectForm.withText(form))
      .wait(1000)
      .hover(this.submitTicketToMove)
      .click(this.submitTicketToMove)
      .wait(5000);
  }

  async moveTicketForm(subject, form) {
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(this.addTicketBtn)
      .click(this.addTicketBtn)
      .wait(1000)
      .hover(this.subjectField)
      .typeText(this.subjectField, subject, { paste: true })
      .wait(2000)
      .hover(this.createTicketBtn)
      .click(this.createTicketBtn)
      .wait(4000)
      .hover(this.moreActionIcon)
      .click(this.moreActionIcon)
      .wait(500)
      .hover(this.moveTicketbtn)
      .click(this.moveTicketbtn)
      .wait(1000)
      .hover(this.formBtn)
      .click(this.formBtn)
      .wait(1000)
      .hover(this.formDropDown)
      .click(this.formDropDown)
      .wait(1000)
      .hover(this.selectForm.withText(form))
      .click(this.selectForm.withText(form))
      .wait(1000)
      .hover(this.submitTicketToMove)
      .click(this.submitTicketToMove)
      .wait(3000)
      .expect(this.commentsTab.exists).ok();
  }
}
export default moveTicket;
