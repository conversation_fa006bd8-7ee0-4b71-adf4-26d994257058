import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";

const naviBar = new navbar();
const golVari = new golvar();

class report {

  constructor() {
    this.reportIcon = Selector(`${golVari.horizontalMenu} [data-tc-reporting-tab]`);
    this.settingIcon = Selector('[data-dropdown-menu="settings_menu"]');
    this.selectReport = Selector('[data-tc-settings-menu="Reports"]');
    this.continueBtn = Selector('[data-tc-continue]');
    this.selectColName = Selector('[data-tc-select-all-checmark="reports"]');
    this.viewText = Selector('[data-tc-label-text]');
    this.selectTabular = Selector('[data-tc-select-type="Tabular"]');
    this.selectGraphical = Selector('[data-tc-select-type="Graphical"]');
    this.valueDropDown = Selector('[data-tc-drop-down="graph value"]');
    this.selectValue = this.valueDropDown.find('option');
    this.itemDropDown = Selector('[data-tc-select-item]');
    this.selectContract = this.itemDropDown.find('li span span');
    this.inputItem = Selector('[data-tc-select-item] input');
    this.inputTitle = Selector('[data-tc-field="title"]');
    this.inputPrefix = Selector('[data-tc-field="label prefix"]');
    this.downloadBtn = Selector('[data-tc-btn="download pdf"]');
    this.moveSelectedbtn = Selector('[data-tc-move-to-selected-btn]');
    this.downloadCsv = Selector('[data-tc-csv]');
    this.downloadXls = Selector('[data-tc-excel]');
    this.downloadPdf = Selector('[data-tc-pdf]');
    this.downloadJson = Selector('[data-tc-json]');
    this.downloadHtml = Selector('[data-tc-html]');
    this.viewTitle = Selector('[data-tc-title="Reports"]');
    this.extensionCsv = '.csv';
    this.extensionXls = '.xlsx';
    this.extensionPdf = '.pdf';
    this.extensionJson = '.json';
    this.extensionHtml = '.html';
  }

  async reports(module, timeFrame) {
    const selectModule = Selector(`[data-tc-select-module="${module}"]`);
    const selectTimeFrame = Selector(`[data-tc-select-timeframe="${timeFrame}"]`);
    const viewCsv = Selector(`[data-tc-report-name="${module + this.extensionCsv}"]`);
    const viewXls = Selector(`[data-tc-report-name="${module + this.extensionXls}"]`);
    const viewPdf = Selector(`[data-tc-report-name="${module + this.extensionPdf}"]`);
    const viewJson = Selector(`[data-tc-report-name="${module + this.extensionJson}"]`);
    const viewHtml = Selector(`[data-tc-report-name="${module + this.extensionHtml}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(this.reportIcon)
      .click(this.reportIcon)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('/reports?module=help_tickets')
      .wait(3000);
    naviBar.refresh();
    await t.hover(selectModule)
      .click(selectModule)
      .wait(1000)
      .hover(this.continueBtn)
      .click(this.continueBtn)
      .wait(1000)
      .hover(this.selectColName)
      .click(this.selectColName)
      .wait(2000)
      .hover(this.moveSelectedbtn)
      .click(this.moveSelectedbtn)
      .wait(2000)
      .hover(this.continueBtn)
      .click(this.continueBtn)
      .wait(2000)
      .hover(selectTimeFrame)
      .click(selectTimeFrame)
      .wait(2000)
      .hover(this.continueBtn)
      .click(this.continueBtn)
      .wait(2000)
      .hover(this.downloadCsv)
      .click(this.downloadCsv)
      .wait(7000)
      .hover(this.downloadXls)
      .click(this.downloadXls)
      .wait(7000)
      .hover(this.downloadPdf)
      .click(this.downloadPdf)
      .wait(7000)
      .hover(this.downloadJson)
      .click(this.downloadJson)
      .wait(7000)
      .hover(this.downloadHtml)
      .click(this.downloadHtml)
      .wait(7000)
      .hover(viewCsv)
      .expect(viewCsv.innerText).contains(module + this.extensionCsv)
      .wait(2000)
      .expect(viewXls.innerText).contains(module + this.extensionXls)
      .wait(2000)
      .expect(viewPdf.innerText).contains(module + this.extensionPdf)
      .wait(2000)
      .expect(viewJson.innerText).contains(module + this.extensionJson)
      .wait(2000)
      .expect(viewHtml.innerText).contains(module + this.extensionHtml)
      .wait(2000);
  }

  async reportsWithTabular(module, timeFrame) {
    const selectModule = Selector(`[data-tc-select-module="${module}"]`);
    const selectTimeFrame = Selector(`[data-tc-select-timeframe="${timeFrame}"]`);
    const viewCsv = Selector(`[data-tc-report-name="${module + this.extensionCsv}"]`);
    const viewXls = Selector(`[data-tc-report-name="${module + this.extensionXls}"]`);
    const viewPdf = Selector(`[data-tc-report-name="${module + this.extensionPdf}"]`);
    const viewJson = Selector(`[data-tc-report-name="${module + this.extensionJson}"]`);
    const viewHtml = Selector(`[data-tc-report-name="${module + this.extensionHtml}"]`);
    await t.hover(this.settingIcon)
      .click(this.settingIcon)
      .wait(1000)
      .hover(this.selectReport)
      .click(this.selectReport)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/reports')
      .wait(3000);
    naviBar.refresh();
    await t.hover(selectModule)
      .click(selectModule)
      .wait(1000)
      .expect(this.viewText.innerText).contains('What type of report would you like to build?')
      .wait(500)
      .hover(this.selectTabular)
      .click(this.selectTabular)
      .wait(1000)
      .hover(this.continueBtn)
      .click(this.continueBtn)
      .wait(1000)
      .hover(this.selectColName)
      .click(this.selectColName)
      .wait(2000)
      .hover(this.moveSelectedbtn)
      .click(this.moveSelectedbtn)
      .wait(2000)
      .hover(this.continueBtn)
      .click(this.continueBtn)
      .wait(2000)
      .hover(selectTimeFrame)
      .click(selectTimeFrame)
      .wait(2000)
      .hover(this.continueBtn)
      .click(this.continueBtn)
      .wait(2000)
      .hover(this.downloadCsv)
      .click(this.downloadCsv)
      .wait(7000);
    // .hover(this.downloadXls) //downloading issue
    // .click(this.downloadXls)
    // .wait(7000)
    // .hover(this.downloadPdf)
    // .click(this.downloadPdf)
    // .wait(7000)
    // .hover(this.downloadJson)
    // .click(this.downloadJson)
    // .wait(7000)
    // .hover(this.downloadHtml)
    // .click(this.downloadHtml)
    // .wait(7000)
    // .hover(viewCsv)
    // .expect(viewCsv.innerText).contains(module + this.extensionCsv)
    // .wait(2000)
    // .expect(viewXls.innerText).contains(module + this.extensionXls)
    // .wait(2000)
    // .expect(viewPdf.innerText).contains(module + this.extensionPdf)
    // .wait(2000)
    // .expect(viewJson.innerText).contains(module + this.extensionJson)
    // .wait(2000)
    // .expect(viewHtml.innerText).contains(module + this.extensionHtml)
    // .wait(2000)
  }

  async reportsWithGraphical(module, graphselection, value, selectItem, graphTitle, labelPrefix) {
    const selectModule = Selector(`[data-tc-select-module="${module}"]`);
    const selectGraph = Selector(`[data-tc-select-type="${graphselection}"]`);
    const selectItemOpt = Selector(`[data-tc-vendor-drop-down-listing="${selectItem}"]`);
    await t.hover(this.settingIcon)
      .click(this.settingIcon)
      .wait(1000)
      .hover(this.selectReport)
      .click(this.selectReport)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/reports')
      .wait(3000);
    naviBar.refresh();
    await t.hover(selectModule)
      .click(selectModule)
      .wait(1000)
      .expect(this.viewText.innerText).contains('What type of report would you like to build?')
      .wait(500)
      .hover(this.selectGraphical)
      .click(this.selectGraphical)
      .wait(1000)
      .hover(this.continueBtn)
      .click(this.continueBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/reports')
      .wait(1000)
      .click(selectGraph)
      .wait(1000)
      .hover(this.valueDropDown)
      .click(this.valueDropDown)
      .wait(1000)
      .click(this.selectValue.withText(value))
      .wait(1000)
      .click(this.itemDropDown)
      .wait(500)
      .click(selectItemOpt)
      .wait(1000)
      .hover(this.continueBtn)
      .click(this.continueBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/reports')
      .wait(1000)
      .typeText(this.inputTitle, graphTitle, { replace: true })
      .wait(1000)
      .typeText(this.inputPrefix, labelPrefix, { replace: true })
      .wait(1000)
      .hover(this.continueBtn)
      .click(this.continueBtn)
      .wait(1000)
      .click(this.downloadBtn)
      .wait(3000);
  }

  async reportsWithGraphicalContract(module, graphselection, value, selectItem, graphTitle, labelPrefix) {
    const selectModule = Selector(`[data-tc-select-module="${module}"]`);
    const selectGraph = Selector(`[data-tc-select-type="${graphselection}"]`);
    await t.hover(this.settingIcon)
      .click(this.settingIcon)
      .wait(1000)
      .hover(this.selectReport)
      .click(this.selectReport)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/reports')
      .wait(3000);
    naviBar.refresh();
    await t.hover(selectModule)
      .click(selectModule)
      .wait(1000)
      .expect(this.viewText.innerText).contains('What type of report would you like to build?')
      .wait(500)
      .hover(this.selectGraphical)
      .click(this.selectGraphical)
      .wait(1000)
      .hover(this.continueBtn)
      .click(this.continueBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/reports')
      .wait(1000)
      .click(selectGraph)
      .wait(1000)
      .hover(this.valueDropDown)
      .click(this.valueDropDown)
      .wait(1000)
      .click(this.selectValue.withText(value))
      .wait(1000)
      .click(this.itemDropDown)
      .wait(500)
      .click(this.selectContract.withText(selectItem))
      .wait(1000)
      .hover(this.continueBtn)
      .click(this.continueBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/reports')
      .wait(1000)
      .typeText(this.inputTitle, graphTitle, { replace: true })
      .wait(1000)
      .typeText(this.inputPrefix, labelPrefix, { replace: true })
      .wait(1000)
      .hover(this.continueBtn)
      .click(this.continueBtn)
      .wait(1000)
      .click(this.downloadBtn)
      .wait(3000);
  }
} export default report;
