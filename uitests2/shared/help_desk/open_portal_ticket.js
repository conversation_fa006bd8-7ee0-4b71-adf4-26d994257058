import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addTicket from "./add_tickets";
import deleteTicket from "./delete_tickets";
import signinpage from "../signin/signinpage";
import indexPageAction from "./ticket_index_page_action";
import addAsset from "../assets/add_assets";

const naviBar = new navbar();
const golVari = new golvar();
const ticketPage = new addTicket();
const delTic = new deleteTicket();
const logIntoApp = new signinpage();
const indexPage = new indexPageAction();
const addAstPage = new addAsset();

class openPortalTicket {

  constructor() {
    this.editNameField = Selector('[data-tc-update-name]');
    this.portalUrl = `${golVari.url}/help_center`;
    this.ticketDashboardUrl = `${golVari.url}/help_tickets/dashboard`;
    this.ticketPageUrl = `${golVari.url}/help_tickets/`;
    this.attachment = Selector('[data-tc="attachments"]');
    this.inputFile = Selector('[data-tc-text-field="attachments"] [data-tc-uploading-input-attachment]');
    this.createBtn = Selector('[data-tc-save-form-btn="Create ticket"]');
    this.successMessage = Selector('[data-tc-message1]');
    this.createdMessage = Selector('[data-tc-message2]');
    this.createdByInput = Selector('[data-tc-text-field="created_by"] [data-tc-text]');
    this.descField = Selector('[data-tc="description"]');
    this.attachField = Selector('[data-tc-text-field="attachments"]');
  }

  async createTicketWithlogin(subject, priority, status, createdBy, description, workSpaceSelected, ticketForm) {
    const viewPriority = Selector(`[data-tc-view-priority="${priority}"]`);
    const viewStatus = Selector(`[data-tc-view-status="${status}"]`);
    const viewCreatedBy = Selector(`[data-tc-view-selected="${createdBy}"]`);
    const verifySelectedSpace = Selector(`[data-tc-view-select-space="${workSpaceSelected}"]`);
    const formSelection = Selector(`[data-tc-select-ticket-form="${ticketForm}"]`);
    const verifySelectedForm = Selector(`[data-tc-view-selected-form="${ticketForm}"]`);
    const filterApplied = Selector(`[data-tc-apply-filter="${workSpaceSelected}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .navigateTo(this.portalUrl)
      .wait(2000)
      .hover(verifySelectedSpace)
      .wait(500)
      .expect(verifySelectedSpace.innerText).contains(workSpaceSelected)
      .wait(500)
      .click(verifySelectedSpace)
      .wait(1000)
      .hover(formSelection)
      .wait(500)
      .click(formSelection)
      .wait(2000)
      .hover(verifySelectedSpace)
      .wait(500)
      .expect(verifySelectedSpace.innerText).contains(workSpaceSelected)
      .wait(1000)
      .hover(verifySelectedForm)
      .wait(500)
      .expect(verifySelectedForm.innerText).contains(ticketForm)
      .wait(1000)
      .hover(ticketPage.subjectField)
      .typeText(ticketPage.subjectField, subject, { paste: true })
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_center/')
      .wait(1000)
      .hover(ticketPage.priorityDropDown)
      .click(ticketPage.priorityDropDown)
      .wait(1000)
      .hover(ticketPage.selectPriority.withText(priority))
      .click(ticketPage.selectPriority.withText(priority))
      .wait(1000)
      .hover(ticketPage.statusDropDown)
      .click(ticketPage.statusDropDown)
      .wait(1000)
      .hover(ticketPage.selectStatus.withText(status))
      .click(ticketPage.selectStatus.withText(status))
      .wait(1000)
      .hover(ticketPage.createdByDropDown)
      .click(ticketPage.createdByDropDown)
      .wait(500)
      .hover(ticketPage.selectCreatedBy.withText(createdBy))
      .click(ticketPage.selectCreatedBy.withText(createdBy))
      .wait(1000)
      .hover(ticketPage.descriptionField)
      .click(ticketPage.descriptionField)
      .typeText(ticketPage.descriptionField, description, { paste: true })
      .wait(1000)
      // .hover(this.attachment) // need to add scroller in the functionality
      // .wait(500)
      // .setFilesToUpload(this.inputFile, '../../shared/help_desk/ticketImage.jpg')
      // .wait(1000)
      .hover(this.createBtn)
      .wait(500)
      .click(this.createBtn)
      .wait(4000)
      .hover(this.successMessage)
      .wait(1000)
      .expect(this.successMessage.innerText).contains('Success!')
      .wait(500)
      .expect(this.createdMessage.innerText).contains('Your ticket has been submitted.')
      .wait(1000)
      .navigateTo(this.ticketDashboardUrl)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(2000)
      .hover(delTic.filterIcon)
      .wait(500)
      .click(delTic.filterIcon)
      .wait(1000)
      .hover(filterApplied)
      .click(filterApplied)
      .wait(1000)
      .click(indexPage.dismissFilterContainer)
      .wait(1000)
      .hover(delTic.searchField)
      .typeText(delTic.searchField, subject, { replace: true })
      .pressKey('enter')
      .wait(2000)
      .hover(delTic.searchedTicket)
      .expect(delTic.searchedTicket.innerText).contains(subject)
      .wait(1000)
      .click(delTic.searchedTicket)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .hover(ticketPage.viewSubject)
      .expect(ticketPage.viewSubject.innerText).contains(subject)
      .wait(500)
      .hover(viewStatus)
      .expect(viewStatus.innerText).contains(status)
      .wait(500)
      .hover(viewPriority)
      .expect(viewPriority.innerText).contains(priority)
      .wait(500)
      .hover(viewCreatedBy)
      .expect(viewCreatedBy.innerText).contains(createdBy)
      .hover(ticketPage.redirectBackTic)
      .click(ticketPage.redirectBackTic)
      .wait(500);
  }

  async portalClassicWithlogin(subject, priority, status, createdBy, description, ticketForm) {
    const viewPriority = Selector(`[data-tc-view-priority="${priority}"]`);
    const viewStatus = Selector(`[data-tc-view-status="${status}"]`);
    const viewCreatedBy = Selector(`[data-tc-view-selected="${createdBy}"]`);
    const formSelection = Selector(`[data-tc-select-ticket-form="${ticketForm}"]`);
    const verifySelectedForm = Selector(`[data-tc-view-selected-form="${ticketForm}"]`);

    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .navigateTo(this.portalUrl)
      .wait(2000)
      .hover(formSelection)
      .wait(500)
      .click(formSelection)
      .wait(2000)
      .hover(verifySelectedForm)
      .wait(500)
      .expect(verifySelectedForm.innerText).contains(ticketForm)
      .wait(1000)
      .hover(ticketPage.subjectField)
      .typeText(ticketPage.subjectField, subject, { paste: true })
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_center/')
      .wait(1000)
      .hover(ticketPage.priorityDropDown)
      .click(ticketPage.priorityDropDown)
      .wait(1000)
      .hover(ticketPage.selectPriority.withText(priority))
      .click(ticketPage.selectPriority.withText(priority))
      .wait(1000)
      .hover(ticketPage.statusDropDown)
      .click(ticketPage.statusDropDown)
      .wait(1000)
      .hover(ticketPage.selectStatus.withText(status))
      .click(ticketPage.selectStatus.withText(status))
      .wait(1000)
      .hover(ticketPage.createdByDropDown)
      .click(ticketPage.createdByDropDown)
      .wait(500)
      .hover(ticketPage.selectCreatedBy.withText(createdBy))
      .click(ticketPage.selectCreatedBy.withText(createdBy))
      .wait(1000)
      .hover(ticketPage.descriptionField)
      .click(ticketPage.descriptionField)
      .typeText(ticketPage.descriptionField, description, { paste: true })
      .wait(1000)
      .hover(this.createBtn)
      .wait(500)
      .click(this.createBtn)
      .wait(4000)
      .hover(this.successMessage)
      .wait(1000)
      .expect(this.successMessage.innerText).contains('Success!')
      .wait(500)
      .expect(this.createdMessage.innerText).contains('Your ticket has been submitted.')
      .wait(1000)
      .navigateTo(this.ticketDashboardUrl)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(2000)
      .hover(delTic.searchField)
      .typeText(delTic.searchField, subject, { replace: true })
      .pressKey('enter')
      .wait(2000)
      .hover(delTic.searchedTicket)
      .expect(delTic.searchedTicket.innerText).contains(subject)
      .wait(1000)
      .click(delTic.searchedTicket)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .hover(ticketPage.viewSubject)
      .expect(ticketPage.viewSubject.innerText).contains(subject)
      .wait(500)
      .hover(viewStatus)
      .expect(viewStatus.innerText).contains(status)
      .wait(500)
      .hover(viewPriority)
      .expect(viewPriority.innerText).contains(priority)
      .wait(500)
      .hover(viewCreatedBy)
      .expect(viewCreatedBy.innerText).contains(createdBy)
      .hover(ticketPage.redirectBackTic)
      .click(ticketPage.redirectBackTic)
      .wait(500);
  }

  async portalClassicWithoutLogin(email, password, subject1, priority1, status1, guestEmail, description1, ticketForm1) {
    const priorityAdded = Selector(`[data-tc-view-priority="${priority1}"]`);
    const statusAdded = Selector(`[data-tc-view-status="${status1}"]`);
    const createdByAdded = Selector(`[data-tc-view-selected="${guestEmail}"]`);
    const formSelection1 = Selector(`[data-tc-select-ticket-form="${ticketForm1}"]`);
    const verifySelectedForm1 = Selector(`[data-tc-view-selected-form="${ticketForm1}"]`);
    naviBar.signout();
    await t.wait(500)
      .navigateTo(this.portalUrl)
      .wait(1000)
      .hover(formSelection1)
      .wait(500)
      .click(formSelection1)
      .wait(2000)
      .hover(verifySelectedForm1)
      .expect(verifySelectedForm1.innerText).contains(ticketForm1)
      .wait(1000)
      .hover(ticketPage.subjectField)
      .typeText(ticketPage.subjectField, subject1, { paste: true })
      .expect(golVari.currentpageurl()).contains('/help_center/')
      .wait(500)
      .hover(ticketPage.priorityDropDown)
      .click(ticketPage.priorityDropDown)
      .wait(500)
      .hover(ticketPage.selectPriority.withText(priority1))
      .click(ticketPage.selectPriority.withText(priority1))
      .hover(ticketPage.statusDropDown)
      .click(ticketPage.statusDropDown)
      .wait(500)
      .hover(ticketPage.selectStatus.withText(status1))
      .click(ticketPage.selectStatus.withText(status1))
      .typeText(this.createdByInput, guestEmail, { paste: true })
      .wait(500)
      .hover(ticketPage.descriptionField)
      .click(ticketPage.descriptionField)
      .typeText(ticketPage.descriptionField, description1, { paste: true })
      .wait(500)
      // .hover(this.attachment) // need to add scroller in the functionality
      // .wait(500)
      // .setFilesToUpload(this.inputFile, '../../shared/help_desk/ticketImage.jpg')
      // .wait(1000)
      .hover(this.createBtn)
      .wait(500)
      .click(this.createBtn)
      .wait(4000)
      .hover(this.successMessage)
      .wait(1000)
      .expect(this.successMessage.innerText).contains('Success!')
      .wait(500)
      .expect(this.createdMessage.innerText).contains('Your ticket has been submitted.')
      .wait(1000);
    logIntoApp.siginCompanySelect(email, password, golVari.companyname);
    await t.wait(1000)
      .hover(addAstPage.companyViewToggle)
      .wait(1000)
      .click(addAstPage.companyViewToggle)
      .wait(1000)
      .navigateTo(this.ticketPageUrl)
      .wait(1000)
      .hover(delTic.searchField)
      .typeText(delTic.searchField, subject1, { replace: true })
      .pressKey('enter')
      .wait(2000)
      .hover(delTic.searchedTicket)
      .expect(delTic.searchedTicket.innerText).contains(subject1)
      .wait(1000)
      .click(delTic.searchedTicket)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .hover(ticketPage.viewSubject)
      .expect(ticketPage.viewSubject.innerText).contains(subject1)
      .wait(1000)
      .hover(statusAdded)
      .expect(statusAdded.innerText).contains(status1)
      .wait(500)
      .hover(priorityAdded)
      .expect(priorityAdded.innerText).contains(priority1)
      .wait(500)
      .hover(createdByAdded)
      .expect(createdByAdded.innerText).contains(guestEmail)
      .wait(500)
      .hover(ticketPage.redirectBackTic)
      .click(ticketPage.redirectBackTic)
      .wait(500);
  }

  async createTicketWithoutLogin(email, password, subject1, priority1, status1, guestEmail, description1, workSpaceSelected1, ticketForm1) {
    const priorityAdded = Selector(`[data-tc-view-priority="${priority1}"]`);
    const statusAdded = Selector(`[data-tc-view-status="${status1}"]`);
    const createdByAdded = Selector(`[data-tc-view-selected="${guestEmail}"]`);
    const verifySelectedSpace1 = Selector(`[data-tc-view-select-space="${workSpaceSelected1}"]`);
    const formSelection1 = Selector(`[data-tc-select-ticket-form="${ticketForm1}"]`);
    const verifySelectedForm1 = Selector(`[data-tc-view-selected-form="${ticketForm1}"]`);
    const filterApplied = Selector(`[data-tc-apply-filter="${workSpaceSelected1}"]`);
    naviBar.signout();
    await t.wait(500)
      .navigateTo(this.portalUrl)
      .wait(1000)
      .hover(verifySelectedSpace1)
      .wait(500)
      .expect(verifySelectedSpace1.innerText).contains(workSpaceSelected1)
      .wait(500)
      .click(verifySelectedSpace1)
      .wait(1000)
      .hover(formSelection1)
      .wait(500)
      .click(formSelection1)
      .wait(2000)
      .hover(verifySelectedSpace1)
      .wait(500)
      .expect(verifySelectedSpace1.innerText).contains(workSpaceSelected1)
      .wait(1000)
      .hover(verifySelectedForm1)
      .wait(500)
      .expect(verifySelectedForm1.innerText).contains(ticketForm1)
      .wait(1000)
      .hover(ticketPage.subjectField)
      .typeText(ticketPage.subjectField, subject1, { paste: true })
      .expect(golVari.currentpageurl()).contains('/help_center/')
      .wait(500)
      .hover(ticketPage.priorityDropDown)
      .click(ticketPage.priorityDropDown)
      .wait(500)
      .hover(ticketPage.selectPriority.withText(priority1))
      .click(ticketPage.selectPriority.withText(priority1))
      .hover(ticketPage.statusDropDown)
      .click(ticketPage.statusDropDown)
      .wait(500)
      .hover(ticketPage.selectStatus.withText(status1))
      .click(ticketPage.selectStatus.withText(status1))
      .typeText(this.createdByInput, guestEmail, { paste: true })
      .wait(500)
      .hover(ticketPage.descriptionField)
      .click(ticketPage.descriptionField)
      .typeText(ticketPage.descriptionField, description1, { paste: true })
      .wait(500)
      // .hover(this.attachment) // need to add scroller in the functionality
      // .wait(500)
      // .setFilesToUpload(this.inputFile, '../../shared/help_desk/ticketImage.jpg')
      // .wait(1000)
      .hover(this.createBtn)
      .wait(500)
      .click(this.createBtn)
      .wait(4000)
      .hover(this.successMessage)
      .wait(1000)
      .expect(this.successMessage.innerText).contains('Success!')
      .wait(500)
      .expect(this.createdMessage.innerText).contains('Your ticket has been submitted.')
      .wait(1000);
    logIntoApp.siginCompanySelect(email, password, golVari.companyname);
    await t.wait(1000)
      .hover(addAstPage.companyViewToggle)
      .wait(1000)
      .click(addAstPage.companyViewToggle)
      .wait(1000)
      .navigateTo(this.ticketPageUrl)
      .wait(1000)
      .hover(delTic.filterIcon)
      .wait(500)
      .click(delTic.filterIcon)
      .wait(1000)
      .hover(filterApplied)
      .click(filterApplied)
      .wait(1000)
      .click(indexPage.dismissFilterContainer)
      .wait(1000)
      .hover(delTic.searchField)
      .typeText(delTic.searchField, subject1, { replace: true })
      .pressKey('enter')
      .wait(2000)
      .hover(delTic.searchedTicket)
      .expect(delTic.searchedTicket.innerText).contains(subject1)
      .wait(1000)
      .click(delTic.searchedTicket)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .hover(ticketPage.viewSubject)
      .expect(ticketPage.viewSubject.innerText).contains(subject1)
      .wait(1000)
      .hover(statusAdded)
      .expect(statusAdded.innerText).contains(status1)
      .wait(500)
      .hover(priorityAdded)
      .expect(priorityAdded.innerText).contains(priority1)
      .wait(500)
      .hover(createdByAdded)
      .expect(createdByAdded.innerText).contains(guestEmail)
      .wait(500)
      .hover(ticketPage.redirectBackTic)
      .click(ticketPage.redirectBackTic)
      .wait(500);
  }
} export default openPortalTicket;
