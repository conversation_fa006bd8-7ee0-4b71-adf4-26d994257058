import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addWorkSpace from "./add_work_space";

const naviBar = new navbar();
const golVari = new golvar();
const addSpace = new addWorkSpace();

class deletedWorkSpace {

  constructor() {
    this.settingIcon = Selector(`${golVari.horizontalMenu} [data-tc-setting-icon="Help Desk"]`);
    this.workSpaceTab = Selector('[data-tc-workspaces-tab]');
    this.deleteBtn = Selector('[data-tc-delete-modal]');
    this.inputDelete = Selector('[data-tc-delete-field]');
    this.confirmDelete = Selector('[data-tc-delete-workspace]');
    this.deletedResult = Selector('[data-tc-no-found-work-space]');
  }

  async deleteWorkSpaces(spaceName) {
    const viewName = Selector(`[data-tc-value="${spaceName}"]`);
    const dotIcon = Selector(`[data-tc-workspace="${spaceName}"] [data-tc-icon="ellipsis workspace"]`);
    const deleteIcon = Selector(`[data-tc-workspace="${spaceName}"] [data-tc-icon="delete workspace"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(this.workSpaceTab)
      .click(this.workSpaceTab)
      .expect(golVari.currentpageurl()).contains('/help_tickets/workspaces')
      .hover(viewName)
      .expect(viewName.innerText).eql(spaceName)
      .hover(dotIcon)
      .click(dotIcon)
      .wait(300)
      .hover(deleteIcon)
      .click(deleteIcon)
      .wait(300)
      .typeText(this.inputDelete, 'DELETE', { paste: true })
      .wait(500)
      .click(this.confirmDelete)
      .wait(5000)
      .hover(addSpace.searchSpace)
      .typeText(addSpace.searchSpace, spaceName, { replace: true })
      .pressKey('enter')
      .wait(500)
      .hover(this.deletedResult)
      .expect(this.deletedResult.innerText).contains('No workspaces found, try changing filtering criteria.');
  }
} export default deletedWorkSpace
