import { Selector, t } from "testcafe";
import Navbar from "../Components/navbar";
import Golvar from "../Globalvariable";

const naviBar = new Navbar();
const golVari = new Golvar();

class incomingEmail {
    constructor() {
      this.incoming_btn = Selector(`${golVari.horizontalMenu} [data-tc-incoming]`);
      this.noEmailText = Selector('[data-tc="empty-incoming"]');
    }

    async noIncomingEmail(noEmail) {
      await t
        .hover(naviBar.helpdesk)
        .click(naviBar.helpdesk)
        .wait(1000)
        .hover(this.incoming_btn)
        .click(this.incoming_btn)
        .wait(1000)
        .expect(this.noEmailText.innerText).contains(noEmail);
    }
}

export default incomingEmail;
