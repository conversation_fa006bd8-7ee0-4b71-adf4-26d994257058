import moment from "moment-timezone";
import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addTicket from "./add_tickets";
import deleteTicket from "./delete_tickets";

const naviBar = new navbar();
const golVari = new golvar();
const addTic = new addTicket();
const delTic = new deleteTicket();

class editTickets {

  constructor() {
    this.prioritySelector = 'data-tc-ticket-priority-dropdown';
    this.removeSelector = 'data-tc-remove';
    this.inputSubject = Selector('[data-tc-input-field="subject"]');
    this.saveSubject = Selector('[data-tc-save-btn="subject"]');
    this.dropDownCreatedBy = Selector('[data-tc-drop-down="created_by"]');
    this.inputCreatedBy = Selector('[data-tc-drop-down="created_by"] input');
    this.dropDownAssignedTo = Selector('[data-tc-drop-down="assigned_to"]');
    this.inputAssignedTo = Selector('[data-tc-drop-down="assigned_to"] input');
    this.dropDownDevice = Selector('[data-tc-drop-down="impacted_devices"]');
    this.inputDevice = Selector('[data-tc-drop-down="impacted_devices"] input');
    this.dropDownLocation = Selector('[data-tc-drop-down="location"]');
    this.selectLocation1 = this.dropDownLocation.find('li');
    this.inputLocation = Selector('[data-tc-drop-down="location"] input');
    this.commentBtn = Selector('[data-tc-add-new-comment]');
    this.dropDownPriority = Selector(`[${this.prioritySelector}]`);
    this.prioritySelect = Selector('[data-tc-priority-options] [data-tc-priority-option]');
    this.dropDownStatus = Selector('[data-tc-ticket-status-dropdown]');
    this.statusSelect = Selector('[data-tc-status-options] [data-tc-status-option]');
    this.saveComment = Selector('[data-tc-add-comment]');
    this.tasksTab = Selector('[data-tc-tasks]');
    this.addTaskBtn = Selector('[data-tc-add-task]');
    this.taskDescription = Selector('[data-tc-task-description-input]');
    this.taskDueDate = Selector('[data-tc-task-due-at] [data-tc-dates-input]');
    this.taskCompletedDate = Selector('[data-tc-task-completed-at] [data-tc-dates-input]');
    this.taskAssignee = Selector('[data-tc-multi-user-field]');
    this.inputTaskAssignee = Selector('[data-tc-multi-user-field] input');
    this.saveTask = Selector('[data-tc-save-form-btn="Save task"]');
    this.viewAddedTask = Selector('[data-tc-task-description]');
    this.historyTab = Selector('[data-tc-history]');
    // this.viewComment = Selector('[data-tc-comment] [data-tc-view="description"]');
    this.timeSpentTab = Selector('[data-tc-time-spent]');
    this.inputTimeSpent = Selector('[data-tc-time-spent-entry]');
    this.inputDate = Selector('[data-tc-time-spent-date] [data-tc-dates-input]');
    // this.inputNotes = Selector('[data-tc-input-editor="ticketComment"]');
    this.inputNotes = Selector('[data-tc-notes="timespent"] trix-editor');
    this.viewSpent = Selector('[data-tc-view-spent]');
    this.notBtn = Selector('[data-tc-not-now]');
    this.notifyPopup = Selector(".notification-content");
    this.attachmentTab = Selector('[data-tc-attachments]');
    this.locField = Selector('[data-tc="location"]');
    this.selectMenuAll = this.dropDownAssignedTo.find('[data-tc-menu="all"]');
    this.selectMenuGroup = this.dropDownAssignedTo.find('[data-tc-menu="groups"]');
    this.timeSpentEntry = Selector('[data-tc-added-time-spent]');
  }

  async editTicketswithBaseForm(subject, createdBy, assignedTo, impactedDevice, location, description, editSubject, editPriority, editStatus, editCreatedBy, editAssignedTo, editImpactedDevice, editLocation, editDescription, comments, taskDescription, taskPriority, taskDueTo, taskCompleteTo, taskUser, timeSpent, date, notes) {
    const selectTaskPrority = Selector(`[data-tc-tasks-priorty="${taskPriority}"]`);
    const removeCreatedBy = Selector(`[${this.removeSelector}] [data-tc-remove-icon="${createdBy}"]`);
    const removeAssignedTo = Selector(`[${this.removeSelector}] [data-tc-remove-icon="${assignedTo}"]`);
    const removeDevice = Selector(`[${this.removeSelector}] [data-tc-remove-icon="${impactedDevice}"]`);
    const removeLocation = Selector(`[${this.removeSelector}] [data-tc-remove-icon="${location}"]`);
    const viewEditPriority = Selector(`[data-tc-view-priority="${editPriority}"]`);
    const viewEditStatus = Selector(`[data-tc-view-status="${editStatus}"]`);
    const viewNewTask = Selector(`[data-tc-view-task-added="${taskDescription}"]`);
    // const viewPriority = Selector(`[data-tc-view-priority="${editPriority}"]`);
    // const viewStatus = Selector(`[data-tc-view-status="${editStatus}"]`);
    const viewCreatedBy = Selector(`[data-tc-view-selected="${editCreatedBy}"]`);
    const viewAssignedTo = Selector(`[data-tc-view-selected="${editAssignedTo}"]`);
    const viewDevice = Selector(`[data-tc-view-selected="${editImpactedDevice}"]`);
    const viewLocation = Selector(`[data-tc-view-selected="${editLocation}"]`);
    const selectTaskUser = Selector(`[data-tc-selected-object="${taskUser}"]`);
    const viewEntry = Selector(`[data-tc-time-entry="${taskUser}"]`);
    const viewAddedTask = Selector(`[data-tc-view-task-description="${taskDescription}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .hover(delTic.searchField)
      .typeText(delTic.searchField, subject, { replace: true })
      .pressKey('enter')
      .wait(2000)
      .hover(delTic.searchedTicket)
      .expect(delTic.searchedTicket.innerText).contains(subject)
      .click(delTic.searchedTicket)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .hover(addTic.viewSubject)
      .expect(addTic.viewSubject.innerText).contains(subject)
      .click(addTic.viewSubject)
      .wait(1000)
      .hover(this.inputSubject)
      .click(this.inputSubject)
      .typeText(this.inputSubject, editSubject, { replace: true })
      .wait(1000)
      .hover(this.saveSubject)
      .click(this.saveSubject)
      .expect(addTic.viewSubject.innerText).contains(editSubject)
      .wait(1000)
      .hover(this.tasksTab)
      .click(this.tasksTab)
      .wait(1000)
      .click(this.addTaskBtn)
      .wait(1000)
      .hover(this.taskDescription)
      .typeText(this.taskDescription, taskDescription, { paste: true })
      .wait(1000)
      .hover(selectTaskPrority)
      .click(selectTaskPrority)
      .wait(500)
      .hover(this.taskDueDate)
      .typeText(this.taskDueDate, taskDueTo, { paste: true })
      .hover(this.taskCompletedDate)
      .typeText(this.taskCompletedDate, taskCompleteTo, { paste: true })
      .hover(this.taskAssignee)
      .click(this.taskAssignee)
      .wait(100)
      .hover(selectTaskUser)
      .click(selectTaskUser)
      .wait(500)
      .hover(this.saveTask)
      .click(this.saveTask)
      .wait(1000)
      .expect(viewAddedTask.innerText).contains(taskDescription)
      .wait(1000)
      .hover(this.historyTab)
      .click(this.historyTab)
      .wait(500)
      .expect(viewNewTask.innerText).contains(taskDescription)
      .wait(1000)
      .hover(this.timeSpentTab)
      .click(this.timeSpentTab)
      .wait(500)
      .hover(this.inputTimeSpent)
      .click(this.inputTimeSpent)
      .typeText(this.inputTimeSpent, timeSpent, { replace: true })
      .pressKey('enter')
      .wait(500)
      .hover(this.inputDate)
      .click(this.inputDate)
      .typeText(this.inputDate, date, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .typeText(this.inputNotes, notes, { replace: true })
      .hover(this.saveComment)
      .click(this.saveComment)
      .wait(1000)
      .expect(this.timeSpentEntry.exists).ok('Check the entry added or not.')
      .hover(this.dropDownPriority)
      .click(this.dropDownPriority)
      .wait(1000)
      .hover(this.prioritySelect.withText(editPriority))
      .wait(1000)
      .click(this.prioritySelect.withText(editPriority))
      .wait(1000)
      .expect(viewEditPriority.innerText).contains(editPriority)
      .wait(1000)
      .hover(this.dropDownStatus)
      .click(this.dropDownStatus)
      .wait(1000)
      .hover(this.statusSelect.withText(editStatus))
      .click(this.statusSelect.withText(editStatus))
      .wait(1000)
      .expect(viewEditStatus.innerText).contains(editStatus)
      .wait(1000)
      .hover(removeCreatedBy)
      .click(removeCreatedBy)
      .wait(3000)
      .hover(this.dropDownCreatedBy)
      .click(this.dropDownCreatedBy)
      .wait(2000)
      .hover(this.inputCreatedBy)
      .typeText(this.inputCreatedBy, editCreatedBy, { replace: true })
      .wait(2000)
      .pressKey('enter')
      .wait(1000)
      .hover(removeAssignedTo)
      .click(removeAssignedTo)
      .wait(500)
      .hover(this.notifyPopup)
      .wait(3000)
      .hover(this.dropDownAssignedTo)
      .click(this.dropDownAssignedTo)
      .wait(3000)
      .hover(this.selectMenuAll)
      .click(this.selectMenuAll)
      .wait(500)
      .hover(this.inputAssignedTo)
      .typeText(this.inputAssignedTo, editAssignedTo, { replace: true })
      .wait(2000)
      .pressKey('enter')
      .wait(1000)
      .hover(this.notifyPopup)
      .wait(3000)
      .hover(removeDevice)
      .click(removeDevice)
      .wait(2000)
      .hover(this.dropDownDevice)
      .click(this.dropDownDevice)
      .wait(2000)
      .hover(this.inputDevice)
      .typeText(this.inputDevice, editImpactedDevice, { replace: true })
      .wait(2000)
      .pressKey('enter')
      .wait(2000)
      .hover(this.locField)
      .wait(500)
      .hover(removeLocation)
      .click(removeLocation)
      .wait(2000)
      .hover(this.dropDownLocation)
      .click(this.dropDownLocation)
      .wait(2000)
      .hover(this.selectLocation1.withText(editLocation))
      .wait(500)
      .click(this.selectLocation1.withText(editLocation))
      .wait(1000)
      .hover(viewEditStatus)
      .expect(viewEditStatus.innerText).contains(editStatus)
      .wait(1000)
      .hover(viewEditPriority)
      .expect(viewEditPriority.innerText).contains(editPriority)
      .wait(1000)
      .hover(viewCreatedBy)
      .expect(viewCreatedBy.innerText).contains(editCreatedBy)
      .wait(1000)
      .hover(viewAssignedTo)
      .expect(viewAssignedTo.innerText).contains(editAssignedTo)
      .wait(1000)
      .hover(viewDevice)
      .expect(viewDevice.innerText).contains(editImpactedDevice)
      .wait(1000)
      .hover(viewLocation)
      .expect(viewLocation.innerText).contains(editLocation)
      .wait(2000);
  }

  async editTicketwithCustomForm() {

  }
} export default editTickets;
