import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addlocation from "../company_settings/add_location";
import deleteTicket from "./delete_tickets";

const naviBar = new navbar();
const golVari = new golvar();
const addLocPage = new addlocation();
const delTicPage = new deleteTicket();

class addTicket {

  constructor() {
    this.statusSelector = 'data-tc-ticket-status-dropdown';
    this.addTicketBtn = Selector(`${golVari.horizontalMenu} [data-tc-add="helpdesk"]`);
    this.subjectField = Selector('[data-tc-text-field="subject"] [data-tc-task-value]');
    this.priorityDropDown = Selector('[data-tc-priority-select]');
    this.selectPriority = Selector('[data-tc-priority-options] [data-tc-priority-select-option]');
    this.statusLabel = Selector('[data-tc-check-label="Status"]');
    this.statusDropDown = Selector('[data-tc-ticket-status-dropdown]');
    this.selectStatus = Selector('[data-tc-status-select-option]');
    this.createdByDropDown = Selector('[data-tc-text-field="created_by"]');
    this.selectCreatedBy = this.createdByDropDown.find('li');
    this.assignedToDropDown = Selector('[data-tc-text-field="assigned_to"]');
    this.selectAssignedTo = this.assignedToDropDown.find('li');
    this.createdBysideNav = Selector('[data-tc-require-field="created_by"]');
    this.deviceDropDown = Selector('[data-tc-text-field="impacted_devices"]');
    this.selectDevice = this.deviceDropDown.find('li');
    this.locationDropDown = Selector('[data-tc-text-field="location"]');
    this.selectLocation = this.locationDropDown.find('li');
    this.descriptionField = Selector('[data-tc-trix-editor]');
    this.attachment = Selector('[data-tc-attachment]');
    this.inputFile = Selector('[data-tc-attachment] [data-tc-uploading-input-attachments]');
    this.createTicketBtn = Selector('[data-tc-save-form-btn="Create ticket"]');
    this.viewSubject = Selector('[data-tc-ticket-subject]');
    this.toggleFormBtn = Selector(`${golVari.horizontalMenu} [data-tc-toggle-btn="helpdesk"]`);
    this.viewSelectedTic = Selector('[data-tc-form-drop-down] span');
    this.prioritySideTab = Selector('[data-tc-require-field="priority"]');
    this.statusSideTab = Selector('[data-tc-require-field="status"]');
    this.staffListLabel = Selector('[data-tc-check-label="People List"]');
    this.staffListField = Selector('[data-tc-text-field="people_list"]');
    this.selectStaff = this.staffListField.find('li');
    this.telecomListLabel = Selector('[data-tc-check-label="Telecom List"]');
    this.telecomListField = Selector('[data-tc-text-field="telecom_list"]');
    this.selectTelecom = this.telecomListField.find('li');
    this.contractListLabel = Selector('[data-tc-check-label="Contract List"]');
    this.contractListField = Selector('[data-tc-text-field="contract_list"]');
    this.selectContract = this.contractListField.find('li');
    this.locationListLabel = Selector('[data-tc-check-label="Location List"]');
    this.locationListField = Selector('[data-tc-text-field="location_list"]');
    this.selectLocationList = this.locationListField.find('li');
    this.vendorListLabel = Selector('[data-tc-check-label="Vendor List"]');
    this.vendorListField = Selector('[data-tc-text-field="vendor_list"]');
    this.selectVendor = this.vendorListField.find('li');
    this.assetListLabel = Selector('[data-tc-check-label="Asset List"]');
    this.assetListField = Selector('[data-tc-text-field="asset_list"]');
    this.selectAsset = this.assetListField.find('li');
    this.attachmentLabel = Selector('[data-tc-check-label="Attachments"]');
    this.categoryLabel = Selector('[data-tc-check-label="Category"]');
    this.categoryField = Selector('[data-tc-text-field="category"]');
    this.selectCategory = this.categoryField.find('li');
    this.descriptionLabel = Selector('[data-tc-check-label="Description"]');
    this.newDescField = Selector('[data-tc-text-field="description"] [data-tc-trix-editor]');
    this.viewText = Selector('[data-text-tc="text"]');
    this.viewStaffList = Selector('[data-tc-selected="people_list"]');
    this.viewContractList = Selector('[data-tc-selected="contract_list"]');
    this.viewTelecomList = Selector('[data-tc-selected="telecom_list"]');
    this.viewLocationList = Selector('[data-tc-selected="location_list"]');
    this.viewVendorList = Selector('[data-tc-selected="vendor_list"]');
    this.viewAssetList = Selector('[data-tc-selected="asset_list"]');
    this.phoneLabel = Selector('[data-tc-check-label="Phone"]');
    this.phoneField = Selector('[data-tc-text-field="phone"] input');
    this.viewAssignedTo1 = Selector('[data-tc-selected="assigned_to"]');
    this.closeDatePopup = Selector('[data-tc-dates-input] button');
    this.notifyPop = Selector('.notification-content');
    this.subjectErrorMessage = Selector('[data-tc-field-error-messages="Please enter a Subject."]');
    this.backBtn = Selector('[data-tc-back]');
    this.formDropDown = Selector('[data-tc-form-drop-down]');
    this.selectDifferForm = this.formDropDown.find('li');
    this.viewTicNumber = Selector('[data-tc-ticket-number]');
    this.redirectBackTic = Selector('[data-tc-icon="Back to all tickets"]');
  }

  async addTicketwithSubjectOnly(subject) {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(this.addTicketBtn)
      .click(this.addTicketBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/new?formId=')
      .hover(this.subjectField)
      .typeText(this.subjectField, subject, { paste: true })
      .wait(500)
      .hover(this.createTicketBtn)
      .click(this.createTicketBtn)
      .wait(4000)
  }

  async addTicketWithStatus(subject, status) {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(this.addTicketBtn)
      .click(this.addTicketBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/new?formId=')
      .hover(this.subjectField)
      .typeText(this.subjectField, subject, { paste: true })
      .wait(500)
      .hover(this.statusDropDown)
      .click(this.statusDropDown)
      .wait(500)
      .hover(this.selectStatus.withText(status))
      .click(this.selectStatus.withText(status))
      .wait(500)
      .hover(this.createTicketBtn)
      .click(this.createTicketBtn)
      .wait(4000)
      .hover(this.viewSubject)
      .expect(this.viewSubject.innerText).contains(subject)
      .hover(this.redirectBackTic)
      .click(this.redirectBackTic)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/');
  }

  async addTicketwithBaseForm(subject, priority, status, createdBy, assignedTo, impactedDevice, location, description) {
    const viewPriority = Selector(`[data-tc-view-priority="${priority}"]`);
    const viewStatus = Selector(`[data-tc-view-status="${status}"]`);
    const viewCreatedBy = Selector(`[data-tc-field="created_by"] [data-tc-view-selected="${createdBy}"]`);
    const viewAssignedTo = Selector(`[data-tc-field="assigned_to"] [data-tc-view-selected="${assignedTo}"]`);
    const viewDevice = Selector(`[data-tc-view-selected="${impactedDevice}"]`);
    const viewLocation = Selector(`[data-tc-view-selected="${location}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(this.addTicketBtn)
      .click(this.addTicketBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/new?formId=')
      .hover(this.subjectField)
      .typeText(this.subjectField, subject, { paste: true })
      .wait(500)
      .hover(this.priorityDropDown)
      .click(this.priorityDropDown)
      .wait(500)
      .hover(this.selectPriority.withText(priority))
      .click(this.selectPriority.withText(priority))
      .wait(500)
      .hover(this.statusDropDown)
      .click(this.statusDropDown)
      .wait(500)
      .hover(this.selectStatus.withText(status))
      .click(this.selectStatus.withText(status))
      .wait(500)
      .hover(this.createdByDropDown)
      .click(this.createdByDropDown)
      .wait(500)
      .hover(this.selectCreatedBy.withText(createdBy))
      .click(this.selectCreatedBy.withText(createdBy))
      .wait(500)
      .click(this.createdBysideNav)
      .wait(500)
      .hover(this.assignedToDropDown)
      .click(this.assignedToDropDown)
      .wait(500)
      .hover(this.selectAssignedTo.withText(assignedTo))
      .click(this.selectAssignedTo.withText(assignedTo))
      .wait(500)
      .hover(this.deviceDropDown)
      .click(this.deviceDropDown)
      .wait(500)
      .hover(this.selectDevice.withText(impactedDevice))
      .click(this.selectDevice.withText(impactedDevice))
      .wait(500)
      .hover(this.locationDropDown)
      .click(this.locationDropDown)
      .wait(500)
      .hover(this.selectLocation.withText(location))
      .click(this.selectLocation.withText(location))
      .wait(500)
      .hover(this.descriptionField)
      .click(this.descriptionField)
      .typeText(this.descriptionField, description, { paste: true })
      .wait(500)
      .hover(this.attachment)
      .click(this.attachment)
      .setFilesToUpload(this.inputFile, '../../shared/help_desk/ticketImage.jpg')
      .wait(2000)
      .hover(this.createTicketBtn)
      .click(this.createTicketBtn)
      .wait(4000)
      .hover(this.viewSubject)
      .expect(this.viewSubject.innerText).contains(subject)
      .wait(500)
      .hover(viewStatus)
      .expect(viewStatus.innerText).contains(status)
      .wait(500)
      .hover(viewPriority)
      .expect(viewPriority.innerText).contains(priority)
      .wait(500)
      .hover(viewCreatedBy)
      .expect(viewCreatedBy.innerText).contains(createdBy)
      .wait(500)
      .hover(viewAssignedTo)
      .expect(viewAssignedTo.innerText).contains(assignedTo)
      .wait(500)
      .hover(viewDevice)
      .expect(viewDevice.innerText).contains(impactedDevice)
      .wait(500)
      .hover(viewLocation)
      .expect(viewLocation.innerText).contains(location)
      .wait(1000)
  }

  async addTicThroCustomForm(selectForm, ticSubject, staffUser, selectTelecoms, selectContracts, ticPriority, selectLocs, selectVendors, selectAssets, status1, createdBy1, assignedTo1, textArea, richText, tag1, date1, phoneNumber, list, text1, number, category1, description1) {
    const formOption = Selector(`${golVari.horizontalMenu} [data-tc-form-name="${selectForm}"]`);
    const viewTextArea = Selector(`[data-tc-text-area="${textArea}"]`);
    const viewPriority1 = Selector(`[data-tc-view-priority="${ticPriority}"]`);
    const viewStatus1 = Selector(`[data-tc-view-status="${status1}"]`);
    const viewCreatedBy1 = Selector(`[data-tc-view-selected="${createdBy1}"`);
    const viewNumber1 = Selector(`[data-tc-view-number="${number}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(this.toggleFormBtn)
      .click(this.toggleFormBtn)
      .wait(500)
      .hover(formOption)
      .click(formOption)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/new?formId')
      .wait(500)
      .hover(this.viewSelectedTic)
      .wait(1000)
      .expect(this.viewSelectedTic.innerText).contains(selectForm)
      .wait(500)
      .hover(this.subjectField)
      .typeText(this.subjectField, ticSubject, { paste: true })
      .wait(1000)
      .hover(this.staffListLabel)
      .wait(500)
      .click(this.staffListField)
      .wait(1000)
      .click(this.selectStaff.withText(staffUser))
      .wait(1000)
      .click(this.prioritySideTab)
      .wait(1000)
      .hover(this.priorityDropDown)
      .click(this.priorityDropDown)
      .wait(1000)
      .hover(this.selectPriority.withText(ticPriority))
      .click(this.selectPriority.withText(ticPriority))
      .wait(1000)
      .hover(this.telecomListLabel)
      .wait(500)
      .click(this.telecomListField)
      .wait(1000)
      .click(this.selectTelecom.withText(selectTelecoms))
      .wait(1000)
      .hover(this.contractListLabel)
      .wait(500)
      .click(this.contractListField)
      .wait(1000)
      .click(this.selectContract.withText(selectContracts))
      .wait(1000)
      .hover(this.locationListLabel)
      .wait(500)
      .click(this.locationListField)
      .wait(1000)
      .click(this.selectLocationList.withText(selectLocs))
      .wait(1000)
      .hover(this.vendorListLabel)
      .wait(500)
      .click(this.vendorListField)
      .wait(1000)
      .click(this.selectVendor.withText(selectVendors))
      .wait(1000)
      .hover(this.assetListLabel)
      .wait(500)
      .click(this.assetListField)
      .wait(1000)
      .click(this.selectAsset.withText(selectAssets))
      .wait(1000)
      .click(this.statusSideTab)
      .wait(1000)
      .hover(this.statusLabel)
      .wait(1000)
      .hover(this.statusDropDown)
      .click(this.statusDropDown)
      .wait(1000)
      .hover(this.selectStatus.withText(status1))
      .click(this.selectStatus.withText(status1))
      .wait(1000)
      .click(this.createdBysideNav)
      .wait(1000)
      .hover(this.createdByDropDown)
      .wait(500)
      .click(this.createdByDropDown)
      .hover(this.selectCreatedBy.withText(createdBy1))
      .click(this.selectCreatedBy.withText(createdBy1))
      .wait(1000)
      .hover(this.attachmentLabel)
      .wait(1000)
      .hover(this.attachment)
      .wait(500)
      .click(this.attachment)
      .setFilesToUpload(this.inputFile, '../../shared/help_desk/ticketImage.jpg')
      .wait(1000)
      .hover(addLocPage.textAreaLabel)
      .wait(500)
      .typeText(addLocPage.inputTextArea, textArea, { paste: true })
      .wait(1000)
      .hover(addLocPage.richAreaLabel)
      .wait(1000)
      .typeText(addLocPage.inputRichText, richText, { paste: true })
      .wait(1000)
      .scrollIntoView(addLocPage.richAreaLabel)
      .wait(500)
      .hover(addLocPage.radioBtnLabel)
      .wait(1000)
      .click(addLocPage.checkedRadio)
      .wait(1000)
      .scrollIntoView(addLocPage.checkBoxLabel)
      .wait(500)
      .hover(addLocPage.checkBoxLabel)
      .wait(1000)
      .click(addLocPage.checkedBox)
      .wait(1000)
      .scrollIntoView(addLocPage.tagLabel)
      .wait(500)
      .click(addLocPage.tagField)
      .wait(500)
      .click(addLocPage.selectTag.withText(tag1))
      .wait(1000)
      .typeText(addLocPage.inputDate, date1, { paste: true })
      .wait(1000)
      .click(this.closeDatePopup)
      .wait(1000)
      .hover(this.phoneLabel)
      .wait(500)
      .typeText(this.phoneField, phoneNumber, { paste: true })
      .wait(1000)
      .hover(addLocPage.listLabel)
      .wait(500)
      .click(addLocPage.listDropDown)
      .wait(500)
      .click(addLocPage.selectList.withText(list))
      .wait(1000)
      .hover(addLocPage.numberLabel)
      .wait(500)
      .typeText(addLocPage.inputNumber, number, { paste: true })
      .wait(1000)
      .hover(addLocPage.textLabel)
      .wait(1000)
      .typeText(addLocPage.inputText, text1, { paste: true })
      .wait(1000)
      .hover(this.assignedToDropDown)
      .click(this.assignedToDropDown)
      .wait(1000)
      .hover(this.selectAssignedTo.withText(assignedTo1))
      .click(this.selectAssignedTo.withText(assignedTo1))
      .wait(1000)
      .hover(this.newDescField)
      .wait(500)
      .click(this.newDescField)
      .wait(500)
      .typeText(this.newDescField, description1, { paste: true })
      .wait(1000)
      .hover(this.categoryLabel)
      .wait(500)
      .click(this.categoryField)
      .wait(1000)
      .click(this.selectCategory.withText(category1))
      .wait(1000)
      .hover(this.createTicketBtn)
      .click(this.createTicketBtn)
      .wait(4000)
      .hover(this.viewSubject)
      .expect(this.viewSubject.innerText).contains(ticSubject)
      .wait(1000)
      .hover(viewTextArea)
      .wait(1000)
      .expect(viewTextArea.innerText).contains(textArea)
      .wait(500)
      .hover(this.viewText)
      .wait(500)
      .expect(this.viewText.innerText).contains(text1)
      .hover(this.viewStaffList)
      .wait(500)
      .expect(this.viewStaffList.innerText).contains(staffUser)
      .wait(1000)
      .hover(this.viewContractList)
      .wait(500)
      .expect(this.viewContractList.innerText).contains(selectContracts)
      .wait(1000)
      .hover(this.viewTelecomList)
      .wait(500)
      .expect(this.viewTelecomList.innerText).contains(selectTelecoms)
      .wait(1000)
      .hover(this.viewAssetList)
      .wait(500)
      .expect(this.viewAssetList.innerText).contains(selectAssets)
      .wait(1000)
      .hover(this.viewLocationList)
      .wait(500)
      .expect(this.viewLocationList.innerText).contains(selectLocs)
      .wait(1000)
      .hover(this.viewVendorList)
      .wait(500)
      .expect(this.viewVendorList.innerText).contains(selectVendors)
      .wait(1000)
      .hover(viewStatus1)
      .expect(viewStatus1.innerText).contains(status1)
      .wait(1000)
      .hover(viewPriority1)
      .expect(viewPriority1.innerText).contains(ticPriority)
      .wait(1000)
      .hover(viewCreatedBy1)
      .expect(viewCreatedBy1.innerText).contains(createdBy1)
      .wait(1000)
      .hover(viewNumber1)
      .wait(500)
      .expect(viewNumber1.innerText).contains(number)
      .wait(1000)
      .hover(this.viewAssignedTo1)
      .expect(this.viewAssignedTo1.innerText).contains(assignedTo1)
      .wait(1000)
  }

  async addTicThroCustomForm1(selectForm1, ticSubject1, staffUser1, selectTelecoms1, selectContracts1, ticPriority1, selectLocs1, selectVendors1, selectAssets1, status2, createdBy2, assignedTo2, textArea2, richText2, tag2, date2, phoneNumber2, list2, text2, number2, category2, description2) {
    const viewTextArea = Selector(`[data-tc-text-area="${textArea2}"]`);
    const viewPriority1 = Selector(`[data-tc-view-priority="${ticPriority1}"]`);
    const viewStatus1 = Selector(`[data-tc-view-status="${status2}"]`);
    const viewCreatedBy1 = Selector(`[data-tc-view-selected="${createdBy2}"`);
    const viewNumber1 = Selector(`[data-tc-view-number="${number2}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(this.addTicketBtn)
      .click(this.addTicketBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/new?formId=')
      .wait(1000)
      .hover(this.formDropDown)
      .wait(1000)
      .click(this.formDropDown)
      .wait(1000)
      .hover(this.selectDifferForm.with(selectForm1))
      .wait(1000)
      .click(this.selectDifferForm.withText(selectForm1))
      .wait(1000)
      .hover(this.viewSelectedTic)
      .wait(1000)
      .expect(this.viewSelectedTic.innerText).contains(selectForm1)
      .wait(500)
      .hover(this.subjectField)
      .typeText(this.subjectField, ticSubject1, { paste: true })
      .wait(1000)
      .hover(this.staffListLabel)
      .wait(500)
      .click(this.staffListField)
      .wait(1000)
      .click(this.selectStaff.withText(staffUser1))
      .wait(1000)
      .click(this.prioritySideTab)
      .wait(1000)
      .hover(this.priorityDropDown)
      .click(this.priorityDropDown)
      .wait(1000)
      .hover(this.selectPriority.withText(ticPriority1))
      .click(this.selectPriority.withText(ticPriority1))
      .wait(1000)
      .hover(this.telecomListLabel)
      .wait(500)
      .click(this.telecomListField)
      .wait(1000)
      .click(this.selectTelecom.withText(selectTelecoms1))
      .wait(1000)
      .hover(this.contractListLabel)
      .wait(500)
      .click(this.contractListField)
      .wait(1000)
      .click(this.selectContract.withText(selectContracts1))
      .wait(1000)
      .hover(this.locationListLabel)
      .wait(500)
      .click(this.locationListField)
      .wait(1000)
      .click(this.selectLocationList.withText(selectLocs1))
      .wait(1000)
      .hover(this.vendorListLabel)
      .wait(500)
      .click(this.vendorListField)
      .wait(1000)
      .click(this.selectVendor.withText(selectVendors1))
      .wait(1000)
      .hover(this.assetListLabel)
      .wait(500)
      .click(this.assetListField)
      .wait(1000)
      .click(this.selectAsset.withText(selectAssets1))
      .wait(1000)
      .click(this.statusSideTab)
      .wait(1000)
      .hover(this.statusLabel)
      .wait(1000)
      .hover(this.statusDropDown)
      .click(this.statusDropDown)
      .wait(1000)
      .hover(this.selectStatus.withText(status2))
      .click(this.selectStatus.withText(status2))
      .wait(1000)
      .click(this.createdBysideNav)
      .wait(1000)
      .hover(this.createdByDropDown)
      .wait(500)
      .click(this.createdByDropDown)
      .hover(this.selectCreatedBy.withText(createdBy2))
      .click(this.selectCreatedBy.withText(createdBy2))
      .wait(1000)
      .hover(this.attachmentLabel)
      .wait(1000)
      .hover(this.attachment)
      .wait(500)
      .click(this.attachment)
      .setFilesToUpload(this.inputFile, '../../shared/help_desk/ticketImage.jpg')
      .wait(1000)
      .hover(addLocPage.textAreaLabel)
      .wait(500)
      .typeText(addLocPage.inputTextArea, textArea2, { paste: true })
      .wait(1000)
      .hover(addLocPage.richAreaLabel)
      .wait(1000)
      .typeText(addLocPage.inputRichText, richText2, { paste: true })
      .wait(1000)
      .scrollIntoView(addLocPage.richAreaLabel)
      .wait(500)
      .hover(addLocPage.radioBtnLabel)
      .wait(1000)
      .click(addLocPage.checkedRadio)
      .wait(1000)
      .scrollIntoView(addLocPage.checkBoxLabel)
      .wait(500)
      .hover(addLocPage.checkBoxLabel)
      .wait(1000)
      .click(addLocPage.checkedBox)
      .wait(1000)
      .scrollIntoView(addLocPage.tagLabel)
      .wait(500)
      .click(addLocPage.tagField)
      .wait(500)
      .click(addLocPage.selectTag.withText(tag2))
      .wait(1000)
      .typeText(addLocPage.inputDate, date2, { paste: true })
      .wait(1000)
      .click(this.closeDatePopup)
      .wait(1000)
      .hover(this.phoneLabel)
      .wait(500)
      .typeText(this.phoneField, phoneNumber2, { paste: true })
      .wait(1000)
      .hover(addLocPage.listLabel)
      .wait(500)
      .click(addLocPage.listDropDown)
      .wait(500)
      .click(addLocPage.selectList.withText(list2))
      .wait(1000)
      .hover(addLocPage.numberLabel)
      .wait(500)
      .typeText(addLocPage.inputNumber, number2, { paste: true })
      .wait(1000)
      .hover(addLocPage.textLabel)
      .wait(1000)
      .typeText(addLocPage.inputText, text2, { paste: true })
      .wait(1000)
      .hover(this.assignedToDropDown)
      .click(this.assignedToDropDown)
      .wait(1000)
      .hover(this.selectAssignedTo.withText(assignedTo2))
      .click(this.selectAssignedTo.withText(assignedTo2))
      .wait(1000)
      .hover(this.newDescField)
      .wait(500)
      .click(this.newDescField)
      .wait(500)
      .typeText(this.newDescField, description2, { paste: true })
      .wait(1000)
      .hover(this.categoryLabel)
      .wait(500)
      .click(this.categoryField)
      .wait(1000)
      .click(this.selectCategory.withText(category2))
      .wait(1000)
      .hover(this.createTicketBtn)
      .click(this.createTicketBtn)
      .wait(4000)
      .hover(this.viewSubject)
      .expect(this.viewSubject.innerText).contains(ticSubject1)
      .wait(1000)
      .hover(viewTextArea)
      .wait(1000)
      .expect(viewTextArea.innerText).contains(textArea2)
      .wait(500)
      .hover(this.viewText)
      .wait(500)
      .expect(this.viewText.innerText).contains(text2)
      .hover(this.viewStaffList)
      .wait(500)
      .expect(this.viewStaffList.innerText).contains(staffUser1)
      .wait(1000)
      .hover(this.viewContractList)
      .wait(500)
      .expect(this.viewContractList.innerText).contains(selectContracts1)
      .wait(1000)
      .hover(this.viewTelecomList)
      .wait(500)
      .expect(this.viewTelecomList.innerText).contains(selectTelecoms1)
      .wait(1000)
      .hover(this.viewAssetList)
      .wait(500)
      .expect(this.viewAssetList.innerText).contains(selectAssets1)
      .wait(1000)
      .hover(this.viewLocationList)
      .wait(500)
      .expect(this.viewLocationList.innerText).contains(selectLocs1)
      .wait(1000)
      .hover(this.viewVendorList)
      .wait(500)
      .expect(this.viewVendorList.innerText).contains(selectVendors1)
      .wait(1000)
      .hover(viewStatus1)
      .expect(viewStatus1.innerText).contains(status2)
      .wait(1000)
      .hover(viewPriority1)
      .expect(viewPriority1.innerText).contains(ticPriority1)
      .wait(1000)
      .hover(viewCreatedBy1)
      .expect(viewCreatedBy1.innerText).contains(createdBy2)
      .wait(1000)
      .hover(viewNumber1)
      .wait(500)
      .expect(viewNumber1.innerText).contains(number2)
      .wait(1000)
      .hover(this.viewAssignedTo1)
      .expect(this.viewAssignedTo1.innerText).contains(assignedTo2)
      .wait(1000)
  }

  async addTicWithoutSubject(priority0, status0, createdBy0, assignedTo0, impactedDevice0, location0, description0) {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(this.addTicketBtn)
      .click(this.addTicketBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/new?formId=')
      .hover(this.priorityDropDown)
      .click(this.priorityDropDown)
      .wait(1000)
      .hover(this.selectPriority.withText(priority0))
      .click(this.selectPriority.withText(priority0))
      .wait(1000)
      .hover(this.statusDropDown)
      .click(this.statusDropDown)
      .wait(1000)
      .hover(this.selectStatus.withText(status0))
      .click(this.selectStatus.withText(status0))
      .wait(1000)
      .hover(this.createdByDropDown)
      .click(this.createdByDropDown)
      .hover(this.selectCreatedBy.withText(createdBy0))
      .click(this.selectCreatedBy.withText(createdBy0))
      .wait(1000)
      .click(this.createdBysideNav)
      .wait(1000)
      .hover(this.assignedToDropDown)
      .click(this.assignedToDropDown)
      .wait(1000)
      .hover(this.selectAssignedTo.withText(assignedTo0))
      .click(this.selectAssignedTo.withText(assignedTo0))
      .wait(1000)
      .hover(this.deviceDropDown)
      .click(this.deviceDropDown)
      .wait(1000)
      .hover(this.selectDevice.withText(impactedDevice0))
      .click(this.selectDevice.withText(impactedDevice0))
      .wait(1000)
      .hover(this.locationDropDown)
      .click(this.locationDropDown)
      .wait(1000)
      .hover(this.selectLocation.withText(location0))
      .click(this.selectLocation.withText(location0))
      .wait(1000)
      .hover(this.descriptionField)
      .click(this.descriptionField)
      .typeText(this.descriptionField, description0, { paste: true })
      .wait(1000)
      .hover(this.attachment)
      .click(this.attachment)
      .setFilesToUpload(this.inputFile, '../../shared/help_desk/ticketImage.jpg')
      .wait(1000)
      .hover(this.createTicketBtn)
      .click(this.createTicketBtn)
      .wait(1000)
      .hover(this.notifyPop)
      .expect(this.notifyPop.innerText).contains('Please correct the errors before saving.')
      .wait(1000)
      .hover(this.subjectErrorMessage)
      .wait(1000)
      .expect(this.subjectErrorMessage.innerText).contains('Please enter a Subject.')
      .wait(1000)
  }

  async navigateBackToTicTab() {
    const viewindexPage = Selector(`${golVari.horizontalMenu} [data-tc-crumb-name="Help Tickets"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(this.addTicketBtn)
      .click(this.addTicketBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/new?formId=')
      .wait(1000)
      .hover(this.backBtn)
      .wait(1000)
      .click(this.backBtn)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(500)
      .hover(viewindexPage)
      .wait(1000)
      .expect(viewindexPage.innerText).contains('Help Tickets')
      .wait(2000)
  }

  async viewTicIndex(prefix, index) {
    await t.hover(this.viewTicNumber)
      .expect(this.viewTicNumber.innerText).contains(`#${prefix}-${index}`)
      .wait(500)
  }

  async navigateToTicTab() {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTicPage.ticketTab)
      .click(delTicPage.ticketTab)
      .wait(1000)
  }

  async navigateToTicShowPage(ticket) {
    const desireTic = Selector(`[data-tc-view-subject="${ticket}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTicPage.ticketTab)
      .click(delTicPage.ticketTab)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(500)
      .click(desireTic)
      .wait(1000)
      .expect(this.viewSubject.innerText).contains(ticket)
      .wait(500)
  }
} export default addTicket
