import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addKnowledgeBase from "./add_knowledge_base";

const naviBar = new navbar();
const golVari = new golvar();
const articlePage = new addKnowledgeBase();

class deleteKnowledgeBase {

  constructor() {
    this.deleteBtn = Selector('[data-tc-icon="delete article"]');
    this.articleDelModal = Selector('[data-tc-text="article delete"]');
    this.deleteConfirm = Selector('[data-tc-modal-article] [data-tc-confirm="article delete"]');
    this.noArticleFound = Selector('[data-tc-text="no article found"]');
  }

  async deleteArtilces(articleTitle) {
    const articleBox = Selector(`[data-tc-article="${articleTitle}"]`);
    const viewTitleOnModal = Selector(`[data-tc-title="${articleTitle}"]`);
    await t.hover(naviBar.resourceNavMenu)
      .click(naviBar.resourceNavMenu)
      .wait(500)
      .hover(articlePage.articleNavTab)
      .click(articlePage.articleNavTab)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('help_tickets/articles')
      .wait(1000)
      .hover(articleBox)
      .expect(articleBox.exists).ok()
      .click(articleBox)
      .wait(1000)
      .hover(viewTitleOnModal)
      .expect(viewTitleOnModal.innerText).contains(articleTitle)
      .wait(500)
      .hover(this.deleteBtn)
      .click(this.deleteBtn)
      .wait(1000)
      .hover(this.articleDelModal)
      .expect(this.articleDelModal.innerText).contains('Are you absolutely sure you want to delete this article?')
      .wait(500)
      .hover(this.deleteConfirm)
      .click(this.deleteConfirm)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/articles')
      .wait(1000)
      .expect(articleBox.exists).notOk()
      .typeText(articlePage.searchField, articleTitle, { replace: true })
      .wait(1000)
      .expect(this.noArticleFound.innerText).contains('No article found.')
      .wait(500)
      .click(articlePage.searchField).pressKey('ctrl+a delete')
      .wait(500);
  }
} export default deleteKnowledgeBase
