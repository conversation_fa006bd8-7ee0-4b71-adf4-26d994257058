import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import deleteTicket from "./delete_tickets";
import indexPageAction from "./ticket_index_page_action";

const naviBar = new navbar();
const golVari = new golvar();
const delTic = new deleteTicket();
const indexPage = new indexPageAction();
const mergeTitleSelector = '[data-tc-title="Unmerge ticket"]';


class MergeTickets {

  constructor() {
    this.mergeIcon = Selector('[data-tc-otp="merge tickets"]');
    this.mergeModal = Selector('[data-tc-header="merge tickets"] h2');
    this.disableSaveBtn = Selector('[data-tc-disable-save="true"]');
    this.saveBtn = Selector('[data-tc-save-button="merge tickets"]');
    this.cancelBtn = Selector('[data-tc-cancel-button]');
    this.parentDropDown = Selector('[data-tc-merge-drop-down]');
    this.selectTicket = this.parentDropDown.find('option');
    this.checkBoxComment = Selector('[data-tc-checkbox="comments"]');
    this.checkBoxDesc = Selector('[data-tc-checkbox="description"]');
    this.viewMergedheader = Selector('[data-tc-view-header="merged tickets"]');
    this.notifyPopup = Selector('.notification-content');
    this.unSelectAll = Selector('[data-tc-btn="bulk unselect all"]');
    this.unmergeTitle = Selector(`${mergeTitleSelector} h2`);
    this.confirmUnmergeButton = Selector('[data-tc-button="yes unmerge"]');
    this.viewSubjectTic = Selector('[data-tc-ticket-subject]');
  }

  async ticketMergingWithParent(ticket1, ticket2) {
    const ticCheckBox1 = Selector(`[data-tc-ticket-checkbox="${ticket1}"]`);
    const ticCheckBox2 = Selector(`[data-tc-ticket-checkbox="${ticket2}"]`);
    const viewChildTic = Selector(`[data-tc-view-subject="${ticket2}"]`);
    const selectTicket1 = Selector(`[data-tc-ticket-option="${ticket1}"]`);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(2000)
      .click(ticCheckBox1)
      .wait(500)
      .click(ticCheckBox2)
      .wait(2000)
      .expect(naviBar.ticBulkUpdateBtn.visible).ok()
      .wait(500)
      .click(naviBar.ticBulkUpdateBtn)
      .wait(500)
      .expect(this.mergeIcon.visible).ok()
      .wait(1000)
      .hover(this.mergeIcon)
      .wait(500)
      .click(this.mergeIcon)
      .wait(2000)
      .expect(this.mergeModal.innerText).contains('Merge Tickets')
      .wait(500)
      .click(this.parentDropDown)
      .wait(500)
      .click(selectTicket1)
      .wait(500)
      .click(this.checkBoxComment)
      .wait(500)
      .click(this.checkBoxDesc)
      .wait(1000)
      .click(this.saveBtn)
      .wait(500)
      .expect(this.notifyPopup.innerText).contains('Tickets merged successfully!')
      .wait(1000)
      .expect(ticCheckBox2.exists).notOk()
      .wait(1000)
      .typeText(delTic.searchField, ticket2, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .expect(delTic.viewDelTic.innerText).contains('No tickets present.')
      .wait(1000)
      .typeText(delTic.searchField, ticket1, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(delTic.searchedTicket)
      .expect(delTic.searchedTicket.innerText).contains(ticket1)
      .wait(500)
      .click(delTic.searchedTicket)
      .wait(2000)
      .hover(this.viewMergedheader)
      .wait(500)
      .expect(this.viewMergedheader.innerText).contains('Merged Tickets')
      .wait(1000)
      .expect(viewChildTic.innerText).contains(ticket2)
      .wait(1000)
      .click(indexPage.redirectBackTic)
      .wait(500)
  }

  async ticketMergingWithoutParent(ticket1, ticket2) {
    const ticCheckBox1 = Selector(`[data-tc-ticket-checkbox="${ticket1}"]`);
    const ticCheckBox2 = Selector(`[data-tc-ticket-checkbox="${ticket2}"]`);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(500)
      .click(ticCheckBox1)
      .wait(500)
      .click(ticCheckBox2)
      .wait(2000)
      .expect(naviBar.ticBulkUpdateBtn.visible).ok()
      .wait(500)
      .click(naviBar.ticBulkUpdateBtn)
      .wait(500)
      .expect(this.mergeIcon.visible).ok()
      .wait(1000)
      .hover(this.mergeIcon)
      .wait(500)
      .click(this.mergeIcon)
      .wait(2000)
      .expect(this.mergeModal.innerText).contains('Merge Tickets')
      .wait(1000)
      .expect(this.disableSaveBtn.exists).ok()
      .wait(500)
      .hover(this.disableSaveBtn)
      .wait(500)
      .hover(this.cancelBtn)
      .wait(500)
      .click(this.cancelBtn)
      .wait(1000)
    await t.eval(() => location.reload(true));
  }

  async navigateToMergedTicket(parentTic, childTic) {
    const viewMergedTic = Selector(`[data-tc-view-subject="${childTic}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(2000)
      .typeText(delTic.searchField, parentTic, { replace: true })
      .pressKey('enter')
      .wait(2000)
      .hover(delTic.searchedTicket)
      .expect(delTic.searchedTicket.innerText).contains(parentTic)
      .wait(500)
      .click(delTic.searchedTicket)
      .wait(2000)
      .expect(this.viewMergedheader.innerText).contains('Merged Tickets')
      .wait(1000)
      .expect(viewMergedTic.innerText).contains(childTic)
      .wait(500)
      .click(viewMergedTic)
      .wait(3000)
      .expect(delTic.searchedTicket.innerText).contains(childTic)
      .wait(1000);
  }

  async unmergeChildTicketNCheckRemoveLink(parentTic1, childTic1) {
    const parentSubTic = Selector(`[data-tc-view-subject="${parentTic1}"]`);
    const childSubTic = Selector(`[data-tc-view-subject="${childTic1}"]`);
    const viewChildSubTic = Selector(`${mergeTitleSelector} [data-tc-view-subject="${childTic1}"]`);
    const childMergeIcon = Selector(`[data-tc-ticket="${childTic1}"] [data-tc-icon="unmerge ticket"]`);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(500)
      .click(parentSubTic)
      .wait(1000)
      .expect(this.viewSubjectTic.innerText).contains(parentTic1)
      .wait(500)
      .expect(this.mergeModal.innerText).contains('Merge Tickets')
      .wait(500)
      .expect(childSubTic.innerText).contains(childTic1)
      .hover(childSubTic)
      .click(childMergeIcon)
      .wait(500)
      .expect(this.unmergeTitle.innerText).contains('Unmerge Attached Ticket')
      .hover(viewChildSubTic)
      .expect(viewChildSubTic.innerText).contains(childTic1)
      .wait(1000)
      .hover(this.confirmUnmergeButton)
      .click(this.confirmUnmergeButton)
      .wait(1000)
      .expect(childSubTic.exists).notOk()
      .wait(500)
      .expect(this.viewSubjectTic.innerText).contains(parentTic1)
      .wait(500)
      .click(indexPage.redirectBackTic)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .typeText(delTic.searchField, parentTic1, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(this.viewSubjectTic)
      .expect(this.viewSubjectTic.innerText).contains(parentTic1)
      .wait(500);
  }

  async ticketMerges(parentTicket, childTicket) {
    const ticCheckBox1 = Selector(`[data-tc-ticket-checkbox="${parentTicket}"]`);
    const ticCheckBox2 = Selector(`[data-tc-ticket-checkbox="${childTicket}"]`);
    const viewChildTic = Selector(`[data-tc-view-subject="${childTicket}"]`);
    const selectTicket = Selector(`[data-tc-ticket-option="${parentTicket}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .click(ticCheckBox1)
      .wait(500)
      .click(ticCheckBox2)
      .wait(2000)
      .expect(naviBar.ticBulkUpdateBtn.visible).ok()
      .wait(500)
      .click(naviBar.ticBulkUpdateBtn)
      .wait(500)
      .expect(this.mergeIcon.exists).ok()
      .wait(1000)
      .hover(this.mergeIcon)
      .wait(500)
      .click(this.mergeIcon)
      .wait(2000)
      .expect(this.mergeModal.innerText).contains('Merge Tickets')
      .wait(500)
      .click(this.parentDropDown)
      .wait(500)
      .click(selectTicket)
      .wait(500)
      .click(this.checkBoxComment)
      .wait(500)
      .click(this.checkBoxDesc)
      .wait(1000)
      .click(this.saveBtn)
      .wait(500)
      .expect(this.notifyPopup.innerText).contains('Tickets merged successfully!')
      .wait(1000)
      .click(naviBar.helpdesk)
      .wait(1000)
      .click(delTic.ticketTab)
      .wait(1000)
      .expect(ticCheckBox2.exists).notOk()
      .wait(1000)
      .typeText(delTic.searchField, childTicket, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .expect(delTic.viewDelTic.innerText).contains('No tickets present.')
      .wait(1000)
      .typeText(delTic.searchField, parentTicket, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(delTic.searchedTicket)
      .expect(delTic.searchedTicket.innerText).contains(parentTicket)
      .wait(500)
      .click(delTic.searchedTicket)
      .wait(2000)
      .hover(this.viewMergedheader)
      .wait(500)
      .expect(this.viewMergedheader.innerText).contains('Merged Tickets')
      .wait(1000)
      .expect(viewChildTic.innerText).contains(childTicket)
      .wait(1000);
  }
} export default MergeTickets;
