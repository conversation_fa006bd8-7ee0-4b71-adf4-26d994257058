import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import deletedWorkSpace from './delete_work_space';
import editWorkSpace from './edit_work_space';

const golVari = new golvar();
const naviBar = new navbar();
const delSpacePage = new deletedWorkSpace();
const editSpacePage = new editWorkSpace();

class slaPolicies {

  constructor() {
    this.slaTab = Selector('[data-tc-tab="SLA Policy"]');
    this.checkHeader = Selector('[data-tc-heading="SLA Policies"]');
    this.verifyParagraph = Selector('[data-tc-paragraph="SLA Policies"]');
    this.addBtn = Selector('[data-tc-add-policy]');
    this.addPolicyTitle = Selector('[data-tc-title="new policy"]');
    this.inputName = Selector('[data-tc-policy-name]');
    this.formDropDown = Selector('[data-tc-drop-down="custom form"]');
    this.inputDescription = Selector('[data-tc-field="policy description"]');
    this.addReminderBtn = Selector('[data-tc-link="add reminder"]');
    this.addEscalationBtn = Selector('[data-tc-link="add escalation"]');
    this.addConditionBtn = Selector('[data-tc-link="add condition"]');
    this.conditionMenu = Selector('[data-tc-menu="condition"]');
    this.conditionTypeDropDown = Selector('[data-tc-condition-select]');
    this.conditionValueDropDown = Selector('[data-tc-drop-down="contributor"]');
    this.conditionFormValueDropDown = Selector('[data-tc-drop-down="form field condition"]');
    this.firstRTimeAttribute = '[data-tc-range="first response time"]';
    this.resolutionTimeAttribute = '[data-tc-range="resolution time"]';
    this.reminderMenu = Selector('[data-tc-header-text="Reminder 1"]');
    this.reminderMenu2 = Selector('[data-tc-header-text="Reminder 2"]');
    this.targetDropDown = Selector('[data-tc-select-target]');
    this.approachInField = Selector('[data-tc-main-head="Reminder 1"] [data-tc-field="approaches in"]');
    this.approachInField2 = Selector('[data-tc-main-head="Reminder 2"] [data-tc-field="approaches in"]');
    this.inputEmailSubject = Selector('[data-tc-subject]');
    this.escalateMenu = Selector('[data-tc-header-text="Escalation 1"]');
    this.escalateMenu2 = Selector('[data-tc-header-text="Escalation 2"]');
    this.reminderTaskAssignee = Selector('[data-tc-field="send reminder to"] [data-tc-multi-user-field]');
    this.saveBtn = Selector('[data-tc-save-form-btn="Save policy"]');
    this.editBtn = Selector('[data-tc-save-form-btn="Update policy"]');
    this.deleteBtn = Selector('[data-tc-delete-modal]');
    this.deleteModalText = Selector('[data-tc-modal="policy deletion text"]');
    this.deleteConfirmBtn = Selector('[data-tc-btn="delete policy"]');
  }

  async navigateToSLA() {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(delSpacePage.settingIcon)
      .click(delSpacePage.settingIcon)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/general')
      .hover(this.slaTab)
      .click(this.slaTab)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/sla/policies')
      .expect(this.checkHeader.innerText).eql('SLA Policies')
      .expect(this.verifyParagraph.innerText).eql('Service Level Agreement (SLA) Policies allow companies to set time limits for ticket actions according to the priority of their corresponding custom forms.')
  }

  async addNewPolicy(name, form, description, conditionType, conditionTypeValue, priority1Values
    , priority2Values, priority3Values, target, approachesIn, escalateCondition, escalatePeople,
    reminderSubject, escalateSubject, reminderBody, escalateBody) {

    const policyName = Selector(`[data-tc-policy-name="${name}"]`);
    const selectForm = this.formDropDown.find('li').withText(form);
    const selectConditionType = this.conditionTypeDropDown.find('li').withText(conditionType);
    const selectConditionValue = this.conditionValueDropDown.find('li').withText(conditionTypeValue);
    const [priority1, priority1FRT, priority1RT, priority1Oprt] = priority1Values.split(':');
    const [priority1FRTDays, priority1FRTHours, priority1FRTMinutes] = priority1FRT.split(';');
    const [priority1RTDays, priority1RTHours, priority1RTMinutes] = priority1RT.split(';');
    const [priority2, priority2FRT, priority2RT, priority2Oprt] = priority2Values.split(':');
    const [priority2FRTDays, priority2FRTHours, priority2FRTMinutes] = priority2FRT.split(';');
    const [priority2RTDays, priority2RTHours, priority2RTMinutes] = priority2RT.split(';');
    const [priority3, priority3FRT, priority3RT, priority3Oprt] = priority3Values.split(':');
    const [priority3FRTDays, priority3FRTHours, priority3FRTMinutes] = priority3FRT.split(';');
    const [priority3RTDays, priority3RTHours, priority3RTMinutes] = priority3RT.split(';');
    const priority1ColSelector = Selector(`[data-tc-prority-col="${priority1}"]`);
    const viewPriority1 = Selector(`[data-tc-prority="${priority1}"]`);
    const priority2ColSelector = Selector(`[data-tc-prority-col="${priority2}"]`);
    const viewPriority2 = Selector(`[data-tc-prority="${priority2}"]`);
    const priority3ColSelector = Selector(`[data-tc-prority-col="${priority3}"]`);
    const viewPriority3 = Selector(`[data-tc-prority="${priority3}"]`);
    const firstRTimeInput1 = priority1ColSelector.find(`${this.firstRTimeAttribute} [data-tc-any-value]`);
    const priority1FRTDay = priority1ColSelector.find(`${this.firstRTimeAttribute} [data-tc-days]`);
    const priority1FRTHour = priority1ColSelector.find(`${this.firstRTimeAttribute} [data-tc-minutes]`);
    const priority1FRTMintues = priority1ColSelector.find(`${this.firstRTimeAttribute} [data-tc-hours]`);
    const priorityResolTimeInput1 = priority1ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-any-value]`);
    const priority1ResolTDay = priority1ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-days]`);
    const priority1ResolTHour = priority1ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-minutes]`);
    const priority1ResolTMintues = priority1ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-hours]`);
    const priority1OpHours = priority1ColSelector.find('[data-tc-select-operational-hours]');
    const selectPriorit1OpHours = priority1ColSelector.find('li').withText(priority1Oprt);
    const firstRTimeInput2 = priority2ColSelector.find(`${this.firstRTimeAttribute} [data-tc-any-value]`);
    const priority2FRTDay = priority2ColSelector.find(`${this.firstRTimeAttribute} [data-tc-days]`);
    const priority2FRTHour = priority2ColSelector.find(`${this.firstRTimeAttribute} [data-tc-minutes]`);
    const priority2FRTMintues = priority2ColSelector.find(`${this.firstRTimeAttribute} [data-tc-hours]`);
    const priorityResolTimeInput2 = priority2ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-any-value]`);
    const priority2ResolTDay = priority2ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-days]`);
    const priority2ResolTHour = priority2ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-minutes]`);
    const priority2ResolTMintues = priority2ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-hours]`);
    const priority2OpHours = priority2ColSelector.find('[data-tc-select-operational-hours]');
    const selectPriorit2OpHours = priority2OpHours.find('li').withText(priority2Oprt);
    const firstRTimeInput3 = priority3ColSelector.find(`${this.firstRTimeAttribute} [data-tc-any-value]`);
    const priority3FRTDay = priority3ColSelector.find(`${this.firstRTimeAttribute} [data-tc-days]`);
    const priority3FRTHour = priority3ColSelector.find(`${this.firstRTimeAttribute} [data-tc-minutes]`);
    const priority3FRTMintues = priority3ColSelector.find(`${this.firstRTimeAttribute} [data-tc-hours]`);
    const priorityResolTimeInput3 = priority3ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-any-value]`);
    const priority3ResolTDay = priority3ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-days]`);
    const priority3ResolTHour = priority3ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-minutes]`);
    const priority3ResolTMintues = priority3ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-hours]`);
    const priority3OpHours = priority3ColSelector.find('[data-tc-select-operational-hours]');
    const selectPriorit3OpHours = priority3OpHours.find('li').withText(priority3Oprt);
    const selectTarget = this.targetDropDown.find('li').withText(target);
    const [approachHour, approachMinutes] = approachesIn.split(':');
    const inputApproachHours = this.approachInField.find('.hours li').withText(approachHour);
    const inputApproachMintues = this.approachInField.find('.minutes li').withText(approachMinutes);
    const selectEscalateCondition = this.targetDropDown.find('li').withText(escalateCondition);
    const selectReminderMember = this.reminderTaskAssignee.find(`[data-tc-selected-object="${conditionTypeValue}"]`);
    const selectEscalateMember = this.reminderTaskAssignee.find(`[data-tc-selected-object="${escalatePeople}"]`);
    const reminderBodyInput = Selector(`[data-tc-main-head="${reminderSubject}"] trix-editor`);
    const escalateBodyInput = Selector(`[data-tc-main-head="${escalateSubject}"] trix-editor`);

    await t.expect(golVari.currentpageurl()).contains('help_tickets/settings/sla/policies')
      .hover(this.addBtn)
      .click(this.addBtn)
      .wait(500)
      .expect(this.addPolicyTitle.innerText).eql('New Policy')
      .typeText(this.inputName, name, { paste: true })
      .hover(this.formDropDown)
      .click(this.formDropDown)
      .wait(500)
      .hover(selectForm)
      .click(selectForm)
      .wait(500)
      .typeText(this.inputDescription, description, { paste: true })
      .hover(this.addConditionBtn)
      .click(this.addConditionBtn)
      .wait(500)
      .hover(this.conditionMenu)
      .click(this.conditionMenu)
      .wait(500)
      .hover(this.conditionTypeDropDown)
      .click(this.conditionTypeDropDown)
      .hover(selectConditionType)
      .click(selectConditionType)
      .wait(500)
      .hover(this.conditionValueDropDown)
      .click(this.conditionValueDropDown)
      .wait(500)
      .hover(selectConditionValue)
      .click(selectConditionValue)
      .wait(500)
      .hover(priority1ColSelector)
      .expect(viewPriority1.innerText).eql(priority1)
      .hover(firstRTimeInput1)
      .click(firstRTimeInput1)
      .wait(200)
      .typeText(priority1FRTDay, priority1FRTDays, { replace: true })
      .typeText(priority1FRTHour, priority1FRTHours, { replace: true })
      .typeText(priority1FRTMintues, priority1FRTMinutes, { replace: true })
      .wait(500)
      .hover(priorityResolTimeInput1)
      .click(priorityResolTimeInput1)
      .wait(200)
      .typeText(priority1ResolTDay, priority1RTDays, { replace: true })
      .typeText(priority1ResolTHour, priority1RTHours, { replace: true })
      .typeText(priority1ResolTMintues, priority1RTMinutes, { replace: true })
      .wait(500)
      .hover(priority1OpHours)
      .click(priority1OpHours)
      .wait(200)
      .click(selectPriorit1OpHours)
      .wait(500)
      .hover(priority2ColSelector)
      .expect(viewPriority2.innerText).eql(priority2)
      .hover(firstRTimeInput2)
      .click(firstRTimeInput2)
      .wait(200)
      .typeText(priority2FRTDay, priority2FRTDays, { replace: true })
      .typeText(priority2FRTHour, priority2FRTHours, { replace: true })
      .typeText(priority2FRTMintues, priority2FRTMinutes, { replace: true })
      .wait(500)
      .hover(priorityResolTimeInput2)
      .click(priorityResolTimeInput2)
      .wait(200)
      .typeText(priority2ResolTDay, priority2RTDays, { replace: true })
      .typeText(priority2ResolTHour, priority2RTHours, { replace: true })
      .typeText(priority2ResolTMintues, priority2RTMinutes, { replace: true })
      .wait(500)
      .hover(priority2OpHours)
      .click(priority2OpHours)
      .wait(200)
      .click(selectPriorit2OpHours)
      .wait(500)
      .hover(priority3ColSelector)
      .expect(viewPriority3.innerText).eql(priority3)
      .hover(firstRTimeInput3)
      .click(firstRTimeInput3)
      .wait(200)
      .typeText(priority3FRTDay, priority3FRTDays, { replace: true })
      .typeText(priority3FRTHour, priority3FRTHours, { replace: true })
      .typeText(priority3FRTMintues, priority3FRTMinutes, { replace: true })
      .wait(500)
      .hover(priorityResolTimeInput3)
      .click(priorityResolTimeInput3)
      .wait(200)
      .typeText(priority3ResolTDay, priority3RTDays, { replace: true })
      .typeText(priority3ResolTHour, priority3RTHours, { replace: true })
      .typeText(priority3ResolTMintues, priority3RTMinutes, { replace: true })
      .wait(500)
      .hover(priority3OpHours)
      .click(priority3OpHours)
      .wait(200)
      .click(selectPriorit3OpHours)
      .wait(500)
      .hover(this.reminderMenu)
      .click(this.reminderMenu)
      .wait(500)
      .hover(this.targetDropDown)
      .click(this.targetDropDown)
      .wait(200)
      .click(selectTarget)
      .wait(500)
      .hover(this.approachInField)
      .click(this.approachInField)
      .wait(200)
      .click(inputApproachHours)
      .wait(500)
      .click(inputApproachMintues)
      .wait(1000)
      .click(this.approachInField)
      .wait(200)
      .hover(this.reminderTaskAssignee)
      .click(this.reminderTaskAssignee)
      .wait(500)
      .click(selectReminderMember)
      .wait(500)
      .hover(this.inputEmailSubject)
      .typeText(this.inputEmailSubject, reminderSubject, { paste: true })
      .wait(500)
      .click(reminderBodyInput)
      .typeText(reminderBodyInput, reminderBody, { paste: true })
      .wait(200)
      .hover(this.addEscalationBtn)
      .click(this.addEscalationBtn)
      .wait(500)
      .hover(this.escalateMenu)
      .click(this.escalateMenu)
      .wait(200)
      .hover(this.targetDropDown)
      .click(this.targetDropDown)
      .wait(200)
      .click(selectEscalateCondition)
      .wait(200)
      .hover(this.reminderTaskAssignee)
      .click(this.reminderTaskAssignee)
      .wait(500)
      .click(selectEscalateMember)
      .wait(500)
      .hover(this.inputEmailSubject)
      .typeText(this.inputEmailSubject, escalateSubject, { paste: true })
      .wait(500)
      .hover(escalateBodyInput)
      .typeText(escalateBodyInput, escalateBody, { paste: true })
      .wait(500)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/sla/policies')
      .hover(policyName)
      .expect(policyName.innerText).eql(name)
      .wait(500);
  }

  async addNewPolicyWithCustomForm(name, form, description, conditionType, conditionTypeValue, priority1Values
    , priority2Values, priority3Values, priority4Values, priority5Values, priority6Values, target, approachesIn,
    escalateCondition, escalatePeople, reminderSubject, escalateSubject, reminderBody, escalateBody) {

    const policyName = Selector(`[data-tc-policy-name="${name}"]`);
    const selectForm = this.formDropDown.find('li').withText(form);
    const selectConditionType = this.conditionTypeDropDown.find('li').withText(conditionType);
    const selectConditionValue = this.conditionValueDropDown.find('li').withText(conditionTypeValue);
    const [priority1, priority1FRT, priority1RT, priority1Oprt] = priority1Values.split(':');
    const [priority1FRTDays, priority1FRTHours, priority1FRTMinutes] = priority1FRT.split(';');
    const [priority1RTDays, priority1RTHours, priority1RTMinutes] = priority1RT.split(';');
    const [priority2, priority2FRT, priority2RT, priority2Oprt] = priority2Values.split(':');
    const [priority2FRTDays, priority2FRTHours, priority2FRTMinutes] = priority2FRT.split(';');
    const [priority2RTDays, priority2RTHours, priority2RTMinutes] = priority2RT.split(';');
    const [priority3, priority3FRT, priority3RT, priority3Oprt] = priority3Values.split(':');
    const [priority3FRTDays, priority3FRTHours, priority3FRTMinutes] = priority3FRT.split(';');
    const [priority3RTDays, priority3RTHours, priority3RTMinutes] = priority3RT.split(';');
    const [priority4, priority4FRT, priority4RT, priority4Oprt] = priority4Values.split(':');
    const [priority4FRTDays, priority4FRTHours, priority4FRTMinutes] = priority4FRT.split(';');
    const [priority4RTDays, priority4RTHours, priority4RTMinutes] = priority4RT.split(';');
    const [priority5, priority5FRT, priority5RT, priority5Oprt] = priority5Values.split(':');
    const [priority5FRTDays, priority5FRTHours, priority5FRTMinutes] = priority5FRT.split(';');
    const [priority5RTDays, priority5RTHours, priority5RTMinutes] = priority5RT.split(';');
    const [priority6, priority6FRT, priority6RT, priority6Oprt] = priority6Values.split(':');
    const [priority6FRTDays, priority6FRTHours, priority6FRTMinutes] = priority6FRT.split(';');
    const [priority6RTDays, priority6RTHours, priority6RTMinutes] = priority6RT.split(';');
    const priority1ColSelector = Selector(`[data-tc-prority-col="${priority1}"]`);
    const viewPriority1 = Selector(`[data-tc-prority="${priority1}"]`);
    const priority2ColSelector = Selector(`[data-tc-prority-col="${priority2}"]`);
    const viewPriority2 = Selector(`[data-tc-prority="${priority2}"]`);
    const priority3ColSelector = Selector(`[data-tc-prority-col="${priority3}"]`);
    const viewPriority3 = Selector(`[data-tc-prority="${priority3}"]`);
    const viewPriority4 = Selector(`[data-tc-prority="${priority4}"]`);
    const priority4ColSelector = Selector(`[data-tc-prority-col="${priority4}"]`);
    const viewPriority5 = Selector(`[data-tc-prority="${priority5}"]`);
    const priority5ColSelector = Selector(`[data-tc-prority-col="${priority5}"]`);
    const viewPriority6 = Selector(`[data-tc-prority="${priority6}"]`);
    const priority6ColSelector = Selector(`[data-tc-prority-col="${priority6}"]`);
    const firstRTimeInput1 = priority1ColSelector.find(`${this.firstRTimeAttribute} [data-tc-any-value]`);
    const priority1FRTDay = priority1ColSelector.find(`${this.firstRTimeAttribute} [data-tc-days]`);
    const priority1FRTHour = priority1ColSelector.find(`${this.firstRTimeAttribute} [data-tc-minutes]`);
    const priority1FRTMintues = priority1ColSelector.find(`${this.firstRTimeAttribute} [data-tc-hours]`);
    const priorityResolTimeInput1 = priority1ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-any-value]`);
    const priority1ResolTDay = priority1ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-days]`);
    const priority1ResolTHour = priority1ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-minutes]`);
    const priority1ResolTMintues = priority1ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-hours]`);
    const priority1OpHours = priority1ColSelector.find('[data-tc-select-operational-hours]');
    const selectPriorit1OpHours = priority1ColSelector.find('li').withText(priority1Oprt);
    const firstRTimeInput2 = priority2ColSelector.find(`${this.firstRTimeAttribute} [data-tc-any-value]`);
    const priority2FRTDay = priority2ColSelector.find(`${this.firstRTimeAttribute} [data-tc-days]`);
    const priority2FRTHour = priority2ColSelector.find(`${this.firstRTimeAttribute} [data-tc-minutes]`);
    const priority2FRTMintues = priority2ColSelector.find(`${this.firstRTimeAttribute} [data-tc-hours]`);
    const priorityResolTimeInput2 = priority2ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-any-value]`);
    const priority2ResolTDay = priority2ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-days]`);
    const priority2ResolTHour = priority2ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-minutes]`);
    const priority2ResolTMintues = priority2ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-hours]`);
    const priority2OpHours = priority2ColSelector.find('[data-tc-select-operational-hours]');
    const selectPriorit2OpHours = priority2OpHours.find('li').withText(priority2Oprt);
    const firstRTimeInput3 = priority3ColSelector.find(`${this.firstRTimeAttribute} [data-tc-any-value]`);
    const priority3FRTDay = priority3ColSelector.find(`${this.firstRTimeAttribute} [data-tc-days]`);
    const priority3FRTHour = priority3ColSelector.find(`${this.firstRTimeAttribute} [data-tc-minutes]`);
    const priority3FRTMintues = priority3ColSelector.find(`${this.firstRTimeAttribute} [data-tc-hours]`);
    const priorityResolTimeInput3 = priority3ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-any-value]`);
    const priority3ResolTDay = priority3ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-days]`);
    const priority3ResolTHour = priority3ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-minutes]`);
    const priority3ResolTMintues = priority3ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-hours]`);
    const priority3OpHours = priority3ColSelector.find('[data-tc-select-operational-hours]');
    const selectPriorit3OpHours = priority3OpHours.find('li').withText(priority3Oprt);
    const firstRTimeInput4 = priority4ColSelector.find(`${this.firstRTimeAttribute} [data-tc-any-value]`);
    const priority4FRTDay = priority4ColSelector.find(`${this.firstRTimeAttribute} [data-tc-days]`);
    const priority4FRTHour = priority4ColSelector.find(`${this.firstRTimeAttribute} [data-tc-minutes]`);
    const priority4FRTMintues = priority4ColSelector.find(`${this.firstRTimeAttribute} [data-tc-hours]`);
    const priorityResolTimeInput4 = priority4ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-any-value]`);
    const priority4ResolTDay = priority4ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-days]`);
    const priority4ResolTHour = priority4ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-minutes]`);
    const priority4ResolTMintues = priority4ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-hours]`);
    const priority4OpHours = priority4ColSelector.find('[data-tc-select-operational-hours]');
    const selectPriorit4OpHours = priority4ColSelector.find('li').withText(priority4Oprt);
    const firstRTimeInput5 = priority5ColSelector.find(`${this.firstRTimeAttribute} [data-tc-any-value]`);
    const priority5FRTDay = priority5ColSelector.find(`${this.firstRTimeAttribute} [data-tc-days]`);
    const priority5FRTHour = priority5ColSelector.find(`${this.firstRTimeAttribute} [data-tc-minutes]`);
    const priority5FRTMintues = priority5ColSelector.find(`${this.firstRTimeAttribute} [data-tc-hours]`);
    const priorityResolTimeInput5 = priority5ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-any-value]`);
    const priority5ResolTDay = priority5ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-days]`);
    const priority5ResolTHour = priority5ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-minutes]`);
    const priority5ResolTMintues = priority5ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-hours]`);
    const priority5OpHours = priority5ColSelector.find('[data-tc-select-operational-hours]');
    const selectPriorit5OpHours = priority5ColSelector.find('li').withText(priority5Oprt);
    const firstRTimeInput6 = priority6ColSelector.find(`${this.firstRTimeAttribute} [data-tc-any-value]`);
    const priority6FRTDay = priority6ColSelector.find(`${this.firstRTimeAttribute} [data-tc-days]`);
    const priority6FRTHour = priority6ColSelector.find(`${this.firstRTimeAttribute} [data-tc-minutes]`);
    const priority6FRTMintues = priority6ColSelector.find(`${this.firstRTimeAttribute} [data-tc-hours]`);
    const priorityResolTimeInput6 = priority6ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-any-value]`);
    const priority6ResolTDay = priority6ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-days]`);
    const priority6ResolTHour = priority6ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-minutes]`);
    const priority6ResolTMintues = priority6ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-hours]`);
    const priority6OpHours = priority6ColSelector.find('[data-tc-select-operational-hours]');
    const selectPriorit6OpHours = priority6ColSelector.find('li').withText(priority6Oprt);
    const selectTarget = this.targetDropDown.find('li').withText(target);
    const [approachHour, approachMinutes] = approachesIn.split(':');
    const inputApproachHours = this.approachInField.find('.hours li').withText(approachHour);
    const inputApproachMintues = this.approachInField.find('.minutes li').withText(approachMinutes);
    const selectEscalateCondition = this.targetDropDown.find('li').withText(escalateCondition);
    const selectReminderMember = this.reminderTaskAssignee.find(`[data-tc-selected-object="${conditionTypeValue}"]`);
    const selectEscalateMember = this.reminderTaskAssignee.find(`[data-tc-selected-object="${escalatePeople}"]`);
    const reminderBodyInput = Selector(`[data-tc-main-head="${reminderSubject}"] trix-editor`);
    const escalateBodyInput = Selector(`[data-tc-main-head="${escalateSubject}"] trix-editor`);

    await t.expect(golVari.currentpageurl()).contains('help_tickets/settings/sla/policies')
      .hover(this.addBtn)
      .click(this.addBtn)
      .wait(500)
      .expect(this.addPolicyTitle.innerText).eql('New Policy')
      .typeText(this.inputName, name, { paste: true })
      .hover(this.formDropDown)
      .click(this.formDropDown)
      .wait(500)
      .hover(selectForm)
      .click(selectForm)
      .wait(500)
      .typeText(this.inputDescription, description, { paste: true })
      .hover(this.addConditionBtn)
      .click(this.addConditionBtn)
      .wait(500)
      .hover(this.conditionMenu)
      .click(this.conditionMenu)
      .wait(500)
      .hover(this.conditionTypeDropDown)
      .click(this.conditionTypeDropDown)
      .hover(selectConditionType)
      .click(selectConditionType)
      .wait(500)
      .hover(this.conditionValueDropDown)
      .click(this.conditionValueDropDown)
      .wait(500)
      .hover(selectConditionValue)
      .click(selectConditionValue)
      .wait(500)
      .hover(priority1ColSelector)
      .expect(viewPriority1.innerText).eql(priority1)
      .hover(firstRTimeInput1)
      .click(firstRTimeInput1)
      .wait(200)
      .typeText(priority1FRTDay, priority1FRTDays, { replace: true })
      .typeText(priority1FRTHour, priority1FRTHours, { replace: true })
      .typeText(priority1FRTMintues, priority1FRTMinutes, { replace: true })
      .wait(500)
      .hover(priorityResolTimeInput1)
      .click(priorityResolTimeInput1)
      .wait(200)
      .typeText(priority1ResolTDay, priority1RTDays, { replace: true })
      .typeText(priority1ResolTHour, priority1RTHours, { replace: true })
      .typeText(priority1ResolTMintues, priority1RTMinutes, { replace: true })
      .wait(500)
      .hover(priority1OpHours)
      .click(priority1OpHours)
      .wait(200)
      .click(selectPriorit1OpHours)
      .wait(500)
      .hover(priority2ColSelector)
      .expect(viewPriority2.innerText).eql(priority2)
      .hover(firstRTimeInput2)
      .click(firstRTimeInput2)
      .wait(200)
      .typeText(priority2FRTDay, priority2FRTDays, { replace: true })
      .typeText(priority2FRTHour, priority2FRTHours, { replace: true })
      .typeText(priority2FRTMintues, priority2FRTMinutes, { replace: true })
      .wait(500)
      .hover(priorityResolTimeInput2)
      .click(priorityResolTimeInput2)
      .wait(200)
      .typeText(priority2ResolTDay, priority2RTDays, { replace: true })
      .typeText(priority2ResolTHour, priority2RTHours, { replace: true })
      .typeText(priority2ResolTMintues, priority2RTMinutes, { replace: true })
      .wait(500)
      .hover(priority2OpHours)
      .click(priority2OpHours)
      .wait(200)
      .click(selectPriorit2OpHours)
      .wait(500)
      .hover(priority3ColSelector)
      .expect(viewPriority3.innerText).eql(priority3)
      .hover(firstRTimeInput3)
      .click(firstRTimeInput3)
      .wait(200)
      .typeText(priority3FRTDay, priority3FRTDays, { replace: true })
      .typeText(priority3FRTHour, priority3FRTHours, { replace: true })
      .typeText(priority3FRTMintues, priority3FRTMinutes, { replace: true })
      .wait(500)
      .hover(priorityResolTimeInput3)
      .click(priorityResolTimeInput3)
      .wait(200)
      .typeText(priority3ResolTDay, priority3RTDays, { replace: true })
      .typeText(priority3ResolTHour, priority3RTHours, { replace: true })
      .typeText(priority3ResolTMintues, priority3RTMinutes, { replace: true })
      .wait(500)
      .hover(priority3OpHours)
      .click(priority3OpHours)
      .wait(200)
      .click(selectPriorit3OpHours)
      .wait(500)
      .hover(priority4ColSelector)
      .expect(viewPriority4.innerText).eql(priority4)
      .hover(firstRTimeInput4)
      .click(firstRTimeInput4)
      .wait(200)
      .typeText(priority4FRTDay, priority4FRTDays, { replace: true })
      .typeText(priority4FRTHour, priority4FRTHours, { replace: true })
      .typeText(priority4FRTMintues, priority4FRTMinutes, { replace: true })
      .wait(500)
      .hover(priorityResolTimeInput4)
      .click(priorityResolTimeInput4)
      .wait(200)
      .typeText(priority4ResolTDay, priority4RTDays, { replace: true })
      .typeText(priority4ResolTHour, priority4RTHours, { replace: true })
      .typeText(priority4ResolTMintues, priority4RTMinutes, { replace: true })
      .wait(500)
      .hover(priority4OpHours)
      .click(priority4OpHours)
      .wait(200)
      .click(selectPriorit4OpHours)
      .wait(500)
      .hover(priority5ColSelector)
      .expect(viewPriority5.innerText).eql(priority5)
      .hover(firstRTimeInput5)
      .click(firstRTimeInput5)
      .wait(200)
      .typeText(priority5FRTDay, priority5FRTDays, { replace: true })
      .typeText(priority5FRTHour, priority5FRTHours, { replace: true })
      .typeText(priority5FRTMintues, priority5FRTMinutes, { replace: true })
      .wait(500)
      .hover(priorityResolTimeInput5)
      .click(priorityResolTimeInput5)
      .wait(200)
      .typeText(priority5ResolTDay, priority5RTDays, { replace: true })
      .typeText(priority5ResolTHour, priority5RTHours, { replace: true })
      .typeText(priority5ResolTMintues, priority5RTMinutes, { replace: true })
      .wait(500)
      .hover(priority5OpHours)
      .click(priority5OpHours)
      .wait(200)
      .click(selectPriorit5OpHours)
      .wait(500)
      .hover(priority6ColSelector)
      .expect(viewPriority6.innerText).eql(priority6)
      .hover(firstRTimeInput6)
      .click(firstRTimeInput6)
      .wait(200)
      .typeText(priority6FRTDay, priority6FRTDays, { replace: true })
      .typeText(priority6FRTHour, priority6FRTHours, { replace: true })
      .typeText(priority6FRTMintues, priority6FRTMinutes, { replace: true })
      .wait(500)
      .hover(priorityResolTimeInput6)
      .click(priorityResolTimeInput6)
      .wait(200)
      .typeText(priority6ResolTDay, priority6RTDays, { replace: true })
      .typeText(priority6ResolTHour, priority6RTHours, { replace: true })
      .typeText(priority6ResolTMintues, priority6RTMinutes, { replace: true })
      .wait(500)
      .hover(priority6OpHours)
      .click(priority6OpHours)
      .wait(200)
      .click(selectPriorit6OpHours)
      .wait(500)
      .hover(this.reminderMenu)
      .click(this.reminderMenu)
      .wait(500)
      .hover(this.targetDropDown)
      .click(this.targetDropDown)
      .wait(200)
      .click(selectTarget)
      .wait(500)
      .hover(this.approachInField)
      .click(this.approachInField)
      .wait(200)
      .click(inputApproachHours)
      .wait(500)
      .click(inputApproachMintues)
      .wait(1000)
      .click(this.approachInField)
      .wait(200)
      .hover(this.reminderTaskAssignee)
      .click(this.reminderTaskAssignee)
      .wait(500)
      .click(selectReminderMember)
      .wait(500)
      .hover(this.inputEmailSubject)
      .typeText(this.inputEmailSubject, reminderSubject, { paste: true })
      .wait(500)
      .click(reminderBodyInput)
      .typeText(reminderBodyInput, reminderBody, { paste: true })
      .wait(200)
      .hover(this.addEscalationBtn)
      .click(this.addEscalationBtn)
      .wait(500)
      .hover(this.escalateMenu)
      .click(this.escalateMenu)
      .wait(200)
      .hover(this.targetDropDown)
      .click(this.targetDropDown)
      .wait(200)
      .click(selectEscalateCondition)
      .wait(200)
      .hover(this.reminderTaskAssignee)
      .click(this.reminderTaskAssignee)
      .wait(500)
      .click(selectEscalateMember)
      .wait(500)
      .hover(this.inputEmailSubject)
      .typeText(this.inputEmailSubject, escalateSubject, { paste: true })
      .wait(500)
      .hover(escalateBodyInput)
      .typeText(escalateBodyInput, escalateBody, { paste: true })
      .wait(500)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/sla/policies')
      .hover(policyName)
      .expect(policyName.innerText).eql(name)
      .wait(500);
  }

  async editPolicy(addedName, editName, form, description, conditionType, conditionTypeValue, priority1Values
    , priority2Values, priority3Values, target, approachesIn, escalateCondition, escalatePeople,
    reminderSubject, escalateSubject, reminderBody, escalateBody) {

    const addedPolicyName = Selector(`[data-tc-policy-name="${addedName}"]`);
    const editPolicyName = Selector(`[data-tc-policy-name="${editName}"]`);
    const selectForm = this.formDropDown.find('li').withText(form);
    const selectConditionType = this.conditionTypeDropDown.find('li').withText(conditionType);
    const selectConditionValue = this.conditionValueDropDown.find('li').withText(conditionTypeValue);
    const [priority1, priority1FRT, priority1RT, priority1Oprt] = priority1Values.split(':');
    const [priority1FRTDays, priority1FRTHours, priority1FRTMinutes] = priority1FRT.split(';');
    const [priority1RTDays, priority1RTHours, priority1RTMinutes] = priority1RT.split(';');
    const [priority2, priority2FRT, priority2RT, priority2Oprt] = priority2Values.split(':');
    const [priority2FRTDays, priority2FRTHours, priority2FRTMinutes] = priority2FRT.split(';');
    const [priority2RTDays, priority2RTHours, priority2RTMinutes] = priority2RT.split(';');
    const [priority3, priority3FRT, priority3RT, priority3Oprt] = priority3Values.split(':');
    const [priority3FRTDays, priority3FRTHours, priority3FRTMinutes] = priority3FRT.split(';');
    const [priority3RTDays, priority3RTHours, priority3RTMinutes] = priority3RT.split(';');
    const priority1ColSelector = Selector(`[data-tc-prority-col="${priority1}"]`);
    const viewPriority1 = Selector(`[data-tc-prority="${priority1}"]`);
    const priority2ColSelector = Selector(`[data-tc-prority-col="${priority2}"]`);
    const viewPriority2 = Selector(`[data-tc-prority="${priority2}"]`);
    const priority3ColSelector = Selector(`[data-tc-prority-col="${priority3}"]`);
    const viewPriority3 = Selector(`[data-tc-prority="${priority3}"]`);
    const firstRTimeInput1 = priority1ColSelector.find(`${this.firstRTimeAttribute} [data-tc-any-value]`);
    const priority1FRTDay = priority1ColSelector.find(`${this.firstRTimeAttribute} [data-tc-days]`);
    const priority1FRTHour = priority1ColSelector.find(`${this.firstRTimeAttribute} [data-tc-minutes]`);
    const priority1FRTMintues = priority1ColSelector.find(`${this.firstRTimeAttribute} [data-tc-hours]`);
    const priorityResolTimeInput1 = priority1ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-any-value]`);
    const priority1ResolTDay = priority1ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-days]`);
    const priority1ResolTHour = priority1ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-minutes]`);
    const priority1ResolTMintues = priority1ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-hours]`);
    const priority1OpHours = priority1ColSelector.find('[data-tc-select-operational-hours]');
    const selectPriorit1OpHours = priority1ColSelector.find('li').withText(priority1Oprt);
    const firstRTimeInput2 = priority2ColSelector.find(`${this.firstRTimeAttribute} [data-tc-any-value]`);
    const priority2FRTDay = priority2ColSelector.find(`${this.firstRTimeAttribute} [data-tc-days]`);
    const priority2FRTHour = priority2ColSelector.find(`${this.firstRTimeAttribute} [data-tc-minutes]`);
    const priority2FRTMintues = priority2ColSelector.find(`${this.firstRTimeAttribute} [data-tc-hours]`);
    const priorityResolTimeInput2 = priority2ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-any-value]`);
    const priority2ResolTDay = priority2ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-days]`);
    const priority2ResolTHour = priority2ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-minutes]`);
    const priority2ResolTMintues = priority2ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-hours]`);
    const priority2OpHours = priority2ColSelector.find('[data-tc-select-operational-hours]');
    const selectPriorit2OpHours = priority2OpHours.find('li').withText(priority2Oprt);
    const firstRTimeInput3 = priority3ColSelector.find(`${this.firstRTimeAttribute} [data-tc-any-value]`);
    const priority3FRTDay = priority3ColSelector.find(`${this.firstRTimeAttribute} [data-tc-days]`);
    const priority3FRTHour = priority3ColSelector.find(`${this.firstRTimeAttribute} [data-tc-minutes]`);
    const priority3FRTMintues = priority3ColSelector.find(`${this.firstRTimeAttribute} [data-tc-hours]`);
    const priorityResolTimeInput3 = priority3ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-any-value]`);
    const priority3ResolTDay = priority3ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-days]`);
    const priority3ResolTHour = priority3ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-minutes]`);
    const priority3ResolTMintues = priority3ColSelector.find(`${this.resolutionTimeAttribute} [data-tc-hours]`);
    const priority3OpHours = priority3ColSelector.find('[data-tc-select-operational-hours]');
    const selectPriorit3OpHours = priority3OpHours.find('li').withText(priority3Oprt);
    const selectTarget = this.targetDropDown.find('li').withText(target);
    const [approachHour, approachMinutes] = approachesIn.split(':');
    const inputApproachHours = this.approachInField2.find('.hours li').withText(approachHour);
    const inputApproachMintues = this.approachInField2.find('.minutes li').withText(approachMinutes);
    const selectEscalateCondition = this.targetDropDown.find('li').withText(escalateCondition);
    const selectReminderMember = this.reminderTaskAssignee.find(`[data-tc-selected-object="${conditionTypeValue}"]`);
    const selectEscalateMember = this.reminderTaskAssignee.find(`[data-tc-selected-object="${escalatePeople}"]`);
    const reminderBodyInput = Selector(`[data-tc-main-head="${reminderSubject}"] trix-editor`);
    const escalateBodyInput = Selector(`[data-tc-main-head="${escalateSubject}"] trix-editor`);

    await t.expect(golVari.currentpageurl()).contains('help_tickets/settings/sla/policies')
      .hover(addedPolicyName)
      .expect(addedPolicyName.innerText).eql(addedName)
      .click(addedPolicyName)
      .wait(500)
      .expect(this.addPolicyTitle.innerText).eql('Update Policy')
      .typeText(this.inputName, editName, { replace: true })
      .hover(this.formDropDown)
      .click(this.formDropDown)
      .wait(500)
      .typeText(this.inputDescription, description, { replace: true })
      .wait(500)
      .hover(this.conditionMenu)
      .click(this.conditionMenu)
      .wait(500)
      .hover(this.conditionTypeDropDown)
      .click(this.conditionTypeDropDown)
      .hover(selectConditionType)
      .click(selectConditionType)
      .wait(500)
      .hover(this.conditionValueDropDown)
      .click(this.conditionValueDropDown)
      .wait(500)
      .hover(selectConditionValue)
      .click(selectConditionValue)
      .wait(500)
      .hover(priority1ColSelector)
      .expect(viewPriority1.innerText).eql(priority1)
      .hover(firstRTimeInput1)
      .click(firstRTimeInput1)
      .wait(200)
      .typeText(priority1FRTDay, priority1FRTDays, { replace: true })
      .typeText(priority1FRTHour, priority1FRTHours, { replace: true })
      .typeText(priority1FRTMintues, priority1FRTMinutes, { replace: true })
      .wait(500)
      .hover(priorityResolTimeInput1)
      .click(priorityResolTimeInput1)
      .wait(200)
      .typeText(priority1ResolTDay, priority1RTDays, { replace: true })
      .typeText(priority1ResolTHour, priority1RTHours, { replace: true })
      .typeText(priority1ResolTMintues, priority1RTMinutes, { replace: true })
      .wait(500)
      .hover(priority1OpHours)
      .click(priority1OpHours)
      .wait(200)
      .click(selectPriorit1OpHours)
      .wait(500)
      .hover(priority2ColSelector)
      .expect(viewPriority2.innerText).eql(priority2)
      .hover(firstRTimeInput2)
      .click(firstRTimeInput2)
      .wait(200)
      .typeText(priority2FRTDay, priority2FRTDays, { replace: true })
      .typeText(priority2FRTHour, priority2FRTHours, { replace: true })
      .typeText(priority2FRTMintues, priority2FRTMinutes, { replace: true })
      .wait(500)
      .hover(priorityResolTimeInput2)
      .click(priorityResolTimeInput2)
      .wait(200)
      .typeText(priority2ResolTDay, priority2RTDays, { replace: true })
      .typeText(priority2ResolTHour, priority2RTHours, { replace: true })
      .typeText(priority2ResolTMintues, priority2RTMinutes, { replace: true })
      .wait(500)
      .hover(priority2OpHours)
      .click(priority2OpHours)
      .wait(200)
      .click(selectPriorit2OpHours)
      .wait(500)
      .hover(priority3ColSelector)
      .expect(viewPriority3.innerText).eql(priority3)
      .hover(firstRTimeInput3)
      .click(firstRTimeInput3)
      .wait(200)
      .typeText(priority3FRTDay, priority3FRTDays, { replace: true })
      .typeText(priority3FRTHour, priority3FRTHours, { replace: true })
      .typeText(priority3FRTMintues, priority3FRTMinutes, { replace: true })
      .wait(500)
      .hover(priorityResolTimeInput3)
      .click(priorityResolTimeInput3)
      .wait(200)
      .typeText(priority3ResolTDay, priority3RTDays, { replace: true })
      .typeText(priority3ResolTHour, priority3RTHours, { replace: true })
      .typeText(priority3ResolTMintues, priority3RTMinutes, { replace: true })
      .wait(500)
      .hover(priority3OpHours)
      .click(priority3OpHours)
      .wait(200)
      .click(selectPriorit3OpHours)
      .wait(500)
      .hover(this.addReminderBtn)
      .click(this.addReminderBtn)
      .wait(200)
      .hover(this.reminderMenu2)
      .click(this.reminderMenu2)
      .wait(500)
      .hover(this.targetDropDown)
      .click(this.targetDropDown)
      .wait(200)
      .click(selectTarget)
      .wait(500)
      .hover(this.approachInField2)
      .click(this.approachInField2)
      .wait(200)
      .click(inputApproachHours)
      .wait(500)
      .click(inputApproachMintues)
      .wait(1000)
      .click(this.approachInField2)
      .wait(200)
      .hover(this.reminderTaskAssignee)
      .click(this.reminderTaskAssignee)
      .wait(500)
      .click(selectReminderMember)
      .wait(500)
      .hover(this.inputEmailSubject)
      .typeText(this.inputEmailSubject, reminderSubject, { paste: true })
      .wait(500)
      .click(reminderBodyInput)
      .typeText(reminderBodyInput, reminderBody, { paste: true })
      .wait(200)
      .hover(this.addEscalationBtn)
      .click(this.addEscalationBtn)
      .wait(500)
      .hover(this.escalateMenu2)
      .click(this.escalateMenu2)
      .wait(200)
      .hover(this.targetDropDown)
      .click(this.targetDropDown)
      .wait(200)
      .click(selectEscalateCondition)
      .wait(200)
      .hover(this.reminderTaskAssignee)
      .click(this.reminderTaskAssignee)
      .wait(500)
      .click(selectEscalateMember)
      .wait(500)
      .hover(this.inputEmailSubject)
      .typeText(this.inputEmailSubject, escalateSubject, { paste: true })
      .wait(500)
      .hover(escalateBodyInput)
      .typeText(escalateBodyInput, escalateBody, { paste: true })
      .wait(500)
      .hover(this.editBtn)
      .click(this.editBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/sla/policies')
      .hover(editPolicyName)
      .expect(editPolicyName.innerText).eql(editName)
      .wait(500);
  }

  async deletePolicy(name) {
    const policyName = Selector(`[data-tc-policy-name="${name}"]`);
    await t.expect(golVari.currentpageurl()).contains('help_tickets/settings/sla/policies')
      .wait(500)
      .expect(policyName.innerText).eql(name)
      .hover(policyName)
      .click(policyName)
      .wait(500)
      .expect(this.addPolicyTitle.innerText).eql('Update Policy')
      .hover(this.deleteBtn)
      .click(this.deleteBtn)
      .wait(500)
      .hover(this.deleteModalText)
      .expect(this.deleteModalText.innerText).eql(`Are you sure, you want to DELETE policy ${name} ?`)
      .wait(500)
      .hover(this.deleteConfirmBtn)
      .click(this.deleteConfirmBtn)
      .wait(1000)
      .expect(policyName.exists).notOk()
      .wait(500)
  }
} export default slaPolicies
