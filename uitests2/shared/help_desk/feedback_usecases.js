import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addTicket from "./add_tickets";
import indexPageAction from "./ticket_index_page_action";

const naviBar = new navbar();
const golVari = new golvar();
const addTicPage = new addTicket();
const ticIndexPage = new indexPageAction();

class ticketFeedBack {
  constructor() {
    this.feedBackIcon = Selector('[data-tc-label="feedback"]');
    this.feedModalTitle = Selector('[data-tc-title="feedback modal"] h2');
    this.notGoodBtn = Selector('[data-tc-btn="not good"]');
    this.greatBtn = Selector('[data-tc-btn="great"]');
    this.textArea = Selector('[data-tc-field="survey comment"]');
    this.submitSurvey = Selector('[data-tc-btn="submit survey"]');
    this.viewSuccessSurvey = Selector('[data-tc-view="great"]');
    this.viewDangerSurvey = Selector('[data-tc-view="not good"]');
  }

  async provideTicFeedBack(ticSubject, opinion, notes) {
    const ticket = Selector(`[data-tc-ticket="${ticSubject}"]`);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(500)
      .expect(ticket.exists).ok('Check the ticket is present or not.')
      .hover(ticket)
      .click(ticket)
      .wait(1000)
      .hover(addTicPage.viewSubject)
      .expect(addTicPage.viewSubject.innerText).eql(ticSubject)
      .wait(500);
    if (opinion == 'great') {
      await t.hover(this.feedBackIcon)
        .click(this.feedBackIcon)
        .wait(500)
        .expect(this.feedModalTitle.exists).ok('Check modal is open or not.')
        .expect(this.feedModalTitle.innerText).eql('Let us know what you think...')
        .hover(this.greatBtn)
        .click(this.greatBtn)
        .wait(500)
        .typeText(this.textArea, notes, { paste: true })
        .wait(500)
        .hover(this.submitSurvey)
        .click(this.submitSurvey)
        .wait(500)
        .hover(this.viewSuccessSurvey)
        .expect(this.viewSuccessSurvey.innerText).eql(' Great')
        .hover(ticIndexPage.redirectBackTic)
        .click(ticIndexPage.redirectBackTic)
        .wait(1000)
        .expect(golVari.currentpageurl()).contains('/help_tickets/');
    }

    else {
      await t.hover(this.feedBackIcon)
        .click(this.feedBackIcon)
        .wait(500)
        .expect(this.feedModalTitle.exists).ok('Check modal is open or not.')
        .expect(this.feedModalTitle.innerText).eql('Let us know what you think...')
        .hover(this.notGoodBtn)
        .click(this.notGoodBtn)
        .wait(500)
        .typeText(this.textArea, notes, { paste: true })
        .wait(500)
        .hover(this.submitSurvey)
        .click(this.submitSurvey)
        .wait(500)
        .hover(this.viewDangerSurvey)
        .expect(this.viewDangerSurvey.innerText).eql(' Not so good')
        .hover(ticIndexPage.redirectBackTic)
        .click(ticIndexPage.redirectBackTic)
        .wait(1000)
        .expect(golVari.currentpageurl()).contains('/help_tickets/');
    }
  }
}
export default ticketFeedBack;
