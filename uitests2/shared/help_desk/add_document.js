import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addTicket from "./add_tickets";

const naviBar = new navbar();
const golVari = new golvar();
const ticPage = new addTicket();

class addDocument {

  constructor() {
    this.docTab = Selector('[data-tc-sub-menu="document"]');
    this.addDocBtn = Selector(`${golVari.horizontalMenu} [data-tc-add-document]`);
    this.docNameField = Selector('[data-tc-field="document name"]');
    this.saveBtn = Selector('[data-tc-save-form-btn="Save"]');
    this.searchField = Selector('[data-tc-search-document]');
  }

  async addDocuments(docName) {
    const viewDocName = Selector(`[data-tc-view-document="${docName}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(naviBar.resourceNavMenu)
      .click(naviBar.resourceNavMenu)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/articles')
      .wait(500)
      .hover(this.docTab)
      .click(this.docTab)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/documents')
      .hover(this.addDocBtn)
      .click(this.addDocBtn)
      .wait(1000)
      .click(ticPage.attachment)
      .setFilesToUpload(ticPage.inputFile, '../../shared/help_desk/ticketImage.jpg')
      .wait(1000)
      .typeText(this.docNameField, docName, { paste: true })
      .wait(1000)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(2000)
      .hover(this.searchField)
      .typeText(this.searchField, docName, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .expect(viewDocName.innerText).contains(docName)
      .wait(1000)
  }
} export default addDocument
