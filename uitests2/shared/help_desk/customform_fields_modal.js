import { Selector, t } from "testcafe";

class fieldModal {

  constructor() {
    this.nameField = Selector('[data-tc-edit-name]');
    this.labelField = Selector('[data-tc-edit-label]');
    this.typeDropDown = Selector('[data-tc-edit-type]');
    this.typeOption = this.typeDropDown.find('option');
    this.requiredToggle = Selector('[data-tc-edit-required] [data-tc-slide-toggle]');
    this.requiredCloseToggle = Selector('[data-tc-require-close-field] [data-tc-slide-toggle]');
    this.noteField = Selector('[data-tc-input-text-field="Note"]');
    this.defaultValueField = Selector('[data-tc-input-text-field="Default Value"]');
    this.pagePosition = Selector('[data-tc-select-position-value]');
  }

  async basicModal(name, label, type) {
    await t.wait(1000)
      .typeText(this.nameField, name, { replace: true })
      .wait(500)
      .hover(this.labelField)
      .typeText(this.labelField, label, { replace: true })
      .wait(500)
      .hover(this.typeDropDown)
      .click(this.typeDropDown)
      .wait(500)
      .click(this.typeOption.withText(type))
      .wait(1000)
  }

  async notesAndTogglesModal(name1, label1, type1, note1) {
    await t.wait(1000)
    this.basicModal(name1, label1, type1);
    await t.wait(500)
    if (this.noteField.exists) {
      await t.hover(this.noteField)
        .typeText(this.noteField, note1, { replace: true })
        .wait(1000)
        .hover(this.requiredToggle)
        .click(this.requiredToggle)
        .wait(1000)
        .hover(this.requiredCloseToggle)
        .click(this.requiredCloseToggle)
        .wait(1000)
    }
    else {
      await t.wait(1000)
        .hover(this.requiredToggle)
        .click(this.requiredToggle)
        .wait(1000)
        .hover(this.requiredCloseToggle)
        .click(this.requiredCloseToggle)
        .wait(1000)
    }
  }

  async DefaultValueInputModal(name2, label2, type2, note2, defValue, pagePos) {
    await t.wait(1000)
    this.notesAndTogglesModal(name2, label2, type2, note2);
    await t.wait(500)
      .hover(this.defaultValueField)
      .typeText(this.defaultValueField, defValue, { replace: true })
      .wait(1000);
    this.pagePosition(pagePos)
  }

  async DefaultValueSelectModal() {

  }

  // async addOptionModal() {

  // }

  // async prepoluatedDataModal() {

  // }
  // async addTagModal() {

  // }

  // async NameFormatModal() {

  // }

  async pagePosition(pagePtn) {
    const pageSelection = Selector(`[data-tc-select-position="${pagePtn}"]`);
    await t.hover(this.pagePosition)
      .click(this.pagePosition)
      .wait(1000)
      .hover(pageSelection)
      .click(pageSelection)
      .wait(1000)
  }

} export default fieldModal
