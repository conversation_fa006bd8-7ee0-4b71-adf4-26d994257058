import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import deleteTicket from "./delete_tickets";
import filtersMenu from "./filters_menu";
import indexPageAction from "./ticket_index_page_action";

const naviBar = new navbar();
const delTicPage = new deleteTicket();
const filters = new filtersMenu();
const golVari = new golvar();
const indexPage = new indexPageAction();

class bulkUpdate {

  constructor() {
    this.ticBulkUpdateBtn = Selector('[data-tc-btn="bulk update"]');
    this.archiveOption = Selector('[data-tc-otp="archive"]');
    this.mergeOption = Selector('[data-tc-otp="merge tickets"]');
    this.updateStatus = Selector('[data-tc-otp="update status"]');
    this.updateAssignedTo = Selector('[data-tc-otp="update assignment"]');
    this.updateCannedResponse = Selector('[data-tc-otp="add canned response"]');
    this.modalArchiveBtn = Selector('[data-tc-modal-archive-btn]');
    this.ticketIndex = Selector('[data-tc-tickets]');
    this.bulkDelete = Selector('[data-tc-btn="bulk delete"]');
    this.modalDeleteBtn = Selector('[data-tc-modal-delete-btn]');
    this.statusDropdown = Selector('[data-tc-status-field]');
    this.selectOption = this.statusDropdown.find('option');
    this.assignedToDropdown = Selector('[data-tc-assigned-to-dropdown]');
    this.selectAssignedTo = this.assignedToDropdown.find('li');
    this.cannedResponseDropdown = Selector('[data-tc-canned-response-dropdown]');
    this.selectResponse = this.cannedResponseDropdown.find('option');
    this.verfiyStatus = Selector('[data-tc-ticket-status-dropdown]');
    this.bulkStatusUpdate = Selector('[data-tc-bulk-update-status]');
    this.bulkUpdateCannedResponse = Selector('[data-tc-bulk-update-canned-response]');
    this.commentDelIcon = Selector('[data-tc-delete-comment-icon]');
    this.bulkAssignUpdate = Selector('[data-tc-update-assign-to-modal]');
    this.random = Selector('[data-tc-tab="horizontal"]');
    this.mergeDropdown = Selector('[data-tc-merge-drop-down]');
    this.saveMerge = Selector('[data-tc-save-button="merge tickets"]');
    this.verifyMerge = Selector('[data-tc-view-header="merged tickets"]');
    this.quicklyTitle = Selector('[data-tc-title="quickly edit"]');
  }

  async bulkCannedResponse(ticket1, ticket2, response, subject) {
    const checkBox = Selector(`[data-tc-ticket-checkbox="${ticket1}"]`);
    const checkBox2 = Selector(`[data-tc-ticket-checkbox="${ticket2}"]`);
    const ticket = Selector(`[data-tc-ticket="${subject}"]`);
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(delTicPage.ticketTab)
      .click(delTicPage.ticketTab)
      .wait(500)
      .hover(indexPage.gridToggleIcon)
      .click(indexPage.gridToggleIcon)
      .wait(500)
      .hover(checkBox)
      .click(checkBox)
      .wait(500)
      .hover(checkBox2)
      .click(checkBox2)
      .expect(this.ticBulkUpdateBtn.exists).ok()
      .hover(this.ticBulkUpdateBtn)
      .click(this.ticBulkUpdateBtn)
      .wait(500)
      .hover(this.updateCannedResponse)
      .click(this.updateCannedResponse)
      .wait(500)
      .hover(this.cannedResponseDropdown)
      .click(this.cannedResponseDropdown)
      .wait(500)
      .click(this.selectResponse.withText(response))
      .wait(500)
      .hover(this.bulkUpdateCannedResponse)
      .click(this.bulkUpdateCannedResponse)
      .wait(500)
      .hover(ticket)
      .click(ticket)
      .wait(2000)
      .expect(this.commentDelIcon.exists).ok();
  }

  async bulkArchive(ticket1, ticket2) {
    const checkBox = Selector(`[data-tc-ticket-checkbox="${ticket1}"]`);
    const checkBox2 = Selector(`[data-tc-ticket-checkbox="${ticket2}"]`);
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(delTicPage.ticketTab)
      .click(delTicPage.ticketTab)
      .wait(500)
      .hover(indexPage.gridToggleIcon)
      .click(indexPage.gridToggleIcon)
      .wait(500)
      .hover(checkBox)
      .click(checkBox)
      .wait(500)
      .hover(checkBox2)
      .click(checkBox2)
      .expect(this.ticBulkUpdateBtn.exists).ok()
      .hover(this.ticBulkUpdateBtn)
      .click(this.ticBulkUpdateBtn)
      .expect(this.archiveOption.exists).ok()
      .hover(this.archiveOption)
      .click(this.archiveOption)
      .wait(1000)
      .hover(this.modalArchiveBtn)
      .click(this.modalArchiveBtn)
      .wait(500)
      .hover(filters.filterBtn)
      .click(filters.filterBtn)
      .wait(1000)
      .hover(filters.archivedStatus)
      .click(filters.archivedStatus)
      .wait(500)
      .expect(checkBox.exists).ok();
  }

  async bulkAssignment(ticket1, ticket2, assignedTo) {
    const checkBox = Selector(`[data-tc-ticket-checkbox="${ticket1}"]`);
    const checkBox2 = Selector(`[data-tc-ticket-checkbox="${ticket2}"]`);
    const selectAssignedUser = Selector(`[data-tc-selected-object="${assignedTo}"]`);
    const ticketA = Selector(`[data-tc-ticket="${ticket1}"]`);
    const ticketB = Selector(`[data-tc-ticket="${ticket2}"]`);
    const viewSelectedAssignedTo = Selector(`[data-tc-view-selected="${assignedTo}"] [data-tc-selected="assigned_to"]`);

    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(delTicPage.ticketTab)
      .click(delTicPage.ticketTab)
      .wait(500)
      .hover(indexPage.gridToggleIcon)
      .click(indexPage.gridToggleIcon)
      .wait(500)
      .hover(checkBox)
      .click(checkBox)
      .wait(500)
      .hover(checkBox2)
      .click(checkBox2)
      .wait(500)
      .expect(this.ticBulkUpdateBtn.exists).ok()
      .hover(this.ticBulkUpdateBtn)
      .click(this.ticBulkUpdateBtn)
      .wait(500)
      .hover(this.updateAssignedTo)
      .click(this.updateAssignedTo)
      .wait(1000)
      .expect(this.quicklyTitle.visible).ok()
      .hover(this.assignedToDropdown)
      .click(this.assignedToDropdown)
      .wait(1000)
      .click(selectAssignedUser)
      .wait(500)
      .click(this.quicklyTitle)
      .wait(500)
      .hover(this.bulkAssignUpdate)
      .click(this.bulkAssignUpdate)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets')
      .click(ticketA)
      .wait(1000)
      .expect(viewSelectedAssignedTo.innerText).contains(assignedTo)
      .click(indexPage.redirectBackTic)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets')
      .click(ticketB)
      .wait(1000)
      .expect(viewSelectedAssignedTo.innerText).contains(assignedTo)
      .click(indexPage.redirectBackTic);
  }

  async bulkStatus(ticket1, ticket2, status, statusval) {
    const checkBox = Selector(`[data-tc-ticket-checkbox="${ticket1}"]`);
    const checkBox2 = Selector(`[data-tc-ticket-checkbox="${ticket2}"]`);
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(delTicPage.ticketTab)
      .click(delTicPage.ticketTab)
      .wait(500)
      .hover(indexPage.gridToggleIcon)
      .click(indexPage.gridToggleIcon)
      .wait(500)
      .hover(checkBox)
      .click(checkBox)
      .wait(500)
      .hover(checkBox2)
      .click(checkBox2)
      .wait(500)
      .hover(this.ticBulkUpdateBtn)
      .click(this.ticBulkUpdateBtn)
      .wait(500)
      .hover(this.updateStatus)
      .click(this.updateStatus)
      .wait(500)
      .hover(this.statusDropdown)
      .click(this.statusDropdown)
      .wait(500)
      .click(this.selectOption.withText(status))
      .wait(500)
      .hover(this.bulkStatusUpdate)
      .click(this.bulkStatusUpdate)
      .wait(1000)
      .expect(this.verfiyStatus.innerText).contains(statusval);
  }


  async deleteBulk(ticket1, ticket2) {
    const checkBox = Selector(`[data-tc-ticket-checkbox="${ticket1}"]`);
    const checkBox2 = Selector(`[data-tc-ticket-checkbox="${ticket2}"]`);
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(delTicPage.ticketTab)
      .click(delTicPage.ticketTab)
      .wait(500)
      .hover(indexPage.gridToggleIcon)
      .click(indexPage.gridToggleIcon)
      .wait(500)
      .hover(checkBox)
      .click(checkBox)
      .wait(500)
      .hover(checkBox2)
      .click(checkBox2)
      .hover(this.bulkDelete)
      .click(this.bulkDelete)
      .hover(this.modalDeleteBtn)
      .click(this.modalDeleteBtn);
  }
}
export default bulkUpdate
