import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import deleteTicket from "./delete_tickets";
import MergeTickets from "./merge_tickets";

const naviBar = new navbar();
const golVari = new golvar();
const delTic = new deleteTicket();
const mergeTic = new MergeTickets();

class DeleteMergedTickets {

  constructor() {

  }

  async deleteChildticket(ticParent, ticChild) {
    const selectParentTic = Selector(`[data-tc-view-subject="${ticParent}"]`);
    const childTic = Selector(`[data-tc-view-subject="${ticChild}"]`);
    mergeTic.navigateToMergedTicket(ticParent, ticChild)
    await t.wait(3000)
      .hover(delTic.archiveBtn)
      .click(delTic.archiveBtn)
      .wait(500)
      .hover(delTic.confirmArchived)
      .click(delTic.confirmArchived)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets')
      .wait(1000)
      .hover(selectParentTic)
      .click(selectParentTic)
      .wait(1000)
      .click(childTic)
      .wait(1000)
      .expect(delTic.searchedTicket.innerText).contains(ticChild)
      .wait(500)
      .hover(delTic.deleteBtn)
      .click(delTic.deleteBtn)
      .wait(1000)
      .hover(delTic.confirmDelete)
      .click(delTic.confirmDelete)
      .wait(4000)
  }
} export default DeleteMergedTickets
