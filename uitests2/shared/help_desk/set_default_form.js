import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addCustomForm from "./add_custom_form";
import addCustomForms from '../../shared/company_settings/add_custom_forms';
import validationCustomForm from "./validation_custom_form";
import openPortalTicket from "./open_portal_ticket";

const naviBar = new navbar();
const golVari = new golvar();
const custForm = new addCustomForm();
const newForm = new addCustomForms();
const valForm = new validationCustomForm();
const openPortalPage = new openPortalTicket();

class customFormOperation {

  constructor() {
    this.disableArchive = Selector('[data-tc-disable-archive="true"]');
    this.defaultBox = Selector('[data-tc-check-box="default"]');
    this.viewCanceledField = Selector('[data-tc-drop-element-sections] [data-tc-dragged-form="Category"]');
    this.verifyTextLabel = Selector(`[data-tc="label can't be empty"]`);
    this.inputLabel = Selector('[data-tc-edit-label-name="Category"]');
    this.cancelBtn = Selector('[data-tc-cancel-btn="category"]');
    this.disableSaveBtn = Selector('[data-tc-save-disable="true"]');
    this.removeRadioBtn = Selector('[data-tc-radiobutton-delete]');
    this.removeListBtn = Selector('[data-tc-list-delete]');
    this.changeFormLink = Selector('[data-tc-link="change form"]');
    this.notifyPopup = Selector('.notification-content');
    this.disableSubjectRemove = Selector('[data-tc-disable-remove="Subject"]');
    this.disablePriorityRemove = Selector('[data-tc-disable-remove="Priority"]');
    this.disableStatusRemove = Selector('[data-tc-disable-remove="Status"]');
    this.disableCreatedByRemove = Selector('[data-tc-disable-remove="Created By"]');
    this.disableAssignedToRemove = Selector('[data-tc-disable-remove="Assigned To"]');
    this.disableDescriptionRemove = Selector('[data-tc-disable-remove="Description"]');
    this.backLinkIcon = Selector('[data-tc="back to custom forms"]');
    this.formNameDropDown = Selector('[data-tc-drop-down="forms"]');
    this.selectFormName = this.formNameDropDown.find('li span');
    this.openTicTab = Selector('[data-tc-tab="open ticket portal"]');
    this.unhideAssignedTo = Selector('[data-tc-eye-icon="Assigned To"]');
    this.unhideDescription = Selector('[data-tc-eye-icon="Description"]');
    this.hideAssignedTo = Selector('[data-tc-eye-disabled="Assigned To"]');
    this.hideDescription = Selector('[data-tc-eye-disabled="Description"]');
    this.viewAssignedToField = Selector('[data-tc="assigned_to"]');
    this.viewDescField = Selector('[data-tc="description"]');
    this.requiredFieldError = Selector('[data-tc-error="required fields"]');
    this.requiredFieldMessage = 'Please enter all required (*) fields.';
  }

  async setFromsDefault(formName) {
    const viewFormName = Selector(`[data-tc-form-name="${formName}"]`);
    await t.wait(1000)
    naviBar.navigatetocompanysetting()
    await t.hover(custForm.customFormTab)
      .click(custForm.customFormTab)
      .expect(golVari.currentpageurl()).contains('company/custom_forms')
      .wait(1000)
      .hover(custForm.selectModule)
      .click(custForm.selectModule)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/custom_forms')
      .wait(1000)
      .hover(viewFormName)
      .click(viewFormName)
      .wait(1000)
      .expect(custForm.viewFormName.innerText).contains(formName)
      .wait(1000)
      .hover(valForm.descLabel)
      .wait(1000)
      .hover(this.defaultBox)
      .click(this.defaultBox)
      .wait(1000)
      .hover(custForm.saveForm)
      .click(custForm.saveForm)
      .wait(2000)
      .expect(custForm.checkPopup.innerText).contains('Successfully updated this custom form.')
      .wait(1000)
  }

  async changeTicForm(formName1) {
    await t.wait(1000)
    naviBar.navigatetocompanysetting()
    await t.hover(custForm.customFormTab)
      .click(custForm.customFormTab)
      .expect(golVari.currentpageurl()).contains('company/custom_forms')
      .wait(1000)
      .hover(custForm.selectModule)
      .click(custForm.selectModule)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/custom_forms')
      .wait(1000)
      .hover(custForm.addFormBtnHorizontal)
      .click(custForm.addFormBtnHorizontal)
      .wait(1000)
      .hover(this.changeFormLink)
      .click(this.changeFormLink)
      .wait(1000)
      .hover(this.formNameDropDown)
      .click(this.formNameDropDown)
      .wait(500)
      .click(this.selectFormName.withText(formName1))
      .wait(2000)
      .hover(custForm.viewFormName)
      .expect(custForm.viewFormName.innerText).contains(formName1)
      .wait(1000)
      .hover(this.backLinkIcon)
      .click(this.backLinkIcon)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/custom_forms')
      .wait(1000)
  }

  async validationTicFormField() {
    const saveRadioBtn = Selector('[data-tc-save-edit="Radio Button"]');
    const cancelRadioBtn = Selector('[data-tc-cancel-edit="Radio Button"]');
    const saveListBtn = Selector('[data-tc-save-edit="List"]');
    const cancelListBtn = Selector('[data-tc-cancel-edit="List"]');
    naviBar.navigatetocompanysetting()
    await t.hover(custForm.customFormTab)
      .click(custForm.customFormTab)
      .expect(golVari.currentpageurl()).contains('company/custom_forms')
      .wait(1000)
      .hover(custForm.selectModule)
      .click(custForm.selectModule)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/custom_forms')
      .wait(500)
      .hover(custForm.addFormBtnHorizontal)
      .click(custForm.addFormBtnHorizontal)
      .wait(1000)
      .dragToElement(custForm.categoryElement, custForm.draggableSection)
      .wait(1000)
      .hover(custForm.cancelCatBtn)
      .wait(500)
      .click(custForm.cancelCatBtn)
      .wait(1000)
      .expect(custForm.cancelCatBtn.exists).notOk()
      .wait(500)
      .expect(this.viewCanceledField.exists).notOk()
      .wait(500)
      .dragToElement(custForm.categoryElement, custForm.draggableSection)
      .wait(1000)
      .typeText(this.inputLabel, ' ', { replace: true })
      .pressKey("backspace")
      .wait(1000)
      .hover(this.verifyTextLabel)
      .expect(this.verifyTextLabel.innerText).contains(`Label can't be empty.`)
      .wait(500)
      .expect(this.disableSaveBtn.exists).ok()
      .wait(500)
      .click(this.cancelBtn)
      .wait(1000)
      .dragToElement(newForm.radioBtnDragField, newForm.customizableSection)
      .wait(500)
      .hover(saveRadioBtn)
      .click(saveRadioBtn)
      .wait(1000)
      .hover(this.requiredFieldError)
      .expect(this.requiredFieldError.innerText).contains(`${this.requiredFieldMessage}`)
      .click(cancelRadioBtn)
      .wait(1000)
      .dragToElement(newForm.listDragField, newForm.customizableSection)
      .wait(2000)
      .hover(saveListBtn)
      .click(saveListBtn)
      .wait(500)
      .expect(this.requiredFieldError.innerText).contains(`${this.requiredFieldMessage}`)
      .wait(500)
      .click(cancelListBtn)
      .wait(500)
      .hover(this.disableDescriptionRemove)
      .wait(500)
      .expect(this.disableDescriptionRemove.exists).ok()
      .wait(500)
      .expect(this.disableAssignedToRemove.exists).ok()
      .wait(500)
      .expect(this.disableCreatedByRemove.exists).ok()
      .wait(500)
      .expect(this.disableCreatedByRemove.exists).ok()
      .wait(500)
      .expect(this.disableStatusRemove.exists).ok()
      .wait(500)
      .click(custForm.editStatusIcon)
      .wait(1000)
      .click(custForm.requiredStatusToggle)
      .wait(1000)
      .click(custForm.saveStatusBtn)
      .wait(1000)
      .expect(this.disableStatusRemove.exists).ok()
      .wait(500)
      .hover(this.disablePriorityRemove)
      .wait(500)
      .expect(this.disablePriorityRemove.exists).ok()
      .wait(500)
      .hover(custForm.editPriorityIcon)
      .click(custForm.editPriorityIcon)
      .wait(1000)
      .click(custForm.requiredPriorityToggle)
      .wait(1000)
      .click(custForm.savePriorityBtn)
      .wait(1000)
      .expect(this.disablePriorityRemove.exists).ok()
      .wait(500)
      .hover(this.disableSubjectRemove)
      .wait(500)
      .expect(this.disableSubjectRemove.exists).ok()
      .wait(1000)
      .hover(this.backLinkIcon)
      .click(this.backLinkIcon)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/custom_forms')
      .wait(1000)
  }

  async hideFieldOpenPortal(formName1, workSpaceSelected1) {
    const viewFormName1 = Selector(`[data-tc-form-name="${formName1}"]`);
    const verifySelectedSpace = Selector(`[data-tc-view-select-space="${workSpaceSelected1}"]`);
    const formSelection = Selector(`[data-tc-select-ticket-form="${formName1}"]`);
    const verifySelectedForm = Selector(`[data-tc-view-selected-form="${formName1}"]`);
    naviBar.navigatetocompanysetting()
    await t.hover(custForm.customFormTab)
      .click(custForm.customFormTab)
      .expect(golVari.currentpageurl()).contains('company/custom_forms')
      .wait(1000)
      .hover(custForm.selectModule)
      .click(custForm.selectModule)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/custom_forms')
      .wait(500)
      .hover(custForm.addFormBtnHorizontal)
      .click(custForm.addFormBtnHorizontal)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/custom_forms/new')
      .wait(1000)
      .hover(custForm.editNameIcon)
      .click(custForm.editNameIcon)
      .wait(500)
      .hover(custForm.formNameField)
      .typeText(custForm.formNameField, formName1, { replace: true })
      .wait(500)
      .hover(custForm.saveNameBtn)
      .click(custForm.saveNameBtn)
      .wait(1000)
      .expect(custForm.viewFormName.innerText).contains(formName1)
      .wait(1000)
      .hover(this.openTicTab)
      .click(this.openTicTab)
      .wait(1000)
      .hover(this.unhideAssignedTo)
      .click(this.unhideAssignedTo)
      .wait(1000)
      .hover(this.unhideDescription)
      .click(this.unhideDescription)
      .wait(1000)
      .hover(valForm.createFormBtn)
      .click(valForm.createFormBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/custom_forms')
      .wait(500)
      .expect(viewFormName1.innerText).contains(formName1)
      .wait(1000)
      .navigateTo(openPortalPage.portalUrl)
      .wait(2000)
      .hover(verifySelectedSpace)
      .wait(500)
      .expect(verifySelectedSpace.innerText).contains(workSpaceSelected1)
      .wait(500)
      .click(verifySelectedSpace)
      .wait(1000)
      .hover(formSelection)
      .wait(500)
      .click(formSelection)
      .wait(2000)
      .hover(verifySelectedSpace)
      .wait(500)
      .expect(verifySelectedSpace.innerText).contains(workSpaceSelected1)
      .wait(1000)
      .hover(verifySelectedForm)
      .wait(500)
      .expect(verifySelectedForm.innerText).contains(formName1)
      .wait(1000)
      .expect(this.viewAssignedToField.exists).notOk()
      .wait(1000)
      .expect(this.viewDescField.exists).notOk()
      .wait(1000)
      .navigateTo(openPortalPage.ticketDashboardUrl)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
  }
} export default customFormOperation
