; import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import deleteTicket from "./delete_tickets";
import indexPageAction from "./ticket_index_page_action";
import helpDeskVar from "./help_desk_variables";

const naviBar = new navbar();
const golVari = new golvar();
const delTic = new deleteTicket();
const indexPage = new indexPageAction();
const deskVari = new helpDeskVar();

class ticketFilters {

  constructor() {
    this.viewByFieldFilter = Selector('[data-tc-view-label="filter by field"]');
    this.viewCommonFilter = Selector('[data-tc-view-label="common filters"]');
    this.viewPriorityFilter = Selector('[data-tc-view-label="filter by priority"]');
    this.viewStatusFilter = Selector('[data-tc-view-label="filter by status"]');
    this.viewAssignFilter = Selector('[data-tc-view-label="filter by assignment"]');
    this.viewTimeFilter = Selector('[data-tc-view-label="filter by timeframe"]');
    this.viewSourceFilter = Selector('[data-tc-view-label="filter by source"]');
    this.filterOption = Selector('[data-tc-filter-option-icon]');
    this.viewModalHeader = Selector('[data-tc-header="filter by field"] h2');
    this.viewText = Selector('[data-tc-view-text]');
    this.viewTicketText = Selector('[data-tc-view-text="desired tickets"]');
    this.viewTypeText = Selector('[data-tc-view-text="type"]');
    this.viewKeywordText = Selector('[data-tc-view-text="keywords"]');
    this.viewoptionText = Selector('[data-tc-view-text="options"]');
    this.viewResultText = Selector('[data-tc-view-text="result options"]');
    this.assetText = Selector('[data-tc-assets]');
    this.contractText = Selector('[data-tc-contracts]');
    this.vendorText = Selector('[data-tc-vendor]');
    this.telecomText = Selector('[data-tc-telecom]');
    this.locationText = Selector('[data-tc-location]');
    this.staffText = Selector('[data-tc-staff]');
    this.createdByText = Selector('[data-tc-created-by]');
    this.assignedToText = Selector('[data-tc-assigned-to]');
    this.closeModalBtn = Selector('[data-tc-close-btn="options"]');
    this.filterField = Selector('[data-tc-filter-by-field]');
    this.inputFilter = this.filterField.find('input');
    this.fieldResult = this.filterField.find('li span');
    this.removeActiveFilter = Selector('[data-tc-active-filter-close]');
  }

  async clearAllFilters(filterName) {
    const selectAll = Selector(`[data-tc-view-label="${filterName}"] [data-tc-icon="Select All"]`);
    const clearAll = Selector(`[data-tc-view-label="${filterName}"] [data-tc-icon="Clear All"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(delTic.filterIcon)
      .wait(500)
      .click(delTic.filterIcon)
      .wait(1000)
      .hover(selectAll)
      .click(selectAll)
      .wait(500)
      .hover(clearAll)
      .click(clearAll)
      .wait(500)
      .click(indexPage.dismissFilterContainer)
      .wait(500);
  }

  async applyFilter(filterName, filterBy, isLabel) {
    const filterApplied = Selector(`[data-tc-apply-filter="${filterName}"]`);
    const viewAppliedFilter = Selector(`[data-tc-applied-filter="${filterName}"]`);
    const viewFilterLabel = Selector(`[data-tc-active-filter-label="${filterBy}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(delTic.filterIcon)
      .wait(500)
      .click(delTic.filterIcon)
      .wait(1000);
    if (isLabel == 'with label') {
      await t.hover(filterApplied)
        .click(filterApplied)
        .wait(1000)
        .click(indexPage.dismissFilterContainer)
        .wait(2000)
        .hover(viewFilterLabel)
        .wait(500)
        .expect(viewAppliedFilter.innerText).contains(filterName)
        .wait(1000)
    }
    else {
      await t.hover(filterApplied)
        .click(filterApplied)
        .wait(1000)
        .click(indexPage.dismissFilterContainer)
        .wait(2000)
        .expect(viewAppliedFilter.innerText).contains(filterName)
        .wait(1000)
    }
  }

  async removeFilter(filterName1) {
    const filterApplied1 = Selector(`[data-tc-apply-filter="${filterName1}"]`);
    const viewAppliedFilter1 = Selector(`[data-tc-applied-filter="${filterName1}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(delTic.filterIcon)
      .wait(500)
      .click(delTic.filterIcon)
      .wait(1000)
      .hover(filterApplied1)
      .click(filterApplied1)
      .wait(1000)
      .click(indexPage.dismissFilterContainer)
      .wait(2000)
      .expect(viewAppliedFilter1.exists).notOk()
      .wait(1000)
  }

  async viewAllFiltersTypeNOption() {
    await t.hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(delTic.filterIcon)
      .wait(500)
      .click(delTic.filterIcon)
      .wait(1000)
      .hover(this.viewByFieldFilter)
      .wait(500)
      .expect(this.viewByFieldFilter.innerText).contains('Filter by Field')
      .wait(500)
      .hover(this.viewCommonFilter)
      .wait(500)
      .expect(this.viewCommonFilter.innerText).contains('Common Filters')
      .wait(500)
      .hover(this.viewPriorityFilter)
      .wait(500)
      .expect(this.viewPriorityFilter.innerText).contains('Filter by Priority')
      .wait(500)
      .hover(this.viewStatusFilter)
      .wait(500)
      .expect(this.viewStatusFilter.innerText).contains('Filter by Status')
      .wait(500)
      .hover(this.viewAssignFilter)
      .wait(500)
      .expect(this.viewAssignFilter.innerText).contains('Filter by Assignment')
      .wait(500)
      .hover(this.viewTimeFilter)
      .wait(500)
      .expect(this.viewTimeFilter.innerText).contains('Filter by Timeframe')
      .wait(500)
      .hover(this.viewSourceFilter)
      .wait(500)
      .expect(this.viewSourceFilter.innerText).contains('Filter by Source')
      .wait(1000)
      .click(this.filterOption)
      .wait(2000)
      .expect(this.viewModalHeader.innerText).contains('Filter by Field')
      .wait(500)
      .hover(this.viewText)
      .wait(500)
      .expect(this.viewTicketText.innerText).contains('Now you can get the desired helptickets by applying custom filters.')
      .wait(500)
      .expect(this.viewTypeText.innerText).contains('Thinking about what to type?')
      .wait(500)
      .expect(this.viewKeywordText.innerText).contains('You can type any of the following keywords to search respective filters.')
      .wait(500)
      .expect(this.assetText.innerText).contains('Asset')
      .wait(500)
      .expect(this.contractText.innerText).contains('Contract')
      .wait(500)
      .expect(this.vendorText.innerText).contains('Vendor')
      .wait(500)
      .expect(this.telecomText.innerText).contains('Telecom')
      .wait(500)
      .expect(this.locationText.innerText).contains('Location')
      .wait(500)
      .expect(this.staffText.innerText).contains('People')
      .wait(500)
      .expect(this.createdByText.innerText).contains('Created by')
      .wait(500)
      .expect(this.assignedToText.innerText).contains('Assigned to')
      .wait(500)
      .expect(this.viewoptionText.innerText).contains('After typing any of the above keywords, you will get the list of options.')
      .wait(500)
      .expect(this.viewResultText.innerText).contains('For applying filter, just select any one of the options and you will have the filtered help tickets.')
      .wait(1000)
      .click(this.closeModalBtn)
      .wait(1000)
      .expect(this.viewModalHeader.visible).notOk()
      .wait(1000)
      .click(indexPage.dismissFilterContainer)
      .wait(1000)
  }

  async filterByField(fieldName2, FieldValue) {
    const catText = fieldName2 + ' is ' + FieldValue;
    const viewAppliedFilter1 = Selector(`[data-tc-applied-filter="${catText}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(delTic.filterIcon)
      .wait(500)
      .click(delTic.filterIcon)
      .wait(1000)
      .click(this.filterField)
      .wait(500)
      .typeText(this.filterField, catText, { paste: true })
      .wait(3000)
      .pressKey('enter')
      .wait(2000)
      // .click(this.fieldResult.withText(catText))
      // .wait(1000)
      .expect(viewAppliedFilter1.innerText).contains(catText)
      .wait(1000)
      .click(indexPage.dismissFilterContainer)
      .wait(1000)
      .expect(viewAppliedFilter1.innerText).contains(catText)
      .wait(1000)
  }

  async removeFilterByField(fieldName3, FieldValue1) {
    const catText1 = fieldName3 + ' is ' + FieldValue1;
    const viewAppliedFilter1 = Selector(`[data-tc-applied-filter="${catText1}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(delTic.filterIcon)
      .wait(500)
      .click(delTic.filterIcon)
      .wait(1000)
      .click(this.filterField)
      .wait(1000)
      .typeText(this.filterField, catText1, { paste: true })
      .wait(3000)
      .pressKey('enter')
      .wait(2000)
      .expect(viewAppliedFilter1.exists).notOk()
      .wait(1000)
      .click(indexPage.dismissFilterContainer)
      .wait(1000)
      .expect(viewAppliedFilter1.exists).notOk()
      .wait(1000)
  }

  async filterSearchNoResult(searchItem) {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(delTic.filterIcon)
      .wait(500)
      .click(delTic.filterIcon)
      .wait(1000)
      .click(this.filterField)
      .wait(500)
      .typeText(this.filterField, searchItem, { paste: true })
      .wait(1000)
      .expect(this.fieldResult.innerText).contains('No elements found. Consider changing the search query.')
      .wait(1000)
      .click(indexPage.dismissFilterContainer)
      .wait(1000)
  }

  async viewStatProirFilters(status1, status2, status3, priority1, priority2, priority3) {
    const viewLowProir = Selector('[data-tc-apply-filter="Low"]');
    const viewMeduimProir = Selector('[data-tc-apply-filter="Medium"]');
    const viewHighProir = Selector('[data-tc-apply-filter="High"]');
    const viewProir1 = Selector(`[data-tc-apply-filter="${priority1}"]`);
    const viewProir2 = Selector(`[data-tc-apply-filter="${priority2}"]`);
    const viewProir3 = Selector(`[data-tc-apply-filter="${priority3}"]`);
    const viewOpenStat = Selector('[data-tc-apply-filter="Open"]');
    const viewInProgressStat = Selector('[data-tc-apply-filter="In Progress"]');
    const viewCloseStat = Selector('[data-tc-apply-filter="Closed"]');
    const viewActiveStat = Selector('[data-tc-apply-filter="Active"]');
    const viewNewStat = Selector('[data-tc-apply-filter="New"]');
    const viewUnreadStat = Selector('[data-tc-apply-filter="Unread"]');
    const viewArchivedStat = Selector('[data-tc-apply-filter="Archived"]');
    const viewStat1 = Selector(`[data-tc-apply-filter="${status1}"]`);
    const viewStat2 = Selector(`[data-tc-apply-filter="${status2}"]`);
    const viewStat3 = Selector(`[data-tc-apply-filter="${status3}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .wait(500)
      .click(delTic.filterIcon)
      .wait(500)
      .hover(this.viewPriorityFilter)
      .expect(viewLowProir.innerText).contains('Low')
      .expect(viewMeduimProir.innerText).contains('Medium')
      .expect(viewHighProir.innerText).contains('High')
      .expect(viewProir1.innerText).contains(priority1)
      .expect(viewProir2.innerText).contains(priority2)
      .expect(viewProir3.innerText).contains(priority3)
      .wait(500)
      .hover(this.viewStatusFilter)
      .expect(viewOpenStat.innerText).contains('Open')
      .expect(viewInProgressStat.innerText).contains('In Progress')
      .expect(viewCloseStat.innerText).contains('Closed')
      .expect(viewActiveStat.innerText).contains('Active')
      .expect(viewNewStat.innerText).contains('New')
      .expect(viewArchivedStat.innerText).contains('Archived')
      .expect(viewUnreadStat.innerText).contains('Unread')
      .expect(viewStat1.innerText).contains(status1)
      .expect(viewStat2.innerText).contains(status2)
      .expect(viewStat3.innerText).contains(status3)
      .wait(1000)
  }

  async removeMultiFilters(filterName2, filterName3) {
    const filterApplied1 = Selector(`[data-tc-apply-filter="${filterName2}"]`);
    const viewAppliedFilter1 = Selector(`[data-tc-applied-filter="${filterName2}"]`);
    const filterApplied2 = Selector(`[data-tc-apply-filter="${filterName3}"]`);
    const viewAppliedFilter2 = Selector(`[data-tc-applied-filter="${filterName3}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(delTic.filterIcon)
      .wait(500)
      .click(delTic.filterIcon)
      .wait(1000)
      .hover(filterApplied1)
      .click(filterApplied1)
      .wait(1000)
      .hover(filterApplied2)
      .click(filterApplied2)
      .wait(1000)
      .click(indexPage.dismissFilterContainer)
      .wait(2000)
      .expect(viewAppliedFilter1.exists).notOk()
      .wait(500)
      .expect(viewAppliedFilter2.exists).notOk()
      .wait(1000)
  }

  async removeNAppliedFilters(removedFilter, applyFilter) {
    const filterRemoved = Selector(`[data-tc-apply-filter="${removedFilter}"]`);
    const viewRemovedFilter = Selector(`[data-tc-applied-filter="${removedFilter}"]`);
    const appliedFilter = Selector(`[data-tc-apply-filter="${applyFilter}"]`);
    const checkAppliedFilter = Selector(`[data-tc-applied-filter="${applyFilter}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(delTic.filterIcon)
      .wait(500)
      .click(delTic.filterIcon)
      .wait(1000)
      .hover(filterRemoved)
      .click(filterRemoved)
      .wait(1000)
      .hover(appliedFilter)
      .click(appliedFilter)
      .wait(1000)
      .click(indexPage.dismissFilterContainer)
      .wait(2000)
      .expect(viewRemovedFilter.exists).notOk()
      .wait(500)
      .expect(checkAppliedFilter.exists).ok()
      .wait(1000)
  }

} export default ticketFilters
