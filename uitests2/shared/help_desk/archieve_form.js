import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addCustomForm from "./add_custom_form";
import deleteCustomForm from '../../shared/help_desk/delete_custom_form';

const naviBar = new navbar();
const golVari = new golvar();
const custForm = new addCustomForm();
const delForm = new deleteCustomForm();

class archiveCustomForm {

  constructor() {
    this.disableArchive = Selector('[data-tc-disable-archive="true"]');
  }

  async archiveDefaultForms(formName) {
    const viewFormName = Selector(`[data-tc-form-name="${formName}"]`);
    await t.wait(1000)
    naviBar.navigatetocompanysetting()
    await t.hover(custForm.customFormTab)
      .click(custForm.customFormTab)
      .expect(golVari.currentpageurl()).contains('company/custom_forms')
      .wait(1000)
      .hover(custForm.selectModule)
      .click(custForm.selectModule)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/custom_forms')
      .wait(1000)
      .hover(viewFormName)
      .click(viewFormName)
      .wait(1000)
      .hover(delForm.archiveBtn)
      .click(delForm.archiveBtn)
      .wait(1000)
      .expect(this.disableArchive.exists).ok()
      .wait(1000)
      .hover(delForm.cancelUnarchieveBtn)
      .click(delForm.cancelUnarchieveBtn)
      .wait(1000)
  }
} export default archiveCustomForm
