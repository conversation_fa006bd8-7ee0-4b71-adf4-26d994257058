import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import switchWorkSpace from "./switch_work_space";
import editTickets from "./edit_tickets";
import addTicket from "./add_tickets";

const naviBar = new navbar();
const golVari = new golvar();
const toggleSpace = new switchWorkSpace();
const addTicPage = new addTicket();
const editTicPage = new editTickets();

class automatedTask {

  constructor() {
    this.toggleBtn = Selector('[data-tc-slide-toggle]');
    this.activeTask = Selector('[data-tc-active-task="true"]');
    this.addTaskBtn = Selector(`${golVari.horizontalMenu} [data-tc-add-new-task]`);
    this.eventNameInput = Selector('[data-tc-event-name]');
    this.triggerField = Selector('[data-tc-event-select]');
    this.selectTrigger = this.triggerField.find('li');
    this.nextBtn = Selector('[data-tc-submit]');
    this.actionField = Selector('[data-tc-action-select]');
    this.selectAction = this.actionField.find('li');
    this.eventDetails = Selector('[data-tc-event-detail]');
    this.selectDetails = this.eventDetails.find('li');
    this.recipientsField = Selector('[data-tc-target-option]');
    this.selectRecipient = this.recipientsField.find('li');
    this.saveTaskBtn = Selector('[data-tc-save-form-btn="Save"]');
    this.taskUrl = `${golVari.url}/help_tickets/automated_tasks`;
    this.addAlertText = Selector('[data-tc-trix-editor]');
    this.notifyPopup = Selector('.notification-content');
    this.alertIcon = Selector('[data-tc-alert-icon]');
    this.alertCount = Selector('[data-tc-alert-count]');
    this.closeModalBtn = Selector('[data-tc-ticket-alerts-modal] .sweet-action-close');
    this.automationTab = Selector(`${golVari.horizontalMenu} [data-tc-automation-tab]`);
    this.automatedTask1 = Selector('[data-tc-automated="Notify Assigned-To Changes - Email"]');
    this.automatedTask2 = Selector('[data-tc-automated="Notify Priority Changes - Email"]');
    this.automatedTask3 = Selector('[data-tc-automated="Notify Status Changes - Email"]');
    this.automatedTask4 = Selector('[data-tc-automated="Notify Comment Added - Email"]');
    this.automatedTask5 = Selector('[data-tc-automated="Notify Attachment Added - Email"]');
    this.automatedTask6 = Selector('[data-tc-automated="Notify Status Changes - Email"]');
    this.automatedTask7 = Selector('[data-tc-automated="Notify Status Closed - Email"]');
    this.automatedTask8 = Selector('[data-tc-automated="Notify Ticket Created - Email"]');
    this.defaultLabel1 = Selector('[data-tc-default-icon="Notify Priority Changes - Email"]');
    this.defaultLabel2 = Selector('[data-tc-default-icon="Notify Priority Changes - Email"]');
    this.defaultLabel3 = Selector('[data-tc-default-icon="Notify Status Changes - Email"]');
    this.defaultLabel4 = Selector('[data-tc-default-icon="Notify Comment Added - Email"]');
    this.defaultLabel5 = Selector('[data-tc-default-icon="Notify Attachment Added - Email"]');
    this.defaultLabel6 = Selector('[data-tc-default-icon="Notify Ticket Created - Email"]');
    this.defaultLabel7 = Selector('[data-tc-default-icon="Notify Status Closed - Email"]');
    this.backButton = Selector('[data-tc-button="back to automated task"]');
    this.idSelector = Selector('[data-tc="task id"]');
    this.recipientDropDown = Selector('[data-tc-target-option]');
    this.recipientDropIcon = this.recipientDropDown.find('div');
    this.selectRecipient = this.recipientDropDown.find('ul li span');
  }

  async navigateAutomatedTasks() {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(this.automationTab)
      .wait(500)
      .click(this.automationTab)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('help_tickets/automated_tasks')
      .wait(1000);
  }

  async enableAutomatedTask(taskName) {
    const task = Selector(`[data-tc-automated="${taskName}"]`);
    const toggleIcon = task.find('[data-tc-slide-toggle]');
    const viewtaskActivation = task.find('[data-tc-active-task="true"]');
    await t.expect(golVari.currentpageurl()).contains('help_tickets/automated_tasks')
      .wait(1000)
      .hover(task)
      .expect(task.innerText).eql(taskName)
      .wait(500)
      .click(toggleIcon)
      .wait(500)
      .expect(viewtaskActivation.visible).ok();
  }

  async addAutomatedTasks(eventName, details, triggerSelect, actionSelect, recipient, text, workSpace) {
    const viewTask = Selector(`[data-tc-task-modal="${eventName}"] [data-tc-automated="${eventName}"]`);
    const viewAction = Selector(`[data-tc-task-modal="${eventName}"] [data-tc-event-action]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(this.automationTab)
      .wait(500)
      .click(this.automationTab)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('help_tickets/automated_tasks')
      .wait(1000)
      .hover(this.addTaskBtn)
      .click(this.addTaskBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/automated_tasks/new')
      .wait(500)
      .typeText(this.eventNameInput, eventName, { paste: true })
      .wait(500)
      .click(this.triggerField)
      .wait(500)
      .click(this.selectTrigger.withText(triggerSelect))
      .wait(1000)
      .click(this.eventDetails)
      .wait(500)
      .click(this.selectDetails.withText(details))
      .wait(1000)
      .click(this.actionField)
      .wait(500)
      .click(this.selectAction.withText(actionSelect))
      .wait(500)
      .click(this.recipientsField)
      .wait(500)
      .click(this.selectRecipient.withText(recipient))
      .wait(500)
      .typeText(this.addAlertText, text, { paste: true })
      .wait(500)
      .click(this.saveTaskBtn)
      .wait(2000)
      .expect(viewTask.innerText).eql(eventName)
      .wait(1000)
      .expect(viewAction.innerText).eql(actionSelect);
  }

  async viewExecutedAutomatedTask(alertDescription, counter, workSpace1) {
    const viewMessage = Selector(`[data-tc-alert-message="<div><!--block-->${alertDescription}</div>"]`);
    const selectSpaceList1 = Selector(`[data-workspace-name="${workSpace1}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(toggleSpace.selectedWorkSpace)
      .wait(500)
      .click(toggleSpace.selectedWorkSpace)
      .wait(1000)
      .hover(selectSpaceList1)
      .wait(1000)
      .click(selectSpaceList1)
      .wait(1000)
      .expect(toggleSpace.selectedWorkSpace.innerText).eql(workSpace1)
      .wait(500)
      .hover(this.alertIcon)
      .wait(1000)
      .expect(this.alertCount.innerText).eql(counter)
      .wait(1000)
      .click(this.alertIcon)
      .wait(1000)
      .hover(viewMessage)
      .wait(1000)
      .expect(viewMessage.innerText).eql(alertDescription)
      .wait(1000)
      .click(this.closeModalBtn)
      .wait(1000);
  }

  async deleteAutomatedTask(eventName1) {
    const viewTask = Selector(`[data-tc-task-modal="${eventName1}"] [data-tc-automated="${eventName1}"]`);
    const deleteTask = Selector(`[data-tc-delete-icon="${eventName1}"]`);
    const confirmDelete = Selector(`[data-tc-task-modal="${eventName1}"] [data-tc-i-am-sure]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(this.automationTab)
      .wait(500)
      .click(this.automationTab)
      .wait(2000)
      .hover(viewTask)
      .expect(viewTask.innerText).eql(eventName1)
      .wait(1000)
      .hover(deleteTask)
      .wait(500)
      .click(deleteTask)
      .wait(1000)
      .click(confirmDelete)
      .wait(500)
      .expect(this.notifyPopup.innerText).eql('Task successfully deleted');
  }

  async viewDefaultAutomatedTask() {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(this.automationTab)
      .click(this.automationTab)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/automated_tasks')
      .wait(1000)
      .expect(this.automatedTask1.nth(0).innerText).eql('Notify Assigned-To Changes - Email')
      .hover(this.automatedTask1.nth(0))
      .wait(500)
      .expect(this.automatedTask1.nth(1).innerText).eql('Notify Assigned-To Changes - Email')
      .wait(500)
      .expect(this.automatedTask2.innerText).eql('Notify Priority Changes - Email')
      .expect(this.defaultLabel2.visible).ok()
      .expect(this.automatedTask3.innerText).eql('Notify Status Changes - Email')
      .hover(this.automatedTask3)
      .expect(this.defaultLabel3.visible).ok()
      .expect(this.automatedTask4.innerText).eql('Notify Comment Added - Email')
      .hover(this.automatedTask4)
      .expect(this.defaultLabel4.visible).ok()
      .expect(this.automatedTask5.innerText).eql('Notify Attachment Added - Email')
      .expect(this.defaultLabel5.visible).ok()
      .hover(this.automatedTask5)
      .expect(this.automatedTask8.innerText).eql('Notify Ticket Created - Email')
      .hover(this.automatedTask8)
      .expect(this.defaultLabel6.visible).ok()
      .expect(this.automatedTask7.innerText).eql('Notify Status Closed - Email')
      .hover(this.automatedTask7)
      .expect(this.defaultLabel7.visible).ok()
      .wait(500);
  }

  async assignedToChangesTask() {
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/automated_tasks')
      .click(this.automatedTask1.nth(0))
      .expect(golVari.currentpageurl()).contains('/help_tickets/automated_tasks')
      .wait(1000)
      .expect(this.idSelector.exists).ok();
    const getId = await this.idSelector.innerText;
    const removeHash = getId.replace('#', '');
    await t.click(this.backButton);
    return removeHash;
  }

  async notifyPriorityChangesTask() {
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/automated_tasks')
      .click(this.automatedTask2)
      .expect(golVari.currentpageurl()).contains('/help_tickets/automated_tasks')
      .wait(1000)
      .expect(this.idSelector.exists).ok();
    const getId1 = await this.idSelector.innerText;
    const removeHash1 = getId1.replace('#', '');
    await t.click(this.backButton);
    return removeHash1;
  }

  async notifyStatusChangesTask() {
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/automated_tasks')
      .click(this.automatedTask6)
      .expect(golVari.currentpageurl()).contains('/help_tickets/automated_tasks')
      .wait(1000)
      .expect(this.idSelector.exists).ok();
    const getId2 = await this.idSelector.innerText;
    const removeHash2 = getId2.replace('#', '');
    await t.click(this.backButton);
    return removeHash2;
  }

  async notifyTicketCreatedTask() {
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/automated_tasks')
      .click(this.automatedTask8)
      .expect(golVari.currentpageurl()).contains('/help_tickets/automated_tasks')
      .wait(1000)
      .expect(this.idSelector.exists).ok();
    const getId3 = await this.idSelector.innerText;
    const removeHash3 = getId3.replace('#', '');
    await t.click(this.backButton);
    return removeHash3;
  }

  async notifyAttachmentAddedTask() {
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/automated_tasks')
      .click(this.automatedTask5)
      .expect(golVari.currentpageurl()).contains('/help_tickets/automated_tasks')
      .wait(1000)
      .expect(this.idSelector.exists).ok();
    const getId4 = await this.idSelector.innerText;
    const removeHash4 = getId4.replace('#', '');
    await t.click(this.saveTaskBtn);
    return removeHash4;
  }

  async triggerAssignedToChanges(subject, assignee) {
    const viewAssignee = Selector(`[data-tc-field="assigned_to"] [data-tc-view-selected="${assignee}"]`);
    const selectUser = Selector(`[data-tc-drop-down="assigned_to"] [data-tc-value="${assignee}"]`);
    await t.expect(addTicPage.viewSubject.innerText).contains(subject)
      .wait(2000)
      .hover(editTicPage.dropDownAssignedTo)
      .click(editTicPage.dropDownAssignedTo)
      .wait(1000)
      .hover(editTicPage.selectMenuGroup)
      .click(editTicPage.selectMenuGroup)
      .wait(500)
      .hover(selectUser)
      .click(selectUser)
      .wait(1000)
      .expect(viewAssignee.innerText).eql(assignee);
  }

  async verifyAssigneeChangedHistory(taskId) {
    const viewParentTaskHis = Selector(`[data-tc-id="${taskId}"]`);
    const viewText1 = viewParentTaskHis.find('span span');
    const viewText2 = Selector(`[data-tc-id="${taskId}"] span span b`);
    const viewText3 = viewParentTaskHis.find('span>span:nth-child(2)');
    const viewText4 = viewParentTaskHis.find('span>span:nth-child(2) a');
    await t.click(editTicPage.historyTab)
      .wait(1000)
      .expect(viewText1.innerText).eql('Email has been sent to Users related to the ticket ')
      .expect(viewText3.innerText).eql(`via automated task# ${taskId}`)
  }

  async triggerChangePriority(subject, priority) {
    const viewChangePriority = Selector(`[data-tc-view-priority="${priority}"]`);
    await t.expect(addTicPage.viewSubject.innerText).contains(subject)
      .wait(2000)
      .hover(editTicPage.dropDownPriority)
      .click(editTicPage.dropDownPriority)
      .wait(1000)
      .hover(editTicPage.prioritySelect.withText(priority))
      .wait(1000)
      .click(editTicPage.prioritySelect.withText(priority))
      .wait(1000)
      .expect(viewChangePriority.innerText).contains(priority)
      .wait(1000);
  }

  async verifyProrityChangesHistory(taskId) {
    const viewParentTaskHist = Selector(`[data-tc-id="${taskId}"]`);
    const viewText1 = viewParentTaskHist.find('span span');
    const viewText2 = Selector(`[data-tc-id="${taskId}"] span span b`);
    const viewText3 = viewParentTaskHist.find('span>span:nth-child(2)');
    const viewText4 = viewParentTaskHist.find('span>span:nth-child(2) a');
    await t.click(editTicPage.historyTab)
      .wait(1000)
      .hover(viewParentTaskHist)
      .expect(viewText1.innerText).eql('Email has been sent to All agents of the ticket, Assigned users of the ticket, Creator of the ticket ')
      .expect(viewText3.innerText).eql(`via automated task# ${taskId}`)
  }

  async verifyTicketCreationHistory(taskId) {
    const viewParentTaskHist = Selector(`[data-tc-id="${taskId}"]`);
    const viewText1 = viewParentTaskHist.find('span span');
    const viewText2 = Selector(`[data-tc-id="${taskId}"] span span b`);
    const viewText3 = viewParentTaskHist.find('span>span:nth-child(2)');
    const viewText4 = viewParentTaskHist.find('span>span:nth-child(2) a');
    await t.click(editTicPage.historyTab)
      .wait(2000)
      .hover(viewParentTaskHist)
      .expect(viewText1.innerText).eql('Email has been sent to All agents of the ticket, Assigned users of the ticket, Creator of the ticket ')
      .expect(viewText3.innerText).eql(`via automated task# ${taskId}`)
  }

  async triggerChangeStatus(subject, status) {
    const viewChangeStatus = Selector(`[data-tc-view-status="${status}"]`);
    await t.expect(addTicPage.viewSubject.innerText).contains(subject)
      .wait(1000)
    await t.hover(editTicPage.dropDownStatus)
      .click(editTicPage.dropDownStatus)
      .wait(1000)
      .hover(editTicPage.statusSelect.withText(status))
      .click(editTicPage.statusSelect.withText(status))
      .wait(1000)
      .expect(viewChangeStatus.innerText).contains(status)
      .wait(1000);
  }

  async verifyStatusChangeHistory(taskId) {
    const viewParentTaskHist = Selector(`[data-tc-id="${taskId}"]`);
    const viewText1 = viewParentTaskHist.find('span span');
    const viewText2 = Selector(`[data-tc-id="${taskId}"] span span b`);
    const viewText3 = viewParentTaskHist.find('span>span:nth-child(2)');
    const viewText4 = viewParentTaskHist.find('span>span:nth-child(2) a');
    await t.click(editTicPage.historyTab)
      .wait(1000)
      .hover(viewParentTaskHist)
      .expect(viewText1.innerText).eql('Email has been sent to All agents of the ticket, Assigned users of the ticket, Creator of the ticket ')
      .expect(viewText3.innerText).eql(`via automated task# ${taskId}`)
  }

  async triggerAttachmentAdded(subject) {
    await t.expect(addTicPage.viewSubject.innerText).contains(subject)
      .wait(1000)
      .click(editTicPage.attachmentTab)
      .wait(500)
      .hover(addTicPage.attachment)
      .click(addTicPage.attachment)
      .setFilesToUpload(addTicPage.inputFile, '../../shared/help_desk/ticketImage1.png')
      .wait(500)
      .expect(editTicPage.notifyPopup.innerText).eql('File uploaded successfully')
      .wait(1000);
  }

  async verifyAttachmentAddedHistory(taskId) {
    const viewParentTaskHist = Selector(`[data-tc-id="${taskId}"]`);
    const viewText1 = viewParentTaskHist.find('span span');
    const viewText2 = Selector(`[data-tc-id="${taskId}"] span span b`);
    const viewText3 = viewParentTaskHist.find('span>span:nth-child(2)');
    const viewText4 = viewParentTaskHist.find('span>span:nth-child(2) a');
    await t.click(editTicPage.historyTab)
      .wait(1000)
      .hover(viewParentTaskHist)
      .expect(viewText1.innerText).eql('Email has been sent to All agents of the ticket, Assigned users of the ticket, Creator of the ticket ')
      .expect(viewText3.innerText).eql(`via automated task# ${taskId}`)
  }
} export default automatedTask;
