import { nanoid } from 'nanoid';
import { ClientFunction, Selector } from 'testcafe';
import faker from 'faker';
import { parse } from 'platform';

const generateRandomValue = (length) => {
  const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
  let result = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    result += characters.charAt(randomIndex);
  }
  return result.toLowerCase();
};

const prorityData1 = `High${generateRandomValue(2)}`;
const prorityData2 = `Low${generateRandomValue(2)}`;
const prorityData3 = `Medium${generateRandomValue(2)}`;
const statusData1 = `Todo${generateRandomValue(2)}`;
const statusData2 = `Urgent${generateRandomValue(2)}`;
const statusData3 = `Critical${generateRandomValue(2)}`;

class helpDeskVar {
  constructor() {
    this.everyOneUser = 'Everyone';
    this.adminUser = 'Admins';
    this.helpDeskAgent = 'Help Desk Agents';
    this.workSpaceName = faker.company.companySuffix();
    this.userPerm1 = 'Write All';
    this.userPerm2 = 'Read All / Write Mine';
    this.defaultForm = 'IT General Request';
    this.editworkName = faker.random.word();
    this.defaultWorkSpace = 'Helpdesk';
    this.highPriority = 'High';
    this.lowPriority = 'Low';
    this.meduimPriority = 'Medium';
    this.openStatus = 'Open';
    this.inProgressStatus = 'In Progress';
    this.closeStatus = 'Closed';
    this.HelpTicketModule = 'help_tickets';
    this.ContractModule = 'contract';
    this.vendorModule = 'vendor';
    this.frameSelection = 'Calendar Year';
    this.dueDate = '2023-01-17';
    this.completedDate = '2023-01-02';
    this.completeFutureDate = '2023-01-20';
    this.date = '2022-04-02';
    this.notes = faker.lorem.sentence(5);
    this.comments = faker.lorem.sentence(5);
    this.timeSpent = '22h 3m';
    this.customFormName1 = `Form${faker.lorem.sentence(4)}`;
    this.customFormName2 = `Custom${faker.lorem.sentence(4)}`;
    this.cateName = `category${faker.lorem.sentence(4)}`;
    this.cateLabel = `Category${faker.lorem.sentence(4)}`;
    this.formNotes = faker.lorem.sentence(5);
    this.numName = `number${faker.lorem.sentence(4)}`;
    this.numLabel = `Number${faker.lorem.sentence(4)}`;
    this.tagName = `tag${faker.lorem.sentence(4)}`;
    this.tagLabel = `Tag${faker.lorem.sentence(4)}`;
    this.numDefaultValue = '34';
    this.automatedTask = `Task${faker.lorem.sentence(2)}`;
    this.triggerAction = 'a ticket is created';
    this.detail = 'any ticket';
    this.action = 'add a comment';
    this.taskComments = faker.lorem.sentence(4);
    this.documentName = `Document${nanoid(2)}`;
    this.documentName1 = `test${nanoid(2)}`;
    this.editDocName = `Edit Document${nanoid(2)}`;
    this.selectCategory = 'Facilities';
    this.selectCategory1 = 'Customer Support';
    this.newTitle = `Response${nanoid(2)}`;
    this.editTitle = `editresponse${nanoid(2)}`;
    this.text = faker.lorem.sentence(2);
    this.articleTitle = `Article${nanoid(2)}`;
    this.editArticleTitle = `editArtcile${nanoid(2)}`;
    this.articleTags = `testing${nanoid(2)}`;
    this.editArticleTags = `testingedit${nanoid(2)}`;
    this.articleText = `quibusdamundenullacorrupt${nanoid(2)}`;
    this.articleTitle1 = `Article${nanoid(2)}`;
    this.editArticleTitle1 = `editArtcile${nanoid(2)}`;
    this.articleTags1 = `testing${nanoid(2)}`;
    this.editArticleTags1 = `testingedit${nanoid(2)}`;
    this.articleText1 = `quisdsdsteestind${nanoid(2)}`;
    this.editArticleText = `abcffwwtestererestind${nanoid(2)}`;
    this.eventName = `Automated Task${nanoid(2)}`;
    this.quickViewLabel = 'Select quick view';
    this.ticketCreatedTrigger = 'a ticket is created';
    this.ticketUpdatedTrigger = 'a ticket is updated';
    this.triggerDetail1 = 'any ticket';
    this.actionAlert = 'create an alert';
    this.recipientAllAdmin = 'All Admins';
    this.recipientAllAgents = 'All Agents';
    this.addTextAlert = ' {ticket subject}';
    this.startTimeInHours = '02';
    this.startTimeInMints = '30';
    this.endTimeInHours = '05';
    this.endTimeInMints = '30';
    this.timeSpent1 = '3h';
    this.editStartTimeHr = '04';
    this.editStartTimeMint = '00';
    this.editEndTimeHr = '06';
    this.editEndTimeMint = '00';
    this.editTimeSpent = '2h';
    this.editNotes = faker.lorem.sentence(3);
    this.prority1 = prorityData1;
    this.prority2 = prorityData2;
    this.prority3 = prorityData3;
    this.status1 = statusData1;
    this.status2 = statusData2;
    this.status3 = statusData3;
    this.randomAlphabet = generateRandomAlphabetCharacter();
    this.ticPrefix = generateRandomAlphabetCharacter();
    this.inputRandomIndex = generateRandomNumber(20, 100).toString();
    this.convertToInt = parseInt(this.inputRandomIndex, 10);
    this.incrementIndex = this.convertToInt + 1;
    this.convertToString = this.incrementIndex.toString();
    this.viewRandomIndex = this.convertToString;
    this.myActiveFilter = 'My Active Filter';
    this.allActiveFilter = 'All Active Tickets';
    this.unassignedFilter = 'Unassigned';
    this.closeFilter = 'Closed';
    this.noIncomingText = 'There are no incoming emails at this time.';
    this.attachmentAddedText = 'Show more';
    this.filterByFieldHeaderText = 'Filter by Field';
    this.filterByWorkspaceHeaderText = 'Filter by Workspace';
    this.filterHelpdesk = 'Helpdesk';
    this.appliedHelpdesk = 'Workspace : Helpdesk';
    this.commonFilterHeaderText = 'Common Filters';
    this.ticketsThisMonthText = 'Tickets this month';
    this.almostDueText = 'Almost due';
    this.recentlyClosedText = 'Recently closed';
    this.createdTodayText = 'Created today';
    this.completedThisWeekText = 'Completed this week';
    this.completedThisMonthText = 'Completed this month';
    this.closedByDateText = 'Closed by date';
    this.createdByMeText = 'Created by me';
    this.assignedToMeText = 'Assigned to me';
    this.filterByPriorityHeaderText = 'Filter by Priority';
    this.lowText = 'Low';
    this.mediumText = 'Medium';
    this.highText = 'High';
    this.appliedLow = 'Priority: Low';
    this.appliedMedium = 'Priority: Medium';
    this.appliedHigh = 'Priority: High';
    this.filterByStatusHeaderText = 'Filter by Status';
    this.activeText = 'Active';
    this.openText = 'Open';
    this.closedText = 'Closed';
    this.unreadText = 'Unread';
    this.archivedText = 'Archived';
    this.inProgressText = 'In Progress';
    this.activeStatusPill = 'Status: Active';
    this.closedStatusPill = 'Status: Closed';
    this.appliedOpen = 'Status: Open';
    this.appliedArchived = 'Status: Archived';
    this.appliedInProgress = 'Status: In Progress';
    this.appliedUnread = 'Status: Unread';
    this.filterByAssignmentHeaderText = 'Filter by Assignment';
    this.assignedText = 'Assigned';
    this.unAssignedText = 'Unassigned';
    this.appliedAssigned = 'Assigned';
    this.appliedUnAssigned = 'Unassigned';
    this.filterByTimeFrameHeaderText = 'Filter by Timeframe';
    this.currentMonthText = 'Current month';
    this.lastMonthText = 'Last month';
    this.lastSixMonthText = 'Last 6 months';
    this.lastTwelveMonthText = 'Last 12 months';
    this.calendarYearText = 'Calendar year';
    this.fiscalYearText = 'Fiscal year';
    this.lastTwoYearsText = 'Last 2 years';
    this.customDateText = 'Custom date';
    this.filterBySourceHeaderText = 'Filter by Source';
    this.manuallyAddedText = 'Manually Added';
    this.emailText = 'Email';
    this.slackText = 'Slack';
    this.autoGeneratedText = 'Auto Generated';
    this.microSoftTeamsText = 'Microsoft Teams';
    this.splitText = 'Split';
    this.mobileAppText = 'Mobile App';
    this.appliedManual = 'Source: Manually Added';
    this.appliedEmail = 'Source: Email';
    this.appliedSlack = 'Source: Slack';
    this.appliedAutoGenerated = 'Source: Auto Generated';
    this.appliedTeams = 'Source: Microsoft Teams';
    this.appliedSplit = 'Source: Split';
    this.appliedApp = 'Source: Mobile App';
    this.filterByFormText = 'Filter by Form';
    this.itGeneralText = 'IT General Request';
    this.appliedIT = 'IT General Request';
    this.randomNumberVar = generateRandomDigit();
    this.scheduleTaskName = `Schedulling ${nanoid(2)}`;
    this.policyName = `SLA Testing ${nanoid(2)}`;
    this.policyName1 = `SLA Custom ${nanoid(2)}`;
    this.editPolicyName = `SLA Edit Testing ${nanoid(2)}`;
    this.specificStaff = 'Created by Specific Staff';
    this.specificGroup = 'Created by Member of Specific Group';
    this.formFields = 'Form Field';
    this.firstResponseTarget = 'First Response Target';
    this.resolutionTarget = 'Resolution Target';
    this.emailSubjectReminder = `Reminder for ${nanoid(3)}`;
    this.emailSubjectEscalate = `Escalate to ${nanoid(3)}`;
    this.reminderEmailBody = `This is reminder for ${faker.lorem.sentence(4)}`;
    this.escalateEmailBody = `This is email is esacalate to ${faker.lorem.sentence(4)}`;
    this.editEmailSubjectReminder = `Edit reminder for ${nanoid(3)}`;
    this.editEmailSubjectEscalate = `Edit escalate to ${nanoid(3)}`;
    this.editReminderEmailBody = `This is editing reminder for ${faker.lorem.sentence(4)}`;
    this.editEscalateEmailBody = `This is email is editing esacalate to ${faker.lorem.sentence(4)}`;

    function generateRandomNumber(min, max) {
      return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    function generateRandomAlphabetCharacter() {
      const alphabet = 'abcdefghijklmnopqrstuvwxyz';
      const randomIndex = Math.floor(Math.random() * alphabet.length);
      return alphabet[randomIndex];
    }

    function generateRandomDigit() {
      const digits = '0123456789';
      const randomIndex = Math.floor(Math.random() * digits.length);
      return digits[randomIndex];
    }
  }
} export default helpDeskVar;
