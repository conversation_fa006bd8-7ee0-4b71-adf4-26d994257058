import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import addcontracts from "../contracts/add_contract";
import golvar from "../Globalvariable";
import addFAQ from "./add_faq";

const naviBar = new navbar();
const golVari = new golvar();
const contractPage = new addcontracts();
const faqPage = new addFAQ();

class editFAQ {

  constructor() {
    this.editIcon = Selector('[data-tc-faq-edit]');
    this.checkEditPage = Selector('[data-tc-view-header="edit faq"]');
  }

  async editFAQs(question, editQuestion, editAnswer, editCategory) {
    const viewQuestion = Selector(`[data-tc-question="<div><!--block-->${question}</div>"]`);
    const viewEditQuestion = Selector(`[data-tc-question="<div><!--block-->${editQuestion}</div>"]`);
    const viewSelectedCategory = Selector(`[data-tc-view-category="${editCategory}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(naviBar.resourceNavMenu)
      .click(naviBar.resourceNavMenu)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/articles')
      .wait(500)
      .hover(faqPage.faqTab)
      .click(faqPage.faqTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/faqs')
      .wait(500)
      .hover(faqPage.searchField)
      .typeText(faqPage.searchField, question, { replace: true })
      .pressKey('enter')
      .wait(2000)
      .expect(viewQuestion.innerText).contains(question)
      .wait(1000)
      .hover(viewQuestion)
      .click(viewQuestion)
      .wait(1000)
      .hover(this.editIcon)
      .click(this.editIcon)
      .wait(1000)
      .expect(this.checkEditPage.innerText).contains('Edit FAQ')
      .wait(1000)
      .hover(contractPage.categoryDropDown)
      .wait(1000)
      .click(contractPage.categoryDropDown)
      .wait(500)
      .click(contractPage.selectCategory.withText(editCategory))
      .wait(1000)
      .typeText(faqPage.questionField, editQuestion, { replace: true })
      .wait(2000)
      .typeText(faqPage.answerField, editAnswer, { replace: true })
      .wait(2000)
      .hover(faqPage.submitBtn)
      .click(faqPage.submitBtn)
      .wait(2000)
      .hover(faqPage.searchField)
      .typeText(faqPage.searchField, editQuestion, { replace: true })
      .pressKey('enter')
      .wait(2000)
      .expect(viewSelectedCategory.innerText).contains(editCategory)
      .wait(1000)
      .expect(viewEditQuestion.innerText).contains(editQuestion)
      .wait(1000)
  }
} export default editFAQ
