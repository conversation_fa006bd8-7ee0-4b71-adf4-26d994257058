import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";

const naviBar = new navbar();
const golVari = new golvar();

class addCannedResponse {

  constructor() {
    this.cannedTab = Selector('[data-tc-sub-menu="canned response"]');
    this.searchField = Selector('[data-tc-search-field="canned response"]');
    this.addRepsBtn = Selector(`${golVari.horizontalMenu} [data-tc-add-response]`);
    this.titleField = Selector('[data-tc-field="Response title"]');
    this.textToReplaceField = Selector('[data-tc-input-editor="response_text"]');
    this.submitBtn = Selector('[data-tc-save-form-btn="Save Response"]');
    this.viewTitle = Selector('[data-test-response-title]');
  }

  async addCannedResponses(title, text) {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(naviBar.resourceNavMenu)
      .click(naviBar.resourceNavMenu)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/articles')
      .wait(500)
      .hover(this.cannedTab)
      .click(this.cannedTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/responses')
      .wait(500)
      .hover(this.addRepsBtn)
      .click(this.addRepsBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/responses/new')
      .wait(1000)
      .click(this.titleField)
      .wait(500)
      .typeText(this.titleField, title, { paste: true })
      .wait(2000)
      .typeText(this.textToReplaceField, text, { paste: true })
      .wait(2000)
      .hover(this.submitBtn)
      .click(this.submitBtn)
      .wait(2000)
      .hover(this.searchField)
      .typeText(this.searchField, title, { replace: true })
      .pressKey('enter')
      .wait(2000)
      .expect(this.viewTitle.innerText).contains(title)
      .wait(1000)
  }
} export default addCannedResponse
