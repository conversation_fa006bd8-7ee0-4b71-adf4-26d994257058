import { Selector, t } from "testcafe";
import fieldModal from "./customform_fields_modal";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";


const custFormFields = new fieldModal();
const naviBar = new navbar();
const golVari = new golvar();
const statusName = 'Status' + '_' + Math.floor((Math.random() * 20) + 1);
const statusLabel = statusName;
// const selectType =;

class addCustomForm {

  constructor() {
    this.customFormTab = Selector('[data-tc-custom-forms-tab]');
    this.selectModule = Selector('[data-tc-select-module="Help Tickets"]');
    this.addFormBtn = Selector('[data-tc-add-custom-form]');
    this.addFormBtnHorizontal = Selector(`${golVari.horizontalMenu} [data-tc-add-custom-form]`);
    this.editNameIcon = Selector('[data-tc-edit-icon="Custom Form"]');
    this.formNameField = Selector('[data-tc-form-name]');
    this.saveNameBtn = Selector('[data-tc-save-form-preferences]');
    this.viewFormName = Selector('[data-tc-edit-custom-form]');
    this.draggableSection = Selector('[data-tc-drop-element-sections]');
    this.categoryElement = Selector('[data-tc-category]');
    this.numberElement = Selector('[data-tc-number]');
    this.tagELement = Selector('[data-tc-tag]');
    this.inputName = Selector('[data-tc-edit-name]');
    this.inputLabel = Selector('[data-tc-edit-label]');
    this.inputNote = Selector('[data-tc-input-text-field="Note"]');
    this.inputDefaultValue = Selector('[data-tc-edit-note]');
    this.saveForm = Selector('[data-tc-save-form-btn="Save Form"]');
    this.disableNameBtn = Selector('[data-tc-disable-btn="true"]');
    this.cancelBtn = Selector('[data-tc-cancel-btn="form name"]');
    this.checkPopup = Selector('.notification-content');
    this.cancelCatBtn = Selector('[data-tc-cancel-edit="Category"]');
    this.requiredCreatedByToggle = Selector('[data-tc-field-modal="Created By"] [data-tc-edit-required="Is this field required?"]');
    this.requiredStatusToggle = Selector('[data-tc-edit-field="Status"] [data-tc-edit-required]');
    this.requiredPriorityToggle = Selector('[data-tc-edit-field="Priority"] [data-tc-edit-required]');
    this.requiredSubjectToggle = Selector('[data-tc-edit-field="Subject"] [data-tc-edit-required]');
    this.saveSubjectBtn = Selector('[data-tc-save-edit="Subject"]');
    this.savePriorityBtn = Selector('[data-tc-save-edit="Priority"]');
    this.saveStatusBtn = Selector('[data-tc-save-edit="Status"]');
    this.saveCreatedByBtn = Selector('[data-tc-save-edit="Created By"]');
    this.editSubjectIcon = Selector('[data-tc-edit-icon="Subject"]');
    this.editPriorityIcon = Selector('[data-tc-edit-icon="Priority"]');
    this.editStatusIcon = Selector('[data-tc-edit-icon="Status"]');
    this.editCreatedByIcon = Selector('[data-tc-edit-icon="Created By"]');
    this.editAssignedToIcon = Selector('[data-tc-edit-icon="Assigned To"]');
    this.editDescriptionIcon = Selector('[data-tc-edit-icon="Description"]');
  }

  async addCustomForms(formName, catName, catLabels, numName, numLabels, numDefaultValue, tagName, tagLabels, note) {
    const saveCatBtn = Selector(`[data-tc-save-edit="${catLabels}"]`);
    const saveNumBtn = Selector(`[data-tc-save-edit="${numLabels}"]`);
    const saveTagBtn = Selector(`[data-tc-save-edit="${tagLabels}"]`);
    const viewFormName = Selector(`[data-tc-form-name="${formName}"]`);
    await t.wait(1000)
    naviBar.navigatetocompanysetting()
    await t.hover(this.customFormTab)
      .click(this.customFormTab)
      .expect(golVari.currentpageurl()).contains('company/custom_forms')
      .wait(1000)
      .hover(this.selectModule)
      .click(this.selectModule)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/custom_forms')
      .wait(500)
      .hover(this.addFormBtn)
      .click(this.addFormBtn)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/custom_forms/new')
      .wait(1000)
      .hover(this.editNameIcon)
      .click(this.editNameIcon)
      .wait(500)
      .hover(this.formNameField)
      .typeText(this.formNameField, formName, { replace: true })
      .wait(500)
      .hover(this.saveNameBtn)
      .click(this.saveNameBtn)
      .wait(1000)
      .expect(this.viewFormName.innerText).contains(formName)
      .wait(1000)
      .dragToElement(this.categoryElement, this.draggableSection)
      .wait(1000)
      .hover(this.inputName)
      .typeText(this.inputName, catName, { replace: true })
      .wait(1000)
      .hover(this.inputLabel)
      .typeText(this.inputLabel, catLabels, { replace: true })
      .wait(1000)
      .hover(this.Note)
      .typeText(this.inputNote, note, { replace: true })
      .wait(1000)
      .hover(saveCatBtn)
      .click(saveCatBtn)
      .wait(1000)
      .hover(this.inputName)
      .typeText(this.inputName, numName, { replace: true })
      .wait(1000)
      .hover(this.inputLabel)
      .typeText(this.inputLabel, numLabels, { replace: true })
      .wait(1000)
      .hover(this.inputDefaultValue)
      .typeText(this.inputDefaultValue, numDefaultValue, { replace: true })
      .wait(1000)
      .hover(this.inputNote)
      .typeText(this.inputNote, note, { replace: true })
      .wait(1000)
      .hover(saveNumBtn)
      .click(saveNumBtn)
      .typeText(this.inputName, tagName, { replace: true })
      .wait(1000)
      .hover(this.inputLabel)
      .typeText(this.inputLabel, tagLabels, { replace: true })
      .wait(1000)
      .hover(this.inputNote)
      .typeText(this.inputNote, note, { replace: true })
      .wait(1000)
      .hover(saveTagBtn)
      .click(saveTagBtn)
      .wait(1000)
      .hover(this.saveForm)
      .click(this.saveForm)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/custom_forms')
      .hover(viewFormName)
      .expect(viewFormName.innerText).contains(formName)
      .wait(1000)
  }
} export default addCustomForm
