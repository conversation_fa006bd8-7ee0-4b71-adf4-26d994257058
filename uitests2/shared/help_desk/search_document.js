import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";

const naviBar = new navbar();
const golVari = new golvar();

class searchDocument {

  constructor() {
    this.searchField = Selector('[data-tc-search-document]');
    this.searchedName = Selector('[data-tc-document-name]');
    this.noDocText = Selector('[data-tc-no-doc-found]');
  }

  async searchDocuments(docuName) {
     await t.wait(1000)
      .hover(this.searchField)
      .typeText(this.searchField, docuName, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .expect(this.searchedName.innerText).contains(docuName)
  }

  async searchNotAddedDoc(documName) {
     await t.hover(this.searchField)
      .typeText(this.searchField, documName, { replace: true })
      .hover(this.noDocText)
      .expect(this.noDocText.innerText).contains('Nothing found, try searching again.')
  }
} export default searchDocument
