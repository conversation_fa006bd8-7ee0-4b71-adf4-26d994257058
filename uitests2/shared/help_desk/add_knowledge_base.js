import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addWorkSpace from "./add_work_space";
import editAsset from "../assets/edit_assets";

const naviBar = new navbar();
const golVari = new golvar();
const addSpacePage = new addWorkSpace();
const editAstPage = new editAsset();

class addKnowledgeBase {

  constructor() {
    this.addArticleBtn = Selector(`${golVari.horizontalMenu} [data-tc-btn="add article"]`);
    this.contentTab = Selector('[data-tc-subpage-menu-item="content"]');
    this.articleNavTab = Selector('[data-tc-sub-menu="knowledge base"]');
    this.reviewDateTab = Selector('[data-tc-subpage-menu-item="review"]');
    this.audienceTab = Selector('[data-tc-subpage-menu-item="audience"]');
    this.mainTitleModal = Selector('[data-tc-title="New Article"]');
    this.titleLabel = Selector('[data-tc-title="article title"] label');
    this.titleField = Selector('[data-tc-title="article title"] input');
    this.tagDropDown = Selector('[data-tc-input="article tags"] div');
    this.inputTags = this.tagDropDown.find('input');
    this.categoryLabel = Selector('[data-tc-title="article category"] label');
    this.categoryDropDown = Selector('[data-tc-dropdown="select category"]');
    this.selectCategory = this.categoryDropDown.find('li');
    this.documentLabel = Selector('[data-tc-title="article default document"] label');
    this.documentDropDown = Selector('[data-tc-dropdown="article default docs"] div');
    this.bodyField = Selector('[data-tc-input-editor="article_text"]');
    this.reviewQuestion = Selector('[data-tc-review-question]');
    this.reviewAnswer = Selector('[data-tc-review-answer]');
    this.reviewByLabel = Selector('[data-tc-title="review by"]');
    this.reviewByDropdown = Selector('[data-tc-icon="review by date"]');
    this.audienceQuestion1 = Selector('[data-tc-question="article public"]');
    this.audienceAnswer1 = Selector('[data-tc-answer="article public"]');
    this.makePublicText = Selector('[data-tc-text="make it public"]');
    this.makePublicToggle = Selector('[data-tc-toggle="make article public"]');
    this.activeToggleCheck = Selector('[data-tc-active-task="true"]');
    this.audienceQuestion2 = Selector('[data-tc-question="article audience"]');
    this.audienceAnswer2 = Selector('[data-tc-answer="article audience"]');
    this.saveBtn = Selector('[data-tc-save-form-btn="Save Article"]');
    this.searchField = Selector('[data-tc-article-search]');
    this.articlePermDropdown = Selector('[data-tc-dropdown-icon="audience"]');
    this.viewArticlePerm = this.articlePermDropdown.find('span');
    this.addCategoryLink = Selector('[data-tc-link="add new category"]');
    this.cateModalTitle = Selector('[data-tc-title="add new category"] h2');
    this.categoryModalLabel = Selector('[data-tc-label="category"]');
    this.inputCategory = Selector('[data-tc-new-category]');
    this.saveCateBtn = Selector('[data-tc-save-form-btn="Save Category"]');
    this.customPermiOption = Selector('[data-tc-option="custom permissions"]');
  }

  async navigateToArticles() {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(naviBar.resourceNavMenu)
      .click(naviBar.resourceNavMenu)
      .wait(1000)
      .hover(this.articleNavTab)
      .click(this.articleNavTab)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('help_tickets/articles')
      .wait(1000);
  }

  async addArticlesWithAddedvalues(title, tags, selectCategory, selectDocument, bodyText, date, currentCompany, location) {
    const viewTitleOnModal = Selector(`[data-tc-title="${title}"]`);
    const selectDocOption = Selector(`[data-tc-doc-value="${selectDocument}"]`);
    const viewSelectDoc = Selector(`[data-tc-listed-document="${selectDocument}"]`);
    const selectDateOption = Selector(`[data-tc-date-option="${date}"]`);
    const articleBox = Selector(`[data-tc-article="${title}"]`);
    const viewaddedTitle = articleBox.find(`[data-tc-view-title="${title}"]`);
    const viewBody = articleBox.find('p');
    const viewTag = articleBox.find(`[data-tc-view-article-tags="${tags}"]`);
    const viewCategory = articleBox.find(`[data-tc-view-article-category="${selectCategory}"]`);
    const publicArticle = articleBox.find('[data-tc-article-is-public]');
    const selectLocation = editAstPage.locationDropDown.find('li').withText(location);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(naviBar.resourceNavMenu)
      .click(naviBar.resourceNavMenu)
      .wait(1000)
      .hover(this.articleNavTab)
      .click(this.articleNavTab)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('help_tickets/articles')
      .wait(1000)
      .hover(this.addArticleBtn)
      .click(this.addArticleBtn)
      .wait(500)
      .hover(this.mainTitleModal)
      .expect(this.mainTitleModal.innerText).contains('New Article')
      .click(this.contentTab)
      .expect(this.contentTab.innerText).contains('Content')
      .wait(500)
      .expect(this.titleLabel.innerText).contains('Title ')
      .wait(1000)
      .typeText(this.titleField, title, { paste: true })
      .wait(500)
      .click(this.tagDropDown)
      .wait(500)
      .typeText(this.inputTags, tags, { paste: true })
      .wait(500)
      .pressKey('enter')
      .wait(500)
      .hover(this.categoryLabel)
      .expect(this.categoryLabel.innerText).contains('Category ')
      .hover(this.categoryDropDown)
      .wait(1000)
      .click(this.categoryDropDown)
      .wait(500)
      .click(this.selectCategory.withText(selectCategory))
      .wait(1000)
      .hover(this.documentLabel)
      .expect(this.documentLabel.innerText).contains('Attach Document')
      .hover(this.documentDropDown)
      .wait(500)
      .click(this.documentDropDown)
      .wait(1000)
      .click(selectDocOption)
      .wait(500)
      .expect(viewSelectDoc.innerText).contains(selectDocument)
      .wait(500)
      .click(editAstPage.locationDropDown)
      .wait(500)
      .click(selectLocation)
      .hover(this.bodyField)
      .typeText(this.bodyField, bodyText, { paste: true })
      .wait(1000)
      .hover(viewTitleOnModal)
      .expect(viewTitleOnModal.innerText).contains(title)
      .wait(500)
      .click(this.reviewDateTab)
      .expect(this.reviewDateTab.innerText).contains('Review Date')
      .wait(500)
      .hover(this.reviewQuestion)
      .expect(this.reviewQuestion.innerText).contains('When should this be reviewed?')
      .hover(this.reviewAnswer)
      .expect(this.reviewAnswer.innerText).contains(`We will send a reminder to the author of this article on it's review date so that your article does not get outdated.`)
      .wait(500)
      .hover(this.reviewByDropdown)
      .click(this.reviewByDropdown)
      .wait(500)
      .click(selectDateOption)
      .wait(500)
      .click(this.audienceTab)
      .expect(this.audienceTab.innerText).contains('Audience')
      .wait(500)
      .hover(this.audienceQuestion1)
      .expect(this.audienceQuestion1.innerText).contains('Do you want to make this Article publicly available?')
      .hover(this.audienceAnswer1)
      .expect(this.audienceAnswer1.innerText).contains('Articles can be read outside of the system as well. You get to choose if this article goes public or not.')
      .wait(500)
      .hover(this.makePublicText)
      .expect(this.makePublicText.innerText).contains('Make it Public')
      .hover(this.makePublicToggle)
      .click(this.makePublicToggle)
      .wait(500)
      .expect(this.activeToggleCheck.exists).ok()
      .hover(this.audienceQuestion2)
      .expect(this.audienceQuestion2.innerText).contains('Who is this article meant for?')
      .hover(this.audienceAnswer2)
      .expect(this.audienceAnswer2.innerText).contains('You get to choose who this article is for. You can make it accessible to everyone in your company or set custom permissions for specific individuals.')
      .wait(500)
      .hover(this.viewArticlePerm)
      .expect(this.viewArticlePerm.innerText).contains(`Everyone at ${currentCompany}`)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(1000)
      .hover(articleBox)
      .expect(articleBox.exists).ok()
      .expect(viewaddedTitle.innerText).contains(title)
      .expect(viewBody.innerText).contains(bodyText)
      .expect(viewTag.innerText).contains(tags)
      .expect(viewCategory.innerText).contains(selectCategory)
      .expect(publicArticle.innerText).contains('Public')
      .wait(500);
  }

  async addArticlesWithCustomvalues(title, tags, newCategory, selectDocument, bodyText, date, currentCompany, everyonePerm, groupUser, location) {
    const viewTitleOnModal = Selector(`[data-tc-title="${title}"]`);
    const selectDocOption = Selector(`[data-tc-doc-value="${selectDocument}"]`);
    const viewSelectDoc = Selector(`[data-tc-listed-document="${selectDocument}"]`);
    const selectDateOption = Selector(`[data-tc-date-option="${date}"]`);
    const articleBox = Selector(`[data-tc-article="${title}"]`);
    const viewaddedTitle = articleBox.find(`[data-tc-view-title="${title}"]`);
    const viewBody = articleBox.find('p');
    const viewTag = articleBox.find(`[data-tc-view-article-tags="${tags}"]`);
    const viewCategory = articleBox.find(`[data-tc-view-article-category="${newCategory}"]`);
    const publicArticle = articleBox.find('[data-tc-article-is-public]');
    const viewNewCategory = Selector(`[data-tc-category-option="${newCategory}"]`);
    const selectLocation = editAstPage.locationDropDown.find('li').withText(location);
    await t.hover(naviBar.resourceNavMenu)
      .click(naviBar.resourceNavMenu)
      .wait(1000)
      .hover(this.articleNavTab)
      .click(this.articleNavTab)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('help_tickets/articles')
      .wait(1000)
      .hover(this.addArticleBtn)
      .click(this.addArticleBtn)
      .expect(golVari.currentpageurl()).contains('/help_tickets/articles?new=true')
      .wait(1000)
      .hover(this.mainTitleModal)
      .expect(this.mainTitleModal.innerText).contains('New Article')
      .click(this.contentTab)
      .expect(this.contentTab.innerText).contains('Content')
      .wait(500)
      .expect(this.titleLabel.innerText).contains('Title ')
      .typeText(this.titleField, title, { paste: true })
      .wait(500)
      .click(this.tagDropDown)
      .wait(500)
      .typeText(this.inputTags, tags, { paste: true })
      .wait(500)
      .pressKey('enter')
      .wait(500)
      .hover(this.categoryLabel)
      .expect(this.categoryLabel.innerText).contains('Category ')
      .hover(this.addCategoryLink)
      .wait(500)
      .click(this.addCategoryLink)
      .wait(500)
      .expect(this.cateModalTitle.innerText).contains('Add a new Category')
      .expect(this.categoryModalLabel.innerText).contains('Category')
      .wait(1000)
      .typeText(this.inputCategory, newCategory, { paste: true })
      .wait(500)
      .click(this.saveCateBtn)
      .wait(1000)
      .expect(viewNewCategory.visible).ok()
      .wait(500)
      .hover(this.documentLabel)
      .expect(this.documentLabel.innerText).contains('Attach Document')
      .hover(this.documentDropDown)
      .wait(500)
      .click(this.documentDropDown)
      .wait(1000)
      .click(selectDocOption)
      .wait(500)
      .expect(viewSelectDoc.innerText).contains(selectDocument)
      .wait(500)
      .click(editAstPage.locationDropDown)
      .wait(500)
      .click(selectLocation)
      .hover(this.bodyField)
      .typeText(this.bodyField, bodyText, { paste: true })
      .wait(1000)
      .hover(viewTitleOnModal)
      .expect(viewTitleOnModal.innerText).contains(title)
      .wait(500)
      .click(this.reviewDateTab)
      .expect(this.reviewDateTab.innerText).contains('Review Date')
      .wait(500)
      .hover(this.reviewQuestion)
      .expect(this.reviewQuestion.innerText).contains('When should this be reviewed?')
      .hover(this.reviewAnswer)
      .expect(this.reviewAnswer.innerText).contains(`We will send a reminder to the author of this article on it's review date so that your article does not get outdated.`)
      .wait(500)
      .hover(this.reviewByDropdown)
      .click(this.reviewByDropdown)
      .wait(500)
      .click(selectDateOption)
      .wait(500)
      .click(this.audienceTab)
      .expect(this.audienceTab.innerText).contains('Audience')
      .wait(500)
      .hover(this.audienceQuestion1)
      .expect(this.audienceQuestion1.innerText).contains('Do you want to make this Article publicly available?')
      .hover(this.audienceAnswer1)
      .expect(this.audienceAnswer1.innerText).contains('Articles can be read outside of the system as well. You get to choose if this article goes public or not.')
      .wait(500)
      .hover(this.makePublicText)
      .expect(this.makePublicText.innerText).contains('Make it Public')
      .wait(500)
      .hover(this.audienceQuestion2)
      .expect(this.audienceQuestion2.innerText).contains('Who is this article meant for?')
      .hover(this.audienceAnswer2)
      .expect(this.audienceAnswer2.innerText).contains('You get to choose who this article is for. You can make it accessible to everyone in your company or set custom permissions for specific individuals.')
      .wait(500)
      .hover(this.viewArticlePerm)
      .expect(this.viewArticlePerm.innerText).contains(`Everyone at ${currentCompany}`)
      .click(this.viewArticlePerm)
      .wait(500)
      .hover(this.customPermiOption)
      .click(this.customPermiOption)
      .wait(1000)
      .expect(addSpacePage.viewAdmins.innerText).contains('Admins')
      .wait(500)
      .expect(addSpacePage.viewDisableIcon.exists).ok()
      .wait(500)
      .expect(addSpacePage.viewDisablePerm.exists).ok()
      .wait(1000)
      .expect(addSpacePage.defaultPerm.innerText).contains('Write')
      .wait(500)
      .expect(addSpacePage.viewEveryone.innerText).contains('Everyone')
      .expect(addSpacePage.defaultPerm1.innerText).contains('Read')
      .wait(500)
      .hover(addSpacePage.members)
      .click(addSpacePage.members)
      .click(addSpacePage.setPerm1.withText(everyonePerm))
      .wait(1000)
      .click(addSpacePage.addNewUser)
      .wait(500)
      .hover(addSpacePage.userDropDown)
      .click(addSpacePage.userDropDown)
      .wait(1000)
      .typeText(addSpacePage.inputUser, groupUser, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(1000)
      .hover(articleBox)
      .expect(articleBox.exists).ok()
      .expect(viewaddedTitle.innerText).contains(title)
      .expect(viewBody.innerText).contains(bodyText)
      .expect(viewTag.innerText).contains(tags)
      .expect(viewCategory.innerText).contains(newCategory)
      .expect(publicArticle.exists).notOk()
      .wait(500);
  }
} export default addKnowledgeBase
