import moment from "moment-timezone";
import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";

const naviBar = new navbar();
const golVari = new golvar();

class taskScheduler {

  constructor() {
    this.notificationLabel = '[data-tc-label="notification type"]';
    this.recipientLabel = '[data-tc-label="recipients"]';
    this.startTimeRow = '[data-tc-row="start time"]';
    this.startTimePicker = '[data-tc-input="start hour mintues"]';
    this.endTimeRow = '[data-tc-row="end time"]';
    this.endTimePicker = '[data-tc-input="end hours mintues"]';
    this.taskSchedulerTab = Selector('[data-tc-task-scheduler-tab]');
    this.createTaskBtn = Selector('[data-tc-btn="add scheduled task"]');
    this.createModalTitle = Selector('[data-tc-title="Create Scheduled Task"] h2');
    this.editModalTitle = Selector('[data-tc-title="Edit Scheduled Task"] h2');
    this.taskDetailMenu = Selector('[data-tc-task-submenu="Task Details"]');
    this.notificationMenu = Selector('[data-tc-task-submenu="Notification"]');
    this.nameFieldLabel = Selector('[data-tc-label="task name"]');
    this.nameFieldInput = Selector('[data-tc-field="task name"]');
    this.descriptionFieldLabel = Selector('[data-tc-label="task description"]');
    this.descriptionFieldInput = Selector('[data-tc-field="task description"]');
    this.assigneeFieldLabel = Selector('[data-tc-label="task assignee"]');
    this.taskTimeLabel = Selector('[data-tc-label="task time"]');
    this.startDateLabel = Selector('[data-tc-label="task start time"]');
    this.inputStartDate = Selector('[data-tc-column="start time"] [data-tc-dates-input]');
    this.endDateLabel = Selector('[data-tc-label="task end time"]');
    this.inputEndDate = Selector('[data-tc-column="end time"] [data-tc-dates-input]');
    this.startTimeBox = Selector(`${this.startTimeRow} ${this.startTimePicker}`);
    this.selectStartHour = this.startTimeBox.find('.hours li');
    this.selectStartMintues = this.startTimeBox.find('.minutes li');
    this.endTimeBox = Selector(`${this.endTimeRow} ${this.endTimePicker}`);
    this.selectEndHour = this.endTimeBox.find('.hours li');
    this.selectEndMintues = this.endTimeBox.find('.minutes li');
    this.emailAlertDiv = Selector('[data-tc-parent-label="Email Alert"]');
    this.smsAlertDiv = Selector('[data-tc-parent-label="SMS/Text Alert"]');
    this.desktopAlertDiv = Selector('[data-tc-parent-label="Desktop Notification"]');
    this.emailAlertLabel = this.emailAlertDiv.find(`${this.notificationLabel}`);
    this.emailToggle = this.emailAlertDiv.find('[data-tc-slide-toggle]');
    this.smsAlertLabel = this.smsAlertDiv.find(`${this.notificationLabel}`);
    this.smsToggle = this.smsAlertDiv.find('[data-tc-slide-toggle]');
    this.desktopAlertLabel = this.desktopAlertDiv.find(`${this.notificationLabel}`);
    this.desktopToggle = this.desktopAlertDiv.find('[data-tc-slide-toggle]');
    this.emailRecipientLabel = this.emailAlertDiv.find(`${this.recipientLabel}`);
    this.smsRecipientLabel = this.smsAlertDiv.find(`${this.recipientLabel}`);
    this.desktopRecipientLabel = this.desktopAlertDiv.find(`${this.recipientLabel}`);
    this.emailRecipientDropdown = this.emailAlertDiv.find(`${naviBar.multiUserDataArrtibute}`);
    this.smsRecipientDropdown = this.smsAlertDiv.find(`${naviBar.multiUserDataArrtibute}`);
    this.desktopRecipientDropdown = this.desktopAlertDiv.find(`${naviBar.multiUserDataArrtibute}`);
    this.delModalText = Selector('[data-tc-delete-text]');
    this.confirmDeleteBtn = Selector('[data-tc-save-form-btn="Delete"]');
    this.saveBtn = Selector('[data-tc-btn="save scheduler task"]');
    this.viewTaskModalSelector = Selector('[data-tc-title="view scheduled task"]');
    this.taskModalTitle = this.viewTaskModalSelector.find('h2');
    this.viewAssigneeName = this.viewTaskModalSelector.find('[data-tc-user-name]');
    this.notificationTitle = Selector('[data-tc-title-text="notification"]');
    this.viewEmailReceiver = Selector('[data-tc-column="email"] [data-tc-view-assignee]');
    this.viewSMSReceiver = Selector('[data-tc-column="text"] [data-tc-view-assignee]');
    this.viewDesktopNotifyReceiver = Selector('[data-tc-column="desktop"] [data-tc-view-assignee]');
    this.recurryLabel = Selector('[data-tc-label="recurring"]');
    this.recurryToggle = Selector('[data-tc-section="recurring"] [data-tc-slide-toggle]');
    this.patternLabel = Selector('[data-tc-label="recurrence pattern"]');
    this.modalCloseIcon = this.viewTaskModalSelector.find('.sweet-action-close');
  }

  async addTaskSchedule(taskName, description, assignee, startTime, endTime, startHourMintues, endHourMintues) {
    const selectUser = Selector(`[data-tc-selected-object="${assignee}"]`);
    const viewSelectedUser = Selector(`[data-tc-name-selected="${assignee}"]`);
    const sendToEmailUser = this.emailAlertDiv.find(`[data-tc-selected-object="${assignee}"]`);
    const sendToSMSUser = this.smsAlertDiv.find(`[data-tc-selected-object="${assignee}"]`);
    const sendDesktopNotifyUser = this.desktopAlertDiv.find(`[data-tc-selected-object="${assignee}"]`);
    const viewEmailUser = this.emailAlertDiv.find(`[data-tc-assignee-name="${assignee}"]`);
    const viewSMSUser = this.smsAlertDiv.find(`[data-tc-assignee-name="${assignee}"]`);
    const viewDesktopNotifyUser = this.desktopAlertDiv.find(`[data-tc-assignee-name="${assignee}"]`);
    const [startHour, startMinutes] = startHourMintues.split(':');
    const [endHour, endMinutes] = endHourMintues.split(':');
    const taskBoxSelector = `[data-tc-schedule-task="${taskName}"]`;
    const taskBox = Selector(`${taskBoxSelector}`);
    const viewTask = Selector(`${taskBoxSelector} [data-tc-view-name="${taskName}"]`);
    const viewDescription = Selector(`${taskBoxSelector} [data-tc-view-description]`);
    const viewAssignee = Selector(`${taskBoxSelector} [data-tc-user-name]`);
    await t.hover(this.taskSchedulerTab)
      .click(this.taskSchedulerTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/scheduled_tasks')
      .wait(500)
      .hover(this.createTaskBtn)
      .click(this.createTaskBtn)
      .wait(500)
      .expect(this.createModalTitle.innerText).contains('Create Scheduled Task')
      .click(this.taskDetailMenu)
      .expect(this.taskDetailMenu.innerText).contains('Task Details')
      .wait(500)
      .hover(this.nameFieldLabel)
      .expect(this.nameFieldLabel.innerText).contains('Scheduled Task Name')
      .typeText(this.nameFieldInput, taskName, { paste: true })
      .hover(this.descriptionFieldLabel)
      .expect(this.descriptionFieldLabel.innerText).contains('Task Description')
      .typeText(this.descriptionFieldInput, description, { paste: true })
      .wait(500)
      .hover(this.assigneeFieldLabel)
      .expect(this.assigneeFieldLabel.innerText).contains('Assign to')
      .hover(naviBar.multiUserSelector)
      .click(naviBar.multiUserSelector)
      .wait(500)
      .click(selectUser)
      .wait(1000)
      .expect(viewSelectedUser.innerText).contains(assignee)
      .wait(500)
      .expect(this.taskTimeLabel.innerText).contains('Task Time')
      .expect(this.startDateLabel.innerText).contains('Start:')
      .expect(this.endDateLabel.innerText).contains('End:')
      .wait(500)
      .click(this.inputStartDate)
      .typeText(this.inputStartDate, startTime, { paste: true })
      .wait(500)
      .click(this.startTimeBox)
      .wait(500)
      .click(this.selectStartHour.withText(startHour))
      .wait(500)
      .click(this.selectStartMintues.withText(startMinutes))
      .wait(500)
      .click(this.startTimeBox)
      .wait(500)
      .click(this.inputEndDate)
      .typeText(this.inputEndDate, endTime, { paste: true })
      .wait(500)
      .click(this.endTimeBox)
      .wait(500)
      .click(this.selectEndHour.withText(endHour))
      .wait(500)
      .click(this.selectEndMintues.withText(endMinutes))
      .wait(1000)
      .click(this.endTimeBox)
      .wait(1000)
      .hover(this.notificationMenu)
      .click(this.notificationMenu)
      .expect(this.notificationMenu.innerText).contains('Notification')
      .wait(500)
      .hover(this.emailAlertLabel)
      .expect(this.emailAlertLabel.innerText).contains('Email Alert')
      .hover(this.emailToggle)
      .click(this.emailToggle)
      .wait(500)
      .hover(this.smsAlertLabel)
      .expect(this.smsAlertLabel.innerText).contains('SMS/Text Alert')
      .hover(this.smsToggle)
      .click(this.smsToggle)
      .wait(500)
      .hover(this.desktopAlertLabel)
      .expect(this.desktopAlertLabel.innerText).contains('Desktop Notification')
      .hover(this.desktopToggle)
      .click(this.desktopToggle)
      .wait(500)
      .hover(this.emailRecipientLabel)
      .expect(this.emailRecipientLabel.innerText).contains('Recipient(s)')
      .hover(this.emailRecipientDropdown)
      .click(this.emailRecipientDropdown)
      .wait(500)
      .click(sendToEmailUser)
      .wait(500)
      .hover(viewEmailUser)
      .expect(viewEmailUser.innerText).contains(assignee)
      .wait(500)
      .hover(this.smsRecipientLabel)
      .expect(this.smsRecipientLabel.innerText).contains('Recipient(s)')
      .hover(this.smsRecipientDropdown)
      .click(this.smsRecipientDropdown)
      .wait(500)
      .click(sendToSMSUser)
      .wait(500)
      .hover(viewSMSUser)
      .expect(viewSMSUser.innerText).contains(assignee)
      .wait(500)
      .hover(this.desktopRecipientLabel)
      .expect(this.desktopRecipientLabel.innerText).contains('Recipient(s)')
      .hover(this.desktopRecipientDropdown)
      .click(this.desktopRecipientDropdown)
      .wait(500)
      .click(sendDesktopNotifyUser)
      .wait(500)
      .hover(viewDesktopNotifyUser)
      .expect(viewDesktopNotifyUser.innerText).contains(assignee)
      .wait(500)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(1000)
      .hover(taskBox)
      .expect(taskBox.visible).ok('Schedule task should be visible')
      .expect(viewTask.innerText).contains(taskName)
      .hover(viewDescription)
      .expect(viewDescription.innerText).contains(description)
      .hover(viewAssignee)
      .expect(viewAssignee.innerText).contains(assignee)
      .wait(500)
      .click(taskBox)
      .wait(1000)
      .hover(this.taskModalTitle)
      .expect(this.taskModalTitle.innerText).contains('View Scheduled Task')
      .hover(this.viewAssigneeName)
      .expect(this.viewAssigneeName.innerText).contains(assignee)
      .wait(500)
      .hover(this.notificationTitle)
      .expect(this.notificationTitle.innerText).contains('Notifications')
      .expect(this.viewEmailReceiver.innerText).contains(assignee)
      .expect(this.viewSMSReceiver.innerText).contains(assignee)
      .expect(this.viewDesktopNotifyReceiver.innerText).contains(assignee)
      .wait(500)
      .hover(this.modalCloseIcon)
      .click(this.modalCloseIcon)
      .wait(500);
  }

  async addRecuryTaskSchedule(taskName, description, assignee, startTime, endTime, startHourMintues, endHourMintues, patternSelection, rangeRecuryEnd) {
    const selectUser = Selector(`[data-tc-selected-object="${assignee}"]`);
    const viewSelectedUser = Selector(`[data-tc-name-selected="${assignee}"]`);
    const sendToEmailUser = this.emailAlertDiv.find(`[data-tc-selected-object="${assignee}"]`);
    const sendToSMSUser = this.smsAlertDiv.find(`[data-tc-selected-object="${assignee}"]`);
    const sendDesktopNotifyUser = this.desktopAlertDiv.find(`[data-tc-selected-object="${assignee}"]`);
    const viewEmailUser = this.emailAlertDiv.find(`[data-tc-assignee-name="${assignee}"]`);
    const viewSMSUser = this.smsAlertDiv.find(`[data-tc-assignee-name="${assignee}"]`);
    const viewDesktopNotifyUser = this.desktopAlertDiv.find(`[data-tc-assignee-name="${assignee}"]`);
    const [startHour, startMinutes] = startHourMintues.split(':');
    const [endHour, endMinutes] = endHourMintues.split(':');
    const taskBoxSelector = `[data-tc-schedule-task="${taskName}"]`;
    const taskBox = Selector(`${taskBoxSelector}`);
    const viewTask = Selector(`${taskBoxSelector} [data-tc-view-name="${taskName}"]`);
    const viewDescription = Selector(`${taskBoxSelector} [data-tc-view-description]`);
    const viewAssignee = Selector(`${taskBoxSelector} [data-tc-user-name]`);
    await t.hover(this.taskSchedulerTab)
      .click(this.taskSchedulerTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/scheduled_tasks')
      .wait(500)
      .hover(this.createTaskBtn)
      .click(this.createTaskBtn)
      .wait(500)
      .expect(this.createModalTitle.innerText).contains('Create Scheduled Task')
      .click(this.taskDetailMenu)
      .expect(this.taskDetailMenu.innerText).contains('Task Details')
      .wait(500)
      .hover(this.nameFieldLabel)
      .expect(this.nameFieldLabel.innerText).contains('Scheduled Task Name')
      .typeText(this.nameFieldInput, taskName, { paste: true })
      .hover(this.descriptionFieldLabel)
      .expect(this.descriptionFieldLabel.innerText).contains('Task Description')
      .typeText(this.descriptionFieldInput, description, { paste: true })
      .wait(500)
      .hover(this.assigneeFieldLabel)
      .expect(this.assigneeFieldLabel.innerText).contains('Assign to')
      .hover(naviBar.multiUserSelector)
      .click(naviBar.multiUserSelector)
      .wait(500)
      .click(selectUser)
      .wait(1000)
      .expect(viewSelectedUser.innerText).contains(assignee)
      .wait(500)
      .hover(this.recurryLabel)
      .expect(this.recurryLabel.innerText).contains('Recurring')
      .hover(this.recurryToggle)
      .click(this.recurryToggle)
      .wait(500)
      .hover(this.patternLabel)
      .expect(this.patternLabel.innerText).contains('Recurrence Pattern')
      .wait(500)
      .expect(this.taskTimeLabel.innerText).contains('Task Time')
      .expect(this.startDateLabel.innerText).contains('Start:')
      .expect(this.endDateLabel.innerText).contains('End:')
      .wait(500)
      .click(this.startTimeBox)
      .wait(500)
      .click(this.selectStartHour.withText(startHour))
      .wait(500)
      .click(this.selectStartMintues.withText(startMinutes))
      .wait(500)
      .click(this.startTimeBox)
      .wait(500)
      .click(this.endTimeBox)
      .wait(500)
      .click(this.selectEndHour.withText(endHour))
      .wait(500)
      .click(this.selectEndMintues.withText(endMinutes))
      .wait(1000)
      .click(this.endTimeBox)
      .wait(1000);

    // if (patternSelection === 'Weekly') {
    //   await t.hover()


    // } else if (patternSelection === 'Montly') {
    //   await t.hover()



    // } else if (patternSelection === 'Yearly') {
    //   await t.hover()

    // } else {
    //   await t.hover()

    // }

    await t.hover(this.notificationMenu)
      .click(this.notificationMenu)
      .expect(this.notificationMenu.innerText).contains('Notification')
      .wait(500)
      .hover(this.smsAlertLabel)
      .expect(this.smsAlertLabel.innerText).contains('SMS/Text Alert')
      .hover(this.smsToggle)
      .click(this.smsToggle)
      .wait(500)
      .hover(this.desktopAlertLabel)
      .expect(this.desktopAlertLabel.innerText).contains('Desktop Notification')
      .hover(this.desktopToggle)
      .click(this.desktopToggle)
      .wait(500)
      .hover(this.emailRecipientLabel)
      .expect(this.emailRecipientLabel.innerText).contains('Recipient(s)')
      .hover(this.emailRecipientDropdown)
      .click(this.emailRecipientDropdown)
      .wait(500)
      .click(sendToEmailUser)
      .wait(500)
      .hover(viewEmailUser)
      .expect(viewEmailUser.innerText).contains(assignee)
      .wait(500)
      .hover(this.smsRecipientLabel)
      .expect(this.smsRecipientLabel.innerText).contains('Recipient(s)')
      .hover(this.smsRecipientDropdown)
      .click(this.smsRecipientDropdown)
      .wait(500)
      .click(sendToSMSUser)
      .wait(500)
      .hover(viewSMSUser)
      .expect(viewSMSUser.innerText).contains(assignee)
      .wait(500)
      .hover(this.desktopRecipientLabel)
      .expect(this.desktopRecipientLabel.innerText).contains('Recipient(s)')
      .hover(this.desktopRecipientDropdown)
      .click(this.desktopRecipientDropdown)
      .wait(500)
      .click(sendDesktopNotifyUser)
      .wait(500)
      .hover(viewDesktopNotifyUser)
      .expect(viewDesktopNotifyUser.innerText).contains(assignee)
      .wait(500)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(1000)
      .hover(taskBox)
      .expect(taskBox.visible).ok('Schedule task should be visible')
      .expect(viewTask.innerText).contains(taskName)
      .hover(viewDescription)
      .expect(viewDescription.innerText).contains(description)
      .hover(viewAssignee)
      .expect(viewAssignee.innerText).contains(assignee)
      .wait(500)
      .click(taskBox)
      .wait(1000)
      .hover(this.taskModalTitle)
      .expect(this.taskModalTitle.innerText).contains('View Scheduled Task')
      .hover(this.viewAssigneeName)
      .expect(this.viewAssigneeName.innerText).contains(assignee)
      .wait(500)
      .hover(this.notificationTitle)
      .expect(this.notificationTitle.innerText).contains('Notifications')
      .expect(this.viewSMSReceiver.innerText).contains(assignee)
      .expect(this.viewDesktopNotifyReceiver.innerText).contains(assignee)
      .wait(500)
      .hover(this.modalCloseIcon)
      .click(this.modalCloseIcon)
      .wait(500);
  }

  async editTaskSchedule(taskName, assignee, editTaskName, editDescription, editAssignee, editStartTime, editEndTime, editStartHourMintues, editEndHourMintues) {
    const selectedUser = Selector(`[data-tc-selected-object="${assignee}"]`);
    const selectUser = Selector(`[data-tc-selected-object="${editAssignee}"]`);
    const viewSelectedUser = Selector(`[data-tc-name-selected="${editAssignee}"]`);
    const sendToEmailUser = this.emailAlertDiv.find(`[data-tc-selected-object="${editAssignee}"]`);
    const sendToSMSUser = this.smsAlertDiv.find(`[data-tc-selected-object="${editAssignee}"]`);
    const sendDesktopNotifyUser = this.desktopAlertDiv.find(`[data-tc-selected-object="${editAssignee}"]`);
    const viewEmailUser = this.emailAlertDiv.find(`[data-tc-assignee-name="${editAssignee}"]`);
    const viewSMSUser = this.smsAlertDiv.find(`[data-tc-assignee-name="${editAssignee}"]`);
    const viewDesktopNotifyUser = this.desktopAlertDiv.find(`[data-tc-assignee-name="${editAssignee}"]`);
    const [editStartHour, editStartMinutes] = editStartHourMintues.split(':');
    const [editEndHour, editEndMinutes] = editEndHourMintues.split(':');
    const taskBoxSelector = `[data-tc-schedule-task="${taskName}"]`;
    const editTaskBoxSelector = `[data-tc-schedule-task="${editTaskName}"]`;
    const taskBox = Selector(`${taskBoxSelector}`);
    const editTaskBox = Selector(`${editTaskBoxSelector}`);
    const viewTask = Selector(`${taskBoxSelector} [data-tc-view-name="${taskName}"]`);
    const viewEditTask = Selector(`${editTaskBoxSelector} [data-tc-view-name="${editTaskName}"]`);
    const viewDescription = Selector(`${editTaskBoxSelector} [data-tc-view-description]`);
    const viewAssignee = Selector(`${editTaskBoxSelector} [data-tc-user-name]`);
    const editTaskBtn = Selector(`${taskBoxSelector} [data-tc-btn="task edit"]`);
    await t.hover(this.taskSchedulerTab)
      .click(this.taskSchedulerTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/scheduled_tasks')
      .wait(500)
      .hover(taskBox)
      .expect(taskBox.visible).ok('Schedule task should be visible')
      .expect(viewTask.innerText).contains(taskName)
      .wait(500)
      .hover(editTaskBtn)
      .click(editTaskBtn)
      .wait(500)
      .expect(this.editModalTitle.innerText).contains('Edit Scheduled Task')
      .click(this.taskDetailMenu)
      .expect(this.taskDetailMenu.innerText).contains('Task Details')
      .wait(500)
      .hover(this.nameFieldLabel)
      .expect(this.nameFieldLabel.innerText).contains('Scheduled Task Name')
      .typeText(this.nameFieldInput, editTaskName, { replace: true })
      .hover(this.descriptionFieldLabel)
      .expect(this.descriptionFieldLabel.innerText).contains('Task Description')
      .typeText(this.descriptionFieldInput, editDescription, { replace: true })
      .wait(500)
      .hover(this.assigneeFieldLabel)
      .expect(this.assigneeFieldLabel.innerText).contains('Assign to')
      .hover(naviBar.multiUserSelector)
      .click(naviBar.multiUserSelector)
      .wait(500)
      .click(selectedUser)
      .wait(1000)
      .hover(naviBar.multiUserSelector)
      .click(naviBar.multiUserSelector)
      .wait(500)
      .click(selectUser)
      .wait(500)
      .expect(viewSelectedUser.innerText).contains(editAssignee)
      .wait(500)
      .expect(this.taskTimeLabel.innerText).contains('Task Time')
      .expect(this.startDateLabel.innerText).contains('Start:')
      .expect(this.endDateLabel.innerText).contains('End:')
      .wait(500)
      .click(this.inputStartDate)
      .typeText(this.inputStartDate, editStartTime, { replace: true })
      .wait(500)
      .click(this.startTimeBox)
      .wait(500)
      .click(this.selectStartHour.withText(editStartHour))
      .wait(500)
      .click(this.selectStartMintues.withText(editStartMinutes))
      .wait(500)
      .click(this.startTimeBox)
      .wait(500)
      .click(this.inputEndDate)
      .typeText(this.inputEndDate, editEndTime, { replace: true })
      .wait(500)
      .click(this.endTimeBox)
      .wait(500)
      .click(this.selectEndHour.withText(editEndHour))
      .wait(500)
      .click(this.selectEndMintues.withText(editEndMinutes))
      .wait(1000)
      .click(this.endTimeBox)
      .wait(1000)
      .hover(this.notificationMenu)
      .click(this.notificationMenu)
      .expect(this.notificationMenu.innerText).contains('Notification')
      .wait(500)
      .hover(this.emailAlertLabel)
      .expect(this.emailAlertLabel.innerText).contains('Email Alert')
      .hover(this.smsAlertLabel)
      .expect(this.smsAlertLabel.innerText).contains('SMS/Text Alert')
      .hover(this.desktopAlertLabel)
      .expect(this.desktopAlertLabel.innerText).contains('Desktop Notification')
      .hover(this.emailRecipientLabel)
      .expect(this.emailRecipientLabel.innerText).contains('Recipient(s)')
      .hover(this.emailRecipientDropdown)
      .click(this.emailRecipientDropdown)
      .wait(500)
      .click(sendToEmailUser)
      .wait(500)
      .hover(viewEmailUser)
      .expect(viewEmailUser.innerText).contains(editAssignee)
      .wait(500)
      .hover(this.smsRecipientLabel)
      .expect(this.smsRecipientLabel.innerText).contains('Recipient(s)')
      .hover(this.smsRecipientDropdown)
      .click(this.smsRecipientDropdown)
      .wait(500)
      .click(sendToSMSUser)
      .wait(500)
      .hover(viewSMSUser)
      .expect(viewSMSUser.innerText).contains(editAssignee)
      .wait(500)
      .hover(this.desktopRecipientLabel)
      .expect(this.desktopRecipientLabel.innerText).contains('Recipient(s)')
      .hover(this.desktopRecipientDropdown)
      .click(this.desktopRecipientDropdown)
      .wait(500)
      .click(sendDesktopNotifyUser)
      .wait(500)
      .hover(viewDesktopNotifyUser)
      .expect(viewDesktopNotifyUser.innerText).contains(editAssignee)
      .wait(500)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(1000)
      .expect(taskBox.exists).notOk('Old schedule task should not visible because its edit')
      .hover(editTaskBox)
      .expect(editTaskBox.visible).ok('Edited schedule task should be visible')
      .expect(viewEditTask.innerText).contains(editTaskName)
      .hover(viewDescription)
      .expect(viewDescription.innerText).contains(editDescription)
      .hover(viewAssignee)
      .expect(viewAssignee.innerText).contains(editAssignee)
      .wait(500)
      .click(editTaskBox)
      .wait(1000)
      .hover(this.taskModalTitle)
      .expect(this.taskModalTitle.innerText).contains('View Scheduled Task')
      .hover(this.viewAssigneeName)
      .expect(this.viewAssigneeName.innerText).contains(editAssignee)
      .wait(500)
      .hover(this.notificationTitle)
      .expect(this.notificationTitle.innerText).contains('Notifications')
      .expect(this.viewEmailReceiver.withText(assignee).innerText).contains(assignee)
      .expect(this.viewEmailReceiver.withText(editAssignee).innerText).contains(editAssignee)
      .expect(this.viewSMSReceiver.withText(assignee).innerText).contains(assignee)
      .expect(this.viewSMSReceiver.withText(editAssignee).innerText).contains(editAssignee)
      .expect(this.viewDesktopNotifyReceiver.withText(assignee).innerText).contains(assignee)
      .expect(this.viewDesktopNotifyReceiver.withText(editAssignee).innerText).contains(editAssignee)
      .wait(500)
      .hover(this.modalCloseIcon)
      .click(this.modalCloseIcon)
      .wait(500);
  }

  async deleteTaskSchedule(taskName) {
    const taskBoxSelector = `[data-tc-schedule-task="${taskName}"]`;
    const taskBox = Selector(`${taskBoxSelector}`);
    const viewTask = Selector(`${taskBoxSelector} [data-tc-view-name="${taskName}"]`);
    const deleteTaskBtn = Selector(`${taskBoxSelector} [data-tc-btn="task delete"]`);
    await t.hover(this.taskSchedulerTab)
      .click(this.taskSchedulerTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/scheduled_tasks')
      .wait(500)
      .hover(taskBox)
      .expect(taskBox.visible).ok('Schedule task should be visible')
      .expect(viewTask.innerText).contains(taskName)
      .wait(500)
      .hover(deleteTaskBtn)
      .click(deleteTaskBtn)
      .wait(500)
      .hover(this.delModalText)
      .expect(this.delModalText.innerText).contains('Are you sure you want to delete this Scheduled Task?')
      .wait(500)
      .hover(this.confirmDeleteBtn)
      .click(this.confirmDeleteBtn)
      .wait(500)
      .expect(naviBar.notifyPopup.innerText).contains('Scheduled Task deleted successfully!')
      .expect(taskBox.exists).notOk('Schedule task should not visible')
      .wait(500);
  }
} export default taskScheduler;
