import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addFAQ from "./add_faq";

const naviBar = new navbar();
const golVari = new golvar();
const faqPage = new addFAQ();

class deleteFAQ {

  constructor() {
    this.deleteIcon = Selector('[data-tc-faq-delete]');
    this.confirmDelete = Selector('[data-tc-btn="yes delete"]');
    this.cancelDelete = Selector('[data-tc-keepit]');
    this.checkDeletedFaq = Selector('[data-tc-no-faq-found]');
  }

  async deleteFAQs(question, switcher) {
    const viewQuestion = Selector(`[data-tc-question="<div><!--block-->${question}</div>"]`);
    const checkQuestion = Selector(`[data-tc-view-question="<div><div><!--block-->${question}</div></div>"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(naviBar.resourceNavMenu)
      .click(naviBar.resourceNavMenu)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/articles')
      .wait(500)
      .hover(faqPage.faqTab)
      .click(faqPage.faqTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/faqs')
      .wait(500)
      .hover(faqPage.searchField)
      .typeText(faqPage.searchField, question, { replace: true })
      .pressKey('enter')
      .wait(2000)
      .expect(viewQuestion.innerText).contains(question)
      .wait(1000)
      .hover(viewQuestion)
      .click(viewQuestion)
      .wait(1000);
    if (switcher == 'yes') {
      await t.wait(1000)
        .hover(this.deleteIcon)
        .click(this.deleteIcon)
        .wait(1000)
        .hover(this.confirmDelete)
        .click(this.confirmDelete)
        .wait(1000)
        .expect(golVari.currentpageurl()).contains('/help_tickets/faqs')
        .wait(2000)
        .hover(faqPage.searchField)
        .typeText(faqPage.searchField, question, { replace: true })
        .pressKey('enter')
        .wait(2000)
        .expect(this.checkDeletedFaq.innerText).contains("No FAQ's exist, yet.")
        .wait(1000)
    }
    else {
      await t.wait(1000)
      .hover(this.deleteIcon)
      .click(this.deleteIcon)
      .wait(1000)
      .hover(this.cancelDelete)
      .click(this.cancelDelete)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/faqs')
      .wait(2000)
      .expect(checkQuestion.innerText).contains(question)
      .wait(1000)
    }
  }
} export default deleteFAQ
