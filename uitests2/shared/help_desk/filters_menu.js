import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addTicket from "./add_tickets";
import indexPageAction from "./ticket_index_page_action";

const naviBar = new navbar();
const golVari = new golvar();
const actionTicPage = new indexPageAction();

class filtersMenu {
  constructor() {
    this.filterBtn = Selector('[data-tc-filter-menu-button]');
    this.ticketsTab = Selector(`${golVari.horizontalMenu} [data-tc-tickets]`);
    this.filterByFieldHeader = Selector('[data-tc-view-label="filter by field"]');
    this.filterByWorkspaceHeader = Selector('[data-tc-view-label="filter by wo  rkspaces"]');
    this.workSpaceHelpdesk = Selector('[data-tc-workspace="Helpdesk"]');
    this.removeWorkSpaceHelpdesk = Selector('[data-tc-remove-filter="Helpdesk"]');
    this.commonFilterHeader = Selector('[data-tc-view-label="common filters"]');
    this.ticketsThisMonth = Selector('[data-tc-apply-filter="Tickets this month"]');
    this.almostDue = Selector('[data-tc-apply-filter="Almost due"]');
    this.recentlyClosed = Selector('[data-tc-apply-filter="Recently closed"]');
    this.createdToday = Selector('[data-tc-apply-filter="Created today"]');
    this.completedThisWeek = Selector('[data-tc-apply-filter="Completed this week"]');
    this.completedThisMonth = Selector('[data-tc-apply-filter="Completed this month"]');
    this.closedByDate = Selector('[data-tc-apply-filter="Closed by date"]');
    this.createdByMe = Selector('[data-tc-apply-filter="Created by me"]');
    this.assignedToMe = Selector('[data-tc-apply-filter="Assigned to me"]');
    this.filterByPriorityHeader = Selector('[data-tc-view-label="filter by priority"]');
    this.lowPriority = Selector('[data-tc-apply-filter="Low"]');
    this.removeLowPriority = Selector('[data-tc-remove-filter="Low"]');
    this.mediumPriority = Selector('[data-tc-apply-filter="Medium"]');
    this.removeMediumPriority = Selector('[data-tc-remove-filter="Medium"]');
    this.highPriority = Selector('[data-tc-apply-filter="High"]');
    this.removeHighPriority = Selector('[data-tc-remove-filter="High"]');
    this.filterByStatusHeader = Selector('[data-tc-view-label="filter by status"]');
    this.activeStatus = Selector('[data-tc-apply-filter="Active"]');
    this.removeActiveStatus = Selector('[data-tc-remove-filter="Active"]');
    this.openStatus = Selector('[data-tc-apply-filter="Open"]');
    this.removeOpenStatus = Selector('[data-tc-remove-filter="Open"]');
    this.closedStatus = Selector('[data-tc-apply-filter="Closed"]');
    this.removeClosedStatus = Selector('[data-tc-remove-filter="Closed"]');
    this.unreadStatus = Selector('[data-tc-apply-filter="Unread"]');
    this.removeUnreadStatus = Selector('[data-tc-remove-filter="Unread"]');
    this.archivedStatus = Selector('[data-tc-apply-filter="Archived"]');
    this.removeArchivedStatus = Selector('[data-tc-remove-filter="Archived"]');
    this.inProgressStatus = Selector('[data-tc-apply-filter="In Progress"]');
    this.removeInProgressStatus = Selector('[data-tc-remove-filter="In Progress"]');
    this.filterByAssignmentHeader = Selector('[data-tc-view-label="filter by assignment"]');
    this.assignedFilter = Selector('[data-tc-apply-filter="Assigned"]');
    this.removeAssignedFilter = Selector('[data-tc-remove-filter="Assigned"]');
    this.unassignedFilter = Selector('[data-tc-apply-filter="Unassigned"]');
    this.removeUnassignedFilter = Selector('[data-tc-remove-filter="Unassigned"]');
    this.filterByTimeFrameHeader = Selector('[data-tc-view-label="filter by timeframe"]');
    this.filterBySourceHeader = Selector('[data-tc-view-label="filter by source"]');
    this.filterByFormHeader = Selector('[data-tc-view-label="filter by form"]');
    this.currentMonth = Selector('[data-tc-apply-filter="Current month"]');
    this.lastMonth = Selector('[data-tc-apply-filter="Last month"]');
    this.lastSixMonths = Selector('[data-tc-apply-filter="Last 6 months"]');
    this.lastTwelveMonths = Selector('[data-tc-apply-filter="Last 12 months"]');
    this.calendarYear = Selector('[data-tc-apply-filter="Calendar year"]');
    this.fiscalYear = Selector('[data-tc-apply-filter="Fiscal year"]');
    this.lastTwoYears = Selector('[data-tc-apply-filter="Last 2 years"]');
    this.removeLastTwoYears = Selector('[data-tc-remove-filter="Last 2 years"]');
    this.customDate = Selector('[data-tc-apply-filter="Custom date"]');
    this.manuallyAdded = Selector('[data-tc-apply-filter="Manually Added"]');
    this.removeManuallyAdded = Selector('[data-tc-remove-filter="Manually Added"]');
    this.emailFilter = Selector('[data-tc-apply-filter="Email"]');
    this.removeEmail = Selector('[data-tc-remove-filter="Email"]');
    this.slackFilter = Selector('[data-tc-apply-filter="Slack"]');
    this.removeSlack = Selector('[data-tc-remove-filter="Slack"]');
    this.autoGenerated = Selector('[data-tc-apply-filter="Auto Generated"]');
    this.removeAutoGenerated = Selector('[data-tc-remove-filter="Auto Generated"]');
    this.microSoftTeams = Selector('[data-tc-apply-filter="Microsoft Teams"]');
    this.removeMicrosoftTeams = Selector('[data-tc-remove-filter="Microsoft Teams"]');
    this.splitFilter = Selector('[data-tc-apply-filter="Split"]');
    this.removeSplit = Selector('[data-tc-remove-filter="Split"]');
    this.mobileApp = Selector('[data-tc-apply-filter="Mobile App"]');
    this.removeMobileApp = Selector('[data-tc-remove-filter="Mobile App"]');
    this.itGeneralRequest = Selector('[data-tc-apply-filter="IT General Request"]');
    this.removeForm = Selector('[data-tc-remove-filter="IT General Request"]');
    this.removeFilterMenu = Selector('[data-tc-icon="dismiss"]');
    this.statusPill = Selector('[data-tc-active-filter-label="Status"]');
    this.priorityPill = Selector('[data-tc-active-filter-label="Priority"]');
    this.workSpacePill = Selector('[data-tc-active-filter-label="Workspace"]');
    this.sourcePill = Selector('[data-tc-active-filter-label="Source"]');
    this.formPill = Selector('[data-tc-active-filter-label="Form"]');
    this.commonPill1 = Selector('[data-tc-applied-filter="Tickets this month"]');
    this.commonPill2 = Selector('[data-tc-applied-filter="Almost due"]');
    this.commonPill3 = Selector('[data-tc-applied-filter="Recently closed"]');
    this.commonPill4 = Selector('[data-tc-applied-filter="Created today"]');
    this.commonPill5 = Selector('[data-tc-applied-filter="Completed this week"]');
    this.commonPill6 = Selector('[data-tc-applied-filter="Completed this month"]');
    this.commonPill7 = Selector('[data-tc-applied-filter="Closed by date"]');
    this.commonPill8 = Selector('[data-tc-applied-filter="Created by me"]');
    this.commonPill9 = Selector('[data-tc-applied-filter="Assigned to me"]');
    this.assignmentPill1 = Selector('[data-tc-applied-filter="Assigned"]');
    this.assignmentPill2 = Selector('[data-tc-applied-filter="Unassigned"]');
    this.timeFramePill1 = Selector('[data-tc-applied-filter="Current month"]');
    this.timeFramePill2 = Selector('[data-tc-applied-filter="Last month"]');
    this.timeFramePill3 = Selector('[data-tc-applied-filter="Last 6 months"]');
    this.timeFramePill4 = Selector('[data-tc-applied-filter="Last 12 months"]');
    this.timeFramePill5 = Selector('[data-tc-applied-filter="Calendar year"]');
    this.timeFramePill6 = Selector('[data-tc-applied-filter="Fiscal year"]');
    this.timeFramePill8 = Selector('[data-tc-applied-filter="Last 2 years"]');
    this.gridToggleFilter = Selector('[data-tc-menu-container] [data-tc-btn="Grid"]');
    this.formLabel = Selector('[data-tc-filter-label="Form"]');
  }

  async viewWorkspaceAndFieldFilters(filterByFieldText, helpdeskWorkSpace) {
    await t.wait(1000)
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(this.ticketsTab)
      .click(this.ticketsTab)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .click(this.gridToggleFilter)
      .wait(500)
      .expect(this.filterByFieldHeader.innerText).contains(filterByFieldText)
      .wait(500)
      .expect(this.workSpaceHelpdesk.innerText).contains(helpdeskWorkSpace)
      .wait(500)
      .click(actionTicPage.dismissFilterContainer);
  }

  async viewCommonFilters(commonFilterText, ticketsThisMonthFilter, almostDueFilter, recentlyClosedFilter, createdTodayFilter, completedThisWeekFilter, completedThisMonthFilter, closedByDateFilter, createdByMeFilter, assignedToMeFilter) {
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(this.ticketsTab)
      .click(this.ticketsTab)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .expect(this.commonFilterHeader.innerText).contains(commonFilterText)
      .wait(500)
      .expect(this.ticketsThisMonth.innerText).contains(ticketsThisMonthFilter)
      .wait(500)
      .expect(this.almostDue.innerText).contains(almostDueFilter)
      .wait(500)
      .expect(this.recentlyClosed.innerText).contains(recentlyClosedFilter)
      .wait(500)
      .expect(this.createdToday.innerText).contains(createdTodayFilter)
      .wait(500)
      .expect(this.completedThisWeek.innerText).contains(completedThisWeekFilter)
      .wait(500)
      .expect(this.completedThisMonth.innerText).contains(completedThisMonthFilter)
      .wait(500)
      .expect(this.closedByDate.innerText).contains(closedByDateFilter)
      .wait(500)
      .expect(this.createdByMe.innerText).contains(createdByMeFilter)
      .wait(500)
      .expect(this.assignedToMe.innerText).contains(assignedToMeFilter)
      .wait(500)
      .click(actionTicPage.dismissFilterContainer);
  }

  async viewPriorityFilters(priorityText, lowPriorityFilter, mediumPriorityFilter, highPriorityFilter) {
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(this.ticketsTab)
      .click(this.ticketsTab)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .expect(this.filterByPriorityHeader.innerText).contains(priorityText)
      .wait(500)
      .expect(this.lowPriority.innerText).contains(lowPriorityFilter)
      .wait(500)
      .expect(this.mediumPriority.innerText).contains(mediumPriorityFilter)
      .wait(500)
      .expect(this.highPriority.innerText).contains(highPriorityFilter)
      .wait(500)
      .click(actionTicPage.dismissFilterContainer);
  }

  async viewStatusFilters(statusText, activeStatusFilter, openStatusFilter, closedStatusFilter, unreadStatusFilter, archivedStatusFilter, inProgressStatusFilter) {
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(this.ticketsTab)
      .click(this.ticketsTab)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .expect(this.filterByStatusHeader.innerText).contains(statusText)
      .wait(500)
      .expect(this.activeStatus.innerText).contains(activeStatusFilter)
      .wait(500)
      .expect(this.openStatus.innerText).contains(openStatusFilter)
      .wait(500)
      .expect(this.closedStatus.innerText).contains(closedStatusFilter)
      .wait(500)
      .expect(this.unreadStatus.innerText).contains(unreadStatusFilter)
      .wait(500)
      .expect(this.archivedStatus.innerText).contains(archivedStatusFilter)
      .wait(500)
      .expect(this.inProgressStatus.innerText).contains(inProgressStatusFilter)
      .wait(500)
      .click(actionTicPage.dismissFilterContainer);
  }

  async viewAssignmentFilters(assignmentFilterText, assignedFilterText, unassignedFilterText) {
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(this.ticketsTab)
      .click(this.ticketsTab)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .expect(this.filterByAssignmentHeader.innerText).contains(assignmentFilterText)
      .wait(500)
      .expect(this.assignedFilter.innerText).contains(assignedFilterText)
      .wait(500)
      .expect(this.unassignedFilter.innerText).contains(unassignedFilterText)
      .wait(500)
      .click(actionTicPage.dismissFilterContainer);
  }

  async viewFilterByTimeFrame(timeFrameFilterText, currentMonthText, lastMonthText, lastSixMonthsText, lastTwelveMonthsText, calendarYearText, fiscalYearText, lastTwoYearsText, customDateText) {
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(this.ticketsTab)
      .click(this.ticketsTab)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .expect(this.filterByTimeFrameHeader.innerText).contains(timeFrameFilterText)
      .wait(500)
      .expect(this.currentMonth.innerText).contains(currentMonthText)
      .wait(500)
      .expect(this.lastMonth.innerText).contains(lastMonthText)
      .wait(500)
      .expect(this.lastSixMonths.innerText).contains(lastSixMonthsText)
      .wait(500)
      .expect(this.lastTwelveMonths.innerText).contains(lastTwelveMonthsText)
      .wait(500)
      .expect(this.calendarYear.innerText).contains(calendarYearText)
      .wait(500)
      .expect(this.fiscalYear.innerText).contains(fiscalYearText)
      .wait(500)
      .expect(this.lastTwoYears.innerText).contains(lastTwoYearsText)
      .wait(500)
      .expect(this.customDate.innerText).contains(customDateText)
      .wait(500)
      .click(actionTicPage.dismissFilterContainer);
  }

  async viewFilterBySource(sourceText, manuallyAddedText, emailFilterText, slackFilterText, autoGeneratedText, microSoftTeamsText, splitFilterText, mobileAppText) {
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(this.ticketsTab)
      .click(this.ticketsTab)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .expect(this.filterBySourceHeader.innerText).contains(sourceText)
      .wait(500)
      .expect(this.manuallyAdded.innerText).contains(manuallyAddedText)
      .wait(500)
      .expect(this.emailFilter.innerText).contains(emailFilterText)
      .wait(500)
      .expect(this.slackFilter.innerText).contains(slackFilterText)
      .wait(500)
      .expect(this.autoGenerated.innerText).contains(autoGeneratedText)
      .wait(500)
      .expect(this.microSoftTeams.innerText).contains(microSoftTeamsText)
      .wait(500)
      .expect(this.splitFilter.innerText).contains(splitFilterText)
      .wait(500)
      .expect(this.mobileApp.innerText).contains(mobileAppText)
      .wait(500)
      .click(actionTicPage.dismissFilterContainer);
  }

  async viewFilterByForm(formText, itGeneralRequestText) {
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(this.ticketsTab)
      .click(this.ticketsTab)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .expect(this.filterByFormHeader.innerText).contains(formText)
      .wait(500)
      .expect(this.itGeneralRequest.innerText).contains(itGeneralRequestText)
      .wait(500)
      .click(actionTicPage.dismissFilterContainer);
  }

  async applyCommonFilters(appliedTicketsThisMonth, appliedAlmostDue, appliedRecentlyClosed, appliedCreatedToday, appliedCompletedThisWeek, appliedCompletedThisMonth) {
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(this.ticketsTab)
      .click(this.ticketsTab)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.ticketsThisMonth)
      .click(this.ticketsThisMonth)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .expect(this.commonPill1.innerText).contains(appliedTicketsThisMonth)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.almostDue)
      .click(this.almostDue)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .expect(this.commonPill2.innerText).contains(appliedAlmostDue)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.recentlyClosed)
      .click(this.recentlyClosed)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .expect(this.commonPill3.innerText).contains(appliedRecentlyClosed)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.completedThisWeek)
      .click(this.completedThisWeek)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .expect(this.commonPill5.innerText).contains(appliedCompletedThisWeek);
  }

  async applyPriorityFilters(appliedLow, appliedMedium, appliedHigh) {
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(this.ticketsTab)
      .click(this.ticketsTab)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.lowPriority)
      .click(this.lowPriority)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.priorityPill)
      .expect(this.priorityPill.innerText).contains(appliedLow)
      .wait(500)
      .hover(this.removeLowPriority)
      .click(this.removeLowPriority)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.mediumPriority)
      .click(this.mediumPriority)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.priorityPill)
      .expect(this.priorityPill.innerText).contains(appliedMedium)
      .wait(500)
      .hover(this.removeMediumPriority)
      .click(this.removeMediumPriority)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.highPriority)
      .click(this.highPriority)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.priorityPill)
      .expect(this.priorityPill.innerText).contains(appliedHigh)
      .wait(500)
      .hover(this.removeHighPriority)
      .click(this.removeHighPriority);
  }

  async applyStatusFilters(appliedActive, appliedOpen, appliedClosed, appliedUnRead, appliedArchived, appliedInProgress) {
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(this.ticketsTab)
      .click(this.ticketsTab)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.activeStatus)
      .click(this.activeStatus)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.statusPill)
      .expect(this.statusPill.innerText).contains(appliedActive)
      .wait(500)
      .hover(this.removeActiveStatus)
      .click(this.removeActiveStatus)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.openStatus)
      .click(this.openStatus)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.statusPill)
      .expect(this.statusPill.innerText).contains(appliedOpen)
      .wait(500)
      .hover(this.removeOpenStatus)
      .click(this.removeOpenStatus)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.inProgressStatus)
      .click(this.inProgressStatus)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.statusPill)
      .expect(this.statusPill.innerText).contains(appliedInProgress)
      .wait(500)
      .hover(this.removeInProgressStatus)
      .click(this.removeInProgressStatus)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.closedStatus)
      .click(this.closedStatus)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.statusPill)
      .expect(this.statusPill.innerText).contains(appliedClosed)
      .wait(500)
      .hover(this.removeClosedStatus)
      .click(this.removeClosedStatus)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.unreadStatus)
      .click(this.unreadStatus)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.statusPill)
      .expect(this.statusPill.innerText).contains(appliedUnRead)
      .wait(500)
      .hover(this.removeUnreadStatus)
      .click(this.removeUnreadStatus)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.archivedStatus)
      .click(this.archivedStatus)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.statusPill)
      .expect(this.statusPill.innerText).contains(appliedArchived)
      .wait(500)
      .hover(this.removeArchivedStatus)
      .click(this.removeArchivedStatus);
  }

  async applyAssignmentFilters(appliedAssigned, appliedUnAssigned) {
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(this.ticketsTab)
      .click(this.ticketsTab)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.assignedFilter)
      .click(this.assignedFilter)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.assignmentPill1)
      .expect(this.assignmentPill1.innerText).contains(appliedAssigned)
      .wait(500)
      .hover(this.removeAssignedFilter)
      .click(this.removeAssignedFilter)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.unassignedFilter)
      .click(this.unassignedFilter)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.assignmentPill2)
      .expect(this.assignmentPill2.innerText).contains(appliedUnAssigned)
      .wait(500)
      .hover(this.removeUnassignedFilter)
      .click(this.removeUnassignedFilter);
  }

  async applyTimeFrameFilters(appliedCurrentMonth, appliedLastMonth, appliedLastSixMonth, appliedLastTwelveMonth, appliedCalendarYear, appliedFiscalYear, appliedLastTwoYears) {
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(this.ticketsTab)
      .click(this.ticketsTab)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.currentMonth)
      .click(this.currentMonth)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.timeFramePill1)
      .expect(this.timeFramePill1.innerText).contains(appliedCurrentMonth)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.lastMonth)
      .click(this.lastMonth)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.timeFramePill2)
      .expect(this.timeFramePill2.innerText).contains(appliedLastMonth)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.lastSixMonths)
      .click(this.lastSixMonths)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.timeFramePill3)
      .expect(this.timeFramePill3.innerText).contains(appliedLastSixMonth)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.lastTwelveMonths)
      .click(this.lastTwelveMonths)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.timeFramePill4)
      .expect(this.timeFramePill4.innerText).contains(appliedLastTwelveMonth)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.calendarYear)
      .click(this.calendarYear)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.timeFramePill5)
      .expect(this.timeFramePill5.innerText).contains(appliedCalendarYear)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.fiscalYear)
      .click(this.fiscalYear)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.timeFramePill6)
      .expect(this.timeFramePill6.innerText).contains(appliedFiscalYear)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.lastTwoYears)
      .click(this.lastTwoYears)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.timeFramePill8)
      .expect(this.timeFramePill8.innerText).contains(appliedLastTwoYears)
      .wait(500)
      .hover(this.removeLastTwoYears)
      .click(this.removeLastTwoYears);
  }

  async applySourceFilters(appliedManuallyAdded, appliedEmail, appliedSlack, appliedAutoGenerated, appliedMicrosoftTeams, appliedSplit, appliedMobileApp) {
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(this.ticketsTab)
      .click(this.ticketsTab)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.manuallyAdded)
      .click(this.manuallyAdded)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.sourcePill)
      .expect(this.sourcePill.innerText).contains(appliedManuallyAdded)
      .wait(500)
      .hover(this.removeManuallyAdded)
      .click(this.removeManuallyAdded)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.emailFilter)
      .click(this.emailFilter)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.sourcePill)
      .expect(this.sourcePill.innerText).contains(appliedEmail)
      .wait(500)
      .hover(this.removeEmail)
      .click(this.removeEmail)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.slackFilter)
      .click(this.slackFilter)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.sourcePill)
      .expect(this.sourcePill.innerText).contains(appliedSlack)
      .wait(500)
      .hover(this.removeSlack)
      .click(this.removeSlack)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.autoGenerated)
      .click(this.autoGenerated)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.sourcePill)
      .expect(this.sourcePill.innerText).contains(appliedAutoGenerated)
      .wait(500)
      .hover(this.removeAutoGenerated)
      .click(this.removeAutoGenerated)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.microSoftTeams)
      .click(this.microSoftTeams)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.sourcePill)
      .expect(this.sourcePill.innerText).contains(appliedMicrosoftTeams)
      .wait(500)
      .hover(this.removeMicrosoftTeams)
      .click(this.removeMicrosoftTeams)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.splitFilter)
      .click(this.splitFilter)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.sourcePill)
      .expect(this.sourcePill.innerText).contains(appliedSplit)
      .wait(500)
      .hover(this.removeSplit)
      .click(this.removeSplit)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.mobileApp)
      .click(this.mobileApp)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(500)
      .hover(this.sourcePill)
      .expect(this.sourcePill.innerText).contains(appliedMobileApp)
      .wait(500)
      .hover(this.removeMobileApp)
      .click(this.removeMobileApp);
  }

  async applyFormFilters(appliedIT) {
    const viewAppliedFilter = Selector(`[data-tc-applied-filter="${appliedIT}"]`);
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(this.ticketsTab)
      .click(this.ticketsTab)
      .wait(500)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .wait(500)
      .hover(this.itGeneralRequest)
      .click(this.itGeneralRequest)
      .wait(500)
      .hover(this.removeFilterMenu)
      .click(this.removeFilterMenu)
      .wait(1000)
      .expect(this.formLabel.innerText).contains('Form:')
      .expect(viewAppliedFilter.innerText).contains(appliedIT)
      .wait(500)
      .hover(this.removeForm)
      .click(this.removeForm);
  }
}
export default filtersMenu;
