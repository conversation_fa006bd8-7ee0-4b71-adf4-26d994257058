import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import addcontracts from "../contracts/add_contract";
import golvar from "../Globalvariable";

const naviBar = new navbar();
const golVari = new golvar();
const contractPage = new addcontracts();

class addFAQ {

  constructor() {
    this.faqTab = Selector('[data-tc-sub-menu="FAQ"]');
    this.searchField = Selector('[data-tc-faq-search]');
    this.addFaqBtn = Selector(`${golVari.horizontalMenu} [data-tc-add-faq]`);
    this.questionField = Selector('[data-tc-input-editor="question"]');
    this.answerField = Selector('[data-tc-input-editor="answer"]');
    this.submitBtn = Selector('[data-tc-save-form-btn="Submit"]');
  }

  async addFAQs(question, answer, category) {
    const viewQuestion = Selector(`[data-tc-question="<div><!--block-->${question}</div>"]`);
    const viewSelectedCategory = Selector(`[data-tc-view-category="${category}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(naviBar.resourceNavMenu)
      .click(naviBar.resourceNavMenu)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/articles')
      .wait(500)
      .hover(this.faqTab)
      .click(this.faqTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/faqs')
      .wait(500)
      .hover(this.addFaqBtn)
      .click(this.addFaqBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/faqs/new')
      .wait(500)
      .hover(contractPage.categoryDropDown)
      .wait(1000)
      .click(contractPage.categoryDropDown)
      .wait(500)
      .click(contractPage.selectCategory.withText(category))
      .wait(1000)
      .typeText(this.questionField, question, { paste: true })
      .wait(2000)
      .typeText(this.answerField, answer, { paste: true })
      .wait(2000)
      .hover(this.submitBtn)
      .click(this.submitBtn)
      .wait(2000)
      .hover(this.searchField)
      .typeText(this.searchField, question, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .expect(viewSelectedCategory.innerText).contains(category)
      .wait(1000)
      .expect(viewQuestion.innerText).contains(question)
      .wait(1000)
  }
} export default addFAQ
