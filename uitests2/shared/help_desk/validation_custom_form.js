import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addCustomForm from "./add_custom_form";

const naviBar = new navbar();
const golVari = new golvar();
const custForm = new addCustomForm();

class validationCustomForm {

  constructor(){
    this.descLabel = Selector('[data-tc-view-label="Description"]');
    this.createFormBtn = Selector('[data-tc-save-form-btn="Create Form"]');
  }

  async addFormValidation(formName, defaultName, duplicateName) {
    const viewFormName = Selector(`[data-tc-form-name="${formName}"]`);
    await t.wait(1000)
    naviBar.navigatetocompanysetting()
    await t.hover(custForm.customFormTab)
      .click(custForm.customFormTab)
      .expect(golVari.currentpageurl()).contains('company/custom_forms')
      .wait(1000)
      .hover(custForm.selectModule)
      .click(custForm.selectModule)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/custom_forms')
      .wait(500)
      .hover(custForm.addFormBtnHorizontal)
      .click(custForm.addFormBtnHorizontal)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/custom_forms/new')
      .wait(1000)
      .hover(custForm.editNameIcon)
      .click(custForm.editNameIcon)
      .wait(500)
      .typeText(custForm.formNameField, ' ', { replace: true })
      .wait(1000)
      .pressKey('backspace')
      .wait(1000)
      .expect(custForm.disableNameBtn.exists).ok()
      .wait(500)
      .hover(custForm.formNameField)
      .typeText(custForm.formNameField, formName, { replace: true })
      .wait(1000)
      .hover(custForm.cancelBtn)
      .click(custForm.cancelBtn)
      .wait(1000)
      .expect(custForm.viewFormName.innerText).contains(defaultName)
      .wait(500)
      .hover(custForm.editNameIcon)
      .click(custForm.editNameIcon)
      .wait(500)
      .hover(custForm.formNameField)
      .typeText(custForm.formNameField, duplicateName, { replace: true })
      .wait(1000)
      .hover(custForm.saveNameBtn)
      .click(custForm.saveNameBtn)
      .wait(1000)
      .expect(custForm.viewFormName.innerText).contains(duplicateName)
      .wait(1000)
      .hover(this.descLabel)
      .wait(1000)
      .hover(this.createFormBtn)
      .click(this.createFormBtn)
      .wait(500)
      .expect(custForm.checkPopup.innerText).contains('There was an error submitting the form. Form name has already been taken')
      .wait(500)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/custom_forms/new')
      .wait(1000)
      .hover(custForm.editNameIcon)
      .click(custForm.editNameIcon)
      .wait(500)
      .hover(custForm.formNameField)
      .typeText(custForm.formNameField, formName, { replace: true })
      .wait(1000)
      .hover(custForm.saveNameBtn)
      .click(custForm.saveNameBtn)
      .wait(1000)
      .hover(this.descLabel)
      .wait(1000)
      .hover(this.createFormBtn)
      .click(this.createFormBtn)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/custom_forms')
      .wait(500)
      .hover(viewFormName)
      .expect(viewFormName.innerText).contains(formName)
      .wait(1000)
  }
} export default validationCustomForm
