import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addWorkSpace from "./add_work_space";

const naviBar = new navbar();
const golVari = new golvar();
const workSpace = new addWorkSpace();

class switchWorkSpace {

  constructor() {
    this.selectedWorkSpace = Selector('[data-dropdown-menu="workspace_select_menu"] h6');
  }

  async switchWorkSpaces(workName) {
    const viewSpaceName = Selector(`[data-workspace-name="${workName}"]`);
    const settingIcon = Selector(`[data-workspace-name="${workName}"] [data-tc-workspace-setting-icon]`);
    const makeDefault = Selector(`[data-workspace-name="${workName}"] [data-tc-make-default]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(workSpace.spaceDropDown)
      .click(workSpace.spaceDropDown)
      .wait(500)
      .hover(viewSpaceName)
      .expect(viewSpaceName.innerText).contains(workName)
      .wait(1000)
      .hover(settingIcon)
      .wait(500)
      .click(settingIcon)
      .wait(1000)
      .hover(makeDefault)
      .wait(500)
      .click(makeDefault)
      .wait(1000);
  }

  async selectWorkSpace(workSpaceName) {
    const selectSpaceList = Selector(`[data-workspace-name="${workSpaceName}"]`);
    await t.wait(1000)
      .hover(workSpace.spaceDropDown)
      .click(workSpace.spaceDropDown)
      .wait(500)
      .hover(selectSpaceList)
      .wait(1000)
      .click(selectSpaceList)
      .wait(1000);
  }
} export default switchWorkSpace
