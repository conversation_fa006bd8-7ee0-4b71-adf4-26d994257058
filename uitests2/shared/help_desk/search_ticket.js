import { Selector, t } from 'testcafe';
import deleteTicket from './delete_tickets';
import golvar from '../Globalvariable';

const golVari = new golvar();
const delTicPage = new deleteTicket();

class searchTicket {

  // async matchTicRecord(ticSearch) {
  //   const ticSubject = Selector(`[data-tc-ticket="${ticSearch}"]`);
  //   await t.hover(delTicPage.ticketTab)
  //     .click(delTicPage.ticketTab)
  //     .wait(2000)
  //     .expect(golVari.currentpageurl()).contains('/help_tickets/')
  //     .wait(1000)
  //     .hover(ticSubject)
  //     .expect(ticSubject.visible).ok()
  // }

  async matchTicRecord(ticSearch) {
    const ticSubject = Selector(`[data-tc-ticket="${ticSearch}"]`);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .hover(ticSubject)
      .expect(ticSubject.visible).ok()
  }

  // async notMatchTicRecord(ticSearch1) {
  //   const ticSubject1 = Selector(`[data-tc-ticket="${ticSearch1}"]`);
  //   await t.hover(delTicPage.ticketTab)
  //     .click(delTicPage.ticketTab)
  //     .wait(2000)
  //     .expect(golVari.currentpageurl()).contains('/help_tickets/')
  //     .wait(500)
  //     .expect(ticSubject1.exists).notOk()
  // }

  async notMatchTicRecord(ticSearch1) {
    const ticSubject1 = Selector(`[data-tc-ticket="${ticSearch1}"]`);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .expect(ticSubject1.exists).notOk()
  }
} export default searchTicket
