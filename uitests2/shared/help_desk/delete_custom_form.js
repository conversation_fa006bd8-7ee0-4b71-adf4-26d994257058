import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addCustomForm from "./add_custom_form";

const naviBar = new navbar();
const golVari = new golvar();
const custForm = new addCustomForm();

class deleteCustomForm {

  constructor() {
    this.archiveBtn = Selector('[data-tc-archieve-custom-form="Help desk"]');
    this.confirmRemove = Selector('[data-tc-remove-attribute]');
    this.statusFilterDrop = Selector('[data-tc-drop-down-filters]');
    this.selectArchieveFilter = Selector('[data-tc-filter-option="Archived"]');
    this.selectActiveFilter = Selector('[data-tc-filter-option="Active"]');
    this.deleteBtn = Selector('[data-tc-delete-custom-form="Help desk"]');
    this.confirmDelete = Selector('[data-tc-save-form-btn="Yes, Remove"]');
    this.cancelDelete = Selector('[data-tc-btn="No"]');
    this.unArchieveBtn = Selector('[data-tc-unarchieve-custom-form="Help desk"]');
    this.cancelUnarchieveBtn = Selector('[data-tc-btn="cancel"]');
  }

  async deleteCustomForms(formName) {
    const viewaddedForm = Selector(`[data-tc-view-form="${formName}"]`);
    const viewFormName = Selector(`[data-tc-form-name="${formName}"]`);
    await t.wait(1000)
    naviBar.navigatetocompanysetting()
    await t.hover(custForm.customFormTab)
      .click(custForm.customFormTab)
      .expect(golVari.currentpageurl()).contains('company/custom_forms')
      .wait(1000)
      .hover(custForm.selectModule)
      .click(custForm.selectModule)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/custom_forms')
      .wait(1000)
      .hover(viewaddedForm)
      .wait(500)
      .expect(viewFormName.innerText).contains(formName)
      .wait(1000)
      .click(viewaddedForm)
      .wait(1000)
      .hover(this.archiveBtn)
      .click(this.archiveBtn)
      .wait(500)
      .hover(this.confirmRemove)
      .click(this.confirmRemove)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/custom_forms')
      .wait(1000)
      .hover(this.statusFilterDrop)
      .click(this.statusFilterDrop)
      .wait(500)
      .hover(this.selectArchieveFilter)
      .click(this.selectArchieveFilter)
      .wait(500)
      .hover(viewaddedForm)
      .wait(500)
      .expect(viewFormName.innerText).contains(formName)
      .wait(1000)
      .click(viewaddedForm)
      .wait(1000)
      .hover(this.deleteBtn)
      .click(this.deleteBtn)
      .wait(1000)
      .wait(500)
      .hover(this.confirmDelete)
      .click(this.confirmDelete)
      .wait(3000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/custom_forms')
      .wait(2000)
      .expect(viewFormName.exists).notOk()
      .wait(2000)
      .hover(this.statusFilterDrop)
      .click(this.statusFilterDrop)
      .wait(500)
      .hover(this.selectActiveFilter)
      .click(this.selectActiveFilter)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/custom_forms')
      .wait(1000)
      .expect(viewFormName.exists).notOk()
      .wait(1000)
  }

  async cancelDeleteUnarchiveForm(formName1) {
    const viewFormName1 = Selector(`[data-tc-form-name="${formName1}"]`);
    await t.wait(1000)
    naviBar.navigatetocompanysetting()
    await t.hover(custForm.customFormTab)
      .click(custForm.customFormTab)
      .expect(golVari.currentpageurl()).contains('company/custom_forms')
      .wait(1000)
      .hover(custForm.selectModule)
      .click(custForm.selectModule)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/custom_forms')
      .wait(1000)
      .hover(viewFormName1)
      .click(viewFormName1)
      .wait(1000)
      .hover(this.archiveBtn)
      .click(this.archiveBtn)
      .wait(500)
      .hover(this.confirmRemove)
      .click(this.confirmRemove)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/custom_forms')
      .wait(1000)
      .hover(this.statusFilterDrop)
      .click(this.statusFilterDrop)
      .wait(500)
      .hover(this.selectArchieveFilter)
      .click(this.selectArchieveFilter)
      .wait(2000)
      .hover(viewFormName1)
      .click(viewFormName1)
      .wait(1000)
      .hover(this.deleteBtn)
      .click(this.deleteBtn)
      .wait(1000)
      .hover(this.cancelDelete)
      .click(this.cancelDelete)
      .wait(1000)
      .expect(this.deleteBtn.visible).ok()
      .wait(1000)
      .expect(this.unArchieveBtn.visible).ok()
      .wait(500)
      .hover(this.unArchieveBtn)
      .click(this.unArchieveBtn)
      .wait(1000)
      .hover(this.cancelUnarchieveBtn)
      .click(this.cancelUnarchieveBtn)
      .wait(1000)
      .expect(this.cancelUnarchieveBtn.visible).notOk()
      .wait(1000)
      .hover(this.unArchieveBtn)
      .click(this.unArchieveBtn)
      .wait(1000)
      .hover(this.confirmRemove)
      .click(this.confirmRemove)
      .wait(1000)
      .hover(this.statusFilterDrop)
      .click(this.statusFilterDrop)
      .wait(500)
      .hover(this.selectActiveFilter)
      .click(this.selectActiveFilter)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('help_tickets/settings/custom_forms')
      .wait(1000)
      .expect(viewFormName1.visible).ok()
      .wait(1000)
  }
} export default deleteCustomForm
