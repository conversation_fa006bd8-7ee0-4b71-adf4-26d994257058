import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addDocument from "./add_document";

const naviBar = new navbar();
const golVari = new golvar();
const docPage = new addDocument();

class editDocument {

  constructor() {
    this.docNameField = Selector('[data-tc-name]');
    this.saveBtn = Selector('[data-tc-save-form-btn="Save document"]');
  }

  async editDocuments(docName, newDocName) {
    const viewDocName = Selector(`[data-tc-view-document="${docName}"]`);
    const viewEditDocName = Selector(`[data-tc-view-document="${newDocName}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(naviBar.resourceNavMenu)
      .click(naviBar.resourceNavMenu)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/articles')
      .wait(1000)
      .hover(docPage.docTab)
      .click(docPage.docTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/documents')
      .hover(docPage.searchField)
      .wait(500)
      .typeText(docPage.searchField, docName, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .expect(viewDocName.innerText).contains(docName)
      .hover(viewDocName)
      .click(viewDocName)
      .wait(1000)
      .typeText(this.docNameField, newDocName, { replace: true })
      .wait(1000)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(2000)
      .hover(docPage.searchField)
      .typeText(docPage.searchField, newDocName, { replace: true })
      .pressKey('enter')
      .wait(2000)
      .expect(viewEditDocName.innerText).contains(newDocName)
      .wait(1000)
  }
} export default editDocument
