import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import editTickets from "./edit_tickets";
import deleteTicket from "./delete_tickets";


const naviBar = new navbar();
const golVari = new golvar();
const editTicPage = new editTickets();
const delTic = new deleteTicket();

class ticketTasks {

  constructor() {
    this.addStaffBtn = Selector('[data-tc-add-impacted-user]');
    this.removeSelectStaff = Selector('[data-tc-remove-icon="staff user"]');
    this.backNavigationBtn = Selector('[data-tc-back-tasks-btn]');
    this.notifyPopup = Selector('.notification-content');
    this.completedLabel = Selector('[data-tc-completed-label]');
    this.completionDateError = Selector('[data-tc-error="completion date"]');
    this.selectAllBtn = Selector('[data-tc-btn="select all tasks"]');
    this.unselectAllBtn = Selector('[data-tc-btn="Unselect all"]');
    this.parentTaskBtn = Selector('[data-tc-btn="add to parent task"]');
    this.viewPressLabelTask = Selector('[data-tc-press-selecting-merging]');
    this.taskWidget = Selector('[data-tc-open-tasks-tab]');
    this.parentModal = Selector('[data-tc-modal="select parent task"]');
    this.parentDropDown = Selector('[data-tc-drop-down="select parent"]');
    this.selectParent = this.parentDropDown.find('option');
    this.saveBtn = Selector('[data-tc-save-btn="merge task"]');
    this.cancelBtn = Selector('[data-tc-cancel-btn="merge task"]')
  }

  async addTask(taskDescription, taskPriority, taskDueTo, taskCompleteTo, taskUser) {
    const selectTaskPrority = Selector(`[data-tc-tasks-priorty="${taskPriority}"]`);
    const viewDescription = Selector(`[data-tc-view-task-description="${taskDescription}"]`);
    const viewAssignee = Selector(`[data-tc-tasks="${taskDescription}"] [data-tc-member-label]`);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(editTicPage.tasksTab)
      .click(editTicPage.tasksTab)
      .wait(1000)
      .click(editTicPage.addTaskBtn)
      .wait(1000)
      .hover(editTicPage.taskDescription)
      .typeText(editTicPage.taskDescription, taskDescription, { paste: true })
      .wait(1000)
      .hover(selectTaskPrority)
      .click(selectTaskPrority)
      .wait(500)
      .hover(editTicPage.taskDueDate)
      .typeText(editTicPage.taskDueDate, taskDueTo, { paste: true })
      .pressKey('enter')
      .hover(editTicPage.taskCompletedDate)
      .typeText(editTicPage.taskCompletedDate, taskCompleteTo, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .hover(editTicPage.taskAssignee)
      .click(editTicPage.taskAssignee)
      .wait(500)
      .typeText(editTicPage.inputTaskAssignee, taskUser, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .hover(editTicPage.saveTask)
      .click(editTicPage.saveTask)
      .wait(1000)
      .hover(viewDescription)
      .wait(1000)
      .expect(viewDescription.innerText).contains(taskDescription)
      .wait(1000)
      .hover(viewAssignee)
      .wait(1000)
      .expect(viewAssignee.innerText).contains(taskUser)
      .wait(1000)
  }

  async editTask(taskDescription1, editDescription, taskPriority1, taskDueTo1, taskCompleteTo1, taskUser1) {
    const selectTaskPrority1 = Selector(`[data-tc-tasks-priorty="${taskPriority1}"]`);
    const editTaskBtn = Selector(`[data-tc-tasks="${taskDescription1}"] [data-tc-edit-task-btn]`);
    const viewEditedDescription = Selector(`[data-tc-view-task-description="${editDescription}"]`);
    const viewEditedAssignee = Selector(`[data-tc-tasks="${editDescription}"] [data-tc-member-label]`);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(editTicPage.tasksTab)
      .click(editTicPage.tasksTab)
      .wait(1000)
      .hover(editTaskBtn)
      .wait(1000)
      .click(editTaskBtn)
      .wait(1000)
      .hover(editTicPage.taskDescription)
      .typeText(editTicPage.taskDescription, editDescription, { replace: true })
      .wait(1000)
      .hover(selectTaskPrority1)
      .click(selectTaskPrority1)
      .wait(500)
      .hover(editTicPage.taskDueDate)
      .typeText(editTicPage.taskDueDate, taskDueTo1, { replace: true })
      .pressKey('enter')
      .hover(editTicPage.taskCompletedDate)
      .typeText(editTicPage.taskCompletedDate, taskCompleteTo1, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(this.removeSelectStaff)
      .wait(1000)
      .click(this.removeSelectStaff)
      .wait(1000)
      .hover(editTicPage.taskAssignee)
      .click(editTicPage.taskAssignee)
      .wait(500)
      .typeText(editTicPage.inputTaskAssignee, taskUser1, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(editTicPage.saveTask)
      .click(editTicPage.saveTask)
      .wait(1000)
      .hover(viewEditedDescription)
      .wait(1000)
      .expect(viewEditedDescription.innerText).contains(editDescription)
      .wait(1000)
      .hover(viewEditedAssignee)
      .wait(1000)
      .expect(viewEditedAssignee.innerText).contains(taskUser1)
      .wait(1000)
  }

  async navigateBackToTask(subject2) {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .hover(delTic.searchField)
      .typeText(delTic.searchField, subject2, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(delTic.searchedTicket)
      .expect(delTic.searchedTicket.innerText).contains(subject2)
      .wait(1000)
      .click(delTic.searchedTicket)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(editTicPage.tasksTab)
      .click(editTicPage.tasksTab)
      .wait(1000)
      .click(editTicPage.addTaskBtn)
      .wait(1000)
      .hover(this.backNavigationBtn)
      .wait(1000)
      .click(this.backNavigationBtn)
      .wait(1000)
      .hover(editTicPage.addTaskBtn)
      .wait(1000)
      .expect(editTicPage.addTaskBtn.exists).ok()
      .wait(1000)
  }

  async dragTask(task1, task2) {
    const sourceTask = Selector(`[data-tc-task-cursor="${task1}"]`);
    const targetedTask = Selector(`[data-tc-task-cursor="${task2}"]`);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(editTicPage.tasksTab)
      .click(editTicPage.tasksTab)
      .wait(1000)
      .hover(sourceTask)
      .wait(1000)
      .dragToElement(sourceTask, targetedTask)
      .wait(1000)
      .expect(this.notifyPopup.innerText).contains('Successfully updated tasks')
      .wait(2000)
  }

  async markCompleteTask(taskDescription3, taskPriority3, taskDueTo3, taskUser3) {
    const selectTaskPrority3 = Selector(`[data-tc-tasks-priorty="${taskPriority3}"]`);
    const viewDescription3 = Selector(`[data-tc-view-task-description="${taskDescription3}"]`);
    const viewAssignee3 = Selector(`[data-tc-tasks="${taskDescription3}"] [data-tc-member-label]`);
    const markBtn = Selector(`[data-tc-mark-complete="${taskDescription3}"]`);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(editTicPage.tasksTab)
      .click(editTicPage.tasksTab)
      .wait(1000)
      .click(editTicPage.addTaskBtn)
      .wait(1000)
      .hover(editTicPage.taskDescription)
      .typeText(editTicPage.taskDescription, taskDescription3, { paste: true })
      .wait(1000)
      .hover(selectTaskPrority3)
      .click(selectTaskPrority3)
      .wait(500)
      .hover(editTicPage.taskDueDate)
      .typeText(editTicPage.taskDueDate, taskDueTo3, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .hover(editTicPage.taskAssignee)
      .click(editTicPage.taskAssignee)
      .wait(500)
      .typeText(editTicPage.inputTaskAssignee, taskUser3, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .hover(editTicPage.saveTask)
      .click(editTicPage.saveTask)
      .wait(1000)
      .hover(viewDescription3)
      .wait(1000)
      .expect(viewDescription3.innerText).contains(taskDescription3)
      .wait(1000)
      .hover(viewAssignee3)
      .wait(1000)
      .expect(viewAssignee3.innerText).contains(taskUser3)
      .wait(1000)
      .hover(markBtn)
      .wait(1000)
      .click(markBtn)
      .wait(1000)
      .expect(this.completedLabel.innerText).contains('Completed:')
      .wait(1000)
  }

  async tasksValidation(subject5, taskDescription2, taskDueTo2) {
    const taskDescrError = Selector('[data-tc-new-task-form] span');
    const selectNextMonth = Selector('[data-tc-task-completed-at] .mx-calendar-header .mx-icon-next-month');
    const selectDate = Selector('[data-tc-task-completed-at] .cell.cur-month');
    const calendarOkBtn = Selector('[data-tc-task-completed-at] .mx-datepicker-btn.mx-datepicker-btn-confirm');
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .hover(delTic.searchField)
      .typeText(delTic.searchField, subject5, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(delTic.searchedTicket)
      .expect(delTic.searchedTicket.innerText).contains(subject5)
      .wait(1000)
      .click(delTic.searchedTicket)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(editTicPage.tasksTab)
      .click(editTicPage.tasksTab)
      .wait(1000)
      .click(editTicPage.addTaskBtn)
      .wait(1000)
      .hover(editTicPage.saveTask)
      .click(editTicPage.saveTask)
      .wait(1000)
      .expect(this.notifyPopup.innerText).contains('Please correct the highlighted errors before submitting.')
      .wait(1000)
      .hover(taskDescrError)
      .expect(taskDescrError.innerText).contains('The description field is required')
      .wait(1000)
      .hover(editTicPage.taskDescription)
      .typeText(editTicPage.taskDescription, taskDescription2, { replace: true })
      .wait(1000)
      .hover(editTicPage.taskDueDate)
      .typeText(editTicPage.taskDueDate, taskDueTo2, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(editTicPage.taskCompletedDate)
      .click(editTicPage.taskCompletedDate)
      .wait(1000)
      .hover(selectNextMonth)
      .wait(1000)
      .click(selectNextMonth)
      .wait(1000)
      .click(selectDate.withText('1'))
      .wait(1000)
      .click(calendarOkBtn)
      .wait(1000)
      .expect(this.completionDateError.innerText).contains('Completion date cannot be in the future.')
      .wait(1000)
  }

  async taskSelectOrDeselectall(taskDescription4) {
    const task = Selector(`[data-tc-view-task-description="${taskDescription4}"]`);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(editTicPage.tasksTab)
      .click(editTicPage.tasksTab)
      .wait(1000)
      .hover(task)
      .click(task, { modifiers: { ctrl: true } })
      .wait(2000)
      .hover(this.selectAllBtn)
      .wait(1000)
      .click(this.selectAllBtn)
      .wait(2000)
      .expect(this.selectAllBtn.exists).notOk()
      .wait(1000)
      .hover(this.unselectAllBtn)
      .wait(1000)
      .click(this.unselectAllBtn)
      .wait(1000)
      .expect(this.viewPressLabelTask.innerText).contains('Press')
      .wait(1000)
  }

  async singleMergeTask(taskDescription5, parentTask, switcher) {
    const tasks = Selector(`[data-tc-view-task-description="${taskDescription5}"]`);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(editTicPage.tasksTab)
      .click(editTicPage.tasksTab)
      .wait(1000)
      .hover(tasks)
      .click(tasks, { modifiers: { ctrl: true } })
      .wait(2000)
      .hover(this.parentTaskBtn)
      .wait(1000)
      .click(this.parentTaskBtn)
      .wait(2000)
      .expect(this.parentModal.exists).ok()
      .wait(1000);
    if (switcher == 'save') {
      await t.wait(1000)
        .click(this.parentDropDown)
        .wait(1000)
        .click(this.selectParent.withText(parentTask))
        .wait(1000)
        .hover(this.saveBtn)
        .wait(1000)
        .click(this.saveBtn)
        .wait(1000)
        .expect(this.notifyPopup.innerText).contains('Task merged updated successfully')
        .wait(2000);
    }
    else {
      await t.wait(1000)
        .hover(this.cancelBtn)
        .wait(1000)
        .click(this.cancelBtn)
        .wait(1000)
        .hover(this.unselectAllBtn)
        .wait(1000)
        .click(this.unselectAllBtn)
        .wait(2000);
    }
  }

  async multiMergeTask(taskDescription6, taskDescription7, parentTask1) {
    const task1 = Selector(`[data-tc-view-task-description="${taskDescription6}"]`);
    const task2 = Selector(`[data-tc-view-task-description="${taskDescription7}"]`);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(editTicPage.tasksTab)
      .click(editTicPage.tasksTab)
      .wait(1000)
      .hover(task1)
      .click(task1, { modifiers: { ctrl: true } })
      .wait(2000)
      .hover(task2)
      .click(task2, { modifiers: { ctrl: true } })
      .wait(2000)
      .hover(this.parentTaskBtn)
      .wait(1000)
      .click(this.parentTaskBtn)
      .wait(2000)
      .expect(this.parentModal.exists).ok()
      .wait(1000)
    await t.wait(1000)
      .click(this.parentDropDown)
      .wait(1000)
      .click(this.selectParent.withText(parentTask1))
      .wait(1000)
      .hover(this.saveBtn)
      .wait(1000)
      .click(this.saveBtn)
      .wait(1000)
      .expect(this.notifyPopup.innerText).contains('Task merged updated successfully')
      .wait(2000)
  }

  async navigateToTask(subject9) {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .hover(delTic.searchField)
      .typeText(delTic.searchField, subject9, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(delTic.searchedTicket)
      .expect(delTic.searchedTicket.innerText).contains(subject9)
      .wait(1000)
      .click(delTic.searchedTicket)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(this.taskWidget)
      .wait(1000)
      .click(this.taskWidget)
      .wait(2000)
      .expect(editTicPage.addTaskBtn.exists).ok()
      .wait(1000)
  }

  async unLinkMergeTask(taskDescription8) {
    const taskD = Selector(`[data-tc-view-task-description="${taskDescription8}"]`);
    const unlinkBtn = Selector(`[data-tc-task-unlink="${taskDescription8}"]`);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(editTicPage.tasksTab)
      .click(editTicPage.tasksTab)
      .wait(1000)
      .hover(taskD)
      .click(taskD, { modifiers: { ctrl: true } })
      .wait(2000)
      .expect(unlinkBtn.exists).ok()
      .wait(1000)
      .hover(unlinkBtn)
      .wait(1000)
      .click(unlinkBtn)
      .wait(1000)
      .expect(this.notifyPopup.innerText).contains('Subtask removed successfully')
      .wait(1000)
      .expect(unlinkBtn.exists).notOk()
      .wait(2000)
  }
} export default ticketTasks
