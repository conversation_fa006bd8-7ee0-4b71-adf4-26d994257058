import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";

const naviBar = new navbar();
const golVari = new golvar();

class markCommentResolution {
  constructor() {
    this.ticketTab = Selector(`${golVari.horizontalMenu} [data-tc-tickets]`);
    this.commentBtn = Selector('[data-tc-add-new-comment]');
    this.addTicketBtn = Selector(`${golVari.horizontalMenu} [data-tc-add="helpdesk"]`);
    this.subjectField = Selector('[data-tc-text-field="subject"] [data-tc-task-value]');
    this.createTicketBtn = Selector('[data-tc-save-form-btn="Create ticket"]');
    this.commentField = Selector('[data-tc-input-editor="ticketCommentundefined"]');
    this.markResolution = Selector('[data-tc-mark-resolution]');
    this.markButtonDisable = Selector('[data-tc-disable-button="true"]');
  }

  async commentResolution(subject, description, status) {
    const viewStatus = Selector(`[data-tc-view-status="${status}"]`);
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .hover(this.addTicketBtn)
      .click(this.addTicketBtn)
      .wait(1000)
      .hover(this.subjectField)
      .typeText(this.subjectField, subject, { paste: true })
      .wait(2000)
      .hover(this.createTicketBtn)
      .click(this.createTicketBtn)
      .wait(4000)
      .hover(this.commentBtn)
      .click(this.commentBtn)
      .wait(500)
      .expect(this.markButtonDisable.visible).ok()
      .wait(500)
      .typeText(this.commentField, description, { paste: true })
      .wait(1000)
      .expect(this.markButtonDisable.exists).notOk()
      .wait(500)
      .hover(this.markResolution)
      .click(this.markResolution)
      .wait(10000)
      .hover(viewStatus)
      .expect(viewStatus.innerText).contains(status);
  }
}
export default markCommentResolution;
