import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addTicket from "./add_tickets";
import editTickets from "./edit_tickets";
import markCommentResolution from "./mark_comment_resolution";

const naviBar = new navbar();
const golVari = new golvar();
const addTicPage = new addTicket();
const markResPage = new markCommentResolution();
const editTicPage = new editTickets();

class dashboardOperation {

  constructor() {
    this.ticketStatusSection = Selector('[data-tc-section="tickets by status"]');
    this.statusTotalCount = this.ticketStatusSection.find('[data-tc-inner-text]');
    this.byStatusLabel = this.ticketStatusSection.find('h5');
    this.openStatusSection = Selector('[data-tc-section="Open"]');
    this.openStatusLabel = Selector('[data-tc-label="Open"]');
    this.inprogressStatusSection = Selector('[data-tc-section="In Progress"]');
    this.inprogressStatusLabel = Selector('[data-tc-label="In Progress"]');
    this.closeStatusSection = Selector('[data-tc-section="Closed"]');
    this.closeStatusLabel = Selector('[data-tc-label="Closed"]');
    this.showCloseTicBtn = Selector('[data-tc-btn="show close tickets"]');
    this.sourceTicSection = Selector('[data-tc-card-template="tickets by source"]');
    this.sourceTicLabel = Selector('[data-tc-title="Tickets by source"]');
    this.checkTotalSourceTic = this.sourceTicSection.find('[data-tc-inner-text]');
    this.manualSourceSection = Selector('[data-tc-section="Manually Added"]');
    this.manualSourceLabel = Selector('[data-tc-label="Manually Added"]');
    this.customerParentDiv = Selector('[data-tc-div="customer satisfaction"]');
    this.totalSatisfy = this.customerParentDiv.find('[data-tc-inner-text]');
    this.greatParentDiv = Selector('[data-tc-section="Great"]');
    this.greatOptLabel = this.greatParentDiv.find('[data-tc-label="Great"]');
    this.notGoodParentDiv = Selector('[data-tc-section="Not So Good"]');
    this.notGoodOptLabel = this.notGoodParentDiv.find('[data-tc-label="Not So Good"]');
    this.ticketPrioritySection = Selector('[data-tc-title="Tickets by priority"]');
    this.priorityTotalCount = this.ticketPrioritySection.find('[data-tc-inner-text]');
    this.byPriorityLabel = this.ticketPrioritySection.find('h5');
    this.lowPrioritySection = Selector('[data-tc-section="Low"]');
    this.lowPriorityLabel = Selector('[data-tc-label="Low"]');
    this.highPrioritySection = Selector('[data-tc-section="High"]');
    this.highPriorityLabel = Selector('[data-tc-label="High"]');
    this.mediumPrioritySection = Selector('[data-tc-section="Medium"]');
    this.mediumPriorityLabel = Selector('[data-tc-label="Medium"]');
    this.addTicketBtn = Selector('[data-tc-btn="add new ticket"]');
  }
  async navigateToDashboard() {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500);
  }

  async checkTicketByStatus(totalCount, defaultStatusCount, customStatusLabel, customStatusCount) {
    const [countWithoutCloseTic, countWithCloseTic] = totalCount.split(':');
    const [openCount, inprogressCount, closeCount] = defaultStatusCount.split(':');
    const [customLabelA, customLabelB, customLabelC] = customStatusLabel.split(':');
    const [customCountA, customCountB, customCountC] = customStatusCount.split(':');
    const checkOpenCount = this.openStatusSection.find(`[data-tc-data="${openCount}"]`);
    const checkInprogressCount = this.inprogressStatusSection.find(`[data-tc-data="${inprogressCount}"]`);
    const checkCloseCount = this.closeStatusSection.find(`[data-tc-data="${closeCount}"]`);
    const customASection = Selector(`[data-tc-section="${customLabelA}"]`);
    const customALabel = Selector(`[data-tc-label="${customLabelA}"]`);
    const checkCustomACount = customASection.find(`[data-tc-data="${customCountA}"]`);
    const customBSection = Selector(`[data-tc-section="${customLabelB}"]`);
    const customBLabel = Selector(`[data-tc-label="${customLabelB}"]`);
    const checkCustomBCount = customBSection.find(`[data-tc-data="${customCountB}"]`);
    const customCSection = Selector(`[data-tc-section="${customLabelC}"]`);
    const customCLabel = Selector(`[data-tc-label="${customLabelC}"]`);
    const checkCustomCCount = customCSection.find(`[data-tc-data="${customCountC}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .expect(this.ticketStatusSection.exists).ok('Check the ticket by status on dashboard is present or not')
      .expect(this.byStatusLabel.innerText).contains('Tickets by status')
      .wait(500)
      .expect(this.statusTotalCount.innerText).contains(`${countWithoutCloseTic}\nAll tickets`)
      .wait(500)
      .hover(this.openStatusSection)
      .hover(this.openStatusLabel)
      .expect(this.openStatusLabel.innerText).contains('Open')
      .expect(checkOpenCount.innerText).contains(openCount)
      .wait(500)
      .hover(this.inprogressStatusSection)
      .hover(this.inprogressStatusLabel)
      .expect(this.inprogressStatusLabel.innerText).contains('In Progress')
      .expect(checkInprogressCount.innerText).contains(inprogressCount)
      .wait(500)
      .hover(customASection)
      .hover(customALabel)
      .expect(customALabel.innerText).contains(customLabelA)
      .expect(checkCustomACount.innerText).contains(customCountA)
      .wait(500)
      .hover(customBSection)
      .hover(customBLabel)
      .expect(customBLabel.innerText).contains(customLabelB)
      .expect(checkCustomBCount.innerText).contains(customCountB)
      .wait(500)
      .hover(customCSection)
      .hover(customCLabel)
      .expect(customCLabel.innerText).contains(customLabelC)
      .expect(checkCustomCCount.innerText).contains(customCountC)
      .wait(500)
      .hover(this.showCloseTicBtn)
      .click(this.showCloseTicBtn)
      .wait(1000)
      .expect(this.statusTotalCount.innerText).contains(`${countWithCloseTic}\nAll tickets`)
      .wait(500)
      .hover(this.openStatusSection)
      .hover(this.openStatusLabel)
      .expect(this.openStatusLabel.innerText).contains('Open')
      .expect(checkOpenCount.innerText).contains(openCount)
      .wait(500)
      .hover(this.inprogressStatusSection)
      .hover(this.inprogressStatusLabel)
      .expect(this.inprogressStatusLabel.innerText).contains('In Progress')
      .expect(checkInprogressCount.innerText).contains(inprogressCount)
      .wait(500)
      .hover(this.closeStatusSection)
      .hover(this.closeStatusLabel)
      .expect(this.closeStatusLabel.innerText).contains('Closed')
      .expect(checkCloseCount.innerText).contains(closeCount)
      .wait(500)
      .hover(customASection)
      .hover(customALabel)
      .expect(customALabel.innerText).contains(customLabelA)
      .expect(checkCustomACount.innerText).contains(customCountA)
      .wait(500)
      .hover(customBSection)
      .hover(customBLabel)
      .expect(customBLabel.innerText).contains(customLabelB)
      .expect(checkCustomBCount.innerText).contains(customCountB)
      .wait(500)
      .hover(customCSection)
      .hover(customCLabel)
      .expect(customCLabel.innerText).contains(customLabelC)
      .expect(checkCustomCCount.innerText).contains(customCountC)
      .wait(500)
      .hover(this.showCloseTicBtn)
      .click(this.showCloseTicBtn)
      .wait(500);
  }

  async checkTicketByPriority(totalCount, defaultPriorityCount, closedCountTickets) {
    const [countWithoutCloseTic, countWithCloseTic] = totalCount.split(':');
    const [highCount, MediumCount, lowCount] = defaultPriorityCount.split(':');
    const [highCountClose, MediumCountClose, lowCountClose] = closedCountTickets.split(':');
    const checkHighCount = this.highPrioritySection.find(`[data-tc-data="${highCount}"]`);
    const checkMediumCount = this.mediumPrioritySection.find(`[data-tc-data="${MediumCount}"]`);
    const checklowCount = this.lowPrioritySection.find(`[data-tc-data="${lowCount}"]`);
    const checkHighCloseCount = this.highPrioritySection.find(`[data-tc-data="${highCountClose}"]`);
    const checkMediumCloseCount = this.mediumPrioritySection.find(`[data-tc-data="${MediumCountClose}"]`);
    const checkLowCloseCount = this.lowPrioritySection.find(`[data-tc-data="${lowCountClose}"]`);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .expect(this.ticketPrioritySection.exists).ok('Check the ticket by priority on dashboard is present or not')
      .expect(this.byPriorityLabel.innerText).eql('Tickets by priority')
      .wait(500)
      .expect(this.priorityTotalCount.innerText).eql(`${countWithoutCloseTic}\nAll tickets`)
      .wait(500)
      .hover(this.lowPrioritySection)
      .hover(this.lowPriorityLabel)
      .expect(this.lowPriorityLabel.innerText).eql('Low ')
      .expect(checklowCount.innerText).eql(lowCount)
      .wait(500)
      .hover(this.mediumPrioritySection)
      .hover(this.mediumPriorityLabel)
      .expect(this.mediumPriorityLabel.innerText).eql('Medium ')
      .expect(checkMediumCount.innerText).eql(MediumCount)
      .wait(500)
      .hover(this.highPrioritySection)
      .hover(this.highPriorityLabel)
      .expect(this.highPriorityLabel.innerText).eql('High ')
      .expect(checkHighCount.innerText).eql(highCount)
      .wait(500)
      .hover(this.showCloseTicBtn)
      .click(this.showCloseTicBtn)
      .wait(1000)
      .expect(this.priorityTotalCount.innerText).eql(`${countWithCloseTic}\nAll tickets`)
      .wait(500)
      .expect(this.lowPriorityLabel.innerText).eql('Low ')
      .expect(checkLowCloseCount.innerText).eql(lowCountClose)
      .wait(500)
      .hover(this.mediumPrioritySection)
      .hover(this.mediumPriorityLabel)
      .expect(this.mediumPriorityLabel.innerText).eql('Medium ')
      .expect(checkMediumCloseCount.innerText).eql(MediumCountClose)
      .wait(500)
      .hover(this.highPrioritySection)
      .hover(this.highPriorityLabel)
      .expect(this.highPriorityLabel.innerText).eql('High ')
      .expect(checkHighCloseCount.innerText).eql(highCountClose)
      .wait(500)
      .hover(this.showCloseTicBtn)
      .click(this.showCloseTicBtn)
      .wait(500);
  }

  async checkTicketBySource(manualTicCount) {
    const [countWithoutCloseTic, countWithCloseTic] = manualTicCount.split(':');
    const viewManualCountNotClose = this.manualSourceSection.find(`[data-tc-data="${countWithoutCloseTic}"]`);
    const viewManualCountClose = this.manualSourceSection.find(`[data-tc-data="${countWithCloseTic}"]`);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(this.sourceTicLabel)
      .expect(this.sourceTicLabel.innerText).eql('Tickets by source')
      .wait(500)
      .expect(this.checkTotalSourceTic.innerText).eql(`${countWithoutCloseTic}\nAll tickets`)
      .wait(500)
      .expect(this.manualSourceSection.exists).ok('Check the manually added is visible or not')
      .hover(this.manualSourceLabel)
      .expect(viewManualCountNotClose.innerText).eql(countWithoutCloseTic)
      .wait(500)
      .hover(this.showCloseTicBtn)
      .click(this.showCloseTicBtn)
      .wait(500)
      .expect(this.manualSourceSection.exists).ok('Check the manually added is visible or not')
      .hover(this.manualSourceLabel)
      .expect(viewManualCountClose.innerText).eql(countWithCloseTic)
      .hover(this.showCloseTicBtn)
      .click(this.showCloseTicBtn)
      .wait(500);
  }

  async checkCustomerSatisfaction(greatNumber, notGoodNumber, totalPercentage) {
    const totalGreat = this.greatParentDiv.find(`[data-tc-data="${greatNumber}"]`);
    const totalNotGood = this.notGoodParentDiv.find(`[data-tc-data="${notGoodNumber}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(this.customerParentDiv)
      .expect(this.totalSatisfy.innerText).eql(`${totalPercentage}%\nSatisfaction`)
      .wait(500)
      .hover(this.greatParentDiv)
      .expect(this.greatOptLabel.innerText).eql('Great')
      .expect(totalGreat.innerText).eql(greatNumber)
      .hover(this.notGoodParentDiv)
      .expect(this.notGoodOptLabel.innerText).eql('Not So Good')
      .expect(totalNotGood.innerText).eql(notGoodNumber)
      .wait(500)
      .hover(this.showCloseTicBtn)
      .click(this.showCloseTicBtn)
      .wait(500)
      .expect(this.totalSatisfy.innerText).eql(`${totalPercentage}%\nSatisfaction`)
      .hover(this.greatParentDiv)
      .expect(this.greatOptLabel.innerText).eql('Great')
      .expect(totalGreat.innerText).eql(greatNumber)
      .hover(this.notGoodParentDiv)
      .expect(this.notGoodOptLabel.innerText).eql('Not So Good')
      .expect(totalNotGood.innerText).eql(notGoodNumber)
      .wait(500)
      .hover(this.showCloseTicBtn)
      .click(this.showCloseTicBtn);
  }

  async addTicketFromDashboard(subject, priority, status, assignedTo, comments) {
    const [commentNumber, commentText] = comments.split(':');
    const viewSubject = Selector(`[data-tc-subject="${subject}"]`);
    const viewPriority = Selector(`[data-tc-priority="${priority}"]`);
    const viewStatus = Selector(`[data-tc-status="${status}"]`);
    const viewAssignedTo = Selector(`[data-tc-assigned-to="${assignedTo}"]`);
    const viewComments = Selector(`[data-tc-text="${commentNumber}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .hover(this.addTicketBtn)
      .click(this.addTicketBtn)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/new')
      .wait(500)
      .hover(addTicPage.subjectField)
      .typeText(addTicPage.subjectField, subject, { paste: true })
      .hover(addTicPage.priorityDropDown)
      .click(addTicPage.priorityDropDown)
      .hover(addTicPage.selectPriority.withText(priority))
      .click(addTicPage.selectPriority.withText(priority))
      .hover(addTicPage.statusDropDown)
      .click(addTicPage.statusDropDown)
      .wait(500)
      .hover(addTicPage.selectStatus.withText(status))
      .click(addTicPage.selectStatus.withText(status))
      .wait(500)
      .hover(addTicPage.assignedToDropDown)
      .click(addTicPage.assignedToDropDown)
      .wait(500)
      .hover(addTicPage.selectAssignedTo.withText(assignedTo))
      .click(addTicPage.selectAssignedTo.withText(assignedTo))
      .wait(500)
      .hover(addTicPage.createTicketBtn)
      .click(addTicPage.createTicketBtn)
      .wait(4000)
      .hover(addTicPage.viewSubject)
      .expect(addTicPage.viewSubject.innerText).contains(subject)
      .hover(markResPage.commentBtn)
      .click(markResPage.commentBtn)
      .wait(500)
      .typeText(markResPage.commentField, commentText, { paste: true })
      .wait(500)
      .hover(editTicPage.saveComment)
      .click(editTicPage.saveComment)
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .hover(viewSubject)
      .expect(viewSubject.innerText).eql(subject)
      .expect(viewPriority.innerText).eql(` ${priority}`)
      .expect(viewStatus.innerText).eql(` ${status}`)
      .expect(viewAssignedTo.innerText).eql(assignedTo)
      .expect(viewComments.innerText).eql(commentNumber)
      .wait(500);
  }
} export default dashboardOperation;
