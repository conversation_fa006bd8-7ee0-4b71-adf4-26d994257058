import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";

const naviBar = new navbar();
const golVari = new golvar();

class quickView {
  constructor() {
    this.titleCreateElement = '[data-tc-title="create a new quick view"]';
    this.titleEditElement = '[data-tc-title="edit quick view"]';
    this.ticketIndex = Selector(`${golVari.horizontalMenu} [data-tc-tickets]`);
    this.quickViewDropDown = Selector('[data-tc-quick-view]');
    this.allActiveQuickView = Selector('[data-tc-quick-view-option="All Active Tickets"]');
    this.myActiveQuickView = Selector('[data-tc-quick-view-option="My Active Tickets"]');
    this.unAssignedQuickView = Selector('[data-tc-quick-view-option="Unassigned"]');
    this.closedQuickView = Selector('[data-tc-quick-view-option="Closed"]');
    this.statusPill = Selector('[data-tc-active-filter-label="Status"]');
    this.createIcon = Selector('[data-tc-icon="create quick view"]');
    this.editIcon = Selector('[data-tc-icon="edit quick view"]');
    this.viewCreateTitle = Selector(`${this.titleCreateElement} h2`);
    this.viewEditTitle = Selector(`${this.titleEditElement} h2`);
    this.inputFilterName = Selector('[data-tc-field="quick view name"]');
    this.addFilterIcon = Selector('[data-tc-icon="add filter"]');
    this.selectFilterDropDown = Selector('[data-tc-field="select filters"]');
    this.selectOptionDropDown = Selector('[data-tc-field="select filter option"]');
    this.checkMarkBtn = Selector('[data-tc-btn="check filters"]');
    this.previewBtn = Selector('[data-tc-btn="preview btn"]');
    this.saveBtn = Selector('[data-tc-btn="save filters"]');
    this.tableView = Selector('[data-tc-table="tickets filters"]');
    this.notifyPopup = Selector('.notification-title');
    this.closeModalBtn = Selector(`${this.titleEditElement} .sweet-action-close`);
  }

  async viewQuickViewLabel(quickViewText) {
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(this.ticketIndex)
      .click(this.ticketIndex)
      .wait(1000)
      .hover(this.quickViewDropDown)
      .expect(this.quickViewDropDown.exists).ok()
      .expect(this.quickViewDropDown.innerText).eql(quickViewText);
  }

  async viewQuickViewFilters() {
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(this.ticketIndex)
      .click(this.ticketIndex)
      .wait(1000)
      .hover(this.myActiveQuickView)
      .expect(this.myActiveQuickView.exists).ok()
      .expect(this.allActiveQuickView.exists).ok()
      .expect(this.unAssignedQuickView.exists).ok()
      .expect(this.closedQuickView.exists).ok()
      .hover(this.closedQuickViewQuickView);
  }

  async applyQuickViewFilters() {
    await t
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(2000)
      .hover(this.ticketIndex)
      .click(this.ticketIndex)
      .wait(1000)
      .hover(this.myActiveQuickView)
      .click(this.myActiveQuickView)
      .hover(this.allActiveQuickView)
      .click(this.allActiveQuickView)
      .hover(this.unAssignedQuickView)
      .click(this.unAssignedQuickView)
      .hover(this.closedQuickView)
      .click(this.closedQuickView)
      .wait(100)
      .click(this.closedQuickView);
  }

  async createQuickViewFilters(name, filters, filterOptions) {
    const [asset, contract, vendor, telecom, location, people, createdBy] = filters.split(':');
    const [assetValue, contractValue, vendorValue, telecomValue, locationValue, peopleValue,
      createdByValue] = filterOptions.split(':');
    const selectAsset = this.selectFilterDropDown.find('li').withText(asset);
    const selectAssetOption = this.selectOptionDropDown.find('li').withText(assetValue);
    const selectContract = this.selectFilterDropDown.find('li').withText(contract);
    const selectContractOption = this.selectOptionDropDown.find('li').withText(contractValue);
    const selectVendor = this.selectFilterDropDown.find('li').withText(vendor);
    const selectVendorOption = this.selectOptionDropDown.find('li').withText(vendorValue);
    const selectTelecom = this.selectFilterDropDown.find('li').withText(telecom);
    const selectTelecomOption = this.selectOptionDropDown.find('li').withText(telecomValue);
    const selectLocation = this.selectFilterDropDown.find('li').withText(location);
    const selectLocationOption = this.selectOptionDropDown.find('li').withText(locationValue);
    const selectPeople = this.selectFilterDropDown.find('li').withText(people);
    const selectPeopleOption = this.selectOptionDropDown.find('li').withText(peopleValue);
    const selectCreatedby = this.selectFilterDropDown.find('li').withText(createdBy);
    const selectCreatedbyOption = this.selectOptionDropDown.find('li').withText(createdByValue);
    const viewAssetFilter = Selector(`[data-tc-view-filter="${asset}"]`);
    const viewContractFilter = Selector(`[data-tc-view-filter="${contract}"]`);
    const viewVendorFilter = Selector(`[data-tc-view-filter="${vendor}"]`);
    const viewTelecomFilter = Selector(`[data-tc-view-filter="${telecom}"]`);
    const viewLocationFilter = Selector(`[data-tc-view-filter="${location}"]`);
    const viewPeopleFilter = Selector(`[data-tc-view-filter="${people}"]`);
    const viewCreatedbyFilter = Selector(`[data-tc-view-filter="${createdBy}"]`);
    const viewQuickName = Selector(`[data-tc-quick-view-option="${name}"]`);

    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .hover(this.createIcon)
      .click(this.createIcon)
      .wait(500)
      .expect(this.viewCreateTitle.innerText).eql('Create a new Quick View')
      .typeText(this.inputFilterName, name, { paste: true })
      .wait(500)
      .hover(this.addFilterIcon)
      .click(this.addFilterIcon)
      .wait(200)
      .hover(this.selectFilterDropDown)
      .click(this.selectFilterDropDown)
      .wait(200)
      .hover(selectAsset)
      .click(selectAsset)
      .wait(200)
      .hover(this.selectOptionDropDown)
      .click(this.selectOptionDropDown)
      .wait(200)
      .hover(selectAssetOption)
      .click(selectAssetOption)
      .wait(200)
      .hover(this.checkMarkBtn)
      .click(this.checkMarkBtn)
      .wait(200)
      .hover(this.selectFilterDropDown)
      .click(this.selectFilterDropDown)
      .wait(200)
      .hover(selectContract)
      .click(selectContract)
      .wait(200)
      .hover(this.selectOptionDropDown)
      .click(this.selectOptionDropDown)
      .wait(200)
      .hover(selectContractOption)
      .click(selectContractOption)
      .wait(200)
      .hover(this.checkMarkBtn)
      .click(this.checkMarkBtn)
      .wait(200)
      .hover(this.selectFilterDropDown)
      .click(this.selectFilterDropDown)
      .wait(200)
      .hover(selectVendor)
      .click(selectVendor)
      .wait(200)
      .hover(this.selectOptionDropDown)
      .click(this.selectOptionDropDown)
      .wait(200)
      .hover(selectVendorOption)
      .click(selectVendorOption)
      .wait(200)
      .hover(this.checkMarkBtn)
      .click(this.checkMarkBtn)
      .wait(200)
      .hover(this.selectFilterDropDown)
      .click(this.selectFilterDropDown)
      .wait(200)
      .hover(selectTelecom)
      .click(selectTelecom)
      .wait(200)
      .hover(this.selectOptionDropDown)
      .click(this.selectOptionDropDown)
      .wait(200)
      .hover(selectTelecomOption)
      .click(selectTelecomOption)
      .wait(200)
      .hover(this.checkMarkBtn)
      .click(this.checkMarkBtn)
      .wait(200)
      .hover(this.selectFilterDropDown)
      .click(this.selectFilterDropDown)
      .wait(200)
      .hover(selectLocation)
      .click(selectLocation)
      .wait(200)
      .hover(this.selectOptionDropDown)
      .click(this.selectOptionDropDown)
      .wait(200)
      .hover(selectLocationOption)
      .click(selectLocationOption)
      .wait(200)
      .hover(this.checkMarkBtn)
      .click(this.checkMarkBtn)
      .wait(200)
      .hover(this.selectFilterDropDown)
      .click(this.selectFilterDropDown)
      .wait(200)
      .hover(selectPeople)
      .click(selectPeople)
      .wait(200)
      .hover(this.selectOptionDropDown)
      .click(this.selectOptionDropDown)
      .wait(200)
      .hover(selectPeopleOption)
      .click(selectPeopleOption)
      .wait(200)
      .hover(this.checkMarkBtn)
      .click(this.checkMarkBtn)
      .wait(200)
      .hover(this.selectFilterDropDown)
      .click(this.selectFilterDropDown)
      .wait(200)
      .hover(selectCreatedby)
      .click(selectCreatedby)
      .wait(200)
      .hover(this.selectOptionDropDown)
      .click(this.selectOptionDropDown)
      .wait(200)
      .hover(selectCreatedbyOption)
      .click(selectCreatedbyOption)
      .wait(200)
      .hover(this.checkMarkBtn)
      .click(this.checkMarkBtn)
      .wait(200)
      .hover(viewAssetFilter)
      .expect(viewAssetFilter.innerText).eql(`${asset} : ${asset} is ${assetValue}`)
      .hover(viewContractFilter)
      .expect(viewContractFilter.innerText).eql(`${contract} : ${contract} is ${contractValue}`)
      .hover(viewVendorFilter)
      .expect(viewVendorFilter.innerText).eql(`${vendor} : ${vendor} is ${vendorValue}`)
      .hover(viewTelecomFilter)
      .expect(viewTelecomFilter.innerText).eql(`${telecom} : ${telecom} is ${telecomValue}`)
      .hover(viewLocationFilter)
      .expect(viewLocationFilter.innerText).eql(`${location} : ${location} is ${locationValue}`)
      .hover(viewPeopleFilter)
      .expect(viewPeopleFilter.innerText).eql(`${people} : ${people} is ${peopleValue}`)
      .hover(viewCreatedbyFilter)
      .expect(viewCreatedbyFilter.innerText).eql(`${createdBy} : ${createdByValue}`)
      .wait(500)
      // .hover(this.previewBtn)
      // .click(this.previewBtn)
      // .wait(1000)
      // .expect(this.tableView.exists).ok('Check the table is present or not')
      // .wait(1000)
      // .hover(this.tableView) //issue in data loading ticket GN-6084
      // .wait(500)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(1000)
      .hover(viewQuickName)
      .expect(viewQuickName.innerText).eql(name)
      .wait(1000);
  }

  async editQuickViewFilters(name, editName, filters, filterOptions) {
    const [assignedto, priority, status, assignment, timeframe, source, workspace] = filters.split(':');
    const [assignedtoValue, priorityValue, statusValue, assignmentValue, timeframeValue, sourceValue, workspaceValue] = filterOptions.split(':');
    const selectAssignedto = this.selectFilterDropDown.find('li').withText(assignedto);
    const selectAssignedtoOption = this.selectOptionDropDown.find('li').withText(assignedtoValue);
    const selectPriority = this.selectFilterDropDown.find('li').withText(priority);
    const selectPriorityOption = this.selectOptionDropDown.find('li').withText(priorityValue);
    const selectStatus = this.selectFilterDropDown.find('li').withText(status);
    const selectStatusOption = this.selectOptionDropDown.find('li').withText(statusValue);
    const selectAssignment = this.selectFilterDropDown.find('li').withText(assignment);
    const selectAssignmentOption = this.selectOptionDropDown.find('li').withText(assignmentValue);
    const selectTimeframe = this.selectFilterDropDown.find('li').withText(timeframe);
    const selectTimeframeOption = this.selectOptionDropDown.find('li').withText(timeframeValue);
    const selectSource = this.selectFilterDropDown.find('li').withText(source);
    const selectSourceOption = this.selectOptionDropDown.find('li').withText(sourceValue);
    const selectWorkSpace = this.selectFilterDropDown.find('li').withText(workspace);
    const selectWorkSpaceOption = this.selectOptionDropDown.find('li').withText(workspaceValue);
    const viewAssignedtoFilter = Selector(`[data-tc-view-filter="${assignedto}"]`);
    const viewPriorityFilter = Selector(`[data-tc-view-filter="${priority}"]`);
    const viewStatusFilter = Selector(`[data-tc-view-filter="${status}"]`);
    const viewTimeframeFilter = Selector(`[data-tc-view-filter="${timeframe}"]`);
    const viewAssignmentFilter = Selector(`[data-tc-view-filter="${assignment}"]`);
    const viewSourceFilter = Selector(`[data-tc-view-filter="${source}"]`);
    const viewWorkspaceFilter = Selector(`[data-tc-view-filter="${workspace}"]`);
    const viewQuickName = Selector(`[data-tc-quick-view-option="${name}"]`);
    const editIcon = Selector(`[data-tc-edit-icon="${name}"]`);
    const viewNameInModal = Selector(`[data-tc-view-name="${name}"]`);
    const viewQuickEditName = Selector(`[data-tc-quick-view-option="${editName}"]`);
    const viewEditNameInModal = Selector(`[data-tc-view-name="${editName}"]`);

    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .hover(viewQuickName)
      .expect(viewQuickName.innerText).eql(name)
      .wait(100)
      .hover(this.editIcon)
      .click(this.editIcon)
      .wait(1000)
      .expect(this.viewEditTitle.innerText).eql('Edit Quick Views')
      .wait(500)
      .hover(viewNameInModal)
      .expect(viewNameInModal.innerText).eql(name)
      .hover(editIcon)
      .click(editIcon)
      .wait(500)
      .expect(this.viewCreateTitle.innerText).eql('Create a new Quick View')
      .typeText(this.inputFilterName, editName, { replace: true })
      .wait(500)
      .hover(this.addFilterIcon)
      .click(this.addFilterIcon)
      .wait(200)
      .hover(this.selectFilterDropDown)
      .click(this.selectFilterDropDown)
      .wait(200)
      .hover(selectAssignedto)
      .click(selectAssignedto)
      .wait(200)
      .hover(this.selectOptionDropDown)
      .click(this.selectOptionDropDown)
      .wait(200)
      .hover(selectAssignedtoOption)
      .click(selectAssignedtoOption)
      .wait(200)
      .hover(this.checkMarkBtn)
      .click(this.checkMarkBtn)
      .wait(200)
      .hover(this.selectFilterDropDown)
      .click(this.selectFilterDropDown)
      .wait(200)
      .hover(selectPriority)
      .click(selectPriority)
      .wait(200)
      .hover(this.selectOptionDropDown)
      .click(this.selectOptionDropDown)
      .wait(200)
      .hover(selectPriorityOption)
      .click(selectPriorityOption)
      .wait(200)
      .hover(this.checkMarkBtn)
      .click(this.checkMarkBtn)
      .wait(200)
      .hover(this.selectFilterDropDown)
      .click(this.selectFilterDropDown)
      .wait(200)
      .hover(selectStatus)
      .click(selectStatus)
      .wait(200)
      .hover(this.selectOptionDropDown)
      .click(this.selectOptionDropDown)
      .wait(200)
      .hover(selectStatusOption)
      .click(selectStatusOption)
      .wait(200)
      .hover(this.checkMarkBtn)
      .click(this.checkMarkBtn)
      .wait(200)
      .hover(this.selectFilterDropDown)
      .click(this.selectFilterDropDown)
      .wait(200)
      .hover(selectAssignment)
      .click(selectAssignment)
      .wait(200)
      .hover(this.selectOptionDropDown)
      .click(this.selectOptionDropDown)
      .wait(200)
      .hover(selectAssignmentOption)
      .click(selectAssignmentOption)
      .wait(200)
      .hover(this.checkMarkBtn)
      .click(this.checkMarkBtn)
      .wait(200)
      .hover(this.selectFilterDropDown)
      .click(this.selectFilterDropDown)
      .wait(200)
      .hover(selectTimeframe)
      .click(selectTimeframe)
      .wait(200)
      .hover(this.selectOptionDropDown)
      .click(this.selectOptionDropDown)
      .wait(200)
      .hover(selectTimeframeOption)
      .click(selectTimeframeOption)
      .wait(200)
      .hover(this.checkMarkBtn)
      .click(this.checkMarkBtn)
      .wait(200)
      .hover(this.selectFilterDropDown)
      .click(this.selectFilterDropDown)
      .wait(200)
      .hover(selectSource)
      .click(selectSource)
      .wait(200)
      .hover(this.selectOptionDropDown)
      .click(this.selectOptionDropDown)
      .wait(200)
      .hover(selectSourceOption)
      .click(selectSourceOption)
      .wait(200)
      .hover(this.checkMarkBtn)
      .click(this.checkMarkBtn)
      .wait(200)
      .hover(this.selectFilterDropDown)
      .click(this.selectFilterDropDown)
      .wait(200)
      .hover(selectWorkSpace)
      .click(selectWorkSpace)
      .wait(200)
      .hover(this.selectOptionDropDown)
      .click(this.selectOptionDropDown)
      .wait(200)
      .hover(selectWorkSpaceOption)
      .click(selectWorkSpaceOption)
      .wait(200)
      .hover(this.checkMarkBtn)
      .click(this.checkMarkBtn)
      .wait(200)
      .hover(viewAssignedtoFilter)
      .expect(viewAssignedtoFilter.innerText).eql(`${assignedto} : ${assignedtoValue}`)
      .hover(viewPriorityFilter)
      .expect(viewPriorityFilter.innerText).eql(`${priority} : ${priorityValue}`)
      .hover(viewStatusFilter)
      .expect(viewStatusFilter.innerText).eql(`${status} : ${statusValue}`)
      .hover(viewAssignmentFilter)
      .expect(viewAssignmentFilter.innerText).eql(`${assignment} : ${assignmentValue}`)
      .hover(viewSourceFilter)
      .expect(viewSourceFilter.innerText).eql(`${source} : ${sourceValue}`)
      .hover(viewTimeframeFilter)
      .expect(viewTimeframeFilter.innerText).eql(`${timeframe} : ${timeframeValue}`)
      .hover(viewWorkspaceFilter)
      .expect(viewWorkspaceFilter.innerText).eql(`${workspace} : ${workspaceValue}`)
      .wait(500)
      // .hover(this.previewBtn)
      // .click(this.previewBtn)
      // .wait(1000)
      // .expect(this.tableView.exists).ok('Check the table is present or not')
      // .wait(1000)
      // .hover(this.tableView) //issue in data loading ticket GN-6084
      // .wait(500)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(1000)
      .hover(viewEditNameInModal)
      .expect(viewEditNameInModal.innerText).eql(editName)
      .wait(500)
      .hover(this.closeModalBtn)
      .click(this.closeModalBtn)
      .wait(500)
      .hover(viewQuickEditName)
      .click(viewQuickEditName)
      .expect(viewQuickEditName.innerText).eql(editName)
      .wait(500);
  }

  async deleteQuickViewFilters(name) {
    const deleteIcon = Selector(`[data-tc-delete-icon="${name}"]`);
    const viewQuickName = Selector(`[data-tc-quick-view-option="${name}"]`);
    const viewNameInModal = Selector(`[data-tc-view-name="${name}"]`);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .hover(viewQuickName)
      .expect(viewQuickName.innerText).eql(name)
      .wait(100)
      .hover(this.editIcon)
      .click(this.editIcon)
      .wait(1000)
      .expect(this.viewEditTitle.innerText).eql('Edit Quick Views')
      .wait(500)
      .hover(viewNameInModal)
      .expect(viewNameInModal.innerText).eql(name)
      .hover(deleteIcon)
      .click(deleteIcon)
      .wait(500)
      .hover(this.notifyPopup)
      .hover(this.closeModalBtn)
      .click(this.closeModalBtn)
      .wait(500)
      .expect(viewQuickName.exists).notOk('Check the name is deleted');
  }
}
export default quickView
