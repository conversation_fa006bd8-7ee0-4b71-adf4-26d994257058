import moment from "moment-timezone";
import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addTicket from "./add_tickets";
import deleteTicket from "./delete_tickets";
import editTickets from "./edit_tickets";

const naviBar = new navbar();
const golVari = new golvar();
const addTic = new addTicket();
const delTic = new deleteTicket();
const editTicPage = new editTickets();

class timeSpent {

  constructor() {
    this.viewUser = Selector('[data-tc-user-time-spent]');
    this.userName = Selector('[data-tc-view="user"]').value;
    this.inputStartTime = Selector('[data-tc-start-time-input]');
    this.inputEndTime = Selector('[data-tc-end-time-input]');
    this.viewStartEndTime = Selector('[data-tc-view-start-end-time]');
    this.viewTimeSpent = Selector('[data-tc-time-hours="3"]');
    this.notifyPopup = Selector('.notification-content');
    this.updateBtn = Selector('[data-tc-time-update-btn]');
    this.deleteConfirm = Selector('[data-tc-delete-time-spent]');
    this.timeSpentDropDown = Selector('[data-tc-time-spent-entry] div');
    this.startTimeLabel = Selector('[data-tc-title="start time"]');
    this.endTimeLabel = Selector('[data-tc-title="end time"]');
    this.timeEntryModalTitle = Selector('[data-tc-title="before close"] h2');
    this.saveTimeBtn = Selector('[data-tc-btn="save time entry"]');
    this.enterTimeBtn = Selector('[data-tc-btn="enter time spent"]');
    this.selectStartHour = this.inputStartTime.find('.hours li');
    this.selectStartMints = this.inputStartTime.find('.minutes li');
    this.selectEndHour = this.inputEndTime.find('.hours li');
    this.selectEndMints = this.inputEndTime.find('.minutes li');
    this.viewTotalSpent = Selector('[data-tc-view-total-time]');
  }

  async addTimeSpents(startTimeHours, startTimeMinutes, endTimeHours, endTimeMinutes, timeSpent, date, notes) {
    const selectStartTimeHours = Selector(`[data-tc-start-time-input] .hours [data-key="${startTimeHours}"]`);
    const selectStartTimeMints = Selector(`[data-tc-start-time-input] .minutes [data-key="${startTimeMinutes}"]`);
    const selectEndTimeHours = Selector(`[data-tc-end-time-input] .hours [data-key="${endTimeHours}"]`);
    const selectEndTimeMints = Selector(`[data-tc-end-time-input] .minutes [data-key="${endTimeMinutes}"]`);
    // const totalTimeSpent = endTimeSpent - startTimeSpent;
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/')
      .wait(1000)
      .hover(editTicPage.timeSpentTab)
      .click(editTicPage.timeSpentTab)
      .wait(1000)
      .hover(this.inputStartTime)
      .click(this.inputStartTime)
      .wait(1000)
      .hover(selectStartTimeHours)
      .click(selectStartTimeHours)
      .wait(1000)
      .hover(selectStartTimeMints)
      .click(selectStartTimeMints)
      .wait(500)
      .click(editTicPage.timeSpentTab)
      .wait(500)
      .hover(this.inputEndTime)
      .click(this.inputEndTime)
      .wait(1000)
      .hover(selectEndTimeHours)
      .click(selectEndTimeHours)
      .wait(1000)
      .hover(selectEndTimeMints)
      .click(selectEndTimeMints)
      .wait(500)
      .click(editTicPage.timeSpentTab)
      .wait(1000)
      .hover(editTicPage.timeSpentTab)
      .click(editTicPage.timeSpentTab)
      .wait(500)
      .hover(editTicPage.inputTimeSpent)
      .click(this.timeSpentDropDown)
      .typeText(editTicPage.inputTimeSpent, timeSpent, { replace: true })
      .wait(500)
      .hover(editTicPage.inputDate)
      .click(editTicPage.inputDate)
      .typeText(editTicPage.inputDate, date, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(editTicPage.inputNotes)
      .typeText(editTicPage.inputNotes, notes, { paste: true })
      .wait(1000)
      .hover(editTicPage.saveComment)
      .click(editTicPage.saveComment)
      .wait(1000)
      .expect(this.viewTimeSpent.innerText).contains('3 hrs')
      .wait(500)
      .expect(this.viewStartEndTime.innerText).contains(`${startTimeHours}:${startTimeMinutes} - ${endTimeHours}:${endTimeMinutes} (April 2, 2022)`)
      .wait(500);
  }

  async editTimeSpent(startTimeHours1, startTimeMinutes1, endTimeHours1, endTimeMinutes1, timeSpent1, date1, notes1) {
    const selectStartTimeHours1 = Selector(`[data-tc-start-time-input] .hours [data-key="${startTimeHours1}"]`);
    const selectStartTimeMints1 = Selector(`[data-tc-start-time-input] .minutes [data-key="${startTimeMinutes1}"]`);
    const selectEndTimeHours1 = Selector(`[data-tc-end-time-input] .hours [data-key="${endTimeHours1}"]`);
    const selectEndTimeMints1 = Selector(`[data-tc-end-time-input] .minutes [data-key="${endTimeMinutes1}"]`);
    const viewEditTimeSpent = Selector('[data-tc-time-hours="2"]');
    const editBtn = Selector('[data-tc-edit-icon]');
    await t.hover(editTicPage.timeSpentTab)
      .click(editTicPage.timeSpentTab)
      .wait(1000)
      .hover(editBtn)
      .click(editBtn)
      .wait(1000)
      .hover(this.inputStartTime)
      .click(this.inputStartTime)
      .wait(1000)
      .hover(selectStartTimeHours1)
      .click(selectStartTimeHours1)
      .wait(1000)
      .hover(selectStartTimeMints1)
      .click(selectStartTimeMints1)
      .wait(500)
      .click(this.startTimeLabel)
      .wait(1000)
      .hover(this.inputEndTime)
      .click(this.inputEndTime)
      .wait(1000)
      .hover(selectEndTimeHours1)
      .click(selectEndTimeHours1)
      .wait(1000)
      .hover(selectEndTimeMints1)
      .click(selectEndTimeMints1)
      .wait(500)
      .click(this.endTimeLabel)
      .wait(1000)
      .hover(editTicPage.timeSpentTab)
      .click(editTicPage.timeSpentTab)
      .wait(500)
      .hover(editTicPage.inputTimeSpent)
      .click(this.timeSpentDropDown)
      .typeText(editTicPage.inputTimeSpent, timeSpent1, { replace: true })
      .wait(500)
      .hover(editTicPage.inputDate)
      .click(editTicPage.inputDate)
      .typeText(editTicPage.inputDate, date1, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(editTicPage.inputNotes)
      .typeText(editTicPage.inputNotes, notes1, { paste: true })
      .wait(1000)
      .hover(this.updateBtn)
      .click(this.updateBtn)
      .wait(1000)
      .expect(viewEditTimeSpent.innerText).contains('2 hrs')
      .wait(500)
      .expect(this.viewStartEndTime.innerText).contains(`${startTimeHours1}:${startTimeMinutes1} - ${endTimeHours1}:${endTimeMinutes1} (April 2, 2022)`)
      .wait(500);
  }

  async deleteTimeSpent() {
    const deleteBtn = Selector('[data-tc-delete-icon]');
    await t.hover(editTicPage.timeSpentTab)
      .click(editTicPage.timeSpentTab)
      .wait(1000)
      .hover(deleteBtn)
      .click(deleteBtn)
      .wait(1000)
      .hover(this.deleteConfirm)
      .click(this.deleteConfirm)
      .wait(1000)
      .expect(this.notifyPopup.innerText).contains('Successfully deleted this time entry')
      .wait(1000);
  }

  async addTimeEntryModal(subject, editStatus, startHourMintues, endHourMintues, timeSpent, date, notes) {
    const viewEditStatus = Selector(`[data-tc-view-status="${editStatus}"]`);
    const [startHour, startMinutes] = startHourMintues.split(':');
    const [endHour, endMinutes] = endHourMintues.split(':');
    const ticket = Selector(`[data-tc-ticket="${subject}"]`);
    await t.hover(delTic.ticketTab)
      .click(delTic.ticketTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/')
      .expect(ticket.exists).ok('Check the ticket is present or not.')
      .hover(ticket)
      .click(ticket)
      .wait(1000)
      .hover(editTicPage.dropDownStatus)
      .click(editTicPage.dropDownStatus)
      .wait(1000)
      .hover(editTicPage.statusSelect.withText(editStatus))
      .click(editTicPage.statusSelect.withText(editStatus))
      .wait(1000)
      .hover(this.timeEntryModalTitle)
      .expect(this.timeEntryModalTitle.innerText).eql('Before you Close ticket ....')
      .click(this.enterTimeBtn)
      .wait(500)
      .hover(this.inputStartTime)
      .click(this.inputStartTime)
      .wait(1000)
      .click(this.selectStartHour.withText(startHour))
      .wait(500)
      .click(this.selectStartMints.withText(startMinutes))
      .wait(500)
      .click(this.inputStartTime)
      .wait(500)
      .hover(this.inputEndTime)
      .click(this.inputEndTime)
      .wait(1000)
      .click(this.selectEndHour.withText(endHour))
      .wait(500)
      .click(this.selectEndMints.withText(endMinutes))
      .wait(500)
      .click(this.inputEndTime)
      .wait(500)
      .hover(editTicPage.inputTimeSpent)
      .click(this.timeSpentDropDown)
      .typeText(editTicPage.inputTimeSpent, timeSpent, { replace: true })
      .wait(500)
      .click(this.timeSpentDropDown)
      .wait(500)
      .hover(editTicPage.inputDate)
      .click(editTicPage.inputDate)
      .typeText(editTicPage.inputDate, date, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(editTicPage.inputNotes)
      .typeText(editTicPage.inputNotes, notes, { paste: true })
      .wait(500)
      .hover(editTicPage.saveComment)
      .click(editTicPage.saveComment)
      .wait(500)
      .hover(this.saveTimeBtn)
      .click(this.saveTimeBtn)
      .wait(1000)
      .hover(editTicPage.timeSpentTab)
      .click(editTicPage.timeSpentTab)
      .wait(1000)
      .expect(this.viewTotalSpent.innerText).eql(timeSpent)
      .wait(500)
      .expect(this.viewStartEndTime.innerText).contains(`${startHour}:${startMinutes} - ${endHour}:${endMinutes} (April 2, 2022)`)
      .wait(500)
      .expect(viewEditStatus.innerText).eql(` ${editStatus}`);
  }
} export default timeSpent;
