import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addTicket from "./add_tickets";

const naviBar = new navbar();
const golVari = new golvar();
const addTicPage = new addTicket();

class helpDeskPermissions {

  constructor() {
    this.permissionIcon = Selector(`${golVari.horizontalMenu} [data-tc-permissions-icon="Help Desk"]`);
    this.viewPermsModal = Selector(`${golVari.horizontalMenu} [data-tc-view-modal="help desk permissions"]`);
    this.viewPermsTitle = this.viewPermsModal.find('h2');
    this.addGroupBtn = Selector(`${golVari.horizontalMenu} [data-tc-add-btn="new group"]`);
    this.groupDropDown = Selector('[data-tc-contributors-field]');
    this.userDropDown = Selector('[data-tc-contributors-fields]');
    this.selectGroup = this.groupDropDown.find('li');
    this.closePopUp = Selector(`${golVari.horizontalMenu} [data-tc-view-modal="help desk permissions"] .sweet-action-close`);
    this.addUserbtn = Selector(`${golVari.horizontalMenu} [data-tc-add-btn="new user"]`);
    this.notifyPop = Selector('.notification-content');
  }

  async navigateToPermsModal() {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(2000)
      .hover(this.permissionIcon)
      .click(this.permissionIcon)
      .wait(2000)
      .expect(this.viewPermsModal.visible).ok()
      .hover(this.viewPermsModal)
      .wait(500)
      .expect(this.viewPermsTitle.innerText).contains('People with Help Desk module permissions')
      .wait(500)
  }

  async closePermsModal() {
    await t.hover(this.closePopUp)
      .wait(500)
      .click(this.closePopUp)
      .wait(1000)
      .expect(this.viewPermsModal.visible).notOk()
      .wait(1000)
  }
  async addGroupPerms(groupName) {
    const viewAddedGroup = Selector(`[data-tc-view-group="${groupName}"]`);
    const viewDefaultPermission = Selector(`[data-tc-group-name="${groupName}"] span`);
    await t.expect(this.viewPermsTitle.innerText).contains('People with Help Desk module permissions')
      .wait(1000)
      .hover(this.addGroupBtn)
      .wait(500)
      .click(this.addGroupBtn)
      .wait(3000)
      .click(this.groupDropDown)
      .wait(1000)
      .click(this.selectGroup.withText(groupName))
      .wait(500)
      .expect(addTicPage.notifyPop.innerText).contains(`${groupName} group added`)
      .wait(2000)
      .hover(viewAddedGroup)
      .wait(1000)
      .expect(viewAddedGroup.innerText).contains(groupName)
      .wait(1000)
      .expect(viewDefaultPermission.innerText).contains('Read All')
      .wait(1000)
  }

  async addUserPerms(userName) {
    const viewAddedUser = Selector(`[data-tc-view-user="${userName}"]`);
    const selectUser = Selector(`[data-tc-selected-object="${userName}"]`);
    const viewDefaultPermission = Selector(`[data-tc-user-member="${userName}"] span`);
    await t.expect(this.viewPermsTitle.innerText).contains('People with Help Desk module permissions')
      .wait(1000)
      .hover(this.addUserbtn)
      .wait(500)
      .click(this.addUserbtn)
      .wait(3000)
      .click(this.userDropDown)
      .wait(500)
      .click(selectUser)
      .expect(this.notifyPop.innerText).contains('Teammate added')
      .wait(2000)
      .hover(viewAddedUser)
      .wait(1000)
      .expect(viewAddedUser.innerText).contains(userName)
      .wait(1000)
      .expect(viewDefaultPermission.innerText).contains('Read All')
      .wait(1000)
  }

  async checkAddedUserGroupNotExist(groupName1, userName1) {
    const selectUser1 = Selector(`[data-tc-selected-object="${userName1}"]`);
    await t.expect(this.viewPermsTitle.innerText).contains('People with Help Desk module permissions')
      .wait(500)
      .click(this.groupDropDown)
      .wait(500)
      .expect(this.selectGroup.withText(groupName1).exists).notOk()
      .wait(500)
      .click(this.userDropDown)
      .wait(1000)
      .expect(selectUser1.exists).notOk()
      .click(this.viewPermsTitle)
      .wait(500)
  }

  async checkRemovedUserGroupExist(groupName2, userName2) {
    const selectUser1 = Selector(`[data-tc-selected-object="${userName2}"]`);
    await t.expect(this.viewPermsTitle.innerText).contains('People with Help Desk module permissions')
      .wait(1000)
      // .hover(this.addGroupBtn)
      // .wait(500)
      // .click(this.addGroupBtn)
      // .wait(3000)
      .click(this.groupDropDown)
      .wait(1000)
      .expect(this.selectGroup.withText(groupName2).exists).ok()
      .wait(1000)
      // .hover(this.addUserbtn)
      // .wait(500)
      // .click(this.addUserbtn)
      // .wait(3000)
      .click(this.userDropDown)
      .wait(1000)
      .expect(selectUser1.exists).ok()
      .wait(500)
  }

  async deleteUserGroupPerms(groupName3, userName3) {
    const removeGroupIcon = Selector(`[data-tc-remove-icon="${groupName3}"]`);
    const removeUserIcon = Selector(`[data-tc-remove-icon="${userName3}"]`);
    await t.expect(this.viewPermsTitle.innerText).contains('People with Help Desk module permissions')
      .wait(1000)
      .hover(removeGroupIcon)
      .wait(1000)
      .click(removeGroupIcon)
      .wait(500)
      .expect(addTicPage.notifyPop.innerText).contains(`${groupName3} group removed.`)
      .wait(3000)
      .hover(removeUserIcon)
      .wait(1000)
      .click(removeUserIcon)
      .expect(addTicPage.notifyPop.innerText).contains('Teammate permissions removed')
      .wait(1000)
  }

  async viewMemberInGroup(groupName4, userName4) {
    const expandGroupIcon = Selector(`${golVari.horizontalMenu} [data-tc-expand-icon="${groupName4}"]`);
    const viewMember = Selector(`[data-tc-view-member-group="${userName4}"]`);
    await t.expect(this.viewPermsTitle.innerText).contains('People with Help Desk module permissions')
      .wait(1000)
      .hover(expandGroupIcon)
      .wait(1000)
      .click(expandGroupIcon)
      .wait(1000)
      .hover(viewMember)
      .wait(1000)
      .expect(viewMember.innerText).contains(userName4)
      .wait(500)
      .click(expandGroupIcon)
      .wait(500)
  }

  async deleteMemberInGroup(groupName5, userName5, counter) {
    const expandGroupIcon = Selector(`[data-tc-expand-icon="${groupName5}"]`);
    const viewMember = Selector(`[data-tc-view-member-group="${userName5}"]`);
    const removeMemberIcon = Selector(`[data-tc-remove-member-icon="${userName5}"]`);
    const countBfrRemove = counter;
    const countAfrRemove = counter - 1;
    const viewGroupCount = Selector(`[data-tc-view-group="${groupName5}"] small`);
    await t.expect(this.viewPermsTitle.innerText).contains('People with Help Desk module permissions')
      .wait(1000)
      .expect(viewGroupCount.innerText).contains(`(${countBfrRemove} user)`)
      .wait(1000);
    if (groupName5 == 'Admins') {
      await t.wait(1000)
        .hover(expandGroupIcon)
        .wait(1000)
        .click(expandGroupIcon)
        .wait(1000)
        .expect(removeMemberIcon.exists).notOk()
        .wait(500)
        .click(expandGroupIcon)
        .wait(500)
    }
    else {
      await t.wait(1000)
        .hover(expandGroupIcon)
        .wait(1000)
        .click(expandGroupIcon)
        .wait(1000)
        .hover(viewMember)
        .wait(1000)
        .expect(viewMember.innerText).contains(userName5)
        .wait(1000)
        .hover(removeMemberIcon)
        .wait(500)
        .click(removeMemberIcon)
        .expect(this.notifyPop.innerText).contains(`${groupName5} group permissions updated`)
        .wait(2000)
        .expect(viewGroupCount.innerText).contains(`(${countAfrRemove} users)`)
        .wait(500)
        .click(expandGroupIcon)
        .wait(500)
    }
  }

  async changePermissionUserGroup(groupName6, userName6, permissionGroupSet, permissionUserSet) {
    const permissionGroupSelect = Selector(`${golVari.horizontalMenu} [data-tc-group-name="${groupName6}"] span`);
    const selectPermissionGroup = Selector(`${golVari.horizontalMenu} [data-tc-group-name="${groupName6}"] [data-tc-help-desk-permission-items="${permissionGroupSet}"]`);
    const permissionUserSelect = Selector(`${golVari.horizontalMenu} [data-tc-user-member="${userName6}"] span`);
    const selectPermissionUser = Selector(`${golVari.horizontalMenu} [data-tc-user-member="${userName6}"] [data-tc-help-desk-permission-items="${permissionUserSet}"]`);
    await t.expect(this.viewPermsTitle.innerText).contains('People with Help Desk module permissions')
      .wait(1000)
      .hover(permissionGroupSelect)
      .wait(500)
      .click(permissionGroupSelect)
      .wait(1000)
      .click(selectPermissionGroup)
      .wait(1000)
      .expect(addTicPage.notifyPop.innerText).contains(`${groupName6} group permissions updated`)
      .wait(2000)
      .hover(permissionGroupSelect)
      .wait(1000)
      .expect(permissionGroupSelect.innerText).contains(permissionGroupSet)
      .wait(1000)
      .hover(permissionUserSelect)
      .wait(500)
      .click(permissionUserSelect)
      .wait(500)
      .click(selectPermissionUser)
      .wait(500)
      .expect(this.notifyPop.innerText).contains('Teammate permissions updated')
      .wait(2000)
      .hover(permissionUserSelect)
      .wait(1000)
      .expect(permissionUserSelect.innerText).contains(permissionUserSet)
      .wait(1000)
  }
} export default helpDeskPermissions
