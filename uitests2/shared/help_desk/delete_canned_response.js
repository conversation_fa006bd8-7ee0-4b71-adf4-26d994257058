import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addCannedResponse from "./add_canned_response";

const naviBar = new navbar();
const golVari = new golvar();
const responsePage = new addCannedResponse();

class deleteCannedResponse {

  constructor() {
    this.deleteBtn = Selector('[data-tc-response-delete]');
    this.confirmBtn = Selector('[data-tc-response-delete-button]');
    this.viewDeletedResponse = Selector('[data-tc-no-response-found]');
  }

  async deleteCannedResponses(title) {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(naviBar.resourceNavMenu)
      .click(naviBar.resourceNavMenu)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/articles')
      .wait(500)
      .hover(responsePage.cannedTab)
      .click(responsePage.cannedTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/responses')
      .wait(500)
      .hover(responsePage.searchField)
      .typeText(responsePage.searchField, title, { replace: true })
      .pressKey('enter')
      .wait(2000)
      .expect(responsePage.viewTitle.innerText).contains(title)
      .wait(1000)
      .hover(this.deleteBtn)
      .click(this.deleteBtn)
      .wait(1000)
      .hover(this.confirmBtn)
      .click(this.confirmBtn)
      .wait(1000)
      .hover(responsePage.searchField)
      .typeText(responsePage.searchField, title, { replace: true })
      .pressKey('enter')
      .wait(2000)
      .expect(this.viewDeletedResponse.innerText).contains("No Responses exist, yet.")
      .wait(1000)
  }
} export default deleteCannedResponse
