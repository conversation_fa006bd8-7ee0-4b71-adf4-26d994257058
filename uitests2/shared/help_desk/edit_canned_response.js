import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import addCannedResponse from "./add_canned_response";

const naviBar = new navbar();
const golVari = new golvar();
const responsePage = new addCannedResponse();

class editCannedResponse {

  constructor() {
    this.saveRespBtn = Selector('[data-tc-save-form-btn="Save Response"]');
    this.viewEditRespPage = Selector('[data-tc-view-header="edit response"]');
  }

  async editCannedResponses(title, editTitle, editText) {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(naviBar.resourceNavMenu)
      .click(naviBar.resourceNavMenu)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/articles')
      .wait(500)
      .hover(responsePage.cannedTab)
      .click(responsePage.cannedTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/responses')
      .wait(500)
      .hover(responsePage.searchField)
      .typeText(responsePage.searchField, title, { replace: true })
      .pressKey('enter')
      .wait(2000)
      .expect(responsePage.viewTitle.innerText).contains(title)
      .wait(1000)
      .click(responsePage.viewTitle)
      .wait(1000)
      .expect(this.viewEditRespPage.innerText).contains('Edit Response')
      .wait(2000)
      .typeText(responsePage.titleField, editTitle, { replace: true })
      .wait(2000)
      .typeText(responsePage.textToReplaceField, editText, { replace: true })
      .wait(2000)
      .hover(this.saveRespBtn)
      .click(this.saveRespBtn)
      .wait(2000)
      .hover(responsePage.searchField)
      .typeText(responsePage.searchField, editTitle, { replace: true })
      .pressKey('enter')
      .wait(2000)
      .expect(responsePage.viewTitle.innerText).contains(editTitle)
      .wait(1000)
  }
} export default editCannedResponse
