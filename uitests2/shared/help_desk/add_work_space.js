import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";
import customFormOperation from "./set_default_form";

const naviBar = new navbar();
const golVari = new golvar();
const indexPage = new customFormOperation();

class addWorkSpace {

  constructor() {
    this.styleSelector = '[data-tc-style-select-dropdown]';
    this.iconSelector = '[data-tc-icon="workspace icon"]';
    this.colorSelector = '[data-tc-icon="workspace color"]';
    this.gridStyleSelector = '[data-tc-container="grid"]';
    this.spaceDropDown = Selector('[data-dropdown-menu="workspace_select_menu"]');
    this.addWorkSpaceBtn = Selector('[data-tc-add="workspace"]');
    this.nameField = Selector('[data-tc-field="workspace name"]');
    this.descField = Selector('[data-tc-field="workspace description"]');
    this.errorName = Selector('[data-tc-error="workspace name"]');
    this.members = Selector('[data-tc-staff-users="Everyone"]');
    this.setPerm1 = this.members.find('.multiselect__content li span span');
    this.addNewUser = Selector('[data-tc-add-impacted-user]');
    this.userDropDown = Selector('[data-tc-multi-user-field]');
    this.inputUser = Selector('[data-tc-multi-user-field] input');
    this.addFormBtn = Selector('[data-tc-add-custom-form]')
    this.formDropDown = Selector('[data-tc-multi-custom-form]');
    this.inputForm = Selector('[data-tc-multi-custom-form]');
    this.selectForm = this.inputForm.find('li');
    this.saveBtn = Selector('[data-tc-save-form-btn="Create Workspace"]');
    this.searchSpace = Selector('[data-tc-workspace-search]');
    this.viewWorkSpaceName = Selector('[data-tc-workspace-name]');
    this.viewAdmins = Selector('[data-tc-member="Admins"]');
    this.viewEveryone = Selector('[data-tc-member="Everyone"]');
    this.viewDisableIcon = Selector('[data-tc-disable-remove="true"]');
    this.viewDisablePerm = Selector('[data-tc-disable="true"]');
    this.defaultPerm = Selector('[data-tc-staff-users="Admins"] span');
    this.defaultPerm1 = Selector('[data-tc-staff-users="Everyone"] span');
    this.peopleSubMenu = Selector('[data-tc-subpage-menu-item="people"]');
    this.settingSubMenu = Selector('[data-tc-subpage-menu-item="quick_settings"]');
    this.iconDropDown = Selector(`${this.iconSelector} ${this.styleSelector}`);
    this.selectIcon1 = Selector(`${this.gridStyleSelector} [data-tc-style="genuicon-farm"]`);
    this.selectIcon2 = Selector(`${this.gridStyleSelector} [data-tc-style="genuicon-classroom"]`);
    this.colorDropDown = Selector(`${this.colorSelector} ${this.styleSelector}`);
    this.selectColor1 = Selector('[data-tc-style="dot bg-warning"]');
    this.selectColor2 = Selector('[data-tc-style="dot bg-safe"]');
    this.quickSettingLabel = Selector('[data-tc-label="quick setting"]');
  }

  async addWorkSpaces(name, description, groupUser, setPermission1, setPermission2, formName) {
    const staffUser = Selector(`[data-tc-staff-users="${groupUser}"]`);
    const setPerm2 = staffUser.find('.multiselect__content li span span');
    const viewName = Selector(`[data-tc-value="${name}"]`);
    const viewDescription = Selector(`[data-tc-description="${name}"]`);
    const viewWorkSpace = Selector(`[data-workspace-name="${name}"]`);
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(this.spaceDropDown)
      .click(this.spaceDropDown)
      .wait(1000)
      .hover(this.addWorkSpaceBtn)
      .click(this.addWorkSpaceBtn)
      .expect(golVari.currentpageurl()).contains('/help_tickets/workspaces?new=true')
      .wait(500)
      .hover(this.nameField)
      .typeText(this.nameField, name, { paste: true })
      .wait(100)
      .typeText(this.descField, description, { paste: true })
      .wait(100)
      .hover(this.colorDropDown)
      .click(this.colorDropDown)
      .wait(400)
      .hover(this.selectColor1)
      .click(this.selectColor1)
      // .hover(this.iconDropDown)
      // .click(this.iconDropDown)
      // .wait(500)
      // .expect(this.selectIcon1.exists).ok({ timeout: 10000 })
      // .click(this.selectIcon1)
      // .wait(500)
      .hover(this.addFormBtn)
      .click(this.addFormBtn)
      .wait(500)
      .hover(this.formDropDown)
      .click(this.formDropDown)
      .wait(500)
      .click(this.selectForm.withText(formName))
      .wait(100)
      .hover(this.peopleSubMenu)
      .click(this.peopleSubMenu)
      .wait(300)
      .expect(this.viewAdmins.innerText).contains('Admins')
      .wait(500)
      .expect(this.viewDisableIcon.exists).ok()
      .wait(500)
      .expect(this.viewDisablePerm.exists).ok()
      .wait(1000)
      .expect(this.defaultPerm.innerText).contains('Write All')
      .wait(500)
      .expect(this.viewEveryone.innerText).contains('Everyone')
      .wait(500)
      .hover(this.members)
      .click(this.members)
      .click(this.setPerm1.withText(setPermission1))
      .wait(1000)
      .click(this.addNewUser)
      .wait(500)
      .hover(this.userDropDown)
      .click(this.userDropDown)
      .wait(1000)
      .typeText(this.inputUser, groupUser, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .hover(staffUser)
      .click(staffUser)
      .wait(500)
      .click(setPerm2.withText(setPermission2))
      .wait(500)
      .hover(this.settingSubMenu)
      .click(this.settingSubMenu)
      .hover(this.quickSettingLabel)
      .expect(this.quickSettingLabel.innerText).eql('Quick Settings')
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(3000)
      .hover(this.searchSpace)
      .typeText(this.searchSpace, name, { replace: true })
      .pressKey('enter')
      .wait(500)
      .hover(viewName)
      .expect(viewName.innerText).eql(name)
      .expect(viewDescription.innerText).eql(description)
      .wait(500)
      .hover(this.spaceDropDown)
      .click(this.spaceDropDown)
      .wait(500)
      .hover(viewWorkSpace)
      .expect(viewWorkSpace.innerText).contains(name);
  }

  async removeDefaultForm(name1, formName) {
    const viewSpaceName = Selector(`[data-tc-value="${name1}"]`);
    const removeicon = Selector(`[data-tc-custom-form="${formName}"] i`);
    await t.hover(this.searchSpace)
      .typeText(this.searchSpace, name1, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .expect(viewSpaceName.innerText).contains(name1)
      .wait(500)
      .click(viewSpaceName)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/workspaces?new=true')
      .wait(1000)
      .click(removeicon)
      .wait(500)
      .expect(indexPage.notifyPopup.innerText).contains(`This form is linked to an automated task, which is why you can't remove this form`)
      .wait(1000)
  }

  async validateName() {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .hover(this.spaceDropDown)
      .click(this.spaceDropDown)
      .wait(1000)
      .hover(this.addWorkSpaceBtn)
      .click(this.addWorkSpaceBtn)
      .expect(golVari.currentpageurl()).contains('/help_tickets/workspaces?new=true')
      .wait(2000)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(500)
      .expect(indexPage.notifyPopup.innerText).contains(`Please enter a name for workspace.`)
      .wait(1000)
      .expect(this.errorName.innerText).contains('Please enter a name for workspace')
      .wait(500)
      .typeText(this.nameField, 'testing', { paste: true })
      .wait(1000)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(1000)
      .expect(indexPage.notifyPopup.innerText).contains('Must have one custom form selected. Please fix errors before saving.')
      .wait(1000)
  }
} export default addWorkSpace
