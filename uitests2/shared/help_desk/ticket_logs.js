import { Selector, t } from 'testcafe';
import navbar from '../Components/navbar';
import golvar from '../Globalvariable';

const naviBar = new navbar();
const golVari = new golvar();

class ticketLogs {

  constructor() {
    this.logIcon = Selector(`${golVari.horizontalMenu} [data-tc-logs-icon="Help Desk"]`);
    this.searchLogField = Selector('[data-tc-activity-search]');
    this.noLogMessage = Selector('[data-tc-view-message="no activites"]');
    this.createdLog = Selector('[data-tc-view-ticket="created"]');
    this.createdMessage = Selector('[data-tc-created-message]');
    this.eventLogsTab = Selector('[data-tc-sub-tab="event logs"]');
    this.moduleLogsTab = Selector('[data-tc-sub-tab="module logs"]');
    this.activityByFilter = Selector('[data-tc-label="Activity Type"]');
    this.changeByFilter = Selector('[data-tc-label="Changed by"]');

  }

  async navigateToTicketLogs() {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(this.logIcon)
      .click(this.logIcon)
      .expect(golVari.currentpageurl()).contains('/help_tickets/ticket_activities')
      .wait(500);
  }

  async createdTicketLogs(ticketSubject) {
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/ticket_activities')
      .typeText(this.searchLogField, ticketSubject, { replace: true })
      .wait(1000)
      .hover(this.createdLog)
      .expect(this.createdLog.innerText).contains('Created')
      .expect(this.createdMessage.innerText).contains('Ticket was created');
  }

  async deskModuleLogs(changeByFilter, activityType, activityName, viewText) {
    const viewActivity = Selector(`[data-tc-activity="${activityType} ${activityName}"]`);
    const selectChangeBy = Selector(`[data-tc-filter-option="${changeByFilter}"]`);
    const selectActivityBy = Selector(`[data-tc-filter-option="${activityType}"]`);
    await t.expect(golVari.currentpageurl()).contains('/help_tickets/ticket_activities')
      .hover(this.moduleLogsTab)
      .click(this.moduleLogsTab)
      .wait(200)
      .expect(golVari.currentpageurl()).contains('/help_tickets/settings/event_log')
      .hover(this.changeByFilter)
      .click(this.changeByFilter)
      .wait(500)
      .click(selectChangeBy)
      .wait(500)
      .hover(this.activityByFilter)
      .click(this.activityByFilter)
      .wait(500)
      .click(selectActivityBy)
      .wait(500)
      .hover(viewActivity)
      .expect(viewActivity.innerText).eql(`${activityType} ${activityName} ${viewText}`);
  }

  async searchLogNoPresent(value) {
    await t.hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(500)
      .hover(this.logIcon)
      .wait(500)
      .click(this.logIcon)
      .wait(1000)
      .hover(this.searchLogField)
      .wait(500)
      .typeText(this.searchLogField, value, { paste: true })
      .wait(1000)
      .hover(this.noLogMessage)
      .wait(500)
      .expect(this.noLogMessage.innerText).contains('No activities present.')
      .wait(1000)
  }
} export default ticketLogs
