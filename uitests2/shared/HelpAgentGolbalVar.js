import faker from 'faker';
import { ClientFunction } from 'testcafe';
import { Selector, t } from 'testcafe';
import { getConfig } from '../../config.ts';

const config = getConfig();
const email = '<EMAIL>';
const companyName = 'Smoke Testing Company';
const subdomain1 = 'smoke-testing-company';
const fName = 'Qa';
const lName = 'Tester';

class helpAgentGolVar {

  constructor() {
    this.password = '12345678A';
    this.baseurl = config.siteUrl;
    this.envurl = config.ev;
    this.signupurl = this.baseurl + 'users/sign_up/about_you';
    this.firstName = fName;
    this.lastName = lName;
    this.fullName = fName + ' ' + lName;
    this.companyemail = email;
    this.emailaddress = email;
    this.newpassword = faker.internet.password();
    this.companyname = companyName;
    this.subdomain1 = subdomain1;
    this.membername = Selector('[data-tc-view="user"]').textContent;
    this.adminmember = 'Everyone';
    this.guestEmail = faker.internet.email('genuitytest', `${faker.lorem.word()}`, 'work4humanity.com', { allowSpecialCharacters: true });
    this.url = `http://${this.subdomain1}.${this.envurl}`
    // get current page url
    this.currentpageurl = ClientFunction(() => {
      return window.location.href.toString();
    })
    this.getUrl = ClientFunction(() => {
      return window.location.origin.toString();
    })
  }
} export default helpAgentGolVar
