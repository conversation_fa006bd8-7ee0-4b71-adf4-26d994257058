import faker from 'faker';

class contractvar {
  constructor() {
    this.contname = faker.name.firstName();
    this.startdate = '2022-11-8';
    this.alertdate = '2024-12-14';
    this.amount = '123';
    this.editcontname = faker.name.firstName();
    this.editstartdate = '2022-10-9';
    this.editalertdate = '2024-11-02';
    this.editamount = '3232';
    this.category = 'General';
    this.module = 'contracts';
    this.allTime = 'All Time';
    this.calendarYear = 'Calendar Year';
    this.spendYtd = 'Spend YTD';
    this.spendQtd = 'Spend QTD';
    this.totalCost = 'Total Cost';
    this.montlySpend = 'Monthly Spend';
    this.graphTitle = faker.lorem.words();
    this.barChart = 'Barchart';
    this.donutChart = 'Donutchart';
    this.pieChart = 'Piechart';
    this.dollar = '$';
    this.montlyType = 'montly';
    this.fixedType = 'fixed';
  }
} export default contractvar
