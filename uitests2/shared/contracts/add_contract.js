import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import { nanoid } from 'nanoid';
import contractvar from './contract_variables';

const golvari = new golvar();
const navibar = new navbar();
const catname = 'Test' + nanoid(3);
const contractVari = new contractvar();

class addcontracts {
  constructor() {
    this.addcontractbtn = Selector('[data-tc-add-new-contract-btn]');
    this.selectvendor = Selector('[data-tc-multi-select-vendor]');
    this.namefield = Selector('[data-tc-contract-name]');
    this.contractterm = Selector('[data-tc-open-ended]');
    this.startdate = Selector('[data-tc-start-date]');
    this.alertdate = Selector('[data-tc-alert-date]');
    this.contractcost = Selector('[data-tc-currency]');
    this.addcat = Selector('[data-tc-add-category]');
    this.catfield = Selector('[data-tc-new-category]');
    this.savecatbtn = Selector('[data-tc-save-form-btn="Save Category"]');
    this.locationfield = Selector('[data-tc-location-field]');
    this.savebtn = Selector('[data-tc-save-contract-btn]');
    this.contracttitle = Selector('[data-tc-contract-headline]');
    this.backbtn = Selector('[data-tc-back-btn]');
    this.notes = Selector('[data-tc-contract-notes]');
    this.staffmember = Selector('[data-tc-multi-user-field]');
    this.tags = Selector('[data-tc-contract-tags-field]');
    this.createlocbtn = Selector('[data-tc-create="location"]');
    this.searchfield = Selector('[data-tc-search-contract-field]');
    this.searchedcontract = Selector('[data-tc-searched-contract]');
    this.dochover = Selector('[data-tc-document-hover]');
    this.attachment = Selector('[data-tc-attachment]');
    this.categoryDropDown = Selector('[data-tc-category-id]');
    this.selectCategory = this.categoryDropDown.find('option');

  }

  async addcontracts(contractname, startdate, alertdate, currency, location, staffUser, contractType) {
    const contType = Selector(`[data-tc-type-${contractType}="Add"]`);
    await t.hover(navibar.contractsbtn)
      .click(navibar.contractsbtn)
      .expect(golvari.currentpageurl()).contains('/contracts')
      .hover(this.addcontractbtn)
      .click(this.addcontractbtn)
      .wait(500)
      .expect(contType.visible).ok()
      .click(contType)
      .wait(1000)
      .expect(golvari.currentpageurl()).contains('/contracts/new')
      .click(this.selectvendor)
      .typeText(this.selectvendor, 'Testfairy', { paste: true })
      .pressKey('enter')
      .hover(this.namefield)
      .typeText(this.namefield, contractname, { paste: true })
      .click(this.contractterm)
      .click(this.startdate)
      .typeText(this.startdate, startdate, { paste: true })
      .click(this.alertdate)
      .typeText(this.alertdate, alertdate, { paste: true })
      .typeText(this.contractcost, currency, { paste: true })
      .scroll(this.contractcost, 'center')
      .wait(1000)
      .hover(this.categoryDropDown)
      .wait(1000)
      .click(this.categoryDropDown)
      .wait(500)
      .click(this.selectCategory.withText(contractVari.category))
      .wait(1000)
      .hover(this.locationfield)
      .click(this.locationfield)
      .typeText(this.locationfield, location, { paste: true })
      .pressKey('enter')
      .click(this.tags)
      .typeText(this.tags, 'newone', { paste: true })
      .pressKey('enter')
      .click(this.staffmember)
      .typeText(this.staffmember, staffUser, { paste: true })
      .pressKey('enter')
      .typeText(this.notes, 'This is only for testing purposes', { paste: true })
      .scroll(this.notes, 'center')
      .hover(this.dochover)
      .scroll(this.dochover, 'center')
      .hover(this.attachment)
      .click(this.savebtn)
      .wait(1000)
      .expect(this.contracttitle.innerText).contains(contractname) //contract is created
      .wait(500)
      .hover(this.backbtn)
      .click(this.backbtn)
      .wait(1000)
      .click(this.searchfield)
      .typeText(this.searchfield, contractname, { paste: true })
      .wait(1000)
      .hover(this.searchedcontract)
      .click(this.searchedcontract)
      .hover(this.contracttitle)
      .expect(this.contracttitle.innerText).contains(contractname)
    await t.wait(1000)
  }
} export default addcontracts
