import navbar from "../Components/navbar";
import { Selector, t } from "testcafe";
import golvar from "../Globalvariable";
import addAsset from "../assets/add_assets";

const naviBar = new navbar();
const golVari = new golvar();
const addAstPage = new addAsset();
const tlContract = Selector('[data-tc-total-contracts]');
const dashtab = Selector('[data-tc-btn="contracts dashboard"]');
const companyViewToggle = Selector('#my_company_view');

class viewCount {

  async checkCounter(value, moduleNameMnView, moduleNameComView) {
    const checkModule = Selector(`[data-tc-module="${moduleNameComView}"]`);
    const checkTitleViewManagement = Selector(`[data-tc-module="${moduleNameMnView}"] [data-tc-title="${moduleNameMnView}"]`);
    const checkCounterViewManagement = Selector(`[data-tc-module="${moduleNameMnView}"] [data-tc-total="${value}"]`);
    await t.wait(1000)
      .click(naviBar.contractsbtn)
      .wait(2000)
      .click(dashtab)
      .wait(2000)
      .expect(tlContract.innerText).contains(value)
      .wait(1000)
      .hover(naviBar.homeDashboard)
      .click(naviBar.homeDashboard)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/dashboard')
      .wait(1000)
      .hover(checkCounterViewManagement)
      .expect(checkCounterViewManagement.innerText).contains(value)
      .hover(checkCounterViewManagement)
      .expect(checkTitleViewManagement.innerText).contains(moduleNameMnView)
      .wait(500)
      .click(addAstPage.companyViewToggle)
      .wait(500)
      .hover(tlContract)
      .expect(tlContract.innerText).contains(value)
      .expect(checkModule.innerText).contains(moduleNameComView)
      .wait(1000)
      .click(addAstPage.managementViewToggle)
      .wait(500)
  }

  async counterCategoryWise(category, value) {
    const viewCategory = Selector(`[data-tc-view-category="${category}"]`);
    const viewCatCount = Selector(`[data-tc-category-count="${category}"] strong`);
    await t.wait(1000)
      .click(naviBar.contractsbtn)
      .wait(1000)
      .click(dashtab)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/contracts')
      .wait(1000)
      .hover(viewCategory)
      .wait(500)
      .expect(viewCategory.innerText).contains(category)
      .wait(1000)
      .hover(viewCatCount)
      .wait(500)
      .expect(viewCatCount.innerText).contains(value)
      .wait(1000)
  }
} export default viewCount
