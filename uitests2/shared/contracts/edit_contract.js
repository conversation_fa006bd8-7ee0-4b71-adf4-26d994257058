import { Selector, t } from 'testcafe'
import golvar from '../Globalvariable'
import navbar from '../Components/navbar'

const golvari = new golvar()
const navibar = new navbar()

class editcontracts {
  constructor() {
    this.contracttab = Selector('[data-tc-contract-tab]')
    this.selectvendor = Selector('[data-tc-multi-select-vendor]')
    this.namefield = Selector('[data-tc-contract-name]')
    this.contractterm = Selector('[data-tc-open-ended]')
    this.startdate = Selector('[data-tc-start-date]')
    this.alertdate = Selector('[data-tc-alert-date]')
    this.contractcost = Selector('[data-tc-currency]')
    this.addcat = Selector('[data-tc-add-category]')
    this.catfield = Selector('[data-tc-new-category]')
    this.savecatbtn = Selector('[data-tc-btn-add-category]')
    this.addnewloc = Selector('[data-tc-add-new-location]')
    this.locationfield = Selector('[data-tc-location-field]')
    this.savebtn = Selector('[data-tc-save-contract-btn]')
    this.contracttitle = Selector('[data-tc-contract-headline]')
    this.backbtn = Selector('[data-tc-back-btn]')
    this.notes = Selector('[data-tc-contract-notes]')
    this.tags = Selector('[data-tc-contract-tags-field]')
    this.searchfield = Selector('[data-tc-search-contract-field]')
    this.searchedcontract = Selector('[data-tc-searched-contract]')
    this.editbtn = Selector('[data-tc-edit-contract-btn]')
    this.attachment = Selector('[data-tc-attachment]')
  }
  async editcontracts(oldcontractname, contractname, startdate, alertdate, amount) {
    await t.hover(navibar.contractsbtn)
      .click(navibar.contractsbtn)
      .expect(golvari.currentpageurl()).contains('/contracts')
      //search the contract
      .hover(this.contracttab)
      .click(this.contracttab)
      .wait(1000)
      .click(this.searchfield)
      .typeText(this.searchfield, oldcontractname, { paste: true })
      .wait(1000)
      .hover(this.searchedcontract)
      .click(this.searchedcontract)
      .wait(1000)
      .hover(this.contracttitle)
      .expect(this.contracttitle.innerText).contains(oldcontractname)
      .wait(1000)
      .hover(this.editbtn)
      .click(this.editbtn)
      .wait(1000)
      .hover(this.selectvendor)
      .click(this.namefield)
      .selectText(this.namefield)
      .pressKey('delete')
      .typeText(this.namefield, contractname, { paste: true })
      .hover(this.contractterm)
      .hover(this.startdate)
      .click(this.startdate)
      .pressKey('ctrl+a')
      .pressKey('delete')
      .typeText(this.startdate, startdate, { paste: true })
      .hover(this.alertdate)
      .click(this.alertdate)
      .pressKey('ctrl+A')
      .pressKey('delete')
      .typeText(this.alertdate, alertdate, { paste: true })
      .pressKey('enter')
      .hover(this.contractcost)
      .click(this.contractcost)
      .selectText(this.contractcost)
      .pressKey('delete')
      .wait(500)
      .typeText(this.contractcost, amount, { paste: true })
      .hover(this.addnewloc)
      .hover(this.notes)
      .typeText(this.notes, 'This is only for testing purposes ', { paste: true })
      .hover(this.attachment)
      .click(this.savebtn)
      .wait(1000)
      .hover(this.backbtn)
      .click(this.backbtn)
      .wait(1000)
      .click(this.searchfield)
      .typeText(this.searchfield, contractname, { paste: true })
      .wait(1000)
      .hover(this.searchedcontract)
      .click(this.searchedcontract)
      .wait(500)
      .hover(this.contracttitle)
      .expect(this.contracttitle.innerText).contains(contractname)
      await t.wait(1000)
  }
} export default editcontracts
