import { Selector, t } from 'testcafe'
import golvar from '../Globalvariable'
import navbar from '../Components/navbar'

const golvari = new golvar()
const navibar = new navbar()

class clonecontracts {
  constructor() {
    this.contracttab = Selector('[data-tc-contract-tab]')
    this.selectvendor = Selector('[data-tc-multi-select-vendor]')
    this.namefield = Selector('[data-tc-contract-name]')
    this.contractterm = Selector('[data-tc-open-ended]')
    this.startdate = Selector('[data-tc-start-date]')
    this.alertdate = Selector('[data-tc-alert-date]')
    this.contractcost = Selector('[data-tc-currency]')
    this.addcat = Selector('[data-tc-add-category]')
    this.catfield = Selector('[data-tc-new-category]')
    this.savecatbtn = Selector('[data-tc-save-form-btn="Save Category"]')
    this.locationfield = Selector('[data-tc-location-field]')
    this.staffmember = Selector('[data-tc-multi-user-field]')
    this.savebtn = Selector('[data-tc-save-contract-btn]')
    this.contracttitle = Selector('[data-tc-contract-headline]')
    this.backbtn = Selector('[data-tc-back-btn]')
    this.notes = Selector('[data-tc-contract-notes]')
    this.tags = Selector('[data-tc-contract-tags-field]')
    this.searchfield = Selector('[data-tc-search-contract-field]')
    this.searchedcontract = Selector('[data-tc-searched-contract]')
    this.clonebtn = Selector('[data-tc-clone-contract-btn]')
    this.linkedcntab = Selector('[data-tc-linked-contract-tab]')
    this.parentcontract = Selector('[data-tc-contract-hierarchy]')
    this.copy = '(Copy)'
  }
  async clonecontracts(contractname) {
    await t.hover(navibar.contractsbtn)
      .click(navibar.contractsbtn)
      .expect(golvari.currentpageurl()).contains('/contracts')
      //search the contract
      .hover(this.contracttab)
      .click(this.contracttab)
      .click(this.searchfield)
      .typeText(this.searchfield, contractname, { paste: true })
      .wait(1000)
      .hover(this.searchedcontract)
      .click(this.searchedcontract)
      .hover(this.contracttitle)
      .expect(this.contracttitle.innerText).contains(contractname)
      .wait(1000)
      //clone the contract
      .hover(this.clonebtn)
      .click(this.clonebtn)
      .wait(1000)
      .hover(this.selectvendor)
      .hover(this.namefield)
      .hover(this.contractterm)
      .hover(this.startdate)
      .hover(this.alertdate)
      .hover(this.contractcost)
      .click(this.contractcost)
      .selectText(this.contractcost)
      .pressKey('delete')
      .typeText(this.contractcost, '1232', { paste: true })
      .hover(this.locationfield)
      .hover(this.notes)
      .typeText(this.notes, 'This is only for testing purposes ', { paste: true })
      .click(this.savebtn)
      .wait(1000)
      .hover(this.backbtn)
      .click(this.backbtn)
      .click(this.searchfield)
      .typeText(this.searchfield, contractname + " " + this.copy, { paste: true })
      .wait(2000)
      .hover(this.searchedcontract)
      .click(this.searchedcontract)
      .wait(1000)
      .hover(this.contracttitle)
      .expect(this.contracttitle.innerText).contains(contractname + " " + this.copy)
      .wait(500)
      .click(this.linkedcntab)
      .hover(this.parentcontract)
      .expect(this.parentcontract.exists).ok()
      .expect(this.parentcontract.innerText).contains(contractname)
      await t.wait(1000)
  }
} export default clonecontracts
