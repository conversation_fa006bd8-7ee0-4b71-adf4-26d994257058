import { Selector, t } from 'testcafe'
import golvar from '../Globalvariable'
import navbar from '../Components/navbar'

const golvari = new golvar()
const navibar = new navbar()

class deletecontracts {
  constructor() {
    this.contracttab = Selector('[data-tc-contract-tab]')
    this.searchfield = Selector('[data-tc-search-contract-field]')
    this.searchedcontract = Selector('[data-tc-searched-contract]')
    this.contracttitle = Selector('[data-tc-contract-headline]')
    this.archivebtn = Selector('[data-tc-archive-contract-btn]')
    this.confrimationbtn = Selector('[data-tc-archive-confirmation-btn]')
    this.deletebtn = Selector('[data-tc-delete-contract-btn]')
    this.delete = Selector('[data-tc-delete-confirmation-btn]')
    this.checkcontract = Selector('[data-tc-no-contracts]')
  }
  async deletecontracts(contname) {
    await t.hover(navibar.contractsbtn)
      .click(navibar.contractsbtn)
      .expect(golvari.currentpageurl()).contains('/contracts')
      .hover(this.contracttab)
      .click(this.contracttab)
      .wait(1000)
      .click(this.searchfield)
      .typeText(this.searchfield, contname, { paste: true })
      .wait(1000)
      .hover(this.searchedcontract)
      .click(this.searchedcontract)
      .wait(2000)
      .hover(this.contracttitle)
      .expect(this.contracttitle.innerText).contains(contname)
      .wait(1000)
      .hover(this.archivebtn)
      .click(this.archivebtn)
      .wait(1000)
      .hover(this.confrimationbtn)
      .click(this.confrimationbtn)
      .wait(1000)
      .click(this.searchfield)
      .typeText(this.searchfield, contname, { paste: true })
      .wait(1000)
      .hover(this.searchedcontract)
      .click(this.searchedcontract)
      .wait(2000)
      .click(this.deletebtn)
      .wait(1000)
      .click(this.delete)
      .wait(500)
      .click(this.searchfield)
      .typeText(this.searchfield, contname, { paste: true })
      .wait(1000)
      .expect(this.checkcontract.innerText).contains('No results found')
      await t.wait(2000)
  }
} export default deletecontracts
