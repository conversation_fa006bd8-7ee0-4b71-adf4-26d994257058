import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';

const golvari = new golvar();
const naviBar = new navbar();
const selectCompany = naviBar.selectCompany;

class signinpage {
  constructor() {
    this.emailInput = Selector('[data-tc-email-field]');
    this.emailLabel = Selector('[data-tc-label="email"]');
    this.passInput = Selector('[data-tc-password-field]');
    this.passLabel = Selector('[data-tc-label="password"]');
    this.companySetting = Selector('[data-tc-settings-menu="Company Settings"]');
    this.selectCompany = Selector('[data-tc-select-company="parent"]');
    this.loginBtn = Selector('[data-tc-login-btn]');
  }
  async sigin(emailAddress, password) {
    await t.navigateTo(golvari.baseurl)
      .maximizeWindow()
      .wait(1000)
      .expect(golvari.currentpageurl()).contains('/users/sign_in')
      .wait(1000)
      .expect(this.emailLabel.exists).ok()
      .wait(500)
      .expect(this.emailLabel.innerText).contains('Email')
      .wait(500)
      .hover(this.emailInput)
      .typeText(this.emailInput, emailAddress, { paste: true })
      .wait(1000)
      .expect(this.passLabel.exists).ok()
      .hover(this.passInput)
      .typeText(this.passInput, password, { paste: true })
      .wait(500)
      .expect(this.passLabel.innerText).contains('Password')
      .wait(500)
      .hover(this.loginBtn)
      .click(this.loginBtn)
      .wait(20000);
  }

  async siginCompanySelect(emailAddress, password, companyName) {
    const selectCompany = Selector(`[data-tc-select-company-name="${companyName}"]`);
    await t.navigateTo(golvari.baseurl)
      .maximizeWindow()
      .wait(1000)
      .expect(golvari.currentpageurl()).contains('/users/sign_in')
      .wait(1000)
      .expect(this.emailLabel.exists).ok()
      .wait(500)
      .expect(this.emailLabel.innerText).contains('Email')
      .wait(500)
      .hover(this.emailInput)
      .typeText(this.emailInput, emailAddress, { paste: true })
      .wait(1000)
      .expect(this.passLabel.exists).ok()
      .hover(this.passInput)
      .typeText(this.passInput, password, { paste: true })
      .wait(500)
      .expect(this.passLabel.innerText).contains('Password')
      .wait(500)
      .hover(this.loginBtn)
      .click(this.loginBtn)
      .wait(4000)
      .wait(1000)
      .expect(golvari.currentpageurl()).contains('/select_company')
      .expect(selectCompany.exists).ok('Check that company is present or not.')
      .hover(selectCompany)
      .click(selectCompany)
      .wait(2000);
  }
}
export default signinpage
