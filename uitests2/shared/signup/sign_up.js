import { Selector, t } from "testcafe";
import navbar from "../Components/navbar";
import golvar from "../Globalvariable";

const naviBar = new navbar();
const golVari = new golvar();

class signUp {

  constructor() {
    this.fNameInput = Selector('[data-tc-first-name]');
    this.lNameInput = Selector('[data-tc-last-name]');
    this.emailInput = Selector('[data-tc-email]');
    this.nextBtn = Selector('[data-tc-btn-effect]');
    this.passwordInput = Selector('[data-tc-password]');
    this.finishBtn = Selector('[data-tc-save-form-btn="Finish"]');
    this.companyInput = Selector('[data-tc-company-name]');
    this.subdomainInput = Selector('[data-tc-subdomain]');
    this.closeModal = Selector('[data-tc-modal-close-btn]');
    this.cardTitle = Selector('[data-tc-card-title]');
    this.nextModal = Selector('[data-tc-info-modal-next]');
    this.nextInfoModal = Selector('[data-tc-gotit11] [data-tc-info-modal-gotit1]');
    this.closeDashboard = Selector('[data-tc-info-modal-gotit2]');
  }

  async signUpNewCompany(firstName, lastName, userEmail, password, companyName, subdomainName) {
    await t.wait(1000)
      .maximizeWindow()
      .wait(500)
      .navigateTo(golVari.signupurl)
      .wait(2000)
      .typeText(this.fNameInput, firstName, { paste: true })
      .wait(1000)
      .typeText(this.lNameInput, lastName, { paste: true })
      .wait(1000)
      .typeText(this.emailInput, userEmail, { paste: true })
      .wait(1000)
      .typeText(this.passwordInput, password, { paste: true })
      .wait(1000)
      .hover(this.nextBtn)
      .click(this.nextBtn)
      .wait(3000)
      .expect(golVari.currentpageurl()).contains('/company_info')
      .wait(1000)
      .hover(this.companyInput)
      .typeText(this.companyInput, companyName, { replace: true })
      .wait(1000)
      .hover(this.subdomainInput)
      .typeText(this.subdomainInput, subdomainName, { replace: true })
      .wait(1000)
      .hover(this.finishBtn)
      .click(this.finishBtn)
      .wait(10000)
      .expect(golVari.currentpageurl()).contains('/welcome')
      .wait(20000)
      .hover(this.nextModal)
      .click(this.nextModal)
      .wait(1000)
      .hover(this.nextInfoModal)
      .click(this.nextInfoModal)
      .wait(1000)
      .hover(this.closeDashboard)
      .click(this.closeDashboard)
      .wait(1000)
      .hover(naviBar.assetsbtn)
      .click(naviBar.assetsbtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(1000)
      .expect(this.cardTitle.exists).ok()
      .wait(500)
      .hover(this.closeModal)
      .click(this.closeModal)
      .wait(1000)
      .expect(this.cardTitle.exists).notOk()
      .wait(500)
      .hover(naviBar.contractsbtn)
      .click(naviBar.contractsbtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/contracts')
      .wait(1000)
      .expect(this.cardTitle.exists).ok()
      .wait(500)
      .hover(this.closeModal)
      .click(this.closeModal)
      .wait(1000)
      .expect(this.cardTitle.exists).notOk()
      .wait(500)
      .hover(naviBar.vendorbtn)
      .click(naviBar.vendorbtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/vendors/dashboard_transactions')
      .wait(1000)
      .expect(this.cardTitle.exists).ok()
      .wait(500)
      .hover(this.closeModal)
      .click(this.closeModal)
      .wait(1000)
      .expect(this.cardTitle.exists).notOk()
      .wait(500)
      .hover(naviBar.telecombtn)
      .click(naviBar.telecombtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('telecom_providers/all')
      .wait(1000)
      .expect(this.cardTitle.exists).ok()
      .wait(500)
      .hover(this.closeModal)
      .click(this.closeModal)
      .wait(1000)
      .expect(this.cardTitle.exists).notOk()
      .wait(500)
      .hover(naviBar.helpdesk)
      .click(naviBar.helpdesk)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/help_tickets/dashboard')
      .wait(1000)
      .expect(this.cardTitle.exists).ok()
      .wait(500)
      .hover(this.closeModal)
      .click(this.closeModal)
      .wait(1000)
      .expect(this.cardTitle.exists).notOk()
      .wait(500)
  }
} export default signUp
