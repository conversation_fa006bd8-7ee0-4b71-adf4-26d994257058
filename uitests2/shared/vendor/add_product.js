import { Selector, t } from 'testcafe';
import navbar from '../Components/navbar';
import golvar from '../Globalvariable';
import addVendor from './add_vendor';

const golVari = new golvar();
const naviBar = new navbar();
const addVendors = new addVendor();

class addProduct {

  constructor() {
    this.productTab = Selector('[data-tc-products]');
    this.addProductBtn = Selector('[data-tc-add-product]');
    this.vendorField = Selector('[data-tc-current-vendor-name]');
    this.productNameField = Selector('[data-tc-name]');
    this.totalLicenseField = Selector('[data-tc-total-licenses]');
    this.usedLicenseField = Selector('[data-tc-consumed-licenses]');
    this.typeField = Selector('[data-tc-product-type]');
    this.arrivalDateField = Selector('[data-tc-arrival-date]');
    this.viewProductName = Selector('[data-tc-view-product-name]');
    this.saveBtn = Selector('[data-tc-save-form-btn="Save Product"]');
  }

  async addProducts(vendorName, productName, totalLiscense, licenseInUse, category, productType, arrivalDate) {
    await t.hover(naviBar.vendorbtn)
      .click(naviBar.vendorbtn)
      .expect(golVari.currentpageurl()).contains('/vendors/dashboard_transactions')
      .wait(500)
      .click(addVendors.vendorTab)
      .expect(golVari.currentpageurl()).contains('/vendors/all')
      .click(addVendors.searchField)
      .typeText(addVendors.searchField, vendorName, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .expect(addVendors.viewVendorName.innerText).contains(vendorName)
      .click(addVendors.viewVendorName)
      .wait(1000)
      .expect(addVendors.viewVendorName.innerText).contains(vendorName)
    await t.wait(1000)
      .hover(this.productTab)
      .click(this.productTab)
      .expect(golVari.currentpageurl()).contains('/products')
      .hover(this.addProductBtn)
      .click(this.addProductBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/products/new')
      .wait(1000)
      .expect(this.vendorField.innerText).contains(vendorName)
      .hover(this.productNameField)
      .typeText(this.productNameField, productName, { paste: true })
      .hover(this.totalLicenseField)
      .typeText(this.totalLicenseField, totalLiscense, { paste: true })
      .hover(this.usedLicenseField)
      .typeText(this.usedLicenseField, licenseInUse, { paste: true })
      .hover(addVendors.vendorCategory)
      .click(addVendors.vendorCategory)
      .click(`[data-tc-category-option="${category}"]`)
      .wait(1000)
      .hover(this.typeField)
      .typeText(this.typeField, productType, { paste: true })
      .hover(this.arrivalDateField)
      .click(this.arrivalDateField)
      .typeText(this.arrivalDateField, arrivalDate, { paste: true })
      .wait(500)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(1000)
      .hover(this.viewProductName)
      .expect(this.viewProductName.innerText).contains(productName)
      .wait(1000)
  }
} export default addProduct
