import { Selector, t } from 'testcafe';
import navbar from '../Components/navbar';
import golvar from '../Globalvariable';
import editTransaction from './edit_transaction ';
import addTransaction from './add_transaction';

const golVari = new golvar();
const naviBar = new navbar();
const editTxn = new editTransaction();
const addNewTxn = new addTransaction();

class deleteTransaction {

  constructor() {
    this.transactionTab = Selector('[data-tc-transactions-tab]');
    this.archiveIcon = Selector('[data-tc-archive-transaction]');
    this.confirmArchive = Selector('[data-tc-archive-transaction-btn]');
    this.archivedTab = Selector('[data-tc-archived-tab]');
    this.deleteIcon = Selector('[data-tc-delete-transaction]');
    this.toggleIcon = Selector('[data-tc-toggle-transactions]');
    this.confirmDelete = Selector('[data-tc-confirm-delete-btn]');
    this.DeletedTransaction = Selector('[data-tc-no-transactions]');
  }

  async deleteTransactions(transactionName) {
    await t.hover(naviBar.vendorbtn)
      .click(naviBar.vendorbtn)
      .expect(golVari.currentpageurl()).contains('/vendors/dashboard_transactions')
      .wait(500)
      .hover(this.transactionTab)
      .click(this.transactionTab)
      .expect(golVari.currentpageurl()).contains('/vendors/transactions/recognized')
      .hover(this.toggleIcon)
      .click(this.toggleIcon)
      .wait(500)
      .hover(addNewTxn.searchField)
      .typeText(addNewTxn.searchField, transactionName, { paste: true })
      .wait(1000)
      .hover(editTxn.hoverIcon)
      .hover(this.archiveIcon)
      .click(this.archiveIcon)
      .wait(1000)
      .hover(this.confirmArchive)
      .click(this.confirmArchive)
      .wait(1000)
      .hover(this.archivedTab)
      .click(this.archivedTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('vendors/transactions/archived')
      .wait(1000)
      .hover(addNewTxn.searchField)
      .hover(addNewTxn.searchField)
      .typeText(addNewTxn.searchField, transactionName, { paste: true })
      .pressKey('enter')
      .wait(2000)
      .hover(editTxn.hoverIcon)
      .click(this.deleteIcon)
      .wait(500)
      .click(this.confirmDelete)
      .wait(1000)
      .hover(addNewTxn.searchField)
      .typeText(addNewTxn.searchField, transactionName, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .expect(this.DeletedTransaction.innerText).contains('There are no transactions.')
  }
} export default deleteTransaction
