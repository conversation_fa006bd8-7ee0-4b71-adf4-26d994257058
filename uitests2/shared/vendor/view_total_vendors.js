import { Selector, t } from 'testcafe';
import navbar from '../Components/navbar';
import addVendor from './add_vendor';
import golvar from '../Globalvariable';

const naviBar = new navbar();
const addVendors = new addVendor();
const golVari = new golvar();

class viewTotalVendor {

  constructor() {
    this.totalVendors = Selector('[data-tc-total-vendors]');
  }

  async viewCounter(counter) {
    await t.hover(naviBar.vendorbtn)
      .click(naviBar.vendorbtn)
      .expect(golVari.currentpageurl()).contains('/vendors/dashboard_transactions')
      .hover(addVendors.vendorTab)
      .click(addVendors.vendorTab)
      .hover(this.totalVendors)
      .expect(this.totalVendors.innerText).contains(counter)
  }
} export default viewTotalVendor
