import { Selector, t } from 'testcafe';
import navbar from '../Components/navbar';
import golvar from '../Globalvariable';
import vendorVar from './vendor_variables';

const golVari = new golvar();
const naviBar = new navbar();
const vendorVari = new vendorVar();

class addVendor {

  constructor() {
    this.addVendorBtn = Selector('[data-tc-add-vendor-btn]');
    this.selectAddVendor = Selector('[data-tc-select="add vendor"]');
    this.vendorTab = Selector('[data-tc-vendor-tab]');
    this.vendorName = Selector('[data-tc-name]');
    this.vendorUrl = Selector('[data-tc-vendor-url]');
    this.vendorCategory = Selector('[data-tc-category-id]');
    this.vendorBussinessUnit = Selector('[data-tc-vendor-business-unit]');
    this.vendorDescription = Selector('[data-tc-vendor-description]');
    this.vendorAccountNumber = Selector('[data-tc-vendor-account-number]');
    this.vendorTags = Selector('[data-tc-vendor-tag]');
    this.vendorAddress = Selector('[data-tc-vendor-address]');
    this.locationDetials = Selector('[data-tc-location-details]');
    this.relatedItems = Selector('[data-tc-select-ulink]');
    this.vendorCity = Selector('[data-tc-vendor-city]');
    this.vendorState = Selector('[data-tc-vendor-state]');
    this.vendorCountry = Selector('[data-tc-vendor-country]');
    this.countryOption = this.vendorCountry.find('option');
    this.vendorZip = Selector('[data-tc-vendor-zip]');
    this.vendorPhoneNumber = Selector('[data-tc-vendor-phone]');
    this.addAlertBtn = Selector('[data-tc-vendor-alert]');
    this.alertPercentage = Selector('[data-tc-alert-percent]');
    this.paymentMethod = Selector('[data-tc-vendor-billing-requirements]');
    this.paymentTerms = Selector('[data-tc-vendor-payment-terms]');
    this.baseProduct = Selector('[data-tc-base-product-check]');
    this.saveBtn = Selector('[data-tc-save-form-btn="Save vendor"]');
    this.viewVendorName = Selector('[data-tc-vendor-name]');
    this.vendorInfoTab = Selector('[data-tc-vendor-basic-information]');
    this.viewAddress = Selector('[data-tc-view-address1]');
    this.viewCity = Selector('[data-tc-view-city-state-zip]');
    this.viewUrl = Selector('[data-tc-view-url]');
    this.viewDescription = Selector('[data-tc-view-description]');
    this.contactTab = Selector('[data-tc-vendor-contacts]');
    this.addContactBtn = Selector('[data-tc-add-contact]');
    this.billingTab = Selector('[data-tc-vendor-billing]');
    this.managerTab = Selector('[data-tc-vendor-manager]');
    this.assignManagerBtn = Selector('[data-tc-assign-manager]');
    this.managerDropDown = Selector('[data-tc-multi-user-field]');
    this.selectManager1 = this.managerDropDown.find('li');
    this.contractTab = Selector('[data-tc-vendor-contracts]');
    this.assignContractBtn = Selector('[data-tc-add-contract]');
    this.contractDropDown = Selector('[data-tc-contract-name]');
    this.selectConract = Selector('[data-tc-contract-name] .multiselect__element');
    this.alertsTab = Selector('[data-tc-alert-tab]');
    this.fNameField = Selector('[data-tc-first-name]');
    this.lNameFIeld = Selector('[data-tc-last-name]');
    this.email = Selector('[data-tc-email]');
    this.phoneNumberField = Selector('[id="phone_number_phone_number"]');
    this.role = Selector('[data-tc-role]');
    this.notes = Selector('[data-tc-contact-notes]');
    this.searchField = Selector('[data-tc-search-vendor-field]');
    this.saveContactBtn = Selector('[data-tc-save-form-btn="Save contact"]');
    this.notifyPopUp = Selector('[notification-content]');
    this.viewContactName = Selector('[data-tc-view-contact="fullName"]');
    this.viewContactEmail = Selector('[data-tc-view-contact="email"]');
    this.viewContactRole = Selector('[data-tc-view-contact="role"]');
    this.viewContactDepart = Selector('[data-tc-view-contact="department"]');
    this.viewContactNote = Selector('[data-tc-view-contact="notes"]');
    this.viewPaymentMethods = Selector('[data-tc-view-methods]');
    this.viewPaymentTerms = Selector('[data-tc-view-terms]');
    this.viewManager = Selector('[data-tc-user-name]');
    this.countryCodeLabel = Selector('input[placeholder="Country code"]');
    this.selectCountryCode = Selector('.dots-text');
  }

  async addVendors(name, URL, category, bussinessUnit, description, accountNumber, tags, address, city, state, country, zipCode, phoneNumber, alertPercent, billingMethods, billingTerms, staffUser, contractName) {
    const viewPhoneNumber = Selector(`[data-tc-view-number="${vendorVari.countryCode} ${phoneNumber}"]`);
    await t.hover(naviBar.vendorbtn)
      .click(naviBar.vendorbtn)
      .expect(golVari.currentpageurl()).contains('/vendors/dashboard_transactions')
      .hover(this.addVendorBtn)
      .click(this.addVendorBtn)
      .wait(500)
      .hover(this.selectAddVendor)
      .click(this.selectAddVendor)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/vendors/new')
      .typeText(this.vendorName, name, { paste: true })
      .typeText(this.vendorUrl, URL, { paste: true })
      .hover(this.vendorCategory)
      .click(this.vendorCategory)
      .click(`[data-tc-category-option="${category}"]`)
      .wait(1000)
      .typeText(this.vendorBussinessUnit, bussinessUnit, { paste: true })
      .wait(500)
      .typeText(this.vendorDescription, description, { paste: true })
      .wait(500)
      .typeText(this.vendorAccountNumber, accountNumber, { paste: true })
      .wait(500)
      .hover(this.vendorTags)
    await t.click(this.vendorTags)
      .typeText(this.vendorTags, tags, { paste: true })
      .scroll(this.vendorTags, 'center')
      .pressKey('enter')
      .wait(1000)
      .hover(this.relatedItems)
      .hover(this.locationDetials)
      .wait(500)
      .typeText(this.vendorAddress, address, { paste: true })
      .wait(500)
      .typeText(this.vendorCity, city, { paste: true })
      .wait(500)
      .typeText(this.vendorState, state, { paste: true })
      .wait(500)
      .hover(this.vendorCountry)
      .click(this.vendorCountry)
      .wait(500)
      .click(this.countryOption.withText(country))
      .wait(500)
      .typeText(this.vendorZip, zipCode, { paste: true })
      .click(this.countryCodeLabel)
      .wait(500)
      .click(this.selectCountryCode.withText(country))
      .typeText(this.vendorPhoneNumber, phoneNumber, { paste: true })
      .click(this.addAlertBtn)
      .typeText(this.alertPercentage, alertPercent, { paste: true })
      .typeText(this.paymentMethod, billingMethods, { paste: true })
      .typeText(this.paymentTerms, billingTerms, { paste: true })
      .wait(1000)
      .hover(this.baseProduct)
      .click(this.baseProduct)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(1000)
      .hover(this.viewVendorName)
      .expect(this.viewVendorName.innerText).contains(name)
      .hover(this.vendorInfoTab)
      .click(this.vendorInfoTab)
      .expect(golVari.currentpageurl()).contains('/basic_information')
      .hover(this.viewAddress)
      .expect(this.viewAddress.innerText).contains(address)
      .hover(this.viewCity)
      .expect(this.viewCity.innerText).contains(city + ' ' + state + ' ' + zipCode)
      .hover(this.viewUrl)
      .expect(this.viewUrl.innerText).contains(URL)
      .hover(this.viewDescription)
      .expect(this.viewDescription.innerText).contains(description)
      .expect(viewPhoneNumber.innerText).contains(`${vendorVari.countryCode} ${phoneNumber}`)
      .hover(this.contactTab)
      .click(this.contactTab)
    // await t.this.addVendorContact('John', 'tester', '<EMAIL>', '2502322323', 'HR', 'Admin', 'Onyl for testing purposes')
    // await t.wait(1000)
      .hover(this.billingTab)
      .click(this.billingTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/billing')
      .expect(this.viewPaymentMethods.innerText).contains(billingMethods)
      .expect(this.viewPaymentTerms.innerText).contains(billingTerms)
      .wait(1000)
      .hover(this.alertsTab)
      .click(this.alertsTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/alerts')
      .wait(1000)
      .expect(Selector(`[data-tc-alert-trigger="${alertPercent}"]`).innerText).contains(alertPercent)
      .wait(1000)
      .hover(this.contractTab)
      .click(this.contractTab)
      .expect(golVari.currentpageurl()).contains('/contracts')
      .hover(this.assignContractBtn)
      .click(this.assignContractBtn)
      .click(this.contractDropDown)
      .click(this.selectConract.withText(contractName))
      .wait(500)
      .expect(Selector(`[data-tc-contract-name="${contractName}"]`).innerText).contains(contractName)
      .wait(1000)
  }

  async addVendorContact(firstName, lastName, email, phoneNumber, department, role, notes) {
    await t.expect(golVari.currentpageurl()).contains('/contacts')
      .hover(this.addContactBtn)
      .click(this.addContactBtn)
      .typeText(this.fNameField, firstName, { paste: true })
      .typeText(this.lNameFIeld, lastName, { paste: true })
    await t.typeText(this.email, email, { paste: true })
      .typeText(this.phoneNumberField, phoneNumber, { paste: true })
      .typeText(this.department, department, { paste: true })
      .typeText(this.role, role, { paste: true })
      .typeText(this.notes, notes, { paste: true })
      .hover(this.saveContactBtn)
      .click(this.saveContactBtn)
      .expect(this.notifyPopUp.innerText).contains('Contact was successfully created.')
      .wait(1000)
      .click(`[data-tc-contact-email="${email}"]`)
      .wait(500)
      .hover(this.viewContactName)
      .expect(this.viewContactName.innerText).contains(firstName + ' ' + lastName)
      .hover(this.viewContactEmail)
      .expect(thisviewContactEmail.innerText).contains(email)
      .hover(this.viewContactRole)
      .expect(this.viewContactRole.innerText).contains(role)
      .hover(this.viewContactDepart)
      .expect(this.viewContactDepart.innerText).contains(department)
      .hover(this.viewContactNote)
      .expect(this.viewContactNote.innerText).contains(notes)
      .wait(1000)
  }
} export default addVendor
