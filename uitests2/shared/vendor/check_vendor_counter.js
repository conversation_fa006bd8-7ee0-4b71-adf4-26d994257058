import { Selector, t } from 'testcafe';
import golvar from '../Globalvariable';
import navbar from '../Components/navbar';
import addVendor from './add_vendor';


const golVari = new golvar();
const naviBar = new navbar();
const vendorPage = new addVendor();

class viewCount {

  constructor() {
    this.viewVendorCount = Selector('[data-tc-total-vendors]');
  }

  async viewCounts(count) {
    const totalVendors = `Vendors (${count})`;
    await t.hover(naviBar.vendorbtn)
      .click(naviBar.vendorbtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/vendors/dashboard_transactions')
      .wait(1000)
      .hover(vendorPage.vendorTab)
      .wait(500)
      .click(vendorPage.vendorTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/vendors/all')
      .wait(500)
      .hover(this.viewVendorCount)
      .wait(500)
      .expect(this.viewVendorCount.innerText).contains(totalVendors)
      .wait(1000)
  }
} export default viewCount
