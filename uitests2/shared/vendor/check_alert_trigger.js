import { Selector, t } from 'testcafe';
import navbar from '../Components/navbar';
import golvar from '../Globalvariable';
import addVendor from './add_vendor';

const golVari = new golvar();
const naviBar = new navbar();
const addVendors = new addVendor();

class viewAlert {

  constructor() {
    this.viewalertBadge = addVendors.alertsTab.find('span');
  }

  async viewAlerts(vendorName, alerts, alertPrecentage, overPercentage) {
    const viewAlertPrecent = Selector(`[data-tc-alert-trigger="${alertPrecentage}"]`);
    const viewOverPrecent = viewAlertPrecent.find('span');
    await t.hover(naviBar.vendorbtn)
      .click(naviBar.vendorbtn)
      .expect(golVari.currentpageurl()).contains('/vendors/dashboard_transactions')
      .wait(500)
      .click(addVendors.vendorTab)
      .expect(golVari.currentpageurl()).contains('/vendors/all')
      .wait(1000)
      .click(addVendors.searchField)
      .typeText(addVendors.searchField, vendorName, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .expect(addVendors.viewVendorName.innerText).contains(vendorName)
      .click(addVendors.viewVendorName)
      .wait(1000)
      .expect(addVendors.viewVendorName.innerText).contains(vendorName)
      .wait(1000)
      .expect(this.viewalertBadge.innerText).contains(alerts)
      .wait(1000)
      .click(addVendors.alertsTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/alerts')
      .wait(1000)
      .expect(viewAlertPrecent.innerText).contains(`${alertPrecentage}% trigger`)
      .wait(1000)
      .expect(viewOverPrecent.innerText).contains(`/ ${overPercentage}% over`)
      .wait(1000)
  }
} export default viewAlert
