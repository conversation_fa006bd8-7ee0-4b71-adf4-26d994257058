import { Selector, t } from 'testcafe';
import navbar from '../Components/navbar';
import golvar from '../Globalvariable';
import addVendor from './add_vendor';

const golVari = new golvar();
const naviBar = new navbar();
const addVendors = new addVendor();

class editVendor {

  constructor() {
    this.editBtn = Selector('[data-tc-edit-vendor]');
    this.unassignUser = Selector('[data-tc-unassign-manager]');
    this.confirmUserBtn = Selector('[data-tc-manager-unassign-btn]');
    this.unassignContract = Selector('[data-tc-unassign-contract]');
    this.confirmContract = Selector('[data-tc-yes-unassign-contract]');
  }

  async editVendors(vendorName, editName, editURL, editCategory, editBussinessUnit, editDescription, editAccountNumber, editTags, editAddress, editCity, editState, editCountry, editZipCode, editPhoneNumber, editAlertPercent, editBillingMethods, editBillingTerms) {
    await t.hover(naviBar.vendorbtn)
      .click(naviBar.vendorbtn)
      .expect(golVari.currentpageurl()).contains('/vendors/dashboard_transactions')
      .wait(500)
      .click(addVendors.vendorTab)
      .expect(golVari.currentpageurl()).contains('/vendors/all')
      .click(addVendors.searchField)
      .typeText(addVendors.searchField, vendorName, { paste: true })
      .pressKey('enter')
      .wait(2000)
      .expect(addVendors.viewVendorName.innerText).contains(vendorName)
      .click(addVendors.viewVendorName)
      .wait(2000)
      .expect(addVendors.viewVendorName.innerText).contains(vendorName)
    await t.wait(1000)
      .hover(this.editBtn)
      .click(this.editBtn)
      .typeText(addVendors.vendorName, editName, { replace: true })
      .typeText(addVendors.vendorUrl, editURL, { replace: true })
      .hover(addVendors.vendorCategory)
      .click(addVendors.vendorCategory)
      .click(`[data-tc-category-option="${editCategory}"]`)
      .typeText(addVendors.vendorBussinessUnit, editBussinessUnit, { replace: true })
      .typeText(addVendors.vendorDescription, editDescription, { replace: true })
      .typeText(addVendors.vendorAccountNumber, editAccountNumber, { replace: true })
      .wait(1000)
      .hover(addVendors.vendorTags)
      .click(addVendors.vendorTags)
      .hover(addVendors.vendorTags)
      .hover(addVendors.relatedItems)
      .hover(addVendors.locationDetials)
      .wait(1000)
      .typeText(addVendors.vendorAddress, editAddress, { replace: true })
      .typeText(addVendors.vendorCity, editCity, { replace: true })
      .typeText(addVendors.vendorState, editState, { replace: true })
      .hover(addVendors.vendorCountry)
      .click(addVendors.vendorCountry)
      .click(addVendors.countryOption.withText(editCountry))
      .typeText(addVendors.vendorZip, editZipCode, { replace: true })
      .typeText(addVendors.vendorPhoneNumber, editPhoneNumber, { replace: true })
      .typeText(addVendors.alertPercentage, editAlertPercent, { replace: true })
      .typeText(addVendors.paymentMethod, editBillingMethods, { replace: true })
      .typeText(addVendors.paymentTerms, editBillingTerms, { replace: true })
      .wait(1000)
      .hover(addVendors.saveBtn)
      .click(addVendors.saveBtn)
      .wait(2000)
      .hover(addVendors.viewVendorName)
      .expect(addVendors.viewVendorName.innerText).contains(editName)
      .hover(addVendors.vendorInfoTab)
      .click(addVendors.vendorInfoTab)
      .expect(golVari.currentpageurl()).contains('/basic_information')
      .hover(addVendors.viewAddress)
      .expect(addVendors.viewAddress.innerText).contains(editAddress)
      .hover(addVendors.viewCity)
      .expect(addVendors.viewCity.innerText).contains(editCity + ' ' + editState + ' ' + editZipCode)
      .hover(addVendors.viewUrl)
      .expect(addVendors.viewUrl.innerText).contains(editURL)
      .hover(addVendors.viewDescription)
      .expect(addVendors.viewDescription.innerText).contains(editDescription)
      .hover(addVendors.billingTab)
      .click(addVendors.billingTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/billing')
      .expect(addVendors.viewPaymentMethods.innerText).contains(editBillingMethods)
      .expect(addVendors.viewPaymentTerms.innerText).contains(editBillingTerms)
      .wait(1000)
      .hover(addVendors.alertsTab)
      .click(addVendors.alertsTab)
      .expect(golVari.currentpageurl()).contains('/alerts')
      .wait(1000)
      .expect(Selector(`[data-tc-alert-trigger="${editAlertPercent}"]`).innerText).contains(editAlertPercent)
      .hover(addVendors.contractTab)
      .click(addVendors.contractTab)
      .expect(golVari.currentpageurl()).contains('/contracts')
      .wait(500)
      .hover(this.unassignContract)
      .click(this.unassignContract)
      .wait(1000)
      .hover(this.confirmContract)
      .click(this.confirmContract)
      .wait(1000)
  }
} export default editVendor
