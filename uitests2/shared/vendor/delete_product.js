import { Selector, t } from "testcafe";
import golvar from "../Globalvariable";
import navbar from "../Components/navbar";
import addVendor from "./add_vendor";
import editProduct from "./edit_product";
import addProduct from "./add_product";

const golVari = new golvar();
const naviBar = new navbar();
const addVendors = new addVendor();
const editProducts = new editProduct();
const addNewProduct = new addProduct();

class deleteProduct {

  constructor() {
    this.deleteBtn = Selector('[data-tc-delete-product-btn]');
    this.confirmDelete = Selector('[data-tc-delete-product]');
    this.notifyPop = Selector('.notification-content');
  }

  async deleteProducts(vendorName, productName) {
    await t.hover(naviBar.vendorbtn)
      .click(naviBar.vendorbtn)
      .expect(golVari.currentpageurl()).contains('/vendors/dashboard_transactions')
      .wait(500)
      .click(addVendors.vendorTab)
      .expect(golVari.currentpageurl()).contains('/vendors/all')
      .click(addVendors.searchField)
      .typeText(addVendors.searchField, vendorName, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .expect(addVendors.viewVendorName.innerText).contains(vendorName)
      .click(addVendors.viewVendorName)
      .wait(1000)
      .expect(addVendors.viewVendorName.innerText).contains(vendorName)
    await t.wait(1000)
      .hover(addNewProduct.productTab)
      .click(addNewProduct.productTab)
      .expect(golVari.currentpageurl()).contains('/products')
      .wait(1000)
      .hover(editProducts.viewProdName.withText(productName))
      .click(editProducts.viewProdName.withText(productName))
      .wait(1000)
      .hover(this.deleteBtn)
      .click(this.deleteBtn)
      .hover(this.confirmDelete)
      .click(this.confirmDelete)
      .wait(1000)
      .hover(this.notifyPop)
      .expect(this.notifyPop.innerText).contains('Successfully deleted product')
      .wait(500)
  }
} export default deleteProduct
