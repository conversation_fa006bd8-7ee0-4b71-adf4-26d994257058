import { Selector, t } from 'testcafe';
import navbar from '../Components/navbar';
import golvar from '../Globalvariable';
import addVendor from './add_vendor';
import addTransaction from './add_transaction';
import deleteVendor from './delete_vendor';

const golVari = new golvar();
const naviBar = new navbar();
const addVendors = new addVendor();
const addNewTxn = new addTransaction();
const delVendorPage = new deleteVendor();

class editTransaction {

  constructor() {
    this.hoverIcon = Selector('[data-tc-hover-icon]');
    this.editIcon = Selector('[data-tc-edit-transaction]');
  }

  async editTransactions(vendorName, transactionName, editName, editDate, editCurrency, filterApplied) {
    const removeFilter = Selector(`[data-tc-remove-filter="${filterApplied}"]`);
    await t.hover(naviBar.vendorbtn)
      .click(naviBar.vendorbtn)
      .expect(golVari.currentpageurl()).contains('/vendors/dashboard_transactions')
      .wait(500)
      .click(addVendors.vendorTab)
      .expect(golVari.currentpageurl()).contains('/vendors/all')
      .click(addVendors.searchField)
      .typeText(addVendors.searchField, vendorName, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .expect(addVendors.viewVendorName.innerText).contains(vendorName)
      .click(addVendors.viewVendorName)
      .wait(1000)
      .expect(addVendors.viewVendorName.innerText).contains(vendorName)
    await t.wait(1000)
      .hover(addNewTxn.transactionsTab)
      .click(addNewTxn.transactionsTab)
      .hover(addNewTxn.searchField)
      .typeText(addNewTxn.searchField, transactionName, { paste: true })
      .scroll(addNewTxn.searchField, 'center')
      .hover(addNewTxn.viewTransactionName)
      .expect(addNewTxn.viewTransactionName.innerText).contains(transactionName)
      .wait(1000)
      .hover(this.hoverIcon)
      .hover(this.editIcon)
      .click(this.editIcon)
      .wait(1000)
      .typeText(addNewTxn.transactionName, editName, { replace: true })
      .typeText(addNewTxn.amount, editCurrency, { replace: true })
      .hover(addNewTxn.transactionDate)
      .typeText(addNewTxn.transactionDate, editDate, { replace: true })
      .hover(addNewTxn.saveBtn)
      .click(addNewTxn.saveBtn)
      .wait(2000)
      .click(removeFilter)
      .wait(3000)
      .hover(addNewTxn.searchField)
      .typeText(addNewTxn.searchField, editName, { replace: true })
      .scroll(addNewTxn.searchField, 'center')
      .wait(1000)
      .hover(addNewTxn.viewTransactionName)
      .expect(addNewTxn.viewTransactionName.innerText).contains(editName)
      .wait(1000)
  }
} export default editTransaction
