import { Selector, t } from 'testcafe';
import navbar from '../Components/navbar';
import golvar from '../Globalvariable';

const golVari = new golvar();
const naviBar = new navbar();

class importVendor {

  constructor() {
    this.importDropDown = Selector('[data-tc-vendor-import-dropdown]');
    this.importVendorBtn = Selector('[data-tc-import-vendors]');
    this.importTransactionBtn = Selector('[data-tc-import-transactions]');
    this.uploadDoc = Selector('[data-tc-upload-file]');
    this.verifyVendorBtn = Selector('[data-tc-process-file-btn]');
    this.finishImport = Selector('[data-tc-finish-import-btn]');
  }

  async importVendors() {
    await t.hover(naviBar.vendorbtn)
      .click(naviBar.vendorbtn)
      .expect(golVari.currentpageurl()).contains('/vendors/dashboard_transactions')
      .wait(500)
      .hover(this.importDropDown)
      .click(this.importDropDown)
      .wait(500)
      .hover(this.importVendorBtn)
      .click(this.importVendorBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/vendors/import_vendors')
      .wait(1000)
      .hover(this.uploadDoc)
      .setFilesToUpload('', 'uitests2/shared/vendor/vendors_data.xlsx')
      .wait(1000)
      .hover(this.verifyVendorBtn)
      .click(this.verifyVendorBtn)
      .wait(1000)
      .hover(this.finishImport)
      .click(this.finishImport)
      .wait(500)
  }

  async importTransactions() {
    await t.hover(naviBar.vendorbtn)
      .click(naviBar.vendorbtn)
      .expect(golVari.currentpageurl()).contains('/vendors/dashboard_transactions')
      .wait(500)
      .hover(this.importDropDown)
      .click(this.importDropDown)
      .wait(500)
      .hover(this.importTransactionBtn)
      .click(tthis.importTransactionBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/transactions/import_transactions')
      .wait(1000)
      .hover(this.uploadDoc)
      .setFilesToUpload('', 'uitests2/shared/vendor/vendors_data.xlsx')
      .wait(1000)
      .hover(this.verifyVendorBtn)
      .click(this.verifyVendorBtn)
      .wait(1000)
      .hover(this.finishImport)
      .click(this.finishImport)
      .wait(500)
  }
} export default importVendor
