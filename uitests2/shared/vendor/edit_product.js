import { Selector, t } from 'testcafe';
import navbar from '../Components/navbar';
import golvar from '../Globalvariable';
import addVendor from './add_vendor';
import addProduct from './add_product';

const golVari = new golvar();
const naviBar = new navbar();
const addVendors = new addVendor();
const addNewProduct = new addProduct();

class editProduct {

  constructor() {
    this.productTab = Selector('[data-tc-products]');
    this.viewProdName = Selector('[data-tc-product]');
    this.editBtn = Selector('[data-tc-edit-product-btn]');
    this.viewEditTitle = Selector('[data-tc-edit-product-title]');

  }

  async editProducts(vendorName, productName, editProdName, editTotalLiscense, editLicenseInUse, editCategory, editProdType, editArrivalDate) {
    await t.hover(naviBar.vendorbtn)
      .click(naviBar.vendorbtn)
      .expect(golVari.currentpageurl()).contains('/vendors/dashboard_transactions')
      .wait(500)
      .click(addVendors.vendorTab)
      .expect(golVari.currentpageurl()).contains('/vendors/all')
      .click(addVendors.searchField)
      .typeText(addVendors.searchField, vendorName, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .expect(addVendors.viewVendorName.innerText).contains(vendorName)
      .click(addVendors.viewVendorName)
      .wait(1000)
      .expect(addVendors.viewVendorName.innerText).contains(vendorName)
    await t.wait(1000)
      .hover(addNewProduct.productTab)
      .click(addNewProduct.productTab)
      .expect(golVari.currentpageurl()).contains('/products')
      .hover(this.viewProdName.withText(productName))
      .click(this.viewProdName.withText(productName))
      .hover(addNewProduct.viewProductName)
      .expect(addNewProduct.viewProductName.innerText).contains(productName)
      .hover(this.editBtn)
      .click(this.editBtn)
      .wait(1000)
      .expect(this.viewEditTitle.innerText).contains('Edit' + ' ' + productName)
      .hover(addNewProduct.productNameField)
      .typeText(addNewProduct.productNameField, editProdName, { replace: true })
      .hover(addNewProduct.totalLicenseField)
      .typeText(addNewProduct.totalLicenseField, editTotalLiscense, { replace: true })
      .hover(addNewProduct.usedLicenseField)
      .typeText(addNewProduct.usedLicenseField, editLicenseInUse, { replace: true })
      .hover(addVendors.vendorCategory)
      .click(addVendors.vendorCategory)
      .click(`[data-tc-category-option="${editCategory}"]`)
      .wait(1000)
      .hover(addNewProduct.typeField)
      .typeText(addNewProduct.typeField, editProdType, { replace: true })
      .hover(addNewProduct.arrivalDateField)
      .click(addNewProduct.arrivalDateField)
      .typeText(addNewProduct.arrivalDateField, editArrivalDate, { replace: true })
      .wait(500)
      .hover(addNewProduct.saveBtn)
      .click(addNewProduct.saveBtn)
      .wait(1000)
      .hover(addNewProduct.viewProductName)
      .expect(addNewProduct.viewProductName.innerText).contains(editProdName)
      .wait(1000)
  }
} export default editProduct
