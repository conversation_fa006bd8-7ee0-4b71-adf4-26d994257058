import { Selector, t } from 'testcafe';
import navbar from '../Components/navbar';
import golvar from '../Globalvariable';
import addVendor from './add_vendor';

const golVari = new golvar();
const naviBar = new navbar();
const addVendors = new addVendor();

class deleteVendor {

  constructor() {
    this.archiveVendor = Selector('[data-tc-archive-vendor]');
    this.confirmArchive = Selector('[data-tc-vendor-archive-modal-btn]');
    this.filterBtn = Selector('[data-tc-filter-menu-button]');
    this.filterStatus = Selector('[data-tc-filter-status]');
    this.selectFilter = Selector('[data-tc-filter-option="Archived"]');
    this.delVendorBtn = Selector('[data-tc-delete-vendor]');
    this.confirmDelBtn = Selector('[data-tc-vendor-delete-modal-btn]');
    this.viewDelVendor = Selector('[data-tc-no-result-vendor]');
  }

  async deleteVendors(vendorName) {
    await t.hover(naviBar.vendorbtn)
      .click(naviBar.vendorbtn)
      .expect(golVari.currentpageurl()).contains('/vendors/dashboard_transactions')
      .hover(addVendors.vendorTab)
      .click(addVendors.vendorTab)
      .expect(golVari.currentpageurl()).contains('/vendors/all')
      .click(addVendors.searchField)
      .typeText(addVendors.searchField, vendorName, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .expect(addVendors.viewVendorName.innerText).contains(vendorName)
      .click(addVendors.viewVendorName)
      .wait(1000)
      .expect(addVendors.viewVendorName.innerText).contains(vendorName)
      .hover(this.archiveVendor)
      .click(this.archiveVendor)
      .hover(this.confirmArchive)
      .click(this.confirmArchive)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/vendors/all')
      .wait(1000)
      .hover(this.filterBtn)
      .click(this.filterBtn)
      .click(this.filterStatus)
      .click(this.selectFilter)
      .wait(500)
      .click(addVendors.searchField)
      .typeText(addVendors.searchField, vendorName, { replace: true })
      .pressKey('enter')
      .wait(1000)
      await t.wait(1000)
      .expect(addVendors.viewVendorName.innerText).contains(vendorName)
      .click(addVendors.viewVendorName)
      .wait(1000)
      .hover(this.delVendorBtn)
      .click(this.delVendorBtn)
      .wait(1000)
      .hover(this.confirmDelBtn)
      .click(this.confirmDelBtn)
      .wait(1000)
      .typeText(addVendors.searchField, vendorName, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(this.viewDelVendor)
      .expect(this.viewDelVendor.innerText).contains('Not tracking anything here, yet.')
      .wait(1000)
  }
} export default deleteVendor
