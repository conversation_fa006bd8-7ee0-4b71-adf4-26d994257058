import faker from 'faker';

class vendorVar {

  constructor() {
    this.vendorName = faker.name.firstName();
    this.url = faker.internet.url();
    this.description = 'This is only for testing purposes';
    this.accountNumber = faker.finance.account(8);
    this.address = faker.address.streetAddress();
    this.city = 'denver';
    this.state = faker.address.state();
    this.zipCode = faker.address.zipCode('####');
    this.phoneNumber = faker.phone.phoneNumber('684 ### ####');
    this.alertNumber = '10';
    this.overPrecent = '11';
    this.alertBadge = '1';
    this.tags = 'test';
    this.country = 'American Samoa';
    this.countryCode = '+1';
    this.billingMethods = 'COD';
    this.billingTerms = 'Cash on Delivery';
    this.bussinessUnit = 'tender';
    this.category = 'Infrastructure';
    this.editVendorName = faker.name.lastName();
    this.editUrl = faker.internet.url();
    this.editDescription = 'This is only for testing purposes and edit the vendor';
    this.editAccountNumber = faker.finance.account(8);
    this.editAddress = faker.address.streetAddress();
    this.editCity = 'Houston';
    this.editState = faker.address.state();
    this.editZipCode = faker.address.zipCode('####');
    this.editPhoneNumber = faker.phone.phoneNumber('502######');
    this.editAlertNumber = '89';
    this.editTags = 'QA';
    this.editCountry = 'United States';
    this.editBillingMethods = 'CBS';
    this.editBillingTerms = 'Cash before shipment';
    this.editCategory = 'HR';
    this.editBussinessUnit = 'vendor';
    this.productName = 'Product' + faker.commerce.product();
    this.editProductName = faker.commerce.product();
    this.productType = 'IT';
    this.editProductType = 'Administration';
    this.licenseUsed = '6';
    this.editLicenseUsed = '9';
    this.totalLicense = '8';
    this.editTotalLicense = '10';
    this.arrivalDate = '2022-12-19';
    this.editArrivalDate = '2022-12-22';
    this.transactionName = 'Txn' + faker.name.lastName();
    this.transactionName1 = 'cashout' + faker.name.lastName();
    this.transactionDate = '2022-11-18';
    this.amount = '60';
    this.amount1 = '67';
    this.previousDayTxn = '3';
    this.editTxnName = 'edit' + faker.name.lastName();
    this.editTxnDate = '2022-12-4';
    this.editAmount = faker.finance.amount(6, 100, 0);
    this.allTime = 'All Time';
    this.calendarYear = 'Calendar Year';
    this.spendYtd = 'Spend YTD';
    this.spendQtd = 'Spend QTD';
    this.totalCost = 'Total Cost';
    this.montlySpend = 'Monthly Spend';
    this.graphTitle = faker.lorem.words();
    this.barChart = 'Barchart';
    this.donutChart = 'Donutchart';
    this.pieChart = 'Piechart';
    this.dollar = '$';
  }
} export default vendorVar
