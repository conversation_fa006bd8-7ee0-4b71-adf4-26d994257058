import { Selector, t } from 'testcafe';
import navbar from '../Components/navbar';
import golvar from '../Globalvariable';
import addVendor from './add_vendor';

const golVari = new golvar();
const naviBar = new navbar();
const addVendors = new addVendor();

class addTransaction {

  constructor() {
    this.transactionsTab = Selector('[data-tc-transactions-tab]');
    this.addTransactionBtn = Selector('[data-tc-add-transaction]');
    this.transactionName = Selector('[data-tc-transaction-name-field]');
    this.amount = Selector('[data-tc-currency]');
    this.transactionDate = Selector('[data-tc-transaction-date]');
    this.backMonthBtn = this.transactionDate.find('.mx-icon-last-month');
    this.selectsDay = this.transactionDate.find('td.cell.cur-month');
    this.currentMonthBtn = this.transactionDate.find('td.cell.cur-month.today.actived');
    this.selectOkBtn = this.transactionDate.find('button');
    this.productDropDown = Selector('[data-tc-product-selection]');
    this.saveBtn = Selector('[data-tc-save-form-btn="Save transaction"]');
    this.searchField = Selector('[data-tc-search-transaction]');
    this.viewTransactionName = Selector('[data-tc-transaction-name]');
    this.locDropDown = Selector('[data-tc-transaction="location"]');
    this.selectLocation = this.locDropDown.find('input')
  }

  async addTransactions(vendorName, transactionName, date, currency, productName, location) {
    await t.hover(naviBar.vendorbtn)
      .click(naviBar.vendorbtn)
      .expect(golVari.currentpageurl()).contains('/vendors/dashboard_transactions')
      .wait(500)
      .click(addVendors.vendorTab)
      .expect(golVari.currentpageurl()).contains('/vendors/all')
      .wait(1000)
      .click(addVendors.searchField)
      .typeText(addVendors.searchField, vendorName, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .expect(addVendors.viewVendorName.innerText).contains(vendorName)
      .click(addVendors.viewVendorName)
      .wait(1000)
      .expect(addVendors.viewVendorName.innerText).contains(vendorName)
    await t.wait(1000)
      .hover(this.transactionsTab)
      .click(this.transactionsTab)
      .wait(500)
      .hover(this.addTransactionBtn)
      .click(this.addTransactionBtn)
      .wait(1000)
      .hover(this.transactionName)
      .typeText(this.transactionName, transactionName, { replace: true })
      .wait(1000)
      .typeText(this.amount, currency, { paste: true })
      .wait(1000)
      .hover(this.transactionDate)
      .typeText(this.transactionDate, date, { replace: true })
      .wait(1000)
      .click(this.amount)
      .hover(this.productDropDown)
      .click(this.productDropDown)
      .wait(1000)
      .typeText(this.productDropDown, productName, { paste: true })
      .pressKey('enter')
      .wait(500)
      .click(this.locDropDown)
      .typeText(this.selectLocation, location, { paste: true })
      .pressKey('enter')
      .wait(500)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(1000)
      .hover(this.searchField)
      .typeText(this.searchField, transactionName, { paste: true })
      .scroll(this.searchField, 'center')
      .wait(2000)
      .hover(this.viewTransactionName)
      .expect(this.viewTransactionName.innerText).contains(transactionName)
      .wait(2000)
  }

  async addTransactionswithlastmonth(vendorName, transactionName, selectDay, currency, productName, location) {
    await t.hover(naviBar.vendorbtn)
      .click(naviBar.vendorbtn)
      .expect(golVari.currentpageurl()).contains('/vendors/dashboard_transactions')
      .wait(500)
      .click(addVendors.vendorTab)
      .expect(golVari.currentpageurl()).contains('/vendors/all')
      .wait(1000)
      .click(addVendors.searchField)
      .typeText(addVendors.searchField, vendorName, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .expect(addVendors.viewVendorName.innerText).contains(vendorName)
      .click(addVendors.viewVendorName)
      .wait(1000)
      .expect(addVendors.viewVendorName.innerText).contains(vendorName)
    await t.wait(1000)
      .hover(this.transactionsTab)
      .click(this.transactionsTab)
      .wait(500)
      .hover(this.addTransactionBtn)
      .click(this.addTransactionBtn)
      .wait(1000)
      .hover(this.transactionName)
      .typeText(this.transactionName, transactionName, { replace: true })
      .wait(1000)
      .typeText(this.amount, currency, { paste: true })
      .wait(1000)
      .hover(this.transactionDate)
      .wait(500)
      .click(this.transactionDate)
      .wait(500)
      .hover(this.backMonthBtn)
      .wait(500)
      .click(this.backMonthBtn)
      .wait(500)
      .click(this.selectsDay.withText(selectDay))
      .wait(500)
      .click(this.selectOkBtn)
      .wait(1000)
      .hover(this.productDropDown)
      .click(this.productDropDown)
      .wait(1000)
      .typeText(this.productDropDown, productName, { paste: true })
      .pressKey('enter')
      .wait(500)
      .click(this.locDropDown)
      .typeText(this.selectLocation, location, { paste: true })
      .pressKey('enter')
      .wait(500)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(1000)
      .hover(this.searchField)
      .typeText(this.searchField, transactionName, { paste: true })
      .scroll(this.searchField, 'center')
      .wait(2000)
      .hover(this.viewTransactionName)
      .expect(this.viewTransactionName.innerText).contains(transactionName)
      .wait(2000)
  }

  async addTxnCurrentMonth(vendorName1, transactionName1, currency1, productName1, location1) {
    await t.hover(naviBar.vendorbtn)
      .click(naviBar.vendorbtn)
      .expect(golVari.currentpageurl()).contains('/vendors/dashboard_transactions')
      .wait(500)
      .click(addVendors.vendorTab)
      .expect(golVari.currentpageurl()).contains('/vendors/all')
      .click(addVendors.searchField)
      .typeText(addVendors.searchField, vendorName1, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .expect(addVendors.viewVendorName.innerText).contains(vendorName1)
      .click(addVendors.viewVendorName)
      .wait(1000)
      .expect(addVendors.viewVendorName.innerText).contains(vendorName1)
      .wait(3000)
      .hover(this.transactionsTab)
      .click(this.transactionsTab)
      .wait(500)
      .hover(this.addTransactionBtn)
      .click(this.addTransactionBtn)
      .wait(1000)
      .hover(this.transactionName)
      .typeText(this.transactionName, transactionName1, { replace: true })
      .wait(1000)
      .typeText(this.amount, currency1, { paste: true })
      .wait(1000)
      .hover(this.transactionDate)
      .wait(500)
      .click(this.transactionDate)
      .wait(1000)
      .click(this.currentMonthBtn)
      .wait(500)
      .click(this.selectOkBtn)
      .wait(1000)
      .hover(this.productDropDown)
      .click(this.productDropDown)
      .wait(1000)
      .typeText(this.productDropDown, productName1, { paste: true })
      .pressKey('enter')
      .wait(500)
      .click(this.locDropDown)
      .typeText(this.selectLocation, location1, { paste: true })
      .pressKey('enter')
      .wait(500)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(1000)
      .hover(this.searchField)
      .typeText(this.searchField, transactionName1, { paste: true })
      .scroll(this.searchField, 'center')
      .wait(2000)
      .hover(this.viewTransactionName)
      .expect(this.viewTransactionName.innerText).contains(transactionName1)
      .wait(2000)
  }
} export default addTransaction
