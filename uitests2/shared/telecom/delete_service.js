import { Selector, t } from "testcafe";
import navbar from '../Components/navbar';
import golvar from "../Globalvariable";
import addService from "./add_service";
import addProvider from "./add_provider";

const naviBar = new navbar();
const golVari = new golvar();
const addSvcs = new addService();
const addProv = new addProvider();

class deleteService {

  constructor() {
    this.deleteBtn = Selector('[data-tc-delete-service]');
    this.confirmBtn = Selector('[data-tc-delete-service-associated]');
  }

  async deleteServices(providerName, serviceName) {
    const providerName1 = Selector(`[data-tc-searched-name="${providerName}"]`);
    await t.hover(naviBar.telecombtn)
      .click(naviBar.telecombtn)
      .expect(golVari.currentpageurl()).contains('/telecom_providers/all')
      .wait(1000)
      .expect(providerName1.innerText).contains(providerName)
      .wait(500)
      .click(providerName1)
      .wait(1000)
      .expect(addProv.viewProvider.innerText).contains(providerName)
      .hover(addProv.serviceTab)
      .click(addProv.serviceTab)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/services')
      .wait(1000)
      .hover(`[data-tc-service="${serviceName}"]`)
      .click(`[data-tc-service="${serviceName}"]`)
      .wait(1000)
      .hover(addSvcs.viewServiceName)
      .expect(addSvcs.viewServiceName.innerText).contains(serviceName)
      .hover(this.deleteBtn)
      .click(this.deleteBtn)
      .wait(1000)
      .hover(this.confirmBtn)
      .click(this.confirmBtn)
  }
} export default deleteService
