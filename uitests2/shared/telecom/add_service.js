import { Selector, t } from "testcafe";
import navbar from '../Components/navbar';
import golvar from "../Globalvariable";
import addProvider from "./add_provider";
import addServiceForm from "./add_service_form";
import telecomVar from "./telecom_variables";

const naviBar = new navbar();
const golVari = new golvar();
const addProv = new addProvider();
const addSvcForm = new addServiceForm();
const telecomVari = new telecomVar();
const newCxn = telecomVari.cxnValue;
const newSubnet = telecomVari.subnetValue;
const newNotes = telecomVari.notes;
const currency = telecomVari.amount;
const newGateway = telecomVari.gatewayValue;
const newDNS = telecomVari.dnsValue;
const newAccNumber = telecomVari.accountNum;

class addService {

  constructor() {
    this.searchField = Selector('[data-tc-telecom-search]');
    this.searchedProvider = Selector('[data-tc-view-provider-name]');
    this.addServiceBtn = Selector('[data-tc-add-service-btn]');
    this.selectedProvider = Selector('[data-tc-current-provider]');
    this.createBtn = Selector('[data-tc-save-form-btn="Create Service"]');
    this.viewServiceName = Selector('[data-tc-service-name]');
    this.viewLocation = Selector('[data-tc-view-location]');
    this.viewNote = Selector('[data-tc-view-note]');
    this.viewType = Selector('[data-tc-view-type]');
    this.viewAccNumber = Selector('[data-tc-view-account-number]');
    this.viewPin = Selector('[data-tc-view-pin]');
    this.viewConnection = Selector('[data-tc-view-connection]');
    this.viewEquipment = Selector('[data-tc-view-equipment]');
    this.viewCirId = Selector('[data-tc-view-circuitid]');
    this.viewSpeed = Selector('[data-tc-view-speed]');
    this.viewLine = Selector('[data-tc-view-lines]');
    this.viewSubnet = Selector('[data-tc-view-subnet]');
    this.viewGateway = Selector('[data-tc-view-gateway]');
    this.viewDNS1 = Selector('[data-tc-view-dns1]');
    this.viewDNS2 = Selector('[data-tc-view-dns2]');
  }

  async addServices(providerName, name, location) {
    const selectProvider = Selector(`[data-tc-searched-name="${providerName}"]`);
    await t.hover(naviBar.telecombtn)
      .click(naviBar.telecombtn)
      .expect(golVari.currentpageurl()).contains('/telecom_providers/all')
      .hover(this.searchField)
      .typeText(this.searchField, providerName, { paste: true })
      .wait(1000)
      .pressKey('enter')
      .wait(2000)
      .hover(selectProvider)
      .expect(selectProvider.innerText).contains(providerName)
      .click(selectProvider)
      .wait(1000)
      .expect(addProv.viewProvider.innerText).contains(providerName)
      .hover(addProv.serviceTab)
      .click(addProv.serviceTab)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/services')
      .wait(1000)
      .hover(this.addServiceBtn)
      .click(this.addServiceBtn)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('/services/new')
      .hover(this.selectedProvider)
      .expect(this.selectedProvider.innerText).contains(providerName)
      .wait(1000)
    addSvcForm.addServicesForm(name, telecomVari.type, currency, newAccNumber, telecomVari.pin, location, newNotes, newCxn, telecomVari.eqptValue, telecomVari.cirIDValue, telecomVari.speedValue, telecomVari.linesValue, newSubnet, newGateway, newDNS, newDNS)
    await t.wait(1000)
      .hover(this.createBtn)
      .click(this.createBtn)
      .wait(1000)
      .expect(addProv.viewProvider.innerText).contains(providerName)
      .hover(this.viewServiceName)
      .expect(this.viewServiceName.innerText).contains(name)
      .hover(this.viewType)
      .expect(this.viewType.innerText).contains(telecomVari.type)
      .hover(this.viewLocation)
      .expect(this.viewLocation.innerText).contains(location)
      .wait(1000)
      .hover(this.viewNote)
      .expect(this.viewNote.innerText).contains(newNotes)
      .hover(this.viewAccNumber)
      .expect(this.viewAccNumber.innerText).contains(newAccNumber)
      .hover(this.viewPin)
      .expect(this.viewPin.innerText).contains(telecomVari.pin)
      .wait(1000)
      .hover(this.viewConnection)
      .expect(this.viewConnection.innerText).contains(newCxn)
      .wait(1000)
      .hover(this.viewEquipment)
      .expect(this.viewEquipment.innerText).contains(telecomVari.eqptValue)
      .hover(this.viewCirId)
      .expect(this.viewCirId.innerText).contains(telecomVari.cirIDValue)
      .hover(this.viewSpeed)
      .expect(this.viewSpeed.innerText).contains(telecomVari.speedValue)
      .hover(this.viewLine)
      .expect(this.viewLine.innerText).contains(telecomVari.linesValue)
      .hover(this.viewSubnet)
      .expect(this.viewSubnet.innerText).contains(newSubnet)
      .hover(this.viewGateway)
      .expect(this.viewGateway.innerText).contains(newGateway)
      .hover(this.viewDNS1)
      .expect(this.viewDNS1.innerText).contains(newDNS)
      .hover(this.viewDNS2)
      .expect(this.viewDNS2.innerText).contains(newDNS)
      .wait(1000)
  }
} export default addService
