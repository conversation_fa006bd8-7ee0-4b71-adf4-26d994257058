import faker from 'faker';

class telecomVar {

  constructor() {
    this.phoneNumber = faker.phone.phoneNumber('501######');
    this.friendlyName = faker.name.firstName();
    this.editPhoneNumber = faker.phone.phoneNumber('255######');
    this.editFriendlyName = faker.name.lastName();
    this.ipAddress = faker.internet.ip();
    this.editIpAddress = faker.internet.ip();
    this.provName = 'IT' + faker.name.firstName();
    this.servName = faker.internet.userName();
    this.newSvcName = 'Test' + faker.internet.userName();
    this.amount = faker.finance.amount();
    this.accountNum = faker.finance.account(5);
    this.type = 'Sip Trunks';
    this.pin = '2423';
    this.notes = faker.lorem.sentences(2);
    this.editNotes = faker.lorem.sentences(1);
    this.cxnValue = faker.internet.domainWord();
    this.eqptValue = 'router';
    this.subnetValue = faker.internet.ip();
    this.gatewayValue = faker.internet.ip();
    this.linesValue = '6';
    this.dnsValue = faker.internet.domainName();
    this.speedValue = '3333 MBPS';
    this.cirIDValue = '324P3434';
    this.editName = 'Edit' + faker.internet.userName();
    this.editAmount = faker.finance.amount();
    this.editAccNum = faker.finance.account(6);
    this.editType = 'Hosted PBX';
    this.editPin = '4444';
    this.editCxnValue = faker.internet.domainWord();
    this.editEqptValue = 'switch';
    this.editSubnetValue = faker.internet.ip();
    this.editGatewayValue = faker.internet.ip();
    this.editLinesValue = '10';
    this.editDnsValue = faker.internet.domainName(4);
    this.editSpeedValue = '4545 MBPS';
    this.editCirIDValue = '324P454545';
    this.voiceCategory = 'voice';
    this.dataCategory = 'data';
    this.consolidatedCategory = 'consolidated';
  }
} export default telecomVar
