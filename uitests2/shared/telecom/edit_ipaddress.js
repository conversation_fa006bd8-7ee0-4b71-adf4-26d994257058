import { Selector, t } from "testcafe";
import navbar from '../Components/navbar';
import golvar from "../Globalvariable";
import addPhoneNumber from "./add_phonenumber";
import addIpAddresses from "./add_ipaddress";
import editPhoneNumbers from "./edit_phonenumber";

const naviBar = new navbar();
const golVari = new golvar();
const addNewPhonenumber = new addPhoneNumber();
const addNewIpAddress = new addIpAddresses();
const editPnumber = new editPhoneNumbers();

class editIpAddress {

  constructor() {
    this.editIcon = Selector('[data-tc-edit-ipaddress]');
  }

  async editIpAddresses(ipAddress, editIpAddress, editFriendlyName, editProvider, notes) {
    await t.hover(naviBar.telecombtn)
      .click(naviBar.telecombtn)
      .expect(golVari.currentpageurl()).contains('/telecom_providers/all')
      .hover(addNewIpAddress.ipAddressTab)
      .click(addNewIpAddress.ipAddressTab)
      .expect(golVari.currentpageurl()).contains('/telecom_providers/ip_addresses')
      .hover(addNewIpAddress.searchIpField)
      .click(addNewIpAddress.searchIpField)
      .typeText(addNewIpAddress.searchIpField, ipAddress, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .hover(addNewIpAddress.viewIpAddress)
      .expect(addNewIpAddress.viewIpAddress.innerText).contains(ipAddress)
      .wait(500)
      .hover(editPnumber.dottedIcon)
      .hover(this.editIcon)
      .click(this.editIcon)
      .wait(1000)
      .typeText(addNewIpAddress.ipAddressField, editIpAddress, { replace: true })
      .click(addNewPhonenumber.providerField)
      .hover(`[data-tc-vendor-drop-down-listing="${editProvider}"]`)
      .click(`[data-tc-vendor-drop-down-listing="${editProvider}"]`)
      .wait(1000)
      .typeText(addNewPhonenumber.friendNameField, editFriendlyName, { replace: true })
      .typeText(addNewPhonenumber.notesField, notes, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .click(addNewIpAddress.saveIpBtn)
      .wait(1000)
      .hover(addNewIpAddress.searchIpField)
      .click(addNewIpAddress.searchIpField)
      .typeText(addNewIpAddress.searchIpField, editIpAddress, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(addNewIpAddress.viewIpAddress)
      .expect(addNewIpAddress.viewIpAddress.innerText).contains(editIpAddress)
      .hover(addNewPhonenumber.viewFriendlyName)
      .expect(addNewPhonenumber.viewFriendlyName.innerText).contains(editFriendlyName)
      // .hover(addNewPhonenumber.viewProviderName)
      // .expect(addNewPhonenumber.viewProviderName.innerText).contains(editProvider)
      .wait(1000)
  }
} export default editIpAddress
