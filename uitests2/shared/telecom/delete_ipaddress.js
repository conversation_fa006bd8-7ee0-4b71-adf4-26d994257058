import { Selector, t } from "testcafe";
import navbar from '../Components/navbar'
import golvar from "../Globalvariable";
import addIpAddresses from "./add_ipaddress";
import editPhoneNumber from "./edit_phonenumber.js";

const naviBar = new navbar();
const golVari = new golvar();
const addNewIpAddress = new addIpAddresses();
const editPnumber = new editPhoneNumber();

class deleteIpAddress {

  constructor() {
    this.deleteBtn = Selector('[data-tc-delete-ipaddress]');
    this.confirmDeleteBtn = Selector('[data-tc-delete-ipaddr]');
    this.viewDeletedIpaddr = Selector('[data-tc-no-result-ipaddress]');
  }

  async deleteIpAddresses(ipAddress) {
    await t.hover(naviBar.telecombtn)
      .click(naviBar.telecombtn)
      .expect(golVari.currentpageurl()).contains('/telecom_providers/all')
      .hover(addNewIpAddress.ipAddressTab)
      .click(addNewIpAddress.ipAddressTab)
      .expect(golVari.currentpageurl()).contains('/telecom_providers/ip_addresses')
      .wait(1000)
      .typeText(addNewIpAddress.searchIpField, ipAddress, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .hover(editPnumber.dottedIcon)
      .hover(this.deleteBtn)
      .click(this.deleteBtn)
      .hover(this.confirmDeleteBtn)
      .click(this.confirmDeleteBtn)
      .wait(1000)
      .typeText(addNewIpAddress.searchIpField, ipAddress, { replace: true })
      .wait(500)
      .hover(this.viewDeletedIpaddr)
      .expect(this.viewDeletedIpaddr.innerText).contains('No results found, try clearing the search box')
      .wait(1000)
  }
} export default deleteIpAddress
