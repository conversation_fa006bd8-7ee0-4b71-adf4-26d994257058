import { Selector, t } from "testcafe";
import navbar from '../Components/navbar';
import golvar from "../Globalvariable";
import assetForm from "../assets/assets_form_ui_test_case";

const naviBar = new navbar();
const golVari = new golvar();
const astFormPage = new assetForm();

class addPhoneNumber {

  constructor() {
    this.phoneTab = Selector('[data-tc-phone-numbers-tab]');
    this.addPhoneBtn = Selector('[data-tc-add-phone-number]');
    this.phoneNumberField = Selector('input[placeholder="Phone number"]');
    this.providerField = Selector('[data-tc-multi-select-vendor]');
    this.inputProviderName = Selector('[id="tc-vendor-field"]');
    this.locationField = Selector('[data-tc-location]');
    this.friendNameField = Selector('[data-tc-friendly-name]');
    this.notesField = Selector('[data-tc-notes-field]');
    this.saveBtn = Selector('[data-tc-save-form-btn="Save Phone Number"]');
    this.searchField = Selector('[data-tc-search-phone-number-field]');
    this.viewPhoneNumber = Selector('[data-tc-listed-phonenumber]');
    this.viewFriendlyName = Selector('[data-tc-listed-friendlyname]');
    this.viewProviderName = Selector('[data-tc-listed-provider]');
    this.viewLocationName = Selector('[data-tc-listed-location]');
  }

  async addPhoneNumbers(phoneNumber, providerName, locationName, friendlyName, notes) {
    await t.hover(naviBar.telecombtn)
      .click(naviBar.telecombtn)
      .expect(golVari.currentpageurl()).contains('/telecom_providers/all')
      .hover(this.phoneTab)
      .click(this.phoneTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/telecom_providers/phone_numbers')
      .hover(this.addPhoneBtn)
      .click(this.addPhoneBtn)
      .wait(1000)
      .hover(this.phoneNumberField)
      .click(this.phoneNumberField)
      .typeText(this.phoneNumberField, phoneNumber, { paste: true })
      .click(this.providerField)
      .typeText(this.inputProviderName, providerName, { paste: true })
      .pressKey('enter')
      .wait(500)
      .click(astFormPage.assetLocation)
      .typeText(astFormPage.assetLocation, locationName, { paste: true })
      .wait(500)
      .pressKey('enter')
      .wait(500)
      .click(this.friendNameField)
      .typeText(this.friendNameField, friendlyName, { paste: true })
      .click(this.notesField)
      .typeText(this.notesField, notes, { paste: true })
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(500)
      .hover(this.searchField)
      .click(this.searchField)
      .typeText(this.searchField, phoneNumber, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .hover(this.viewPhoneNumber)
      .expect(this.viewPhoneNumber.innerText).contains(phoneNumber)
      .hover(this.viewFriendlyName)
      .expect(this.viewFriendlyName.innerText).contains(friendlyName)
      .hover(this.viewProviderName)
      .expect(this.viewProviderName.innerText).contains(providerName)
      .hover(this.viewLocationName)
      .expect(this.viewLocationName.innerText).contains(locationName)
      .wait(1000)
  }
} export default addPhoneNumber
