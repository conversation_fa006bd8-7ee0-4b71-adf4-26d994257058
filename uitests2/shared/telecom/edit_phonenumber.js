import { Selector, t } from "testcafe";
import navbar from '../Components/navbar';
import golvar from "../Globalvariable";
import addPhoneNumber from "./add_phonenumber";

const naviBar = new navbar();
const golVari = new golvar();
const addNewPhonenumber = new addPhoneNumber();

class editPhoneNumber {

  constructor() {
    this.dottedIcon = Selector('[data-tc-dotted-icon]');
    this.editIcon = Selector('[data-tc-edit-number]');
  }

  async editPhoneNumbers(phoneNumber, editPnumber, editFriendlyName, editProvider, notes) {
    await t.hover(naviBar.telecombtn)
      .click(naviBar.telecombtn)
      .expect(golVari.currentpageurl()).contains('/telecom_providers/all')
      .hover(addNewPhonenumber.phoneTab)
      .click(addNewPhonenumber.phoneTab)
      .expect(golVari.currentpageurl()).contains('/telecom_providers/phone_numbers')
      .hover(addNewPhonenumber.searchField)
      .click(addNewPhonenumber.searchField)
      .typeText(addNewPhonenumber.searchField, phoneNumber, { paste: true })
      .pressKey('enter')
      .wait(500)
      .hover(addNewPhonenumber.viewPhoneNumber)
      .expect(addNewPhonenumber.viewPhoneNumber.innerText).contains(phoneNumber)
      .hover(this.dottedIcon)
      .click(this.editIcon)
      .wait(500)
      .typeText(addNewPhonenumber.phoneNumberField, editPnumber, { replace: true })
      .click(addNewPhonenumber.providerField)
      .hover(`[data-tc-vendor-drop-down-listing="${editProvider}"]`)
      .click(`[data-tc-vendor-drop-down-listing="${editProvider}"]`)
      .wait(1000)
      .typeText(addNewPhonenumber.friendNameField, editFriendlyName, { replace: true })
      .typeText(addNewPhonenumber.notesField, notes, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .click(addNewPhonenumber.saveBtn)
      .wait(1000)
      .hover(addNewPhonenumber.searchField)
      .click(addNewPhonenumber.searchField)
      .wait(1000)
      .typeText(addNewPhonenumber.searchField, editPnumber, { replace: true })
      .pressKey('enter')
      .wait(1000)
      .hover(addNewPhonenumber.viewPhoneNumber)
      .expect(addNewPhonenumber.viewPhoneNumber.innerText).contains(editPnumber)
      .hover(addNewPhonenumber.viewFriendlyName)
      .expect(addNewPhonenumber.viewFriendlyName.innerText).contains(editFriendlyName)
      // .hover(addNewPhonenumber.viewProviderName)
      // .expect(addNewPhonenumber.viewProviderName.innerText).contains(editProvider)
      .wait(1000)
  }
} export default editPhoneNumber
