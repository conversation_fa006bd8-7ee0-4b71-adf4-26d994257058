import { Selector, t } from "testcafe";
import navbar from '../Components/navbar'
import golvar from "../Globalvariable";
import addPhoneNumber from "./add_phonenumber";
import editPhoneNumber from "./edit_phonenumber.js";

const naviBar = new navbar();
const golVari = new golvar();
const addNewPhonenumber = new addPhoneNumber();
const editPnumber = new editPhoneNumber();

class deletePhoneNumber {

  constructor() {
    this.deleteBtn = Selector('[data-tc-delete-number]');
    this.confirmDeleteBtn = Selector('[data-tc-delete-num]');
    this.viewDeletedNumber = Selector('[data-tc-no-result-phonenumber]');
  }

  async deletePhoneNumbers(phoneNumber) {
    await t.hover(naviBar.telecombtn)
      .click(naviBar.telecombtn)
      .expect(golVari.currentpageurl()).contains('/telecom_providers/all')
      .hover(addNewPhonenumber.phoneTab)
      .click(addNewPhonenumber.phoneTab)
      .wait(1000)
      .typeText(addNewPhonenumber.searchField, phoneNumber, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .hover(editPnumber.dottedIcon)
      .hover(this.deleteBtn)
      .click(this.deleteBtn)
      .wait(500)
      .hover(this.confirmDeleteBtn)
      .click(this.confirmDeleteBtn)
      .wait(1000)
      .typeText(addNewPhonenumber.searchField, phoneNumber, { replace: true })
      .wait(1000)
      .hover(this.viewDeletedNumber)
      .expect(this.viewDeletedNumber.innerText).contains('No results found, try clearing the search box')
      .wait(1000)
  }
} export default deletePhoneNumber
