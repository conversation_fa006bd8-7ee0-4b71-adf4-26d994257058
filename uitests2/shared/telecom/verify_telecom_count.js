import navbar from "../Components/navbar";
import { Selector, t } from "testcafe";
import golvar from "../Globalvariable";

const naviBar = new navbar();
const golVari = new golvar();
const totalIpAddr = Selector('[data-tc-view-count="ip addresses"]');
const totalPhoneNum = Selector('[data-tc-view-count="phone numbers"]');
const providerTab = Selector('[data-tc-providers]');

class checkCountTelecom {

  async checkCountIp(value) {
    await t.wait(1000)
      .click(naviBar.telecombtn)
      .wait(1000)
      .click(providerTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/telecom_providers/all')
      .wait(500)
      .hover(totalIpAddr)
      .wait(500)
      .expect(totalIpAddr.innerText).contains(value)
      .wait(1000)
  }

  async checkCountPhone(value) {
    await t.wait(1000)
      .click(naviBar.telecombtn)
      .wait(1000)
      .click(providerTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/telecom_providers/all')
      .wait(500)
      .hover(totalPhoneNum)
      .wait(500)
      .expect(totalPhoneNum.innerText).contains(value)
      .wait(1000)
  }

  async counterCategoryWise(category, value) {
    const telecomCategory = Selector(`[data-tc-category="${category}"]`);
    const viewCount = Selector(`[data-tc-${category}-count]`);
    await t.wait(1000)
      .click(naviBar.telecombtn)
      .wait(1000)
      .click(providerTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/telecom_providers/all')
      .wait(500)
      .hover(telecomCategory)
      .wait(500)
      .expect(viewCount.innerText).contains(value)
      .wait(1000)
  }
} export default checkCountTelecom
