import { Selector, t } from "testcafe";
import navbar from '../Components/navbar';
import golvar from "../Globalvariable";
import addService from "./add_service";
import addProvider from "./add_provider";
import addServiceForm from "./add_service_form";
import telecomVar from "./telecom_variables";

const naviBar = new navbar();
const golVari = new golvar();
const addSvcs = new addService();
const addProv = new addProvider();
const editSvcForm = new addServiceForm();
const telecomVari = new telecomVar();

class editService {

  constructor() {
    this.editBtn = Selector('[data-tc-edit-service]');
    this.saveBtn = Selector('[data-tc-save-form-btn="Save Service"]');
  }

  async editServices(providerName, serviceName, editServiceName, editLoc) {
    const selectProvider = Selector(`[data-tc-searched-name="${providerName}"]`);
    await t.wait(1000)
      .hover(naviBar.telecombtn)
      .click(naviBar.telecombtn)
      .expect(golVari.currentpageurl()).contains('/telecom_providers/all')
      .wait(1000)
      .hover(selectProvider)
      .expect(selectProvider.innerText).contains(providerName)
      .click(selectProvider)
      .wait(1000)
      .expect(addProv.viewProvider.innerText).contains(providerName)
      .hover(addProv.serviceTab)
      .click(addProv.serviceTab)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/services')
      .wait(1000)
      .hover(`[data-tc-service="${serviceName}"]`)
      .click(`[data-tc-service="${serviceName}"]`)
      .wait(1000)
      .hover(addSvcs.viewServiceName)
      .expect(addSvcs.viewServiceName.innerText).contains(serviceName)
      .hover(this.editBtn)
      .click(this.editBtn)
      .expect(golVari.currentpageurl()).contains('/edit')
    editSvcForm.addServicesForm(editServiceName, telecomVari.editType, telecomVari.editAmount, telecomVari.editAccNum, telecomVari.editPin, editLoc, telecomVari.editNotes, telecomVari.editCxnValue, telecomVari.editEqptValue, telecomVari.editCirIDValue, telecomVari.editSpeedValue, telecomVari.editLinesValue, telecomVari.editSubnetValue, telecomVari.editGatewayValue, telecomVari.editDnsValue, telecomVari.editDnsValue)
    await t.wait(1000)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(1000)
      .expect(addProv.viewProvider.innerText).contains(providerName)
      .hover(addSvcs.viewServiceName)
      .expect(addSvcs.viewServiceName.innerText).contains(editServiceName)
      .hover(addSvcs.viewType)
      .expect(addSvcs.viewType.innerText).contains(telecomVari.editType)
      .hover(addSvcs.viewLocation)
      .expect(addSvcs.viewLocation.innerText).contains(editLoc)
      .wait(1000)
      .hover(addSvcs.viewNote)
      .expect(addSvcs.viewNote.innerText).contains(telecomVari.editNotes)
      .hover(addSvcs.viewAccNumber)
      .expect(addSvcs.viewAccNumber.innerText).contains(telecomVari.editAccNum)
      .hover(addSvcs.viewPin)
      .expect(addSvcs.viewPin.innerText).contains(telecomVari.editPin)
      .wait(1000)
      .hover(addSvcs.viewConnection)
      .expect(addSvcs.viewConnection.innerText).contains(telecomVari.editCxnValue)
      .wait(1000)
      .hover(addSvcs.viewEquipment)
      .expect(addSvcs.viewEquipment.innerText).contains(telecomVari.editEqptValue)
      .hover(addSvcs.viewCirId)
      .expect(addSvcs.viewCirId.innerText).contains(telecomVari.editCirIDValue)
      .hover(addSvcs.viewSpeed)
      .expect(addSvcs.viewSpeed.innerText).contains(telecomVari.editSpeedValue)
      .hover(addSvcs.viewLine)
      .expect(addSvcs.viewLine.innerText).contains(telecomVari.editLinesValue)
      .hover(addSvcs.viewSubnet)
      .expect(addSvcs.viewSubnet.innerText).contains(telecomVari.editSubnetValue)
      .hover(addSvcs.viewGateway)
      .expect(addSvcs.viewGateway.innerText).contains(telecomVari.editGatewayValue)
      .hover(addSvcs.viewDNS1)
      .expect(addSvcs.viewDNS1.innerText).contains(telecomVari.editDnsValue)
      .hover(addSvcs.viewDNS2)
      .expect(addSvcs.viewDNS2.innerText).contains(telecomVari.editDnsValue)
      .wait(1000)
  }
} export default editService
