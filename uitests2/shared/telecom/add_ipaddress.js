import { Selector, t } from "testcafe";
import navbar from '../Components/navbar';
import golvar from "../Globalvariable";
import addPhoneNumber from "./add_phonenumber";
import assetForm from "../assets/assets_form_ui_test_case";

const naviBar = new navbar();
const golVari = new golvar();
const addNewPhoneNumber = new addPhoneNumber();
const astFormPage = new assetForm();

class addIpAddress {

  constructor() {
    this.ipAddressTab = Selector('[data-tc-ip-addresses-tab]');
    this.addIpBtn = Selector('[data-tc-add-ipaddress-btn]');
    this.ipAddressField = Selector('[data-vv-as="IP address"]');
    this.saveIpBtn = Selector('[data-tc-btn="parent"] button');
    this.searchIpField = Selector('[data-tc-search-ip-field]');
    this.viewIpAddress = Selector('[data-tc-listed-ipaddress]');
  }

  async addIpAddresses(ipAddress, providerName, locationName, friendlyName, notes) {
    await t.hover(naviBar.telecombtn)
      .click(naviBar.telecombtn)
      .expect(golVari.currentpageurl()).contains('/telecom_providers/all')
      .hover(this.ipAddressTab)
      .click(this.ipAddressTab)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('telecom_providers/ip_addresses')
      .hover(this.addIpBtn)
      .click(this.addIpBtn)
      .wait(1000)
      .hover(this.ipAddressField)
      .typeText(this.ipAddressField, ipAddress, { paste: true })
      .click(addNewPhoneNumber.providerField)
      .typeText(addNewPhoneNumber.inputProviderName, providerName, { paste: true })
      .pressKey('enter')
      .wait(500)
      .click(astFormPage.assetLocation)
      .typeText(astFormPage.assetLocation, locationName, { paste: true })
      .wait(500)
      .pressKey('enter')
      .wait(500)
      .click(addNewPhoneNumber.friendNameField)
      .typeText(addNewPhoneNumber.friendNameField, friendlyName, { paste: true })
      .click(addNewPhoneNumber.notesField)
      .typeText(addNewPhoneNumber.notesField, notes, { paste: true })
      .hover(this.saveIpBtn)
      .click(this.saveIpBtn)
      .wait(1000)
      .hover(this.searchIpField)
      .click(this.searchIpField)
      .typeText(this.searchIpField, ipAddress, { paste: true })
      .pressKey('enter')
      .wait(1000)
      .hover(this.viewIpAddress)
      .expect(this.viewIpAddress.innerText).contains(ipAddress)
      .wait(1000)
      .hover(addNewPhoneNumber.viewFriendlyName)
      .expect(addNewPhoneNumber.viewFriendlyName.innerText).contains(friendlyName)
      .hover(addNewPhoneNumber.viewProviderName)
      .expect(addNewPhoneNumber.viewProviderName.innerText).contains(providerName)
      .hover(addNewPhoneNumber.viewLocationName)
      .expect(addNewPhoneNumber.viewLocationName.innerText).contains(locationName)
      .wait(1000)
  }
} export default addIpAddress
