import { Selector, t } from "testcafe";
import navbar from '../Components/navbar';
import golvar from "../Globalvariable";
import addServiceForm from "./add_service_form";
import telecomVar from "./telecom_variables";

const naviBar = new navbar();
const golVari = new golvar();
const addSvcForm = new addServiceForm();
const telecomVari = new telecomVar();

class addProvider {

  constructor() {
    this.addProviderBtn = Selector('[data-tc-add-provider-btn]');
    this.providerDropDown = Selector('[data-tc-multi-select-vendor]');
    this.providerField = Selector('[data-tc-select-provider]');
    this.serviceModal = Selector('[data-tc-service-title]');
    this.viewProvider = Selector('[data-tc-header-provider]');
    this.serviceTab = Selector('[data-tc-service-side-nav]');
    this.viewServiceName = Selector('[data-tc-service-name]');
    this.saveBtn = Selector('[data-tc-save-form-btn="Save Telecom Provider"]');
  }

  async addProviders(providerName, serviceName, location) {
    await t.hover(naviBar.telecombtn)
      .click(naviBar.telecombtn)
      .expect(golVari.currentpageurl()).contains('/telecom_providers/all')
      .hover(this.addProviderBtn)
      .click(this.addProviderBtn)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('/telecom_providers/new')
      .wait(500)
      .click(this.providerDropDown)
      .typeText(this.providerField, providerName, { paste: true })
      .wait(500)
      .pressKey('enter')
      .wait(1000)
      .expect(this.serviceModal.exists).ok()
      .wait(1000)
    addSvcForm.addServicesForm(serviceName, telecomVari.type, telecomVari.amount, telecomVari.accountNum, telecomVari.pin, location, telecomVari.notes, telecomVari.cxnValue, telecomVari.eqptValue, telecomVari.cirIDValue, telecomVari.speedValue, telecomVari.linesValue, telecomVari.subnetValue, telecomVari.gatewayValue, telecomVari.dnsValue, telecomVari.dnsValue)
    await t.wait(1000)
      .hover(this.saveBtn)
      .click(this.saveBtn)
      .wait(2000)
      .expect(this.viewProvider.innerText).contains(providerName)
      .hover(this.serviceTab)
      .click(this.serviceTab)
      .wait(500)
      .expect(golVari.currentpageurl()).contains('/services')
      .wait(1000)
      .hover(`[data-tc-service="${serviceName}"]`)
      .click(`[data-tc-service="${serviceName}"]`)
      .expect(this.viewServiceName.innerText).contains(serviceName)
      .wait(500)
  }
} export default addProvider
