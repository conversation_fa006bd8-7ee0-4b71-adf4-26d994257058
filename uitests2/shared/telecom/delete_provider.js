import { Selector, t } from "testcafe";
import navbar from '../Components/navbar';
import golvar from "../Globalvariable";
import addService from "./add_service";

const naviBar = new navbar();
const golVari = new golvar();
const addSvcs = new addService();

class deleteProvider {

  constructor() {
    this.archiveIcon = Selector('[data-tc-archive-provider]');
    this.confirmArchive = Selector('[data-tc-provider-confirm-archive]');
    this.notifyPop = Selector('.notification-content');
    this.filterIcon = Selector('[data-tc-filter-menu-button]');
    this.statusDropDown = Selector('[data-tc-filter-status]');
    this.selectFilter = Selector('[data-tc-filter-option="Archived"]');
    this.deleteBtn = Selector('[data-tc-delete-telecom]');
    this.confirmDelete = Selector('[data-tc-confirm-delete]');
    this.searchResult = Selector('[data-tc-no-result-telecom]');
  }

  async deleteProviders(providerName) {
    const selectProvider = Selector(`[data-tc-searched-name="${providerName}"]`);
    await t.hover(naviBar.telecombtn)
      .click(naviBar.telecombtn)
      .expect(golVari.currentpageurl()).contains('/telecom_providers/all')
      .wait(1000)
      .hover(selectProvider)
      .expect(selectProvider.innerText).contains(providerName)
      .click(selectProvider)
      .wait(1000)
      .hover(this.archiveIcon)
      .click(this.archiveIcon)
      .wait(1000)
      .hover(this.confirmArchive)
      .click(this.confirmArchive)
      .wait(1000)
      .hover(this.filterIcon)
      .click(this.filterIcon)
      .wait(1000)
      .hover(this.statusDropDown)
      .click(this.statusDropDown)
      .wait(1000)
      .hover(this.selectFilter)
      .click(this.selectFilter)
      .wait(1000)
      .hover(addSvcs.searchField)
      .typeText(addSvcs.searchField, providerName, { replace: true })
      .pressKey('enter')
      .wait(2000)
      .hover(selectProvider)
      .expect(selectProvider.innerText).contains(providerName)
      .click(selectProvider)
      .wait(2000)
      .hover(this.deleteBtn)
      .click(this.deleteBtn)
      .wait(2000)
      .hover(this.confirmDelete)
      .click(this.confirmDelete)
      .expect(this.notifyPop.innerText).contains('Successfully deleted telecom provider')
      .wait(2000)
      .hover(addSvcs.searchField)
      .typeText(addSvcs.searchField, providerName, { replace: true })
      .pressKey('enter')
      .wait(2000)
      .hover(this.searchResult)
      .expect(this.searchResult.innerText).contains('No results found, try clearing the search box, removing filters')
  }
} export default deleteProvider
