import { Selector, t } from "testcafe";

class addServiceForm {

  constructor() {
    this.serviceField = Selector('[data-tc-telecom-name]');
    this.category = Selector('[data-tc-telecom-voice]');
    this.serviceType = Selector('[data-tc-service-type-select]');
    this.typeOption = this.serviceType.find('option');
    this.costField = Selector('[data-tc-currency]');
    this.accountNumField = Selector('[data-tc-account-number]');
    this.pinField = Selector('[data-tc-PIN]');
    this.locationDropDown = Selector('[data-tc-location-field]');
    this.noteField = Selector('[data-tc-notes]');
    this.advancedOptIcon = Selector('[data-tc-toggle-options]');
    this.connectionField = Selector('[data-tc-telecom="connection"]');
    this.equipmentField = Selector('[data-tc-telecom="equipment"]');
    this.circuitField = Selector('[data-tc-telecom="circuit id"]');
    this.netSpeedField = Selector('[data-tc-telecom="network speed"]');
    this.linesField = Selector('[data-tc-telecom="number of lines"]');
    this.subnetField = Selector('[data-tc-telecom="subnet"]');
    this.gatewayField = Selector('[data-tc-telecom="gateway"]');
    this.dns1Field = Selector('[data-tc-telecom="DNS1"]');
    this.dns2Field = Selector('[data-tc-telecom="DNS2"]');
  }

  async addServicesForm(name, type, amount, accountNumber, pin, locName, notes, connection, equipment, cirId, netSpeed, lines, subnet, gateway, dns1, dns2) {
    await t.hover(this.serviceField)
      .typeText(this.serviceField, name, { replace: true })
      .hover(this.category)
      .click(this.category)
      .wait(1000)
      .hover(this.serviceType)
      .click(this.serviceType)
      .wait(1000)
      .click(this.typeOption.withText(type))
      .wait(500)
      .typeText(this.costField, amount, { replace: true })
      .hover(this.accountNumField)
      .typeText(this.accountNumField, accountNumber, { replace: true })
      .hover(this.pinField)
      .typeText(this.pinField, pin, { replace: true })
      .hover(this.locationDropDown)
      .click(this.locationDropDown)
      .typeText(this.locationDropDown, locName, { paste: true })
      .pressKey('enter')
      .wait(500)
      .typeText(this.accountNumField, accountNumber, { replace: true })
      .typeText(this.noteField, notes, { replace: true })
      .hover(this.advancedOptIcon)
      .click(this.advancedOptIcon)
      .wait(1000)
      .hover(this.connectionField)
      .typeText(this.connectionField, connection, { replace: true })
      .wait(1000)
      .hover(this.equipmentField)
      .typeText(this.equipmentField, equipment, { replace: true })
      .wait(1000)
      .hover(this.circuitField)
      .typeText(this.circuitField, cirId, { replace: true })
      .wait(1000)
      .hover(this.netSpeedField)
      .typeText(this.netSpeedField, netSpeed, { replace: true })
      .wait(1000)
      .hover(this.linesField)
      .typeText(this.linesField, lines, { replace: true })
      .wait(1000)
      .hover(this.subnetField)
      .typeText(this.subnetField, subnet, { replace: true })
      .wait(1000)
      .hover(this.gatewayField)
      .typeText(this.gatewayField, gateway, { replace: true })
      .wait(1000)
      .hover(this.dns1Field)
      .typeText(this.dns1Field, dns1, { replace: true })
      .wait(1000)
      .hover(this.dns2Field)
      .typeText(this.dns2Field, dns2, { replace: true })
      .wait(1000)
  }
} export default addServiceForm
