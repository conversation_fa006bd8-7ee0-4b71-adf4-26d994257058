import { Selector, t } from "testcafe";
import golvar from "../Globalvariable";

const golVari = new golvar();

class navbar {

  constructor() {
    this.profilebar = Selector('[data-tc-main-menu-toggle]');
    this.logoutbtn = Selector('[id="main_menu_logout"]');
    this.addcompanybtn = Selector('[data-tc-settings-menu="Add Company"]');
    //side navigation bar
    this.assetsbtn = Selector('[data-module="assets"]');
    this.contractsbtn = Selector('[data-module="contracts"]');
    this.vendorbtn = Selector('[data-module="vendors"]');
    this.telecombtn = Selector('[data-module="telecom"]');
    this.helpdesk = Selector('[data-module="helpdesk"]');
    this.homeDashboard = Selector('[data-tc-module="home dashboard"]');
    //Setting icon on header
    this.settingicon = Selector('[data-tc-settings-menu-toggle]');
    this.companysetting = Selector('[data-tc-settings-menu="Company Settings"]');
    this.selectCompany = Selector('[data-tc-select-company="parent"]');
    this.resourceNavMenu = Selector(`${golVari.horizontalMenu} [data-tc-resources-tab]`);
    this.notifyPopup = Selector('.notification-content');
    this.assetScrollBody = Selector('[data-tc-scrollable] .simplebar-scrollbar');
    this.ticBulkUpdateBtn = Selector('[data-tc-btn="bulk update"]');
    this.peopleSelection = Selector('[data-tc-settings-menu="People"]');
    this.multiUserSelector = Selector('[data-tc-multi-user-field]');
    this.multiUserDataArrtibute = '[data-tc-multi-user-field]';
    this.companySettingTab = Selector('[data-tc-settings-nav-tab]');
    this.editIcon = 'data-tc-icon="edit"';
  }

  async signout() {
    await t.click(this.profilebar)
      .hover(this.logoutbtn)
      .click(this.logoutbtn)
      .wait(2000)
      .expect(golVari.currentpageurl()).contains('/users/sign_in')
      .wait(1000)
  }

  async navigatetocompanysetting() {
    await t.hover(this.settingicon)
      .click(this.settingicon)
      .hover(this.companysetting)
      .click(this.companysetting)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('company/overview');
  }

  async navigatetoPeoplePage() {
    await t.wait(1000)
      .hover(this.settingicon)
      .click(this.settingicon)
      .hover(this.peopleSelection)
      .click(this.peopleSelection)
      .wait(1000)
      .expect(golVari.currentpageurl()).contains('company/users')
  }

  async navigateToAssetModule() {
    await t.hover(this.assetsbtn)
      .click(this.assetsbtn)
      .expect(golVari.currentpageurl()).contains('/managed_assets')
      .wait(500)
  }

  async refresh() {
    await ClientFunction(() => {
      document.location.reload();
    })
  }

  async scrollToPosition(scrollX, scrollY) {
    await t.eval(() => {
      window.scrollTo(scrollX, scrollY);
    });
  }

  async getDocumentWidth() {
    return await this.assetScrollBody.scrollWidth;
  }

} export default navbar
