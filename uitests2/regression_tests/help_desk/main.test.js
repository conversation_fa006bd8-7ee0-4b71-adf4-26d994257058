import signinpage from '../../shared/signin/signinpage';
import addlocation from '../../shared/company_settings/add_location';
import golvar from '../../shared/Globalvariable';
import navbar from '../../shared/Components/navbar';
import companyvar from '../../shared/company_settings/company_variables';
import deletelocation from '../../shared/company_settings/delete_location';
import addAsset from '../../shared/assets/add_assets';
import assetVar from '../../shared/assets/asset_variables';
import deleteAsset from '../../shared/assets/delete_assets';
import deleteTicket from '../../shared/help_desk/delete_tickets';
import addTicket from '../../shared/help_desk/add_tickets';
import editTickets from '../../shared/help_desk/edit_tickets';
import helpDeskVar from '../../shared/help_desk/help_desk_variables';
import addgroup from '../../shared/company_settings/add_group';
import deletegroup from '../../shared/company_settings/delete_group';
import { nanoid } from 'nanoid';
import faker from 'faker';
import addWorkSpace from '../../shared/help_desk/add_work_space';
import deletedWorkSpace from '../../shared/help_desk/delete_work_space';
import automatedTask from '../../shared/help_desk/automated_tasks';
import editWorkSpace from '../../shared/help_desk/edit_work_space';
import switchWorkSpace from '../../shared/help_desk/switch_work_space';
import report from '../../shared/help_desk/reports';
import addDocument from '../../shared/help_desk/add_document';
import editDocument from '../../shared/help_desk/edit_document';
import deleteDocument from '../../shared/help_desk/delete_document';
import addFAQ from '../../shared/help_desk/add_faq';
import editFAQ from '../../shared/help_desk/edit_faq';
import deleteFAQ from '../../shared/help_desk/delete_faq';
import addCannedResponse from '../../shared/help_desk/add_canned_response';
import editCannedResponse from '../../shared/help_desk/edit_canned_response';
import deleteCannedResponse from '../../shared/help_desk/delete_canned_response';
import addKnowledgeBase from '../../shared/help_desk/add_knowledge_base';
import editKnowledgeBase from '../../shared/help_desk/edit_knowledge_base';
import deleteKnowledgeBase from '../../shared/help_desk/delete_knowledge_base';
import openPortalTicket from '../../shared/help_desk/open_portal_ticket';
import viewCount from '../../shared/assets/dashboard_asset_count';
import addCustomForms from '../../shared/company_settings/add_custom_forms';
import contractvar from '../../shared/contracts/contract_variables';
import addcontracts from '../../shared/contracts/add_contract';
import deletecontracts from '../../shared/contracts/delete_contract';
import addstaff from '../../shared/company_settings/add_staff';
import deletestaff from '../../shared/company_settings/delete_archive_staff';
import indexPageAction from '../../shared/help_desk/ticket_index_page_action';
import addVendor from '../../shared/vendor/add_vendor';
import deleteVendor from '../../shared/vendor/delete_vendor';
import addProvider from '../../shared/telecom/add_provider';
import deleteProvider from '../../shared/telecom/delete_provider';
import vendorVar from '../../shared/vendor/vendor_variables';
import ticketTasks from '../../shared/help_desk/tasks';
import timeSpent from '../../shared/help_desk/time_spent';
import archiveTicket from '../../shared/help_desk/archieve_ticket';
import ticketLogs from '../../shared/help_desk/ticket_logs';
import helpDeskPermissions from '../../shared/help_desk/permissions';
import ticketFilters from '../../shared/help_desk/index_filters';
import MergeTickets from '../../shared/help_desk/merge_tickets';
import DeleteMergedTickets from '../../shared/help_desk/delete_merge_tickets';
import deleteCustomForm from '../../shared/help_desk/delete_custom_form';
import validationCustomForm from '../../shared/help_desk/validation_custom_form';
import archiveCustomForm from '../../shared/help_desk/archieve_form';
import customFormOperation from '../../shared/help_desk/set_default_form';
import searchTicket from '../../shared/help_desk/search_ticket';
import sortingTickets from '../../shared/help_desk/sorting_ticket';
import markCommentResolution from '../../shared/help_desk/mark_comment_resolution';
import moveTicket from '../../shared/help_desk/move_ticket';
import quickView from '../../shared/help_desk/quick_view';
import incomingEmail from '../../shared/help_desk/incoming_email';
import attachmentComment from '../../shared/help_desk/attachment_comment';
import filtersMenu from '../../shared/help_desk/filters_menu';

const logIntoApp = new signinpage();
const golVari = new golvar();
const naviBar = new navbar();
const companyVari = new companyvar();
const addNewAst = new addAsset();
const editTic = new editTickets();
const addNewTicket = new addTicket();
const delTicket = new deleteTicket();
const deskVar = new helpDeskVar();
const AddNewSpace = new addWorkSpace();
const automateTask = new automatedTask();
const delWorkSpace = new deletedWorkSpace();
const editSpace = new editWorkSpace();
const switchNewSpace = new switchWorkSpace();
const addNewDoc = new addDocument();
const editDoc = new editDocument();
const delDoc = new deleteDocument();
const addNewFaq = new addFAQ();
const editNewFaq = new editFAQ();
const delFaq = new deleteFAQ();
const addNewResp = new addCannedResponse();
const editResp = new editCannedResponse();
const delResp = new deleteCannedResponse();
const addArticle = new addKnowledgeBase();
const editArticle = new editKnowledgeBase();
const delArticle = new deleteKnowledgeBase();
const ticketPortal = new openPortalTicket();
const addNewForm = new addCustomForms();
const indexPage = new indexPageAction();
const tasks = new ticketTasks();
const tmSpent = new timeSpent();
const archievedTicket = new archiveTicket();
const viewLogs = new ticketLogs();
const deskPerms = new helpDeskPermissions();
const filterApply = new ticketFilters();
const mergeTic = new MergeTickets();
const delCustForm = new deleteCustomForm();
const validateFormName = new validationCustomForm();
const archiveDefaultForm = new archiveCustomForm();
const custFormOp = new customFormOperation();
const searchingTicket = new searchTicket();
const checkSorting = new sortingTickets();
const closeOnComment = new markCommentResolution();
const switchTicketLocation = new moveTicket();
const quickViewFilters = new quickView();
const incomingEmails = new incomingEmail();
const attachmentComments = new attachmentComment();
const filtersHelpdesk = new filtersMenu();

const workSpaceName = deskVar.workSpaceName;
const editWorkName = deskVar.editworkName;
const editWorkName1 = 'Updating workspace' + nanoid(2);
const documentName = deskVar.documentName;
const documentName1 = deskVar.documentName1;
const editDocName = deskVar.editDocName;
const ticketSubject = 'testing' + faker.lorem.word(5);
const ticketSubject1 = 'Need to Fix' + nanoid(2);
const editSubject = faker.lorem.word(5);
const ticketSubject2 = "Test Ticket" + faker.lorem.word(5);
const ticketSubject3 = 'Ticket open' + nanoid(1);
const ticketSubject4 = 'Ticket portal' + nanoid(2);
const ticketSubject5 = 'Ticket Work' + nanoid(2);
const ticketSubject6 = 'Ticket Differ' + nanoid(2);
const ticketSubject7 = 'Ticket Merge' + nanoid(2);
const ticketSubject8 = 'Ticket Merging' + nanoid(2);
const ticketSubject9 = 'As a user' + nanoid(2);
const ticketSubject10 = 'update and refix ticket' + nanoid(2);
const ticketSubject11 = 'Implement notification ' + nanoid(2);
const ticketSubject12 = 'Trigger task' + nanoid(2);
const ticketSubject13 = 'MultiTasking Tickets' + nanoid(1);
const ticketSubject14 = 'help desk operation' + nanoid(1);
const newQuestion = 'question' + nanoid(1) + '?';
const newAnswer = 'Answer' + nanoid(2);
const editQuestion = 'editquestion' + nanoid(1) + '?';
const editAnswer = 'EditAnswer' + nanoid(2);
const responsetitle = deskVar.newTitle;
const editResponseTitle = deskVar.editTitle;
const category = 'Marketing';
const nameLoc = golVari.locName3;
const nameLoc2 = golVari.locName4;
const astName1 = golVari.assetName1;
const astName2 = golVari.assetName2;
const groupName1 = golVari.groupName1;
const groupName2 = golVari.groupName2;
const groupName3 = golVari.groupName3;
const staffFstName = golVari.firstName2;
const staffLstName = golVari.lastName2;
const fullName1 = staffFstName + ' ' + staffLstName;
const emailUser = golVari.email2;
const staffFstName1 = golVari.firstName3;
const staffLstName1 = golVari.lastName3;
const fullName2 = staffFstName1 + ' ' + staffLstName1;
const emailUser1 = golVari.email3;
const companyUserFullName = golVari.fullName;
const contract = golVari.contract;
const contract1 = golVari.contract1;
const vendor = golVari.vendor;
const vendor1 = golVari.vendor1;
const telecomService = golVari.telecomService1;
const telecomService1 = golVari.telecomService2;
const taskDesc = 'Test Task' + nanoid(2);
const taskDesc1 = 'Test Task2' + nanoid(2);
const taskDesc2 = 'Tasks completed' + nanoid(2);
const taskDesc3 = 'Tasks Qa' + nanoid(2);
const taskDesc4 = 'Automated Task' + nanoid(1);
const editTaskDesc = 'Edit Task' + nanoid(2);
const userName = golVari.firstName + ' ' + golVari.lastName;
const custFormName = 'Custom Form' + nanoid(2);
const custTicFormName = companyVari.ticketFormName;
const automatedTaskName = deskVar.eventName;
const custStatus1 = deskVar.status1;
const custStatus2 = deskVar.status2;
const custStatus3 = deskVar.status3;
const custPriority1 = deskVar.prority1;
const custPriority2 = deskVar.prority2;
const custPriority3 = deskVar.prority3;

fixture('Help desk ticket and workspaces regression test cases')

test('Help desk ticket ui testcases', async t => {
  logIntoApp.siginCompanySelect(golVari.emailaddress, golVari.password, golVari.companyname);
  await t.wait(2000);
  await t.hover(addNewAst.companyViewToggle);
  await t.wait(1000);
  await t.click(addNewAst.companyViewToggle);
  await t.wait(2000);
  automateTask.addAutomatedTasks(automatedTaskName, deskVar.triggerDetail1, deskVar.ticketCreatedTrigger, deskVar.actionAlert, deskVar.recipientAllAdmin, deskVar.addTextAlert, deskVar.defaultWorkSpace);
  await t.wait(1000);
  filterApply.viewAllFiltersTypeNOption()
  await t.wait(1000);
  addNewForm.addTicketCustomform(custTicFormName, companyVari.option, companyVari.optionName, custStatus1, custStatus2, custStatus3, custPriority1, custPriority2, custPriority3);
  await t.wait(1000);
  custFormOp.changeTicForm(custTicFormName)
  await t.wait(1000);
  custFormOp.validationTicFormField()
  await t.wait(1000);
  addNewTicket.addTicketwithBaseForm(ticketSubject, deskVar.highPriority, deskVar.openStatus, groupName1, groupName2, astName1, nameLoc, deskVar.notes);
  await t.wait(1000);
  addNewTicket.addTicketwithBaseForm(ticketSubject1, deskVar.lowPriority, deskVar.inProgressStatus, groupName1, groupName2, astName2, nameLoc2, deskVar.notes);
  await t.wait(1000);
  addNewTicket.addTicketwithBaseForm(ticketSubject7, deskVar.highPriority, deskVar.closeStatus, groupName1, groupName2, astName2, nameLoc, deskVar.notes);
  await t.wait(1000);
  addNewTicket.addTicketwithBaseForm(ticketSubject8, deskVar.highPriority, deskVar.inProgressStatus, groupName1, groupName2, astName1, nameLoc2, deskVar.notes);
  await t.wait(1000);
  addNewTicket.addTicketwithBaseForm(ticketSubject9, deskVar.meduimPriority, deskVar.closeStatus, groupName1, groupName2, astName1, nameLoc, deskVar.notes);
  await t.wait(1000);
  addNewTicket.addTicThroCustomForm(custTicFormName, ticketSubject10, fullName1, telecomService, contract, deskVar.meduimPriority, nameLoc, vendor, astName2, deskVar.openStatus, deskVar.everyOneUser, deskVar.adminUser, companyVari.textArea, companyVari.richText, companyVari.tag1, companyVari.date1, companyVari.phoneNumber1, companyVari.list1, companyVari.text1, companyVari.number1, category, companyVari.notes);
  await t.wait(1000);
  addNewTicket.addTicThroCustomForm(custTicFormName, ticketSubject11, fullName2, telecomService1, contract1, deskVar.lowPriority, nameLoc2, vendor1, astName1, deskVar.openStatus, deskVar.everyOneUser, deskVar.adminUser, companyVari.textArea, companyVari.richText, companyVari.tag1, companyVari.date1, companyVari.phoneNumber1, companyVari.list1, companyVari.text1, companyVari.number1, category, companyVari.notes);
  await t.wait(2000);
  indexPage.viewHDGrid(); //Sorting check in grid view
  await t.wait(1000);
  checkSorting.setItemsInAscendingOrder('Ticket Number');
  await t.wait(1000);
  checkSorting.viewSortedItems('0', ticketSubject);
  await t.wait(1000);
  checkSorting.viewSortedItems('1', ticketSubject1);
  await t.wait(1000);
  checkSorting.viewSortedItems('2', ticketSubject8);
  await t.wait(1000);
  checkSorting.viewSortedItems('3', ticketSubject10);
  await t.wait(1000);
  checkSorting.viewSortedItems('4', ticketSubject11);
  await t.wait(1000);
  checkSorting.setItemsInDescendingOrder('Ticket Number');
  await t.wait(1000);
  checkSorting.viewSortedItems('0', ticketSubject11);
  await t.wait(1000);
  checkSorting.viewSortedItems('1', ticketSubject10);
  await t.wait(1000);
  checkSorting.viewSortedItems('2', ticketSubject8);
  await t.wait(1000);
  checkSorting.viewSortedItems('3', ticketSubject1);
  await t.wait(1000);
  checkSorting.viewSortedItems('4', ticketSubject);
  await t.wait(1000);
  checkSorting.setItemsInAscendingOrder('Subject');
  await t.wait(1000);
  checkSorting.viewSortedItems('0', ticketSubject11);
  await t.wait(1000);
  checkSorting.viewSortedItems('1', ticketSubject1);
  await t.wait(1000);
  checkSorting.viewSortedItems('2', ticketSubject);
  await t.wait(1000);
  checkSorting.viewSortedItems('3', ticketSubject8);
  await t.wait(1000);
  checkSorting.viewSortedItems('4', ticketSubject10);
  await t.wait(1000);
  checkSorting.setItemsInDescendingOrder('Subject');
  await t.wait(1000);
  checkSorting.viewSortedItems('0', ticketSubject10);
  await t.wait(1000);
  checkSorting.viewSortedItems('1', ticketSubject8);
  await t.wait(1000);
  checkSorting.viewSortedItems('2', ticketSubject);
  await t.wait(1000);
  checkSorting.viewSortedItems('3', ticketSubject1);
  await t.wait(1000);
  checkSorting.viewSortedItems('4', ticketSubject11);
  await t.wait(1000);
  checkSorting.setItemsInAscendingOrder('Assigned To');
  await t.wait(1000);
  checkSorting.viewSortedItems('0', ticketSubject10);
  await t.wait(1000);
  checkSorting.viewSortedItems('1', ticketSubject11);
  await t.wait(1000);
  checkSorting.viewSortedItems('2', ticketSubject);
  await t.wait(1000);
  checkSorting.viewSortedItems('3', ticketSubject1);
  await t.wait(1000);
  checkSorting.viewSortedItems('4', ticketSubject8);
  await t.wait(1000);
  checkSorting.setItemsInDescendingOrder('Assigned To');
  await t.wait(1000);
  checkSorting.viewSortedItems('0', ticketSubject);
  await t.wait(1000);
  checkSorting.viewSortedItems('1', ticketSubject1);
  await t.wait(1000);
  checkSorting.viewSortedItems('2', ticketSubject8);
  await t.wait(1000);
  checkSorting.viewSortedItems('3', ticketSubject10);
  await t.wait(1000);
  checkSorting.viewSortedItems('4', ticketSubject11);
  await t.wait(1000);
  checkSorting.setItemsInAscendingOrder('Created By');
  await t.wait(1000);
  checkSorting.viewSortedItems('0', ticketSubject);
  await t.wait(1000);
  checkSorting.viewSortedItems('1', ticketSubject1);
  await t.wait(1000);
  checkSorting.viewSortedItems('2', ticketSubject8);
  await t.wait(1000);
  checkSorting.viewSortedItems('3', ticketSubject10);
  await t.wait(1000);
  checkSorting.viewSortedItems('4', ticketSubject11);
  await t.wait(1000);
  checkSorting.setItemsInDescendingOrder('Created By');
  await t.wait(1000);
  checkSorting.viewSortedItems('0', ticketSubject);
  await t.wait(1000);
  checkSorting.viewSortedItems('1', ticketSubject1);
  await t.wait(1000);
  checkSorting.viewSortedItems('2', ticketSubject8);
  await t.wait(1000);
  checkSorting.viewSortedItems('3', ticketSubject10);
  await t.wait(1000);
  checkSorting.viewSortedItems('4', ticketSubject11);
  await t.wait(1000);
  checkSorting.setItemsInAscendingOrder('Priority');
  await t.wait(1000);
  checkSorting.viewSortedItems('0', ticketSubject1);
  await t.wait(1000);
  checkSorting.viewSortedItems('1', ticketSubject11);
  await t.wait(1000);
  checkSorting.viewSortedItems('2', ticketSubject10);
  await t.wait(1000);
  checkSorting.viewSortedItems('3', ticketSubject);
  await t.wait(1000);
  checkSorting.viewSortedItems('4', ticketSubject8);
  await t.wait(1000);
  checkSorting.setItemsInDescendingOrder('Priority');
  await t.wait(1000);
  checkSorting.viewSortedItems('4', ticketSubject1);
  await t.wait(1000);
  checkSorting.viewSortedItems('3', ticketSubject11);
  await t.wait(1000);
  checkSorting.viewSortedItems('2', ticketSubject10);
  await t.wait(1000);
  checkSorting.viewSortedItems('1', ticketSubject);
  await t.wait(1000);
  checkSorting.viewSortedItems('0', ticketSubject8);
  await t.wait(1000);
  checkSorting.setItemsInAscendingOrder('Status');
  await t.wait(1000);
  checkSorting.viewSortedItems('0', ticketSubject);
  await t.wait(1000);
  checkSorting.viewSortedItems('1', ticketSubject10);
  await t.wait(1000);
  checkSorting.viewSortedItems('2', ticketSubject11);
  await t.wait(1000);
  checkSorting.viewSortedItems('3', ticketSubject1);
  await t.wait(1000);
  checkSorting.viewSortedItems('4', ticketSubject8);
  await t.wait(1000);
  checkSorting.setItemsInDescendingOrder('Status');
  await t.wait(1000);
  checkSorting.viewSortedItems('4', ticketSubject11);
  await t.wait(1000);
  checkSorting.viewSortedItems('3', ticketSubject10);
  await t.wait(1000);
  checkSorting.viewSortedItems('2', ticketSubject);
  await t.wait(1000);
  checkSorting.viewSortedItems('1', ticketSubject8);
  await t.wait(1000);
  checkSorting.viewSortedItems('0', ticketSubject1);
  await t.wait(1000);
  filterApply.removeFilter('Active');
  await t.wait(1000);
  filterApply.applyFilter('Active', 'Status', 'with label');
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject1);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9);
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7);
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8);
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10);
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11);
  await t.wait(1000);
  filterApply.removeFilter('Active');
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject1);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9);
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject);
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7);
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.applyFilter('Open', 'Status', 'with label')
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  filterApply.removeFilter('Open')
  await t.wait(1000);
  filterApply.applyFilter('In Progress', 'Status', 'with label')
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject11)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject)
  await t.wait(1000);
  filterApply.removeFilter('In Progress')
  await t.wait(1000);
  filterApply.applyFilter('Closed', 'Status', 'with label')
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.removeFilter('Closed')
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.applyFilter('In Progress', 'Status', 'with label')
  await t.wait(1000);
  filterApply.applyFilter('Closed', 'Status', 'with label')
  await t.wait(1000);
  filterApply.applyFilter('Open', 'Status', 'with label')
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.removeFilter('Closed')
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.removeFilter('Open')
  await t.wait(1000);
  filterApply.removeFilter('In Progress')
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.applyFilter('Tickets this month', 'Common Filter', 'with no label')
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.removeFilter('Tickets this month')
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.applyFilter('Low', 'Priority', 'with label')
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject10)
  await t.wait(1000);
  filterApply.removeFilter('Low')
  await t.wait(1000);
  filterApply.applyFilter('Medium', 'Priority', 'with label')
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.removeFilter('Medium')
  await t.wait(1000);
  filterApply.applyFilter('High', 'Priority', 'with label')
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.removeFilter('High')
  await t.wait(1000);
  filterApply.applyFilter('Low', 'Priority', 'with label')
  await t.wait(1000);
  filterApply.applyFilter('Medium', 'Priority', 'with label')
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.removeFilter('Low')
  await t.wait(1000);
  filterApply.applyFilter('High', 'Priority', 'with label')
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.removeFilter('Medium')
  await t.wait(1000);
  filterApply.removeFilter('High')
  await t.wait(1000);
  filterApply.applyFilter('IT General Request', 'Form', 'with label')
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.removeFilter('IT General Request')
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.applyFilter(custTicFormName, 'Form', 'with label')
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  filterApply.applyFilter('IT General Request', 'Form', 'with label')
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.removeFilter('IT General Request')
  await t.wait(1000);
  filterApply.removeFilter(custTicFormName)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.applyFilter('Manually Added', 'Source', 'with label')
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.removeFilter('Manually Added')
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.filterByField('Asset', astName1)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject10)
  await t.wait(1000);
  filterApply.removeFilterByField('Asset', astName1)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.filterByField('Asset', astName2)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.removeFilterByField('Asset', astName2)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.filterByField('Location', nameLoc)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.removeFilterByField('Location', nameLoc)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.filterByField('Location', nameLoc2)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject10)
  await t.wait(1000);
  filterApply.removeFilterByField('Location', nameLoc2)
  await t.wait(1000);
  filterApply.filterByField('Vendor', vendor)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject11)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  filterApply.removeFilterByField('Vendor', vendor)
  await t.wait(1000);
  filterApply.filterByField('Vendor', vendor1)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  filterApply.removeFilterByField('Vendor', vendor1)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject1)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject9)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject)
  await t.wait(1000);
  searchingTicket.notMatchTicRecord(ticketSubject7)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject8)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject10)
  await t.wait(1000);
  searchingTicket.matchTicRecord(ticketSubject11)
  await t.wait(1000);
  filterApply.applyFilter('Active', 'Status', 'with label');
  await t.wait(1000);
  addNewTicket.navigateToTicTab();
  await t.wait(1000);
  mergeTic.ticketMergingWithoutParent(ticketSubject10, ticketSubject8);
  await t.wait(1000);
  mergeTic.ticketMergingWithParent(ticketSubject10, ticketSubject8);
  await t.wait(1000);
  mergeTic.unmergeChildTicketNCheckRemoveLink(ticketSubject10, ticketSubject8);
  await t.wait(1000);
  viewLogs.searchLogNoPresent(ticketSubject2);
  await t.wait(1000);
  viewLogs.createdTicketLogs(ticketSubject);
  await t.wait(1000);
  addNewTicket.addTicketwithBaseForm(ticketSubject13, deskVar.highPriority, deskVar.inProgressStatus, groupName1, groupName2, astName1, nameLoc, deskVar.notes);
  await t.wait(1000);
  viewLogs.createdTicketLogs(ticketSubject);
  await t.wait(1000);
  tasks.tasksValidation(ticketSubject13, taskDesc, deskVar.dueDate, deskVar.completeFutureDate);
  await t.wait(1000);
  addNewTicket.navigateToTicShowPage(ticketSubject13);
  await t.wait(1000);
  tasks.addTask(taskDesc1, deskVar.meduimPriority, deskVar.dueDate, deskVar.completedDate, deskVar.helpDeskAgent);
  await t.wait(1000);
  addNewTicket.navigateToTicShowPage(ticketSubject);
  await t.wait(1000);
  tasks.addTask(taskDesc2, deskVar.highPriority, deskVar.dueDate, deskVar.completedDate, deskVar.helpDeskAgent);
  await t.wait(1000);
  tasks.addTask(taskDesc4, deskVar.meduimPriority, deskVar.dueDate, deskVar.completedDate, deskVar.helpDeskAgent);
  await t.wait(1000);
  tasks.addTask(taskDesc3, deskVar.lowPriority, deskVar.dueDate, deskVar.completedDate, deskVar.everyOneUser);
  await t.wait(1000);
  tasks.dragTask(taskDesc2, taskDesc3);
  await t.wait(1000);
  tasks.editTask(taskDesc3, editTaskDesc, deskVar.highPriority, deskVar.dueDate, deskVar.completedDate, deskVar.everyOneUser);
  await t.wait(1000);
  tasks.markCompleteTask(taskDesc2, deskVar.highPriority, deskVar.dueDate, deskVar.helpDeskAgent);
  await t.wait(1000);
  tasks.taskSelectOrDeselectall(editTaskDesc);
  await t.wait(1000);
  tasks.singleMergeTask(taskDesc2, '', 'cancel');
  await t.wait(1000);
  tasks.singleMergeTask(taskDesc2, editTaskDesc, 'save');;
  await t.wait(1000);
  tasks.multiMergeTask(taskDesc4, taskDesc2, editTaskDesc);
  await t.wait(1000);
  tasks.unLinkMergeTask(taskDesc2);
  await t.wait(1000);
  tmSpent.addTimeSpents(deskVar.startTimeInHours, deskVar.startTimeInMints, deskVar.endTimeInHours, deskVar.endTimeInMints, deskVar.timeSpent1, deskVar.date, deskVar.notes);
  await t.wait(1000);
  tmSpent.editTimeSpent(deskVar.editStartTimeHr, deskVar.editStartTimeMint, deskVar.editEndTimeHr, deskVar.editEndTimeMint, deskVar.editTimeSpent, deskVar.date, deskVar.editNotes);
  await t.wait(1000);
  tmSpent.deleteTimeSpent();
  await t.wait(1000);
  filterApply.removeFilter('Active')
  await t.wait(1000);
  archievedTicket.archivedTickets(ticketSubject, 'cancel');
  await t.wait(1000);
  archievedTicket.archivedTickets(ticketSubject, 'yes');
  await t.wait(1000);
  archievedTicket.unarchiveTickets(ticketSubject, 'cancel');
  await t.wait(1000);
  archievedTicket.unarchiveTickets(ticketSubject, 'yes');
  await t.wait(1000);
  filterApply.removeFilter('Archived'); //need to applied filter
  await t.wait(1000);
  filterApply.applyFilter('Active', 'Status', 'with label'); //issue is here the active already applied when this active filter applied then active filter diisappear
  await t.wait(1000);
  addNewTicket.addTicWithoutSubject(deskVar.lowPriority, deskVar.openStatus, groupName1, groupName2, astName1, nameLoc, deskVar.notes);
  await t.wait(1000);
  addNewTicket.navigateBackToTicTab();
  await t.wait(1000);
  addNewTicket.addTicThroCustomForm(custTicFormName, ticketSubject2, fullName1, telecomService, contract, deskVar.highPriority, nameLoc2, vendor, astName1, deskVar.openStatus, deskVar.everyOneUser, deskVar.adminUser, companyVari.textArea, companyVari.richText, companyVari.tag1, companyVari.date1, companyVari.phoneNumber1, companyVari.list1, companyVari.text1, companyVari.number1, category, companyVari.notes);
  await t.wait(1000);
  addNewTicket.addTicThroCustomForm1(custTicFormName, ticketSubject6, fullName1, telecomService, contract, deskVar.highPriority, nameLoc2, vendor, astName1, deskVar.openStatus, deskVar.everyOneUser, deskVar.adminUser, companyVari.textArea, companyVari.richText, companyVari.tag1, companyVari.date1, companyVari.phoneNumber1, companyVari.list1, companyVari.text1, companyVari.number1, category, companyVari.notes);
  await t.wait(1000);
  indexPage.updateTicketThroughGridView(ticketSubject2, deskVar.openStatus, deskVar.lowPriority);
  await t.wait(1000);
  filterApply.removeMultiFilters('Open', 'Low'); // //in this both  filters are remove
  await t.wait(1000);
  filterApply.removeNAppliedFilters('Active', 'Closed'); // //in this active filter remove and closed filter applied
  await t.wait(1000);
  editTic.editTicketswithBaseForm(ticketSubject9, groupName1, groupName2, astName1, nameLoc, '', editSubject, deskVar.lowPriority, deskVar.openStatus, deskVar.everyOneUser, deskVar.adminUser, astName1, nameLoc, '', deskVar.comments, deskVar.notes, deskVar.highPriority, deskVar.dueDate, deskVar.completedDate, deskVar.helpDeskAgent, deskVar.timeSpent, deskVar.date, deskVar.notes);
  await t.wait(1000);
  filterApply.removeNAppliedFilters('Closed', 'Active'); // //in this active filter applied and closed filter remove
  await t.wait(1000);
  delTicket.deleteTickets(editSubject, 'cancel');
  await t.wait(1000);
  filterApply.removeFilter('Archived');
  await t.wait(1000);
  delTicket.deleteTickets(editSubject, 'yes');
  await t.wait(1000);
  addNewDoc.addDocuments(documentName);
  await t.wait(1000);
  // addArticle.addKnowledgeBases(deskVar.articleTitle, deskVar.articleTags, deskVar.selectCategory, documentName, deskVar.articleText); //need to revamp because of change knowledge base
  // await t.wait(1000);
  addNewDoc.addDocuments(documentName1);
  await t.wait(1000);
  // editArticle.editKnowledgeBases(deskVar.articleTitle, deskVar.selectCategory, deskVar.editArticleTitle, deskVar.editArticleTags, deskVar.selectCategory1, documentName1, deskVar.articleText); due to change in knowledge base
  // await t.wait(1000);
  // delArticle.deleteKnowledgeBases(deskVar.editArticleTitle, deskVar.selectCategory1); //due to change in knowledge base
  // await t.wait(1000);
  delDoc.deleteDocuments(documentName1);
  await t.wait(1000);
  editDoc.editDocuments(documentName, editDocName);
  await t.wait(1000);
  delDoc.deleteDocuments(editDocName);
  await t.wait(1000);
  addNewFaq.addFAQs(newQuestion, newAnswer, deskVar.selectCategory);
  await t.wait(1000);
  editNewFaq.editFAQs(newQuestion, editQuestion, editAnswer, deskVar.selectCategory1);
  await t.wait(1000);
  delFaq.deleteFAQs(editQuestion, 'cancel');
  await t.wait(1000);
  delFaq.deleteFAQs(editQuestion, 'yes');
  await t.wait(1000);
  addNewResp.addCannedResponses(responsetitle, deskVar.text);
  await t.wait(1000);
  editResp.editCannedResponses(responsetitle, editResponseTitle, deskVar.text);
  await t.wait(1000);
  delResp.deleteCannedResponses(editResponseTitle);
  await t.wait(1000);
  archiveDefaultForm.archiveDefaultForms('IT General Request');
  await t.wait(1000);
  custFormOp.setFromsDefault(custTicFormName);
  await t.wait(2000);
  archiveDefaultForm.archiveDefaultForms(custTicFormName);
  await t.wait(2000);
  custFormOp.setFromsDefault('IT General Request');
  await t.wait(2000);
  archiveDefaultForm.archiveDefaultForms('IT General Request');
  await t.wait(1000);
  validateFormName.addFormValidation(companyVari.ticketFormName1, 'Blank State Copy', 'IT General Request');
  await t.wait(1000);
  delCustForm.cancelDeleteUnarchiveForm(custTicFormName);
  await t.wait(1000);
  delCustForm.deleteCustomForms(companyVari.ticketFormName1);
  await t.wait(1000);
  AddNewSpace.validateName();
  await t.wait(1000);
  deskPerms.navigateToPermsModal(); ////Permission modal open and its operation start here
  await t.wait(1000);
  deskPerms.addGroupPerms(groupName3);
  await t.wait(1000);
  deskPerms.addUserPerms(userName);
  await t.wait(1000);
  deskPerms.viewMemberInGroup(groupName3, companyUserFullName);
  await t.wait(1000);
  deskPerms.changePermissionUserGroup(groupName3, userName, 'Read All / Write Mine', 'Write All');
  await t.wait(1000);
  deskPerms.changePermissionUserGroup(groupName3, userName, 'Write All', 'Read All / Write Mine');
  await t.wait(1000);
  deskPerms.checkAddedUserGroupNotExist(groupName3, userName);
  await t.wait(1000);
  deskPerms.deleteMemberInGroup(groupName3, companyUserFullName, '1');
  await t.wait(1000);
  deskPerms.deleteMemberInGroup('Admins', userName, '1');
  await t.wait(1000);
  deskPerms.deleteUserGroupPerms(groupName3, userName);
  await t.wait(1000);
  deskPerms.checkRemovedUserGroupExist(groupName3, userName);
  await t.wait(1000);
  deskPerms.closePermsModal(); ////Close the permission modal
  await t.wait(1000);
  naviBar.signout();
  await t.wait(1000);
});

test('Check the automated task is trigger', async t => {
  logIntoApp.siginCompanySelect(golVari.emailaddress, golVari.password, golVari.companyname);
  await t.wait(2000);
  await t.hover(addNewAst.companyViewToggle);
  await t.wait(1000);
  await t.click(addNewAst.companyViewToggle);
  await t.wait(2000);
  addNewTicket.addTicketwithBaseForm(ticketSubject12, deskVar.meduimPriority, deskVar.openStatus, groupName2, groupName1, astName1, nameLoc, deskVar.notes);
  await t.wait(1000);
  automateTask.viewDefaultAutomatedTask(); //this testcases need to run first before checking automated task
  await t.wait(1000);
  const getTaskId1 = await automateTask.assignedToChangesTask();
  await t.wait(1000);
  const getTaskId2 = await automateTask.notifyPriorityChangesTask();
  await t.wait(1000);
  const getTaskId3 = await automateTask.notifyStatusChangesTask();
  await t.wait(1000);
  const getTaskId4 = await automateTask.notifyTicketCreatedTask();
  await t.wait(1000);
  const getTaskId5 = await automateTask.notifyAttachmentAddedTask();
  await t.wait(1000);
  automateTask.checkTicketCreatedTrigger(getTaskId4, ticketSubject12);
  await t.wait(1000);
  automateTask.checkAssignedToChangesTrigger(getTaskId1, ticketSubject12, groupName3);
  await t.wait(1000);
  automateTask.checknotifyPriorityChangesTrigger(getTaskId2, ticketSubject12, deskVar.highPriority);
  await t.wait(1000);
  automateTask.checknotifyStatusChangesTrigger(getTaskId3, ticketSubject12, deskVar.inProgressStatus);
  await t.wait(1000);
  // automateTask.checkAttachmentAddedTrigger(getTaskId5, ticketSubject12) // Attachment issue https://www.loom.com/share/0da9b67413f04b5c894371a2e32b6029
  // await t.wait(1000);
  delTicket.deleteTickets(ticketSubject12, 'yes');
  await t.wait(1000);
  filterApply.removeFilter('Archived');
  await t.wait(1000);
  naviBar.signout();
  await t.wait(1000);
});

test('Help desk workspace ui testcases', async t => {
  logIntoApp.siginCompanySelect(golVari.emailaddress, golVari.password, golVari.companyname);
  await t.wait(2000);
  await t.hover(addNewAst.companyViewToggle);
  await t.wait(1000);
  await t.click(addNewAst.companyViewToggle);
  await t.wait(2000);
  AddNewSpace.addWorkSpaces(workSpaceName, groupName1, deskVar.userPerm1, deskVar.userPerm2, deskVar.defaultForm);
  await t.wait(1000);
  switchNewSpace.switchWorkSpaces(deskVar.defaultWorkSpace);
  await t.wait(1000);
  editSpace.copyForm(workSpaceName, custTicFormName);
  await t.wait(1000);
  AddNewSpace.removeDefaultForm(workSpaceName, deskVar.defaultForm);
  await t.wait(1000);
  switchNewSpace.switchWorkSpaces(workSpaceName);
  await t.wait(1000);
  automateTask.addAutomatedTasks(automatedTaskName, deskVar.triggerDetail1, deskVar.ticketCreatedTrigger, deskVar.actionAlert, deskVar.recipientAllAdmin, deskVar.addTextAlert, workSpaceName);
  await t.wait(1000);
  switchNewSpace.selectWorkSpace(workSpaceName);
  await t.wait(1000);
  addNewTicket.addTicketwithBaseForm(ticketSubject5, deskVar.highPriority, deskVar.inProgressStatus, groupName1, groupName2, astName1, nameLoc, deskVar.notes);
  await t.wait(1000);
  automateTask.deleteAutomatedTask(automatedTaskName, workSpaceName);
  await t.wait(1000);
  indexPage.updateTicketThroughListView(ticketSubject5, groupName2, deskVar.inProgressStatus, deskVar.meduimPriority, groupName1);
  await t.wait(1000);
  switchNewSpace.selectWorkSpace(workSpaceName);
  await t.wait(1000);
  editSpace.editWorkSpaces(workSpaceName, editWorkName, groupName2, deskVar.userPerm2, deskVar.userPerm1);
  await t.wait(1000);
  editSpace.editWorkSpaces(deskVar.defaultWorkSpace, editWorkName1, groupName3, deskVar.userPerm2, deskVar.userPerm1);
  await t.wait(1000);
  editSpace.removeMember(editWorkName, deskVar.everyOneUser);
  await t.wait(1000);
  ticketPortal.createTicketWithlogin(ticketSubject3, deskVar.highPriority, deskVar.inProgressStatus, groupName1, deskVar.notes, editWorkName1, deskVar.defaultForm);
  await t.wait(1000);
  ticketPortal.createTicketWithoutLogin(golVari.emailaddress, golVari.password, ticketSubject4, deskVar.highPriority, deskVar.inProgressStatus, golVari.guestEmail, deskVar.notes, editWorkName1, deskVar.defaultForm);
  await t.wait(1000);
  switchNewSpace.selectWorkSpace(editWorkName1);
  await t.wait(1000);
  custFormOp.hideFieldOpenPortal(custTicFormName, editWorkName1);
  await t.wait(1000);
  delCustForm.deleteCustomForms(custTicFormName);
  await t.wait(1000);
  switchTicketLocation.moveTicketWorkspace(ticketSubject13, editWorkName, deskVar.defaultForm);
  await t.wait(1000);
  delWorkSpace.deleteWorkSpaces(editWorkName);
  await t.wait(1000);
  filtersHelpdesk.viewWorkspaceAndFieldFilters(deskVar.filterByFieldHeaderText, deskVar.filterHelpdesk);
  await t.wait(1000);
  filtersHelpdesk.viewCommonFilters(deskVar.commonFilterHeaderText, deskVar.ticketsThisMonthText, deskVar.almostDueText, deskVar.recentlyClosedText, deskVar.createdTodayText, deskVar.completedThisWeekText, deskVar.completedThisMonthText, deskVar.closedByDateText, deskVar.createdByMeText, deskVar.assignedToMeText);
  await t.wait(1000);
  filtersHelpdesk.viewPriorityFilters(deskVar.filterByPriorityHeaderText, deskVar.lowText, deskVar.mediumText, deskVar.highText);
  await t.wait(1000);
  filtersHelpdesk.viewStatusFilters(deskVar.filterByStatusHeaderText, deskVar.activeText, deskVar.openText, deskVar.closedText, deskVar.unreadText, deskVar.archivedText, deskVar.inProgressText);
  await t.wait(1000);
  filtersHelpdesk.viewAssignmentFilters(deskVar.filterByAssignmentHeaderText, deskVar.assignedText, deskVar.unAssignedText);
  await t.wait(1000);
  filtersHelpdesk.viewFilterByTimeFrame(deskVar.filterByTimeFrameHeaderText, deskVar.currentMonthText, deskVar.lastMonthText, deskVar.lastSixMonthText, deskVar.lastTwelveMonthText, deskVar.calendarYearText, deskVar.fiscalYearText, deskVar.lastTwoYearsText, deskVar.customDateText);
  await t.wait(1000);
  filtersHelpdesk.viewFilterBySource(deskVar.filterBySourceHeaderText, deskVar.manuallyAddedText, deskVar.emailText, deskVar.slackText, deskVar.autoGeneratedText, deskVar.microSoftTeamsText, deskVar.splitText, deskVar.mobileAppText);
  await t.wait(1000);
  filtersHelpdesk.viewFilterByForm(deskVar.filterByFormText, deskVar.itGeneralText);
  await t.wait(1000);
  filtersHelpdesk.applyCommonFilters(deskVar.ticketsThisMonthText, deskVar.almostDueText, deskVar.recentlyClosedText, deskVar.createdTodayText, deskVar.completedThisWeekText, deskVar.completedThisMonthText);
  await t.wait(1000);
  filtersHelpdesk.applyPriorityFilters(deskVar.appliedLow, deskVar.appliedMedium, deskVar.appliedHigh);
  await t.wait(1000);
  filtersHelpdesk.applyStatusFilters(deskVar.activeStatusPill, deskVar.appliedOpen, deskVar.closedStatusPill, deskVar.appliedUnread, deskVar.appliedArchived, deskVar.appliedInProgress);
  await t.wait(1000);
  filtersHelpdesk.applyAssignmentFilters(deskVar.appliedAssigned, deskVar.appliedUnAssigned);
  await t.wait(1000);
  filtersHelpdesk.applyTimeFrameFilters(deskVar.currentMonthText, deskVar.lastMonthText, deskVar.lastSixMonthText, deskVar.lastTwelveMonthText, deskVar.calendarYearText, deskVar.fiscalYearText, deskVar.lastTwoYearsText);
  await t.wait(1000);
  filtersHelpdesk.applySourceFilters(deskVar.appliedManual, deskVar.appliedEmail, deskVar.appliedSlack, deskVar.appliedAutoGenerated, deskVar.appliedTeams, deskVar.appliedSplit, deskVar.appliedApp);
  await t.wait(1000);
  filtersHelpdesk.applyFormFilters(deskVar.appliedIT);
  await t.wait(1000);
  attachmentComments.addCommentForAttachment(ticketSubject14, deskVar.attachmentAddedText);
  await t.wait(2000);
  incomingEmails.noIncomingEmail(deskVar.noIncomingText);
  await t.wait(2000);
  quickViewFilters.viewQuickViewFilters(deskVar.myActiveFilter, deskVar.allActiveFilter, deskVar.unassignedFilter, deskVar.closeFilter);
  await t.wait(1000);
  quickViewFilters.applyQuickViewFilters();
  await t.wait(1000);
  switchTicketLocation.moveTicketForm(ticketSubject14, formName);
  await t.wait(1000);
  settingOperations.generalSettings();
  await t.wait(1000);
  closeOnComment.commentResolution(ticketSubject14, deskVar.notes, deskVar.closeStatus);
  await t.wait(2000);
  naviBar.signout();
  await t.wait(1000);
});
