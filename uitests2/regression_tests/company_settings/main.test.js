import addcompany from '../../shared/company_settings/add_childcompany';
import navbar from '../../shared/Components/navbar';
import companyvar from '../../shared/company_settings/company_variables';
import signinpage from '../../shared/signin/signinpage';
import golvar from '../../shared/Globalvariable';

const addnewcompany = new addcompany();
const navibar = new navbar();
const companyvari = new companyvar();
const loginintoapp = new signinpage();
const golvari = new golvar();
const childCompanyName = companyvari.companyname1;

fixture(`Company setting regression test cases`)

test('Company setting ui testcases', async t => {
  loginintoapp.sigin(golvari.emailaddress, golvari.password)
  await t.wait(2000);
  addnewcompany.addchildcompany(childCompanyName, companyvari.subdomain1)
  await t.wait(1000);
  navibar.signout()
  await t.wait(2000)
})
