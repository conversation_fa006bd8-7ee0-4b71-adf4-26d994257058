import addAssetType from "../../shared/assets/add_asset_type";
import signinpage from "../../shared/signin/signinpage";
import assetVar from "../../shared/assets/asset_variables";
import golvar from "../../shared/Globalvariable";
import navbar from '../../shared/Components/navbar';
import removeAssetsType from "../../shared/assets/remove_Assets_type";
import assetListColumn from "../../shared/assets/asset_list_view_column";
import addAsset from "../../shared/assets/add_assets";
import deleteAsset from "../../shared/assets/delete_assets";
import assetSoftware from "../../shared/assets/assets_software";
import editAsset from "../../shared/assets/edit_assets";
import viewCount from "../../shared/assets/dashboard_asset_count";
import assetValidations from "../../shared/assets/assets_validation";
import assetTags from "../../shared/assets/assets_tags";
import applyAssetFilters from "../../shared/assets/asset_filter";
import relatedItemOperation from "../../shared/assets/related_items";
import assetHardware from "../../shared/assets/asset_hardware";
import assetIndexPageAction from "../../shared/assets/asset_index_page_action";
import { nanoid } from 'nanoid';
import assetStatus from "../../shared/assets/asset_statuses";

const golVari = new golvar();
const naviBar = new navbar();
const addCustType = new addAssetType();
const loginIntoApp = new signinpage();
const assetVari = new assetVar();
const listColumn = new assetListColumn();
const removeAssetType = new removeAssetsType();
const delAsset = new deleteAsset();
const addManagedAssets = new addAsset();
const editManagedAssets = new editAsset();
const softwareCrud = new assetSoftware();
const checkAstCount = new viewCount();
const checkAstValid = new assetValidations();
const asstTags = new assetTags();
const applyingFilter = new applyAssetFilters();
const itemsOperation = new relatedItemOperation();
const hardwareCrud = new assetHardware();
const indexPageOp = new assetIndexPageAction();
const statusOperation = new assetStatus();

const locName1 = golVari.locName3;
const locName2 = golVari.locName4;
const sfwName = 'Acumen Software';
const sfwType = 'Application';
const sfwProdNum = '6653321';
const sfwInstallDate = '2021-12-22';
const editSfwName = 'Titan Software';
const editSfwType = 'Operating System';
const editSfwProdNum = '443434343444';
const editSfwInstallDate = '2020-11-08';
const depreName = 'depr' + assetVari.deprName;
const assetName1 = `Com ${assetVari.assetsName1}`;
const assetName2 = `laptop ${assetVari.assetsName2}`;
const assetName3 = `Hp ${assetVari.assetsName3}`;
const assetName4 = `Asus ${assetVari.assetsName4}`;
const assetName5 = `Dell ${assetVari.assetsName5}`;
const assetName6 = `Apple ${assetVari.assetsName6}`;
const assetName7 = `Lenovo ${assetVari.assetsName7}`;
const assetName8 = `Switch ${assetVari.assetsName7}`;
const assetName9 = `Router ${assetVari.assetsName2}`;
const editAssetName = `Edit Assets${nanoid(2)}`;
const customAssetType = assetVari.assetType1;
const assetDepartments1 = assetVari.assetsDepartment1;
const assetDepartments2 = assetVari.assetsDepartment2;
const assetDepartments3 = 'Sales';
const astName3Mac = assetVari.assetsMac2;
const astName3Ip = assetVari.assetsIp2;
const astName3Serial = assetVari.assetsSerialNumber2;
const astName4Mac = assetVari.assetsMac3;
const astName4Ip = assetVari.assetsIp3;
const astName4Serial = assetVari.assetsSerialNumber3;
const astName5Mac = assetVari.assetsMac4;
const astName5Ip = assetVari.assetsIp4;
const astName5Serial = assetVari.assetsSerialNumber4;
const astName6Mac = assetVari.assetsMac5;
const astName6Ip = assetVari.assetsIp5;
const astName6Serial = assetVari.assetsSerialNumber5;
const assetName10 = `Assets A${nanoid(2)}`;
const assetName11 = `Assets B${nanoid(2)}`;
const assetName12 = `Assets C${nanoid(2)}`;
const assetBulkNames = `${assetName10};${assetName11};${assetName12}`;
const editAssetBulkNames = `Edit AssetA${nanoid(1)};Edit AssetB${nanoid(1)};Edit AssetC${nanoid(1)}`;
const assetSerialNumbers = `${assetVari.assetSerialNumber4};${assetVari.assetSerialNumber5};${assetVari.assetSerialNumber6}`;
const productNumbers = `${assetVari.assetsProductNum2};${assetVari.assetsProductNum1}`;
const ipAddress = '************';
const departments = `departments${nanoid(2)}`;
const assettags = `Asset Tags${nanoid(2)}`;
const customStatusName = `Asset Status ${assetVari.randomText}`;
const customStatusName1 = `On Use${assetVari.randomText}`;
const editStatusName = `Edit Status ${assetVari.randomText}`;

fixture('Regression test for assets module')

test('Assets regression ui testcases ', async t => {
  loginIntoApp.siginCompanySelect(golVari.emailaddress, golVari.password, golVari.companyname);
  await t.wait(1000);
  await t.hover(addManagedAssets.companyViewToggle)
  await t.wait(1000);
  await t.click(addManagedAssets.companyViewToggle)
  await t.wait(2000);
  addManagedAssets.navigateToAstShowPage();
  await t.wait(1000);
  checkAstValid.addAstWithoutData();
  await t.wait(1000);
  checkAstValid.AddAstWithinvalidIPMac();
  await t.wait(1000);
  addManagedAssets.addAssets(assetName1, assetVari.fireWall, assetVari.assetsTags1, assetVari.assetsNotes1, assetVari.assetsMac1, assetVari.assetsIp1, assetVari.assetsManufacturer1, assetVari.assetsModel1, assetVari.tags1, assetVari.assetsUuid1, assetVari.assetsUpTime1, assetVari.assetsCost1, assetVari.assetsSalvage1, assetVari.assetsDepreType1, assetVari.assetsUsedBy1, assetVari.assetsManagedBy1, locName1, assetDepartments1, assetVari.assetSerialNumber1, assetVari.assetsProductNum1, '2021-12-08', '2023-11-12', '2021-12-07', assetVari.assetKey1, assetVari.assetsDetials1);
  await t.wait(2000);
  checkAstValid.validateNotes();
  await t.wait(2000);
  hardwareCrud.addHardwareDetails(assetVari.fireWall, assetVari.processor, assetVari.memory1, assetVari.hardDrive6, assetVari.assetsSerialNumber1, assetVari.assetsProductNum1, assetVari.assetsIp1, assetVari.port, assetVari.assetsMac1, assetVari.connectionInterface1, assetVari.imei);
  await t.wait(1000);
  softwareCrud.addSoftware(sfwName, sfwType, sfwProdNum, sfwInstallDate);
  await t.wait(2000);
  asstTags.addTags(assetName1, assetVari.assetTags3);
  await t.wait(2000);
  asstTags.removeTags(assetName1, assetVari.assetTags3);
  await t.wait(2000);
  editManagedAssets.editAssets(assetName1, editAssetName, assetVari.editAssetsType1, assetVari.editAssetsTags1, assetVari.editAssetsNotes1, assetVari.editAssetsMac1, assetVari.editAssetsIp1, assetVari.editAssetsManufacturer1, assetVari.editAssetsModel1, assetVari.editTags1, assetVari.editAssetsUuid1, assetVari.editAssetsUpTime1, assetVari.editAssetsCost1, assetVari.editAssetsSalvage1, assetDepartments2, assetVari.editAssetsUsedBy1, assetVari.editAssetsManagedBy1, locName2, assetDepartments3, assetVari.editAssetSerialNumber1, assetVari.editAssetsProductNum1, '2021-12-08', '2023-11-12', '2021-12-07', assetVari.assetKey2, assetVari.editAssetsDetials1);
  await t.wait(2000);
  softwareCrud.editSoftware(sfwName, editSfwName, editSfwType, editSfwProdNum, editSfwInstallDate);
  await t.wait(2000);
  addManagedAssets.addAssets(assetName2, assetVari.appleDevice, assetVari.assetsTags1, assetVari.assetsNotes1, assetVari.assetsMac1, assetVari.assetsIp1, assetVari.assetsManufacturer1, assetVari.assetsModel1, assetVari.tags1, assetVari.assetsUuid1, assetVari.assetsUpTime1, assetVari.assetsCost1, assetVari.assetsSalvage1, assetVari.assetsDepreType1, assetVari.assetsUsedBy1, assetVari.assetsManagedBy1, locName1, assetDepartments1, assetVari.assetsSerialNumber1, assetVari.assetsProductNum1, '2021-12-08', '2023-11-12', '2021-12-07', assetVari.assetKey1, assetVari.assetsDetials1);
  await t.wait(2000);
  hardwareCrud.addHardwareDetails(assetVari.appleDevice, assetVari.processor, assetVari.memory1, assetVari.hardDrive6, assetVari.assetsSerialNumber1, assetVari.assetsProductNum1, assetVari.assetsIp1, assetVari.port, assetVari.assetsMac1, assetVari.connectionInterface1, assetVari.imei);
  await t.wait(1000);
  hardwareCrud.editHardwareDetails(assetName2, assetVari.appleDevice, assetVari.processor, assetVari.memory1, assetVari.hardDrive6, assetVari.assetsSerialNumber1, assetVari.assetsProductNum1, assetVari.assetsIp1, assetVari.port, assetVari.assetsMac1, assetVari.connectionInterface1, assetVari.imei);
  await t.wait(1000);
  addManagedAssets.addAssets(assetName3, assetVari.dongle, assetVari.assetsTags2, assetVari.assetsNotes1, astName3Mac, astName3Ip, assetVari.assetsManufacturer1, assetVari.assetsModel1, assetVari.tags1, assetVari.assetsUuid1, assetVari.assetsUpTime1, assetVari.assetsCost1, assetVari.assetsSalvage1, assetVari.assetsDepreType1, assetVari.assetsUsedBy1, assetVari.assetsManagedBy1, locName1, assetDepartments1, astName3Serial, assetVari.assetsProductNum2, '2021-12-08', '2023-11-12', '2021-12-07', assetVari.assetKey1, assetVari.assetsDetials1);
  await t.wait(2000);
  hardwareCrud.addHardwareDetails(assetVari.dongle, assetVari.processor2, assetVari.memory2, assetVari.hardDrive1, astName3Serial, assetVari.assetsProductNum2, astName3Ip, assetVari.port, astName3Mac, assetVari.connectionInterface3, assetVari.imei1);
  await t.wait(1000);
  addManagedAssets.addAssets(assetName4, assetVari.switch, assetVari.assetsTags3, assetVari.assetsNotes1, astName4Mac, astName4Ip, assetVari.assetsManufacturer1, assetVari.assetsModel1, assetVari.tags1, assetVari.assetsUuid1, assetVari.assetsUpTime1, assetVari.assetsCost1, assetVari.assetsSalvage1, assetVari.assetsDepreType1, assetVari.assetsUsedBy1, assetVari.assetsManagedBy1, locName1, assetDepartments1, astName4Serial, assetVari.assetsProductNum3, '2021-12-08', '2023-11-12', '2021-12-07', assetVari.assetKey1, assetVari.assetsDetials1);
  await t.wait(2000);
  hardwareCrud.addHardwareDetails(assetVari.switch, assetVari.processor3, assetVari.memory3, assetVari.hardDrive2, astName4Serial, assetVari.assetsProductNum3, astName4Ip, assetVari.assetsPort2, astName4Mac, assetVari.connectionInterface4, assetVari.imei2);
  await t.wait(1000);
  addManagedAssets.addAssets(assetName5, assetVari.iPhone, assetVari.assetsTags4, assetVari.assetsNotes1, astName5Mac, astName5Ip, assetVari.assetsManufacturer1, assetVari.assetsModel1, assetVari.tags1, assetVari.assetsUuid1, assetVari.assetsUpTime1, assetVari.assetsCost1, assetVari.assetsSalvage1, assetVari.assetsDepreType1, assetVari.assetsUsedBy1, assetVari.assetsManagedBy1, locName1, assetDepartments1, astName5Serial, assetVari.assetsProductNum4, '2021-12-08', '2023-11-12', '2021-12-07', assetVari.assetKey1, assetVari.assetsDetials1);
  await t.wait(2000);
  hardwareCrud.addHardwareDetails(assetVari.iPhone, assetVari.processor4, assetVari.memory4, assetVari.hardDrive3, astName5Serial, assetVari.assetsProductNum4, astName5Ip, assetVari.port, astName5Mac, assetVari.connectionInterface5, assetVari.imei3);
  await t.wait(1000);
  addManagedAssets.addAssets(assetName6, assetVari.phoneSystem, assetVari.assetsTags5, assetVari.assetsNotes1, astName6Mac, astName6Ip, assetVari.assetsManufacturer1, assetVari.assetsModel1, assetVari.tags1, assetVari.assetsUuid1, assetVari.assetsUpTime1, assetVari.assetsCost1, assetVari.assetsSalvage1, assetVari.assetsDepreType1, assetVari.assetsUsedBy1, assetVari.assetsManagedBy1, locName1, assetDepartments1, astName6Serial, assetVari.assetsProductNum5, '2021-12-08', '2023-11-12', '2021-12-07', assetVari.assetKey1, assetVari.assetsDetials1);
  await t.wait(2000);
  hardwareCrud.addHardwareDetails(assetVari.phoneSystem, assetVari.processor5, assetVari.memory5, assetVari.hardDrive4, astName6Serial, assetVari.assetsProductNum5, astName6Ip, assetVari.port, astName6Mac, assetVari.connectionInterface6, assetVari.imei4);
  await t.wait(1000);
  addManagedAssets.addAssets(assetName7, assetVari.printer, assetVari.assetsTags6, assetVari.assetsNotes1, assetVari.assetsMac6, assetVari.assetsIp6, assetVari.assetsManufacturer1, assetVari.assetsModel1, assetVari.tags1, assetVari.assetsUuid1, assetVari.assetsUpTime1, assetVari.assetsCost1, assetVari.assetsSalvage1, assetVari.assetsDepreType1, assetVari.assetsUsedBy1, assetVari.assetsManagedBy1, locName1, assetDepartments1, assetVari.assetsSerialNumber6, assetVari.assetsProductNum6, '2021-12-08', '2023-11-12', '2021-12-07', assetVari.assetKey1, assetVari.assetsDetials1);
  await t.wait(2000);
  hardwareCrud.addHardwareDetails(assetVari.printer, assetVari.processor6, assetVari.memory6, assetVari.hardDrive5, assetVari.assetsSerialNumber6, assetVari.assetsProductNum6, assetVari.assetsIp6, assetVari.port, assetVari.assetsMac6, assetVari.connectionInterface7, assetVari.imei5);
  await t.wait(1000);
  softwareCrud.softwareValidation(sfwName, sfwType, sfwProdNum, sfwInstallDate);
  await t.wait(2000);
  indexPageOp.archiveAsset(assetName7);
  await t.wait(1000);
  applyingFilter.filterAsset('Status', 'Active');
  await t.wait(2000);
  addManagedAssets.addAssetWithNameNType(assetName8, assetVari.assetsType5);
  await t.wait(1000);
  editManagedAssets.editAssetDetails(assetName8, assetVari.assetsUsedBy1, assetVari.assetsManagedBy1, locName1, assetDepartments1, assetVari.assetSerialNumber1, assetVari.assetsProductNum1, 'Ready to Use');
  await t.wait(1000);
  itemsOperation.addRelatedItems(locName2);
  await t.wait(1000);
  itemsOperation.removeRelatedItems(locName2);
  await t.wait(1000);
  indexPageOp.unarchiveAsset(assetName7);
  await t.wait(1000);
  naviBar.navigateToAssetModule();
  await t.wait(1000);
  indexPageOp.applyTags(assetName7, assetVari.tags, assetVari.tags1);
  await t.wait(1000);
  indexPageOp.removeTags(assetName7, assetVari.tags1);
  await t.wait(1000);
  checkAstCount.viewCounts(assetVari.appleDevice, '1');
  await t.wait(1000);
  checkAstCount.viewCounterDashboard('10', 'assets');
  await t.wait(1000);
  naviBar.navigateToAssetModule();
  await t.wait(1000);
  applyingFilter.filterAsset('Type', 'Apple Device');
  await t.wait(2000);
  applyingFilter.removeFilter('Apple Device');
  await t.wait(2000);
  applyingFilter.filterAsset('Department', assetDepartments1);
  await t.wait(2000);
  applyingFilter.removeFilter(assetDepartments1);
  await t.wait(2000);
  applyingFilter.filterAsset('Department', assetDepartments3);
  await t.wait(2000);
  applyingFilter.removeFilter(assetDepartments3);
  await t.wait(2000);
  applyingFilter.filterAsset('Source', 'Manually Added');
  await t.wait(2000);
  applyingFilter.removeFilter('Manually Added');
  await t.wait(2000);
  applyingFilter.filterAsset('Warranty', 'Expired');
  await t.wait(2000);
  applyingFilter.removeFilter('Expired');
  await t.wait(2000);
  applyingFilter.filterAsset('Location', locName1);
  await t.wait(2000);
  applyingFilter.removeFilter(locName1);
  await t.wait(2000);
  applyingFilter.filterAsset('Availability', 'Ready to Use');
  await t.wait(2000);
  applyingFilter.removeFilter('Ready to Use');
  await t.wait(2000);
  addCustType.addAssetTypes(customAssetType);
  await t.wait(1000);
  addManagedAssets.addAssets(assetName9, customAssetType, assetVari.assetsTags2, assetVari.assetsNotes2, assetVari.assetsMac2, assetVari.assetsIp2, assetVari.assetsManufacturer2, assetVari.assetsModel2, assetVari.tags2, assetVari.assetsUuid2, assetVari.assetsUpTime2, assetVari.assetsCost2, assetVari.assetsSalvage2, depreName, assetVari.assetsUsedBy2, assetVari.assetsManagedBy2, locName2, assetDepartments2, assetVari.assetSerialNumber7, assetVari.assetsProductNum2, '2021-12-08', '2023-11-12', '2021-12-07', assetVari.assetKey2, assetVari.assetsDetials2);
  await t.wait(2000);
  softwareCrud.addSoftware(sfwName, sfwType, sfwProdNum, sfwInstallDate);
  await t.wait(1000);
  listColumn.addViewAssetColumn()
  await t.wait(2000);
  listColumn.viewListAssetData(assetName9, customAssetType, 'Manually Added', assetVari.tags2, assetVari.assetsNotes2, locName2, assetDepartments2, assetVari.assetsModel2, assetVari.assetsMac2, '', assetVari.assetsManufacturer2, assetVari.assetsIp2, assetVari.assetsProductNum2, assetVari.assetSerialNumber7, 'OtherDetail', assetVari.assetsTags2, '', assetVari.assetsUsedBy2, assetVari.assetsManagedBy2, assetVari.assetsCost2);
  await t.wait(2000);
  listColumn.removeViewAllAssetColumn();
  await t.wait(2000);
  listColumn.checkDefaultListColumn();
  await t.wait(2000);
  indexPageOp.navigateToAstIndexPage();
  await t.wait(2000);
  indexPageOp.cancelMergeAssets(assetName4, assetName5);
  await t.wait(2000);
  indexPageOp.mergeAssets(assetName4, assetName5, astName4Mac, astName4Ip, astName4Serial);
  await t.wait(2000);
  indexPageOp.cancelUnmergeAssets(assetName4);
  await t.wait(2000);
  indexPageOp.unmergeAssets(assetName4, assetName5, astName4Mac, astName4Ip, astName4Serial, astName5Mac, astName5Ip, astName5Serial);
  await t.wait(2000);
  indexPageOp.archiveAsset(assetName3);
  await t.wait(2000);
  applyingFilter.filterAsset('Status', 'Active');
  await t.wait(2000);
  indexPageOp.mergeAssetsIndividualFields(assetName4, assetName5, astName5Mac, astName5Ip, astName5Serial);
  await t.wait(2000);
  removeAssetType.removeNChangeCustomTypes(customAssetType, assetName9, assetVari.mobile);
  await t.wait(2000);
  removeAssetType.removeDefaultTypes(assetVari.thinClient);
  await t.wait(2000);
  addCustType.addDefaultTypes(assetVari.thinClient);
  await t.wait(1000);
  addCustType.viewAllDefaultTypes();
  await t.wait(1000);
  addCustType.addAssetTypeOnlyName(assetVari.assetType2);
  await t.wait(1000);
  addCustType.editDefaultType(assetVari.fireWall);
  await t.wait(1000);
  addCustType.editDefaultImage('Apple Device');
  await t.wait(1000);
  statusOperation.naviToAssetStatus();
  await t.wait(1000);
  statusOperation.addAssetStatus(customStatusName, assetVari.exclamationIcon);
  await t.wait(1000);
  statusOperation.addAssetStatus(customStatusName1, assetVari.arrowIcon);
  await t.wait(1000);
  statusOperation.applyStatusInAsset(assetName2, customStatusName);
  await t.wait(1000);
  statusOperation.applyStatusInAsset(assetName6, 'In Stock');
  await t.wait(1000);
  statusOperation.applyStatusInAsset(editAssetName, 'Ready to Use');
  await t.wait(1000);
  statusOperation.naviToAssetStatus();
  await t.wait(1000);
  statusOperation.editAssetStatus(customStatusName, editStatusName, assetVari.arrowIcon);
  await t.wait(1000);
  statusOperation.viewStatusChangesInAsset(assetName2, editStatusName);
  await t.wait(1000);
  statusOperation.naviToAssetStatus();
  await t.wait(1000);
  statusOperation.removeAssetStatus(customStatusName1);
  await t.wait(1000);
  statusOperation.removeNChangeStatus(assetName2, 'Testings');
  await t.wait(1000);
  statusOperation.viewStatusChangesInAsset(assetName2, 'Unusable');
  await t.wait(1000);
  statusOperation.validateNameNType('Ready to Use', assetVari.arrowIcon);
  await t.wait(1000);
  statusOperation.assetStatusValidate();
  await t.wait(1000);
  statusOperation.checkDefaultNotEditable();
  await t.wait(1000);
  statusOperation.assetStatusValidate();
  await t.wait(1000);
  addManagedAssets.addAssetWithNameNType(assetName10, assetVari.server);
  await t.wait(1000);
  addManagedAssets.addAssetWithNameNType(assetName11, assetVari.tv);
  await t.wait(1000);
  addManagedAssets.addAssetWithNameNType(assetName12, assetVari.windows);
  await t.wait(1000);
  indexPageOp.bulkUpdateModelCheck(assetName10);
  await t.wait(1000);
  indexPageOp.bulkCancelUpdation(assetBulkNames, editAssetBulkNames, assetSerialNumbers,
    `${assetVari.server};${assetVari.tv};${assetVari.windows}`, assetVari.appleDevice);
  await t.wait(2000);
  indexPageOp.bulkUpdation(assetBulkNames, editAssetBulkNames, assetSerialNumbers, ipAddress, productNumbers, assettags, departments,
    'Huawei', `${golVari.fullName};Admin`, `${golVari.groupName3};${golVari.groupName2}`, assetVari.assetsNotes1, `${assetVari.appleDevice};${assetVari.wap}`);
  await t.wait(2000);
  delAsset.bulk3Deletion(editAssetBulkNames);
  await t.wait(1000);
  delAsset.bulkDeletion(assetName7);
  await t.wait(1000);
  checkAstCount.viewCounterDashboard('0', 'assets');
  await t.wait(1000);
  naviBar.signout();
  await t.wait(1000);
});
