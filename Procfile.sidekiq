worker_sidekiq_critical: bundle exec sidekiq -C config/sidekiq_critical.yml
worker_sidekiq_automated_tasks: bundle exec sidekiq -C config/sidekiq_automated_tasks.yml
worker_sidekiq_low_intensity: bundle exec sidekiq -C config/sidekiq_low_intensity.yml
worker_sidekiq_high_intensity: bundle exec sidekiq -C config/sidekiq_high_intensity.yml
worker_sidekiq_bulk_operations: bundle exec sidekiq -C config/sidekiq_bulk_operations.yml
worker_sidekiq_integrations: bundle exec sidekiq -C config/sidekiq_integrations.yml
worker_sidekiq_assets: bundle exec sidekiq -C config/sidekiq_assets.yml
worker_sidekiq_backup: bundle exec sidekiq -C config/sidekiq_backup.yml
worker_sidekiq_critical_logs: bundle exec sidekiq -C config/sidekiq_critical_logs.yml 
