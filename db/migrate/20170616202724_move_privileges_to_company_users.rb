class MovePrivilegesToCompanyUsers < ActiveRecord::Migration[5.0]
  def change
    add_reference :privileges, :company_user
    sql =<<~END
      INSERT INTO privileges (name, permission_type, company_user_id, created_at, updated_at)
        SELECT
          privileges.name,
          privileges.permission_type,
          company_users.id,
          privileges.created_at,
          privileges.updated_at
      FROM company_users LEFT OUTER JOIN privileges on privileges.user_id = company_users.user_id
        WHERE privileges.user_id is not null
    END
    execute sql
    remove_column :privileges, :user_id, :integer
  end
end
