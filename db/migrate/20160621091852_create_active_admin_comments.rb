class CreateActiveAdminComments < ActiveRecord::Migration[5.0]
  def self.up
    create_table :active_admin_comments do |t|
      t.string :namespace
      t.text   :body
      t.string :resource_id,   null: false
      t.string :resource_type, null: false
      t.references :author, polymorphic: true
      t.timestamps
    end
    add_index :active_admin_comments, [:namespace], name: 'active_admin_comments_for_namespace'
    add_index :active_admin_comments, [:author_type, :author_id], name: 'active_admin_comments_for_author'
    add_index :active_admin_comments, [:resource_type, :resource_id], name: 'active_admin_comments_for_resource'
  end

  def self.down
    remove_index :active_admin_comments, name: 'active_admin_comments_for_namespace'
    remove_index :active_admin_comments, name: 'active_admin_comments_for_author'
    remove_index :active_admin_comments, name: 'active_admin_comments_for_resource'

    drop_table :active_admin_comments
  end
end
