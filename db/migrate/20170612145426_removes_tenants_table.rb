class RemovesTenantsTable < ActiveRecord::Migration[5.0]
  def change
    remove_column :assets, :tenant_id, :integer
    remove_column :contracts, :tenant_id, :integer
    remove_column :contract_categories, :tenant_id, :integer
    remove_column :companies, :tenant_id, :integer
    remove_column :employees, :tenant_id, :integer
    remove_column :file_forms, :tenant_id, :integer
    remove_column :invoices, :tenant_id, :integer
    remove_column :ip_addresses, :tenant_id, :integer
    remove_column :locations, :tenant_id, :integer
    remove_column :phone_numbers, :tenant_id, :integer
    remove_column :telecom_services, :tenant_id, :integer
    remove_column :users, :tenant_id, :integer
    drop_table :user_tenants
    drop_table :tenants
  end
end
