class AddCompanyIdToPhoneNumbers < ActiveRecord::Migration[5.0]
  def change
    add_reference :phone_numbers, :company
    add_reference :ip_addresses, :company
    execute 'update phone_numbers set company_id = telecom_services.company_id from telecom_services where telecom_services.id = phone_numbers.telecom_service_id'
    execute 'update ip_addresses set company_id = telecom_services.company_id from telecom_services where telecom_services.id = ip_addresses.telecom_service_id'
  end
end
