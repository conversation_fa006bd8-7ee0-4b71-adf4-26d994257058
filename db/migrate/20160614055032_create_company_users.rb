class CreateCompanyUsers < ActiveRecord::Migration[5.0]
  def up
    create_table :company_users do |t|
      t.references :user, index: true, foreign_key: true
      t.references :company, index: true, foreign_key: true

      t.timestamps null: false
    end
    execute <<-SQL.squish
      alter table company_users
        add constraint company_users_unique unique (user_id, company_id);
    SQL
  end

  def drop
    drop_table :company_users,
               :company_users, [:company_id, :user_id], :unique => true
    execute <<-SQL.squish
      alter table company_users
        drop constraint if exists company_users_unique;
    SQL
  end
end
