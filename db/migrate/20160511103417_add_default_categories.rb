class AddDefaultCategories < ActiveRecord::Migration[5.0]
  class ContractCategory < ActiveRecord::Base
    enum category_type: [:Default, :Custom]
  end

  def change
    add_reference :contract_categories, :tenant, index: true, foreign_key: true
    add_column :contracts, :primary_contact, :integer

    # ContractCategory.where(category_type: ContractCategory.category_types[:Default]).destroy_all
    # ContractCategory.create({
    #   name: "IT",
    #   category_type: ContractCategory.category_types[:Default]
    # })
    #  ContractCategory.create({
    #   name: "Finance",
    #   category_type: ContractCategory.category_types[:Default]
    # })
    # ContractCategory.create({
    #   name: "Clients",
    #   category_type: ContractCategory.category_types[:Default]
    # })
    # ContractCategory.create({
    #   name: "Facility Services",
    #   category_type: ContractCategory.category_types[:Default]
    # })
    # ContractCategory.create({
    #   name: "HRM",
    #   category_type: ContractCategory.category_types[:Default]
    # })
    # ContractCategory.create({
    #   name: "Legal",
    #   category_type: ContractCategory.category_types[:Default]
    # })
    # ContractCategory.create({
    #   name: "Management",
    #   category_type: ContractCategory.category_types[:Default]
    # })
    # ContractCategory.create({
    #   name: "Marketing",
    #   category_type: ContractCategory.category_types[:Default]
    # })
    # ContractCategory.create({
    #   name: "Sales",
    #   category_type: ContractCategory.category_types[:Default]
    # })
  end
end
