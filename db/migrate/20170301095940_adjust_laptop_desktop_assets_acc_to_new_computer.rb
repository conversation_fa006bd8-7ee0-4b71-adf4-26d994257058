class AdjustLaptopDesktopAssetsAccToNewComputer < ActiveRecord::Migration[5.0]
  def change
    # assets = Asset.unscoped.joins(:asset_type).where("asset_types.name IN (?)", ["Laptop", "Desktop", "Server"])
    # assets.each do |asset|
    #   computer = asset.asset_attributes.find_by name: "Computer"
    #   if computer.present?
    #     at = computer.asset_attributes
    #     hashed = { hardware_details: {processor: "#{at["cpu_speed"]} #{at["cpu_core_count"]}", memory: at["memory"], hard_drive: at["disk_space"], mac_address: at["mac_address"]}, software_details: {operating_system: at["host_name"], product_key: "", install_date: ""} }
    #     computer.update(
    #       asset_attributes: hashed,
    #       nested_attributes: true
    #     )
    #   end
    # end
  end
end
