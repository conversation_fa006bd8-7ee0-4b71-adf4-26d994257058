class AddAttributesToAssets < ActiveRecord::Migration[5.0]
  def change
    add_column :assets, :brand, :string
    add_column :assets, :model, :string
    add_column :assets, :warrenty_expiry, :datetime
    add_column :assets, :acquisition_date, :datetime
    add_column :assets, :last_update, :datetime
    add_column :asset_attributes, :nested_attributes, :boolean, default: false
    add_column :asset_types, :nested_attributes, :boolean, default: false
  end
end
