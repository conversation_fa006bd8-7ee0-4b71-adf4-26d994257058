class AddTenantIdForScoping < ActiveRecord::Migration[5.0]
  def change
    add_reference :users,        :tenant, index: true, foreign_key: true
    add_reference :companies,     :tenant, index: true, foreign_key: true
    add_reference :contracts,   :tenant, index: true, foreign_key: true
    add_reference :employees,   :tenant, index: true, foreign_key: true
    add_reference :assets,      :tenant, index: true, foreign_key: true
    add_reference :file_forms,  :tenant, index: true, foreign_key: true

  end
end
