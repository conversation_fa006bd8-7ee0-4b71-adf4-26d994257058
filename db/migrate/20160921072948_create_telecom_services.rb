class CreateTelecomServices < ActiveRecord::Migration[5.0]
  def change
    create_table :telecom_services do |t|
      t.string :provider
      t.string :service_type
      t.string :account_number
      t.string :term
      t.float :mrc
      t.references :company, index: true, foreign_key: true
      t.references :contract, index: true, foreign_key: true
      t.references :location, index: true, foreign_key: true
      t.references :tenant,   index: true, foreign_key: true
      t.integer :service_type

      t.timestamps null: false
    end
  end
end
