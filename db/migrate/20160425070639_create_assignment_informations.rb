class CreateAssignmentInformations < ActiveRecord::Migration[5.0]
  def change
    create_table :assignment_informations do |t|
      t.references :location, index: true, foreign_key: true
      t.string :managed_by
      t.string :department
      t.string :used_by
      t.references :asset, index: true, foreign_key: true
      t.datetime :assigned_on
      t.string :attached_file

      t.timestamps null: false
    end
  end
end
