# Because some vendors are their own product (e.g. AWS), prioritize product images first.
# If an image exists in the products hash (above) no need to rewrite it here.
vendor_images = {
  "DigitalOcean" => "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/digitalocean.png",
  "Google Cloud Platform" => "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/products/google-cloud.png",
  "Azure" => "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/products/azure.png",
  "Adobe" => "https://nulodgic-static-assets.s3.amazonaws.com/images/vendor_logos/Adobe.jpg",
  "Backhub" => "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/backhub.png",
  "Base CRM" => "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/basecrm.png",
  "Bluehost" => "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/BlueHost.png",
  "CloudApp" => "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/CloudApp.png",
  "Clubhouse" => "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/Clubhouse.jpg",
  "Highrise" => "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/highrise.png",
  "Hiver" => "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/hiver.svg",
  "Papertrail" => "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/papertrail.png",
  "Postman" => "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/postman.png",
  "Robin" => "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/robin.jpg",
  "Symphony" => "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/symphony.png",
  "Timely" => "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/timely.png",
  "Vonage" => "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/vonage.png",
  "Xfinity" => "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/xfinity.png"
}

vendor_images.keys.each do |name|
  default_vendor = DefaultVendor.find_by(name: name)
  raise "Missing default vendor #{name}" unless default_vendor
  default_vendor.logo_url = vendor_images[name]
  default_vendor.save!
end
