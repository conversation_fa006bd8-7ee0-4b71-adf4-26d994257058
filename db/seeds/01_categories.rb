category_names = [
  'Advertising',
  'Analytics',
  'Cloud',
  'Customer Support',
  'Developer Tools',
  'DevOps',
  'Facilities',
  'Finance and Accounting',
  'General',
  'Infrastructure',
  'IT and Security',
  'HR',
  'Marketing',
  'Other',
  'Product and Design',
  'Productivity',
  'Sales and Business Development',
  'Telecom'
]

categories = {}
category_names.each do |category_name|
  cat = DefaultCategory.find_or_create_by({ name: category_name })
  name = cat.name.gsub(" and ", " And ").remove(' ').underscore
  categories[name.to_sym] = cat
end

cat = DefaultCategory.find_or_initialize_by({ name: "Uncategorized" })
cat.id = 0
cat.save!
name = cat.name.gsub(" and ", " And ").remove(' ').underscore
categories[name.to_sym] = cat
