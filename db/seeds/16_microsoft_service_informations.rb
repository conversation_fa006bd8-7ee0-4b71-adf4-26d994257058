start_time = Time.now
service_informations = [
  { :friendly_name=>"App Connect", :guid=>"0bfc98ed-1dbc-4a97-b246-701754e48b17", :string_id=>"SPZA" },
  { :friendly_name=>"Exchange Foundation", :guid=>"113feb6c-3fe4-4440-bddc-54d774bf0318", :string_id=>"EXCHANGE_S_FOUNDATION" },
  { :friendly_name=>"Audio Conferencing", :guid=>"3e26ee1f-8a5f-4d52-aee2-b81ce45c8f40", :string_id=>"MCOMEETADV" },
  { :friendly_name=>"Microsoft Azure Active Directory Basic", :guid=>"c4da7f8a-5ee2-4c99-a7e1-87d2df57f6fe", :string_id=>"AAD_BASIC" },
  { :friendly_name=>"Azure Active Directory Premium P1", :guid=>"41781fb2-bc02-4b7c-bd55-b576c07bb09d", :string_id=>"AAD_PREMIUM" },
  { :friendly_name=>"Microsoft Azure Multi Factor Authentication", :guid=>"8a256a2b-b617-496d-b51b-e76466e88db0", :string_id=>"MFA_PREMIUM" },
  { :friendly_name=>"Cloud App Security Discovery", :guid=>"932ad362-64a8-4783-9106-97849a1a30b9", :string_id=>"ADALLOM_S_DISCOVERY" },
  { :friendly_name=>"Azure Active Directory Premium P2", :guid=>"eec0eb4f-6444-4f95-aba0-50c24d67f998", :string_id=>"AAD_PREMIUM_P2" },
  { :friendly_name=>"Azure Information Protection Premium P1", :guid=>"6c57d4b6-3b23-47a5-9bc9-69f17b4947b3", :string_id=>"RMS_S_PREMIUM" },
  { :friendly_name=>"Microsoft Azure Active Directory Rights", :guid=>"bea4c11e-220a-4e6d-8eb8-8ea15d019f90", :string_id=>"RMS_S_ENTERPRISE" },
  { :friendly_name=>"Microsoft Social Engagement   Service Discontinuation", :guid=>"03acaee3-9492-4f40-aed4-bcb6b32981b6", :string_id=>"NBENTERPRISE" },
  { :friendly_name=>"Powerapps For Dynamics 365", :guid=>"0b03f40b-c404-40c3-8651-2aceb74365fa", :string_id=>"POWERAPPS_DYN_P2" },
  { :friendly_name=>"Sharepoint Online (Plan 2)", :guid=>"5dbe027f-**************-606e4d348a72", :string_id=>"SHAREPOINTENTERPRISE" },
  { :friendly_name=>"Flow For Dynamics 365", :guid=>"b650d915-9886-424b-a08d-633cede56f57", :string_id=>"FLOW_DYN_P2" },
  { :friendly_name=>"Dynamics 365 Customer Engagement Plan", :guid=>"d56f3deb-50d8-465a-bedb-f079817ccac1", :string_id=>"DYN365_ENTERPRISE_P1" },
  { :friendly_name=>"Office Online", :guid=>"e95bec33-7c88-4a70-8e19-b10bd9d0c014", :string_id=>"SHAREPOINTWAC" },
  { :friendly_name=>"Project Online Desktop Client", :guid=>"fafd7243-e5c1-4a3a-9e40-495efcb1d3c3", :string_id=>"PROJECT_CLIENT_SUBSCRIPTION" },
  { :friendly_name=>"Project Online Service", :guid=>"fe71d6c3-a2ea-4499-9778-da042bf08063", :string_id=>"SHAREPOINT_PROJECT" },
  { :friendly_name=>"Project Online Essentials", :guid=>"1259157c-8581-4875-bca7-2ffb18c51bda", :string_id=>"PROJECT_ESSENTIALS" },
  { :friendly_name=>"Flow For Dynamics 365", :guid=>"7e6d7d78-73de-46ba-83b1-6d25117334ba", :string_id=>"FLOW_DYN_APPS" },
  { :friendly_name=>"Powerapps For Dynamics 365", :guid=>"874fc546-6efe-4d22-90b8-5c4e7aa59f4b", :string_id=>"POWERAPPS_DYN_APPS" },
  { :friendly_name=>"Dynamics 365 For Customer Service", :guid=>"99340b49-fb81-4b1e-976b-8f2ae8e9394f", :string_id=>"DYN365_ENTERPRISE_CUSTOMER_SERVICE" },
  { :friendly_name=>"Dynamics 365 For Financials", :guid=>"920656a2-7dd8-4c83-97b6-a356414dbd36", :string_id=>"DYN365_FINANCIALS_BUSINESS" },
  { :friendly_name=>"Dynamics 365 For Sales", :guid=>"2da8e897-7791-486b-b08f-cc63c8129df7", :string_id=>"DYN365_ENTERPRISE_SALES" },
  { :friendly_name=>"Dynamics 365 For Talent   Attract Experience Team Member", :guid=>"643d201a-9884-45be-962a-06ba97062e5e", :string_id=>"DYN365_Enterprise_Talent_Attract_TeamMember" },
  { :friendly_name=>"Dynamics 365 For Talent   Onboard Experience", :guid=>"f2f49eef-4b3f-4853-809a-a055c6103fe0", :string_id=>"DYN365_Enterprise_Talent_Onboard_TeamMember" },
  { :friendly_name=>"Dynamics 365 For Team Members", :guid=>"6a54b05e-4fab-40e7-9828-428db3b336fa", :string_id=>"DYN365_ENTERPRISE_TEAM_MEMBERS" },
  { :friendly_name=>"Dynamics 365 For Operations Team Members", :guid=>"f5aa7b45-8a36-4cd1-bc37-5d06dea98645", :string_id=>"Dynamics_365_for_Operations_Team_members" },
  { :friendly_name=>"Dynamics 365 For Retail Team Members", :guid=>"c0454a3d-32b5-4740-b090-78c32f48f0ad", :string_id=>"Dynamics_365_for_Retail_Team_members" },
  { :friendly_name=>"Dynamics 365 For Talent Team Members", :guid=>"d5156635-0704-4f66-8803-93258f8b2678", :string_id=>"Dynamics_365_for_Talent_Team_members" },
  { :friendly_name=>"Flow For Dynamics 365", :guid=>"1ec58c70-f69c-486a-8109-4b87ce86e449", :string_id=>"FLOW_DYN_TEAM" },
  { :friendly_name=>"Powerapps For Dynamics 365", :guid=>"52e619e2-2730-439a-b0d3-d09ab7e8b705", :string_id=>"POWERAPPS_DYN_TEAM" },
  { :friendly_name=>"Common Data Service", :guid=>"d1142cfd-872e-4e77-b6ff-d98ec5a51f66", :string_id=>"DDYN365_CDS_DYN_P2" },
  { :friendly_name=>"Dynamics 365 For Talent", :guid=>"65a1ebf4-6732-4f00-9dcb-3d115ffdeecd", :string_id=>"DYN365_TALENT_ENTERPRISE" },
  { :friendly_name=>"Dynamics 365 For Operations", :guid=>"95d2cd7b-1007-484b-8595-5e97e63fe189", :string_id=>"Dynamics_365_for_Operations" },
  { :friendly_name=>"Dynamics 365 For Retail", :guid=>"a9e39199-8369-444b-89c1-5fe65ec45665", :string_id=>"Dynamics_365_for_Retail" },
  { :friendly_name=>"Dynamics 365 Hiring Free Plan", :guid=>"f815ac79-c5dd-4bcc-9b78-d97f7b817d0d", :string_id=>"Dynamics_365_Hiring_Free_PLAN" },
  { :friendly_name=>"Dynamics 365 For Talent: Onboard", :guid=>"300b8114-8555-4313-b861-0c115d820f50", :string_id=>"Dynamics_365_Onboarding_Free_PLAN" },
  { :friendly_name=>"Microsoft Intune", :guid=>"c1ec4a95-1f05-45b3-a911-aa3fa01094f5", :string_id=>"INTUNE_A" },
  { :friendly_name=>"Microsoft Cloud App Security", :guid=>"2e2ddb96-6af9-4b1d-a3f0-d6ecfd22edb2", :string_id=>"ADALLOM_S_STANDALONE" },
  { :friendly_name=>"Azure Advanced Threat Protection", :guid=>"14ab5db5-e6c4-4b20-b4bc-13e36fd2227f", :string_id=>"ATA" },
  { :friendly_name=>"Azure Information Protection Premium P2", :guid=>"5689bec4-755d-4753-8b61-40975025187c", :string_id=>"RMS_S_PREMIUM2" },
  { :friendly_name=>"Exchange Online (Plan 1)", :guid=>"9aaf7827-d63c-4b61-89c3-182f06f82e5c", :string_id=>"EXCHANGE_S_STANDARD" },
  { :friendly_name=>"Exchange Online (Plan 2)", :guid=>"efb87545-963c-4e0d-99df-69c6916d9eb0", :string_id=>"EXCHANGE_S_ENTERPRISE" },
  { :friendly_name=>"Exchange Online Archiving For Exchange Online", :guid=>"176a09a6-7ec5-4039-ac02-b2791c6ba793", :string_id=>"EXCHANGE_S_ARCHIVE_ADDON" },
  { :friendly_name=>"Exchange Online Archiving For Exchange Server", :guid=>"da040e0a-b393-4bea-bb76-928b3fa1cf5a", :string_id=>"EXCHANGE_S_ARCHIVE" },
  { :friendly_name=>"Exchange S Essentials", :guid=>"1126bef5-da20-4f07-b45e-ad25d2581aa8", :string_id=>"EXCHANGE_S_ESSENTIALS" },
  { :friendly_name=>"Exchange Online Kiosk", :guid=>"4a82b400-a79f-41a4-b4e2-e94f5787b113", :string_id=>"EXCHANGE_S_DESKLESS" },
  { :friendly_name=>"Exchange Online Pop", :guid=>"90927877-dcff-4af6-b346-2332c0b15bb7", :string_id=>"EXCHANGE_B_STANDARD" },
  { :friendly_name=>"Azure Active Directory For Education", :guid=>"3a3976ce-de18-4a87-a78e-5e9245e252df", :string_id=>"AAD_EDU" },
  { :friendly_name=>"Intune For Education", :guid=>"da24caf9-af8e-485c-b7c8-e73336da2693", :string_id=>"INTUNE_EDU" },
  { :friendly_name=>"Windows Store Service", :guid=>"a420f25f-a7b3-4ff5-a9d0-5d58f73b537d", :string_id=>"WINDOWS_STORE" },
  { :friendly_name=>"Azure Active Directory Basic For Edu", :guid=>"1d0f309f-fdf9-4b2a-9ae7-9c48b91f1426", :string_id=>"AAD_BASIC_EDU" },
  { :friendly_name=>"Education Analytics", :guid=>"a9b86446-fa4e-498f-a92a-41b447e03337", :string_id=>"EducationAnalyticsP1" },
  { :friendly_name=>"Flow For Office 365", :guid=>"76846ad7-7776-4c40-a281-a386362dd1b9", :string_id=>"FLOW_O365_P2" },
  { :friendly_name=>"Information Protection For Office 365   Standard", :guid=>"5136a095-5cf0-4aff-bec3-e84448b38ea5", :string_id=>"MIP_S_CLP1" },
  { :friendly_name=>"Insights By My Analytics", :guid=>"33c4f319-9bdd-48d6-9c4d-410b750a4a5a", :string_id=>"MYANALYTICS_P2" },
  { :friendly_name=>"Microsoft Bookings", :guid=>"199a5c09-e0ca-4e37-8f7c-b05d533e1ea2", :string_id=>"MICROSOFTBOOKINGS" },
  { :friendly_name=>"Microsoft Forms (Plan 2)", :guid=>"9b5de886-f035-4ff2-b3d8-c9127bea3620", :string_id=>"OFFICE_FORMS_PLAN_2" },
  { :friendly_name=>"Microsoft Kaizala Pro Plan 3", :guid=>"aebd3021-9f8f-4bf8-bbe3-0ed2f4f047a1", :string_id=>"KAIZALA_O365_P3" },
  { :friendly_name=>"Microsoft Planner", :guid=>"b737dad2-2f6c-4c65-90e3-ca563267e8b9", :string_id=>"PROJECTWORKMANAGEMENT" },
  { :friendly_name=>"Microsoft Search", :guid=>"94065c59-bc8e-4e8b-89e5-5138d471eaff", :string_id=>"MICROSOFT_SEARCH" },
  { :friendly_name=>"Microsoft Staff Hub", :guid=>"8c7d2df8-86f0-4902-b2ed-a0458298f3b3", :string_id=>"Deskless" },
  { :friendly_name=>"Microsoft Stream For O365 E3 Sku", :guid=>"9e700747-8b1d-45e5-ab8d-ef187ceec156", :string_id=>"STREAM_O365_E3" },
  { :friendly_name=>"Microsoft Teams", :guid=>"57ff2da0-773e-42df-b2af-ffb7a2317929", :string_id=>"TEAMS1" },
  { :friendly_name=>"Minecraft Education Edition", :guid=>"4c246bbc-f513-4311-beff-eba54c353256", :string_id=>"MINECRAFT_EDUCATION_EDITION" },
  { :friendly_name=>"Mobile Device Management For Office 365", :guid=>"882e1d05-acd1-4ccb-8708-6ee03664b117", :string_id=>"INTUNE_O365" },
  { :friendly_name=>"Office 365 Advanced Security Management", :guid=>"8c098270-9dd4-4350-9b30-ba4703f3b36b", :string_id=>"ADALLOM_S_O365" },
  { :friendly_name=>"Office 365 Pro Plus", :guid=>"43de0ff5-c92c-492b-9116-175376d08c38", :string_id=>"OFFICESUBSCRIPTION" },
  { :friendly_name=>"Office For The Web (Education)", :guid=>"e03c7e47-402c-463c-ab25-949079bedb21", :string_id=>"SHAREPOINTWAC_EDU" },
  { :friendly_name=>"Power Apps For Office 365", :guid=>"c68f8d98-5534-41c8-bf36-22fa496fa792", :string_id=>"POWERAPPS_O365_P2" },
  { :friendly_name=>"School Data Sync (Plan 2)", :guid=>"500b6a2a-7a50-4f40-b5f9-160e5b8c2f48", :string_id=>"SCHOOL_DATA_SYNC_P2" },
  { :friendly_name=>"Share Point Plan 2 For Edu", :guid=>"63038b2c-28d0-45f6-bc36-33062963b498", :string_id=>"SHAREPOINTENTERPRISE_EDU" },
  { :friendly_name=>"Skype For Business Online (Plan 2)", :guid=>"0feaeb32-d00e-4d66-bd5a-43b5b83db82c", :string_id=>"MCOSTANDARD" },
  { :friendly_name=>"Sway", :guid=>"a23b959c-7ce8-4e57-9140-b90eb88a9e97", :string_id=>"SWAY" },
  { :friendly_name=>"To Do (Plan 2)", :guid=>"c87f142c-d1e9-4363-8630-aaea9c4d9ae5", :string_id=>"BPOS_S_TODO_2" },
  { :friendly_name=>"Whiteboard (Plan 2)", :guid=>"94a54592-cd8b-425e-87c6-97868b000b91", :string_id=>"WHITEBOARD_PLAN2" },
  { :friendly_name=>"Windows 10 Enterprise (New)", :guid=>"e7c91390-7625-45be-94e0-e16907e03118", :string_id=>"Virtualization Rights for Windows 10 (E3/E5+VDA)" },
  { :friendly_name=>"Yammer For Academic", :guid=>"2078e8df-cff6-4290-98cb-5408261a760a", :string_id=>"YAMMER_EDU" },
  { :friendly_name=>"Customer Lockbox", :guid=>"9f431833-0334-42de-a7dc-70aa40db46db", :string_id=>"LOCKBOX_ENTERPRISE" },
  { :friendly_name=>"Flow For Office 365", :guid=>"07699545-9485-468e-95b6-2fca3738be01", :string_id=>"FLOW_O365_P3" },
  { :friendly_name=>"Information Barriers", :guid=>"c4801e8a-cb58-4c35-aca6-f2dcc106f287", :string_id=>"INFORMATION_BARRIERS" },
  { :friendly_name=>"Information Protection For Office 365   Premium", :guid=>"efb0351d-3b08-4503-993d-383af8de41e3", :string_id=>"MIP_S_CLP2" },
  { :friendly_name=>"Microsoft 365 Advanced Auditing", :guid=>"2f442157-a11c-46b9-ae5b-6e39ff4e5849", :string_id=>"M365_ADVANCED_AUDITING" },
  { :friendly_name=>"Microsoft 365 Phone System", :guid=>"4828c8ec-dc2e-4779-b502-87ac9ce28ab7", :string_id=>"MCOEV" },
  { :friendly_name=>"Microsoft Defender Advanced Threat Protection", :guid=>"871d91ec-ec1a-452b-a83f-bd76c7d770ef", :string_id=>"WINDEFATP" },
  { :friendly_name=>"Microsoft Forms (Plan 3)", :guid=>"96c1e14a-ef43-418d-b115-9636cdaa8eed", :string_id=>"OFFICE_FORMS_PLAN_3" },
  { :friendly_name=>"Microsoft Kaizala", :guid=>"0898bdbb-73b0-471a-81e5-20f1fe4dd66e", :string_id=>"KAIZALA_STANDALONE" },
  { :friendly_name=>"Microsoft My Analytics (Full)", :guid=>"34c0d7a0-a70f-4668-9238-47f9fc208882", :string_id=>"EXCHANGE_ANALYTICS" },
  { :friendly_name=>"Microsoft Stream For O365 E5 Sku", :guid=>"6c6042f5-6f01-4d67-b8c1-eb99d36eed3e", :string_id=>"STREAM_O365_E5" },
  { :friendly_name=>"Office 365 Advanced E Discovery", :guid=>"4de31727-a228-4ec3-a5bf-8e45b5ca48cc", :string_id=>"EQUIVIO_ANALYTICS" },
  { :friendly_name=>"Office 365 Advanced Threat Protection (Plan 1)", :guid=>"f20fedf3-f3c3-43c3-8267-2bfdd51c0939", :string_id=>"ATP_ENTERPRISE" },
  { :friendly_name=>"Office 365 Advanced Threat Protection (Plan 2)", :guid=>"8e0c0a52-6a6c-4d40-8370-dd62790dcd70", :string_id=>"THREAT_INTELLIGENCE" },
  { :friendly_name=>"Office 365 Privileged Access Management", :guid=>"b1188c4c-1b36-4018-b48b-ee07604f6feb", :string_id=>"PAM_ENTERPRISE" },
  { :friendly_name=>"Office 365 Safe Docs", :guid=>"bf6f5520-59e3-4f82-974b-7dbbc4fd27c7", :string_id=>"SAFEDOCS" },
  { :friendly_name=>"Power Bi Pro", :guid=>"70d33638-9c74-4d01-bfd3-562de28bd4ba", :string_id=>"BI_AZURE_P2" },
  { :friendly_name=>"Power Apps For Office 365 Plan 3", :guid=>"9c0dab89-a30c-4117-86e7-97bda240acd2", :string_id=>"POWERAPPS_O365_P3" },
  { :friendly_name=>"Premium Encryption In Office 365", :guid=>"617b097b-4b93-4ede-83de-5f075bb5fb2f", :string_id=>"PREMIUM_ENCRYPTION" },
  { :friendly_name=>"To Do (Plan 3)", :guid=>"3fb82609-8c27-4f7b-bd51-30634711ee67", :string_id=>"BPOS_S_TODO_3" },
  { :friendly_name=>"Whiteboard (Plan 3)", :guid=>"4a51bca5-1eff-43f5-878c-177680f191af", :string_id=>"WHITEBOARD_PLAN3" },
  { :friendly_name=>"Microsoft Forms (Plan E1)", :guid=>"159f4cd6-e380-449f-a816-af1a9ef76344", :string_id=>"FORMS_PLAN_E1" },
  { :friendly_name=>"Office 365 Business", :guid=>"094e7854-93fc-4d55-b2c0-3ab5369ebdc1", :string_id=>"OFFICE_BUSINESS" },
  { :friendly_name=>"Onedrivestandard", :guid=>"13696edf-5a08-49f6-8134-03083ed8ba30", :string_id=>"ONEDRIVESTANDARD" },
  { :friendly_name=>"Bpos S Todo 1", :guid=>"5e62787c-c316-451f-b873-1d05acd4d12c", :string_id=>"BPOS_S_TODO_1" },
  { :friendly_name=>"Flow For Office 365", :guid=>"0f9b09cb-62d1-4ff4-9129-43f4996f83f4", :string_id=>"FLOW_O365_P1" },
  { :friendly_name=>"Officemobile Subscription", :guid=>"c63d4d19-e8cb-460e-b37c-4d6c34603745", :string_id=>"OFFICEMOBILE_SUBSCRIPTION" },
  { :friendly_name=>"Powerapps For Office 365", :guid=>"92f7a6f3-b89b-4bbd-8c30-809e6da5ad1c", :string_id=>"POWERAPPS_O365_P1" },
  { :friendly_name=>"Sharepointstandard", :guid=>"c7699d2e-19aa-44de-8edf-1736da088ca1", :string_id=>"SHAREPOINTSTANDARD" },
  { :friendly_name=>"Yammer Enterprise", :guid=>"7547a3fe-08ee-4ccb-b430-5077c5041653", :string_id=>"YAMMER_ENTERPRISE" },
  { :friendly_name=>"Yammer Midsize", :guid=>"41bf139a-4e60-409f-9346-a1361efc6dfb", :string_id=>"YAMMER_MIDSIZE" },
  { :friendly_name=>"Outlook Customer Manager", :guid=>"5bfe124c-bbdc-4494-8835-f1297d457d79", :string_id=>"O365_SB_Relationship_Management" },
  { :friendly_name=>"Azure Active Directory", :guid=>"de377cbc-0019-4ec2-b77c-3f223947e102", :string_id=>"AAD_SMB" },
  { :friendly_name=>"Intune Smbiz", :guid=>"8e9ff0ff-aa7a-4b20-83c1-2f636b600ac2", :string_id=>"INTUNE_SMBIZ" },
  { :friendly_name=>"Microsoft Stream For O365 E1 Sku", :guid=>"743dd19e-1ce3-4c62-a3ad-49ba8f63a2f6", :string_id=>"STREAM_O365_E1" },
  { :friendly_name=>"Windows 10 Business", :guid=>"8e229017-d77b-43d5-9305-903395523b99", :string_id=>"WINBIZ" },
  { :friendly_name=>"Microsoft Forms (Plan E3)", :guid=>"2789c901-c14e-48ab-a76a-be334d9d793a", :string_id=>"FORMS_PLAN_E3" },
  { :friendly_name=>"Windows 10 Enterprise", :guid=>"21b439ba-a0ca-424f-a6cc-52f954a5b111", :string_id=>"WIN10_PRO_ENT_SUB" },
  { :friendly_name=>"Microsoft Forms (Plan E5)", :guid=>"e212cbc7-0961-4c40-9825-01117710dcb1", :string_id=>"FORMS_PLAN_E5" },
  { :friendly_name=>"Microsoft Teams For Dod (Ar)", :guid=>"fd500458-c24c-478e-856c-a6067a8376cd", :string_id=>"TEAMS_AR_DOD" },
  { :friendly_name=>"Microsoft Teams For Gcc High (Ar)", :guid=>"9953b155-8aef-4c56-92f3-72b0487fce41", :string_id=>"TEAMS_AR_GCCHIGH" },
  { :friendly_name=>"Azure Rights Management", :guid=>"6a76346d-5d6e-4051-9fe3-ed3f312b5597", :string_id=>"RMS_S_ENTERPRISE_GOV" },
  { :friendly_name=>"Microsoft Stream For O365 K Sku", :guid=>"3ffba0d2-38e5-4d5e-8ec0-98f2b05c09d9", :string_id=>"STREAM_O365_K" },
  { :friendly_name=>"Share Point Online Kiosk", :guid=>"902b47e5-dcb2-4fdc-858b-c63a90a2bdb9", :string_id=>"SHAREPOINTDESKLESS" },
  { :friendly_name=>"Skype For Business Online (Plan 1)", :guid=>"afc06cb0-b4f4-4473-8286-d644f70d8faf", :string_id=>"MCOIMP" },
  { :friendly_name=>"Flow For Office 365 K1", :guid=>"bd91b1a4-9f94-4ecf-b45b-3a65e5c8128a", :string_id=>"FLOW_O365_S1" },
  { :friendly_name=>"Microsoft Forms (Plan F1)", :guid=>"f07046bd-2a3c-4b96-b0be-dea79d7cbfb8", :string_id=>"FORMS_PLAN_K" },
  { :friendly_name=>"Microsoft Kaizala Pro Plan 1", :guid=>"73b2a583-6a59-42e3-8e83-54db46bc3278", :string_id=>"KAIZALA_O365_P1" },
  { :friendly_name=>"Power Apps For Office 365 K1", :guid=>"e0287f9f-e222-4f98-9a83-f379e249159a", :string_id=>"POWERAPPS_O365_S1" },
  { :friendly_name=>"To Do (Firstline)", :guid=>"80873e7a-cd2a-4e67-b061-1b5381a676a5", :string_id=>"BPOS_S_TODO_FIRSTLINE" },
  { :friendly_name=>"Whiteboard (Firstline)", :guid=>"36b29273-c6d0-477a-aca6-6fbe24f538e3", :string_id=>"WHITEBOARD_FIRSTLINE1" },
  { :friendly_name=>"Windows 10 Enterprise E3 (Local Only)", :guid=>"e041597c-9c7f-4ed9-99b0-2663301576f7", :string_id=>"WIN10_ENT_LOC_F1" },
  { :friendly_name=>"Microsoft Dynamics Crm Online Basic", :guid=>"bf36ca64-95c6-4918-9275-eb9f4ce2c04f", :string_id=>"CRMPLAN2" },
  { :friendly_name=>"Microsoft Dynamics Crm Online Professional", :guid=>"f9646fb2-e3b2-4309-95de-dc4833737456", :string_id=>"CRMSTANDARD" },
  { :friendly_name=>"Microsoft Dynamics Marketing Sales Collaboration   Eligibility Criteria Apply", :guid=>"3413916e-ee66-4071-be30-6f94d4adfeda", :string_id=>"MDM_SALES_COLLABORATION" },
  { :friendly_name=>"Microsoft Social Engagement Professional   Eligibility Criteria Apply", :guid=>"3e58e97c-9abe-ebab-cd5f-d543d1529634", :string_id=>"NBPROFESSIONALFORCRM" },
  { :friendly_name=>"Ms Imagine Academy", :guid=>"d736def0-1fde-43f0-a5be-e3f8b2de6e41", :string_id=>"IT_ACADEMY_AD" },
  { :friendly_name=>"Microsoft Communications Compliance", :guid=>"41fcdd7d-4733-4863-9cf4-c65b83ce2df4", :string_id=>"COMMUNICATIONS_COMPLIANCE" },
  { :friendly_name=>"Microsoft Communications Dlp", :guid=>"6dc145d6-95dd-4191-b9c3-185575ee6f6b", :string_id=>"COMMUNICATIONS_DLP" },
  { :friendly_name=>"Microsoft Customer Key", :guid=>"6db1f1db-2b46-403f-be40-e39395f08dbb", :string_id=>"CUSTOMER_KEY" },
  { :friendly_name=>"Microsoft Data Investigations", :guid=>"46129a58-a698-46f0-aa5b-17f6586297d9", :string_id=>"DATA_INVESTIGATIONS" },
  { :friendly_name=>"Microsoft Information Governance", :guid=>"e26c2fcc-ab91-4a61-b35c-03cdc8dddf66", :string_id=>"INFO_GOVERNANCE" },
  { :friendly_name=>"Microsoft Records Management", :guid=>"65cc641f-cccd-4643-97e0-a17e3045e541", :string_id=>"RECORDS_MANAGEMENT" },
  { :friendly_name=>"Sharepoint S Developer", :guid=>"a361d6e2-509e-4e25-a8ad-950060064ef4", :string_id=>"SHAREPOINT_S_DEVELOPER" },
  { :friendly_name=>"Office Online For Developer", :guid=>"527f7cdd-0e86-4c47-b879-f5fd357a3ac6", :string_id=>"SHAREPOINTWAC_DEVELOPER" },
  { :friendly_name=>"Skype For Business Online (Plan 3)", :guid=>"27216c54-caf8-4d0d-97e2-517afb5c08f6", :string_id=>"MCOVOICECONF" },
  { :friendly_name=>"Exchange Online Plan 1", :guid=>"fc52cc4b-ed7d-472d-bbe7-b081c23ecc56", :string_id=>"EXCHANGE_S_STANDARD_MIDMARKET" },
  { :friendly_name=>"Skype For Business Online (Plan 2) For Midsize", :guid=>"b2669e95-76ef-4e7e-a367-002f60a39f3e", :string_id=>"MCOSTANDARD_MIDMARKET" },
  { :friendly_name=>"Sharepointenterprise Midmarket", :guid=>"6b5b6a67-fc72-4a1f-a2b5-beecf05de761", :string_id=>"SHAREPOINTENTERPRISE_MIDMARKET" },
  { :friendly_name=>"Exchange Online (P1)", :guid=>"d42bdbd6-c335-4231-ab3d-c8f348d5aff5", :string_id=>"EXCHANGE_L_STANDARD" },
  { :friendly_name=>"Skype For Business Online (Plan P1)", :guid=>"70710b6b-3ab4-4a38-9f6d-9f169461650a", :string_id=>"MCOLITE" },
  { :friendly_name=>"Sharepointlite", :guid=>"a1f3d0a8-84c0-4ae0-bae4-685917b8ab48", :string_id=>"SHAREPOINTLITE" },
  { :friendly_name=>"Office Pro Plus Subscription Smbiz", :guid=>"8ca59559-e2ca-470b-b7dd-afd8c0dee963", :string_id=>"OFFICE_PRO_PLUS_SUBSCRIPTION_SMBIZ" },
  { :friendly_name=>"Onedriveenterprise", :guid=>"afcafa6a-d966-4462-918c-ec0b4e0fe642", :string_id=>"ONEDRIVEENTERPRISE" },
  { :friendly_name=>"Microsoft Power Bi Reporting And Analytics Plan 1", :guid=>"2125cfd7-2110-4567-83c4-c1cd5275163d", :string_id=>"BI_AZURE_P1" },
  { :friendly_name=>"Microsoft Power Bi Information Services Plan 1", :guid=>"fc0a60aa-feee-4746-a0e3-aecfe81a38dd", :string_id=>"SQL_IS_SSIM" },
  { :friendly_name=>"Domestic And International Calling Plan", :guid=>"5a10155d-f5c1-411a-a8ec-e99aae125390", :string_id=>"MCOPSTN2" },
  { :friendly_name=>"Domestic Calling Plan", :guid=>"4ed3ff63-69d7-4fb7-b984-5aec7f605ca8", :string_id=>"MCOPSTN1" },
  { :friendly_name=>"Domestic Calling Plan", :guid=>"54a152dc-90de-4996-93d2-bc47e670fc06", :string_id=>"MCOPSTN5" },
  { :friendly_name=>"Onedrive Basic", :guid=>"da792a53-cbc0-4184-a10d-e544dd34b3c1", :string_id=>"ONEDRIVE_BASIC" },
  { :friendly_name=>"Visioonline", :guid=>"2bdbaf8f-738f-4ac7-9234-3c3ee2ce7d0f", :string_id=>"VISIOONLINE" },
  { :friendly_name=>"Visio Client Subscription", :guid=>"663a804f-1c30-4ff0-9915-9db84f0d1cea", :string_id=>"VISIO_CLIENT_SUBSCRIPTION" },
  { :friendly_name=> "Microsoft 365 Advanced Communications", :guid=> "604ec28a-ae18-4bc6-91b0-11da94504ba9", :string_id=> "TEAMS_ADVCOMMS" },
  { :friendly_name=> "AI Builder capacity add-on", :guid=> "a7c70a41-5e02-4271-93e6-d9b4184d83f5", :string_id=> "CDSAICAPACITY" },
  { :friendly_name=> "Microsoft 365 Audit Platform", :guid=> "f6de4823-28fa-440b-b886-4783fa86ddba", :string_id=> "M365_AUDIT_PLATFORM" },
  { :friendly_name=> "Microsoft Application Protection and Governance (A)", :guid=> "5f3b1ded-75c0-4b31-8e6e-9b077eaadfd5", :string_id=> "MICROSOFT_APPLICATION_PROTECTION_AND_GOVERNANCE_A" },
  { :friendly_name=> "Microsoft Application Protection and Governance (D)", :guid=> "2e6ffd72-52d1-4541-8f6c-938f9a8d4cdc", :string_id=> "MICROSOFT_APPLICATION_PROTECTION_AND_GOVERNANCE_D" },
  { :friendly_name=> "Microsoft Invoicing", :guid=> "39b5c996-467e-4e60-bd62-46066f572726", :string_id=> "DYN365BC_MS_INVOICING" },
  { :friendly_name=> "Common Data Service for Apps File Capacity", :guid=> "dd12a3a8-caec-44f8-b4fb-2f1a864b51e3", :string_id=> "CDS_FILE_CAPACITY" },
  { :friendly_name=> "Common Data Service for Apps Database Capacity", :guid=> "360bcc37-0c11-4264-8eed-9fa7a3297c9b", :string_id=> "CDS_DB_CAPACITY" },
  { :friendly_name=> "Common Data Service for Apps Database Capacity for Government", :guid=> "1ddffef6-4f69-455e-89c7-d5d72105f915", :string_id=> "CDS_DB_CAPACITY_GOV" },
  { :friendly_name=> "Common Data Service for Apps Log Capacity", :guid=> "dc48f5c5-e87d-43d6-b884-7ac4a59e7ee9", :string_id=> "CDS_LOG_CAPACITY" },
  { :friendly_name=> "COMMUNICATIONS CREDITS", :guid=> "505e180f-f7e0-4b65-91d4-00d670bbd18c", :string_id=> "MCOPSTNC" },
  { :friendly_name=> "Compliance Manager Premium Assessment Add-On", :guid=> "3a117d30-cfac-4f00-84ac-54f8b6a18d78", :string_id=> "COMPLIANCE_MANAGER_PREMIUM_ASSESSMENT_ADDON" },
  { :friendly_name=> "Microsoft Dynamics CRM Online Storage Add-On", :guid=> "77866113-0f3e-4e6e-9666-b1e25c6f99b0", :string_id=> "CRMSTORAGE" },
  { :friendly_name=> "Microsoft Dynamics CRM Online Instance", :guid=> "eeea837a-c885-4167-b3d5-ddde30cbd85f", :string_id=> "CRMINSTANCE" },
  { :friendly_name=> "Microsoft Dynamics CRM Online Additional Test Instance", :guid=> "a98b7619-66c7-4885-bdfc-1d9c8c3d279f", :string_id=> "CRMTESTINSTANCE" },
  { :friendly_name=> "Dynamics 365 AI for Market Insights - Free", :guid=> "339f4def-5ad8-4430-8d12-da5fd4c769a7", :string_id=> "SOCIAL_ENGAGEMENT_APP_USER" },
  { :friendly_name=> "Asset Maintenance Add-in", :guid=> "********-5b40-40d4-835c-abd48009b1d9", :string_id=> "D365_AssetforSCM" },
  { :friendly_name=> "Dynamics 365 Business Central Additional Environment Addon", :guid=> "d397d6c6-9664-4502-b71c-66f39c400ca4", :string_id=> "DYN365_BUSCENTRAL_ENVIRONMENT" },
  { :friendly_name=> "Dynamics 365 Business Central Database Capacity", :guid=> "ae6b27b3-fe31-4e77-ae06-ec5fabbc103a", :string_id=> "DYN365_BUSCENTRAL_DB_CAPACITY" },
  { :friendly_name=> "Dynamics 365 Business Central External Accountant", :guid=> "170991d7-b98e-41c5-83d4-db2052e1795f", :string_id=> "DYN365_FINANCIALS_ACCOUNTANT" },
  { :friendly_name=> "Dynamics 365 Business Central for IWs", :guid=> "3f2afeed-6fb5-4bf9-998f-f2912133aead", :string_id=> "PROJECT_MADEIRA_PREVIEW_IW" },
  { :friendly_name=> "Dynamics 365 Business Central Premium", :guid=> "8e9002c0-a1d8-4465-b952-817d2948e6e2", :string_id=> "DYN365_BUSCENTRAL_PREMIUM" },
  { :friendly_name=> "Dynamics 365 for Team Members", :guid=> "d9a6391b-8970-4976-bd94-5f205007c8d8", :string_id=> "DYN365_FINANCIALS_TEAM_MEMBERS" },
  { :friendly_name=> "Dynamics 365 Customer Service Insights for CE Plan", :guid=> "1412cdc1-d593-4ad1-9050-40c30ad0b023", :string_id=> "D365_CSI_EMBED_CE" },
  { :friendly_name=> "Dynamics 365 Project Operations", :guid=> "69f07c66-bee4-4222-b051-195095efee5b", :string_id=> "D365_ProjectOperations" },
  { :friendly_name=> "Dynamics 365 Project Operations CDS", :guid=> "18fa3aba-b085-4105-87d7-55617b8585e6", :string_id=> "D365_ProjectOperationsCDS" },
  { :friendly_name=> "Microsoft Dynamics 365 Customer Voice for Customer Engagement Plan", :guid=> "97f29a83-1a20-44ff-bf48-5e4ad11f3e51", :string_id=> "Forms_Pro_CE" },
  { :friendly_name=> "Project for Project Operations", :guid=> "0a05d977-a21a-45b2-91ce-61c240dbafa2", :string_id=> "PROJECT_FOR_PROJECT_OPERATIONS" },
  { :friendly_name=> "Common Data Service for Customer Insights Trial", :guid=> "94e5cbf6-d843-4ee8-a2ec-8b15eb52019e", :string_id=> "CDS_CUSTOMER_INSIGHTS_TRIAL" },
  { :friendly_name=> "Dynamics 365 Customer Insights Engagement Insights Viral", :guid=> "e2bdea63-235e-44c6-9f5e-5b0e783f07dd", :string_id=> "DYN365_CUSTOMER_INSIGHTS_ENGAGEMENT_INSIGHTS_BASE_TRIAL" },
  { :friendly_name=> "Dynamics 365 Customer Insights Viral Plan", :guid=> "ed8e8769-94c5-4132-a3e7-7543b713d51f", :string_id=> "DYN365_CUSTOMER_INSIGHTS_VIRAL" },
  { :friendly_name=> "Microsoft Dynamics 365 Customer Voice for Customer Insights", :guid=> "fe581650-cf61-4a09-8814-4bd77eca9cb5", :string_id=> "Forms_Pro_Customer_Insights" },
  { :friendly_name=> "Dynamics 365 for Customer Service Enterprise Attach", :guid=> "61a2665f-1873-488c-9199-c3d0bc213fdf", :string_id=> "D365_CUSTOMER_SERVICE_ENT_ATTACH" },
  { :friendly_name=> "Power Pages Internal User", :guid=> "60bf28f9-2b70-4522-96f7-335f5e06c941", :string_id=> "Power_Pages_Internal_User" },
  { :friendly_name=> "Customer Voice for Dynamics 365 vTrial", :guid=> "dbe07046-af68-4861-a20d-1c8cbda9194f", :string_id=> "CUSTOMER_VOICE_DYN365_VIRAL_TRIAL" },
  { :friendly_name=> "Dynamics 365 AI for Customer Service Virtual Agents Viral", :guid=> "ce312d15-8fdf-44c0-9974-a25a177125ee", :string_id=> "CCIBOTS_PRIVPREV_VIRAL" },
  { :friendly_name=> "Dynamics 365 Customer Service Digital Messaging vTrial", :guid=> "3bf52bdf-5226-4a97-829e-5cca9b3f3392", :string_id=> "DYN365_CS_MESSAGING_VIRAL_TRIAL" },
  { :friendly_name=> "Dynamics 365 Customer Service Enterprise vTrial", :guid=> "94fb67d3-465f-4d1f-a50a-952da079a564", :string_id=> "DYN365_CS_ENTERPRISE_VIRAL_TRIAL" },
  { :friendly_name=> "Dynamics 365 Customer Service Insights vTrial", :guid=> "33f1466e-63a6-464c-bf6a-d1787928a56a", :string_id=> "DYNB365_CSI_VIRAL_TRIAL" },
  { :friendly_name=> "Dynamics 365 Customer Service Voice vTrial", :guid=> "3de81e39-4ce1-47f7-a77f-8473d4eb6d7c", :string_id=> "DYN365_CS_VOICE_VIRAL_TRIAL" },
  { :friendly_name=> "Power Apps for Dynamics 365 vTrial", :guid=> "54b37829-818e-4e3c-a08a-3ea66ab9b45d", :string_id=> "POWER_APPS_DYN365_VIRAL_TRIAL" },
  { :friendly_name=> "Power Automate for Dynamics 365 vTrial", :guid=> "81d4ecb8-0481-42fb-8868-51536c5aceeb", :string_id=> "POWER_AUTOMATE_DYN365_VIRAL_TRIAL" },
  { :friendly_name=> "Dynamics 365 AI for Customer Service Trial", :guid=> "4ade5aa6-5959-4d2c-bf0a-f4c9e2cc00f2", :string_id=> "DYN365_AI_SERVICE_INSIGHTS" },
  { :friendly_name=> "Common Data Service", :guid=> "363430d1-e3f7-43bc-b07b-767b6bb95e4b", :string_id=> "DYN365_CDS_FORMS_PRO" },
  { :friendly_name=> "Dynamics 365 Customer Voice", :guid=> "17efdd9f-c22c-4ad8-b48e-3b1f3ee1dc9a", :string_id=> "FORMS_PRO" },
  { :friendly_name=> "Power Automate for Dynamics 365 Customer Voice", :guid=> "57a0746c-87b8-4405-9397-df365a9db793", :string_id=> "FLOW_FORMS_PRO" },
  { :friendly_name=> "Dynamics 365 for Customer Service Pro", :guid=> "6929f657-b31b-4947-b4ce-5066c3214f54", :string_id=> "DYN365_CUSTOMER_SERVICE_PRO" },
  { :friendly_name=> "Power Apps for Customer Service Pro", :guid=> "c507b04c-a905-4940-ada6-918891e6d3ad", :string_id=> "POWERAPPS_CUSTOMER_SERVICE_PRO" },
  { :friendly_name=> "Power Automate for Customer Service Pro", :guid=> "0368fc9c-3721-437f-8b7d-3d0f888cdefc", :string_id=> "FLOW_CUSTOMER_SERVICE_PRO" },
  { :friendly_name=> "Dynamics 365 Customer Voice Base Plan", :guid=> "296820fe-dce5-40f4-a4f2-e14b8feef383", :string_id=> "Customer_Voice_Base" },
  { :friendly_name=> "Microsoft Dynamics 365 Customer Voice Add-on", :guid=> "90a816f6-de5f-49fd-963c-df490d73b7b5", :string_id=> "Forms_Pro_AddOn" },
  { :friendly_name=> "Dynamics Customer Voice Add-On", :guid=> "e6e35e2d-2e7f-4e71-bc6f-2f40ed062f5d", :string_id=> "CUSTOMER_VOICE_ADDON" },
  { :friendly_name=> "Common Data Service", :guid=> "e9830cfd-e65d-49dc-84fb-7d56b9aa2c89", :string_id=> "CDS_FORM_PRO_USL" },
  { :friendly_name=> "Microsoft Dynamics 365 Customer Voice USL", :guid=> "3ca0766a-643e-4304-af20-37f02726339b", :string_id=> "Forms_Pro_USL" },
  { :friendly_name=> "Microsoft Dynamics CRM Online - Portal Add-On", :guid=> "1d4e9cb1-708d-449c-9f71-943aa8ed1d6a", :string_id=> "CRM_ONLINE_PORTAL" },
  { :friendly_name=> "Dynamics 365 Field Service Enterprise vTrial", :guid=> "20d1455b-72b2-4725-8354-a177845ab77d", :string_id=> "DYN365_FS_ENTERPRISE_VIRAL_TRIAL" },
  { :friendly_name=> "Common Data Service for Dynamics 365 Finance", :guid=> "e95d7060-d4d9-400a-a2bd-a244bf0b609e", :string_id=> "DYN365_CDS_FINANCE" },
  { :friendly_name=> "Dynamics 365 for Finance and Operations Enterprise edition - Regulatory Service", :guid=> "c7657ae3-c0b0-4eed-8c1d-6a7967bd9c65", :string_id=> "DYN365_REGULATORY_SERVICE" },
  { :friendly_name=> "Microsoft Dynamics 365 for Finance", :guid=> "9f0e1b4e-9b33-4300-b451-b2c662cd4ff7", :string_id=> "D365_Finance" },
  { :friendly_name=> "Dynamics 365 for Case Management", :guid=> "2822a3a1-9b8f-4432-8989-e11669a60dc8", :string_id=> "DYN365_ENTERPRISE_CASE_MANAGEMENT" },
  { :friendly_name=> "Dynamics 365 Customer Service Chat Application Integration", :guid=> "426ec19c-d5b1-4548-b894-6fe75028c30d", :string_id=> "DYN365_CS_CHAT_FPA" },
  { :friendly_name=> "Dynamics 365 for Customer Service Chat", :guid=> "f69129db-6dc1-4107-855e-0aaebbcd9dd4", :string_id=> "DYN365_CS_CHAT" },
  { :friendly_name=> "Power Virtual Agents for Chat", :guid=> "19e4c3a8-3ebe-455f-a294-4f3479873ae3", :string_id=> "POWER_VIRTUAL_AGENTS_D365_CS_CHAT" },
  { :friendly_name=> "Dynamics 365 Customer Service Insights for CS Enterprise", :guid=> "5b1e5982-0e88-47bb-a95e-ae6085eda612", :string_id=> "D365_CSI_EMBED_CSEnterprise" },
  { :friendly_name=> "Microsoft Dynamics 365 Customer Voice for Customer Service Enterprise", :guid=> "67bf4812-f90b-4db9-97e7-c0bbbf7b2d09", :string_id=> "Forms_Pro_Service" },
  { :friendly_name=> "Dynamics 365 for Field Service Attach", :guid=> "55c9148b-d5f0-4101-b5a0-b2727cfc0916", :string_id=> "D365_FIELD_SERVICE_ATTACH" },
  { :friendly_name=> "Dynamics 365 for Field Service", :guid=> "8c66ef8a-177f-4c0d-853c-d4f219331d09", :string_id=> "DYN365_ENTERPRISE_FIELD_SERVICE" },
  { :friendly_name=> "Microsoft Dynamics 365 Customer Voice for Field Service", :guid=> "9c439259-63b0-46cc-a258-72be4313a42d", :string_id=> "Forms_Pro_FS" },
  { :friendly_name=> "CRM Hybrid Connector", :guid=> "0210d5c8-49d2-4dd1-a01b-a91c7c14e0bf", :string_id=> "CRM_HYBRIDCONNECTOR" },
  { :friendly_name=> "Dynamics 365 for Marketing Additional Application", :guid=> "51cf0638-4861-40c0-8b20-1161ab2f80be", :string_id=> "DYN365_MARKETING_APPLICATION_ADDON" },
  { :friendly_name=> "Dynamics 365 for Marketing 50K Addnl Contacts", :guid=> "e626a4ec-1ba2-409e-bf75-9bc0bc30cca7", :string_id=> "DYN365_MARKETING_50K_CONTACT_ADDON" },
  { :friendly_name=> "Dynamics 365 Marketing Sandbox Application AddOn", :guid=> "1599de10-5250-4c95-acf2-491f74edce48", :string_id=> "DYN365_MARKETING_SANDBOX_APPLICATION_ADDON" },
  { :friendly_name=> "Dynamics 365 for Marketing", :guid=> "a3a4fa10-5092-401a-af30-0462a95a7ac8", :string_id=> "DYN365_MARKETING_APP" },
  { :friendly_name=> "Microsoft Dynamics 365 Customer Voice for Marketing Application", :guid=> "22b657cf-0a9e-467b-8a91-5e31f21bc570", :string_id=> "Forms_Pro_Marketing_App" },
  { :friendly_name=> "Dynamics 365 for Marketing MSE User", :guid=> "2824c69a-1ac5-4397-8592-eae51cb8b581", :string_id=> "DYN365_MARKETING_MSE_USER" },
  { :friendly_name=> "Dynamics 365 for Marketing USL", :guid=> "5d7a6abc-eebd-46ab-96e1-e4a2f54a2248", :string_id=> "DYN365_MARKETING_USER" },
  { :friendly_name=> "Microsoft Dynamics 365 Customer Voice for Marketing", :guid=> "76366ba0-d230-47aa-8087-b6d55dae454f", :string_id=> "Forms_Pro_Marketing" },
  { :friendly_name=> "Dynamics 365 AI for Sales (Embedded)", :guid=> "fedc185f-0711-4cc0-80ed-0a92da1a8384", :string_id=> "DYN365_SALES_INSIGHTS" },
  { :friendly_name=> "Microsoft Viva Sales Premium with Power Automate", :guid=> "a933a62f-c3fb-48e5-a0b7-ac92b94b4420", :string_id=> "Microsoft_Viva_Sales_PowerAutomate" },
  { :friendly_name=> "Microsoft Viva Sales Premium & Trial", :guid=> "8ba1ff15-7bf6-4620-b65c-ecedb6942766", :string_id=> "Microsoft_Viva_Sales_PremiumTrial" },
  { :friendly_name=> "Microsoft Dynamics 365 Customer Voice for Sales Enterprise", :guid=> "8839ef0e-91f1-4085-b485-62e06e7c7987", :string_id=> "Forms_Pro_SalesEnt" },
  { :friendly_name=> "Dynamics 365 Marketing", :guid=> "393a0c96-9ba1-4af0-8975-fa2f853a25ac", :string_id=> "DYN365_BUSINESS_Marketing" },
  { :friendly_name=> "Dynamics 365 Sales Enterprise vTrial", :guid=> "7f636c80-0961-41b2-94da-9642ccf02de0", :string_id=> "DYN365_SALES_ENTERPRISE_VIRAL_TRIAL" },
  { :friendly_name=> "Dynamics 365 Sales Insights vTrial", :guid=> "456747c0-cf1e-4b0d-940f-703a01b964cc", :string_id=> "DYN365_SALES_INSIGHTS_VIRAL_TRIAL" },
  { :friendly_name=> "Dynamics 365 for Sales Professional", :guid=> "88d83950-ff78-4e85-aa66-abfc787f8090", :string_id=> "DYN365_SALES_PRO" },
  { :friendly_name=> "Power Apps for Sales Pro", :guid=> "6f9f70ce-138d-49f8-bb8b-2e701b7dde75", :string_id=> "POWERAPPS_SALES_PRO" },
  { :friendly_name=> "Dynamics 365 for Sales Professional Trial", :guid=> "73f205fc-6b15-47a5-967e-9e64fdf72d0a", :string_id=> "D365_SALES_PRO_IW" },
  { :friendly_name=> "Dynamics 365 for Sales Professional Trial", :guid=> "db39a47e-1f4f-462b-bf5b-2ec471fb7b88", :string_id=> "D365_SALES_PRO_IW_Trial" },
  { :friendly_name=> "Dynamics 365 for Sales Pro Attach", :guid=> "065f3c64-0649-4ec7-9f47-ef5cf134c751", :string_id=> "D365_SALES_PRO_ATTACH" },
  { :friendly_name=> "COMMON DATA SERVICE FOR DYNAMICS 365 SUPPLY CHAIN MANAGEMENT", :guid=> "b6a8b974-2956-4e14-ae81-f0384c363528", :string_id=> "DYN365_CDS_SUPPLYCHAINMANAGEMENT" },
  { :friendly_name=> "DYNAMICS 365 FOR SUPPLY CHAIN MANAGEMENT", :guid=> "1224eae4-0d91-474a-8a52-27ec96a63fe7", :string_id=> "D365_SCM" },
  { :friendly_name=> "Common Data Service", :guid=> "2d925ad8-2479-4bd8-bb76-5b80f1d48935", :string_id=> "DYN365_CDS_DYN_APPS" },
  { :friendly_name=> "Dynamics 365 for HCM Trial", :guid=> "5ed38b64-c3b7-4d9f-b1cd-0de18c9c4331", :string_id=> "Dynamics_365_for_HCM_Trial" },
  { :friendly_name=> "Common Data Service", :guid=> "1315ade1-0410-450d-b8e3-8050e6da320f", :string_id=> "DYN365_CDS_GUIDES" },
  { :friendly_name=> "Dynamics 365 Guides", :guid=> "0b2c029c-dca0-454a-a336-887285d6ef07", :string_id=> "GUIDES" },
  { :friendly_name=> "Power Apps for Guides", :guid=> "816971f4-37c5-424a-b12b-b56881f402e7", :string_id=> "POWERAPPS_GUIDES" },
  { :friendly_name=> "Dynamics 365 for Retail Device", :guid=> "ceb28005-d758-4df7-bb97-87a617b93d6c", :string_id=> "DYN365_RETAIL_DEVICE" },
  { :friendly_name=> "Dynamics 365 for Operations Devices", :guid=> "2c9fb43e-915a-4d61-b6ca-058ece89fd66", :string_id=> "Dynamics_365_for_OperationsDevices" },
  { :friendly_name=> "Dynamics 365 for Operations non-production multi-box instance for standard acceptance testing (Tier 2)", :guid=> "d8ba6fb2-c6b1-4f07-b7c8-5f2745e36b54", :string_id=> "Dynamics_365_for_Operations_Sandbox_Tier2" },
  { :friendly_name=> "Dynamics 365 for Operations Enterprise Edition - Sandbox Tier 4:Standard Performance Testing", :guid=> "f6b5efb1-1813-426f-96d0-9b4f7438714f", :string_id=> "Dynamics_365_for_Operations_Sandbox_Tier4" },
  { :friendly_name=> "DYNAMICS 365 P1 TRIAL FOR INFORMATION WORKERS", :guid=> "056a5f80-b4e0-4983-a8be-7ad254a113c9", :string_id=> "DYN365_ENTERPRISE_P1_IW" },
  { :friendly_name=> "Common Data Service for Remote Assist", :guid=> "0850ebb5-64ee-4d3a-a3e1-5a97213653b5", :string_id=> "CDS_REMOTE_ASSIST" },
  { :friendly_name=> "Microsoft Remote Assist", :guid=> "4f4c7800-298a-4e22-8867-96b17850d4dd", :string_id=> "MICROSOFT_REMOTE_ASSIST" },
  { :friendly_name=> "Dynamics 365 for Sales Enterprise Attach", :guid=> "3ae52229-572e-414f-937c-ff35a87d4f29", :string_id=> "D365_SALES_ENT_ATTACH" },
  { :friendly_name=> "DYNAMICS 365 FOR TALENT: ONBOARD", :guid=> "048a552e-c849-4027-b54c-4c7ead26150a", :string_id=> "Dynamics_365_Talent_Onboard" },
  { :friendly_name=> "DYNAMICS 365 TEAM MEMBERS", :guid=> "4092fdb5-8d81-41d3-be76-aaba4074530b", :string_id=> "DYN365_TEAM_MEMBERS" },
  { :friendly_name=> "Exchange Enterprise CAL Services (EOP DLP)", :guid=> "75badc48-628e-4446-8460-41344d73abd6", :string_id=> "EOP_ENTERPRISE_PREMIUM" },
  { :friendly_name=> "Microsoft Azure Rights Management Service", :guid=> "31cf2cfc-6b0d-4adc-a336-88b724ed8122", :string_id=> "RMS_S_BASIC" },
  { :friendly_name=> "Exchange Online (Plan 1) for Government", :guid=> "e9b4930a-925f-45e2-ac2a-3f7788ca6fdd", :string_id=> "EXCHANGE_S_STANDARD_GOV" },
  { :friendly_name=> "Exchange Online Protection", :guid=> "326e2b78-9d27-42c9-8509-46c827743a17", :string_id=> "EOP_ENTERPRISE" },
  { :friendly_name=> "Dynamics 365 Operations Trial Environment", :guid=> "e2f705fd-2468-4090-8c58-fad6e6b1e724", :string_id=> "ERP_TRIAL_INSTANCE" },
  { :friendly_name=> "Microsoft 365 Defender", :guid=> "bf28f719-7844-4079-9c78-c1307898e192", :string_id=> "MTP" },
  { :friendly_name=> "Microsoft Defender Vulnerability Management", :guid=> "36810a13-b903-490a-aa45-afbeb7540832", :string_id=> "TVM_PREMIUM_1" },
  { :friendly_name=> "Intune Advanced endpoint analytics", :guid=> "2a4baa0e-5e99-4c38-b1f2-6864960f1bd1", :string_id=> "Intune_AdvancedEA" },
  { :friendly_name=> "Intune Endpoint Privilege Management", :guid=> "bb73f429-78ef-4ff2-83c8-722b04c3e7d1", :string_id=> "Intune-EPM" },
  { :friendly_name=> "Intune Plan 2", :guid=> "d9923fe3-a2de-4d29-a5be-e3e83bb786be", :string_id=> "INTUNE_P2" },
  { :friendly_name=> "Microsoft Tunnel for Mobile Application Management", :guid=> "a6e407da-7411-4397-8a2e-d9b52780849e", :string_id=> "Intune-MAMTunnel" },
  { :friendly_name=> "Remote help", :guid=> "a4c6cf29-1168-4076-ba5c-e8fe0e62b17e", :string_id=> "REMOTE_HELP" },
  { :friendly_name=> "Common Data Service for Teams", :guid=> "95b76021-6a53-4741-ab8b-1d1f3d66a95a", :string_id=> "CDS_O365_P2" },
  { :friendly_name=> "Information Protection and Governance Analytics � Standard", :guid=> "2b815d45-56e4-4e3a-b65c-66cb9175b560", :string_id=> "ContentExplorer_Standard" },
  { :friendly_name=> "Microsoft Defender for Endpoint Plan 1", :guid=> "292cc034-7b7c-4950-aaf5-943befd3f1d4", :string_id=> "MDE_LITE" },
  { :friendly_name=> "Microsoft Search", :guid=> "94065c59-bc8e-4e8b-89e5-5138d471eaff", :string_id=> "MICROSOFT_SEARCH" },
  { :friendly_name=> "Nucleus", :guid=> "db4d623d-b514-490b-b7ef-8885eee514de", :string_id=> "Nucleus" },
  { :friendly_name=> "Office 365 Cloud App Security", :guid=> "8c098270-9dd4-4350-9b30-ba4703f3b36b", :string_id=> "ADALLOM_S_O365" },
  { :friendly_name=> "Project for Office (Plan E3)", :guid=> "31b4e2fc-4cd6-4e7d-9c1b-41407303bd66", :string_id=> "PROJECT_O365_P2" },
  { :friendly_name=> "Viva Learning Seeded", :guid=> "b76fb638-6ba6-402a-b9f9-83d28acb3d86", :string_id=> "VIVA_LEARNING_SEEDED" },    
  { :friendly_name=> "Universal Print", :guid=> "795f6fe0-cc4d-4773-b050-5dde4dc704c9", :string_id=> "UNIVERSAL_PRINT_01" },
  { :friendly_name=> "Windows Update for Business Deployment Service", :guid=> "7bf960f6-2cd9-443a-8046-5dbff9558365", :string_id=> "WINDOWSUPDATEFORBUSINESS_DEPLOYMENTSERVICE" },
  { :friendly_name=> "Common Data Service", :guid=> "4ff01e01-1ba7-4d71-8cf8-ce96c3bbcf14", :string_id=> "DYN365_CDS_O365_P2" },
  { :friendly_name=> "Power Apps for Office 365", :guid=> "c68f8d98-5534-41c8-bf36-22fa496fa792", :string_id=> "POWERAPPS_O365_P2" },
  { :friendly_name=> "Power Virtual Agents for Office 365", :guid=> "041fe683-03e4-45b6-b1af-c0cdc516daee", :string_id=> "POWER_VIRTUAL_AGENTS_O365_P2" },
  { :friendly_name=> "Universal Print Without Seeding", :guid=> "b67adbaf-a096-42c9-967e-5a84edbe0086", :string_id=> "UNIVERSAL_PRINT_NO_SEEDING" },
  { :friendly_name=> "Microsoft 365 Apps for Enterprise (Unattended)", :guid=> "8d77e2d9-9e28-4450-8431-0def64078fc5", :string_id=> "OFFICESUBSCRIPTION_unattended" },
  { :friendly_name=> "Common Data Service for Teams", :guid=> "afa73018-811e-46e9-988f-f75d2b1b8430", :string_id=> "CDS_O365_P3" },
  { :friendly_name=> "Data Classification in Microsoft 365", :guid=> "cd31b152-6326-4d1b-ae1b-997b625182e6", :string_id=> "MIP_S_Exchange" },
  { :friendly_name=> "Information Protection and Governance Analytics - Premium", :guid=> "d9fa6af4-e046-4c89-9226-729a0786685d", :string_id=> "Content_Explorer" },
  { :friendly_name=> "Microsoft 365 Communication Compliance", :guid=> "a413a9ff-720c-4822-98ef-2f37c2a21f4c", :string_id=> "MICROSOFT_COMMUNICATION_COMPLIANCE" },
  { :friendly_name=> "Microsoft Excel Advanced Analytics", :guid=> "531ee2f8-b1cb-453b-9c21-d2180d014ca5", :string_id=> "EXCEL_PREMIUM" },
  { :friendly_name=> "Microsoft Insider Risk Management", :guid=> "d587c7a3-bda9-4f99-8776-9bcf59c84f75", :string_id=> "INSIDER_RISK" },
  { :friendly_name=> "Microsoft ML-Based Classification", :guid=> "d2d51368-76c9-4317-ada2-a12c004c432f", :string_id=> "ML_CLASSIFICATION" },
  { :friendly_name=> "Project for Office (Plan E5)", :guid=> "b21a6b06-1988-436e-a07b-51ec6d9f52ad", :string_id=> "PROJECT_O365_P3" },
  { :friendly_name=> "Microsoft Insider Risk Management", :guid=> "9d0c4ee5-e4a1-4625-ab39-d82b619b1a34", :string_id=> "INSIDER_RISK_MANAGEMENT" },
  { :friendly_name=> "To-Do (Plan 3)", :guid=> "3fb82609-8c27-4f7b-bd51-30634711ee67", :string_id=> "BPOS_S_TODO_3" },
  { :friendly_name=> "Common Data Service", :guid=> "28b0fa46-c39a-4188-89e2-58e979a6b014", :string_id=> "DYN365_CDS_O365_P3" },
  { :friendly_name=> "Power Virtual Agents for Office 365", :guid=> "ded3d325-1bdc-453e-8432-5bac26d7a014", :string_id=> "POWER_VIRTUAL_AGENTS_O365_P3" },
  { :friendly_name=> "Microsoft 365 Apps for Enterprise (Device)", :guid=> "3c994f28-87d5-4273-b07a-eb6190852599", :string_id=> "OFFICE_PROPLUS_DEVICE" },
  { :friendly_name=> "EXCHANGE FOUNDATION FOR GOVERNMENT", :guid=> "922ba911-5694-4e99-a794-73aed9bfeec8", :string_id=> "EXCHANGE_FOUNDATION_GOV" },
  { :friendly_name=> "MICROSOFT 365 AUDIO CONFERENCING FOR GOVERNMENT", :guid=> "f544b08d-1645-4287-82de-8d91f37c02a1", :string_id=> "MCOMEETADV_GOV" },
  { :friendly_name=> "Microsoft 365 Audio Conferencing Pay-Per-Minute", :guid=> "bb038288-76ab-49d6-afc1-eaa6c222c65a", :string_id=> "MCOMEETACPEA" },
  { :friendly_name=> "Viva Engage Core", :guid=> "a82fbf69-b4d7-49f4-83a6-915b2cf354f4", :string_id=> "VIVAENGAGE_CORE" },
  { :friendly_name=> "Microsoft Kaizala Pro", :guid=> "54fc630f-5a40-48ee-8965-af0503c1386e", :string_id=> "KAIZALA_O365_P2" },
  { :friendly_name=> "Stream for Office 365", :guid=> "3c53ea51-d578-46fa-a4c0-fd0a92809a60", :string_id=> "STREAM_O365_SMB" },
  { :friendly_name=> "Whiteboard (Plan 1)", :guid=> "b8afc642-032e-4de5-8c0a-507a7bba7e5d", :string_id=> "WHITEBOARD_PLAN1" },
  { :friendly_name=> "Data Loss Prevention", :guid=> "9bec7e34-c9fa-40b7-a9d1-bd6d1165c7ed", :string_id=> "BPOS_S_DlpAddOn" },
  { :friendly_name=> "Microsoft 365 Lighthouse (Plan 1)", :guid=> "6f23d6a9-adbf-481c-8538-b4c095654487", :string_id=> "M365_LIGHTHOUSE_CUSTOMER_PLAN1" },
  { :friendly_name=> "Microsoft 365 Lighthouse (Plan 2)", :guid=> "d55411c9-cfff-40a9-87c7-240f14df7da5", :string_id=> "M365_LIGHTHOUSE_PARTNER_PLAN1" },
  { :friendly_name=> "Microsoft Defender for Business", :guid=> "bfc1bbd9-981b-4f71-9b82-17c35fd0e2a4", :string_id=> "MDE_SMB" },
  { :friendly_name=> "Office Shared Computer Activation", :guid=> "276d6e8a-f056-4f70-b7e8-4fc27f79f809", :string_id=> "OFFICE_SHARED_COMPUTER_ACTIVATION" },      
  { :friendly_name=> "Domestic Calling for Government", :guid=> "3c8a8792-7866-409b-bb61-1b20ace0368b", :string_id=> "MCOPSTN1_GOV" },
  { :friendly_name=> "Windows Autopatch", :guid=> "9a6eeb79-0b4b-4bf0-9808-39d99a2cd5a3", :string_id=> "Windows_Autopatch" },
  { :friendly_name=> "Graph Connectors Search with Index", :guid=> "a6520331-d7d4-4276-95f5-15c0933bc757", :string_id=> "GRAPH_CONNECTORS_SEARCH_INDEX" },
  { :friendly_name=> "Microsoft 365 Domestic Calling Plan (120 min) at User Level", :guid=> "16935b20-87c0-4908-934a-22aa267d0d26", :string_id=> "MCOPSTN8" },
  { :friendly_name=> "Common Data Service for Teams", :guid=> "90db65a7-bf11-4904-a79f-ef657605145b", :string_id=> "CDS_O365_F1" }, 
  { :friendly_name=> "Project for Office (Plan F)", :guid=> "7f6f28c2-34bb-4d4b-be36-48ca2e77e1ec", :string_id=> "PROJECT_O365_F3" },
  { :friendly_name=> "Common Data Service", :guid=> "ca6e61ec-d4f4-41eb-8b88-d96e0e14323f", :string_id=> "DYN365_CDS_O365_F1" },
  { :friendly_name=> "Power Virtual Agents for Office 365", :guid=> "ba2fdb48-290b-4632-b46a-e4ecc58ac11a", :string_id=> "POWER_VIRTUAL_AGENTS_O365_F1" },
  { :friendly_name=> "Azure Information Protection Premium P1 for GCC", :guid=> "1b66aedf-8ca1-4f73-af76-ec76c6180f98", :string_id=> "RMS_S_PREMIUM_GOV" },
  { :friendly_name=> "Common Data Service - O365 F1", :guid=> "29007dd3-36c0-4cc2-935d-f5bca2c2c473", :string_id=> "DYN365_CDS_O365_F1_GCC" },
  { :friendly_name=> "Common Data Service for Teams_F1 GCC", :guid=> "5e05331a-0aec-437e-87db-9ef5934b5771", :string_id=> "CDS_O365_F1_GCC" },
  { :friendly_name=> "Exchange Online (Kiosk) for Government", :guid=> "88f4d7ef-a73b-4246-8047-516022144c9f", :string_id=> "EXCHANGE_S_DESKLESS_GOV" },
  { :friendly_name=> "Forms for Government (Plan F1)", :guid=> "bfd4133a-bbf3-4212-972b-60412137c428", :string_id=> "FORMS_GOV_F1" },
  { :friendly_name=> "Microsoft Stream for O365 for Government (F1)", :guid=> "d65648f1-9504-46e4-8611-2658763f28b8", :string_id=> "STREAM_O365_K_GOV" },
  { :friendly_name=> "Microsoft Teams for Government", :guid=> "304767db-7d23-49e8-a945-4a7eb65f9f28", :string_id=> "TEAMS_GOV" },
  { :friendly_name=> "Office 365 Planner for Government", :guid=> "5b4ef465-7ea1-459a-9f91-033317755a51", :string_id=> "PROJECTWORKMANAGEMENT_GOV" },
  { :friendly_name=> "Office for the Web for Government", :guid=> "8f9f0f3b-ca90-406c-a842-95579171f8ec", :string_id=> "SHAREPOINTWAC_GOV" },
  { :friendly_name=> "Office Mobile Apps for Office 365 for GCC", :guid=> "4ccb60ee-9523-48fd-8f63-4b090f1ad77a", :string_id=> "OFFICEMOBILE_SUBSCRIPTION_GOV" },
  { :friendly_name=> "Power Apps for Office 365 F3 for Government", :guid=> "49f06c3d-da7d-4fa0-bcce-1458fdd18a59", :string_id=> "POWERAPPS_O365_S1_GOV" },
  { :friendly_name=> "Power Automate for Office 365 F3 for Government", :guid=> "5d32692e-5b24-4a59-a77e-b2a8650e25c1", :string_id=> "FLOW_O365_S1_GOV" },
  { :friendly_name=> "SharePoint KioskG", :guid=> "b1aeb897-3a19-46e2-8c27-a609413cf193", :string_id=> "SHAREPOINTDESKLESS_GOV" },
  { :friendly_name=> "Skype for Business Online (Plan 1) for Government", :guid=> "8a9f17f1-5872-44e8-9b11-3caade9dc90f", :string_id=> "MCOIMP_GOV" },
  { :friendly_name=> "Microsoft Defender for Cloud Apps for DOD", :guid=> "6ebdddb7-8e55-4af2-952b-69e77262f96c", :string_id=> "ADALLOM_S_STANDALONE_DOD" },
  { :friendly_name=> "Customer Lockbox for Government", :guid=> "89b5d3b1-3855-49fe-b46c-87c66dbc1526", :string_id=> "LOCKBOX_ENTERPRISE_GOV" },
  { :friendly_name=> "Office 365 Advanced eDiscovery for Government", :guid=> "d1cbfb67-18a8-4792-b643-630b7f19aad1", :string_id=> "EQUIVIO_ANALYTICS_GOV" },
  { :friendly_name=> "Common Data Service for Teams", :guid=> "bce5e5ca-c2fd-4d53-8ee2-58dfffed4c10", :string_id=> "CDS_O365_P3_GCC" },
  { :friendly_name=> "Exchange Online (Plan 2) for Government", :guid=> "8c3069c0-ccdb-44be-ab77-986203a67df2", :string_id=> "EXCHANGE_S_ENTERPRISE_GOV" },
  { :friendly_name=> "Microsoft 365 Apps for enterprise G", :guid=> "de9234ff-6483-44d9-b15e-dca72fdd27af", :string_id=> "OFFICESUBSCRIPTION_GOV" },
  { :friendly_name=> "Microsoft 365 Phone System for Government", :guid=> "db23fce2-a974-42ef-9002-d78dd42a0f22", :string_id=> "MCOEV_GOV" },
  { :friendly_name=> "Microsoft Defender for Office 365 (Plan 1) for Government", :guid=> "493ff600-6a2b-4db6-ad37-a7d4eb214516", :string_id=> "ATP_ENTERPRISE_GOV" },
  { :friendly_name=> "Microsoft Defender for Office 365 (Plan 2) for Government", :guid=> "900018f1-0cdb-4ecb-94d4-90281760fdc6", :string_id=> "THREAT_INTELLIGENCE_GOV" },
  { :friendly_name=> "Microsoft Forms for Government (Plan E5)", :guid=> "843da3a8-d2cc-4e7a-9e90-dc46019f964c", :string_id=> "FORMS_GOV_E5" },
  { :friendly_name=> "Microsoft MyAnalytics for Government (Full)", :guid=> "208120d1-9adb-4daf-8c22-816bd5d237e7", :string_id=> "EXCHANGE_ANALYTICS_GOV" },
  { :friendly_name=> "Power BI Pro for Government", :guid=> "944e9726-f011-4353-b654-5f7d2663db76", :string_id=> "BI_AZURE_P_2_GOV" },
  { :friendly_name=> "SharePoint Plan 2G", :guid=> "153f85dd-d912-4762-af6c-d6e0fb4f6692", :string_id=> "SHAREPOINTENTERPRISE_GOV" },
  { :friendly_name=> "Skype for Business Online (Plan 2) for Government", :guid=> "a31ef4a2-f787-435e-8335-e47eb0cafc94", :string_id=> "MCOSTANDARD_GOV" },
  { :friendly_name=> "Stream for Office 365 for Government (E5)", :guid=> "92c2089d-9a53-49fe-b1a6-9e6bdf959547", :string_id=> "STREAM_O365_E5_GOV" },
  { :friendly_name=> "Azure Information Protection Premium P2 for GCC", :guid=> "5400a66d-eaa5-427d-80f2-0f26d59d8fce", :string_id=> "RMS_S_PREMIUM2_GOV" },
  { :friendly_name=> "Common Data Service", :guid=> "a7d3fb37-b6df-4085-b509-50810d991a39", :string_id=> "DYN365_CDS_O365_P3_GCC" },
  { :friendly_name=> "Power Apps for Office 365 for Government", :guid=> "0eacfc38-458a-40d3-9eab-9671258f1a3e", :string_id=> "POWERAPPS_O365_P3_GOV" },
  { :friendly_name=> "Power Automate for Office 365 for Government", :guid=> "8055d84a-c172-42eb-b997-6c2ae4628246", :string_id=> "FLOW_O365_P3_GOV" },
  { :friendly_name=> "COMMON DATA SERVICE - O365 P2 GCC", :guid=> "06162da2-ebf9-4954-99a0-00fee96f95cc", :string_id=> "DYN365_CDS_O365_P2_GCC" },
  { :friendly_name=> "COMMON DATA SERVICE FOR TEAMS_P2 GCC", :guid=> "a70bbf38-cdda-470d-adb8-5804b8770f41", :string_id=> "CDS_O365_P2_GCC" },
  { :friendly_name=> "FORMS FOR GOVERNMENT (PLAN E3)", :guid=> "24af5f65-d0f3-467b-9f78-ea798c4aeffc", :string_id=> "FORMS_GOV_E3" },
  { :friendly_name=> "INSIGHTS BY MYANALYTICS FOR GOVERNMENT", :guid=> "6e5b7995-bd4f-4cbd-9d19-0e32010c72f0", :string_id=> "MYANALYTICS_P2_GOV" },
  { :friendly_name=> "MICROSOFT STREAM FOR O365 FOR GOVERNMENT (E3)", :guid=> "2c1ada27-dbaa-46f9-bda6-ecb94445f758", :string_id=> "STREAM_O365_E3_GOV" },
  { :friendly_name=> "POWER APPS FOR OFFICE 365 FOR GOVERNMENT", :guid=> "0a20c815-5e81-4727-9bdc-2b5a117850c3", :string_id=> "POWERAPPS_O365_P2_GOV" },
  { :friendly_name=> "POWER AUTOMATE FOR OFFICE 365 FOR GOVERNMENT", :guid=> "c537f360-6a00-4ace-a7f5-9128d0ac1e4b", :string_id=> "FLOW_O365_P2_GOV" },
  { :friendly_name=> "MICROSOFT BUSINESS CENTER", :guid=> "cca845f9-fd51-4df6-b563-976a37c56ce0", :string_id=> "MICROSOFT_BUSINESS_CENTER" },
  { :friendly_name=> "MDE_SecurityManagement", :guid=> "1689aade-3d6a-4bfc-b017-46d2672df5ad", :string_id=> "Intune_Defender" },  
  { :friendly_name=> "SecOps Investigation for MDI", :guid=> "61d18b02-6889-479f-8f36-56e6e0fe5792", :string_id=> "ADALLOM_FOR_AATP" },
  { :friendly_name=> "Common Data Service - DEV VIRAL", :guid=> "d8c638e2-9508-40e3-9877-feb87603837b", :string_id=> "DYN365_CDS_DEV_VIRAL" },
  { :friendly_name=> "Flow for Developer", :guid=> "c7ce3f26-564d-4d3a-878d-d8ab868c85fe", :string_id=> "FLOW_DEV_VIRAL" },
  { :friendly_name=> "PowerApps for Developer", :guid=> "a2729df7-25f8-4e63-984b-8a8484121554", :string_id=> "POWERAPPS_DEV_VIRAL" },
  { :friendly_name=> "Common Data Service - VIRAL", :guid=> "17ab22cd-a0b3-4536-910a-cb6eb12696c0", :string_id=> "DYN365_CDS_VIRAL" },
  { :friendly_name=> "Flow Free", :guid=> "50e68c76-46c6-4674-81f9-75456511b170", :string_id=> "FLOW_P2_VIRAL" },
  { :friendly_name=> "Flow P2 Viral", :guid=> "d20bfa21-e9ae-43fc-93c2-20783f0840c3", :string_id=> "FLOW_P2_VIRAL_REAL" },
  { :friendly_name=> "PowerApps Trial", :guid=> "d5368ca3-357e-4acb-9c21-8495fb025d1f", :string_id=> "POWERAPPS_P2_VIRAL" },
  { :friendly_name=> "Common Data Service - P2", :guid=> "6ea4c1ef-c259-46df-bce2-943342cd3cb2", :string_id=> "DYN365_CDS_P2" },
  { :friendly_name=> "Power Automate (Plan 2)", :guid=> "56be9436-e4b2-446c-bb7f-cc15d16cca4d", :string_id=> "FLOW_P2" },
  { :friendly_name=> "Power Apps (Plan 2)", :guid=> "00527d7f-d5bc-4c2a-8d1e-6c0de2410c81", :string_id=> "POWERAPPS_P2" },
  { :friendly_name=> "Microsoft Dynamics 365 Customer Voice for Relationship Sales", :guid=> "507172c0-6001-4f4f-80e7-f350507af3e5", :string_id=> "Forms_Pro_Relationship_Sales" },
  { :friendly_name=> "Microsoft Relationship Sales solution", :guid=> "56e3d4ca-2e31-4c3f-8d57-89c1d363503b", :string_id=> "DYN365_ ENTERPRISE _RELATIONSHIP_SALES" },
  { :friendly_name=> "MICROSOFT STREAM", :guid=> "acffdce6-c30f-4dc2-81c0-372e33c515ec", :string_id=> "MICROSOFTSTREAM" },
  { :friendly_name=> "Microsoft Stream Plan 2", :guid=> "d3a458d0-f10d-48c2-9e44-86f3f684029e", :string_id=> "STREAM_P2" },
  { :friendly_name=> "Microsoft Stream Storage Add-On", :guid=> "83bced11-77ce-4071-95bd-************", :string_id=> "STREAM_STORAGE" },
  { :friendly_name=> "Microsoft Teams Audio Conferencing with dial-out to select geographies", :guid=> "9974d6cf-cd24-4ba2-921c-e2aa687da846", :string_id=> "MCOMEETBASIC" },
  { :friendly_name=> "MCO FREE FOR MICROSOFT TEAMS (FREE)", :guid=> "617d9209-3b90-4879-96e6-838c42b2701d", :string_id=> "MCOFREE" },
  { :friendly_name=> "MICROSOFT TEAMS (FREE)", :guid=> "4fa4026d-ce74-4962-a151-8e96d57ea8e4", :string_id=> "TEAMS_FREE" },
  { :friendly_name=> "TEAMS FREE SERVICE", :guid=> "bd6f2ac2-991a-49f9-b23c-18c96a02c228", :string_id=> "TEAMS_FREE_SERVICE" },
  { :friendly_name=> "Microsoft Teams Essentials", :guid=> "f4f2f6de-6830-442b-a433-e92249faebe2", :string_id=> "TeamsEss" },
  { :friendly_name=> "COMMON DATA SERVICE FOR TEAMS_P1", :guid=> "bed136c6-b799-4462-824d-fc045d3a9d25", :string_id=> "CDS_O365_P1" },
  { :friendly_name=> "MICROSOFT TEAMS", :guid=> "42a3ec34-28ba-46b6-992f-db53a675ac5b", :string_id=> "MCO_TEAMS_IW" },
  { :friendly_name=> "POWER VIRTUAL AGENTS FOR OFFICE 365 P1", :guid=> "0683001c-0492-4d59-9515-d9a6426b5813", :string_id=> "POWER_VIRTUAL_AGENTS_O365_P1" },
  { :friendly_name=> "SKYPE FOR BUSINESS CLOUD PBX FOR SMALL AND MEDIUM BUSINESS", :guid=> "ed777b71-af04-42ca-9798-84344c66f7c6", :string_id=> "MCOEVSMB" },
  { :friendly_name=> "Microsoft 365 Phone Standard Resource Account", :guid=> "f47330e9-c134-43b3-9993-e7f004506889", :string_id=> "MCOEV_VIRTUALUSER" },
  { :friendly_name=> "Microsoft 365 Phone Standard Resource Account for Government", :guid=> "0628a73f-3b4a-4989-bd7b-0f8823144313", :string_id=> "MCOEV_VIRTUALUSER_GOV" },
  { :friendly_name=> "Microsoft eCDN", :guid=> "85704d55-2e73-47ee-93b4-4b8ea14db92b", :string_id=> "MICROSOFT_ECDN" },
  { :friendly_name=> "Microsoft Teams Premium Intelligent", :guid=> "0504111f-feb8-4a3c-992a-70280f9a2869", :string_id=> "TEAMSPRO_MGMT" },
  { :friendly_name=> "Microsoft Teams Premium Personalized", :guid=> "cc8c0802-a325-43df-8cba-995d0c6cb373", :string_id=> "TEAMSPRO_CUST" },
  { :friendly_name=> "Microsoft Teams Premium Secure", :guid=> "f8b44f54-18bb-46a3-9658-44ab58712968", :string_id=> "TEAMSPRO_PROTECTION" },
  { :friendly_name=> "Microsoft Teams Premium Virtual Appointment", :guid=> "9104f592-f2a7-4f77-904c-ca5a5715883f", :string_id=> "TEAMSPRO_VIRTUALAPPT" },
  { :friendly_name=> "Microsoft Teams Premium Virtual Appointments", :guid=> "711413d0-b36e-4cd4-93db-0a50a4ab7ea3", :string_id=> "MCO_VIRTUAL_APPT" },
  { :friendly_name=> "Microsoft Teams Premium Webinar", :guid=> "78b58230-ec7e-4309-913c-93a45cc4735b", :string_id=> "TEAMSPRO_WEBINAR" },
  { :friendly_name=> "Teams Room Standard", :guid=> "92c6b761-01de-457a-9dd9-793a975238f7", :string_id=> "Teams_Room_Standard" },
  { :friendly_name=> "Microsoft Threat Experts - Experts on Demand", :guid=> "b83a66d4-f05f-414d-ac0f-ea1c5239c42b", :string_id=> "EXPERTS_ON_DEMAND" },
  { :friendly_name=> "Graph Connectors Search with Index (Microsoft Viva Topics)", :guid=> "b74d57b2-58e9-484a-9731-aeccbba954f0", :string_id=> "GRAPH_CONNECTORS_SEARCH_INDEX_TOPICEXP" },
  { :friendly_name=> "Microsoft Viva Insights", :guid=> "b622badb-1b45-48d5-920f-4b27a2c0996c", :string_id=> "WORKPLACE_ANALYTICS_INSIGHTS_USER" },
  { :friendly_name=> "Microsoft Viva Insights Backend", :guid=> "ff7b261f-d98b-415b-827c-42a3fdf015af", :string_id=> "WORKPLACE_ANALYTICS_INSIGHTS_BACKEND" },
  { :friendly_name=> "Microsoft Viva Topics", :guid=> "c815c93d-0759-4bb8-b857-bc921a71be83", :string_id=> "CORTEX" },
  { :friendly_name=> "Viva Engage Communities and Communications", :guid=> "43304c6a-1d4e-4e0b-9b06-5b2a2ff58a90", :string_id=> "VIVAENGAGE_COMMUNITIES_AND_COMMUNICATIONS" },
  { :friendly_name=> "Viva Engage Knowledge", :guid=> "c244cc9e-622f-4576-92ea-82e233e44e36", :string_id=> "VIVAENGAGE_KNOWLEDGE" },
  { :friendly_name=> "Viva Goals", :guid=> "b44c6eaf-5c9f-478c-8f16-8cea26353bfb", :string_id=> "Viva_Goals_Premium" },
  { :friendly_name=> "Viva Learning", :guid=> "7162bd38-edae-4022-83a7-c5837f951759", :string_id=> "VIVA_LEARNING_PREMIUM" },
  { :friendly_name=> "Exchange Online Multi-Geo", :guid=> "897d51f1-2cfa-4848-9b30-469149f5e68e", :string_id=> "EXCHANGEONLINE_MULTIGEO" },
  { :friendly_name=> "SharePoint Multi-Geo", :guid=> "735c1d98-dd3f-4818-b4ed-c8052e18e62d", :string_id=> "SHAREPOINTONLINE_MULTIGEO" },
  { :friendly_name=> "Teams Multi-Geo", :guid=> "41eda15d-6b52-453b-906f-bc4a5b25a26b", :string_id=> "TEAMSMULTIGEO" },
  { :friendly_name=> "Nonprofit Portal", :guid=> "7dbc2d88-20e2-4eb6-b065-4510b38d6eb2", :string_id=> "NONPROFIT_PORTAL" },
  { :friendly_name=> "Common Data Service - O365 P1", :guid=> "40b010bb-0b69-4654-ac5e-ba161433f4b4", :string_id=> "DYN365_CDS_O365_P1" },
  { :friendly_name=> "Project for Office (Plan E1)", :guid=> "a55dfd10-0864-46d9-a3cd-da5991a3e0e2", :string_id=> "PROJECT_O365_P1" },
  { :friendly_name=> "School Data Sync (Plan 1)", :guid=> "c33802dd-1b50-4b9a-8bb9-f13d2cdeadac", :string_id=> "SCHOOL_DATA_SYNC_P1" },
  { :friendly_name=> "SharePoint (Plan 1) for Education", :guid=> "0a4983bb-d3e5-4a09-95d8-b2d0127b3df5", :string_id=> "SHAREPOINTSTANDARD_EDU" },
  { :friendly_name=> "SHAREPOINTSTORAGE_GOV", :guid=> "e5bb877f-6ac9-4461-9e43-ca581543ab16", :string_id=> "SHAREPOINTSTORAGE_GOV" },
  { :friendly_name=> "Office 365 Extra File Storage", :guid=> "be5a7ed5-c598-4fcd-a061-5e6724c68a58", :string_id=> "SHAREPOINTSTORAGE" },
  { :friendly_name=> "Common Data Service - O365 P1 GCC", :guid=> "8eb5e9bc-783f-4425-921a-c65f45dd72c6", :string_id=> "DYN365_CDS_O365_P1_GCC" },
  { :friendly_name=> "Common Data Service for Teams_P1 GCC", :guid=> "959e5dec-6522-4d44-8349-132c27c3795a", :string_id=> "CDS_O365_P1_GCC" },
  { :friendly_name=> "Forms for Government (Plan E1)", :guid=> "f4cba850-4f34-4fd2-a341-0fddfdce1e8f", :string_id=> "FORMS_GOV_E1" },
  { :friendly_name=> "Microsoft Stream for O365 for Government (E1)", :guid=> "15267263-5986-449d-ac5c-124f3b49b2d6", :string_id=> "STREAM_O365_E1_GOV" },
  { :friendly_name=> "Power Apps for Office 365 for Government", :guid=> "c42aa49a-f357-45d5-9972-bc29df885fee", :string_id=> "POWERAPPS_O365_P1_GOV" },
  { :friendly_name=> "Power Automate for Office 365 for Government", :guid=> "ad6c8870-6356-474c-901c-64d7da8cea48", :string_id=> "FLOW_O365_P1_GOV" },
  { :friendly_name=> "SharePoint Plan 1G", :guid=> "f9c43823-deb4-46a8-aa65-8b551f0c4f8a", :string_id=> "SharePoint Plan 1G" },
  { :friendly_name=> "LOGIC FLOWS", :guid=> "0b4346bb-8dc3-4079-9dfc-513696f56039", :string_id=> "POWERFLOWSFREE" },
  { :friendly_name=> "MICROSOFT POWER VIDEOS BASIC", :guid=> "2c4ec2dc-c62d-4167-a966-52a3e6374015", :string_id=> "POWERVIDEOSFREE" },
  { :friendly_name=> "MICROSOFT POWERAPPS", :guid=> "e61a2945-1d4e-4523-b6e7-30ba39d20f32", :string_id=> "POWERAPPSFREE" },
  { :friendly_name=> "CDS Per app baseline access", :guid=> "94a669d1-84d5-4e54-8462-53b0ae2c8be5", :string_id=> "CDS_PER_APP_IWTRIAL" },
  { :friendly_name=> "Flow per app baseline access", :guid=> "dd14867e-8d31-4779-a595-304405f5ad39", :string_id=> "Flow_Per_APP_IWTRIAL" },
  { :friendly_name=> "PowerApps per app baseline access", :guid=> "35122886-cef5-44a3-ab36-97134eabd9ba", :string_id=> "POWERAPPS_PER_APP_IWTRIAL" },
  { :friendly_name=> "CDS PowerApps per app plan", :guid=> "9f2f00ad-21ae-4ceb-994b-d8bc7be90999", :string_id=> "CDS_PER_APP" },
  { :friendly_name=> "Power Apps per App Plan", :guid=> "b4f657ff-d83e-4053-909d-baa2b595ec97", :string_id=> "POWERAPPS_PER_APP" },
  { :friendly_name=> "Power Automate for Power Apps per App Plan", :guid=> "c539fa36-a64e-479a-82e1-e40ff2aa83ee", :string_id=> "Flow_Per_APP" },
  { :friendly_name=> "AI Builder capacity Per App add-on", :guid=> "5d7a2e9a-4ee5-4f1c-bc9f-abc481bf39d8", :string_id=> "CDSAICAPACITY_PERAPP" },
  { :friendly_name=> "Dataverse for Power Apps per app", :guid=> "6f0e9100-ff66-41ce-96fc-3d8b7ad26887", :string_id=> "DATAVERSE_POWERAPPS_PER_APP_NEW" },
  { :friendly_name=> "Power Apps per app", :guid=> "14f8dac2-0784-4daa-9cb2-6d670b088d64", :string_id=> "POWERAPPS_PER_APP_NEW" },
  { :friendly_name=> "Power Apps per User Plan", :guid=> "ea2cf03b-ac60-46ae-9c1d-eeaeb63cec86", :string_id=> "POWERAPPS_PER_USER" },
  { :friendly_name=> "Power Automate for Power Apps per User Plan", :guid=> "dc789ed8-0170-4b65-a415-eb77d5bb350a", :string_id=> "Flow_PowerApps_PerUser" },
  { :friendly_name=> "AI Builder capacity Per User add-on", :guid=> "91f50f7b-2204-4803-acac-5cf5668b8b39", :string_id=> "CDSAICAPACITY_PERUSER" },
  { :friendly_name=> "AI Builder capacity Per User add-on", :guid=> "74d93933-6f22-436e-9441-66d205435abb", :string_id=> "CDSAICAPACITY_PERUSER_NEW" },
  { :friendly_name=> "Common Data Service for Government", :guid=> "37396c73-2203-48e6-8be1-d882dae53275", :string_id=> "DYN365_CDS_P2_GOV" },
  { :friendly_name=> "Power Apps per User Plan for Government", :guid=> "8f55b472-f8bf-40a9-be30-e29919d4ddfe", :string_id=> "POWERAPPS_PER_USER_GCC" },
  { :friendly_name=> "Power Automate for Power Apps per User Plan for GCC", :guid=> "8e3eb3bd-bc99-4221-81b8-8b8bc882e128", :string_id=> "Flow_PowerApps_PerUser_GCC" },
  { :friendly_name=> "Common Data Service for Government", :guid=> "ce361df2-f2a5-4713-953f-4050ba09aad8", :string_id=> "DYN365_CDS_P1_GOV" },
  { :friendly_name=> "Power Automate (Plan 1) for Government", :guid=> "774da41c-a8b3-47c1-8322-b9c1ab68be9f", :string_id=> "FLOW_P1_GOV" },
  { :friendly_name=> "PowerApps Plan 1 for Government", :guid=> "5ce719f1-169f-4021-8a64-7d24dcaec15f", :string_id=> "POWERAPPS_P1_GOV" },
  { :friendly_name=> "Common Data Service Power Apps Portals Login Capacity", :guid=> "32ad3a4e-2272-43b4-88d0-80d284258208", :string_id=> "CDS_POWERAPPS_PORTALS_LOGIN" },
  { :friendly_name=> "Power Apps Portals Login Capacity Add-On", :guid=> "084747ad-b095-4a57-b41f-061d84d69f6f", :string_id=> "POWERAPPS_PORTALS_LOGIN" },
  { :friendly_name=> "Common Data Service Power Apps Portals Login Capacity for GCC", :guid=> "0f7b9a29-7990-44ff-9d05-a76be778f410", :string_id=> "CDS_POWERAPPS_PORTALS_LOGIN_GCC" },
  { :friendly_name=> "Power Apps Portals Login Capacity Add-On for Government", :guid=> "bea6aef1-f52d-4cce-ae09-bed96c4b1811", :string_id=> "POWERAPPS_PORTALS_LOGIN_GCC" },
  { :friendly_name=> "CDS PowerApps Portals page view capacity add-on", :guid=> "72c30473-7845-460a-9feb-b58f216e8694", :string_id=> "CDS_POWERAPPS_PORTALS_PAGEVIEW" },
  { :friendly_name=> "Power Apps Portals Page View Capacity Add-On", :guid=> "1c5a559a-ec06-4f76-be5b-6a315418495f", :string_id=> "POWERAPPS_PORTALS_PAGEVIEW" },
  { :friendly_name=> "CDS PowerApps Portals page view capacity add-on for GCC", :guid=> "352257a9-db78-4217-a29d-8b8d4705b014", :string_id=> "CDS_POWERAPPS_PORTALS_PAGEVIEW_GCC" },
  { :friendly_name=> "Power Apps Portals Page View Capacity Add-On for Government", :guid=> "483d5646-7724-46ac-ad71-c78b7f099d8d", :string_id=> "POWERAPPS_PORTALS_PAGEVIEW_GCC" },
  { :friendly_name=> "Common data service for Flow per business process plan", :guid=> "c84e52ae-1906-4947-ac4d-6fb3e5bf7c2e", :string_id=> "CDS_Flow_Business_Process" },
  { :friendly_name=> "Flow per business process plan", :guid=> "7e017b61-a6e0-4bdc-861a-932846591f6e", :string_id=> "FLOW_BUSINESS_PROCESS" },
  { :friendly_name=> "Flow per user plan", :guid=> "c5002c70-f725-4367-b409-f0eff4fee6c0", :string_id=> "FLOW_PER_USER" },
  { :friendly_name=> "Power Automate per User Plan for Government", :guid=> "769b8bee-2779-4c5a-9456-6f4f8629fd41", :string_id=> "FLOW_PER_USER_GCC" },
  { :friendly_name=> "Common Data Service Attended RPA", :guid=> "3da2fd4c-1bee-4b61-a17f-94c31e5cab93", :string_id=> "CDS_ATTENDED_RPA" },
  { :friendly_name=> "Power Automate RPA Attended", :guid=> "375cd0ad-c407-49fd-866a-0bff4f8a9a4d", :string_id=> "POWER_AUTOMATE_ATTENDED_RPA" },
  { :friendly_name=> "Common Data Service Unattended RPA", :guid=> "b475952f-128a-4a44-b82a-0b98a45ca7fb", :string_id=> "CDS_UNATTENDED_RPA" },
  { :friendly_name=> "Power Automate Unattended RPA add-on", :guid=> "0d373a98-a27a-426f-8993-f9a425ae99c5", :string_id=> "POWER_AUTOMATE_UNATTENDED_RPA" },
  { :friendly_name=> "Power BI (free)", :guid=> "2049e525-b859-401b-b2a0-e0a31c4b1fe4", :string_id=> "BI_AZURE_P0" },
  { :friendly_name=> "Power BI Premium P", :guid=> "9da49a6d-707a-48a1-b44a-53dcde5267f8", :string_id=> "PBI_PREMIUM_P1_ADDON" },
  { :friendly_name=> "Power BI Premium Per User", :guid=> "0bf3c642-7bb5-4ccc-884e-59d09df0266c", :string_id=> "BI_AZURE_P3" },
  { :friendly_name=> "Power Pages vTrial for Makers", :guid=> "6817d093-2d30-4249-8bd6-774f01efa78c", :string_id=> "POWER_PAGES_VTRIAL" },
  { :friendly_name=> "Common Data Service for Virtual Agent Base", :guid=> "0a0a23fa-fea1-4195-bb89-b4789cb12f7f", :string_id=> "CDS_VIRTUAL_AGENT_BASE" },
  { :friendly_name=> "Power Automate for Virtual Agent", :guid=> "4b81a949-69a1-4409-ad34-9791a6ec88aa", :string_id=> "FLOW_VIRTUAL_AGENT_BASE" },
  { :friendly_name=> "Virtual Agent Base", :guid=> "f6934f16-83d3-4f3b-ad27-c6e9c187b260", :string_id=> "VIRTUAL_AGENT_BASE" },
  { :friendly_name=> "Common Data Service", :guid=> "cb867b3c-7f38-4d0d-99ce-e29cd69812c8", :string_id=> "CDS_VIRTUAL_AGENT_USL" },
  { :friendly_name=> "Power Automate for Virtual Agent", :guid=> "82f141c9-2e87-4f43-8cb2-12d2701dc6b3", :string_id=> "FLOW_VIRTUAL_AGENT_USL" },
  { :friendly_name=> "Virtual Agent", :guid=> "1263586c-59a4-4ad0-85e1-d50bc7149501", :string_id=> "VIRTUAL_AGENT_USL" },
  { :friendly_name=> "Common Data Service for CCI Bots", :guid=> "cf7034ed-348f-42eb-8bbd-dddeea43ee81", :string_id=> "DYN365_CDS_CCI_BOTS" },
  { :friendly_name=> "Flow for CCI Bots", :guid=> "5d798708-6473-48ad-9776-3acc301c40af", :string_id=> "FLOW_CCI_BOTS" },
  { :friendly_name=> "Priva - Risk", :guid=> "f281fb1f-99a7-46ab-9edb-ffd74e260ed3", :string_id=> "PRIVACY_MANGEMENT_RISK" },
  { :friendly_name=> "Priva - Risk (Exchange)", :guid=> "ebb17a6e-6002-4f65-acb0-d386480cebc1", :string_id=> "PRIVACY_MANGEMENT_RISK_EXCHANGE" },
  { :friendly_name=> "Data Classification in Microsoft 365 - Company Level", :guid=> "5b96ffc4-3853-4cf4-af50-e38505080f6b", :string_id=> "MIP_S_EXCHANGE_CO" },
  { :friendly_name=> "Privacy Management - Subject Rights Request (1)", :guid=> "93d24177-c2c3-408a-821d-3d25dfa66e7a", :string_id=> "PRIVACY_MANGEMENT_DSR_EXCHANGE_1" },
  { :friendly_name=> "Privacy Management - Subject Rights Request (1 - Exchange)", :guid=> "07a4098c-3f2d-427f-bfe2-5889ed75dd7b", :string_id=> "PRIVACY_MANGEMENT_DSR_1" },
  { :friendly_name=> "Privacy Management - Subject Rights Request (10 - Exchange)", :guid=> "f0241705-7b44-4401-a6b6-7055062b5b03", :string_id=> "PRIVACY_MANGEMENT_DSR_EXCHANGE_10" },
  { :friendly_name=> "Privacy Management - Subject Rights Request (10)", :guid=> "74853901-d7a9-428e-895d-f4c8687a9f0b", :string_id=> "PRIVACY_MANGEMENT_DSR_10" },
  { :friendly_name=> "Privacy Management - Subject Rights Request", :guid=> "8bbd1fea-6dc6-4aef-8abc-79af22d746e4", :string_id=> "PRIVACY_MANGEMENT_DSR" },
  { :friendly_name=> "Privacy Management - Subject Rights Request (Exchange)", :guid=> "7ca7f875-98db-4458-ab1b-47503826dd73", :string_id=> "PRIVACY_MANGEMENT_DSR_EXCHANGE" },
  { :friendly_name=> "Privacy Management - Subject Rights Request (100 - Exchange)", :guid=> "5c221cec-2c39-435b-a1e2-7cdd7fac5913", :string_id=> "PRIVACY_MANGEMENT_DSR_EXCHANGE_100" },
  { :friendly_name=> "Privacy Management - Subject Rights Request (100)", :guid=> "500f440d-167e-4030-a3a7-8cd35421fbd8", :string_id=> "PRIVACY_MANGEMENT_DSR_100" },
  { :friendly_name=> "Project Online Essentials for Government", :guid=> "fdcb7064-f45c-46fa-b056-7e0e9fdf4bf3", :string_id=> "PROJECT_ESSENTIALS_GOV" },
  { :friendly_name=> "COMMON DATA SERVICE FOR PROJECT P1", :guid=> "a6f677b3-62a6-4644-93e7-2a85d240845e", :string_id=> "DYN365_CDS_FOR_PROJECT_P1" },
  { :friendly_name=> "POWER AUTOMATE FOR PROJECT P1", :guid=> "00283e6b-2bd8-440f-a2d5-87358e4c89a1", :string_id=> "Power_Automate_For_Project_P1" },
  { :friendly_name=> "PROJECT P1", :guid=> "4a12c688-56c6-461a-87b1-30d6f32136f9", :string_id=> "PROJECT_P1" },
  { :friendly_name=> "Common Data Service for Project", :guid=> "50554c47-71d9-49fd-bc54-42a2765c555c", :string_id=> "DYN365_CDS_PROJECT" },
  { :friendly_name=> "Flow for Project", :guid=> "fa200448-008c-4acb-abd4-ea106ed2199d", :string_id=> "FLOW_FOR_PROJECT" },
  { :friendly_name=> "Project P3", :guid=> "818523f5-016b-4355-9be8-ed6944946ea7", :string_id=> "PROJECT_PROFESSIONAL" },
  { :friendly_name=> "Project Online Service for Education", :guid=> "664a2fed-6c7a-468e-af35-d61740f0ec90", :string_id=> "SHAREPOINT_PROJECT_EDU" },
  { :friendly_name=> "Project P3 for Faculty", :guid=> "22572403-045f-432b-a660-af949c0a77b5", :string_id=> "PROJECT_PROFESSIONAL_FACULTY" },
  { :friendly_name=> "Project Online Desktop Client for Government", :guid=> "45c6831b-ad74-4c7f-bd03-7c2b3fa39067", :string_id=> "PROJECT_CLIENT_SUBSCRIPTION_GOV" },
  { :friendly_name=> "Project Online Service for Government", :guid=> "e57afa78-1f19-4542-ba13-b32cd4d8f472", :string_id=> "SHAREPOINT_PROJECT_GOV" },
  { :friendly_name=> "Rights Management Adhoc", :guid=> "7a39d7dd-e456-4e09-842a-0204ee08187b", :string_id=> "RMS_S_ADHOC" },
  { :friendly_name=> "IoT Intelligence Add-in Additional Machines", :guid=> "a5f38206-2f48-4d83-9957-525f4e75e9c0", :string_id=> "D365_IOTFORSCM_ADDITIONAL" },
  { :friendly_name=> "Iot Intelligence Add-in for D365 Supply Chain Management", :guid=> "83dd9619-c7d5-44da-9250-dc4ee79fff7e", :string_id=> "D365_IOTFORSCM" },
  { :friendly_name=> "Common Data Service for SharePoint Syntex", :guid=> "3069d530-e41b-421c-ad59-fb1001a23e11", :string_id=> "CDS_O365_E5_KM" },
  { :friendly_name=> "SharePoint Syntex", :guid=> "f00bd55e-1633-416e-97c0-03684e42bc42", :string_id=> "Intelligent_Content_Services" },
  { :friendly_name=> "SharePoint Syntex - SPO type", :guid=> "fd2e7f90-1010-487e-a11b-d2b1ae9651fc", :string_id=> "Intelligent_Content_Services_SPO_type" },
  { :friendly_name=> "MCOPSTN3", :guid=> "6b340437-d6f9-4dc5-8cc2-99163f7f83d6", :string_id=> "MCOPSTN3" },
  { :friendly_name=> "Meeting Room Managed Services", :guid=> "bdaa59a3-74fd-4137-981a-31d4f84eb8a0", :string_id=> "MMR_P1" },
  { :friendly_name=> "AUSTRALIA CALLING PLAN", :guid=> "7861360b-dc3b-4eba-a3fc-0d323a035746", :string_id=> "MCOPSTNEAU" },
  { :friendly_name=> "ONEDRIVE FOR BUSINESS BASIC FOR GOVERNMENT", :guid=> "98709c2e-96b5-4244-95f5-a0ebe139fb8a", :string_id=> "ONEDRIVE_BASIC_GOV" },
  { :friendly_name=> "VISIO DESKTOP APP FOR Government", :guid=> "f85945f4-7a55-4009-bc39-6a5f14a8eac1", :string_id=> "VISIO_CLIENT_SUBSCRIPTION_GOV" },
  { :friendly_name=> "VISIO WEB APP FOR GOVERNMENT", :guid=> "8a9ecb07-cfc0-48ab-866c-f83c4d911576", :string_id=> "VISIOONLINE_GOV" },
  { :friendly_name=> "Dataverse for PAD", :guid=> "59231cdf-b40d-4534-a93e-14d0cd31d27e", :string_id=> "DATAVERSE_FOR_POWERAUTOMATE_DESKTOP" },
  { :friendly_name=> "PAD for Windows", :guid=> "2d589a15-b171-4e61-9b5f-31d15eeb2872", :string_id=> "POWERAUTOMATE_DESKTOP_FOR_WIN" },
  { :friendly_name=> "Windows 365 Business 1 vCPU 2 GB 64 GB", :guid=> "3b98b912-1720-4a1e-9630-c9a41dbb61d8", :string_id=> "CPC_B_1C_2RAM_64GB" },
  { :friendly_name=> "Windows 365 Business 2 vCPU 4 GB 128 GB", :guid=> "1a13832e-cd79-497d-be76-24186f55c8b0", :string_id=> "CPC_B_2C_4RAM_128GB" },
  { :friendly_name=> "Windows 365 Business 2 vCPU 4 GB 256 GB", :guid=> "a0b1c075-51c9-4a42-b34c-308f3993bb7e", :string_id=> "CPC_B_2C_4RAM_256GB" },
  { :friendly_name=> "Windows 365 Business 2 vCPU 4 GB 64 GB", :guid=> "a790cd6e-a153-4461-83c7-e127037830b6", :string_id=> "CPC_B_2C_4RAM_64GB" },
  { :friendly_name=> "Windows 365 Business 2 vCPU, 8 GB, 128 GB", :guid=> "9d2eed2c-b0c0-4a89-940c-bc303444a41b", :string_id=> "CPC_SS_2" },
  { :friendly_name=> "Windows 365 Business 2 vCPU 8 GB 256 GB", :guid=> "1a3ef005-2ef6-434b-8be1-faa56c892854", :string_id=> "CPC_B_2C_8RAM_256GB" },
  { :friendly_name=> "Windows 365 Business 4 vCPU 16 GB 128 GB", :guid=> "1d4f75d3-a19b-49aa-88cb-f1ea1690b550", :string_id=> "CPC_B_4C_16RAM_128GB" },
  { :friendly_name=> "Windows 365 Business 4 vCPU 16 GB 256 GB", :guid=> "30f6e561-8805-41d0-80ce-f82698b72d7d", :string_id=> "CPC_B_4C_16RAM_256GB" },
  { :friendly_name=> "Windows 365 Business 4 vCPU 16 GB 512 GB", :guid=> "15499661-b229-4a1f-b0f9-bd5832ef7b3e", :string_id=> "CPC_B_4C_16RAM_512GB" },
  { :friendly_name=> "Windows 365 Business 8 vCPU 32 GB 128 GB", :guid=> "648005fc-b330-4bd9-8af6-771f28958ac0", :string_id=> "CPC_B_8C_32RAM_128GB" },
  { :friendly_name=> "Windows 365 Business 8 vCPU 32 GB 256 GB", :guid=> "d7a5113a-0276-4dc2-94f8-ca9f2c5ae078", :string_id=> "CPC_B_8C_32RAM_256GB" },
  { :friendly_name=> "Windows 365 Business 8 vCPU 32 GB 512 GB", :guid=> "4229a0b4-7f34-4835-b068-6dc8d10be57c", :string_id=> "CPC_B_8C_32RAM_512GB" },
  { :friendly_name=> "Windows 365 Enterprise 1 vCPU 2 GB 64 GB", :guid=> "86d70dbb-d4c6-4662-ba17-3014204cbb28", :string_id=> "CPC_E_1C_2GB_64GB" },
  { :friendly_name=> "Windows 365 Enterprise 2 vCPU 4 GB 64 GB", :guid=> "23a25099-1b2f-4e07-84bd-b84606109438", :string_id=> "CPC_E_2C_4GB_64GB" },
  { :friendly_name=> "Windows 365 Enterprise 2 vCPU 4 GB 128 GB", :guid=> "545e3611-3af8-49a5-9a0a-b7867968f4b0", :string_id=> "CPC_1" },
  { :friendly_name=> "Windows 365 Enterprise 2 vCPU 4 GB 256 GB", :guid=> "0d143570-9b92-4f57-adb5-e4efcd23b3bb", :string_id=> "CPC_E_2C_4GB_256GB" },
  { :friendly_name=> "Windows 365 Enterprise 2 vCPU 8 GB 128 GB", :guid=> "3efff3fe-528a-4fc5-b1ba-845802cc764f", :string_id=> "CPC_2" },
  { :friendly_name=> "Windows 365 Enterprise 2 vCPU 8 GB 256 GB", :guid=> "d3468c8c-3545-4f44-a32f-b465934d2498", :string_id=> "CPC_E_2C_8GB_256GB" },
  { :friendly_name=> "Windows 365 Enterprise 4 vCPU 16 GB 128 GB", :guid=> "2de9c682-ca3f-4f2b-b360-dfc4775db133", :string_id=> "CPC_E_4C_16GB_128GB" },
  { :friendly_name=> "Windows 365 Enterprise 4 vCPU 16 GB 256 GB", :guid=> "9ecf691d-8b82-46cb-b254-cd061b2c02fb", :string_id=> "CPC_E_4C_16GB_256GB" },
  { :friendly_name=> "Windows 365 Enterprise 4 vCPU 16 GB 512 GB", :guid=> "3bba9856-7cf2-4396-904a-00de74fba3a4", :string_id=> "CPC_E_4C_16GB_512GB" },
  { :friendly_name=> "Windows 365 Enterprise 8 vCPU 32 GB 128 GB", :guid=> "2f3cdb12-bcde-4e37-8529-e9e09ec09e23", :string_id=> "CPC_E_8C_32GB_128GB" },
  { :friendly_name=> "Windows 365 Enterprise 8 vCPU 32 GB 256 GB", :guid=> "69dc175c-dcff-4757-8389-d19e76acb45d", :string_id=> "CPC_E_8C_32GB_256GB" },
  { :friendly_name=> "Windows 365 Enterprise 8 vCPU 32 GB 512 GB", :guid=> "0e837228-8250-4047-8a80-d4a34ba11658", :string_id=> "CPC_E_8C_32GB_512GB" },
  { :friendly_name=> "Windows 365 Shared Use 2 vCPU 4 GB 64 GB", :guid=> "64981bdb-a5a6-4a22-869f-a9455366d5bc", :string_id=> "CPC_S_2C_4GB_64GB" },
  { :friendly_name=> "Windows 365 Shared Use 2 vCPU 4 GB 128 GB", :guid=> "51855c77-4d2e-4736-be67-6dca605f2b57", :string_id=> "CPC_S_2C_4GB_128GB" },
  { :friendly_name=> "Windows 365 Shared Use 2 vCPU 4 GB 256 GB", :guid=> "aa8fbe7b-695c-4c05-8d45-d1dddf6f7616", :string_id=> "CPC_S_2C_4GB_256GB" },
  { :friendly_name=> "Windows 365 Shared Use 2 vCPU 8 GB 128 GB", :guid=> "057efbfe-a95d-4263-acb0-12b4a31fed8d", :string_id=> "CPC_S_2C_8GB_128GB" },
  { :friendly_name=> "Windows 365 Shared Use 2 vCPU 8 GB 256 GB", :guid=> "50ef7026-6174-40ba-bff7-f0e4fcddbf65", :string_id=> "CPC_S_2C_8GB_256GB" },
  { :friendly_name=> "Windows 365 Shared Use 4 vCPU 16 GB 128 GB", :guid=> "dd3801e2-4aa1-4b16-a44b-243e55497584", :string_id=> "CPC_S_4C_16GB_128GB" },
  { :friendly_name=> "Windows 365 Shared Use 4 vCPU 16 GB 256 GB", :guid=> "2d1d344e-d10c-41bb-953b-b3a47521dca0", :string_id=> "CPC_S_4C_16GB_256GB" },
  { :friendly_name=> "Windows 365 Shared Use 4 vCPU 16 GB 512 GB", :guid=> "48b82071-99a5-4214-b493-406a637bd68d", :string_id=> "CPC_S_4C_16GB_512GB" },
  { :friendly_name=> "Windows 365 Shared Use 8 vCPU 32 GB 128 GB", :guid=> "e4dee41f-a5c5-457d-b7d3-c309986fdbb2", :string_id=> "CPC_S_8C_32GB_128GB" },
  { :friendly_name=> "Windows 365 Shared Use 8 vCPU 32 GB 256 GB", :guid=> "1e2321a0-f81c-4d43-a0d5-9895125706b8", :string_id=> "CPC_S_8C_32GB_256GB" },
  { :friendly_name=> "Windows 365 Shared Use 8 vCPU 32 GB 512 GB", :guid=> "fa0b4021-0f60-4d95-bf68-95036285282a", :string_id=> "CPC_S_8C_32GB_512GB" },
  { :friendly_name=> "Windows Store for Business EDU Store_faculty", :guid=> "aaa2cd24-5519-450f-a1a0-160750710ca1", :string_id=> "Windows Store for Business EDU Store_faculty" },
  { :friendly_name=> "Microsoft Workplace Analytics", :guid=> "f477b0f0-3bb1-4890-940c-40fcee6ce05f", :string_id=> "WORKPLACE_ANALYTICS" }
]

service_informations.each do |attr|
  service_info = Integrations::Microsoft::MicrosoftServiceInformation.new(attr)
  service_info.save!
end
Integrations::Microsoft::MicrosoftServiceInformation.where('updated_at < ?', start_time).destroy_all
