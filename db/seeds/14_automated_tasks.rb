if Rails.env.development?
  AutomatedTasks::AutomatedTask.destroy_all
  AutomatedTasks::EventType.destroy_all
  AutomatedTasks::EventSubjectType.destroy_all
  AutomatedTasks::TaskAction.destroy_all
  AutomatedTasks::ActionType.destroy_all
end

##
# Old Seeds
=begin
AutomatedTasks::EventType.find_or_create_by(name: "{a ticket} is created",
                            module: 'help_tickets',
                            model: 'HelpTicket',
                            event_class: 'TicketAdded',
                            icon: 'nulodgicon-ios-copy-outline')

event_type = AutomatedTasks::EventType.find_by(name: "{a comment} is added to a ticket",
                                               module: 'help_tickets')
event_type&.update(model: 'HelpTicketComment')
AutomatedTasks::EventType.find_or_create_by(name: "{a comment} is added to a ticket",
                            module: 'help_tickets',
                            model: 'HelpTicketComment',
                            event_class: 'CommentAdded',
                            icon: 'genuicon-comment-o')
AutomatedTasks::EventType.find_or_create_by(name: "{an agent} is assigned to a ticket") do |event_type|
  event_type.module = 'help_tickets'
  event_type.model = 'HelpTicketAssignment'
  event_type.event_class = 'TicketAssigned'
  event_type.icon = 'genuicon-ios-person-outline'
end
AutomatedTasks::EventType.find_or_create_by(name: "{a priority} is set to a ticket",
                            module: 'help_tickets',
                            model: 'HelpTicket',
                            event_class: 'TicketPrioritized',
                            icon: 'genuicon-exclamation')
event_type = AutomatedTasks::EventType.find_by(name: "{a task} is added to a ticket")
event_type.update!(name: "{a project task} is added to a ticket") if event_type
AutomatedTasks::EventType.find_or_create_by(name: "{a project task} is added to a ticket",
                            module: 'help_tickets',
                            model: 'ProjectTask',
                            event_class: 'TaskAdded',
                            icon: 'genuicon-clipboard')
AutomatedTasks::EventType.find_or_create_by(name: "{a time entry} is added to a ticket",
                            module: 'help_tickets',
                            model: 'TimeSpent',
                            event_class: 'TimeEntryAdded',
                            icon: 'genuicon-clock-o')

# ticket details
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "any ticket",
                              key: "ticket",
                              subject_class: 'AnyTicket',
                              icon: "nulodgicon-ios-copy-outline")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a ticket with [a subject]",
                              key: "ticket",
                              subject_class: 'TicketSubject',
                              icon: "nulodgicon-ios-copy-outline")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a ticket with [a description]",
                              key: "ticket",
                              subject_class: 'TicketDescription',
                              icon: "nulodgicon-ios-list-outline")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a ticket created by [a user or group]",
                              key: "ticket",
                              subject_class: 'TicketUser',
                              icon: "nulodgicon-person")

subject = AutomatedTasks::ObjectSubjectType.find_by(name: "a ticket from [an email]")
subject.update(name: "a ticket from [an email address]") if subject

AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a ticket from [an email address]",
                              key: "ticket",
                              subject_class: 'TicketEmail',
                              icon: "nulodgicon-ios-email-outline")

AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a comment includes a mention to [a user or group]",
                               key: "comment",
                               subject_class: "CommentUserRef",
                               icon: "nulodgicon-person")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a comment with [text]",
                               key: "comment",
                               subject_class: "CommentText",
                               icon: 'genuicon-comment-o')
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a comment that references [a contract]",
                               key: 'comment',
                               subject_class: "CommentContractRef",
                               icon: 'genuicon-envelope-o')
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a comment that references [an asset]",
                               key: 'comment',
                               subject_class: 'CommentAssetRef',
                               icon: "genuicon-laptop")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a comment that references [a vendor]",
                               key: 'comment',
                               subject_class: 'CommentVendorRef',
                               icon: 'genuicon-envelope-o')

subject = AutomatedTasks::ObjectSubjectType.find_by(name: "an agent is or includes [user or group]")
subject.destroy if subject
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "an agent is or includes [a user or group]",
                               key: 'agent',
                               subject_class: 'AgentIncluded',
                               icon: 'genuicon-envelope-o')

subject = AutomatedTasks::ObjectSubjectType.find_by(name: "an agent is not and/or excludes [a user or group]")
subject.update!(name: "an agent is not or excludes [a user or group]") if subject

AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "an agent is not or excludes [a user or group]",
                                                    key: 'agent',
                                                    subject_class: 'AgentExcluded',
                                                    icon: 'genuicon-envelope-o')

subject = AutomatedTasks::ObjectSubjectType.find_by(name: "the same [priority] ")
subject.update!(name: "a priority that is equal to [a priority]") if subject
subject = AutomatedTasks::ObjectSubjectType.find_by(name: "a [priority] or lower")
subject.update!(name: "a priority that is equal to or lower than [a priority]") if subject
subject = AutomatedTasks::ObjectSubjectType.find_by(name: "a [priority] or higher")
subject.update!(name: "a priority that is equal to or higher than [a priority]") if subject

AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a priority that is equal to [a priority]",
                               key: 'priority',
                               subject_class: 'PriorityEquals',
                               icon: 'genuicon-envelope-o')
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a priority that is equal to or lower than [a priority]",
                               key: 'priority',
                               subject_class: 'PriorityLower',
                               icon: 'genuicon-envelope-o')
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a priority that is equal to or higher than [a priority]",
                               key: 'priority',
                               subject_class: 'PriorityHigher',
                               icon: 'genuicon-envelope-o')

subject = AutomatedTasks::ObjectSubjectType.find_by(name: "a task is created with a [description]")
subject.update!(name: "a project task is created with a [description]",
                key: "project task") if subject

subject = AutomatedTasks::ObjectSubjectType.find_by(name: "a task assignment is or contains [a user or group]")
subject.update!(name: "a project task assignment is or contains [a user or group]",
                key: "project task") if subject

AutomatedTasks::ObjectSubjectType.find_or_create_by(
                               name: "a project task is created with a [description]",
                               key: 'project task',
                               subject_class: 'TaskDescription',
                               icon: 'genuicon-envelope-o')
AutomatedTasks::ObjectSubjectType.find_or_create_by(
                               name: "a project task assignment is or contains [a user or group]",
                               key: 'project task',
                               subject_class: 'TaskAssignment',
                               icon: 'genuicon-envelope-o')

AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a time entry exceed [duration]",
                               key: 'time entry',
                               subject_class: 'EntryDurationExceeds',
                               icon: 'genuicon-envelope-o')
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a time entry is less than [duration]",
                               key: 'time entry',
                               subject_class: 'EntryDurationLessThan',
                               icon: 'genuicon-envelope-o')
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a time entry is created by or includes [a user or group]",
                               key: 'time entry',
                               subject_class: 'EntryCreatedBy',
                               icon: 'genuicon-envelope-o')

# ticket details
AutomatedTasks::SimpleSubjectType.find_or_create_by(name: "any /string/",
                             subject_class: "AnyString",
                             key: "string",)
AutomatedTasks::SimpleSubjectType.find_or_create_by(name: "/a string/ contains [text]",
                             subject_class: "StringContains",
                             key: "string")
AutomatedTasks::SimpleSubjectType.find_or_create_by(name: "/a string/ equals [text]",
                             subject_class: "StringEquals",
                             key: "string")
AutomatedTasks::SimpleSubjectType.find_or_create_by(name: "/a string/ does not equal [text]",
                             subject_class: "StringNotEquals",
                             key: "string")
AutomatedTasks::SimpleSubjectType.find_or_create_by(name: "/a string/ does not contain [text]",
                             subject_class: "StringNotContains",
                             key: "string")

subject = AutomatedTasks::SimpleSubjectType.find_by(name: "/an email/ equals [text]")
if subject
  subject.key = 'email address'
  subject.name  = "/an email address/ equals [text]"
  subject.save!
end

def add_strings(name, parent)
  options = [
    {
      name: "any #{name}",
      subject_class: "AnyString",
    },
    {
      name: "/#{name}/ that contains [text]",
      subject_class: "StringContains",
    },
    {
      name: "/#{name}/ that equals [text]",
      subject_class: "StringEquals",
    },
    {
      name: "/#{name}/ that does not equal [text]",
      subject_class: "StringNotEquals",
    },
  ]
  options.each do |option|
    AutomatedTasks::SimpleSubjectType.find_or_create_by(option.merge(key: "string", parent: parent))
  end
end

def add_emails(parent)
  options = [
    {
      name: "/email address/ that equals [text]",
      subject_class: "EmailEquals",
    },
    {
      name: "/email address/ domain that equals [text]",
      subject_class: 'EmailDomainEquals',
    },
    {
      name: "/email address/ that does not equal [text]",
      subject_class: "EmailNotEquals",
    },
    {
      name: "/email address/ domain that does not match [text]",
      subject_class: 'EmailDomainNotEquals',
    },
  ]
  options.each do |option|
    type = AutomatedTasks::SimpleSubjectType.find_or_create_by(option.merge(key: "email address", parent: parent))
    type.save!
  end
end

ticket_created = AutomatedTasks::EventType.find_or_create_by(
    name: "{a ticket} is created",
    module: 'help_tickets',
    model: 'HelpTicket',
    event_class: 'TicketAdded',
    icon: 'nulodgicon-ios-copy-outline'
)
any_ticket = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "any ticket",
    key: "ticket",
    subject_class: 'AnyTicket',
    icon: "nulodgicon-ios-copy-outline",
    parent: ticket_created,
)
by_user = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a ticket/ that is created by [a user or group]",
    key: "ticket",
    subject_class: 'TicketUser',
    icon: "nulodgicon-person",
    parent: ticket_created,
)
from_email = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a ticket/ that is created from [an email address]",
    key: "ticket",
    subject_class: 'TicketEmail',
    icon: "nulodgicon-ios-email-outline",
    parent: ticket_created,
)
add_emails(from_email)
form_field = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a ticket/ with {a form field}",
    key: 'ticket',
    subject_class: 'TicketFormField',
    parent: ticket_created
)

any_form_value = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a form field/ with any form value",
    key: 'form field',
    subject_class: 'FormFieldAny',
    parent: form_field
)
form_value_equal = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a form field/ that does contain [a form value]",
    key: 'form field',
    subject_class: 'FormValueEqual',
    parent: form_field
)
form_value_not_equal = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a form field/ that doesn't contain [a form value]",
    key: 'form field',
    subject_class: 'FormValueNotEqual',
    parent: form_field
)

ticket_changed = AutomatedTasks::EventType.find_or_create_by(
    name: "{a ticket} is updated",
    module: 'help_tickets',
    model: 'HelpTicket',
    event_class: 'TicketFieldChanged'
)
any_ticket = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "any ticket",
    key: "ticket",
    subject_class: 'AnyTicket',
    icon: "nulodgicon-ios-copy-outline",
    parent: ticket_changed
)
by_user = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a ticket/ which is updated by [a user or group]",
    key: "ticket",
    subject_class: 'TicketUser',
    icon: "nulodgicon-person",
    parent: ticket_changed
)
from_email = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a ticket/ which is updated by [an email address]",
    key: "ticket",
    subject_class: 'TicketEmail',
    icon: "nulodgicon-ios-email-outline",
    parent: ticket_changed
)
add_emails(from_email)

form_field = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a ticket/ with {a form field}",
    key: 'ticket',
    subject_class: 'TicketFormField',
    parent: ticket_changed
)

form_value_equal = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a form field/ with any form value",
    key: 'form field',
    subject_class: 'AnyObject',
    parent: form_field
)
form_value_equal = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a form field/ that does contain [a form value]",
    key: 'form field',
    subject_class: 'FormValueEqual',
    parent: form_field
)
form_value_not_equal = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a form field/ that doesn't contain [a form value]",
    key: 'form field',
    subject_class: 'FormValueNotEqual',
    parent: form_field
)

comment_created = AutomatedTasks::EventType.find_or_create_by(
    name: "{a comment} is added to a ticket",
    module: 'help_tickets',
    model: 'HelpTicketComment',
    event_class: 'CommentAdded',
    icon: 'genuicon-comment-o'
)

any_commment = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "any comment",
    key: "comment",
    subject_class: "AnyObject",
    icon: "nulodgicon-person",
    parent: comment_created,
)
with_user = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a comment/ which includes a mention to [a user or group]",
    key: "comment",
    subject_class: "CommentUserRef",
    icon: "nulodgicon-person",
    parent: comment_created,
)

with_text = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a comment/ which includes [text]",
    key: "comment",
    subject_class: "CommentText",
    icon: 'genuicon-comment-o',
    parent: comment_created,
)

with_contract = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a comment/ that references [a contract]",
    key: 'comment',
    subject_class: "CommentContractRef",
    icon: 'genuicon-envelope-o',
    parent: comment_created,
)

with_asset = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a comment/ that references [an asset]",
    key: 'comment',
    subject_class: 'CommentAssetRef',
    icon: "genuicon-laptop",
    parent: comment_created,
)

with_vendor = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a comment/ that references [a vendor]",
    key: 'comment',
    subject_class: 'CommentVendorRef',
    icon: 'genuicon-envelope-o',
    parent: comment_created,
)

task_created = AutomatedTasks::EventType.find_or_create_by(
    name: "{a project task} is added to a ticket",
    module: 'help_tickets',
    model: 'ProjectTask',
    event_class: 'TaskAdded',
    icon: 'genuicon-clipboard'
)
any_task = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "any project task",
    key: 'project task',
    subject_class: 'AnyObject',
    icon: 'genuicon-envelope-o',
    parent: task_created,
)
with_description = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a project task/ that has a [description]",
    key: 'project task',
    subject_class: 'TaskDescription',
    icon: 'genuicon-envelope-o',
    parent: task_created,
)
with_user = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a project task/ that is assigned to [a user or group]",
    key: 'project task',
    subject_class: 'TaskAssignment',
    icon: 'genuicon-envelope-o',
    parent: task_created,
)

entry_created = AutomatedTasks::EventType.find_or_create_by(
    name: "{a time entry} is added to a ticket",
    module: 'help_tickets',
    model: 'TimeSpent',
    event_class: 'TimeEntryAdded',
    icon: 'genuicon-android-time'
)
exceed_duration = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "any time entry",
    key: 'time entry',
    subject_class: 'AnyObject',
    icon: 'genuicon-envelope-o',
    parent: entry_created,
)
exceed_duration = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a time entry/ that exceeds [duration]",
    key: 'time entry',
    subject_class: 'EntryDurationExceeds',
    icon: 'genuicon-envelope-o',
    parent: entry_created,
)
less_duration = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a time entry/ that is less than [duration]",
    key: 'time entry',
    subject_class: 'EntryDurationLessThan',
    icon: 'genuicon-envelope-o',
    parent: entry_created,
)
includes_user = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a time entry/ which is created by or includes [a user or group]",
    key: 'time entry',
    subject_class: 'EntryCreatedBy',
    icon: 'genuicon-envelope-o',
    parent: entry_created,
)

###
#
agent_assigned = AutomatedTasks::EventType.find_or_create_by(
  name: "{an agent} is assigned to a ticket",
  module: 'help_tickets',
  model: 'HelpTicketAssignment',
  event_class: 'TicketAssigned',
  icon: 'genuicon-ios-person-outline'
)
any_assignment = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "any agent",
    key: 'agent',
    subject_class: 'AnyObject',
    icon: 'genuicon-envelope-o',
    parent: agent_assigned,
)
includes_user = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/an agent/ that is or includes [a user or group]",
    key: 'agent',
    subject_class: 'AgentIncluded',
    icon: 'genuicon-envelope-o',
    parent: agent_assigned,
)
excludes_user = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/an agent/ that is not or excludes [a user or group]",
    key: 'agent',
    subject_class: 'AgentExcluded',
    icon: 'genuicon-envelope-o',
    parent: agent_assigned,
)

###
#
attachment_added = AutomatedTasks::EventType.find_or_create_by(
  name: "{an attachment} is added to a ticket",
  module: 'help_tickets',
  model: 'HelpTicketAttachment',
  event_class: 'HelpTicketAttachmentAdded',
  icon: 'genuicon-ios-person-outline'
)
any_assignment = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "any attachment",
    key: 'attachment',
    subject_class: 'AnyObject',
    icon: 'genuicon-envelope-o',
    parent: attachment_added,
)
includes_user = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/an attachment/ that filename includes [text]",
    key: 'attachment',
    subject_class: 'AttachmentIncluded',
    icon: 'genuicon-envelope-o',
    parent: attachment_added,
)
includes_user = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/an attachment/ that filename excludes [text]",
    key: 'attachment',
    subject_class: 'AttachmentExcluded',
    icon: 'genuicon-envelope-o',
    parent: attachment_added,
)

AutomatedTasks::ActionType.find_or_create_by(name: 'send an [email]',
                               module: 'help_tickets',
                               model: 'HelpTicket',
                               action_class: 'SendEmail',
                               icon: 'genuicon-envelope-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'assign a [priority]',
                               module: 'help_tickets',
                               model: 'HelpTicket',
                               action_class: 'AssignPriority',
                               icon: 'genuicon-flag-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'set [status] for a ticket',
                               module: 'help_tickets',
                               model: 'HelpTicket',
                               action_class: 'SetStatus',
                               icon: 'genuicon-flag-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'add a [comment]',
                               module: 'help_tickets',
                               model: 'HelpTicket',
                               action_class: 'AddComment',
                               icon: 'genuicon-comment-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'assign an [agent]',
                               module: 'help_tickets',
                               model: 'HelpTicket',
                               action_class: 'AssignAgent',
                               icon: 'genuicon-person-add')
AutomatedTasks::ActionType.find_or_create_by(name: 'create an [alert]',
                               module: 'help_tickets',
                               model: 'HelpTicket',
                               action_class: 'AddAlert',
                               icon: 'genuicon-alerts')
AutomatedTasks::ActionType.find_or_create_by(name: 'send a [sms]',
                               module: 'help_tickets',
                               model: 'HelpTicket',
                               action_class: 'SendSms',
                               icon: 'genuicon-clock-o')

AutomatedTasks::ActionType.find_or_create_by(name: 'send an [email]',
                               module: 'help_tickets',
                               model: 'HelpTicketComment',
                               action_class: 'SendEmail',
                               icon: 'genuicon-envelope-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'assign a [priority]',
                               module: 'help_tickets',
                               model: 'HelpTicketComment',
                               action_class: 'AssignPriority',
                               icon: 'genuicon-flag-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'set [status] for a ticket',
                               module: 'help_tickets',
                               model: 'HelpTicketComment',
                               action_class: 'SetStatus',
                               icon: 'genuicon-flag-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'add a [comment]',
                               module: 'help_tickets',
                               model: 'HelpTicketComment',
                               action_class: 'AddComment',
                               icon: 'genuicon-comment-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'assign an [agent]',
                               module: 'help_tickets',
                               model: 'HelpTicketComment',
                               action_class: 'AssignAgent',
                               icon: 'genuicon-person-add')
AutomatedTasks::ActionType.find_or_create_by(name: 'create an [alert]',
                               module: 'help_tickets',
                               model: 'HelpTicketComment',
                               action_class: 'AddAlert',
                               icon: 'genuicon-alerts')
AutomatedTasks::ActionType.find_or_create_by(name: 'send a [sms]',
                               module: 'help_tickets',
                               model: 'HelpTicketComment',
                               action_class: 'SendSms',
                               icon: 'genuicon-clock-o')

AutomatedTasks::ActionType.find_or_create_by(name: 'send an [email]',
                               module: 'help_tickets',
                               model: 'HelpTicketActivity',
                               action_class: 'SendEmail',
                               icon: 'genuicon-envelope-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'assign a [priority]',
                               module: 'help_tickets',
                               model: 'HelpTicketActivity',
                               action_class: 'AssignPriority',
                               icon: 'genuicon-flag-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'set [status] for a ticket',
                               module: 'help_tickets',
                               model: 'HelpTicketActivity',
                               action_class: 'SetStatus',
                               icon: 'genuicon-flag-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'assign an [agent]',
                               module: 'help_tickets',
                               model: 'HelpTicketActivity',
                               action_class: 'AssignAgent',
                               icon: 'genuicon-person-add')
AutomatedTasks::ActionType.find_or_create_by(name: 'create an [alert]',
                               module: 'help_tickets',
                               model: 'HelpTicketActivity',
                               action_class: 'AddAlert',
                               icon: 'genuicon-alerts')
AutomatedTasks::ActionType.find_or_create_by(name: 'send a [sms]',
                               module: 'help_tickets',
                               model: 'HelpTicketActivity',
                               action_class: 'SendSms',
                               icon: 'genuicon-clock-o')

AutomatedTasks::ActionType.find_or_create_by(name: 'send an [email]',
                               module: 'help_tickets',
                               model: 'ProjectTask',
                               action_class: 'SendEmail',
                               icon: 'genuicon-envelope-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'assign a [priority]',
                               module: 'help_tickets',
                               model: 'ProjectTask',
                               action_class: 'AssignPriority',
                               icon: 'genuicon-flag-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'set [status] for a ticket',
                               module: 'help_tickets',
                               model: 'ProjectTask',
                               action_class: 'SetStatus',
                               icon: 'genuicon-flag-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'assign an [agent]',
                               module: 'help_tickets',
                               model: 'ProjectTask',
                               action_class: 'AssignAgent',
                               icon: 'genuicon-person-add')
AutomatedTasks::ActionType.find_or_create_by(name: 'create an [alert]',
                               module: 'help_tickets',
                               model: 'ProjectTask',
                               action_class: 'AddAlert',
                               icon: 'genuicon-alerts')
AutomatedTasks::ActionType.find_or_create_by(name: 'send a [sms]',
                               module: 'help_tickets',
                               model: 'ProjectTask',
                               action_class: 'SendSms',
                               icon: 'genuicon-clock-o')

AutomatedTasks::ActionType.find_or_create_by(name: 'send an [email]',
                               module: 'help_tickets',
                               model: 'HelpTicketAssignment',
                               action_class: 'SendEmail',
                               icon: 'genuicon-envelope-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'assign a [priority]',
                               module: 'help_tickets',
                               model: 'HelpTicketAssignment',
                               action_class: 'AssignPriority',
                               icon: 'genuicon-flag-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'set [status] for a ticket',
                               module: 'help_tickets',
                               model: 'HelpTicketAssignment',
                               action_class: 'SetStatus',
                               icon: 'genuicon-flag-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'add a [comment]',
                               module: 'help_tickets',
                               model: 'HelpTicketAssignment',
                               action_class: 'AddComment',
                               icon: 'genuicon-comment-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'create an [alert]',
                               module: 'help_tickets',
                               model: 'HelpTicketAssignment',
                               action_class: 'AddAlert',
                               icon: 'genuicon-alerts')
AutomatedTasks::ActionType.find_or_create_by(name: 'send a [sms]',
                               module: 'help_tickets',
                               model: 'HelpTicketAssignment',
                               action_class: 'SendSms',
                               icon: 'genuicon-clock-o')

AutomatedTasks::ActionType.find_or_create_by(name: 'send an [email]',
                               module: 'help_tickets',
                               model: 'TimeSpent',
                               action_class: 'SendEmail',
                               icon: 'genuicon-envelope-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'assign a [priority]',
                               module: 'help_tickets',
                               model: 'TimeSpent',
                               action_class: 'AssignPriority',
                               icon: 'genuicon-flag-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'set [status] for a ticket',
                               module: 'help_tickets',
                               model: 'TimeSpent',
                               action_class: 'SetStatus',
                               icon: 'genuicon-flag-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'assign an [agent]',
                               module: 'help_tickets',
                               model: 'TimeSpent',
                               action_class: 'AssignAgent',
                               icon: 'genuicon-person-add')
AutomatedTasks::ActionType.find_or_create_by(name: 'create an [alert]',
                               module: 'help_tickets',
                               model: 'TimeSpent',
                               action_class: 'AddAlert',
                               icon: 'genuicon-alerts')
AutomatedTasks::ActionType.find_or_create_by(name: 'send a [sms]',
                               module: 'help_tickets',
                               model: 'TimeSpent',
                               action_class: 'SendSms',
                               icon: 'genuicon-clock-o')

AutomatedTasks::ActionType.find_or_create_by(name: 'send an [email]',
                               module: 'help_tickets',
                               model: 'HelpTicketAttachment',
                               action_class: 'SendEmail',
                               icon: 'genuicon-envelope-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'assign a [priority]',
                               module: 'help_tickets',
                               model: 'HelpTicketAttachment',
                               action_class: 'AssignPriority',
                               icon: 'genuicon-exclamation-circle')
AutomatedTasks::ActionType.find_or_create_by(name: 'set [status] for a ticket',
                               module: 'help_tickets',
                               model: 'HelpTicketAttachment',
                               action_class: 'SetStatus',
                               icon: 'genuicon-flag-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'assign an [agent]',
                               module: 'help_tickets',
                               model: 'HelpTicketAttachment',
                               action_class: 'AssignAgent',
                               icon: 'genuicon-clock-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'create an [alert]',
                               module: 'help_tickets',
                               model: 'HelpTicketAttachment',
                               action_class: 'AddAlert',
                               icon: 'genuicon-clock-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'send a [sms]',
                               module: 'help_tickets',
                               model: 'HelpTicketAttachment',
                               action_class: 'SendSms',
                               icon: 'genuicon-clock-o')

=begin
AutomatedTasks::EventType.find_or_create_by(name: "{a contract} is created",
                            module: 'contracts',
                            model: 'Contract',
                            event_class: 'ContractAdded',
                            icon: 'nulodgicon-ios-copy-outline')
AutomatedTasks::EventType.find_or_create_by(name: "{an alert date} is added to a contract",
                            module: 'contracts',
                            model: 'AlertDate',
                            event_class: 'AlertDateAdded',
                            icon: 'genuicon-comment-o')
AutomatedTasks::EventType.find_or_create_by(name: "[a user or group] is added to a contract",
                            module: 'contracts',
                            model: 'Contract',
                            event_class: 'ContractUserAdded',
                            icon: 'genuicon-ios-person-outline')
AutomatedTasks::EventType.find_or_create_by(name: "{an attachment} is added to a contract",
                            module: 'contracts',
                            model: 'Contract',
                            event_class: 'ContractAttachmentAdded',
                            icon: 'genuicon-ios-person-outline')

AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "any contract",
                              key: "contract",
                              subject_class: 'AnyContract',
                              icon: "nulodgicon-ios-copy-outline")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a contract with [a name]",
                              key: "contract",
                              subject_class: 'ConractName',
                              icon: "nulodgicon-ios-copy-outline")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a contract with [a note]",
                              key: "contract",
                              subject_class: 'ContractNote',
                              icon: "nulodgicon-ios-list-outline")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a contract with {a location}",
                              key: "contract",
                              subject_class: 'ContractLocation',
                              icon: "nulodgicon-person")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a contract with [a category]",
                              key: "contract",
                              subject_class: 'ContractLocation',
                              icon: "nulodgicon-ios-email-outline")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a contract with [a monthly cost]",
                              key: "contract",
                              subject_class: 'ContractMonthlyCost',
                              icon: "nulodgicon-ios-email-outline")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a contract with [a total cost]",
                              key: "contract",
                              subject_class: 'ContractTotalCost',
                              icon: "nulodgicon-ios-email-outline")

AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a location from [a state]",
                               key: 'location',
                               subject_class: 'LocationState',
                               icon: 'genuicon-envelope-o')
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a location from [a country]",
                               key: 'location',
                               subject_class: 'LocationCountry',
                               icon: 'genuicon-envelope-o')
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a location from [a city]",
                               key: 'location',
                               subject_class: 'LocationCity',
                               icon: 'genuicon-envelope-o')
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a location from [a zip code]",
                               key: 'location',
                               subject_class: 'LocationZip',
                               icon: 'genuicon-envelope-o')

AutomatedTasks::EventType.find_or_create_by(name: "{a telecom provider} is created",
                            module: 'telecom',
                            model: 'TelecomProvider',
                            event_class: 'TelecomProviderAdded',
                            icon: 'nulodgicon-ios-copy-outline')
AutomatedTasks::EventType.find_or_create_by(name: "{a telecom provider} is recommended",
                            module: 'telecom',
                            model: 'TelecomProvider',
                            event_class: 'TelecomProviderRecommended',
                            icon: 'nulodgicon-ios-copy-outline')
AutomatedTasks::EventType.find_or_create_by(name: "{a telecom provider} is discovered",
                            module: 'telecom',
                            model: 'TelecomProvider',
                            event_class: 'TelecomProviderDiscovered',
                            icon: 'nulodgicon-ios-copy-outline')
AutomatedTasks::EventType.find_or_create_by(name: "{a telecom service} is created",
                            module: 'telecom',
                            model: 'TelecomProvider',
                            event_class: 'TelecomServiceAdded',
                            icon: 'nulodgicon-ios-copy-outline')
AutomatedTasks::EventType.find_or_create_by(name: "{an ip address} is created",
                            module: 'telecom',
                            model: 'IpAddress',
                            event_class: 'IpAddressAdded',
                            icon: 'genuicon-comment-o')
AutomatedTasks::EventType.find_or_create_by(name: "{a phone number} is created",
                            module: 'telecom',
                            model: 'PhoneNumber',
                            event_class: 'PhoneNumberAdded',
                            icon: 'genuicon-ios-person-outline')

AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "any telecom provider",
                              key: "telecom provider",
                              subject_class: 'AnyProvider',
                              icon: "nulodgicon-ios-copy-outline")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a telecom provider with [a name]",
                              key: "telecom provider",
                              subject_class: 'ProviderName',
                              icon: "nulodgicon-ios-copy-outline")

AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "any telecom service",
                              key: "telecom service",
                              subject_class: 'AnyTelecomService',
                              icon: "nulodgicon-ios-list-outline")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a telecom service with {a name}",
                              key: "telecom service",
                              subject_class: 'TelecomServiceName',
                              icon: "nulodgicon-person")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a telecom service with {a note}",
                              key: "telecom service",
                              subject_class: 'TelecomServiceNote',
                              icon: "nulodgicon-person")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a telecom service with {a service category}",
                              key: "telecom service",
                              subject_class: 'TelecomServiceCategory',
                              icon: "nulodgicon-person")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a telecom service with {a location}",
                              key: "telecom service",
                              subject_class: 'TelecomServiceLocation',
                              icon: "nulodgicon-person")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a telecom service with {a monthly cost}",
                              key: "telecom service",
                              subject_class: 'TelecomServiceMonthlyCost',
                              icon: "nulodgicon-person")

AutomatedTasks::EventType.find_or_create_by(name: "{an asset} is created",
                            module: 'assets',
                            model: 'ManagedAsset',
                            event_class: 'AssetAdded',
                            icon: 'nulodgicon-ios-copy-outline')
AutomatedTasks::EventType.find_or_create_by(name: "{a discovered asset} is created",
                            module: 'assets',
                            model: 'DiscoveredAsset',
                            event_class: 'AssetDiscovered',
                            icon: 'nulodgicon-ios-copy-outline')
AutomatedTasks::EventType.find_or_create_by(name: "{an asset software} is created",
                            module: 'assets',
                            model: 'AssetSoftware',
                            event_class: 'AssetSoftwareAdded',
                            icon: 'genuicon-comment-o')
AutomatedTasks::EventType.find_or_create_by(name: "{a phone number} is created",
                            module: 'assets',
                            model: 'PhoneNumber',
                            event_class: 'PhoneNumberAdded',
                            icon: 'genuicon-ios-person-outline')
=end

AutomatedTasks::ValueType.find_or_create_by(name: 'text', key: 'text')
AutomatedTasks::ValueType.find_or_create_by(name: 'category', key: 'category')
AutomatedTasks::ValueType.find_or_create_by(name: 'service category', key: 'service category')
AutomatedTasks::ValueType.find_or_create_by(name: 'city', key: 'city')
AutomatedTasks::ValueType.find_or_create_by(name: 'zip code', key: 'zip code')
AutomatedTasks::ValueType.find_or_create_by(name: 'state', key: 'state')
AutomatedTasks::ValueType.find_or_create_by(name: 'cost', key: 'cost')
AutomatedTasks::ValueType.find_or_create_by(name: 'user or group', key: 'user or group')
AutomatedTasks::ValueType.find_or_create_by(name: 'duration', key: 'duration')
AutomatedTasks::ValueType.find_or_create_by(name: 'priority', key: 'priority')
AutomatedTasks::ValueType.find_or_create_by(name: 'status', key: 'status')
AutomatedTasks::ValueType.find_or_create_by(name: 'contract', key: 'contract')
AutomatedTasks::ValueType.find_or_create_by(name: 'asset', key: 'asset')
AutomatedTasks::ValueType.find_or_create_by(name: 'vendor', key: 'vendor')
AutomatedTasks::ValueType.find_or_create_by(name: 'form field', key: 'form field')
AutomatedTasks::ValueType.find_or_create_by(name: 'form value', key: 'form value')
AutomatedTasks::ValueType.find_or_create_by(name: 'telecom service', key: 'telecom service')

##
# New seeds
def add_priorities(name, parent)
  options = [
    {
      name: "/a priority/ that is equal to [a priority]",
      subject_class: 'PriorityEquals',
    },
    {
      name: "/a priority/ that is equal to or lower than [a priority]",
      subject_class: 'PriorityLower',
    },
    {
      name: "/a priority/ that is equal to or higher than [a priority]",
      subject_class: 'PriorityHigher',
    }
  ]
  options.each do |option|
    AutomatedTasks::ObjectSubjectType.find_or_create_by(option.merge(key: 'priority', parent: parent))
  end
end

def add_strings(name, parent)
  options = [
    {
      name: "any #{name}",
      subject_class: "AnyString",
    },
    {
      name: "/#{name}/ that contains [text]",
      subject_class: "StringContains",
    },
    {
      name: "/#{name}/ that excludes [text]",
      subject_class: "StringNotContains",
    },
    {
      name: "/#{name}/ that equals [text]",
      subject_class: "StringEquals",
    },
    {
      name: "/#{name}/ that does not equal [text]",
      subject_class: "StringNotEquals",
    }
  ]
  options.each do |option|
    type = AutomatedTasks::ObjectSubjectType.find_or_create_by(option.merge(key: name))
    type.parent = parent
    type.save!
  end
end

def add_emails(parent)
  options = [
    {
      name: "/email address/ that equals [text]",
      subject_class: "EmailEquals",
    },
    {
      name: "/email address/ domain that equals [text]",
      subject_class: 'EmailDomainEquals',
    },
    {
      name: "/email address/ that does not equal [text]",
      subject_class: "EmailNotEquals",
    },
    {
      name: "/email address/ domain that does not match [text]",
      subject_class: 'EmailDomainNotEquals',
    }
  ]
  options.each do |option|
    type = AutomatedTasks::SimpleSubjectType.find_or_create_by(option.merge(key: "email address", parent: parent))
    type.save!
  end
end

ticket_created = AutomatedTasks::EventType.find_or_create_by(
    name: "{a ticket} is created",
    module: 'help_tickets',
    model: 'HelpTicket',
    event_class: 'TicketAdded',
    icon: 'nulodgicon-ios-copy-outline'
)

any_ticket = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "any ticket",
    key: "ticket",
    subject_class: 'AnyTicket',
    icon: "nulodgicon-ios-copy-outline",
    parent: ticket_created
)
# Currently commented...
# # Really need to create a task to convert these over
# by_user = AutomatedTasks::ObjectSubjectType.find_by(
#     name: "/a ticket/ that is created by [a user or group]",
#     key: "ticket",
#     subject_class: 'TicketUser',
#     icon: "nulodgicon-person",
#     parent: ticket_created,
# )
# by_user&.destroy
# from_email = AutomatedTasks::ObjectSubjectType.find_by(
#     name: "/a ticket/ that is created from [an email address]",
#     key: "ticket",
#     subject_class: 'TicketEmail',
#     icon: "nulodgicon-ios-email-outline",
#     parent: ticket_created,
# )
# from_email&.destroy
#add_emails(from_email)
form_field = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a ticket/ with {a form field}",
    key: 'ticket',
    subject_class: 'TicketFormField',
    parent: ticket_created
)

ticket_with_source = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a ticket/ {with source}",
    key: 'ticket',
    subject_class: 'TicketWithSource',
    parent: ticket_created
)

ticket_created_during = AutomatedTasks::ObjectSubjectType.find_or_create_by(
  name: '/a ticket/ {during}',
  key: 'ticket',
  subject_class: 'TicketCreatedDuringBusinessHour',
  parent: ticket_created
)

any_object = AutomatedTasks::ObjectSubjectType.find_by(
    name: "/a form field/ with any form value",
    key: 'form field',
    subject_class: 'AnyObject',
    parent: form_field
)
if any_object
  any_object.event_details.find_each do |detail|
    parent_detail = detail.parent
    json = JSON.parse(parent_detail.value)
    json['any'] = true
    parent_detail.value = json.to_json
    parent_detail.save!
    parent_detail.children.destroy_all
  end
end
any_object&.destroy

=begin
any_form_value = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a form field/ with any form value",
    key: 'form field',
    subject_class: 'AnyObject',
    parent: form_field
)

form_value_equal = AutomatedTasks::ObjectSubjectType.find_by(
    name: "/a form field/ that does contain [a form value]",
    parent: form_field
)
if form_value_equal
  form_value_equal.name = "/a form field/ that contains [a form value]"
  form_value_equal.save!
end

form_value_equal = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a form field/ that contains [a form value]",
    key: 'form field',
    subject_class: 'FormValueEqual',
    parent: form_field
)

form_value_not_equal = AutomatedTasks::ObjectSubjectType.find_by(
  name: "/a form field/ that doesn't contain [a form value]",
  parent: form_field,
)
if form_value_not_equal
  form_value_not_equal.name = "/a form field/ that excludes [a form value]"
  form_value_not_equal.save!
end

form_value_not_equal = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a form field/ that excludes [a form value]",
    key: 'form field',
    subject_class: 'FormValueNotEqual',
    parent: form_field
)
=end

ticket_changed = AutomatedTasks::EventType.find_or_create_by(
    name: "{a ticket} is updated",
    module: 'help_tickets',
    event_class: 'TicketUpdated'
)
ticket_changed.model = "CustomFormValue"
ticket_changed.save!
any_ticket = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "any ticket",
    key: "ticket",
    subject_class: 'AnyTicket',
    icon: "nulodgicon-ios-copy-outline",
    parent: ticket_changed
)
# Currently commented...
# by_user = AutomatedTasks::ObjectSubjectType.find_by(
#     name: "/a ticket/ which is updated by [a user or group]",
#     key: "ticket",
#     subject_class: 'TicketUser',
#     icon: "nulodgicon-person",
#     parent: ticket_changed
# )
# by_user&.destroy
# from_email = AutomatedTasks::ObjectSubjectType.find_by(
#     name: "/a ticket/ which is updated by [an email address]",
#     parent: ticket_changed
# )
# from_email&.destroy!

form_field = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a ticket/ with {a form field}",
    key: 'ticket',
    subject_class: 'TicketFormField',
    parent: ticket_changed
)

ticket_with_source = AutomatedTasks::ObjectSubjectType.find_or_create_by(
  name: "/a ticket/ {with source}",
  key: "ticket",
  subject_class: 'TicketWithSourceAndField',
  parent: ticket_changed
)

any_object = AutomatedTasks::ObjectSubjectType.find_by(
    name: "/a form field/ with any form value",
    key: 'form field',
    subject_class: 'AnyObject',
    parent: form_field
)
if any_object
  any_object.event_details.find_each do |detail|
    parent_detail = detail.parent
    json = JSON.parse(parent_detail.value)
    json['any'] = true
    parent_detail.value = json.to_json
    parent_detail.save!
    parent_detail.children.destroy_all
  end
end
any_object&.destroy

form_value_equal = AutomatedTasks::ObjectSubjectType.find_by(
    name: "/a form field/ that does contain [a form value]",
    parent: form_field
)
if form_value_equal
  form_value_equal.name = "/a form field/ that contains [a form value]"
  form_value_equal.save!
end

form_value_equal = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a form field/ that contains [a form value]",
    key: 'form field',
    subject_class: 'FormValueEqual',
    parent: form_field
)

form_value_not_equal = AutomatedTasks::ObjectSubjectType.find_by(
  name: "/a form field/ that doesn't contain [a form value]",
  parent: form_field
)
if form_value_not_equal
  form_value_not_equal.name = "/a form field/ that excludes [a form value]"
  form_value_not_equal.save!
end

form_value_not_equal = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a form field/ that excludes [a form value]",
    key: 'form field',
    subject_class: 'FormValueNotEqual',
    parent: form_field
)

ticket_remains_in_same_state = AutomatedTasks::EventType.find_or_create_by(
  name: "{a ticket} remains in the same state",
  module: 'help_tickets',
  event_class: 'TicketRemainsInSameState'
)
ticket_remains_in_same_state.model = "CustomFormValue"
ticket_remains_in_same_state.save!

form_field = AutomatedTasks::ObjectSubjectType.find_or_create_by(
  name: "/a ticket/ with {a form field}",
  key: 'ticket',
  subject_class: 'TicketFormField',
  parent: ticket_remains_in_same_state
)

comment_created = AutomatedTasks::EventType.find_or_create_by(
    name: "{a comment} is added to a ticket",
    module: 'help_tickets',
    model: 'HelpTicketComment',
    event_class: 'CommentAdded',
    icon: 'genuicon-comment-o'
)

ticket_with_source = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a ticket/ {with source}",
    key: "ticket",
    subject_class: "TicketWithSource",
    parent: comment_created
)

any_commment = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "any comment",
    key: "comment",
    subject_class: "AnyObject",
    icon: "nulodgicon-person",
    parent: comment_created
)

with_text = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a comment/ which includes [text]",
    key: "comment",
    subject_class: "CommentText",
    icon: 'genuicon-comment-o',
    parent: comment_created
)

with_text = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: " a comment which includes any reference",
    key: "comment",
    subject_class: "CommentAnyRef",
    icon: 'genuicon-comment-o',
    parent: comment_created
)

with_user = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a comment/ which includes a mention to [a user or group]",
    key: "comment",
    subject_class: "CommentUserRef",
    icon: "nulodgicon-person",
    parent: comment_created
)

with_contract = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a comment/ that references [a contract]",
    key: 'comment',
    subject_class: "CommentContractRef",
    icon: 'genuicon-envelope-o',
    parent: comment_created
)

with_asset = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a comment/ that references [an asset]",
    key: 'comment',
    subject_class: 'CommentAssetRef',
    icon: "genuicon-laptop",
    parent: comment_created
)

with_vendor = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a comment/ that references [a vendor]",
    key: 'comment',
    subject_class: 'CommentVendorRef',
    icon: 'genuicon-envelope-o',
    parent: comment_created
)

with_telecom_service = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a comment/ that references [a telecom service]",
    key: "comment",
    subject_class: "CommentTelecomServiceRef",
    icon: "genuicon-envelope-o",
    parent: comment_created
)

AutomatedTasks::ObjectSubjectType.find_or_create_by(
  name: "/a comment/ by the teammate who created the ticket",
  key: "comment",
  subject_class: "CommentByTicketCreator",
  icon: "genuicon-person",
  parent: comment_created
)

AutomatedTasks::ObjectSubjectType.find_or_create_by(
  name: "/a comment/ by the external user who created the ticket",
  key: "comment",
  subject_class: "CommentByExternalTicketCreator",
  icon: "genuicon-person",
  parent: comment_created
)

AutomatedTasks::ObjectSubjectType.find_or_create_by(
  name: "/a comment/ by [a user or group]",
  key: "comment",
  subject_class: "CommentBySpecificTeammate",
  icon: "genuicon-person",
  parent: comment_created
)

AutomatedTasks::ObjectSubjectType.find_or_create_by(
  name: "/a comment/ by [people list fields] related to the ticket",
  key: "comment",
  subject_class: "CommentByTeammateInFormField",
  icon: "genuicon-person",
  parent: comment_created
)

AutomatedTasks::ObjectSubjectType.find_or_create_by(
  name: "/a comment/ by agent assigned",
  key: "comment",
  subject_class: "CommentByAgentAssigned",
  icon: "genuicon-person",
  parent: comment_created
)

task_created = AutomatedTasks::EventType.find_or_create_by(
    name: "{a project task} is added to a ticket",
    module: 'help_tickets',
    model: 'ProjectTask',
    event_class: 'TaskAdded',
    icon: 'genuicon-clipboard'
)
any_task = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "any project task",
    key: 'project task',
    subject_class: 'AnyObject',
    icon: 'genuicon-envelope-o',
    parent: task_created
)

with_description = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a project task/ that has a {description}",
    key: 'project task',
    subject_class: 'TaskDescription',
    icon: 'genuicon-envelope-o',
    parent: task_created
)
add_strings("description", with_description)
with_user = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a project task/ that is assigned to [a user or group]",
    key: 'project task',
    subject_class: 'TaskAssignment',
    icon: 'genuicon-envelope-o',
    parent: task_created
)

task_updated = AutomatedTasks::EventType.find_or_create_by(
  name: "{a project task} is updated",
  module: 'help_tickets',
  model: 'ProjectTask',
  event_class: 'TaskUpdated',
  icon: 'genuicon-clipboard'
)

any_task = AutomatedTasks::ObjectSubjectType.find_or_create_by(
  name: "any project task",
  key: 'project task',
  subject_class: 'AnyObject',
  icon: 'genuicon-envelope-o',
  parent: task_updated
)

task_with_description = AutomatedTasks::ObjectSubjectType.find_or_create_by(
  name: "/a project task/ that has a {description}",
  key: 'project task',
  subject_class: 'TaskDescription',
  icon: 'genuicon-envelope-o',
  parent: task_updated
)
add_strings("description", task_with_description)

AutomatedTasks::ObjectSubjectType.find_or_create_by(
  name: "/a project task/ that is assigned to [a user or group]",
  key: 'project task',
  subject_class: 'TaskAssignment',
  icon: 'genuicon-envelope-o',
  parent: task_updated
)

entry_created = AutomatedTasks::EventType.find_or_create_by(
    name: "{a time entry} is added to a ticket",
    module: 'help_tickets',
    model: 'TimeSpent',
    event_class: 'TimeEntryAdded',
    icon: 'genuicon-android-time'
)
exceed_duration = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "any time entry",
    key: 'time entry',
    subject_class: 'AnyObject',
    icon: 'genuicon-envelope-o',
    parent: entry_created
)
exceed_duration = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a time entry/ that exceeds [duration]",
    key: 'time entry',
    subject_class: 'EntryDurationExceeds',
    icon: 'genuicon-envelope-o',
    parent: entry_created
)
less_duration = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a time entry/ that is less than [duration]",
    key: 'time entry',
    subject_class: 'EntryDurationLessThan',
    icon: 'genuicon-envelope-o',
    parent: entry_created
)
includes_user = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a time entry/ which is created by or includes [a user or group]",
    key: 'time entry',
    subject_class: 'EntryCreatedBy',
    icon: 'genuicon-envelope-o',
    parent: entry_created
)

###
#
attachment_added = AutomatedTasks::EventType.find_by(
  name: "{an attachment} is added to a ticket",
  module: 'help_tickets',
  model: 'HelpTicketAttachment',
  event_class: 'HelpTicketAttachmentAdded',
  icon: 'genuicon-ios-person-outline'
)
if (attachment_added)
  attachment_added.model = 'CustomFormAttachment'
  attachment_added.event_class = 'AttachmentAdded'
  attachment_added.save!
end
attachment_added = AutomatedTasks::EventType.find_or_create_by(
  name: "{an attachment} is added to a ticket",
  module: 'help_tickets',
  model: 'CustomFormAttachment',
  event_class: 'AttachmentAdded',
  icon: 'genuicon-ios-person-outline'
)

any_assignment = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "any attachment",
    key: 'attachment',
    subject_class: 'AnyObject',
    icon: 'genuicon-envelope-o',
    parent: attachment_added
)
includes_user = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/an attachment/ that filename includes [text]",
    key: 'attachment',
    subject_class: 'AttachmentIncluded',
    icon: 'genuicon-envelope-o',
    parent: attachment_added
)
includes_user = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/an attachment/ that filename excludes [text]",
    key: 'attachment',
    subject_class: 'AttachmentExcluded',
    icon: 'genuicon-envelope-o',
    parent: attachment_added
)

date_occurred = AutomatedTasks::EventType.find_or_create_by(
    name: "{a date} occurs",
    module: 'help_tickets',
    event_class: 'DateOccurred',
    model: 'ExecutionDate',
    icon: 'genuicon-ios-person-outline'
)
day_of_the_week = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a date/ that is on a [day of the week]",
    key: 'date',
    subject_class: 'DayOfTheWeek',
    icon: 'genuicon-envelope-o',
    parent: date_occurred
)
date_of_the_month = AutomatedTasks::ObjectSubjectType.find_or_create_by(
    name: "/a date/ that is on the [date of the month]",
    key: 'date',
    subject_class: 'DateOfTheMonth',
    icon: 'genuicon-envelope-o',
    parent: date_occurred
)
date_of_the_year_every_six_months = AutomatedTasks::ObjectSubjectType.find_or_create_by(
  name: "a date every 6 months of the year"
)
if date_of_the_year_every_six_months
  date_of_the_year_every_six_months.name = "/a date/ that is on the [date of each year bi-annually]"
  date_of_the_year_every_six_months.subject_class = 'DateOfTheBiAnnualYear'
  date_of_the_year_every_six_months.save!
end

date_of_the_year_every_six_months = AutomatedTasks::ObjectSubjectType.find_or_create_by(
  name: "/a date/ that is on the [date of each year bi-annually]",
  key: 'date',
  subject_class: 'DateOfTheBiAnnualYear',
  icon: 'genuicon-envelope-o',
  parent: date_occurred
)
date_of_the_year = AutomatedTasks::ObjectSubjectType.find_or_create_by(
  name: "/a date/ that is on the [date of the year]",
  key: 'date',
  subject_class: 'DateOfTheYear',
  icon: 'genuicon-envelope-o',
  parent: date_occurred
)
date_of_the_year_quarter = AutomatedTasks::ObjectSubjectType.find_or_create_by(
  name: "/a date/ that is on the [date of each year quarter]",
  key: 'date',
  subject_class: 'DateOfTheQuarterYear',
  icon: 'genuicon-envelope-o',
  parent: date_occurred
)

date_of_the_fiscal_year = AutomatedTasks::ObjectSubjectType.find_by(
  name: "/a date/ that is on the [date] of fiscal year",
  key: 'date'
)
date_of_the_fiscal_year&.destroy!

AutomatedTasks::ActionType.find_or_create_by(name: 'send an [email]',
                                             module: 'help_tickets',
                                             model: 'HelpTicket',
                                             action_class: 'SendEmail',
                                             icon: 'genuicon-envelope-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'add a [comment]',
                                             module: 'help_tickets',
                                             model: 'HelpTicket',
                                             action_class: 'AddComment',
                                             icon: 'genuicon-comment-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'create an [alert]',
                                             module: 'help_tickets',
                                             model: 'HelpTicket',
                                             action_class: 'AddAlert',
                                             icon: 'genuicon-alerts')
AutomatedTasks::ActionType.find_or_create_by(name: 'send a [sms]',
                                             module: 'help_tickets',
                                             model: 'HelpTicket',
                                             action_class: 'SendSms',
                                             icon: 'genuicon-mobile')
AutomatedTasks::ActionType.find_or_create_by(name: 'send a [Mobile Notification]',
                                             module: 'help_tickets',
                                             model: 'HelpTicket',
                                             action_class: 'SendMobileNotification',
                                             icon: 'genuicon-mobile')
AutomatedTasks::ActionType.find_or_create_by(name: 'set a [form field]',
                                             module: 'help_tickets',
                                             model: 'HelpTicket',
                                             action_class: 'SetFormField',
                                             icon: 'genuicon-documents')
AutomatedTasks::ActionType.find_or_create_by(name: 'halt further execution',
                                             module: 'help_tickets',
                                             model: 'HelpTicket',
                                             action_class: 'HaltExecution',
                                             icon: 'genuicon-documents')

AutomatedTasks::ActionType.find_or_create_by(name: 'send an [email]',
                                             module: 'help_tickets',
                                             model: 'HelpTicketComment',
                                             action_class: 'SendEmail',
                                             icon: 'genuicon-envelope-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'add a [comment]',
                                             module: 'help_tickets',
                                             model: 'HelpTicketComment',
                                             action_class: 'AddComment',
                                             icon: 'genuicon-comment-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'create an [alert]',
                                             module: 'help_tickets',
                                             model: 'HelpTicketComment',
                                             action_class: 'AddAlert',
                                             icon: 'genuicon-alerts')
AutomatedTasks::ActionType.find_or_create_by(name: 'send a [sms]',
                                             module: 'help_tickets',
                                             model: 'HelpTicketComment',
                                             action_class: 'SendSms',
                                             icon: 'genuicon-mobile')
AutomatedTasks::ActionType.find_or_create_by(name: 'send a [Mobile Notification]',
                                             module: 'help_tickets',
                                             model: 'HelpTicketComment',
                                             action_class: 'SendMobileNotification',
                                             icon: 'genuicon-mobile')
AutomatedTasks::ActionType.find_or_create_by(name: 'set a [form field]',
                                             module: 'help_tickets',
                                             model: 'HelpTicketComment',
                                             action_class: 'SetFormField',
                                             icon: 'genuicon-documents')
AutomatedTasks::ActionType.find_or_create_by(name: 'halt further execution',
                                             module: 'help_tickets',
                                             model: 'HelpTicketComment',
                                             action_class: 'HaltExecution',
                                             icon: 'genuicon-documents')

AutomatedTasks::ActionType.find_or_create_by(name: 'send an [email]',
                                             module: 'help_tickets',
                                             model: 'HelpTicketActivity',
                                             action_class: 'SendEmail',
                                             icon: 'genuicon-envelope-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'create an [alert]',
                                             module: 'help_tickets',
                                             model: 'HelpTicketActivity',
                                             action_class: 'AddAlert',
                                             icon: 'genuicon-alerts')
AutomatedTasks::ActionType.find_or_create_by(name: 'send a [sms]',
                                             module: 'help_tickets',
                                             model: 'HelpTicketActivity',
                                             action_class: 'SendSms',
                                             icon: 'genuicon-mobile')
AutomatedTasks::ActionType.find_or_create_by(name: 'set a [form field]',
                                             module: 'help_tickets',
                                             model: 'HelpTicketActivity',
                                             action_class: 'SetFormField',
                                             icon: 'genuicon-documents')
AutomatedTasks::ActionType.find_or_create_by(name: 'halt further execution',
                                             module: 'help_tickets',
                                             model: 'HelpTicketActivity',
                                             action_class: 'HaltExecution',
                                             icon: 'genuicon-documents')

AutomatedTasks::ActionType.find_or_create_by(name: 'send an [email]',
                                             module: 'help_tickets',
                                             model: 'ProjectTask',
                                             action_class: 'SendEmail',
                                             icon: 'genuicon-envelope-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'create an [alert]',
                                             module: 'help_tickets',
                                             model: 'ProjectTask',
                                             action_class: 'AddAlert',
                                             icon: 'genuicon-alerts')
AutomatedTasks::ActionType.find_or_create_by(name: 'send a [sms]',
                                             module: 'help_tickets',
                                             model: 'ProjectTask',
                                             action_class: 'SendSms',
                                             icon: 'genuicon-mobile')
AutomatedTasks::ActionType.find_or_create_by(name: 'set a [form field]',
                                             module: 'help_tickets',
                                             model: 'ProjectTask',
                                             action_class: 'SetFormField',
                                             icon: 'genuicon-documents')
AutomatedTasks::ActionType.find_or_create_by(name: 'halt further execution',
                                             module: 'help_tickets',
                                             model: 'ProjectTask',
                                             action_class: 'HaltExecution',
                                             icon: 'genuicon-documents')

AutomatedTasks::ActionType.find_or_create_by(name: 'send an [email]',
                                             module: 'help_tickets',
                                             model: 'TimeSpent',
                                             action_class: 'SendEmail',
                                             icon: 'genuicon-envelope-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'create an [alert]',
                                             module: 'help_tickets',
                                             model: 'TimeSpent',
                                             action_class: 'AddAlert',
                                             icon: 'genuicon-alerts')
AutomatedTasks::ActionType.find_or_create_by(name: 'send a [sms]',
                                             module: 'help_tickets',
                                             model: 'TimeSpent',
                                             action_class: 'SendSms',
                                             icon: 'genuicon-mobile')
AutomatedTasks::ActionType.find_or_create_by(name: 'set a [form field]',
                                             module: 'help_tickets',
                                             model: 'TimeSpent',
                                             action_class: 'SetFormField',
                                             icon: 'genuicon-documents')
AutomatedTasks::ActionType.find_or_create_by(name: 'halt further execution',
                                             module: 'help_tickets',
                                             model: 'TimeSpent',
                                             action_class: 'HaltExecution',
                                             icon: 'genuicon-documents')

action = AutomatedTasks::ActionType.find_by(name: 'send an [email]',
                                            module: 'help_tickets',
                                            model: 'HelpTicketAttachment',
                                            action_class: 'SendEmail',
                                            icon: 'genuicon-envelope-o')
if (action)
  action.model = 'CustomFormAttachment'
  action.save!
else
  AutomatedTasks::ActionType.find_or_create_by(name: 'send an [email]',
                                               module: 'help_tickets',
                                               model: 'CustomFormAttachment',
                                               action_class: 'SendEmail',
                                               icon: 'genuicon-envelope-o')
end
action = AutomatedTasks::ActionType.find_by(name: 'create an [alert]',
                                            module: 'help_tickets',
                                            model: 'HelpTicketAttachment',
                                            action_class: 'AddAlert',
                                            icon: 'genuicon-alerts')
if (action)
  action.model = 'CustomFormAttachment'
  action.save!
else
  AutomatedTasks::ActionType.find_or_create_by(name: 'create an [alert]',
                                               module: 'help_tickets',
                                               model: 'CustomFormAttachment',
                                               action_class: 'AddAlert',
                                               icon: 'genuicon-alerts')
end

action = AutomatedTasks::ActionType.find_by(name: 'send a [sms]',
                                            module: 'help_tickets',
                                            model: 'HelpTicketAttachment',
                                            action_class: 'SendSms',
                                            icon: 'genuicon-mobile')
if (action)
  action.model = 'CustomFormAttachment'
  action.save!
else
  AutomatedTasks::ActionType.find_or_create_by(name: 'send a [sms]',
                                               module: 'help_tickets',
                                               model: 'CustomFormAttachment',
                                               action_class: 'SendSms',
                                               icon: 'genuicon-mobile')
end

action = AutomatedTasks::ActionType.find_by(name: 'set a [form field]',
                                            module: 'help_tickets',
                                            model: 'HelpTicketAttachment',
                                            action_class: 'SetFormField',
                                            icon: 'genuicon-documents')
if (action)
  action.model = 'CustomFormAttachment'
  action.save!
else
  AutomatedTasks::ActionType.find_or_create_by(name: 'set a [form field]',
                                               module: 'help_tickets',
                                               model: 'CustomFormAttachment',
                                               action_class: 'SetFormField',
                                               icon: 'genuicon-documents')
end

AutomatedTasks::ActionType.find_or_create_by(name: 'halt further execution',
                                             module: 'help_tickets',
                                             model: 'CustomFormAttachment',
                                             action_class: 'HaltExecution',
                                             icon: 'genuicon-documents')
AutomatedTasks::ActionType.find_or_create_by(name: 'send an [email]',
                                             module: 'help_tickets',
                                             model: 'CustomFormValue',
                                             action_class: 'SendEmail',
                                             icon: 'genuicon-envelope-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'add a [comment]',
                                             module: 'help_tickets',
                                             model: 'CustomFormValue',
                                             action_class: 'AddComment',
                                             icon: 'genuicon-envelope-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'create an [alert]',
                                             module: 'help_tickets',
                                             model: 'CustomFormValue',
                                             action_class: 'AddAlert',
                                             icon: 'genuicon-alerts')
AutomatedTasks::ActionType.find_or_create_by(name: 'send a [sms]',
                                             module: 'help_tickets',
                                             model: 'CustomFormValue',
                                             action_class: 'SendSms',
                                             icon: 'genuicon-mobile')
AutomatedTasks::ActionType.find_or_create_by(name: 'send a [Mobile Notification]',
                                             module: 'help_tickets',
                                             model: 'CustomFormValue',
                                             action_class: 'SendMobileNotification',
                                             icon: 'genuicon-mobile')
AutomatedTasks::ActionType.find_or_create_by(name: 'set a [form field]',
                                             module: 'help_tickets',
                                             model: 'CustomFormValue',
                                             action_class: 'SetFormField',
                                             icon: 'genuicon-documents')
AutomatedTasks::ActionType.find_or_create_by(name: 'halt further execution',
                                             module: 'help_tickets',
                                             model: 'CustomFormValue',
                                             action_class: 'HaltExecution',
                                             icon: 'genuicon-documents')

AutomatedTasks::ActionType.find_or_create_by(name: 'add a [ticket]',
                                             module: 'help_tickets',
                                             model: 'ExecutionDate',
                                             action_class: 'AddTicket',
                                             icon: 'genuicon-envelope-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'send an [email]',
                                             module: 'help_tickets',
                                             model: 'ExecutionDate',
                                             action_class: 'SendEmail',
                                             icon: 'genuicon-envelope-o')
AutomatedTasks::ActionType.find_or_create_by(name: 'create an [alert]',
                                             module: 'help_tickets',
                                             model: 'ExecutionDate',
                                             action_class: 'AddAlert',
                                             icon: 'genuicon-alerts')
AutomatedTasks::ActionType.find_or_create_by(name: 'send a [sms]',
                                             module: 'help_tickets',
                                             model: 'ExecutionDate',
                                             action_class: 'SendSms',
                                             icon: 'genuicon-mobile')
AutomatedTasks::ActionType.find_or_create_by(name: 'send a [Slack message]',
                                             module: 'help_tickets',
                                             model: 'HelpTicket',
                                             action_class: 'SendSlackMessage',
                                             icon: 'genuicon-slack')

AutomatedTasks::ActionType.find_or_create_by(name: 'send a [Slack message]',
                                             module: 'help_tickets',
                                             model: 'HelpTicketComment',
                                             action_class: 'SendSlackMessage',
                                             icon: 'genuicon-slack')

AutomatedTasks::ActionType.find_or_create_by(name: 'send a [Slack message]',
                                             module: 'help_tickets',
                                             model: 'HelpTicketActivity',
                                             action_class: 'SendSlackMessage',
                                             icon: 'genuicon-slack')

AutomatedTasks::ActionType.find_or_create_by(name: 'send a [Slack message]',
                                             module: 'help_tickets',
                                             model: 'ProjectTask',
                                             action_class: 'SendSlackMessage',
                                             icon: 'genuicon-slack')

AutomatedTasks::ActionType.find_or_create_by(name: 'send a [Slack message]',
                                             module: 'help_tickets',
                                             model: 'TimeSpent',
                                             action_class: 'SendSlackMessage',
                                             icon: 'genuicon-slack')

action = AutomatedTasks::ActionType.find_by(name: 'send a [Slack message]',
                                            module: 'help_tickets',
                                            model: 'HelpTicketAttachment',
                                            action_class: 'SendSlackMessage',
                                            icon: 'genuicon-slack')
if (action)
  action.model = 'CustomFormAttachment'
  action.save!
else
  AutomatedTasks::ActionType.find_or_create_by(name: 'send a [Slack message]',
                                               module: 'help_tickets',
                                               model: 'CustomFormAttachment',
                                               action_class: 'SendSlackMessage',
                                               icon: 'genuicon-slack')
end

AutomatedTasks::ActionType.find_or_create_by(name: 'send a [Slack message]',
                                             module: 'help_tickets',
                                             model: 'CustomFormValue',
                                             action_class: 'SendSlackMessage',
                                             icon: 'genuicon-slack')
AutomatedTasks::ActionType.find_or_create_by(name: 'send a [Desktop notification]',
                                             module: 'help_tickets',
                                             model: 'HelpTicketComment',
                                             action_class: 'SendNotification',
                                             icon: 'genuicon-notification')

AutomatedTasks::ActionType.find_or_create_by(name: 'send a [Microsoft Teams message]',
                                             module: 'help_tickets',
                                             model: 'HelpTicket',
                                             action_class: 'SendMsTeamsMessage',
                                             icon: 'genuicon-ms-teams')

AutomatedTasks::ActionType.find_or_create_by(name: 'send a [Microsoft Teams message]',
                                             module: 'help_tickets',
                                             model: 'HelpTicketComment',
                                             action_class: 'SendMsTeamsMessage',
                                             icon: 'genuicon-ms-teams')

AutomatedTasks::ActionType.find_or_create_by(name: 'send a [Microsoft Teams message]',
                                             module: 'help_tickets',
                                             model: 'HelpTicketActivity',
                                             action_class: 'SendMsTeamsMessage',
                                             icon: 'genuicon-ms-teams')

AutomatedTasks::ActionType.find_or_create_by(name: 'send a [Microsoft Teams message]',
                                             module: 'help_tickets',
                                             model: 'ProjectTask',
                                             action_class: 'SendMsTeamsMessage',
                                             icon: 'genuicon-ms-teams')

AutomatedTasks::ActionType.find_or_create_by(name: 'send a [Microsoft Teams message]',
                                             module: 'help_tickets',
                                             model: 'TimeSpent',
                                             action_class: 'SendMsTeamsMessage',
                                             icon: 'genuicon-ms-teams')

AutomatedTasks::ActionType.find_or_create_by(name: 'send a [Microsoft Teams message]',
                                             module: 'help_tickets',
                                             model: 'CustomFormAttachment',
                                             action_class: 'SendMsTeamsMessage',
                                             icon: 'genuicon-ms-teams')

AutomatedTasks::ActionType.find_or_create_by(name: 'send a [Microsoft Teams message]',
                                             module: 'help_tickets',
                                             model: 'CustomFormValue',
                                             action_class: 'SendMsTeamsMessage',
                                             icon: 'genuicon-ms-teams')

=begin
AutomatedTasks::EventType.find_or_create_by(name: "{a contract} is created",
                            module: 'contracts',
                            model: 'Contract',
                            event_class: 'ContractAdded',
                            icon: 'nulodgicon-ios-copy-outline')
AutomatedTasks::EventType.find_or_create_by(name: "{an alert date} is added to a contract",
                            module: 'contracts',
                            model: 'AlertDate',
                            event_class: 'AlertDateAdded',
                            icon: 'genuicon-comment-o')
AutomatedTasks::EventType.find_or_create_by(name: "[a user or group] is added to a contract",
                            module: 'contracts',
                            model: 'Contract',
                            event_class: 'ContractUserAdded',
                            icon: 'genuicon-ios-person-outline')
AutomatedTasks::EventType.find_or_create_by(name: "{an attachment} is added to a contract",
                            module: 'contracts',
                            model: 'Contract',
                            event_class: 'ContractAttachmentAdded',
                            icon: 'genuicon-ios-person-outline')

AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "any contract",
                              key: "contract",
                              subject_class: 'AnyContract',
                              icon: "nulodgicon-ios-copy-outline")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a contract with [a name]",
                              key: "contract",
                              subject_class: 'ConractName',
                              icon: "nulodgicon-ios-copy-outline")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a contract with [a note]",
                              key: "contract",
                              subject_class: 'ContractNote',
                              icon: "nulodgicon-ios-list-outline")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a contract with {a location}",
                              key: "contract",
                              subject_class: 'ContractLocation',
                              icon: "nulodgicon-person")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a contract with [a category]",
                              key: "contract",
                              subject_class: 'ContractLocation',
                              icon: "nulodgicon-ios-email-outline")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a contract with [a monthly cost]",
                              key: "contract",
                              subject_class: 'ContractMonthlyCost',
                              icon: "nulodgicon-ios-email-outline")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a contract with [a total cost]",
                              key: "contract",
                              subject_class: 'ContractTotalCost',
                              icon: "nulodgicon-ios-email-outline")

AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a location from [a state]",
                               key: 'location',
                               subject_class: 'LocationState',
                               icon: 'genuicon-envelope-o')
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a location from [a country]",
                               key: 'location',
                               subject_class: 'LocationCountry',
                               icon: 'genuicon-envelope-o')
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a location from [a city]",
                               key: 'location',
                               subject_class: 'LocationCity',
                               icon: 'genuicon-envelope-o')
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a location from [a zip code]",
                               key: 'location',
                               subject_class: 'LocationZip',
                               icon: 'genuicon-envelope-o')

AutomatedTasks::EventType.find_or_create_by(name: "{a telecom provider} is created",
                            module: 'telecom',
                            model: 'TelecomProvider',
                            event_class: 'TelecomProviderAdded',
                            icon: 'nulodgicon-ios-copy-outline')
AutomatedTasks::EventType.find_or_create_by(name: "{a telecom provider} is recommended",
                            module: 'telecom',
                            model: 'TelecomProvider',
                            event_class: 'TelecomProviderRecommended',
                            icon: 'nulodgicon-ios-copy-outline')
AutomatedTasks::EventType.find_or_create_by(name: "{a telecom provider} is discovered",
                            module: 'telecom',
                            model: 'TelecomProvider',
                            event_class: 'TelecomProviderDiscovered',
                            icon: 'nulodgicon-ios-copy-outline')
AutomatedTasks::EventType.find_or_create_by(name: "{a telecom service} is created",
                            module: 'telecom',
                            model: 'TelecomProvider',
                            event_class: 'TelecomServiceAdded',
                            icon: 'nulodgicon-ios-copy-outline')
AutomatedTasks::EventType.find_or_create_by(name: "{an ip address} is created",
                            module: 'telecom',
                            model: 'IpAddress',
                            event_class: 'IpAddressAdded',
                            icon: 'genuicon-comment-o')
AutomatedTasks::EventType.find_or_create_by(name: "{a phone number} is created",
                            module: 'telecom',
                            model: 'PhoneNumber',
                            event_class: 'PhoneNumberAdded',
                            icon: 'genuicon-ios-person-outline')

AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "any telecom provider",
                              key: "telecom provider",
                              subject_class: 'AnyProvider',
                              icon: "nulodgicon-ios-copy-outline")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a telecom provider with [a name]",
                              key: "telecom provider",
                              subject_class: 'ProviderName',
                              icon: "nulodgicon-ios-copy-outline")

AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "any telecom service",
                              key: "telecom service",
                              subject_class: 'AnyTelecomService',
                              icon: "nulodgicon-ios-list-outline")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a telecom service with {a name}",
                              key: "telecom service",
                              subject_class: 'TelecomServiceName',
                              icon: "nulodgicon-person")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a telecom service with {a note}",
                              key: "telecom service",
                              subject_class: 'TelecomServiceNote',
                              icon: "nulodgicon-person")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a telecom service with {a service category}",
                              key: "telecom service",
                              subject_class: 'TelecomServiceCategory',
                              icon: "nulodgicon-person")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a telecom service with {a location}",
                              key: "telecom service",
                              subject_class: 'TelecomServiceLocation',
                              icon: "nulodgicon-person")
AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "a telecom service with {a monthly cost}",
                              key: "telecom service",
                              subject_class: 'TelecomServiceMonthlyCost',
                              icon: "nulodgicon-person")

AutomatedTasks::EventType.find_or_create_by(name: "{an asset} is created",
                            module: 'assets',
                            model: 'ManagedAsset',
                            event_class: 'AssetAdded',
                            icon: 'nulodgicon-ios-copy-outline')
AutomatedTasks::EventType.find_or_create_by(name: "{a discovered asset} is created",
                            module: 'assets',
                            model: 'DiscoveredAsset',
                            event_class: 'AssetDiscovered',
                            icon: 'nulodgicon-ios-copy-outline')
AutomatedTasks::EventType.find_or_create_by(name: "{an asset software} is created",
                            module: 'assets',
                            model: 'AssetSoftware',
                            event_class: 'AssetSoftwareAdded',
                            icon: 'genuicon-comment-o')
AutomatedTasks::EventType.find_or_create_by(name: "{a phone number} is created",
                            module: 'assets',
                            model: 'PhoneNumber',
                            event_class: 'PhoneNumberAdded',
                            icon: 'genuicon-ios-person-outline')
=end

AutomatedTasks::ValueType.find_or_create_by(name: 'text', key: 'text')
AutomatedTasks::ValueType.find_or_create_by(name: 'category', key: 'category')
AutomatedTasks::ValueType.find_or_create_by(name: 'service category', key: 'service category')
AutomatedTasks::ValueType.find_or_create_by(name: 'city', key: 'city')
AutomatedTasks::ValueType.find_or_create_by(name: 'zip code', key: 'zip code')
AutomatedTasks::ValueType.find_or_create_by(name: 'state', key: 'state')
AutomatedTasks::ValueType.find_or_create_by(name: 'cost', key: 'cost')
AutomatedTasks::ValueType.find_or_create_by(name: 'user or group', key: 'user or group')
AutomatedTasks::ValueType.find_or_create_by(name: 'duration', key: 'duration')
AutomatedTasks::ValueType.find_or_create_by(name: 'priority', key: 'priority')
AutomatedTasks::ValueType.find_or_create_by(name: 'status', key: 'status')
AutomatedTasks::ValueType.find_or_create_by(name: 'contract', key: 'contract')
AutomatedTasks::ValueType.find_or_create_by(name: 'asset', key: 'asset')
AutomatedTasks::ValueType.find_or_create_by(name: 'vendor', key: 'vendor')
AutomatedTasks::ValueType.find_or_create_by(name: 'form field', key: 'form field')
AutomatedTasks::ValueType.find_or_create_by(name: 'form value', key: 'form value')
AutomatedTasks::ValueType.find_or_create_by(name: 'telecom service', key: 'telecom service')
AutomatedTasks::ValueType.find_or_create_by(name: 'day of the week', key: 'day of the week')
AutomatedTasks::ValueType.find_or_create_by(name: 'date of the month', key: 'date of the month')
AutomatedTasks::ValueType.find_or_create_by(name: 'date of the year', key: 'date of the year')

=begin
TriggeredEventType.find_or_create_by(name: "{a telecom service} is created", module: 'telecom', model: 'TelecomService', template: 'add_telecom')
TriggeredEventType.find_or_create_by(name: "{a telecom provider} is created", module: 'telecom', model: 'TelecomProvider', template: 'add_provider')
TriggeredEventType.find_or_create_by(name: "{a location} is added to a telecom service", module: 'telecom', model: 'TelecomProvider', template: 'add_location')

TriggeredEventType.find_or_create_by(name: "{a vendor} is created", module: 'vendors', model: 'Vendor', template: 'add_vendor')
TriggeredEventType.find_or_create_by(name: "{a transaction} is created", module: 'vendors', model: 'GeneralTransaction', template: 'add_transaction')
TriggeredEventType.find_or_create_by(name: "{a tag} is added to a transaction", module: 'vendors', model: 'GeneralTransactionTag', template: 'add_tag')
TriggeredEventType.find_or_create_by(name: "{a product} is created", module: 'vendors', model: 'Product', template: 'add_product')
TriggeredEventType.find_or_create_by(name: "{an application} is created", module: 'vendors', model: 'app', template: 'add_app')
TriggeredEventType.find_or_create_by(name: "{an alert} is created", module: 'vendors', model: 'Modulealert', template: 'add_alert')

TriggeredEventType.find_or_create_by(name: "{an asset} is created", module: 'assets', model: 'Managedasset', template: 'add_asset')
TriggeredEventType.find_or_create_by(name: "{a software} is created", module: 'assets', model: 'assetSoftware', template: 'add_software')
TriggeredEventType.find_or_create_by(name: "{an asset} is tagged", module: 'assets', model: 'ManagedassetTag', template: 'add_tag')
TriggeredEventType.find_or_create_by(name: "{an asset} is discovered", module: 'assets', model: 'Discoveredasset', template: 'add_discovered')
TriggeredEventType.find_or_create_by(name: "{a location} is assigned to an asset", module: 'assets', model: 'Managedasset', template: 'add_location')
TriggeredEventType.find_or_create_by(name: "{an asset} is expired", module: 'assets', model: 'Managedasset', template: 'expired_asset')

Triggeredaction.find_or_create_by(name: "{a time entry} is added to the ticket", module: 'help_tickets', icon: 'genuicon-android-time')
Triggeredaction.find_or_create_by(name: "{an impacted asset} is added to the ticket", module: 'help_tickets', icon: 'genuicon-ios-person-outline')
Triggeredaction.find_or_create_by(name: "{an impacted user} is added to the ticket", module: 'help_tickets', icon: 'genuicon-ios-person-outline')
Triggeredaction.find_or_create_by(name: "{a comment} is added to the ticket", module: 'help_tickets', icon: 'genuicon-comment-o')
Triggeredaction.find_or_create_by(name: "{a priority} is assigned to the ticket", module: 'help_tickets', icon: 'genuicon-exclamation')
Triggeredaction.find_or_create_by(name: "{an agent} is assigned to the ticket", module: 'help_tickets', icon: 'genuicon-person-stalker')
Triggeredaction.find_or_create_by(name: "{an email} is sent", module: 'help_tickets', icon: 'genuicon-envelope-o')
=end
