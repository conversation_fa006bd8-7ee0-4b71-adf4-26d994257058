services_names = [
  'options/contributor_options#index',
  'options/contributor_options_with_guests#index',
  'api/v1/options/contributor_options_with_guests#index',
  'options/company_options#index',
  'options/custom_form_options#index',
  'options/linkable_options#index',
  'options/location_options#index',
  'options/company_user_options#index',
  'current_company_users#show',
  'company_users#show',
  'company_user_application_controller',
  'company_user_auth_controller',
  'contributor_company_user',
  'company_user_access_check',
  'options/workspace_options#index',
  'contributor_type_group',
  'workspace_default_group',
  'contributor_find',
  'desktop_app_release',
  'company_user_find_by',
  'user_find_by',
  'company_find_by',
  'send_email_recipients_cache',
  'current_user_cache'
]

services_names.each { |name| ServiceOption.find_or_create_by!(service_name: name, service_type: 'cache_type') }
