templates = [
  {
    name: 'Tickets Older than 1 Week',
    description: 'Tickets open for more than 1 week.',
    report_type: 'one_week_old_open_tickets',
    analytics_metrics: {
      charts: [
        {
          name: 'status',
          type: 'donut',
          order: 1,
          cardId: 1,
          friendly_name: 'Status',
          filters: {
            values: [
              { name: 'status', value: 'Open' },
              { time_period: { filter_type: { type: 'status_period', filter: 'custom_date', end_date: 'older_than_one_week' } }}
            ],
            type: 'status',
            custom_date: {
              filter_type: {
              end_date: 'older_than_one_week',
                type: 'status_period',
                filter: 'custom_date'
              }
            }
          }
        },
        {
          name: 'priority',
          type: 'donut',
          order: 2,
          cardId: 2,
          friendly_name: 'Priority',
          filters: {
            values: [
              { name: 'status', value: 'Open' },
              { time_period: { filter_type: { type: 'status_period', filter: 'custom_date', end_date: 'older_than_one_week' } }}
            ],
            type: 'priority',
            custom_date: {
              filter_type: {
              end_date: 'older_than_one_week',
                type: 'status_period',
                filter: 'custom_date'
              }
            }
          }
        },
        {
          name: 'source',
          type: 'donut',
          order: 3,
          cardId: 3,
          friendly_name: 'Source',
          filters: {
            values: [
              { name: 'status', value: 'Open' },
              { time_period: { filter_type: { type: 'status_period', filter: 'custom_date', end_date: 'older_than_one_week' } }}
            ],
            type: 'source',
            custom_date: {
              filter_type: {
              end_date: 'older_than_one_week',
                type: 'status_period',
                filter: 'custom_date'
              }
            }
          }
        },
        {
          name: 'agent',
          type: 'donut',
          order: 4,
          cardId: 4,
          friendly_name: 'Agent',
          filters: {
            values: [
              { name: 'status', value: 'Open' },
              { time_period: { filter_type: { type: 'status_period', filter: 'custom_date', end_date: 'older_than_one_week' } }}
            ],
            type: 'agent',
            custom_date: {
              filter_type: {
              end_date: 'older_than_one_week',
                type: 'status_period',
                filter: 'custom_date'
              }
            }
          }
        },
        {
          name: 'requester',
          type: 'donut',
          order: 5,
          cardId: 5,
          friendly_name: 'Requester',
          filters: {
            values: [
              { name: 'status', value: 'Open' },
              { time_period: { filter_type: { type: 'status_period', filter: 'custom_date', end_date: 'older_than_one_week' } }}
            ],
            type: 'requester',
            custom_date: {
              filter_type: {
              end_date: 'older_than_one_week',
                type: 'status_period',
                filter: 'custom_date'
              }
            }
          }
        }
      ]
    }
  },
  {
    name: 'Tickets Older than 1 Month',
    description: 'Tickets open for more than 1 month.',
    report_type: 'one_month_old_open_tickets',
    analytics_metrics: {
      charts: [
        {
          name: 'status',
          type: 'donut',
          order: 1,
          cardId: 1,
          friendly_name: 'Status',
          filters: {
            values: [
              { name: 'status', value: 'Open' },
              { time_period: { filter_type: { type: 'status_period', filter: 'custom_date', end_date: 'older_than_one_month' } }}
            ],
            type: 'status',
            custom_date: {
              filter_type: {
              end_date: 'older_than_one_month',
                type: 'status_period',
                filter: 'custom_date'
              }
            }
          }
        },
        {
          name: 'priority',
          type: 'donut',
          order: 2,
          cardId: 2,
          friendly_name: 'Priority',
          filters: {
            values: [
              { name: 'status', value: 'Open' },
              { time_period: { filter_type: { type: 'status_period', filter: 'custom_date', end_date: 'older_than_one_month' } }}
            ],
            type: 'priority',
            custom_date: {
              filter_type: {
              end_date: 'older_than_one_month',
                type: 'status_period',
                filter: 'custom_date'
              }
            }
          }
        },
        {
          name: 'source',
          type: 'donut',
          order: 3,
          cardId: 3,
          friendly_name: 'Source',
          filters: {
            values: [
              { name: 'status', value: 'Open' },
              { time_period: { filter_type: { type: 'status_period', filter: 'custom_date', end_date: 'older_than_one_month' } }}
            ],
            type: 'source',
            custom_date: {
              filter_type: {
              end_date: 'older_than_one_month',
                type: 'status_period',
                filter: 'custom_date'
              }
            }
          }
        },
        {
          name: 'agent',
          type: 'donut',
          order: 4,
          cardId: 4,
          friendly_name: 'Agent',
          filters: {
            values: [
              { name: 'status', value: 'Open' },
              { time_period: { filter_type: { type: 'status_period', filter: 'custom_date', end_date: 'older_than_one_month' } }}
            ],
            type: 'agent',
            custom_date: {
              filter_type: {
              end_date: 'older_than_one_month',
                type: 'status_period',
                filter: 'custom_date'
              }
            }
          }
        },
        {
          name: 'requester',
          type: 'donut',
          order: 5,
          cardId: 5,
          friendly_name: 'Requester',
          filters: {
            values: [
              { name: 'status', value: 'Open' },
              { time_period: { filter_type: { type: 'status_period', filter: 'custom_date', end_date: 'older_than_one_month' } }}
            ],
            type: 'requester',
            custom_date: {
              filter_type: {
              end_date: 'older_than_one_month',
                type: 'status_period',
                filter: 'custom_date'
              }
            }
          }
        }
      ]
    }
  },
  {
    name: 'Closed Tickets with Surveys',
    description: 'Survey results from survey responses.',
    report_type: 'closed_tickets_with_surveys',
    analytics_metrics: {
      charts: [
        {
          name: 'survey',
          type: 'bar',
          order: 1,
          cardId: 1,
          friendly_name: 'Survey',
          filters: {
            values: [
              {
                name: 'status',
                value: 'Closed'
              },
              {
                name: 'Closed',
                value: ['Positive', 'Negative']
              }
            ],
            type: 'survey'
          }
        }
      ]
    }
  },
  {
    name: 'Response Time',
    description: 'Tickets that were responded to within target time.',
    report_type: 'first_response_time',
    analytics_metrics: {
      charts: [
        {
          name: 'responseTime',
          type: 'bar',
          order: 1,
          cardId: 1,
          friendly_name: 'Response Time',
          filters: {
            values: [],
            type: 'responseTime'
          }
        }
      ]
    }
  },
  {
    name: 'Average Response Time',
    description: 'Average time of tickets that were responded to within target time.',
    report_type: 'avg_first_response_time',
    analytics_metrics: {
      charts: [
        {
          name: 'avgResponse',
          type: 'bar',
          order: 1,
          cardId: 1,
          friendly_name: 'Avg Response',
          filters: {
            values: [],
            type: 'avgResponse'
          }
        }
      ]
    }
  }
]

templates.each do |template|
  d = DefaultAnalyticsReportTemplate.find_or_initialize_by(report_type: template[:report_type])
  d.name = template[:name]
  d.description = template[:description]
  d.analytics_metrics = template[:analytics_metrics]
  d.save!
end
