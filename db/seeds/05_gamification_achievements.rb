GamificationAchievement.find_or_initialize_by(achievement_type: "CompanyUser", level: 1, value: 1, name: "Welcome to the party", earned: false).update(points: 100)
GamificationAchievement.find_or_initialize_by(achievement_type: "CompanyUser", level: 2, value: 5, name: "Two's company", earned: false).update(points: 300)
GamificationAchievement.find_or_initialize_by(achievement_type: "CompanyUser", level: 3, value: 10, name: "A little help from my friends", earned: false).update(points: 600)
GamificationAchievement.find_or_initialize_by(achievement_type: "CompanyUser", level: 4, value: 20, name: "Now it's a party", earned: false).update(points: 1000)
GamificationAchievement.find_or_initialize_by(achievement_type: "CompanyUser", level: 5, value: 50, name: "Mr. Popular", earned: false).update(points: 1500)

GamificationAchievement.find_or_initialize_by(achievement_type: "Contract", level: 1, value: 1, name: "We've made first contract", earned: false).update(points: 100)
GamificationAchievement.find_or_initialize_by(achievement_type: "Contract", level: 2, value: 3, name: "The ties that bind", earned: false).update(points: 300)
GamificationAchievement.find_or_initialize_by(achievement_type: "Contract", level: 3, value: 6, name: "Love at first sign", earned: false).update(points: 600)
GamificationAchievement.find_or_initialize_by(achievement_type: "Contract", level: 4, value: 10, name: "Signed, sealed, delivered", earned: false).update(points: 1000)
GamificationAchievement.find_or_initialize_by(achievement_type: "Contract", level: 5, value: 15, name: "On the dotted line", earned: false).update(points: 1500)

GamificationAchievement.find_or_initialize_by(achievement_type: "Vendor", level: 1, value: 1, name: "The silver lining", earned: false).update(points: 100)
GamificationAchievement.find_or_initialize_by(achievement_type: "Vendor", level: 2, value: 3, name: "A cloudy day in paradise", earned: false).update(points: 300)
GamificationAchievement.find_or_initialize_by(achievement_type: "Vendor", level: 3, value: 5, name: "Storm's a-brewin'", earned: false).update(points: 600)
GamificationAchievement.find_or_initialize_by(achievement_type: "Vendor", level: 4, value: 10, name: "All your space are belong to us", earned: false).update(points: 1000)
GamificationAchievement.find_or_initialize_by(achievement_type: "Vendor", level: 5, value: 20, name: "Offshore account", earned: false).update(points: 1500)

GamificationAchievement.find_or_initialize_by(achievement_type: "ManagedAsset", level: 1, value: 1, name: "Look what I found", earned: false).update(points: 100)
GamificationAchievement.find_or_initialize_by(achievement_type: "ManagedAsset", level: 2, value: 5, name: "Just getting started", earned: false).update(points: 300)
GamificationAchievement.find_or_initialize_by(achievement_type: "ManagedAsset", level: 3, value: 20, name: "Check out my stuff", earned: false).update(points: 600)
GamificationAchievement.find_or_initialize_by(achievement_type: "ManagedAsset", level: 4, value: 50, name: "Loot, loot, loot", earned: false).update(points: 1000)
GamificationAchievement.find_or_initialize_by(achievement_type: "ManagedAsset", level: 5, value: 100, name: "Mid-life crisis", earned: false).update(points: 1500)

GamificationAchievement.find_or_initialize_by(achievement_type: "HelpTicket", level: 1, value: 1, name: "Good Samaritan", earned: false).update(points: 100)
GamificationAchievement.find_or_initialize_by(achievement_type: "HelpTicket", level: 2, value: 10, name: "Teacher's aide", earned: false).update(points: 300)
GamificationAchievement.find_or_initialize_by(achievement_type: "HelpTicket", level: 3, value: 25, name: "The go-to guy", earned: false).update(points: 600)
GamificationAchievement.find_or_initialize_by(achievement_type: "HelpTicket", level: 4, value: 50, name: "Wikipedia, manifested", earned: false).update(points: 1000)
GamificationAchievement.find_or_initialize_by(achievement_type: "HelpTicket", level: 5, value: 100, name: "Rain man", earned: false).update(points: 1500)

GamificationAchievement.find_or_initialize_by(achievement_type: "TelecomService", level: 1, value: 1, name: "Ground control to Major Com", earned: false).update(points: 100)
GamificationAchievement.find_or_initialize_by(achievement_type: "TelecomService", level: 2, value: 2, name: "Llama la llama", earned: false).update(points: 300)
GamificationAchievement.find_or_initialize_by(achievement_type: "TelecomService", level: 3, value: 3, name: "Ring, ring, ring banana phone", earned: false).update(points: 600)
GamificationAchievement.find_or_initialize_by(achievement_type: "TelecomService", level: 4, value: 5, name: "Calling all cars", earned: false).update(points: 1000)
GamificationAchievement.find_or_initialize_by(achievement_type: "TelecomService", level: 5, value: 8, name: "Unicorn", earned: false).update(points: 1500)
