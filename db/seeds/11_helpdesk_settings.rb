helpdesk_settings = [
  # Access Settings
  {
    section_name: "access_settings",
    setting_type: "allow_followers_see_tickets",
    title: "Allow Impacted users or Followers to view tickets in which they are added",
    description: "Enabling this option allows Impacted users or Followers with write-mine permission to view and comment on tickets in which they are added.",
    order: 0
  },
  # Automation Settings
  {
    section_name: "automation_settings",
    setting_type: "disable_automated_tasks_for_status_field_resolution_comment",
    title: "Prevent duplicate notifications on ticket close when a comment has been marked for resolution",
    description: "Enabling this option will prevent users from receiving ticket has been closed notification when closing a ticket with mark as resolution",
    order: 1
  },
  {
    section_name: "automation_settings",
    setting_type: "allow_self_action_notification",
    title: "Allow ticket notifications for own actions",
    description: "Enabling this option will allow users to receive ticket notifications/emails for actions performed by themselves.",
    order: 2
  },
  {
    section_name: "automation_settings",
    setting_type: 'set_assigned_to_first_agent_respond_to_ticket',
    title: 'Set assigned to based on the first agent to respond to a ticket',
    description: 'Enabling this option will automatically assign the first agent to comment on or change the status of a ticket.',
    order: 3
  },
  # Display Settings
  {
    section_name: "display_settings",
    setting_type: "customize_ticket_number_for_help_desk",
    title: "Customize ticket number",
    description: "Enabling this option will allow the user to customize help ticket numbers to include a prefix and an initial value.",
    order: 4
  },
  {
    section_name: "display_settings",
    setting_type: "expand_add_new_comment_section",
    title: "Expand add new comment section",
    description: "Enabling this option will automatically expand New Comment section on help ticket page.",
    order: 5
  },
  # Help Center Portal Settings
  {
    section_name: "help_center_settings",
    setting_type: "include_workspace_in_help_center",
    title: "Include this workspace in the Public Help Center",
    description: "Enabling this option will include this workspace in the public help center along with its public FAQs and Articles",
    order: 6
  },
  {
    section_name: "help_center_settings",
    setting_type: "select_new_ui_for_help_center",
    title: "Use modern Help Center design",
    description: "Enable the modern design for your Help Center.",
    order: 7
  },
  {
    section_name: "help_center_settings",
    setting_type: "custom_logo_for_help_center",
    title: "Help Center Logo",
    description: "Uploaded a custom Help Center logo — it will display instead of your Company Logo.",
    order: 8
  },
  {
    section_name: "help_center_settings",
    setting_type: "custom_favicon_for_help_center",
    title: "Help Center Favicon",
    description: "Uploaded a custom favicon image — it will display instead of our default Genuity favicon.",
    order: 9
  },
  {
    section_name: "help_center_settings",
    setting_type: "bg_color_for_help_center",
    title: "Background color for Help Center",
    description: "Choose the background color for your Help Center.",
    order: 10
  },
  {
    section_name: "help_center_settings",
    setting_type: "hyperlinks_color_for_help_center",
    title: "Hyperlinks color for Help Center",
    description: "Choose the hyperlinks color for your Help Center.",
    order: 11
  },
  {
    section_name: "help_center_settings",
    setting_type: "allow_public_custom_forms_to_logged_out_users",
    title: "Allow unauthenticated users to access the public Custom Forms",
    description: "This workspace's public Custom Forms will be accessible to logged out and unregisterd users with this setting enabled.",
    order: 12
  },
  {
    section_name: "help_center_settings",
    setting_type: "allow_faq_page_to_logged_out_users",
    title: "Allow unauthenticated users to access the FAQ page",
    description: "A read-only version of the FAQ page will be accessible to logged out and unregisterd users with this setting enabled.",
    order: 13
  },
  {
    section_name: "help_center_settings",
    setting_type: "allow_public_articles_to_logged_out_users",
    title: "Allow unauthenticated users to access the Public Articles",
    description: "A read-only version of the Public Articles will be accessible to logged out and unregisterd users with this setting enabled.",
    order: 14
  },
  # Email Settings
  {
    section_name: "email_settings",
    setting_type: "allow_the_attachment_links_in_email",
    title: "Settings for attachments in a ticket",
    description: "Enable this setting to send an attachment via email. Disabling this setting will send a link to the attachment instead. (Logged in users can access the link)",
    order: 15
  },
  {
    section_name: "email_settings",
    setting_type: "allow_incoming_requests",
    title: "Allow all incoming emailed requests",
    description: "Tickets emailed to your custom helpdesk email address will automatically create a new ticket unless the sender was explicitly blocked.",
    order: 16
  },
  {
    section_name: "email_settings",
    setting_type: "allow_non_user_to_get_update_about_their_tickets",
    title: "Allow unregistered users to receive help ticket updates via email",
    description: "Enabling this option will allow unregistered users (people not added to Genuity) to get email updates about their tickets.",
    order: 17
  },
  {
    section_name: "email_settings",
    setting_type: "allow_making_email_recipients_ticket_followers",
    title: "Automatically set email recipients as 'impacted users' or 'followers' when creating a ticket via email",
    description: "Enabling this option will set email recipients in the 'to' and 'CC' fields as the 'impacted users' or 'followers' when creating a ticket via email.",
    order: 18
  },
  {
    section_name: "email_settings",
    setting_type: "allow_making_email_comment_recipients_ticket_followers",
    title: "Automatically set email comments recipients as 'impacted users' or 'followers' when creating a comment via email",
    description: "Enabling this option will set email comment recipients in the 'CC' fields as the 'impacted users' or 'followers' when creating a comment via email.",
    order: 19
  },
  {
    section_name: "email_settings",
    setting_type: "only_include_last_email_response_to_ticket_comment",
    title: "Only include last email response as ticket comment",
    description: "Enabling this option allows only the last email response to be added as a comment to a help ticket.",
    order: 20
  },
  {
    section_name: "email_settings",
    setting_type: "disable_adding_email_body_attachments_for_help_ticket_comments",
    title: "Disable adding email body attachments for help ticket comments",
    description: "Enabling this option will allow the user to disable adding email body attachments for the help ticket comments.",
    order: 21
  },
  {
    section_name: "email_settings",
    setting_type: "response_to_closed_ticket",
    title: "Response settings to closed help ticket",
    description: "Choose what to do when a user responds via mail to closed help ticket.",
    order: 22
  }
=begin
  {
    setting_type: "custom_helpdesk_email",
    title: "Custom email address for helpdesk",
    description: "Enter which email you wish to use for your helpdesk. A custom email address requires email verification."
  },
  {
    setting_type: "custom_helpdesk_name",
    title: "Custom name for default email address for helpdesk",
    description: "Enter which name you wish to use for your helpdesk."
  },
=end
]

helpdesk_settings.each do |setting|
  d = DefaultHelpdeskSetting.find_or_initialize_by(setting_type: setting[:setting_type])
  d.title = setting[:title]
  d.description = setting[:description]
  d.order = setting[:order]
  d.section_name = setting[:section_name]
  d.save!
end
