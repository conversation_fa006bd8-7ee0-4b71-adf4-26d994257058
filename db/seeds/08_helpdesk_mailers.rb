helpdesk_mailers = [
  {
    module_name: "Helpdesk",
    event: "assigned_to_anyone",
    description: "Notify all people and groups that are related to a ticket that an agent has been assigned to the ticket"
  },
  {
    module_name: "Helpdesk",
    event: "assigned_to_me",
    description: "Notify me whenever a ticket is assigned to me"
  },
  {
    module_name: "Helpdesk",
    event: "impacted_device_changed",
    description: "The impacted device changed"
  },
  {
    module_name: "Helpdesk",
    event: "follower_changed",
    description: "The follower staff member changed"
  },
  {
    module_name: "Helpdesk",
    event: "priority_changed",
    description: "Notify all people and groups that are related to a ticket of a change to the priority of a ticket"
  },
  {
    module_name: "Helpdesk",
    event: "status_changed",
    description: "Notify all people and groups that are related to a ticket of a change to the status of a ticket"
  },
  {
    module_name: "Helpdesk",
    event: "note_added",
    description: "Notify all people and groups that are related to a ticket of a note being added to a ticket"
  },
  {
    module_name: "Helpdesk",
    event: "attachment_added",
    description: "Notify all people and groups that are related to a ticket that an attachment is added to a ticket"
  },
  {
    module_name: "Helpdesk",
    event: "ticket_created",
    description: "Notify all people and groups that are related to a ticket of the ticket being created"
  }
]

helpdesk_mailers.each do |mailer|
  m = DefaultMailer.find_or_initialize_by(module_name: mailer[:module_name], event: mailer[:event])
  m.description = mailer[:description]
  m.save!
end
