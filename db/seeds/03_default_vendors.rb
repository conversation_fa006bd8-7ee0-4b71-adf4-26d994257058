categories = {}
DefaultCategory.find_each do |cat|
  name = cat.name.gsub(" and ", " And ").remove(' ').underscore
  categories[name.to_sym] = cat
end

tags = {
  saas: 'SaaS',
  business_service: 'Business Service',
  advertising: 'Advertising',
  infrastructure: 'Infrastructure',
  cloud: 'Cloud'
}.with_indifferent_access

start_time = Time.now
%w{ telecom_services vendors }.each do |csv_name|
  CSV.foreach(Rails.root.join("db/seeds/#{csv_name}.csv"), headers: true) do |data|
    default_vendor = DefaultVendor.find_or_initialize_by(name: data[0])
    default_vendor.default_category = nil
    category_name = data[1]&.strip
    default_vendor.default_category = categories[category_name.to_sym] if category_name.present?
    default_vendor.tag = nil
    tag_name = data[3]&.strip
    default_vendor.url = data[2]
    default_vendor.tag = tags[tag_name.to_sym] if tag_name.present?
    default_vendor.is_cloud_platform = ["Azure", "Amazon Web Services"].include?(default_vendor.name)
    default_vendor.updated_at = Time.now
    default_vendor.save!
  end
end
DefaultVendor.where('updated_at < ?', start_time).destroy_all
