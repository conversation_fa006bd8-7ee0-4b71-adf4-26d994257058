if Rails.env.development?
  super_admin = User.where(email: "<EMAIL>").first_or_create! do |admin|
    admin.full_name = "Genuity Admin"
    admin.guid = "58b8d46a-c32c-4c5d-b896-b0f3df4ab388"
    admin.super_admin = true
    admin.has_confirmed_email = true
    admin.password = "Testing!23"
  end

  sample_company = Company.find_or_create_by(name: "Dev Company",
                                             subdomain: "dev-company")

  sample_company.default_logo_url = "https://s3.amazonaws.com/nulodgic-static-assets/images/location_defaults/default#{rand(1..16)}_thumbnail.png"
  sample_company.email = super_admin.email
  sample_company.save!

  locations_path = File.join(Rails.root, 'db', 'seeds', 'locations_xls', 'locations_data.xlsx')
  options = {:company_module=>"location", :class=>"Location"}
  xls_import = ImportExport::CompanyModuleImport.new(File.open(locations_path), sample_company.id, options: options)
  ActiveRecord::Base.transaction do
    xls_import.process_file
    ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
  rescue Exception => e
    Rails.logger.error("Transaction failed: #{e.message}")
    ActiveRecord::Base.connection.execute "ROLLBACK"
    raise e
  end

  company_users_path = Dir[File.join(Rails.root, 'db', 'seeds', 'company_users_xls', 'company_users_data.xlsx')]
  company_users_path.each do |company_user_path|
    xls_import = ImportExport::CompanyUserImport.new(File.open(company_user_path), sample_company.id)
    ActiveRecord::Base.transaction do
       xls_import.process_file
       ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue Exception => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
      raise e
    end
  end  

  vendors_path = File.join(Rails.root, 'db', 'seeds', 'vendors_xls', 'vendors_data.xlsx')
  xls_import = ImportExport::CompanyModuleImport.new(File.open(vendors_path), sample_company.id, options: options)
  ActiveRecord::Base.transaction do
    xls_import.process_file
    ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
  rescue Exception => e
    Rails.logger.error("Transaction failed: #{e.message}")
    ActiveRecord::Base.connection.execute "ROLLBACK"
    raise e
  end

  assets_path = Dir[File.join(Rails.root, 'db', 'seeds', 'assets_xls', '*.xlsx')]
  assets_path.each do |asset_path|
    xls_import = ImportExport::AssetImport.new(File.open(asset_path), sample_company.id)
    ActiveRecord::Base.transaction do
      xls_import.process_file
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue Exception => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
      raise e
    end
  end

  contracts_path = Dir[File.join(Rails.root, 'db', 'seeds', 'contracts_xls', '*.xlsx')]
  contracts_path.each do |contract_path|
    is_fixed_term_contract = contract_path.include?('fixed_term')
    options = {
      contract_type: is_fixed_term_contract ? 'fixed_term_contract' : 'open_ended_contract',
      record_type: is_fixed_term_contract ? 'fixed_term' : 'open_ended'
    }
    xls_import = ImportExport::ContractImport.new(File.open(contract_path), sample_company.id, options: options)
    ActiveRecord::Base.transaction do
      xls_import.process_file
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue Exception => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
      raise e
    end
  end

  telecom_service_path = Dir[File.join(Rails.root, 'db', 'seeds', 'telecom_xls', 'telecom_services_data.xlsx')]
  telecom_service_path.each do |service_path|
    xls_import = ImportExport::TelecomServiceImport.new(File.open(service_path), sample_company.id)
    ActiveRecord::Base.transaction do
      xls_import.process_file
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue Exception => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
      raise e
    end
  end

  telecom_phone_numbers_path = Dir[File.join(Rails.root, 'db', 'seeds', 'telecom_xls', 'phone_numbers_data.xlsx')]
  telecom_phone_numbers_path.each do |ph_number_path|
    xls_import = ImportExport::PhoneNumberImport.new(File.open(ph_number_path), sample_company.id)
    ActiveRecord::Base.transaction do
      xls_import.process_file
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue Exception => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
      raise e
    end
  end

  help_tickets_path = File.join(Rails.root, 'db', 'seeds', 'help_tickets_xls', 'help_tickets.xlsx')
  workbook = RubyXL::Parser.parse(help_tickets_path)
  help_ticket_options = {:company_module=>"helpdesk", :class=>"HelpTicket"}
  xls_import =ImportExport::CompanyModuleImport.new(File.open(help_tickets_path), sample_company.id, options: help_ticket_options)
  ActiveRecord::Base.transaction do
    xls_import.process_file
    ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
  rescue Exception => e
    Rails.logger.error("Transaction failed: #{e.message}")
    ActiveRecord::Base.connection.execute "ROLLBACK"
    raise e
  end

  general_transactions_path = Dir[File.join(Rails.root, 'db', 'seeds', 'general_transactions_xls', '*.xlsx')]
  general_transactions_path.each do |general_transaction_path|
    is_non_recurring_transaction = general_transaction_path.include?('non_recurring')
    options = {
      transaction_type: is_non_recurring_transaction ? 'non_recurring_transaction' : 'recurring_transaction',
      record_type: is_non_recurring_transaction ? 'non_recurring' : 'recurring'
    }
    xls_import = ImportExport::GeneralTransactionImport.new(File.open(general_transaction_path), sample_company.id, options: options)
    ActiveRecord::Base.transaction do
      xls_import.process_file
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue Exception => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
      raise e
    end
  end
end
