start_time = Time.now
prod_informations = [
  { :product_name=>"Advanced Communications", :string_id=>"ADV_COMMS", :guid=>"e4654015-5daf-4a48-9b37-4f309dddd88b", :source=> "Microsoft" },
  { :product_name=>"APP CONNECT IW", :string_id=>"SPZA_IW", :guid=>"8f0c5670-4e56-4892-b06d-91c085d7004f", :source=> "Microsoft" },
  { :product_name=>"AUDIO CONFERENCING", :string_id=>"MCOMEETADV", :guid=>"0c266dff-15dd-4b49-8397-2bb16070ed52", :source=> "Microsoft" },
  { :product_name=>"AZURE ACTIVE DIRECTORY BASIC", :string_id=>"AAD_BASIC", :guid=>"2b9c8e7c-319c-43a2-a2a0-48c5c6161de7", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/azure_active_directory.png", :source=> "Microsoft" },
  { :product_name=>"AZURE ACTIVE DIRECTORY PREMIUM P1", :string_id=>"AAD_PREMIUM", :guid=>"078d2b04-f1bd-4111-bbd4-b4b1b354cef4", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/azure_active_directory.png", :source=> "Microsoft" },
  { :product_name=>"AZURE ACTIVE DIRECTORY PREMIUM P2", :string_id=>"AAD_PREMIUM_P2", :guid=>"84a661c4-e949-4bd2-a560-ed7766fcaf2b", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/azure_active_directory.png", :source=> "Microsoft" },
  { :product_name=>"AZURE INFORMATION PROTECTION PLAN 1", :string_id=>"RIGHTSMANAGEMENT", :guid=>"c52ea49f-fe5d-4e95-93ba-1de91d380f89", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/azure_protection.png", :source=> "Microsoft" },
  { :product_name=>"DYNAMICS 365 CUSTOMER ENGAGEMENT PLAN ENTERPRISE EDITION", :string_id=>"DYN365_ENTERPRISE_PLAN1", :guid=>"ea126fc5-a19e-42e2-a731-da9d437bffcf", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/dynamics_365.png", :source=> "Microsoft" },
  { :product_name=>"DYNAMICS 365 FOR CUSTOMER SERVICE ENTERPRISE EDITION", :string_id=>"DYN365_ENTERPRISE_CUSTOMER_SERVICE", :guid=>"749742bf-0d37-4158-a120-33567104deeb", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/dynamics_365.png", :source=> "Microsoft" },
  { :product_name=>"DYNAMICS 365 FOR FINANCIALS BUSINESS EDITION", :string_id=>"DYN365_FINANCIALS_BUSINESS_SKU", :guid=>"cc13a803-544e-4464-b4e4-6d6169a138fa", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/dynamics_365.png", :source=> "Microsoft" },
  { :product_name=>"DYNAMICS 365 FOR SALES AND CUSTOMER SERVICE ENTERPRISE EDITION", :string_id=>"DYN365_ENTERPRISE_SALES_CUSTOMERSERVICE", :guid=>"8edc2cf8-6438-4fa9-b6e3-aa1660c640cc", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/dynamics_365.png", :source=> "Microsoft" },
  { :product_name=>"DYNAMICS 365 FOR SALES ENTERPRISE EDITION", :string_id=>"DYN365_ENTERPRISE_SALES", :guid=>"1e1a282c-9c54-43a2-9310-98ef728faace", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/dynamics_365.png", :source=> "Microsoft" },
  { :product_name=>"DYNAMICS 365 FOR TEAM MEMBERS ENTERPRISE EDITION", :string_id=>"DYN365_ENTERPRISE_TEAM_MEMBERS", :guid=>"8e7a3d30-d97d-43ab-837c-d7701cef83dc", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/dynamics_365.png", :source=> "Microsoft" },
  { :product_name=>"DYNAMICS 365 UNF OPS PLAN ENT EDITION", :string_id=>"Dynamics_365_for_Operations", :guid=>"ccba3cfe-71ef-423a-bd87-b6df3dce59a9", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/dynamics_365.png", :source=> "Microsoft" },
  { :product_name=>"ENTERPRISE MOBILITY + SECURITY E3", :string_id=>"EMS", :guid=>"efccb6f7-5641-4e0e-bd10-b4976e1bf68e", :source=> "Microsoft" },
  { :product_name=>"ENTERPRISE MOBILITY + SECURITY E5", :string_id=>"EMSPREMIUM", :guid=>"b05e124f-c7cc-45a0-a6aa-8cf78c946968", :source=> "Microsoft" },
  { :product_name=>"EXCHANGE ONLINE (PLAN 1)", :string_id=>"EXCHANGESTANDARD", :guid=>"4b9405b0-7788-4568-add1-99614e613b69", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/exchange_online.png", :source=> "Microsoft" },
  { :product_name=>"EXCHANGE ONLINE (PLAN 2)", :string_id=>"EXCHANGEENTERPRISE", :guid=>"19ec0d23-8335-4cbd-94ac-6050e30712fa", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/exchange_online.png", :source=> "Microsoft" },
  { :product_name=>"EXCHANGE ONLINE ARCHIVING FOR EXCHANGE ONLINE", :string_id=>"EXCHANGEARCHIVE_ADDON", :guid=>"ee02fd1b-340e-4a4b-b355-4a514e4c8943", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/exchange_online.png", :source=> "Microsoft" },
  { :product_name=>"EXCHANGE ONLINE ARCHIVING FOR EXCHANGE SERVER", :string_id=>"EXCHANGEARCHIVE", :guid=>"90b5e015-709a-4b8b-b08e-3200f994494c", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/exchange_online.png", :source=> "Microsoft" },
  { :product_name=>"EXCHANGE ONLINE ESSENTIALS", :string_id=>"EXCHANGEESSENTIALS", :guid=>"7fc0182e-d107-4556-8329-7caaa511197b", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/exchange_online.png", :source=> "Microsoft" },
  { :product_name=>"EXCHANGE ONLINE ESSENTIALS", :string_id=>"EXCHANGE_S_ESSENTIALS", :guid=>"e8f81a67-bd96-4074-b108-cf193eb9433b", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/exchange_online.png", :source=> "Microsoft" },
  { :product_name=>"EXCHANGE ONLINE KIOSK", :string_id=>"EXCHANGEDESKLESS", :guid=>"80b2d799-d2ba-4d2a-8842-fb0d0f3a4b82", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/exchange_online.png", :source=> "Microsoft" },
  { :product_name=>"EXCHANGE ONLINE POP", :string_id=>"EXCHANGETELCO", :guid=>"cb0a98a8-11bc-494c-83d9-c1b1ac65327e", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/exchange_online.png", :source=> "Microsoft" },
  { :product_name=>"INTUNE", :string_id=>"INTUNE_A", :guid=>"061f9ace-7d42-4136-88ac-31dc755f143f", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/intune_logo.png", :source=> "Microsoft" },
  { :product_name=>"Microsoft 365 A1", :string_id=>"M365EDU_A1", :guid=>"b17653a4-2443-4e8c-a550-18249dda78bb", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/mso_365.png", :source=> "Microsoft" },
  { :product_name=>"Microsoft 365 A3 for faculty", :string_id=>"M365EDU_A3_FACULTY", :guid=>"4b590615-0888-425a-a965-b3bf7789848d", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/mso_365.png", :source=> "Microsoft" },
  { :product_name=>"Microsoft 365 A3 for students", :string_id=>"M365EDU_A3_STUDENT", :guid=>"7cfd9a2b-e110-4c39-bf20-c6a3f36a3121", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/mso_365.png", :source=> "Microsoft" },
  { :product_name=>"Microsoft 365 A5 for faculty", :string_id=>"M365EDU_A5_FACULTY", :guid=>"e97c048c-37a4-45fb-ab50-922fbf07a370", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/mso_365.png", :source=> "Microsoft" },
  { :product_name=>"Microsoft 365 A5 for students", :string_id=>"M365EDU_A5_STUDENT", :guid=>"46c119d4-0379-4a9d-85e4-97c66d3f909e", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/mso_365.png", :source=> "Microsoft" },
  { :product_name=>"MICROSOFT 365 APPS FOR BUSINESS", :string_id=>"O365_BUSINESS", :guid=>"cdd28e44-67e3-425e-be4c-737fab2899d3", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/mso_365.png", :source=> "Microsoft" },
  { :product_name=>"MICROSOFT 365 APPS FOR BUSINESS", :string_id=>"SMB_BUSINESS", :guid=>"b214fe43-f5a3-4703-beeb-fa97188220fc", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/mso_365.png", :source=> "Microsoft" },
  { :product_name=>"MICROSOFT 365 APPS FOR ENTERPRISE", :string_id=>"OFFICESUBSCRIPTION", :guid=>"c2273bd0-dff7-4215-9ef5-2c7bcfb06425", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/mso_365.png", :source=> "Microsoft" },
  { :product_name=>"MICROSOFT 365 BUSINESS BASIC", :string_id=>"O365_BUSINESS_ESSENTIALS", :guid=>"3b555118-da6a-4418-894f-7df1e2096870", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/mso_365.png", :source=> "Microsoft" },
  { :product_name=>"MICROSOFT 365 BUSINESS BASIC", :string_id=>"SMB_BUSINESS_ESSENTIALS", :guid=>"dab7782a-93b1-4074-8bb1-0e61318bea0b", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/mso_365.png", :source=> "Microsoft" },
  { :product_name=>"MICROSOFT 365 BUSINESS STANDARD", :string_id=>"O365_BUSINESS_PREMIUM", :guid=>"f245ecc8-75af-4f8e-b61f-27d8114de5f3", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/mso_365.png", :source=> "Microsoft" },
  { :product_name=>"MICROSOFT 365 BUSINESS STANDARD", :string_id=>"SMB_BUSINESS_PREMIUM", :guid=>"ac5cef5d-921b-4f97-9ef3-c99076e5470f", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/mso_365.png", :source=> "Microsoft" },
  { :product_name=>"MICROSOFT 365 BUSINESS PREMIUM", :string_id=>"SPB", :guid=>"cbdc14ab-d96c-4c30-b9f4-6ada7cdc1d46", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/mso_365.png", :source=> "Microsoft" },
  { :product_name=>"MICROSOFT 365 E3", :string_id=>"SPE_E3", :guid=>"05e9a617-0261-4cee-bb44-138d3ef5d965", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/mso_365.png", :source=> "Microsoft" },
  { :product_name=>"Microsoft 365 E5", :string_id=>"SPE_E5", :guid=>"06ebc4ee-1bb5-47dd-8120-11324bc54e06", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/mso_365.png", :source=> "Microsoft" },
  { :product_name=>"Microsoft 365 E3_USGOV_DOD", :string_id=>"SPE_E3_USGOV_DOD", :guid=>"d61d61cc-f992-433f-a577-5bd016037eeb", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/mso_365.png", :source=> "Microsoft" },
  { :product_name=>"Microsoft 365 E3_USGOV_GCCHIGH", :string_id=>"SPE_E3_USGOV_GCCHIGH", :guid=>"ca9d1dd9-dfe9-4fef-b97c-9bc1ea3c3658", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/mso_365.png", :source=> "Microsoft" },
  { :product_name=>"Microsoft 365 E5 Compliance", :string_id=>"INFORMATION_PROTECTION_COMPLIANCE", :guid=>"184efa21-98c3-4e5d-95ab-d07053a96e67", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/mso_365.png", :source=> "Microsoft" },
  { :product_name=>"Microsoft 365 E5 Security", :string_id=>"IDENTITY_THREAT_PROTECTION", :guid=>"26124093-3d78-432b-b5dc-48bf992543d5", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/mso_365.png", :source=> "Microsoft" },
  { :product_name=>"Microsoft 365 E5 Security for EMS E5", :string_id=>"IDENTITY_THREAT_PROTECTION_FOR_EMS_E5", :guid=>"44ac31e7-2999-4304-ad94-c948886741d4", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/mso_365.png", :source=> "Microsoft" },
  { :product_name=>"Microsoft 365 F1", :string_id=>"M365_F1", :guid=>"44575883-256e-4a79-9da4-ebe9acabe2b2", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/mso_365.png", :source=> "Microsoft" },
  { :product_name=>"Microsoft 365 F3", :string_id=>"SPE_F1", :guid=>"66b55226-6b4f-492c-910c-a3b7a3c9d993", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/mso_365.png", :source=> "Microsoft" },
  { :product_name=>"Microsoft Defender Advanced Threat Protection", :string_id=>"WIN_DEF_ATP", :guid=>"111046dd-295b-4d6d-9724-d52ac90bd1f2", :source=> "Microsoft" },
  { :product_name=>"MICROSOFT DYNAMICS CRM ONLINE BASIC", :string_id=>"CRMPLAN2", :guid=>"906af65a-2970-46d5-9b58-4e9aa50f0657", :source=> "Microsoft" },
  { :product_name=>"MICROSOFT DYNAMICS CRM ONLINE", :string_id=>"CRMSTANDARD", :guid=>"d17b27af-3f49-4822-99f9-56a661538792", :source=> "Microsoft" },
  { :product_name=>"MS IMAGINE ACADEMY", :string_id=>"IT_ACADEMY_AD", :guid=>"ba9a34de-4489-469d-879c-0f0f145321cd", :source=> "Microsoft" },
  { :product_name=>"Office 365 A5 for faculty", :string_id=>"ENTERPRISEPREMIUM_FACULTY", :guid=>"a4585165-0533-458a-97e3-c400570268c4", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/products/office365-business-essentials.png", :source=> "Microsoft" },
  { :product_name=>"Office 365 A5 for students", :string_id=>"ENTERPRISEPREMIUM_STUDENT", :guid=>"ee656612-49fa-43e5-b67e-cb1fdf7699df", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/products/office365-business-essentials.png", :source=> "Microsoft" },
  { :product_name=>"Office 365 Advanced Compliance", :string_id=>"EQUIVIO_ANALYTICS", :guid=>"1b1b1f7a-8355-43b6-829f-336cfccb744c", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/products/office365-business-essentials.png", :source=> "Microsoft" },
  { :product_name=>"Office 365 Advanced Threat Protection (Plan 1)", :string_id=>"ATP_ENTERPRISE", :guid=>"4ef96642-f096-40de-a3e9-d83fb2f90211", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/products/office365-business-essentials.png", :source=> "Microsoft" },
  { :product_name=>"OFFICE 365 E1", :string_id=>"STANDARDPACK", :guid=>"18181a46-0d4e-45cd-891e-60aabd171b4e", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/products/office365-business-essentials.png", :source=> "Microsoft" },
  { :product_name=>"OFFICE 365 E2", :string_id=>"STANDARDWOFFPACK", :guid=>"6634e0ce-1a9f-428c-a498-f84ec7b8aa2e", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/products/office365-business-essentials.png", :source=> "Microsoft" },
  { :product_name=>"OFFICE 365 E3", :string_id=>"ENTERPRISEPACK", :guid=>"6fd2c87f-b296-42f0-b197-1e91e994b900", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/products/office365-business-essentials.png", :source=> "Microsoft" },
  { :product_name=>"OFFICE 365 E3 DEVELOPER", :string_id=>"DEVELOPERPACK", :guid=>"189a915c-fe4f-4ffa-bde4-85b9628d07a0", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/products/office365-business-essentials.png", :source=> "Microsoft" },
  { :product_name=>"Office 365 E3_USGOV_DOD", :string_id=>"ENTERPRISEPACK_USGOV_DOD", :guid=>"b107e5a3-3e60-4c0d-a184-a7e4395eb44c", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/products/office365-business-essentials.png", :source=> "Microsoft" },
  { :product_name=>"Office 365 E3_USGOV_GCCHIGH", :string_id=>"ENTERPRISEPACK_USGOV_GCCHIGH", :guid=>"aea38a85-9bd5-4981-aa00-616b411205bf", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/products/office365-business-essentials.png", :source=> "Microsoft" },
  { :product_name=>"OFFICE 365 E4", :string_id=>"ENTERPRISEWITHSCAL", :guid=>"1392051d-0cb9-4b7a-88d5-621fee5e8711", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/products/office365-business-essentials.png", :source=> "Microsoft" },
  { :product_name=>"OFFICE 365 E5", :string_id=>"ENTERPRISEPREMIUM", :guid=>"c7df2760-2c81-4ef7-b578-5b5392b571df", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/products/office365-business-essentials.png", :source=> "Microsoft" },
  { :product_name=>"OFFICE 365 E5 WITHOUT AUDIO CONFERENCING", :string_id=>"ENTERPRISEPREMIUM_NOPSTNCONF", :guid=>"26d45bd9-adf1-46cd-a9e1-51e9a5524128", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/products/office365-business-essentials.png", :source=> "Microsoft" },
  { :product_name=>"OFFICE 365 F1", :string_id=>"DESKLESSPACK", :guid=>"4b585984-651b-448a-9e53-3b10f069cf7f", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/products/office365-business-essentials.png", :source=> "Microsoft" },
  { :product_name=>"OFFICE 365 MIDSIZE BUSINESS", :string_id=>"MIDSIZEPACK", :guid=>"04a7fb0d-32e0-4241-b4f5-3f7618cd1162", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/products/office365-business-essentials.png", :source=> "Microsoft" },
  { :product_name=>"OFFICE 365 SMALL BUSINESS", :string_id=>"LITEPACK", :guid=>"bd09678e-b83c-4d3f-aaba-3dad4abd128b", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/products/office365-business-essentials.png", :source=> "Microsoft" },
  { :product_name=>"OFFICE 365 SMALL BUSINESS PREMIUM", :string_id=>"LITEPACK_P2", :guid=>"fc14ec4a-4169-49a4-a51e-2c852931814b", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/products/office365-business-essentials.png", :source=> "Microsoft" },
  { :product_name=>"ONEDRIVE FOR BUSINESS (PLAN 1)", :string_id=>"WACONEDRIVESTANDARD", :guid=>"e6778190-713e-4e4f-9119-8b8238de25df", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/onedrive.png", :source=> "Microsoft" },
  { :product_name=>"ONEDRIVE FOR BUSINESS (PLAN 2)", :string_id=>"WACONEDRIVEENTERPRISE", :guid=>"ed01faf2-1d88-4947-ae91-45ca18703a96", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/onedrive.png", :source=> "Microsoft" },
  { :product_name=>"POWER APPS PER USER PLAN", :string_id=>"POWERAPPS_PER_USER", :guid=>"b30411f5-fea1-4a59-9ad9-3db7c7ead579", :source=> "Microsoft" },
  { :product_name=>"POWER BI FOR OFFICE 365 ADD-ON", :string_id=>"POWER_BI_ADDON", :guid=>"45bc2c81-6072-436a-9b0b-3b12eefbc402", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/power_bi.png", :source=> "Microsoft" },
  { :product_name=>"POWER BI PRO", :string_id=>"POWER_BI_PRO", :guid=>"f8a1db68-be16-40ed-86d5-cb42ce701560", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/power_bi.png", :source=> "Microsoft" },
  { :product_name=>"PROJECT FOR OFFICE 365", :string_id=>"PROJECTCLIENT", :guid=>"a10d5e58-74da-4312-95c8-76be4e5b75a0", :source=> "Microsoft" },
  { :product_name=>"PROJECT ONLINE ESSENTIALS", :string_id=>"PROJECTESSENTIALS", :guid=>"776df282-9fc0-4862-99e2-70e561b9909e", :source=> "Microsoft" },
  { :product_name=>"PROJECT ONLINE PREMIUM", :string_id=>"PROJECTPREMIUM", :guid=>"09015f9f-377f-4538-bbb5-f75ceb09358a", :source=> "Microsoft" },
  { :product_name=>"PROJECT ONLINE PREMIUM WITHOUT PROJECT CLIENT", :string_id=>"PROJECTONLINE_PLAN_1",:guid=>"2db84718-652c-47a7-860c-f10d8abbdae3", :source=> "Microsoft" },
  { :product_name=>"PROJECT ONLINE PROFESSIONAL", :string_id=>"PROJECTPROFESSIONAL", :guid=>"53818b1b-4a27-454b-8896-0dba576410e6", :source=> "Microsoft" },
  { :product_name=>"PROJECT ONLINE WITH PROJECT FOR OFFICE 365", :string_id=>"PROJECTONLINE_PLAN_2", :guid=>"f82a60b8-1ee3-4cfb-a4fe-1c6a53c2656c", :source=> "Microsoft" },
  { :product_name=>"SHAREPOINT ONLINE (PLAN 1)", :string_id=>"SHAREPOINTSTANDARD", :guid=>"1fc08a02-8b3d-43b9-831e-f76859e04e1a", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/sharepoint.png", :source=> "Microsoft" },
  { :product_name=>"SHAREPOINT ONLINE (PLAN 2)", :string_id=>"SHAREPOINTENTERPRISE", :guid=>"a9732ec9-17d9-494c-a51c-d6b45b384dcb", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/sharepoint.png", :source=> "Microsoft" },
  { :product_name=>"SKYPE FOR BUSINESS CLOUD PBX", :string_id=>"MCOEV", :guid=>"e43b5b99-8dfb-405f-9987-dc307f34bcbd", :source=> "Microsoft" },
  { :product_name=>"SKYPE FOR BUSINESS ONLINE (PLAN 1)", :string_id=>"MCOIMP", :guid=>"b8b749f8-a4ef-4887-9539-c95b1eaa5db7", :source=> "Microsoft" },
  { :product_name=>"SKYPE FOR BUSINESS ONLINE (PLAN 2)", :string_id=>"MCOSTANDARD", :guid=>"d42c793f-6c78-4f43-92ca-e8f6a02b035f", :source=> "Microsoft" },
  { :product_name=>"SKYPE FOR BUSINESS PSTN DOMESTIC AND INTERNATIONAL CALLING", :string_id=>"MCOPSTN2", :guid=>"d3b4fe1f-9992-4930-8acb-ca6ec609365e", :source=> "Microsoft" },
  { :product_name=>"SKYPE FOR BUSINESS PSTN DOMESTIC CALLING", :string_id=>"MCOPSTN1", :guid=>"0dab259f-bf13-4952-b7f8-7db8f131b28d", :source=> "Microsoft" },
  { :product_name=>"SKYPE FOR BUSINESS PSTN DOMESTIC CALLING (120 Minutes)", :string_id=>"MCOPSTN5", :guid=>"54a152dc-90de-4996-93d2-bc47e670fc06", :source=> "Microsoft" },
  { :product_name=>"VISIO ONLINE PLAN 1", :string_id=>"VISIOONLINE_PLAN1", :guid=>"4b244418-9658-4451-a2b8-b5e2b364e9bd", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/visio.png", :source=> "Microsoft" },
  { :product_name=>"VISIO Online Plan 2", :string_id=>"VISIOCLIENT", :guid=>"c5928f49-12ba-48f7-ada3-0d743a3601d5", :logo_url=>"https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/apps/visio.png", :source=> "Microsoft" },
  { :product_name=>"WINDOWS 10 ENTERPRISE E3", :string_id=>"WIN10_PRO_ENT_SUB", :guid=>"cb10e6cd-9da4-4992-867b-67546b1db821", :source=> "Microsoft" },
  { :product_name=>"Windows 10 Enterprise E5", :string_id=>"WIN10_VDA_E5", :guid=>"488ba24a-39a9-4473-8ee5-19291e71b002", :source=> "Microsoft" },
  { :product_name=> 'Advanced Communications', :string_id=> 'ADV_COMMS', :guid=> 'e4654015-5daf-4a48-9b37-4f309dddd88b', :source=> "Microsoft" },
  { :product_name=> "AI Builder Capacity add-on", :string_id=> "CDSAICAPACITY", :guid=> "d2dea78b-507c-4e56-b400-39447f4738f8", :source=> "Microsoft" },
  { :product_name=> "App governance add-on to Microsoft Defender for Cloud Apps", :string_id=> "Microsoft_Cloud_App_Security_App_Governance_Add_On", :guid=> "9706eed9-966f-4f1b-94f6-bb2b4af99a5b", :source=> "Microsoft" },
  { :product_name=> "Business Apps (free)", :string_id=> "SMB_APPS", :guid=> "90d8b3f8-712e-4f7b-aa1e-62e7ae6cbe96", :source=> "Microsoft" },
  { :product_name=> "Common Data Service for Apps File Capacity", :string_id=> "CDS_FILE_CAPACITY", :guid=> "631d5fb1-a668-4c2a-9427-8830665a742e", :source=> "Microsoft" },
  { :product_name=> "Common Data Service Database Capacity", :string_id=> "CDS_DB_CAPACITY", :guid=> "e612d426-6bc3-4181-9658-91aa906b0ac0", :source=> "Microsoft" },
  { :product_name=> "Common Data Service Log Capacity", :string_id=> "CDS_LOG_CAPACITY", :guid=> "448b063f-9cc6-42fc-a0e6-40e08724a395", :source=> "Microsoft" },
  { :product_name=> "Communications Credtis", :string_id=> "MCOPSTNC", :guid=> "47794cd0-f0e5-45c5-9033-2eb6b5fc84e0", :source=> "Microsoft" },
  { :product_name=> "Compliance Manager Premium Assessment Add-On for GCC", :string_id=> "CMPA_addon_GCC", :guid=> "a9d7ef53-9bea-4a2a-9650-fa7df58fe094", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 - Additional Database Storage (Qualified Offer)", :string_id=> "CRMSTORAGE", :guid=> "328dc228-00bc-48c6-8b09-1fbc8bc3435d", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 - Additional Production Instance (Qualified Offer)", :string_id=> "CRMINSTANCE", :guid=> "9d776713-14cb-4697-a21d-9a52455c738a", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 - Additional Non-Production Instance (Qualified Offer)", :string_id=> "CRMTESTINSTANCE", :guid=> "e06abcc2-7ec5-4a79-b08b-d9c282376f72", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 AI for Market Insights (Preview)", :string_id=> "SOCIAL_ENGAGEMENT_APP_USER", :guid=> "c6df1e30-1c9f-427f-907c-3d913474a1c7", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Asset Management Addl Assets", :string_id=> "DYN365_ASSETMANAGEMENT", :guid=> "673afb9d-d85b-40c2-914e-7bf46cd5cd75", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Business Central Additional Environment Addon", :string_id=> "DYN365_BUSCENTRAL_ADD_ENV_ADDON", :guid=> "a58f5506-b382-44d4-bfab-225b2fbf8390", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Business Central Database Capacity", :string_id=> "DYN365_BUSCENTRAL_DB_CAPACITY", :guid=> "7d0d4f9a-2686-4cb8-814c-eff3fdab6d74", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Business Central Essentials", :string_id=> "DYN365_BUSCENTRAL_ESSENTIAL", :guid=> "2880026b-2b0c-4251-8656-5d41ff11e3aa", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Business Central External Accountant", :string_id=> "DYN365_FINANCIALS_ACCOUNTANT_SKU", :guid=> "9a1e33ed-9697-43f3-b84c-1b0959dbb1d4", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Business Central for IWs", :string_id=> "PROJECT_MADEIRA_PREVIEW_IW_SKU", :guid=> "6a4a1628-9b9a-424d-bed5-4118f0ede3fd", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Business Central Premium", :string_id=> "DYN365_BUSCENTRAL_PREMIUM", :guid=> "f991cecc-3f91-4cd0-a9a8-bf1c8167e029", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Business Central Team Members", :string_id=> "DYN365_BUSCENTRAL_TEAM_MEMBER", :guid=> "2e3c4023-80f6-4711-aa5d-29e0ecb46835", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Customer Insights vTrial", :string_id=> "DYN365_CUSTOMER_INSIGHTS_VIRAL", :guid=> "036c2481-aa8a-47cd-ab43-324f0c157c2d", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 for Customer Service Enterprise Attach to Qualifying Dynamics 365 Base Offer A", :string_id=> "D365_CUSTOMER_SERVICE_ENT_ATTACH", :guid=> "eb18b715-ea9d-4290-9994-2ebf4b5042d2", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Customer Service Enterprise Viral Trial", :string_id=> "Dynamics_365_Customer_Service_Enterprise_viral_trial", :guid=> "1e615a51-59db-4807-9957-aa83c3657351", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Customer Service Insights Trial", :string_id=> "DYN365_AI_SERVICE_INSIGHTS", :guid=> "61e6bd70-fbdb-4deb-82ea-912842f39431", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Customer Voice Trial", :string_id=> "FORMS_PRO", :guid=> "bc946dac-7877-4271-b2f7-99d2db13cd2c", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Customer Service Professional", :string_id=> "DYN365_CUSTOMER_SERVICE_PRO", :guid=> "1439b6e2-5d59-4873-8c59-d60e2a196e92", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Customer Voice", :string_id=> "DYN365_CUSTOMER_VOICE_BASE", :guid=> "359ea3e6-8130-4a57-9f8f-ad897a0342f1", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Customer Voice Additional Responses", :string_id=> "DYN365_CUSTOMER_VOICE_ADDON", :guid=> "65f71586-ade3-4ce1-afc0-1b452eaf3782", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Enterprise Edition - Additional Portal (Qualified Offer)", :string_id=> "CRM_ONLINE_PORTAL", :guid=> "a4bfb28e-becc-41b0-a454-ac680dc258d3", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Field Service Viral Trial", :string_id=> "Dynamics_365_Field_Service_Enterprise_viral_trial", :guid=> "29fcd665-d8d1-4f34-8eed-3811e3fca7b3", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Finance", :string_id=> "DYN365_FINANCE", :guid=> "55c9eb4e-c746-45b4-b255-9ab6b19d5c62", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 for Case Management Enterprise Edition", :string_id=> "DYN365_ENTERPRISE_CASE_MANAGEMENT",:guid=> "d39fb075-21ae-42d0-af80-22a2599749e0", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 for Customer Service Chat", :string_id=> "DYN365_CS_CHAT",:guid=> "7d7af6c2-0be6-46df-84d1-c181b0272909", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 for Field Service Attach to Qualifying Dynamics 365 Base Offer", :string_id=> "D365_FIELD_SERVICE_ATTACH",:guid=> "a36cdaa2-a806-4b6e-9ae0-28dbd993c20e", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 for Field Service Enterprise Edition", :string_id=> "DYN365_ENTERPRISE_FIELD_SERVICE", :guid=> "c7d15985-e746-4f01-b113-20b575898250", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Hybrid Connector", :string_id=> "CRM_HYBRIDCONNECTOR", :guid=> "de176c31-616d-4eae-829a-718918d7ec23", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 for Marketing Additional Application", :string_id=> "DYN365_MARKETING_APPLICATION_ADDON", :guid=> "99c5688b-6c75-4496-876f-07f0fbd69add", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 for Marketing Addnl Contacts Tier 5", :string_id=> "DYN365_MARKETING_CONTACT_ADDON_T5", :guid=> "d8eec316-778c-4f14-a7d1-a0aca433b4e7", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 for Marketing Additional Non-Prod Application", :string_id=> "DYN365_MARKETING_SANDBOX_APPLICATION_ADDON", :guid=> "c393e9bd-2335-4b46-8b88-9e2a86a85ec1", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 for Marketing Attach", :string_id=> "DYN365_MARKETING_APP_ATTACH", :guid=> "85430fb9-02e8-48be-9d7e-328beb41fa29", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 for Marketing USL", :string_id=> "D365_MARKETING_USER", :guid=> "4b32a493-9a67-4649-8eb9-9fc5a5f75c12", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Sales Premium", :string_id=> "DYN365_SALES_PREMIUM", :guid=> "2edaa1dc-966d-4475-93d6-8ee8dfd96877", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 for Marketing Business Edition", :string_id=> "DYN365_BUSINESS_MARKETING", :guid=> "238e2f8d-e429-4035-94db-6926be4ffe7b", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Regulatory Service - Enterprise Edition Trial",:string_id=> "DYN365_REGULATORY_SERVICE", :guid=> "7ed4877c-0863-4f69-9187-245487128d4f", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Sales Premium Viral Trial", :string_id=> "Dynamics_365_Sales_Premium_Viral_Trial", :guid=> "6ec92958-3cc1-49db-95bd-bc6b3798df71", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 For Sales Professional", :string_id=> "D365_SALES_PRO", :guid=> "be9f9771-1c64-4618-9907-************", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 for Supply Chain Management", :string_id=> "DYN365_SCM", :guid=> "f2e48cb3-9da0-42cd-8464-4a54ce198ad0", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 for Talent", :string_id=> "SKU_Dynamics_365_for_HCM_Trial", :guid=> "3a256e9a-15b6-4092-b0dc-82993f4debc6", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Talent: Attract", :string_id=> "Dynamics_365_Hiring_SKU", :guid=> "e561871f-74fa-4f02-abee-5b0ef54dd36d", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 for Team Members Enterprise Edition", :string_id=> "DYN365_ENTERPRISE_TEAM_MEMBERS", :guid=> "8e7a3d30-d97d-43ab-837c-d7701cef83dc", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Guides", :string_id=> "GUIDES_USER", :guid=> "0a389a77-9850-4dc4-b600-bc66fdfefc60", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Operations - Device", :string_id=> "Dynamics_365_for_Operations_Devices", :guid=> "3bbd44ed-8a70-4c07-9088-6232ddbd5ddd", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 P1 Tria for Information Workers", :string_id=> "DYN365_ENTERPRISE_P1_IW", :guid=> "338148b6-1b11-4102-afb9-f92b6cdc0f8d", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Remote Assist", :string_id=> "MICROSOFT_REMOTE_ASSIST", :guid=> "7a551360-26c4-4f61-84e6-ef715673e083", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Sales Enterprise Attach to Qualifying Dynamics 365 Base Offer", :string_id=> "D365_SALES_ENT_ATTACH", :guid=> "5b22585d-1b71-4c6b-b6ec-160b1a9c2323", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Talent: Onboard", :string_id=> "DYNAMICS_365_ONBOARDING_SKU", :guid=> "b56e7ccc-d5c7-421f-a23b-5c18bdbad7c0", :source=> "Microsoft" },
  { :product_name=> "Dynamics 365 Team Members", :string_id=> "DYN365_TEAM_MEMBERS", :guid=> "7ac9fe77-66b7-4e5e-9e46-10eed1cff547", :source=> "Microsoft" },
  { :product_name=> "Enterprise Mobility + Security A3 for Faculty", :string_id=> "EMS_EDU_FACULTY", :guid=> "aedfac18-56b8-45e3-969b-53edb4ba4952", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 A5 for students use benefit", :string_id=> "M365EDU_A5_STUUSEBNFT", :guid=> "31d57bc7-3a05-4867-ab53-97a17835a411", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 A5 without Audio Conferencing for students use benefit", :string_id=> "M365EDU_A5_NOPSTNCONF_STUUSEBNFT", :guid=> "81441ae1-0b31-4185-a6c0-32b6b84d419f", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 Audio Conferencing Pay-Per-Minute - EA", :string_id=> "MCOMEETACPEA", :guid=> "df9561a4-4969-4e6a-8e73-c601b68ec077", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 Business Voice", :string_id=> "BUSINESS_VOICE_MED2", :guid=> "a6051f20-9cbc-47d2-930d-419183bf6cf1", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 Business Voice (without calling plan)", :string_id=> "BUSINESS_VOICE_DIRECTROUTING", :guid=> "d52db95a-5ecb-46b6-beb0-190ab5cda4a8", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 Domestic Calling Plan (120 Minutes)", :string_id=> "MCOPSTN_5", :guid=> "11dee6af-eca8-419f-8061-6864517c1875", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 Domestic Calling Plan for GCC", :string_id=> "MCOPSTN_1_GOV", :guid=> "923f58ab-fca1-46a1-92f9-89fda21238a8", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 E3 - Unattended License", :string_id=> "SPE_E3_RPA1", :guid=> "c2ac2ee4-9bb1-47e4-8541-d689c7e83371", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 E3 (500 seats min)_HUB", :string_id=> "Microsoft_365_E3", :guid=> "0c21030a-7e60-4ec7-9a0f-0042e0e0211a", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 E5 (500 seats min)_HUB", :string_id=> "Microsoft_365_E5", :guid=> "db684ac5-c0e7-4f92-8284-ef9ebde75d33", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 E5 Developer (without Windows and Audio Conferencing)", :string_id=> "DEVELOPERPACK_E5", :guid=> "c42b9cae-ea4f-4ab7-9717-81576235ccac", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 E5 with Calling Minutes", :string_id=> "SPE_E5_CALLINGMINUTES", :guid=> "a91fc4e0-65e5-4266-aa76-4037509c1626", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 E5 without Audio Conferencing", :string_id=> "SPE_E5_NOPSTNCONF", :guid=> "cd2925a3-**************-638a8c94f773", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 E5 without Audio Conferencing (500 seats min)_HUB", :string_id=> "Microsoft_365_E5_without_Audio_Conferencing", :guid=> "2113661c-6509-4034-98bb-9c47bd28d63c", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 F3 GCC", :string_id=> "M365_F1_GOV", :guid=> "2a914830-d700-444a-b73c-e3f31980d833", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 F5 Compliance Add-on", :string_id=> "SPE_F5_COMP", :guid=> "91de26be-adfa-4a3d-989e-9131cc23dda7", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 F5 Compliance Add-on AR (DOD)_USGOV_DOD", :string_id=> "SPE_F5_COMP_AR_D_USGOV_DOD", :guid=> "9cfd6bc3-84cd-4274-8a21-8c7c41d6c350", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 F5 Compliance Add-on AR_USGOV_GCCHIGH", :string_id=> "SPE_F5_COMP_AR_USGOV_GCCHIGH", :guid=> "9f436c0e-fb32-424b-90be-6a9f2919d506", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 F5 Compliance Add-on GCC", :string_id=> "SPE_F5_COMP_GCC", :guid=> "3f17cf90-67a2-4fdb-8587-37c1539507e1", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 F5 Security Add-on", :string_id=> "SPE_F5_SEC", :guid=> "67ffe999-d9ca-49e1-9d2c-03fb28aa7a48", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 F5 Security + Compliance Add-on", :string_id=> "SPE_F5_SECCOMP", :guid=> "32b47245-eb31-44fc-b945-a8b1576c439f", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 GCC G5", :string_id=> "M365_G5_GCC", :guid=> "e2be619b-b125-455f-8660-fb503e431a5d", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 E5 Suite features", :string_id=> "M365_E5_SUITE_COMPONENTS", :guid=> "99cc8282-2f74-4954-83b7-c6a9a1999067", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 F1", :string_id=> "M365_F1_COMM", :guid=> "50f60901-3181-4b75-8a2c-4c8e4c1d5a72", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 G3 GCC", :string_id=> "M365_G3_GOV", :guid=> "e823ca47-49c4-46b3-b38d-ca11d5abe3d2", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 Security and Compliance for Firstline Workers", :string_id=> "M365_SECURITY_COMPLIANCE_FOR_FLW", :guid=> "2347355b-4e81-41a4-9c22-55057a399791", :source=> "Microsoft" },
  { :product_name=> "Microsoft Business Center", :string_id=> "MICROSOFT_BUSINESS_CENTER", :guid=> "726a0894-2c77-4d65-99da-9775ef05aad1", :source=> "Microsoft" },
  { :product_name=> "Microsoft Cloud App Security", :string_id=> "ADALLOM_STANDALONE", :guid=> "df845ce7-05f9-4894-b5f2-11bbfbcfd2b6", :source=> "Microsoft" },
  { :product_name=> "Microsoft Defender for Endpoint P1", :string_id=> "DEFENDER_ENDPOINT_P1", :guid=> "16a55f2f-ff35-4cd5-9146-fb784e3761a5", :source=> "Microsoft" },
  { :product_name=> "Microsoft Defender for Endpoint P2_XPLAT", :string_id=> "MDATP_XPLAT", :guid=> "b126b073-72db-4a9d-87a4-b17afe41d4ab", :source=> "Microsoft" },
  { :product_name=> "Microsoft Defender for Endpoint Server", :string_id=> "MDATP_Server", :guid=> "509e8ab6-0274-4cda-bcbd-bd164fd562c4", :source=> "Microsoft" },
  { :product_name=> "Microsoft Defender for Identity", :string_id=> "ATA", :guid=> "98defdf7-f6c1-44f5-a1f6-943b6764e7a5", :source=> "Microsoft" },
  { :product_name=> "Microsoft Defender for Office 365 (Plan 1) Faculty", :string_id=> "ATP_ENTERPRISE_FACULTY", :guid=> "26ad4b5c-b686-462e-84b9-d7c22b46837f", :source=> "Microsoft" },
  { :product_name=> "Microsoft Defender for Office 365 (Plan 1) GCC", :string_id=> "ATP_ENTERPRISE_GOV", :guid=> "d0d1ca43-b81a-4f51-81e5-a5b1ad7bb005", :source=> "Microsoft" },
  { :product_name=> "Microsoft Defender for Office 365 (Plan 2) GCC", :string_id=> "THREAT_INTELLIGENCE_GOV", :guid=> "56a59ffb-9df1-421b-9e61-8b568583474d", :source=> "Microsoft" },
  { :product_name=> "Microsoft Defender Vulnerability Management Add-on", :string_id=> "TVM_Premium_Add_on", :guid=> "ad7a56e0-6903-4d13-94f3-5ad491e78960", :source=> "Microsoft" },
  { :product_name=> "Microsoft Intune Device", :string_id=> "INTUNE_A_D", :guid=> "2b317a4a-77a6-4188-9437-b68a77b4e2c6", :source=> "Microsoft" },
  { :product_name=> "Microsoft Intune Device for Government", :string_id=> "INTUNE_A_D_GOV", :guid=> "2c21e77a-e0d6-4570-b38a-7ff2dc17d2ca", :source=> "Microsoft" },
  { :product_name=> "Microsoft Power Apps for Developer", :string_id=> "POWERAPPS_DEV", :guid=> "5b631642-bd26-49fe-bd20-1daaa972ef80", :source=> "Microsoft" },
  { :product_name=> "Microsoft Power Apps Plan 2 Trial", :string_id=> "POWERAPPS_VIRAL", :guid=> "dcb1a3ae-b33f-4487-846a-a640262fadf4", :source=> "Microsoft" },
  { :product_name=> "Microsoft Power Automate Free", :string_id=> "FLOW_FREE", :guid=> "f30db892-07e9-47e9-837c-80727f46fd3d", :source=> "Microsoft" },
  { :product_name=> "Microsoft Power Automate Plan 2", :string_id=> "FLOW_P2", :guid=> "4755df59-3f73-41ab-a249-596ad72b5504", :source=> "Microsoft" },
  { :product_name=> "Microsoft Intune SMB", :string_id=> "INTUNE_SMB", :guid=> "e6025b08-2fa5-4313-bd0a-7e5ffca32958", :source=> "Microsoft" },
  { :product_name=> "Microsoft Intune Suite", :string_id=> "Microsoft_Intune_Suite", :guid=> "a929cd4d-8672-47c9-8664-159c1f322ba8", :source=> "Microsoft" },
  { :product_name=> "Microsoft Power Apps Plan 2 (Qualified Offer)", :string_id=> "POWERFLOW_P2", :guid=> "ddfae3e3-fcb2-4174-8ebd-3023cb213c8b", :source=> "Microsoft" },
  { :product_name=> "Microsoft Relationship Sales solution", :string_id=> "DYN365_ ENTERPRISE _RELATIONSHIP_SALES", :guid=> "4f05b1a3-a978-462c-b93f-781c6bee998f", :source=> "Microsoft" },
  { :product_name=> "Microsoft Stream", :string_id=> "STREAM", :guid=> "1f2f344a-700d-42c9-9427-5cea1d5d7ba6", :source=> "Microsoft" },
  { :product_name=> "Microsoft Stream Plan 2", :string_id=> "STREAM_P2", :guid=> "ec156933-b85b-4c50-84ec-c9e5603709ef", :source=> "Microsoft" },
  { :product_name=> "Microsoft Stream Storage Add-On (500 GB)", :string_id=> "STREAM_STORAGE", :guid=> "9bd7c846-9556-4453-a542-191d527209e8", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Audio Conferencing select dial-out", :string_id=> "Microsoft_Teams_Audio_Conferencing_select_dial_out", :guid=> "1c27243e-fb4d-42b1-ae8c-fe25c9616588", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams (Free)", :string_id=> "TEAMS_FREE", :guid=> "16ddbbfc-09ea-4de2-b1d7-312db6112d70", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Essentials", :string_id=> "Teams_Ess", :guid=> "fde42873-30b6-436b-b361-21af5a6b84ae", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Exploratory", :string_id=> "TEAMS_EXPLORATORY", :guid=> "710779e8-3d4a-4c88-adb9-386c958d1fdf", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Phone Standard for DOD", :string_id=> "MCOEV_DOD", :guid=> "d01d9287-694b-44f3-bcc5-ada78c8d953e", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Phone Standard for Faculty", :string_id=> "MCOEV_FACULTY", :guid=> "d979703c-028d-4de5-acbf-7955566b69b9", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Phone Standard for GCC", :string_id=> "MCOEV_GOV", :guid=> "a460366a-ade7-4791-b581-9fbff1bdaa85", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Phone Standard for GCCHIGH", :string_id=> "MCOEV_GCCHIGH", :guid=> "7035277a-5e49-4abc-a24f-0ec49c501bb5", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Phone Standard for Small and Medium Business", :string_id=> "MCOEVSMB_1", :guid=> "aa6791d3-bb09-4bc2-afed-c30c3fe26032", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Phone Standard for Students", :string_id=> "MCOEV_STUDENT", :guid=> "1f338bbc-767e-4a1e-a2d4-b73207cc5b93", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Phone Standard for TELSTRA", :string_id=> "MCOEV_TELSTRA", :guid=> "ffaf2d68-1c95-4eb3-9ddd-59b81fba0f61", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Phone Standard_USGOV_DOD", :string_id=> "MCOEV_USGOV_DOD", :guid=> "b0e7de67-e503-4934-b729-53d595ba5cd1", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Phone Standard_USGOV_GCCHIGH", :string_id=> "MCOEV_USGOV_GCCHIGH", :guid=> "985fcb26-7b94-475b-b512-89356697be71", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Phone Resource Account", :string_id=> "PHONESYSTEM_VIRTUALUSER", :guid=> "440eaaa8-b3e0-484b-a8be-62870b9ba70a", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Phone Resource Account for GCC", :string_id=> "PHONESYSTEM_VIRTUALUSER_GOV", :guid=> "2cf22bcb-0c9e-4bc6-8daf-7e7654c0f285", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Premium", :string_id=> "Microsoft_Teams_Premium", :guid=> "989a1621-93bc-4be0-835c-fe30171d6463", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Rooms Basic", :string_id=> "Microsoft_Teams_Rooms_Basic", :guid=> "6af4b3d6-14bb-4a2a-960c-6c902aad34f3", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Rooms Basic without Audio Conferencing", :string_id=> "Microsoft_Teams_Rooms_Basic_without_Audio_Conferencing", :guid=> "50509a35-f0bd-4c5e-89ac-22f0e16a00f8", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Rooms Pro", :string_id=> "Microsoft_Teams_Rooms_Pro", :guid=> "4cde982a-ede4-4409-9ae6-b003453c8ea6", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Rooms Pro without Audio Conferencing", :string_id=> "Microsoft_Teams_Rooms_Pro_without_Audio_Conferencing", :guid=> "21943e3a-2429-4f83-84c1-02735cd49e78", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Shared Devices", :string_id=> "MCOCAP", :guid=> "295a8eb0-f78d-45c7-8b5b-1eed5ed02dff", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Shared Devices for GCC", :string_id=> "MCOCAP_GOV", :guid=> "b1511558-69bd-4e1b-8270-59ca96dba0f3", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Rooms Standard", :string_id=> "MEETING_ROOM", :guid=> "6070a4c8-34c6-4937-8dfb-39bbc6397a60", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Rooms Standard without Audio Conferencing", :string_id=> "MEETING_ROOM_NOAUDIOCONF", :guid=> "61bec411-e46a-4dab-8f46-8b58ec845ffe", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Trial", :string_id=> "MS_TEAMS_IW", :guid=> "74fbf1bb-47c6-4796-9623-77dc7371723b", :source=> "Microsoft" },
  { :product_name=> "Microsoft Threat Experts - Experts on Demand", :string_id=> "EXPERTS_ON_DEMAND", :guid=> "9fa2f157-c8e4-4351-a3f2-ffa506da1406", :source=> "Microsoft" },
  { :product_name=> "Microsoft Viva Suite", :string_id=> "VIVA", :guid=> "61902246-d7cb-453e-85cd-53ee28eec138", :source=> "Microsoft" },
  { :product_name=> "Multi-Geo Capabilities in Office 365", :string_id=> "OFFICE365_MULTIGEO", :guid=> "84951599-62b7-46f3-9c9d-30551b2ad607", :source=> "Microsoft" },
  { :product_name=> "Nonprofit Portal", :string_id=> "NONPROFIT_PORTAL", :guid=> "aa2695c9-8d59-4800-9dc8-12e01f1735af", :source=> "Microsoft" },
  { :product_name=> "Office 365 A1 for faculty", :string_id=> "STANDARDWOFFPACK_FACULTY", :guid=> "94763226-9b3c-4e75-a931-5c89701abe66", :source=> "Microsoft" },
  { :product_name=> "Office 365 A1 Plus for faculty", :string_id=> "STANDARDWOFFPACK_IW_FACULTY", :guid=> "78e66a63-337a-4a9a-8959-41c6654dfb56", :source=> "Microsoft" },
  { :product_name=> "Office 365 A1 for students", :string_id=> "STANDARDWOFFPACK_STUDENT", :guid=> "314c4481-f395-4525-be8b-2ec4bb1e9d91", :source=> "Microsoft" },
  { :product_name=> "Office 365 A1 Plus for students", :string_id=> "STANDARDWOFFPACK_IW_STUDENT", :guid=> "e82ae690-a2d5-4d76-8d30-7c6e01e6022e", :source=> "Microsoft" },
  { :product_name=> "Office 365 A3 for faculty", :string_id=> "ENTERPRISEPACKPLUS_FACULTY", :guid=> "e578b273-6db4-4691-bba0-8d691f4da603", :source=> "Microsoft" },
  { :product_name=> "Office 365 A3 for students", :string_id=> "ENTERPRISEPACKPLUS_STUDENT", :guid=> "98b6e773-24d4-4c0d-a968-6e787a1f8204", :source=> "Microsoft" },
  { :product_name=> "Office 365 Advanced Compliance for GCC", :string_id=> "EQUIVIO_ANALYTICS_GOV", :guid=> "1a585bba-1ce3-416e-b1d6-9c482b52fcf6", :source=> "Microsoft" },
  { :product_name=> "Office 365 Extra File Storage for GCC", :string_id=> "SHAREPOINTSTORAGE_GOV", :guid=> "e5788282-6381-469f-84f0-3d7d4021d34d", :source=> "Microsoft" },
  { :product_name=> "Microsoft Teams Commercial Cloud", :string_id=> "TEAMS_COMMERCIAL_TRIAL", :guid=> "29a2f828-8f39-4837-b8ff-c957e86abe3c", :source=> "Microsoft" },
  { :product_name=> "Office 365 Cloud App Security", :string_id=> "ADALLOM_O365", :guid=> "84d5f90f-cd0d-4864-b90b-1c7ba63b4808", :source=> "Microsoft" },
  { :product_name=> "Office 365 Extra File Storage", :string_id=> "SHAREPOINTSTORAGE", :guid=> "99049c9c-6011-4908-bf17-15f496e6519d", :source=> "Microsoft" },
  { :product_name=> "Office 365 G1 GCC", :string_id=> "STANDARDPACK_GOV", :guid=> "3f4babde-90ec-47c6-995d-d223749065d1", :source=> "Microsoft" },
  { :product_name=> "Office 365 G3 GCC", :string_id=> "ENTERPRISEPACK_GOV", :guid=> "535a3a29-c5f0-42fe-8215-d3b9e1f38c4a", :source=> "Microsoft" },
  { :product_name=> "Office 365 G5 GCC", :string_id=> "ENTERPRISEPREMIUM_GOV", :guid=> "8900a2c0-edba-4079-bdf3-b276e293b6a8", :source=> "Microsoft" },
  { :product_name=> "Power Apps and Logic Flows", :string_id=> "POWERAPPS_INDIVIDUAL_USER", :guid=> "87bbbc60-4754-4998-8c88-227dca264858", :source=> "Microsoft" },
  { :product_name=> "PowerApps per app baseline access", :string_id=> "POWERAPPS_PER_APP_IW", :guid=> "bf666882-9c9b-4b2e-aa2f-4789b0a52ba2", :source=> "Microsoft" },
  { :product_name=> "Power Apps per app plan", :string_id=> "POWERAPPS_PER_APP", :guid=> "a8ad7d2b-b8cf-49d6-b25a-69094a0be206", :source=> "Microsoft" },
  { :product_name=> "Power Apps per app plan (1 app or portal)", :string_id=> "POWERAPPS_PER_APP_NEW", :guid=> "b4d7b828-e8dc-4518-91f9-e123ae48440d", :source=> "Microsoft" },
  { :product_name=> "Power Apps per user plan for Government", :string_id=> "POWERAPPS_PER_USER_GCC", :guid=> "8e4c6baa-f2ff-4884-9c38-93785d0d7ba1", :source=> "Microsoft" },
  { :product_name=> "PowerApps Plan 1 for Government", :string_id=> "POWERAPPS_P1_GOV", :guid=> "eca22b68-b31f-4e9c-a20c-4d40287bc5dd", :source=> "Microsoft" },
  { :product_name=> "Power Apps Portals login capacity add-on Tier 2 (10 unit min)", :string_id=> "POWERAPPS_PORTALS_LOGIN_T2", :guid=> "57f3babd-73ce-40de-bcb2-dadbfbfff9f7", :source=> "Microsoft" },
  { :product_name=> "Power Apps Portals login capacity add-on Tier 2 (10 unit min) for Government", :string_id=> "POWERAPPS_PORTALS_LOGIN_T2_GCC", :guid=> "26c903d5-d385-4cb1-b650-8d81a643b3c4", :source=> "Microsoft" },
  { :product_name=> "Power Apps Portals login capacity add-on Tier 3 (50 unit min)", :string_id=> "POWERAPPS_PORTALS_LOGIN_T3", :guid=> "927d8402-8d3b-40e8-b779-34e859f7b497", :source=> "Microsoft" },
  { :product_name=> "Power Apps Portals page view capacity add-on", :string_id=> "POWERAPPS_PORTALS_PAGEVIEW", :guid=> "a0de5e3a-2500-4a19-b8f4-ec1c64692d22", :source=> "Microsoft" },
  { :product_name=> "Power Apps Portals page view capacity add-on for Government", :string_id=> "POWERAPPS_PORTALS_PAGEVIEW_GCC", :guid=> "15a64d3e-5b99-4c4b-ae8f-aa6da264bfe7", :source=> "Microsoft" },
  { :product_name=> "Power Automate per flow plan", :string_id=> "FLOW_BUSINESS_PROCESS", :guid=> "b3a42176-0a8c-4c3f-ba4e-f2b37fe5be6b", :source=> "Microsoft" },
  { :product_name=> "Power Automate per user plan", :string_id=> "FLOW_PER_USER", :guid=> "4a51bf65-409c-4a91-b845-1121b571cc9d", :source=> "Microsoft" },
  { :product_name=> "Power Automate per user plan dept", :string_id=> "FLOW_PER_USER_DEPT", :guid=> "d80a4c5d-8f05-4b64-9926-6574b9e6aee4", :source=> "Microsoft" },
  { :product_name=> "Power Automate per user plan for Government", :string_id=> "FLOW_PER_USER_GCC", :guid=> "c8803586-c136-479a-8ff3-f5f32d23a68e", :source=> "Microsoft" },
  { :product_name=> "Power Automate per user with attended RPA plan", :string_id=> "POWERAUTOMATE_ATTENDED_RPA", :guid=> "eda1941c-3c4f-4995-b5eb-e85a42175ab9", :source=> "Microsoft" },
  { :product_name=> "Power Automate unattended RPA add-on", :string_id=> "POWERAUTOMATE_UNATTENDED_RPA", :guid=> "3539d28c-6e35-4a30-b3a9-cd43d5d3e0e2", :source=> "Microsoft" },
  { :product_name=> "Power BI", :string_id=> "POWER_BI_INDIVIDUAL_USER", :guid=> "e2767865-c3c9-4f09-9f99-6eee6eef861a", :source=> "Microsoft" },
  { :product_name=> "Power BI (free)", :string_id=> "POWER_BI_STANDARD", :guid=> "a403ebcc-fae0-4ca2-8c8c-7a907fd6c235", :source=> "Microsoft" },
  { :product_name=> "Power BI Premium P1", :string_id=> "PBI_PREMIUM_P1_ADDON", :guid=> "7b26f5ab-a763-4c00-a1ac-f6c4b5506945", :source=> "Microsoft" },
  { :product_name=> "Power BI Premium Per User", :string_id=> "PBI_PREMIUM_PER_USER", :guid=> "c1d032e0-5619-4761-9b5c-75b6831e1711", :source=> "Microsoft" },
  { :product_name=> "Power BI Premium Per User Add-On", :string_id=> "PBI_PREMIUM_PER_USER_ADDON", :guid=> "de376a03-6e5b-42ec-855f-093fb50b8ca5", :source=> "Microsoft" },
  { :product_name=> "Power BI Premium Per User Dept", :string_id=> "PBI_PREMIUM_PER_USER_DEPT", :guid=> "f168a3fb-7bcf-4a27-98c3-c235ea4b78b4", :source=> "Microsoft" },
  { :product_name=> "Power BI Pro CE", :string_id=> "POWER_BI_PRO_CE", :guid=> "420af87e-8177-4146-a780-3786adaffbca", :source=> "Microsoft" },
  { :product_name=> "Power BI Pro Dept", :string_id=> "POWER_BI_PRO_DEPT", :guid=> "3a6a908c-09c5-406a-8170-8ebb63c42882", :source=> "Microsoft" },
  { :product_name=> "Power BI Pro for Faculty", :string_id=> "POWER_BI_PRO_FACULTY", :guid=> "de5f128b-46d7-4cfc-b915-a89ba060ea56", :source=> "Microsoft" },
  { :product_name=> "Power BI Pro for GCC", :string_id=> "POWERBI_PRO_GOV", :guid=> "f0612879-44ea-47fb-baf0-3d76d9235576", :source=> "Microsoft" },
  { :product_name=> "Power Pages vTrial for Makers", :string_id=> "Power_Pages_vTrial_for_Makers", :guid=> "3f9f06f5-3c31-472c-985f-62d9c10ec167", :source=> "Microsoft" },
  { :product_name=> "Power Virtual Agent", :string_id=> "VIRTUAL_AGENT_BASE", :guid=> "e4e55366-9635-46f4-a907-fc8c3b5ec81f", :source=> "Microsoft" },
  { :product_name=> "Power Virtual Agent User License", :string_id=> "VIRTUAL_AGENT_USL", :guid=> "4b74a65c-8b4a-4fc8-9f6b-5177ed11ddfa", :source=> "Microsoft" },
  { :product_name=> "Power Virtual Agents Viral Trial", :string_id=> "CCIBOTS_PRIVPREV_VIRAL", :guid=> "606b54a9-78d8-4298-ad8b-df6ef4481c80", :source=> "Microsoft" },
  { :product_name=> "Privacy Management � risk", :string_id=> "PRIVACY_MANAGEMENT_RISK", :guid=> "************************************", :source=> "Microsoft" },
  { :product_name=> "Privacy Management - risk for EDU", :string_id=> "PRIVACY_MANAGEMENT_RISK_EDU", :guid=> "************************************", :source=> "Microsoft" },
  { :product_name=> "Privacy Management - risk GCC", :string_id=> "PRIVACY_MANAGEMENT_RISK_GCC", :guid=> "************************************", :source=> "Microsoft" },
  { :product_name=> "Privacy Management - risk_USGOV_DOD", :string_id=> "PRIVACY_MANAGEMENT_RISK_USGOV_DOD", :guid=> "************************************", :source=> "Microsoft" },
  { :product_name=> "Privacy Management - risk_USGOV_GCCHIGH", :string_id=> "PRIVACY_MANAGEMENT_RISK_USGOV_GCCHIGH", :guid=> "787d7e75-29ca-4b90-a3a9-0b780b35367c", :source=> "Microsoft" },
  { :product_name=> "Privacy Management - subject rights request (1)", :string_id=> "PRIVACY_MANAGEMENT_SUB_RIGHTS_REQ_1_V2", :guid=> "d9020d1c-94ef-495a-b6de-818cbbcaa3b8", :source=> "Microsoft" },
  { :product_name=> "Privacy Management - subject rights request (1) for EDU", :string_id=> "PRIVACY_MANAGEMENT_SUB_RIGHTS_REQ_1_EDU_V2", :guid=> "475e3e81-3c75-4e07-95b6-2fed374536c8", :source=> "Microsoft" },
  { :product_name=> "Privacy Management - subject rights request (1) GCC", :string_id=> "PRIVACY_MANAGEMENT_SUB_RIGHTS_REQ_1_V2_GCC", :guid=> "017fb6f8-00dd-4025-be2b-4eff067cae72", :source=> "Microsoft" },
  { :product_name=> "Privacy Management - subject rights request (1) USGOV_DOD", :string_id=> "PRIVACY_MANAGEMENT_SUB_RIGHTS_REQ_1_V2_USGOV_DOD", :guid=> "d3c841f3-ea93-4da2-8040-6f2348d20954", :source=> "Microsoft" },
  { :product_name=> "Privacy Management - subject rights request (1) USGOV_GCCHIGH", :string_id=> "PRIVACY_MANAGEMENT_SUB_RIGHTS_REQ_1_V2_USGOV_GCCHIGH", :guid=> "706d2425-6170-4818-ba08-2ad8f1d2d078", :source=> "Microsoft" },
  { :product_name=> "Privacy Management - subject rights request (10)", :string_id=> "PRIVACY_MANAGEMENT_SUB_RIGHTS_REQ_10_V2", :guid=> "78ea43ac-9e5d-474f-8537-4abb82dafe27", :source=> "Microsoft" },
  { :product_name=> "Privacy Management - subject rights request (10) for EDU", :string_id=> "PRIVACY_MANAGEMENT_SUB_RIGHTS_REQ_10_EDU_V2", :guid=> "e001d9f1-5047-4ebf-8927-148530491f83", :source=> "Microsoft" },
  { :product_name=> "Privacy Management - subject rights request (10) GCC", :string_id=> "PRIVACY_MANAGEMENT_SUB_RIGHTS_REQ_10_V2_GCC", :guid=> "a056b037-1fa0-4133-a583-d05cff47d551", :source=> "Microsoft" },
  { :product_name=> "Privacy Management - subject rights request (10) USGOV_DOD", :string_id=> "PRIVACY_MANAGEMENT_SUB_RIGHTS_REQ_10_V2_USGOV_DOD", :guid=> "ab28dfa1-853a-4f54-9315-f5146975ac9a", :source=> "Microsoft" },
  { :product_name=> "Privacy Management - subject rights request (10) USGOV_GCCHIGH", :string_id=> "PRIVACY_MANAGEMENT_SUB_RIGHTS_REQ_10_V2_USGOV_GCCHIGH", :guid=> "f6aa3b3d-62f4-4c1d-a44f-0550f40f729c", :source=> "Microsoft" },
  { :product_name=> "Privacy Management - subject rights request (50)", :string_id=> "PRIVACY_MANAGEMENT_SUB_RIGHTS_REQ_50", :guid=> "c416b349-a83c-48cb-9529-c420841dedd6", :source=> "Microsoft" },
  { :product_name=> "Privacy Management - subject rights request (50)", :string_id=> "PRIVACY_MANAGEMENT_SUB_RIGHTS_REQ_50_V2", :guid=> "f6c82f13-9554-4da1-bed3-c024cc906e02", :source=> "Microsoft" },
  { :product_name=> "Privacy Management - subject rights request (50) for EDU", :string_id=> "PRIVACY_MANAGEMENT_SUB_RIGHTS_REQ_50_EDU_V2", :guid=> "ed45d397-7d61-4110-acc0-95674917bb14", :source=> "Microsoft" },
  { :product_name=> "Privacy Management - subject rights request (100)", :string_id=> "PRIVACY_MANAGEMENT_SUB_RIGHTS_REQ_100_V2", :guid=> "cf4c6c3b-f863-4940-97e8-1d25e912f4c4", :source=> "Microsoft" },
  { :product_name=> "Privacy Management - subject rights request (100) for EDU", :string_id=> "PRIVACY_MANAGEMENT_SUB_RIGHTS_REQ_100_EDU_V2", :guid=> "9b85b4f0-92d9-4c3d-b230-041520cb1046", :source=> "Microsoft" },
  { :product_name=> "Privacy Management - subject rights request (100) GCC", :string_id=> "PRIVACY_MANAGEMENT_SUB_RIGHTS_REQ_100_V2_GCC", :guid=> "91bbc479-4c2c-4210-9c88-e5b468c35b83", :source=> "Microsoft" },
  { :product_name=> "Privacy Management - subject rights request (100) USGOV_DOD", :string_id=> "PRIVACY_MANAGEMENT_SUB_RIGHTS_REQ_100_V2_USGOV_DOD", :guid=> "ba6e69d5-ba2e-47a7-b081-66c1b8e7e7d4", :source=> "Microsoft" },
  { :product_name=> "Privacy Management - subject rights request (100) USGOV_GCCHIGH", :string_id=> "PRIVACY_MANAGEMENT_SUB_RIGHTS_REQ_100_V2_USGOV_GCCHIGH", :guid=> "cee36ce4-cc31-481f-8cab-02765d3e441f", :source=> "Microsoft" },
  { :product_name=> "Project Online Essentials for Faculty", :string_id=> "PROJECTESSENTIALS_FACULTY", :guid=> "e433b246-63e7-4d0b-9efa-7940fa3264d6", :source=> "Microsoft" },
  { :product_name=> "Project Online Essentials for GCC", :string_id=> "PROJECTESSENTIALS_GOV", :guid=> "ca1a159a-f09e-42b8-bb82-cb6420f54c8e", :source=> "Microsoft" },
  { :product_name=> "Project Plan 1", :string_id=> "PROJECT_P1", :guid=> "beb6439c-caad-48d3-bf46-0c82871e12be", :source=> "Microsoft" },
  { :product_name=> "Project Plan 1 (for Department)", :string_id=> "PROJECT_PLAN1_DEPT", :guid=> "84cd610f-a3f8-4beb-84ab-d9d2c902c6c9", :source=> "Microsoft" },
  { :product_name=> "Project Plan 3 (for Department)", :string_id=> "PROJECT_PLAN3_DEPT", :guid=> "46102f44-d912-47e7-b0ca-1bd7b70ada3b", :source=> "Microsoft" },
  { :product_name=> "Project Plan 3 for Faculty", :string_id=> "PROJECTPROFESSIONAL_FACULTY", :guid=> "46974aed-363e-423c-9e6a-951037cec495", :source=> "Microsoft" },
  { :product_name=> "Project Plan 3 for GCC", :string_id=> "PROJECTPROFESSIONAL_GOV", :guid=> "074c6829-b3a0-430a-ba3d-aca365e57065", :source=> "Microsoft" },
  { :product_name=> "Project Plan 5 for GCC", :string_id=> "PROJECTPREMIUM_GOV", :guid=> "f2230877-72be-4fec-b1ba-7156d6f75bd6", :source=> "Microsoft" },
  { :product_name=> "Rights Management Service Basic Content Protection", :string_id=> "RMSBASIC", :guid=> "093e8d14-a334-43d9-93e3-30589a8b47d0", :source=> "Microsoft" },
  { :product_name=> "Sensor Data Intelligence Additional Machines Add-in for Dynamics 365 Supply Chain Management", :string_id=> "DYN365_IOT_INTELLIGENCE_ADDL_MACHINES", :guid=> "08e18479-4483-4f70-8f17-6f92156d8ea9", :source=> "Microsoft" },
  { :product_name=> "Sensor Data Intelligence Scenario Add-in for Dynamics 365 Supply Chain Management", :string_id=> "DYN365_IOT_INTELLIGENCE_SCENARIO", :guid=> "9ea4bdef-a20b-4668-b4a7-73e1f7696e0a", :source=> "Microsoft" },
  { :product_name=> "SharePoint Syntex", :string_id=> "Intelligent_Content_Services", :guid=> "f61d4aba-134f-44e9-a2a0-f81a5adb26e4", :source=> "Microsoft" },
  { :product_name=> "Skype for Business PSTN Usage Calling Plan", :string_id=> "MCOPSTNPP", :guid=> "06b48c5f-01d9-4b18-9015-03b52040f51a", :source=> "Microsoft" },
  { :product_name=> "Teams Phone with Calling Plan", :string_id=> "MCOTEAMS_ESSENTIALS", :guid=> "ae2343d1-0999-43f6-ae18-d816516f6e78", :source=> "Microsoft" },
  { :product_name=> "Teams Rooms Premium", :string_id=> "MTR_PREM", :guid=> "4fb214cb-a430-4a91-9c91-4976763aa78f", :source=> "Microsoft" },
  { :product_name=> "TELSTRA Calling for O365", :string_id=> "MCOPSTNEAU2", :guid=> "de3312e1-c7b0-46e6-a7c3-a515ff90bc86", :source=> "Microsoft" },
  { :product_name=> "Universal Print", :string_id=> "UNIVERSAL_PRINT", :guid=> "9f3d9c1d-25a5-4aaa-8e59-23a1e6450a67", :source=> "Microsoft" },
  { :product_name=> "Visio Plan 1", :string_id=> "VISIO_PLAN1_DEPT", :guid=> "ca7f3140-d88c-455b-9a1c-7f0679e31a76", :source=> "Microsoft" },
  { :product_name=> "Visio Plan 2", :string_id=> "VISIO_PLAN2_DEPT", :guid=> "38b434d2-a15e-4cde-9a98-e737c75623e1", :source=> "Microsoft" },
  { :product_name=> "Visio Plan 2 for GCC", :string_id=> "VISIOCLIENT_GOV", :guid=> "4ae99959-6b0f-43b0-b1ce-68146001bdba", :source=> "Microsoft" },
  { :product_name=> "Viva Topics", :string_id=> "TOPIC_EXPERIENCES", :guid=> "4016f256-b063-4864-816e-d818aad600c9", :source=> "Microsoft" },
  { :product_name=> "Windows 10/11 Enterprise E5 (Original)", :string_id=> "WIN_ENT_E5", :guid=> "1e7e1070-8ccb-4aca-b470-d7cb538cb07e", :source=> "Microsoft" },
  { :product_name=> "Windows 10 Enterprise A3 for faculty", :string_id=> "WIN10_ENT_A3_FAC", :guid=> "8efbe2f6-106e-442f-97d4-a59aa6037e06", :source=> "Microsoft" },
  { :product_name=> "Windows 10 Enterprise A3 for students", :string_id=> "WIN10_ENT_A3_STU", :guid=> "d4ef921e-840b-4b48-9a90-ab6698bc7b31", :source=> "Microsoft" },
  { :product_name=> "Windows 10 Enterprise E3", :string_id=> "WIN10_VDA_E3", :guid=> "6a0f6da5-0b87-4190-a6ae-9bb5a2b9546a", :source=> "Microsoft" },
  { :product_name=> "Windows 10 Enterprise E5 Commercial (GCC Compatible)", :string_id=> "WINE5_GCC_COMPAT", :guid=> "938fd547-d794-42a4-996c-1cc206619580", :source=> "Microsoft" },
  { :product_name=> "Windows 10/11 Enterprise E3 VDA", :string_id=> "E3_VDA_only", :guid=> "d13ef257-988a-46f3-8fce-f47484dd4550", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Business 1 vCPU 2 GB 64 GB", :string_id=> "CPC_B_1C_2RAM_64GB", :guid=> "816eacd3-e1e3-46b3-83c8-1ffd37e053d9", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Business 2 vCPU 4 GB 128 GB", :string_id=> "CPC_B_2C_4RAM_128GB", :guid=> "135bee78-485b-4181-ad6e-40286e311850", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Business 2 vCPU 4 GB 256 GB", :string_id=> "CPC_B_2C_4RAM_256GB", :guid=> "805d57c3-a97d-4c12-a1d0-858ffe5015d0", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Business 2 vCPU 4 GB 64 GB", :string_id=> "CPC_B_2C_4RAM_64GB", :guid=> "42e6818f-8966-444b-b7ac-0027c83fa8b5", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Business 2 vCPU 8 GB 128 GB", :string_id=> "CPC_B_2C_8RAM_128GB", :guid=> "71f21848-f89b-4aaa-a2dc-780c8e8aac5b", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Business 2 vCPU 8 GB 256 GB", :string_id=> "CPC_B_2C_8RAM_256GB", :guid=> "750d9542-a2f8-41c7-8c81-************", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Business 4 vCPU 16 GB 128 GB", :string_id=> "CPC_B_4C_16RAM_128GB", :guid=> "ad83ac17-4a5a-4ebb-adb2-079fb277e8b9", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Business 4 vCPU 16 GB 128 GB (with Windows Hybrid Benefit)", :string_id=> "CPC_B_4C_16RAM_128GB_WHB", :guid=> "439ac253-bfbc-49c7-acc0-6b951407b5ef", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Business 4 vCPU 16 GB 256 GB", :string_id=> "CPC_B_4C_16RAM_256GB", :guid=> "b3891a9f-c7d9-463c-a2ec-0b2321bda6f9", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Business 4 vCPU 16 GB 512 GB", :string_id=> "CPC_B_4C_16RAM_512GB", :guid=> "1b3043ad-dfc6-427e-a2c0-5ca7a6c94a2b", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Business 8 vCPU 32 GB 128 GB", :string_id=> "CPC_B_8C_32RAM_128GB", :guid=> "3cb45fab-ae53-4ff6-af40-24c1915ca07b", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Business 8 vCPU 32 GB 256 GB", :string_id=> "CPC_B_8C_32RAM_256GB", :guid=> "fbc79df2-da01-4c17-8d88-17f8c9493d8f", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Business 8 vCPU 32 GB 512 GB", :string_id=> "CPC_B_8C_32RAM_512GB", :guid=> "8ee402cd-e6a8-4b67-a411-54d1f37a2049", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Enterprise 1 vCPU 2 GB 64 GB", :string_id=> "CPC_E_1C_2GB_64GB", :guid=> "0c278af4-c9c1-45de-9f4b-cd929e747a2c", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Enterprise 2 vCPU 4 GB 64 GB", :string_id=> "CPC_E_2C_4GB_64GB", :guid=> "7bb14422-3b90-4389-a7be-f1b745fc037f", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Enterprise 2 vCPU 4 GB 128 GB", :string_id=> "CPC_E_2C_4GB_128GB", :guid=> "226ca751-f0a4-4232-9be5-73c02a92555e", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Enterprise 2 vCPU 4 GB 128 GB (Preview)", :string_id=> "CPC_LVL_1", :guid=> "bce09f38-1800-4a51-8d50-5486380ba84a", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Enterprise 2 vCPU 4 GB 256 GB", :string_id=> "CPC_E_2C_4GB_256GB", :guid=> "5265a84e-8def-4fa2-ab4b-5dc278df5025", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Enterprise 2 vCPU 8 GB 128 GB", :string_id=> "CPC_E_2C_8GB_128GB", :guid=> "e2aebe6c-897d-480f-9d62-fff1381581f7", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Enterprise 2 vCPU 8 GB 128 GB (Preview)", :string_id=> "CPC_LVL_2", :guid=> "461cb62c-6db7-41aa-bf3c-ce78236cdb9e", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Enterprise 2 vCPU 8 GB 256 GB", :string_id=> "CPC_E_2C_8GB_256GB", :guid=> "1c79494f-e170-431f-a409-428f6053fa35", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Enterprise 4 vCPU 16 GB 128 GB", :string_id=> "CPC_E_4C_16GB_128GB", :guid=> "d201f153-d3b2-4057-be2f-fe25c8983e6f", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Enterprise 4 vCPU 16 GB 256 GB", :string_id=> "CPC_E_4C_16GB_256GB", :guid=> "96d2951e-cb42-4481-9d6d-cad3baac177e", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Enterprise 4 vCPU 16 GB 256 GB (Preview)", :string_id=> "CPC_LVL_3", :guid=> "bbb4bf6e-3e12-4343-84a1-54d160c00f40", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Enterprise 4 vCPU 16 GB 512 GB", :string_id=> "CPC_E_4C_16GB_512GB", :guid=> "0da63026-e422-4390-89e8-b14520d7e699", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Enterprise 8 vCPU 32 GB 128 GB", :string_id=> "CPC_E_8C_32GB_128GB", :guid=> "c97d00e4-0c4c-4ec2-a016-9448c65de986", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Enterprise 8 vCPU 32 GB 256 GB", :string_id=> "CPC_E_8C_32GB_256GB", :guid=> "7818ca3e-73c8-4e49-bc34-1276a2d27918", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Enterprise 8 vCPU 32 GB 512 GB", :string_id=> "CPC_E_8C_32GB_512GB", :guid=> "9fb0ba5f-4825-4e84-b239-5167a3a5d4dc", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Shared Use 2 vCPU 4 GB 64 GB", :string_id=> "Windows_365_S_2vCPU_4GB_64GB", :guid=> "1f9990ca-45d9-4c8d-8d04-a79241924ce1", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Shared Use 2 vCPU 4 GB 128 GB", :string_id=> "Windows_365_S_2vCPU_4GB_128GB", :guid=> "*************-4e75-8f5e-d13f4b6092c1", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Shared Use 2 vCPU 4 GB 256 GB", :string_id=> "Windows_365_S_2vCPU_4GB_256GB", :guid=> "8fe96593-34d3-49bb-aeee-fb794fed0800", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Shared Use 2 vCPU 8 GB 128 GB", :string_id=> "Windows_365_S_2vCPU_8GB_128GB", :guid=> "2d21fc84-b918-491e-ad84-e24d61ccec94", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Shared Use 2 vCPU 8 GB 256 GB", :string_id=> "Windows_365_S_2vCPU_8GB_256GB", :guid=> "2eaa4058-403e-4434-9da9-ea693f5d96dc", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Shared Use 4 vCPU 16 GB 128 GB", :string_id=> "Windows_365_S_4vCPU_16GB_128GB", :guid=> "1bf40e76-4065-4530-ac37-f1513f362f50", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Shared Use 4 vCPU 16 GB 256 GB", :string_id=> "Windows_365_S_4vCPU_16GB_256GB", :guid=> "a9d1e0df-df6f-48df-9386-76a832119cca", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Shared Use 4 vCPU 16 GB 512 GB", :string_id=> "Windows_365_S_4vCPU_16GB_512GB", :guid=> "469af4da-121c-4529-8c85-9467bbebaa4b", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Shared Use 8 vCPU 32 GB 128 GB", :string_id=> "Windows_365_S_8vCPU_32GB_128GB", :guid=> "f319c63a-61a9-42b7-b786-5695bc7edbaf", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Shared Use 8 vCPU 32 GB 256 GB", :string_id=> "Windows_365_S_8vCPU_32GB_256GB", :guid=> "fb019e88-26a0-4218-bd61-7767d109ac26", :source=> "Microsoft" },
  { :product_name=> "Windows 365 Shared Use 8 vCPU 32 GB 512 GB", :string_id=> "Windows_365_S_8vCPU_32GB_512GB", :guid=> "f4dc1de8-8c94-4d37-af8a-1fca6675590a", :source=> "Microsoft" },
  { :product_name=> "Windows Store for Business", :string_id=> "WINDOWS_STORE", :guid=> "6470687e-a428-4b7a-bef2-8a291ad947c9", :source=> "Microsoft" },
  { :product_name=> "Windows Store for Business EDU Faculty", :string_id=> "WSFB_EDU_FACULTY", :guid=> "c7e9d9e6-1981-4bf3-bb50-a5bdfaa06fb2", :source=> "Microsoft" },
  { :product_name=> "Microsoft Workplace Analytics", :string_id=> "WORKPLACE_ANALYTICS", :guid=> "3d957427-ecdc-4df2-aacd-01cc9d519da8", :source=> "Microsoft" },
  { :product_name=> "Microsoft 365 Business Voice (US)", :string_id=> "BUSINESS_VOICE_MED2_TELCO", :guid=> "08d7bce8-6e16-490e-89db-1d508e5e9609", :source=> "Microsoft" }
]
default_vendor = DefaultVendor.find_by(name: "Microsoft")

prod_informations.each do |attr|
  prod_info = Integrations::ProductInformation.new(attr)
  prod_info.save!
  product_name = attr[:product_name].titleize
  product = default_vendor.default_products.find_or_create_by(name: product_name)
  product.logo_url = attr[:logo_url]
  product.updated_at = Time.now
  product.save!
end
Integrations::ProductInformation.where('updated_at < ?', start_time).destroy_all
