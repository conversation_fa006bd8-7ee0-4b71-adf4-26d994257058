hardware = AssetType.find_or_create_by(name: "Hardware")
hardware.has_child = true
hardware.save!

computer = AssetType.find_or_create_by(name: "Computer")
computer.parent_type_id = hardware.id
computer.has_child = true
computer.save!

network = AssetType.find_or_create_by(name: "Network")
network.parent_type_id = hardware.id
network.has_child = true
network.save!

router = AssetType.find_or_create_by(name: "Router")
router.parent_type_id = network.id
router.has_child = false
router.save!

switch = AssetType.find_or_create_by(name: "Switch")
switch.parent_type_id = network.id
switch.has_child = false
switch.save!

firewall = AssetType.find_or_create_by(name: "Firewall")
firewall.parent_type_id = network.id
firewall.has_child = false
firewall.save!

mobile_devices = AssetType.find_or_create_by(name: "Mobile Devices")
mobile_devices.parent_type_id = hardware.id
mobile_devices.has_child = true
mobile_devices.save!

mobile = AssetType.find_or_create_by(name: "Mobile")
mobile.parent_type_id = mobile_devices.id
mobile.has_child = false
mobile.save!

tablet = AssetType.find_or_create_by(name: "Tablet")
tablet.parent_type_id = mobile_devices.id
tablet.has_child = false
tablet.save!

printer = AssetType.find_or_create_by(name: "Printer")
printer.parent_type_id = hardware.id
printer.has_child = false
printer.save!

laptop = AssetType.find_or_create_by(name: "Laptop")
laptop.parent_type_id = computer.id
laptop.has_child = false
laptop.save!

desktop = AssetType.find_or_create_by(name: "Desktop")
desktop.parent_type_id = computer.id
desktop.has_child = false
desktop.save!

vmachine = AssetType.find_or_create_by(name: "Virtual Machine")
vmachine.parent_type_id = computer.id
vmachine.has_child = false
vmachine.save!

server = AssetType.find_or_create_by(name: "Server")
server.parent_type_id = computer.id
server.has_child = false
server.save!

phone = AssetType.find_or_create_by(name: "Phone")
phone.parent_type_id = hardware.id
phone.has_child = false
phone.nested_attributes = true
phone.save!

phone_system = AssetType.find_or_create_by(name: "Phone System")
phone_system.parent_type_id = hardware.id
phone_system.has_child = false
phone_system.nested_attributes = true
phone_system.save!

wap = AssetType.find_or_create_by(name: "WAP")
wap.parent_type_id = network.id
wap.has_child = false
wap.save!

thin_client = AssetType.find_or_create_by(name: "Thin Client")
thin_client.parent_type_id = computer.id
thin_client.has_child = false
thin_client.save!

windows_device = AssetType.find_or_create_by(name: "Windows")
windows_device.parent_type_id = computer.id
windows_device.has_child = false
windows_device.save!

apple_device = AssetType.find_or_create_by(name: "Apple Device")
apple_device.parent_type_id = computer.id
apple_device.has_child = false
apple_device.save!

iphone_device = AssetType.find_or_create_by(name: "iPhone")
iphone_device.parent_type_id = mobile_devices.id
iphone_device.has_child = false
iphone_device.save!

ipad_device = AssetType.find_or_create_by(name: "iPad")
ipad_device.parent_type_id = mobile_devices.id
ipad_device.has_child = false
ipad_device.save!

tv_device = AssetType.find_or_create_by(name: "Tv")
tv_device.has_child = false
tv_device.save!

dongle_device = AssetType.find_or_create_by(name: "Dongle")
dongle_device.has_child = false
dongle_device.save!

unrecognized_device = AssetType.find_or_create_by(name: "Unrecognized")
unrecognized_device.has_child = false
unrecognized_device.private_asset_attributes = { }
unrecognized_device.save!

laptop.private_asset_attributes = {
  hardware_details: {
    processor: "string",
    memory: "string",
    hard_drive:  "string",
    mac_address: "string",
    serial_number: "string"
    },
  software_details: {
    operating_system: "string",
    product_key: "string",
    install_date: "datetime"
  }
}
laptop.save!

desktop.private_asset_attributes = {
  hardware_details: {
    processor: "string",
    memory: "string",
    hard_drive: "string",
    mac_address: "string",
    serial_number: "string"
    },
  software_details: {
    operating_system: "string",
    product_key: "string",
    install_date: "datetime"
  }
}
desktop.save!

server.private_asset_attributes = {
  hardware_details: {
    processor: "string",
    memory: "string",
    hard_drive: "string",
    mac_address: "string",
    serial_number: "string"
    },
  software_details: {
    operating_system: "string",
    product_key: "string",
    install_date: "datetime"
  }
}
server.save!

apple_device.private_asset_attributes = {
  hardware_details: {
    processor: "string",
    memory: "string",
    hard_drive: "string",
    mac_address: "string",
    serial_number: "string"
    },
  software_details: {
    operating_system: "string",
    product_key: "string",
    install_date: "datetime"
  }
}
apple_device.save!

windows_device.private_asset_attributes = {
  hardware_details: {
    processor: "string",
    memory: "string",
    hard_drive: "string",
    mac_address: "string",
    serial_number: "string"
    },
  software_details: {
    operating_system: "string",
    product_key: "string",
    install_date: "datetime"
  }
}
windows_device.save!

phone.private_asset_attributes = {
  hardware_properties: {
    mac_address: "string",
    connection_interface: "string"
    },
  software_properties: {
    software: "string"
  }
}
phone.save!
phone_system.private_asset_attributes = {
  hardware_properties: {
    mac_address: "string",
    connection_interface: "string"
    },
  software_properties: {
    operating_system: "string",
    firmware_version: "string"
  }
}
phone_system.save!

router.private_asset_attributes = {
  hardware_details: {
    ports:   "string",
    mac_address: "string",
    serial_number:  "string"
    },
  software_details: {
    firmware: "string",
    ip_address: "string",
    subnet_mask: "string",
    default_gateway: "string"
  }
}
router.save!

switch.private_asset_attributes = {
  hardware_details: {
    ports:   "string",
    mac_address: "string",
    serial_number:  "string"
    },
  software_details: {
    firmware: "string",
    ip_address: "string",
    subnet_mask: "string",
    default_gateway: "string"
  }
}
switch.save!

firewall.private_asset_attributes = {
  hardware_details: {
    ports:   "string",
    mac_address: "string",
    serial_number: "string"
    },
  software_details: {
    firmware: "string",
    ip_address: "string",
    subnet_mask: "string",
    default_gateway: "string"
  }
}
firewall.save!

mobile.private_asset_attributes = {
  hardware_details: {
    manufacturer: "string",
    model: "string",
    imei: "string",
    serial_number: "string"
    },
  software_details: {
    operating_system: "string",
    version: "string"
  }
}
mobile.save!

tablet.private_asset_attributes = {
  hardware_details: {
    manufacturer: "string",
    model: "string",
    imei: "string",
    serial_number: "string"
    },
  software_details: {
    operating_system: "string",
    version: "string"
  }
}
tablet.save!

iphone_device.private_asset_attributes = {
  hardware_details: {
    manufacturer: "string",
    model: "string",
    imei: "string",
    serial_number: "string"
    },
  software_details: {
    operating_system: "string",
    version: "string"
  }
}
iphone_device.save!

ipad_device.private_asset_attributes = {
  hardware_details: {
    manufacturer: "string",
    model: "string",
    imei: "string",
    serial_number: "string"
    },
  software_details: {
    operating_system: "string",
    version: "string"
  }
}
ipad_device.save!

printer.private_asset_attributes = {
  hardware_details: {
    manufacturer: "string",
    model: "string",
    mac_address: "string",
    serial_number: "string"
    },
  software_details: {
    operating_system: "string",
    ip_address: "string"
  }
}
printer.save!

other_asset_type = AssetType.find_or_create_by(name: "Other")
other_asset_type.has_child = false
other_asset_type.private_asset_attributes = { }
other_asset_type.save!

software = AssetType.find_or_create_by(name: "Software")
if software
  ManagedAsset.where(company_asset_type_id: software.id).update_all(company_asset_type_id: other_asset_type.id)
  software.destroy
end
