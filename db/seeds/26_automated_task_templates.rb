AutomatedTasks::TaskEventTemplate.destroy_all
AutomatedTasks::TaskActionTemplate.destroy_all
AutomatedTasks::TaskTemplate.destroy_all

ticket_changed = AutomatedTasks::EventType.find_by(name: "{a ticket} is updated",
                                                   module: 'help_tickets',
                                                   event_class: 'TicketUpdated')

ticket_created = AutomatedTasks::EventType.find_by(name: "{a ticket} is created",
                                                   module: 'help_tickets',
                                                   event_class: 'TicketAdded')

attachment_added = AutomatedTasks::EventType.find_by(name: "{an attachment} is added to a ticket",
                                                     module: 'help_tickets',
                                                     model: 'CustomFormAttachment',
                                                     event_class: 'AttachmentAdded')

comment_created = AutomatedTasks::EventType.find_by(name: "{a comment} is added to a ticket",
                                                    module: 'help_tickets',
                                                    model: 'HelpTicketComment',
                                                    event_class: 'CommentAdded')

send_email = AutomatedTasks::ActionType.find_by(name: 'send an [email]',
                                                module: 'help_tickets',
                                                model: 'HelpTicket',
                                                action_class: 'SendEmail')

form_field = AutomatedTasks::ObjectSubjectType.find_by(name: "/a ticket/ with {a form field}",
                                                       key: 'ticket',
                                                       subject_class: 'TicketFormField')

any_assignment = AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "any attachment",
                                                                     key: 'attachment',
                                                                     subject_class: 'AnyObject')

any_ticket = AutomatedTasks::ObjectSubjectType.find_by(name: "any ticket",
                                                       key: "ticket",
                                                       subject_class: 'AnyTicket')

any_comment = AutomatedTasks::ObjectSubjectType.find_by(name: "any comment",
                                                        key: "comment",
                                                        subject_class: "AnyObject")

# 1. Create Task Template: Notify Ticket Created
notify_ticket_created_template = AutomatedTasks::TaskTemplate.create!(
  name: 'Notify Ticket Created',
  description: 'Sends an email or notification when a ticket is created',
  order: 1
)

# Create Task Event Template for "Notify Ticket Created"
AutomatedTasks::TaskEventTemplate.create!(
  task_template: notify_ticket_created_template,
  value: {
    "class" => "TaskEvent",
    "nodeType" => {
      "id" => ticket_created.id,
      "name" => "{a ticket} is created",
      "model" => "HelpTicket",
      "module" => "help_tickets",
      "eventClass" => "TicketAdded",
      "icon" => "nulodgicon-ios-copy-outline",
      "type" => "Event"
    },
    "nodes" => [
      {
        "class" => "EventDetail",
        "value" => nil,
        "nodes" => [],
        "nodeType" => {
          "id" => any_ticket.id,
          "icon" => "nulodgicon-ios-copy-outline",
          "key" => "ticket",
          "name" => "any ticket",
          "subjectClass" => "AnyTicket",
          "parentType" => "AutomatedTasks::EventType",
          "parentId" => ticket_created,
          "type" => "Object"
        }
      }
    ],
    "value" => nil
  }
)

# Create Task Action Template for "Notify Ticket Created"
AutomatedTasks::TaskActionTemplate.create!(
  task_template: notify_ticket_created_template,
  value: {
    "nodeType" => {
      "id" => send_email.id,
      "name" => "send an [email]",
      "module" => "help_tickets",
      "model" => "HelpTicket",
      "actionClass" => "SendEmail",
      "icon" => "genuicon-envelope-o",
      "type" => "Action"
    },
    "value" => {
      "target" => "assigned,agents,ticket_creator",
      "subject" => "New Ticket Added",
      "body" => "\n<div>\n  <div>\n    <span style=\"font-size: 1rem;\"><b>{ticket_created_by}</b> submitted a new help desk ticket on {ticket_created_at}: <b>\"{ticket_subject}\" [ticket_number]</b></span>\n    <br>\n    <br>\n    {ticket_button}\n    </div>\n  </div>\n</div>\n        ",
      "option" => {
        "value" => 2,
        "name" => "Select Template"
      },
      "template" => {
        "templateTitle" => "Default New Ticket Email",
        "subjectTitle" => "New Ticket [ticket_number]: {ticket_subject}",
        "emailBody" => "\n      <div>\n        <div>\n          <span style=\"font-size: 1rem;\"><b>{ticket_created_by}</b> submitted a new help desk ticket on {ticket_created_at}: <b>\"{ticket_subject}\" [ticket_number]</b></span>\n          <br/>\n          <br/>\n          {ticket_button}\n        </div>\n      </div>\n    ",
        "templateName" => "ticket created",
        "templateType" => "end_users",
        "isDefault" => true,
      }
    }
  }
)

# 2. Create Task Template: Notify on Priority
notify_on_priority_template = AutomatedTasks::TaskTemplate.create!(
  name: 'Notify on Priority',
  description: 'Sends an email or notification when a ticket priority changes',
  order: 2
)

# Create Task Event Template for "Notify on Priority"
AutomatedTasks::TaskEventTemplate.create!(
  task_template: notify_on_priority_template,
  value: {
    "class" => "TaskEvent",
    "nodeType" => {
      "id": ticket_changed.id,
      "name" => "{a ticket} is updated",
      "model" => "CustomFormValue",
      "module" => "help_tickets",
      "eventClass" => "TicketUpdated",
      "icon" => nil,
      "type" => "Event"
    },
    "nodes" => [
      {
        "class" => "EventDetail",
        "value" => {
          "customForms" => [{"id" => "all", "formName" => "Any Form", "isActive" => true }],
          "formField" => {
            "name" => "priority",
            "fieldAttributeType" => "priority",
            "label": "priority",
            "options": [
              {
                "name": "low",
                "color": "#ffd700"
              },
              {
                "name": "medium",
                "color": "#ffa500"
              },
              {
                "name": "high",
                "color": "#ff0000"
              }
            ]
          },
          "any" => true,
          "partialMatch" => false,
          "negate" => nil
        },
        "nodes" => [],
        "nodeType" => {
          "id" => form_field.id,
          "icon" => nil,
          "key" => "ticket",
          "name" => "/a ticket/ with {a form field}",
          "subjectClass" => "TicketFormField",
          "parentType" => "AutomatedTasks::TaskEvent",
          "parentId" => ticket_changed,
          "type" => "Object"
        }
      }
    ],
    "value" => nil
  }
)

# Create Task Action Template for "Notify on Priority"
AutomatedTasks::TaskActionTemplate.create!(
  task_template: notify_on_priority_template,
  value: {
    "nodeType" => {
      "id" => send_email.id,
      "name" => "send an [email]",
      "module" => "help_tickets",
      "model" => "HelpTicket",
      "actionClass" => "SendEmail",
      "icon" => "genuicon-envelope-o",
      "type" => "Action"
    },
    "value" => {
      "target" => "assigned,agents,ticket_creator",
      "subject" => "Ticket priority has been updated",
      "body" => "<div><div><span style=\"font-size: 1rem;\"><b>Priority</b> has been changed for the ticket.</span><br/><br/></div></div>"
    }
  }
)

# 3 Create Task Template: Notify Comment Added - Email
notify_comment_added_template = AutomatedTasks::TaskTemplate.create!(
  name: 'Notify on New Comment',
  description: 'Sends an email or notification when a comment is added to a ticket',
  order: 3
)

# Create Task Event Template for "Notify Comment Added - Email"
AutomatedTasks::TaskEventTemplate.create!(
  task_template: notify_comment_added_template,
  value: {
    "class" => "TaskEvent",
    "nodeType" => {
      "id" => comment_created.id,
      "name" => "{a comment} is added to a ticket",
      "model" => "HelpTicketComment",
      "module" => "help_tickets",
      "eventClass" => "CommentAdded",
      "icon" => "genuicon-comment-o",
      "type" => "Event"
    },
    "nodes" => [
      {
        "class" => "EventDetail",
        "value" => nil,
        "nodes" => [],
        "nodeType" => {
          "id" => any_comment.id,
          "icon" => "nulodgicon-person",
          "key" => "comment",
          "name" => "any comment",
          "subjectClass" => "AnyObject",
          "parentType" => "AutomatedTasks::EventType",
          "parentId" => comment_created,
          "type" => "Object"
        }
      }
    ],
    "value" => nil
  }
)

# Create Task Action Template for "Notify Comment Added - Email"
AutomatedTasks::TaskActionTemplate.create!(
  task_template: notify_comment_added_template,
  value: {
    "nodeType" => {
      "id" => send_email.id,
      "name" => "send an [email]",
      "module" => "help_tickets",
      "model" => "HelpTicketComment",
      "actionClass" => "SendEmail",
      "icon" => "genuicon-envelope-o",
      "type" => "Action"
    },
    "value" => {
      "target" => "assigned,agents,private,ticket_creator",
      "subject" => "Ticket has new comment added",
      "body" => "\n<div>\n  <div>\n    <span style=\"font-size: 0.875rem;\">A new comment has been added by <b>{commenter}</b> to ticket <b>{ticket subject} [ticket_number]</b>.</span>\n    <br/>\n    <br/>\n    <p style=\"font-size: 0.875rem;\">{comment body}</p>\n    <br/>\n    <br/>\n    {ticket_button}\n  </div>\n</div>\n        ",
      "option" => {
        "value" => 2,
        "name" => "Select Template"
      },
      "template" => {
        "templateTitle" => "Default Comment Added Email",
        "subjectTitle" => "Ticket [ticket_number] has new comment added",
        "emailBody" => "\n      <div>\n        <div>\n          <span style=\"font-size: 0.875rem;\">A new comment has been added by <b>{commenter}</b> to ticket <b>{ticket subject} [ticket_number]</b>.</span>\n          <br/>\n          <br/>\n          <p style=\"font-size: 0.875rem;\">{comment body}</p>\n          <br/>\n          <br/>\n          {ticket_button}\n        </div>\n      </div>\n    ",
        "templateName" => "comment added",
        "templateType" => "end_users",
        "isDefault" => true,
      }
    }
  }
)

# 4. Create Task Template: Notify on Close
notify_on_close_template = AutomatedTasks::TaskTemplate.create!(
  name: 'Notify on Close',
  description: 'Sends an email or notification on ticket close',
  order: 4
)

# Create Task Event Template for "Notify on Close"
AutomatedTasks::TaskEventTemplate.create!(
  task_template: notify_on_close_template,
  value: {
    "class" => "TaskEvent",
    "nodeType" => {
      "id" => ticket_changed.id,
      "name" => "{a ticket} is updated",
      "model" => "CustomFormValue",
      "module" => "help_tickets",
      "eventClass" => "TicketUpdated",
      "icon" => nil,
      "type" => "Event"
    },
    "nodes" => [
      {
        "class" => "EventDetail",
        "value" => {
          "customForms" => [{"id" => "all", "formName" => "Any Form", "isActive" => true }],
          "formField" => {
            "name" => "status",
            "fieldAttributeType" => "status",
            "label": "status",
            "options": [
                {
                    "name": "Open",
                    "color": "#2ECC71"
                },
                {
                    "name": "In Progress",
                    "color": "#5DADE2"
                },
                {
                    "name": "Closed",
                    "color": "#626567"
                }
            ]
          },
          "negate" => false,
          "any" => false,
          "partialMatch" => false,
          "status": "Closed"
        },
        "nodeType" => {
          "id" => form_field.id,
          "icon" => nil,
          "key" => "ticket",
          "name" => "/a ticket/ with {a form field}",
          "subjectClass" => "TicketFormField",
          "parentType" => "AutomatedTasks::EventType",
          "parentId" => ticket_changed,
          "type" => "Object"
        }
      }
    ],
    "value" => nil
  }
)

# Create Task Action Template for "Notify on Close"
AutomatedTasks::TaskActionTemplate.create!(
  task_template: notify_on_close_template,
  value: {
    "nodeType" => {
      "id" => send_email.id,
      "name" => "send an [email]",
      "module" => "help_tickets",
      "model" => "HelpTicket",
      "actionClass" => "SendEmail",
      "icon" => "genuicon-envelope-o",
      "type" => "Action"
    },
    "value" => {
      "target" => "ticket_creator",
      "subject" => "Please take a moment to complete a quick survey",
      "body" => "<div><!--block-->The ticket has been closed. Please click on the button below to complete a very quick survey.</div>",
      "option" => {
        "value" => 2,
        "name" => "Select Template"
      },
      "template" => {
        "templateTitle" => "Default Closed Survey Email",
        "subjectTitle" => "Please take a moment to complete a quick survey",
        "emailBody" => "\n      <div>\n        <div>\n          <span style=\"font-size: 0.875rem;\">\n          Now that ticket <b>{ticket subject} [ticket_number]</b> is closed, please click on the button below to complete a very quick survey.\n          </span>\n          <br/>\n          <br/>\n          <p style=\"font-size: 0.875rem;\">{survey_url}</p>\n        </div>\n      </div>\n    ",
        "templateName" => "closed survey",
        "templateType" => "surveys",
        "isDefault" => true,
      }
    }
  }
)

# 5. Create Task Template: Notify Assigned-To Changes
notify_assigned_to_changes_template = AutomatedTasks::TaskTemplate.create!(
  name: 'Notify Assigned-To Changes',
  description: 'Sends an email or notification when a ticket assigned-to changes',
  order: 5
)

# Create Task Event Template for "Notify Assigned-To Changes"
AutomatedTasks::TaskEventTemplate.create!(
  task_template: notify_assigned_to_changes_template,
  value: {
    "class" => "TaskEvent",
    "nodeType" => {
      "id" => ticket_changed.id,
      "name" => "{a ticket} is updated",
      "model" => "CustomFormValue",
      "module" => "help_tickets",
      "eventClass" => "TicketUpdated",
      "icon" => nil,
      "type" => "Event"
    },
    "nodes" => [
      {
        "class" => "EventDetail",
        "value" => {
          "customForms" => [{"id" => "all", "formName" => "Any Form", "isActive" => true }],
          "formField" => {
            "name" => "assigned_to",
            "fieldAttributeType" => "people_list"
          },
          "any" => true,
          "partialMatch" => false 
        },
        "nodes" => [],
        "nodeType" => {
          "id" => form_field.id,
          "icon" => nil,
          "key" => "ticket",
          "name" => "/a ticket/ with {a form field}",
          "subjectClass" => "TicketFormField",
          "parentType" => "AutomatedTasks::EventType",
          "parentId" => ticket_changed,
          "type" => "Object"
        }
      }
    ],
    "value" => nil
  }
)

# Create Task Action Template for "Notify Assigned-To Changes"
AutomatedTasks::TaskActionTemplate.create!(
  task_template: notify_assigned_to_changes_template,
  value: {
    "nodeType" => {
      "id" => send_email.id,
      "name" => "send an [email]",
      "module" => "help_tickets",
      "model" => "HelpTicket",
      "actionClass" => "SendEmail",
      "icon" => "genuicon-envelope-o",
      "type" => "Action"
    },
    "value" => {
      "target" => "related",
      "subject" => "New Ticket Assignment [ticket_number]: {ticket_subject}",
      "body" => "\n<div>\n  <div>\n    <span style=\"font-size: 0.875rem;\"><b>{ticket_assigned_to}</b> has been assigned to ticket <b>{ticket_subject} [ticket_number]</b>.</span>\n    <br/>\n    <br/>\n    <p style=\"font-size: 0.875rem;\">{ticket_description}</p>\n    <br/>\n    <br/>\n    {ticket_button}\n  </div>\n</div>\n        ",
      "option" => {
        "value" => 2,
        "name" => "Select Template"
      },
      "template" => {
        "templateTitle" => "Default Ticket Assigned Email",
        "subjectTitle" => "New Ticket Assignment: {ticket_subject}",
        "emailBody" => "\n      <div>\n        <div>\n          <span style=\"font-size: 0.875rem;\"><b>{ticket_assigned_to}</b> has been assigned to ticket <b>{ticket_subject} [ticket_number]</b>.</span>\n          <br/>\n          <br/>\n          <p style=\"font-size: 0.875rem;\">{ticket_description}</p>\n          <br/>\n          <br/>\n          {ticket_button}\n        </div>\n      </div>\n    ",
        "templateName" => "ticket assigned",
        "templateType" => "end_users",
        "isDefault" => true,
      }
    }
  }
)

# 6. Create Task Template: Notify on Status
notify_on_status_template = AutomatedTasks::TaskTemplate.create!(
  name: 'Notify on Status',
  description: 'Sends an email or notification when a ticket is opened',
  order: 6
)

# Create Task Event Template for "Notify on Status"
AutomatedTasks::TaskEventTemplate.create!(
  task_template: notify_on_status_template,
  value: {
    "class" => "TaskEvent",
    "nodeType" => {
      "id": ticket_changed.id,
      "name" => "{a ticket} is updated",
      "model" => "CustomFormValue",
      "module" => "help_tickets",
      "eventClass" => "TicketUpdated",
      "icon" => nil,
      "type" => "Event"
    },
    "nodes" => [
      {
        "class" => "EventDetail",
        "value" => {
          "customForms" => [{"id" => "all", "formName" => "Any Form", "isActive" => true }],
          "formField" => {
            "name" => "status",
            "fieldAttributeType" => "status",
            "label": "status",
            "options": [
                {
                    "name": "Open",
                    "color": "#2ECC71"
                },
                {
                    "name": "In Progress",
                    "color": "#5DADE2"
                },
                {
                    "name": "Closed",
                    "color": "#626567"
                }
            ]
          },
          "any" => false,
          "negate" => false,
          "partialMatch"=> false,
          "status": "Open"  
        },
        "nodes" => [],
        "nodeType" => {
          "id" => form_field.id,
          "icon" => nil,
          "key" => "ticket",
          "name" => "/a ticket/ with {a form field}",
          "subjectClass" => "TicketFormField",
          "parentType" => "AutomatedTasks::TaskEvent",
          "parentId" => ticket_changed,
          "type" => "Object"
        }
      }
    ],
    "value" => nil
  }
)

# Create Task Action Template for "Notify on Open"
AutomatedTasks::TaskActionTemplate.create!(
  task_template: notify_on_status_template,
  value: {
    "nodeType" => {
      "id" => send_email.id,
      "name" => "send an [email]",
      "module" => "help_tickets",
      "model" => "HelpTicket",
      "actionClass" => "SendEmail",
      "icon" => "genuicon-envelope-o",
      "type" => "Action"
    },
    "value" => {
      "target" => "assigned,ticket_creator,agents",
      "subject" => "Ticket Status has been updated to open",
      "body" => "<div><!--block-->The ticket has been opened.</div>",
      "template" => nil,
      "recipients" => nil,
      "contributors" => [],
      "customField" => nil,
      "selectedCustomForms" => nil
    }
  }
)

# 7. Create Task Template: Notify Attachment Added
notify_attachment_added_template = AutomatedTasks::TaskTemplate.create!(
  name: 'Notify Attachment Added',
  description: 'Sends an email or notification when an attachment is added to a ticket',
  order: 7
)

# Create Task Event Template for "Notify Attachment Added"
AutomatedTasks::TaskEventTemplate.create!(
  task_template: notify_attachment_added_template,
  value: {
    "class" => "TaskEvent",
    "nodeType" => {
      "id" => attachment_added.id,
      "name" => "{an attachment} is added to a ticket",
      "model" => "CustomFormAttachment",
      "module" => "help_tickets",
      "eventClass" => "AttachmentAdded",
      "icon" => "genuicon-ios-person-outline",
      "type" => "Event"
    },
    "nodes" => [
      {
        "class" => "EventDetail",
        "value" => nil,
        "nodes" => [],
        "nodeType" => {
          "id" => any_assignment.id,
          "icon" => "genuicon-envelope-o",
          "key" => "attachment",
          "name" => "any attachment",
          "subjectClass" => "AnyObject",
          "parentType" => "AutomatedTasks::EventType",
          "parentId" => attachment_added,
          "type" => "Object"
        }
      }
    ],
    "value" => nil
  }
)

# Create Task Action Template for "Notify Attachment Added"
AutomatedTasks::TaskActionTemplate.create!(
  task_template: notify_attachment_added_template,
  value: {
    "nodeType" => {
      "id" => send_email.id,
      "name" => "send an [email]",
      "module" => "help_tickets",
      "model" => "HelpTicket",
      "actionClass" => "SendEmail",
      "icon" => "genuicon-envelope-o",
      "type" => "Action"
    },
    "value" => {
      "target" => "assigned,agents,ticket_creator",
      "subject" => "New Attachment Added",
      "body" => "\n          <div>\n            <div>\n              <span style=\"font-size: 0.875rem;\">You have a new attachment for Help Desk ticket <b>&quot;{ticket_subject}&quot; [ticket_number]</b>.</span>\n              <br/>\n              <br/>\n              <p style=\"font-size: 0.875rem;\">{attachment_filename}</p>\n              <br/>\n              <br/>\n              {ticket_button}\n              </div>\n            </div>\n          </div>\n        "
    }
  }
)

