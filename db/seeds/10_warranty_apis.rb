config = {
  url: "https://css.api.hp.com",
  key: Rails.application.credentials.hp_api[:key],
  machine_serial_number: "5CG527213T",
  product_number: "L3Z71UA#ABA"
}
WarrantyApi.find_or_create_by!(name: "HP").update(config: config.to_json)

config = {
  url: "https://api.dell.com",
  machine_serial_number: "5L4Z4Q2"
}
WarrantyApi.find_or_create_by!(name: "Dell").update(config: config.to_json)

config = {
  url: "https://ibase.lenovo.com",
  id: "LSC3",
  machine_serial_number: "PC02MKFF"
}
WarrantyApi.find_or_create_by!(name: "Lenovo").update(config: config.to_json)

config = { url: "https://supportapi.lenovo.com/v2.5/warranty?Serial=" }
WarrantyApi.find_or_create_by!(name: 'Lenovo v2.5').update(config: config.to_json)
