load(File.join(Rails.root, 'db', 'seeds', 'custom_form_templates', '_fields_setup.rb'))
template = CustomFormTemplate.find_by(form_name: 'Base Ticket', company_module: CustomFormTemplate.company_modules["helpdesk"], is_active: true)
if template
  template.form_name = 'Simple Request'
  template.save!
end

template = CustomFormTemplate.find_or_initialize_by(form_name: 'Simple Request', company_module: CustomFormTemplate.company_modules["helpdesk"], is_active: true)
if !template.id?
  template["image_url"] = "https://nulodgic-static-assets.s3.amazonaws.com/images/custom_forms_help_ticket.svg"
  template["description"] = "When you need simple request tickets."
  template.save!
end

unique_fields = [
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["rich_text"],
      label: "Description",
      name: "description"
    },
    field_position: FieldPositionTemplate.positions["left"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["attachment"],
      label: "Attachments",
      name: "attachments"
    },
    field_position: FieldPositionTemplate.positions["main"]
  }
]

fields = COMMON_FIELDS + unique_fields
CustomForms::FieldSeed.new(template, fields).call
