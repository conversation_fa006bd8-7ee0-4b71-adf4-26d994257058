load(File.join(Rails.root, 'db', 'seeds', 'custom_form_templates', '_location_fields_setup.rb'))
template = CustomFormTemplate.find_or_initialize_by(form_name: 'Office Location', company_module: CustomFormTemplate.company_modules["location"], is_active: true)

unique_fields = [
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["text_area"],
      label: "Company Address",
      name: "company_address"
    },
    field_position: FieldPositionTemplate.positions["right"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["text_area"],
      label: "City",
      name: "city"
    },
    field_position: FieldPositionTemplate.positions["right"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["text_area"],
      label: "Country",
      name: "country"
    },
    field_position: FieldPositionTemplate.positions["right"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["avatar"],
      label: "Location Avatar",
      name: "location_avatar",
      required: false,
    },
    field_position: FieldPositionTemplate.positions["title"],
  }
]

template["image_url"] = "https://nulodgic-static-assets.s3.amazonaws.com/images/custom_forms_account.svg"
template["description"] = "When you need to add your office's location."
template.save!
fields = COMMON_LOCATION_FIELDS + unique_fields
CustomForms::FieldSeed.new(template, fields).call
