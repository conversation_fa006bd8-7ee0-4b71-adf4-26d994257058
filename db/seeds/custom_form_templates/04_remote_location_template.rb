load(File.join(Rails.root, 'db', 'seeds', 'custom_form_templates', '_location_fields_setup.rb'))
template = CustomFormTemplate.find_or_initialize_by(form_name: 'Remote Location', company_module: CustomFormTemplate.company_modules["location"], is_active: true)

unique_fields = [
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["text_area"],
      label: "Remote Address",
      name: "remote_address"
    },
    field_position: FieldPositionTemplate.positions["right"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["phone"],
      label: "Contact Number",
      name: "contact_number",
    },
    field_position: FieldPositionTemplate.positions["right"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["avatar"],
      label: "Location Avatar",
      name: "location_avatar",
      required: false,
    },
    field_position: FieldPositionTemplate.positions["title"],
  }
]

template["image_url"] = "https://nulodgic-static-assets.s3.amazonaws.com/images/custom_forms_hardware.svg"
template["description"] = "When you need to add your remote location.."
template.save!
fields = COMMON_LOCATION_FIELDS + unique_fields
CustomForms::FieldSeed.new(template, fields).call
