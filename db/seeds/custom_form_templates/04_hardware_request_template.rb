load(File.join(Rails.root, 'db', 'seeds', 'custom_form_templates', '_fields_setup.rb'))
template = CustomFormTemplate.find_or_initialize_by(form_name: 'Hardware Request', company_module: CustomFormTemplate.company_modules["helpdesk"], is_active: true)

unique_fields = [
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["people_list"],
      label: "Raise this request on behalf of",
      name: "raise_this_request_on_behalf_of"
    },
    field_position: FieldPositionTemplate.positions["right"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["text_area"],
      label: "Why do you need this hardware",
      name: "why_do_you_need_this_hardware",
    },
    field_position: FieldPositionTemplate.positions["left"]
  }
]

template["image_url"] = "https://nulodgic-static-assets.s3.amazonaws.com/images/custom_forms_hardware.svg"
template["description"] = "When you need to receive new hardware requests."
template.save!
fields = COMMON_FIELDS + unique_fields
CustomForms::FieldSeed.new(template, fields).call
