load(File.join(Rails.root, 'db', 'seeds', 'custom_form_templates', '_fields_setup.rb'))
template = CustomFormTemplate.find_or_initialize_by(form_name: 'IT General Request', company_module: CustomFormTemplate.company_modules["helpdesk"], is_active: true)

unique_fields = [
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["people_list"],
      label: "Raise this request on behalf of",
      name: "raise_this_request_on_behalf_of",
      sort_list: "[\"Ascending\", \"First Name, Last Name\"]"
    },
    field_position: FieldPositionTemplate.positions["right"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["asset_list"],
      label: "Impacted Devices",
      name: "impacted_devices",
    },
    field_position: FieldPositionTemplate.positions["right"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["people_list"],
      label: "Followers",
      name: "followers",
      audience: "guests",
      is_followers_field: true,
    },
    field_position: FieldPositionTemplate.positions["right"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["location_list"],
      label: "Location List",
      name: "location_list"
    },
    field_position: FieldPositionTemplate.positions["right"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["rich_text"],
      label: "Description",
      name: "description"
    },
    field_position: FieldPositionTemplate.positions["left"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["attachment"],
      label: "Attachments",
      name: "attachments"
    },
    field_position: FieldPositionTemplate.positions["main"]
  }
]

template["image_url"] = "https://nulodgic-static-assets.s3.amazonaws.com/images/custom_forms_it_request.svg"
template["description"] = "When you need to receive general IT requests."
template.save!
fields = COMMON_FIELDS + unique_fields
CustomForms::FieldSeed.new(template, fields).call
