load(File.join(Rails.root, 'db', 'seeds', 'custom_form_templates', '_location_fields_setup.rb'))
template = CustomFormTemplate.find_or_initialize_by(form_name: 'Base Location', company_module: CustomFormTemplate.company_modules["location"], is_active: true)

unique_fields = [
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["text_area"],
      label: "Address line 2",
      name: "address_2"
    },
    field_position: FieldPositionTemplate.positions["right"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["list"],
      label: "State/Province",
      name: "state",
      list_type: "state",
      options: ["AA","AE","AK","AL","AP","AR","AS","AZ","CA","CO","CT","DC","DE","FL","GA","GU","HI","IA", "ID","IL","IN","KS","KY","LA","MA","MD","ME","MI","MN","MO","MP","MS","MT","NC","ND","NE", "NH","NJ","NM","NV","NY","OH","OK","OR","PA","PR","RI","SC","SD","TN","TX","UM","UT","VA", "VI","VT","WA","WI","WV","WY"].to_json,
    },
    field_position: FieldPositionTemplate.positions["right"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["list"],
      label: "Country",
      name: "country_name",
      list_type: "country",
      options: ["Aruba", "Afghanistan", "Angola", "Anguilla", "Åland Islands", "Albania", "Andorra", "United Arab Emirates", "Argentina", "Armenia", "American Samoa", "Antarctica", "French Southern Territories", "Antigua and Barbuda", "Australia", "Austria", "Azerbaijan", "Burundi", "Belgium", "Benin", "Bonaire, Sint Eustatius and Saba", "Burkina Faso", "Bangladesh", "Bulgaria", "Bahrain", "Bahamas", "Bosnia and Herzegovina", "Saint Barthélemy", "Belarus", "Belize", "Bermuda", "Bolivia", "Brazil", "Barbados", "Brunei Darussalam", "Bhutan", "Bouvet Island", "Botswana", "Central African Republic", "Canada", "Cocos (Keeling) Islands", "Switzerland", "Chile", "China", "Côte d'Ivoire", "Cameroon", "Congo, The Democratic Republic of the", "Congo", "Cook Islands", "Colombia", "Comoros", "Cabo Verde", "Costa Rica", "Cuba", "Curaçao", "Christmas Island", "Cayman Islands", "Cyprus", "Czechia", "Germany", "Djibouti", "Dominica", "Denmark", "Dominican Republic", "Algeria", "Ecuador", "Egypt", "Eritrea", "Western Sahara", "Spain", "Estonia", "Ethiopia", "Finland", "Fiji", "Falkland Islands (Malvinas)", "France", "Faroe Islands", "Micronesia, Federated States of", "Gabon", "United Kingdom", "Georgia", "Guernsey", "Ghana", "Gibraltar", "Guinea", "Guadeloupe", "Gambia", "Guinea-Bissau", "Equatorial Guinea", "Greece", "Grenada", "Greenland", "Guatemala", "French Guiana", "Guam", "Guyana", "Hong Kong", "Heard Island and McDonald Islands", "Honduras", "Croatia", "Haiti", "Hungary", "Indonesia", "Isle of Man", "India", "British Indian Ocean Territory", "Ireland", "Iran, Islamic Republic of", "Iraq", "Iceland", "Israel", "Italy", "Jamaica", "Jersey", "Jordan", "Japan", "Kazakhstan", "Kenya", "Kyrgyzstan", "Cambodia", "Kiribati", "Saint Kitts and Nevis", "Korea, Republic of", "Kuwait", "Lao People's Democratic Republic", "Lebanon", "Liberia", "Libya", "Saint Lucia", "Liechtenstein", "Sri Lanka", "Lesotho", "Lithuania", "Luxembourg", "Latvia", "Macao", "Saint Martin (French part)", "Morocco", "Monaco", "Moldova", "Madagascar", "Maldives", "Mexico", "Marshall Islands", "North Macedonia", "Mali", "Malta", "Myanmar", "Montenegro", "Mongolia", "Northern Mariana Islands", "Mozambique", "Mauritania", "Montserrat", "Martinique", "Mauritius", "Malawi", "Malaysia", "Mayotte", "Namibia", "New Caledonia", "Niger", "Norfolk Island", "Nigeria", "Nicaragua", "Niue", "Netherlands", "Norway", "Nepal", "Nauru", "New Zealand", "Oman", "Pakistan", "Panama", "Pitcairn", "Peru", "Philippines", "Palau", "Papua New Guinea", "Poland", "Puerto Rico", "Korea, Democratic People's Republic of", "Portugal", "Paraguay", "Palestine, State of", "French Polynesia", "Qatar", "Réunion", "Romania", "Russian Federation", "Rwanda", "Saudi Arabia", "Sudan", "Senegal", "Singapore", "South Georgia and the South Sandwich Islands", "Saint Helena, Ascension and Tristan da Cunha", "Svalbard and Jan Mayen", "Solomon Islands", "Sierra Leone", "El Salvador", "San Marino", "Somalia", "Saint Pierre and Miquelon", "Serbia", "South Sudan", "Sao Tome and Principe", "Suriname", "Slovakia", "Slovenia", "Sweden", "Eswatini", "Sint Maarten (Dutch part)", "Seychelles", "Syrian Arab Republic", "Turks and Caicos Islands", "Chad", "Togo", "Thailand", "Tajikistan", "Tokelau", "Turkmenistan", "Timor-Leste", "Tonga", "Trinidad and Tobago", "Tunisia", "Turkey", "Tuvalu", "Taiwan", "Tanzania", "Uganda", "Ukraine", "United States Minor Outlying Islands", "Uruguay", "United States", "Uzbekistan", "Holy See (Vatican City State)", "Saint Vincent and the Grenadines", "Venezuela", "Virgin Islands, British", "Virgin Islands, U.S.", "Vietnam", "Vanuatu", "Wallis and Futuna", "Samoa", "Yemen", "South Africa", "Zambia", "Zimbabwe" ].to_json,
    },
    field_position: FieldPositionTemplate.positions["right"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["phone"],
      label: "Phone Number",
      name: "phone_number"
    },
    field_position: FieldPositionTemplate.positions["right"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["text_area"],
      label: "Notes",
      name: "notes"
    },
    field_position: FieldPositionTemplate.positions["right"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["avatar"],
      label: "Location Avatar",
      name: "location_avatar",
      required: false,
    },
    field_position: FieldPositionTemplate.positions["title"],
  }
]

template["image_url"] = "https://nulodgic-static-assets.s3.amazonaws.com/images/custom_forms_help_ticket.svg"
template["description"] = "When you need standard location."
template.save!
fields = COMMON_LOCATION_FIELDS + unique_fields
CustomForms::FieldSeed.new(template, fields).call
