load(File.join(Rails.root, 'db', 'seeds', 'custom_form_templates', '_staff_fields_setup.rb'))
template = CustomFormTemplate.find_or_initialize_by(form_name: 'Teammates', company_module: CustomFormTemplate.company_modules["company_user"], is_active: true)

unique_fields = [
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["phone"],
      label: "Mobile Phone",
      name: "mobile_phone"
    },
    field_position: FieldPositionTemplate.positions["right"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["phone"],
      label: "Work Phone",
      name: "work_phone"
    },
    field_position: FieldPositionTemplate.positions["right"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["number"],
      label: "Extension",
      name: "extension"
    },
    field_position: FieldPositionTemplate.positions["right"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["text"],
      label: "Title",
      name: "title"
    },
    field_position: FieldPositionTemplate.positions["right"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["text"],
      label: "Department",
      name: "department"
    },
    field_position: FieldPositionTemplate.positions["right"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["location_list"],
      label: "Location",
      name: "location"
    },
    field_position: FieldPositionTemplate.positions["right"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["people_list"],
      label: "Supervisor",
      name: "supervisor",
      sort_list: "[\"Ascending\", \"First Name, Last Name\"]"
    },
    field_position: FieldPositionTemplate.positions["right"]
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["avatar"],
      label: "User Avatar",
      name: "user_avatar",
      required: false,
    },
    field_position: FieldPositionTemplate.positions["title"],
  }
]

template["image_url"] = "https://nulodgic-static-assets.s3.amazonaws.com/images/buddy.png"
template["description"] = "For members of your company."
template.save!
fields = COMMON_STAFF_FIELDS + unique_fields
CustomForms::FieldSeed.new(template, fields).call
