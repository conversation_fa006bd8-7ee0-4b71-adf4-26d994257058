COMMON_FIELDS ||= [
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["text"],
      label: "Subject",
      name: "subject",
      required: true,
    },
    field_position: FieldPositionTemplate.positions["title"],
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["priority"],
      label: "Priority",
      name: "priority",
      default_value: "low",
      required: true,
      options: CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS,
    },
    field_position: FieldPositionTemplate.positions["right"],
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["status"],
      label: "Status",
      name: "status",
      default_value: "Open",
      required: true,
      options: CustomFormFieldTemplate::DEFAULT_STATUS_OPTIONS,
    },
    field_position: FieldPositionTemplate.positions["right"],
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["people_list"],
      label: "Created By",
      name: "created_by",
      default_value: "[\"current\"]",
      required: true,
      audience: "guests"
    },
    field_position: FieldPositionTemplate.positions["right"],
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["people_list"],
      label: "Assigned To",
      name: "assigned_to",
      options: "[]",
      sort_list: "[\"Ascending\",\"First Name, Last Name\"]",
      audience: "staff"
    },
    field_position: FieldPositionTemplate.positions["right"],
  },
  { field: {
      field_attribute_type: CustomFormFieldTemplate.field_attribute_types["rich_text"],
      label: "Description",
      name: "description",
    },
    field_position: FieldPositionTemplate.positions["left"],
  }
]
