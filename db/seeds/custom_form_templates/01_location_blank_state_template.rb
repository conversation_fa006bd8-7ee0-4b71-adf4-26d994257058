load(File.join(Rails.root, 'db', 'seeds', 'custom_form_templates', '_location_fields_setup.rb'))

template = CustomFormTemplate.find_or_initialize_by(form_name: 'Blank State', company_module: CustomFormTemplate.company_modules["location"], is_active: true)
template["image_url"] = "https://nulodgic-static-assets.s3.amazonaws.com/images/custom_forms_blank_slate.svg"
template["description"] = "Full customization, only contains the necessary inputs."
template.save!
CustomForms::FieldSeed.new(template, COMMON_LOCATION_FIELDS).call
