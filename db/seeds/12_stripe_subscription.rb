if Rails.env.staging? || Rails.env.development? || Rails.env.test?
  SubscriptionPlan.find_or_create_by(name: "Basic Plan", price: 2999, interval: "month", status: "active", stripe_identifier: 'basic-plan')
  SubscriptionPlan.find_or_create_by(name: "Yearly Plan", price: 35988, interval: "year", status: "active", stripe_identifier: Rails.application.credentials.stripe[:yearly_plan_id])

  SubscriptionPlan.find_or_create_by(name: "Full Platform Monthly", price: 12499, interval: "month", status: "active", stripe_identifier: 'price_1PNYHDGUVruH73uxZ4q8Mnv6')
  SubscriptionPlan.find_or_create_by(name: "Full Platform Yearly", price: 119988, interval: "year", status: "active", stripe_identifier: 'price_1PNYQeGUVruH73uxBAibolsM')

  SubscriptionPlan.find_or_create_by(name: "Asset Management Monthly", price: 4999, interval: "month", status: "active", stripe_identifier: 'price_1PQ66pGUVruH73uxn8YTPCJZ')
  SubscriptionPlan.find_or_create_by(name: "IT Help Desk Monthly", price: 4999, interval: "month", status: "active", stripe_identifier: 'price_1PQ67DGUVruH73uxymlPNtOq')
  SubscriptionPlan.find_or_create_by(name: "Vendor Management Monthly", price: 4999, interval: "month", status: "active", stripe_identifier: 'price_1PQ68FGUVruH73uxJbU1Mv2d')

  SubscriptionPlan.find_or_create_by(name: "Asset Management Yearly", price: 47988, interval: "year", status: "active", stripe_identifier: 'price_1PQ697GUVruH73uxBT0zpFqY')
  SubscriptionPlan.find_or_create_by(name: "IT Help Desk Yearly", price: 47988, interval: "year", status: "active", stripe_identifier: 'price_1PQ69WGUVruH73uxpgNfU1Lb')
  SubscriptionPlan.find_or_create_by(name: "Vendor Management Yearly", price: 47988, interval: "year", status: "active", stripe_identifier: 'price_1PQ69vGUVruH73uxhvkbyqJg')
elsif Rails.env.production?
  SubscriptionPlan.find_or_create_by(name: "Basic Plan", price: 2999, interval: "month", status: "active", stripe_identifier: 'basic-plan')
  SubscriptionPlan.find_or_create_by(name: "Yearly Plan", price: 35988, interval: "year", status: "active", stripe_identifier: Rails.application.credentials.stripe[:yearly_plan_id])

  SubscriptionPlan.find_or_create_by(name: "Full Platform Monthly", price: 12499, interval: "month", status: "active", stripe_identifier: 'price_1PWM0PGUVruH73uxk5QzdkUM')
  SubscriptionPlan.find_or_create_by(name: "Full Platform Yearly", price: 119988, interval: "year", status: "active", stripe_identifier: 'price_1PWLzuGUVruH73uxTsq67WO9')

  SubscriptionPlan.find_or_create_by(name: "Asset Management Monthly", price: 4999, interval: "month", status: "active", stripe_identifier: 'price_1PWLzIGUVruH73uxjB4KvFVu')
  SubscriptionPlan.find_or_create_by(name: "IT Help Desk Monthly", price: 4999, interval: "month", status: "active", stripe_identifier: 'price_1PWLyWGUVruH73uxjfX6nxpd')
  SubscriptionPlan.find_or_create_by(name: "Vendor Management Monthly", price: 4999, interval: "month", status: "active", stripe_identifier: 'price_1PWLxqGUVruH73uxjcPFIySU')

  SubscriptionPlan.find_or_create_by(name: "Asset Management Yearly", price: 47988, interval: "year", status: "active", stripe_identifier: 'price_1PWLx4GUVruH73uxn7OiNuLu')
  SubscriptionPlan.find_or_create_by(name: "IT Help Desk Yearly", price: 47988, interval: "year", status: "active", stripe_identifier: 'price_1PWLvqGUVruH73uxrqKSQ19Y')
  SubscriptionPlan.find_or_create_by(name: "Vendor Management Yearly", price: 47988, interval: "year", status: "active", stripe_identifier: 'price_1PWLv1GUVruH73uxvYdACa72')
end
