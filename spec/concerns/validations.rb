require 'spec_helper'

shared_examples_for 'url_validations' do
  let (:model) { described_class }

  it "adds http:// to URL" do
    params = { url: "www.apple.com" }
    if model.name != "Company"
      params[:company] = company
    end
    item = FactoryBot.create(model.to_s.underscore.to_sym, params)

    expect(item.url).to eq("http://www.apple.com")
  end

  it "does not add additional http:// if already present in the URL" do
    params = { url: "http://www.apple.com" }
    if model.name != "Company"
      params[:company] = company
    end
    item = FactoryBot.create(model.to_s.underscore.to_sym, params)

    expect(item.url).to eq("http://www.apple.com")
  end

  it "adds error if URL format is invalid" do
    params = { url: "wwwapplecom" }
    if model.name != "Company"
      params[:company] = company
    end
    item = FactoryBot.build(model.to_s.underscore.to_sym, params)

    expect(item.valid?).to eq(false)
    expect(item.errors[:url]).to be_present
    expect(item.errors[:url][0]).to eq("is invalid")
  end
end

shared_examples_for 'phone_number_validations' do
  let (:model) { described_class }

  it "formats the phone number" do
    if (model.name == "PhoneNumber")
      params = { number: "(*************" }
    else
      params = { phone_number: "(*************" }
    end

    if model.name != "Company"
      params[:company] = company
    end
    item = FactoryBot.create(model.to_s.underscore.to_sym, params)

    if (model.name == "PhoneNumber")
      expect(item.number).to eq("1234562345")
    else
      expect(item.phone_number).to eq("1234562345")
    end
  end
end

shared_examples_for 'email_validations' do
  let (:model) { described_class }
  it "accepts a valid email" do
    params = { email: "<EMAIL>" }
    if model.name != "Company"
      params[:company] = company
    end
    item = FactoryBot.create(model.to_s.underscore.to_sym, params)

    expect(item).to be_valid
  end

  it "does not accept an email missing an @ symbol" do
    params = { email: "testAtEmail.com" }
    if model.name != "Company"
      params[:company] = company
    end
    item = FactoryBot.build(model.to_s.underscore.to_sym, params)

    expect(item.valid?).to eq(false)
    expect(item.errors[:email]).to be_present
    expect(item.errors[:email][0]).to eq("is invalid")
  end

  it "does not accept an email missing a proper domain" do
    params = { email: "test@email" }
    if model.name != "Company"
      params[:company] = company
    end
    item = FactoryBot.build(model.to_s.underscore.to_sym, params)

    expect(item.valid?).to eq(false)
    expect(item.errors[:email]).to be_present
    expect(item.errors[:email][0]).to eq("is invalid")
  end
end


