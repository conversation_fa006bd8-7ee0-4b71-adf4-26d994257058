require 'spec_helper'

shared_examples_for 'fiscal_year' do 
  let(:model) { described_class.new }

  let(:fiscal_year) { Time.new("2001-01-01") }
  
  let(:date) { Time.new("2020-01-01") }

  let(:fiscal_year_start)  do
    { 
      month: fiscal_year.month - 1,
      day: fiscal_year.day 
    }
  end

  it "should pass all the checks" do
    model.set_start_year(fiscal_year_start)
    expect(model.fiscal_year(date)).to eq(2020)
    expect(model.fiscal_month(date)).to eq(0)
    expect(model.fiscal_quarter(date + 3.month)).to eq(2)
  end
end
