require 'spec_helper'

shared_examples_for 'fiscal' do
  let(:model) { described_class.new } 
  
  let!(:quarters) { model.quarters }
  context "should pass all checks for quarters" do
    it "should verify 4 quarters in a year" do
      expect(quarters.length).to eq(4)      
    end

    it "should verify months in all quarters" do
      expect(quarters[0]).to eql([1, 2, 3])
      expect(quarters[1]).to eql([4, 5, 6])
      expect(quarters[2]).to eql([7, 8, 9]) 
      expect(quarters[3]).to eql([10, 11, 12])
    end
  end
  
  context "should pass all checks for date range" do
    it "should verify date range from start to end of year" do
      date_range = model.date_range
      expect(date_range).to eql(Date.today.all_year)
    end
    
  end
  
end
