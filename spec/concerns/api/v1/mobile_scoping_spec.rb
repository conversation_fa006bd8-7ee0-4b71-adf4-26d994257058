require 'rails_helper'

include CompanyUserHelper

RSpec.describe Api::V1::MobileScoping, type: :module do
  include Api::V1::MobileScoping

  create_company_and_user

  let(:device_token) { 'some_device_token' }

  describe '#login_response', :skip_login do
    it 'returns a hash with the correct keys and values' do
      token = 'abc123'
      response = login_response(company_user, company, workspace, user, device_token, token)

      expect(response[:token]).to eq(token)
      expect(response[:multiple]).to eq(false)
      expect(response[:current_company][:id]).to eq(company.id)
      expect(response[:company_user][:id]).to eq(company_user.id)
      expect(response[:current_workspace][:id]).to eq(workspace.id)

      expect(response[:is_mfa_verified]).to be true
      expect(response[:is_company_agent]).to be true
      expect(response[:is_email_verified]).to be true
      expect(response[:is_ensure_free_trial_or_subscribed]).to be true
    end
  end

  describe '#scoped_mobile_company_user', :skip_login do
    it 'returns the correct company user' do
      scoped_company_user = scoped_mobile_company_user(company, user)

      expect(scoped_company_user).to eq(company_user)
    end
  end

  describe '#all_expanded_privileges', :skip_login do
    context 'when the company user has a contributor ID' do
      it 'returns all the expanded privileges associated with the contributor' do
        response = all_expanded_privileges(company, user)

        expect(response).to be_present
        expect(response.size).to eq(7)
      end
    end

    context 'when the company user does not have a contributor ID' do
      it 'returns an empty relation' do
        company_user.update(contributor: nil)

        expect(all_expanded_privileges(company, user)).to be_empty
      end
    end
  end

  describe "#is_company_agent", :skip_login do
    context "when current user is super admin" do
      it "returns true" do
        user.update(super_admin: true)

        expect(is_company_agent(company_user, company, user)).to be_truthy
      end
    end

    context "when company user is a member of Admins group" do
      it "returns true" do
        expect(is_company_agent(company_user, company, user)).to be_truthy
      end
    end

    context "when company user is a member of Help Desk Agent group" do
      it "returns true" do
        company_groups = company.groups

        company_groups.find_by(name: "Admins").group_members.find_by(contributor_id: company_user.contributor_id).destroy
        company_groups.find_by(name: "Help Desk Agents").group_members.create(contributor_id: company_user.contributor_id)

        expect(is_company_agent(company_user, company, user)).to be_truthy
      end
    end

    context "when company user is not a member of Help Desk Agents or Admins group" do
      it "returns false" do
        company.groups.find_by(name: "Admins").group_members.find_by(contributor_id: company_user.contributor_id).destroy

        expect(is_company_agent(company_user, company, user)).to be_falsey
      end
    end
  end

  describe "#mfa_verification_required", :skip_login do
    context "with device token" do
      it "returns false" do
        expect(mfa_verification_required(company, user, device_token)).to be_falsey
      end
    end
  end

  describe "#is_mfa_verified", :skip_login do
    context "when mfa is not enabled for the company" do
      it "returns true" do
        allow_any_instance_of(Company).to receive(:mfa_enabled).and_return(false)

        expect(is_mfa_verified(company, company_user, user, device_token)).to be_truthy
      end
    end

    context "when mfa is enabled for the company and verification is not required" do
      it "returns true" do
        allow_any_instance_of(Company).to receive(:mfa_enabled).and_return(true)
        allow(self).to receive(:mfa_verification_required).and_return(false)

        expect(is_mfa_verified(company, company_user, user, device_token)).to be_truthy
      end
    end

    context "when mfa is enabled for the company and verification is required" do
      before do
        allow_any_instance_of(Company).to receive(:mfa_enabled).and_return(true)
        allow(self).to receive(:mfa_verification_required).and_return(true)
      end

      context "when the user is a super admin" do
        it "returns true" do
          user.update(super_admin: true)

          expect(is_mfa_verified(company, company_user, user, device_token)).to be_truthy
        end
      end

      context "when the user is not a super admin" do
        it "returns false" do
          expect(is_mfa_verified(company, company_user, user, device_token)).to eq(false)
        end
      end
    end
  end

  describe '#is_email_verified', :skip_login do
    context 'when the user is not present' do
      it 'returns true' do
        expect(is_email_verified(nil)).to eq(true)
      end
    end

    context 'when the user has not confirmed their email within the allowed time period' do
      it 'returns false' do
        allow(user).to receive(:has_confirmed_email?).and_return(false)
        allow(user).to receive(:created_at).and_return(6.days.ago)

        expect(is_email_verified(user)).to eq(false)
      end
    end

    context 'when the user has confirmed their email within the allowed time period' do
      it 'returns true' do
        allow(user).to receive(:has_confirmed_email?).and_return(true)
        allow(user).to receive(:created_at).and_return(3.days.ago)

        expect(is_email_verified(user)).to eq(true)
      end
    end
  end

  describe '#is_ensure_free_trial_or_subscribed', :skip_login do
    context 'when current_company or current_user is blank' do
      it 'returns true' do
        expect(is_ensure_free_trial_or_subscribed(nil, user)).to be true
        expect(is_ensure_free_trial_or_subscribed(company, nil)).to be true
      end
    end

    context 'when current_user is a super admin' do
      it 'returns true' do
        user.update(super_admin: true)
        expect(is_ensure_free_trial_or_subscribed(company, user)).to be true
      end
    end

    context 'when current_company is not a sample company and allow_access? is true' do
      it 'returns true' do
        expect(is_ensure_free_trial_or_subscribed(company, user)).to be true
      end
    end

    context 'when current_company is a sample company and current_user has an active subscription' do
      it 'returns true' do
        company.update(is_sample_company: true)
        allow_any_instance_of(User).to receive(:has_active_subscription?).and_return(true)

        expect(is_ensure_free_trial_or_subscribed(company, user)).to be true
      end
    end

    context 'when current_company is a sample company and current_user does not have an active subscription' do
      it 'returns false' do
        company.update(is_sample_company: true)

        expect(is_ensure_free_trial_or_subscribed(company, user)).to be_falsey
      end
    end
  end
end
