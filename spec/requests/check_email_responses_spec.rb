require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

RSpec.describe "help ticket email responses", :type => :request do
  create_company_and_user(subdomain: "test", email: "<EMAIL>")

  class Aws::S3::Client
    def get_object(hash)
      key = hash[:key]
      if key =~ /\/(.+)$/
        message_id = $1
        if File.exist?(Rails.root.join("spec", "data", message_id))
          OpenStruct.new(body: File.open(Rails.root.join("spec", "data", message_id)))
        else
          OpenStruct.new(body: "this is data")
        end
      end
    end
  end

  describe "when creating help tickets via email"
    context "with subsequent direct replies" do
      let(:message_id_1) { "09je755o5vd1ma0oio8n1of85v78g36seej3a2o1" }
      let(:message_id_2) { "6b5a49q29m3vq0p0an8prtd5ce1c8g4ntj0l4r01" }
      let(:message_id_3) { "b7ham8pq2v88hm5du4dhvj2vhlm2lbjpiqj3a2o1" }
      let(:from) { "<EMAIL>" }

      it "creates only one ticket with two comments" do
        headers = { "ACCEPT" => "application/json" }
        response = InboundEmailWorker.new.perform(message_id_1, from)

        ticket = HelpTicket.find_by(message_id: 'CA+MGk8RRQ8Af65scAxT=<EMAIL>')
        expect(ticket).to be_present
        company = Company.find_by(subdomain: 'test')

        expect(response[:status]).to eq(:ok)
        response[:json] == {}
        subject = ticket.custom_form.custom_form_fields.find_by(name: "subject")
        expect(ticket).to be_present
        expect(ticket.message_id).to eq('CA+MGk8RRQ8Af65scAxT=<EMAIL>')
        expect(ticket.custom_form_values.find_by(custom_form_field_id: subject.id).value_str).to eq("My Mouse is too small.")
        expect(ticket.company).to eq(company)

        params = { message_id:  message_id_2 }
        response = InboundEmailWorker.new.perform(message_id_2, from)

        comment = HelpTicketComment.find_by(message_id: 'CA+MGk8QzfAcjfpE0JqkR0OTx6B3LZagieX-J0BOr6j=L=<EMAIL>')
        expect(comment).to be_present
        expect(comment.help_ticket).to eq(ticket)
        company = Company.find_by(subdomain: 'test')
        expect(comment.help_ticket.company).to eq(company)
        comment_body = "<!DOCTYPE html PUBLIC \"-//W3C//DTD HTML 4.0 Transitional//EN\" \"http://www.w3.org/TR/REC-html40/loose.dtd\">\n<html><body>\n<div dir=\"ltr\">\n<br><div>Really? Is this really an issue?</div>\n</div>\n<br><div class=\"gmail_quote\">\n<div dir=\"ltr\" class=\"gmail_attr\">On Mon, Jun 14, 2021 at 10:19 AM Bob Lasch &lt;<a href=\"mailto:<EMAIL>\"><EMAIL></a>&gt; wrote:<br>\n</div>\n<blockquote class=\"gmail_quote\" style=\"margin:0px 0px 0px 0.8ex;border-left:1px solid rgb(204,204,204);padding-left:1ex\">\n<div dir=\"ltr\">It's hard to use.</div> </blockquote>\n</div>\n</body></html>\n"
        expect(comment.comment_body).to eq(comment_body)

        response = InboundEmailWorker.new.perform(message_id_3, from)

        comment = HelpTicketComment.find_by(message_id: '<EMAIL>')
        expect(comment).to be_present
        expect(comment.help_ticket).to eq(ticket)
        company = Company.find_by(subdomain: 'test')
        expect(comment.help_ticket.company).to eq(company)
	      comment_body = "<!DOCTYPE html PUBLIC \"-//W3C//DTD HTML 4.0 Transitional//EN\" \"http://www.w3.org/TR/REC-html40/loose.dtd\">\n<html><body>\n<div dir=\"ltr\">\n<br><div>I can't believe we're talking about this.</div>\n</div>\n<br><div class=\"gmail_quote\">\n<div dir=\"ltr\" class=\"gmail_attr\">On Mon, Jun 14, 2021 at 10:20 AM Bob Lasch &lt;<a href=\"mailto:<EMAIL>\"><EMAIL></a>&gt; wrote:<br>\n</div>\n<blockquote class=\"gmail_quote\" style=\"margin:0px 0px 0px 0.8ex;border-left:1px solid rgb(204,204,204);padding-left:1ex\">\n<div dir=\"ltr\">\n<br><div>Really? Is this really an issue?</div>\n</div>\n<br><div class=\"gmail_quote\">\n<div dir=\"ltr\" class=\"gmail_attr\">On Mon, Jun 14, 2021 at 10:19 AM Bob Lasch &lt;<a href=\"mailto:<EMAIL>\"><EMAIL></a>&gt; wrote:<br>\n</div>\n<blockquote class=\"gmail_quote\" style=\"margin:0px 0px 0px 0.8ex;border-left:1px solid rgb(204,204,204);padding-left:1ex\">\n<div dir=\"ltr\">It's hard to use.</div> </blockquote>\n</div> </blockquote>\n</div>\n</body></html>\n"
        expect(comment.comment_body).to eq(comment_body)
      end
    end
end
