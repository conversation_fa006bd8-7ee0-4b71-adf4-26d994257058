# This file is copied to spec/ when you run 'rails generate rspec:install'
ENV["RAILS_ENV"] ||= 'test'
require File.expand_path("../../config/environment", __FILE__)
require 'rspec/rails'
require 'devise'
require 'database_cleaner'
require 'webmock/rspec'

Dir[Rails.root.join("spec/support/**/*.rb")].each { |f| require f }

ActiveRecord::Migration.maintain_test_schema!

DatabaseCleaner.strategy = :truncation

WebMock.disable_net_connect!(allow_localhost: true)

RSpec.configure do |config|
  config.after(:suite) { DatabaseCleaner.clean }
  config.fixture_path = "#{::Rails.root}/spec/fixtures"
  config.include Devise::Test::ControllerHelpers, type: :controller
  config.infer_base_class_for_anonymous_controllers = false
  config.infer_spec_type_from_file_location!
  config.order = "random"
  config.use_transactional_fixtures = true
  config.extend CompanyUserHelper
  config.include LoginHelper
  config.before(:each) do
    stub_request(:get, /api.github.com/).
      with(headers: {'Accept'=>'*/*', 'User-Agent'=>'Ruby'}).
      to_return(status: :ok, body: "stubbed response", headers: {})
  end
  config.before(:each) do
    stub_request(:post, "https://ibase.lenovo.com/POIRequest.aspx").
      with(body: {"xml"=>"<wiInputForm source='ibase'><id>LSC3</id><pw>IBA4LSC3</pw><product>59421810</product><serial>CB32825183</serial><wiOptions><machine/><parts/><service/><upma/><entitle/></wiOptions></wiInputForm>"},
           headers: {'Accept'=>'*/*', 'Accept-Encoding'=>'gzip, deflate', 'Content-Length'=>'198', 'Content-Type'=>'application/x-www-form-urlencoded', 'Host'=>'ibase.lenovo.com'}).
      to_return(status: :ok, body: "<wiOutputForm><warrantyInfo><serviceInfo><wed>2015-12-08</wed></serviceInfo></warrantyInfo></wiOutputForm>", headers: {})
  end
  config.before(:each) do
    stub_request(:post, "https://css.api.hp.com/oauth/v1/token").
      with(body: {"apiKey"=>"", "apiSecret"=>"", "grantType"=>"client_credentials", "scope"=>"warranty"},
           headers: {'Accept'=>'application/json', 'Accept-Encoding'=>'gzip, deflate', 'Content-Length'=>'62', 'Content-Type'=>'application/x-www-form-urlencoded', 'Host'=>'css.api.hp.com'}).
      to_return(status: :ok, body: '{"access_token": "GzxcAZB2wxPAyMDmcBap4sTyxm53", "expires_in": "3599", "scope": "warranty"}', headers: {})
  end
  config.before(:each) do
    stub_request(:post, "https://css.api.hp.com/productWarranty/v1/queries").
      with(body: "[{'sn': '5CD6171GHT','pn': 'W8H92PA'}]",
           headers: {'Accept'=>'application/json', 'Accept-Encoding'=>'gzip, deflate', 'Authorization'=>'Bearer GzxcAZB2wxPAyMDmcBap4sTyxm53', 'Content-Length'=>'38', 'Content-Type'=>'application/json', 'Host'=>'css.api.hp.com'}).
      to_return(status: :ok, body: '{ "sn": "5CD6171GHT", "pn": "W8H92PA", "product": null, "serviceType": null, "type": null, "status": "Expired", "startDate": "0001-01-01T00:00:00", "endDate": "2015-12-08T00:00:00", "serviceLevel": null}', headers: {})
  end
  config.before(:each) do
    stub_request(:get, "https://sandbox.api.dell.com/support/assetinfo/v4/getassetwarranty/BHCLH22?apikey=").
        with(headers: {'Accept'=>'*/*', 'Accept-Encoding'=>'gzip, deflate', 'Host'=>'sandbox.api.dell.com'}).
        to_return(status: :ok, body: '{"AssetWarrantyResponse": [{  "AssetEntitlementData": [{ "EndDate": "2018-04-05T18:59:59" }] }] }', headers: {})
  end
end
