require 'rails_helper'
include CompanyUserHelper

describe CompanyAssetStatus do
  create_company_and_user

  let(:asset_status_1) { CompanyAssetStatus.create!(name: 'Donated', icon: '&times;', company: company) }
  let!(:asset_status_2) { CompanyAssetStatus.create!(name: 'Disposed', icon: '&times;', company: company) }
  let!(:asset_status_3) { CompanyAssetStatus.create!(name: 'For Sale', icon: 'genuicon-exclamation', company: company) }

  context "for validation of name under scope of one company" do
    it "should validate presence of name" do
      asset_status_1.name = nil
      asset_status_1.valid?
      expect(asset_status_1.errors[:name][0]).to eq "can't be blank"
    end

    it "should validate presence of icon" do
      asset_status_1.icon = nil
      asset_status_1.valid?
      expect(asset_status_1.errors[:icon][0]).to eq "can't be blank"
    end

    it "should validate uniqueness of name" do
      asset_status_2.name = 'In Use'
      asset_status_2.valid?
      expect(asset_status_2.errors[:name][0]).to eq "has already been taken"
    end
  end

  context "for validation of name and icon under scope of all companies" do
    it "should create status with same name and icon of status of different company" do
      company_2 = FactoryBot.create(:company, subdomain: 'housefixers')
      expect(CompanyAssetStatus.find_by(name: 'Disposed', icon: '&times;', company: company)).to be_present
      expect(CompanyAssetStatus.create!(name: 'Disposed', icon: '&times;', company: company_2)).to be_present
    end
  end
end
