require 'rails_helper'

RSpec.describe BuildDataSet, type: :model do
  let(:company) { create(:company) }
  let(:company_build) { create(:company_build, company: company) }
  let!(:build_data_set) { create(:build_data_set, company_build: company_build) }

  describe 'associations' do
    it { is_expected.to belong_to(:company_build) }
  end

  describe 'validations' do
    it 'validates uniqueness of company_build_id' do
      duplicate_build_data_set = BuildDataSet.new(company_build: company_build)
      expect(duplicate_build_data_set).not_to be_valid
      expect(duplicate_build_data_set.errors[:company_build_id]).to include('has already been taken')
    end
  end

  describe 'delegations' do
    it 'delegates #company to company_build' do
      expect(build_data_set.company).to eq(company)
    end
  end
end
