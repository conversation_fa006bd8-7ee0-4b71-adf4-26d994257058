require 'rails_helper'
include CompanyUserHelper

describe CustomReport do
  create_company_and_user

  let(:attachment_pdf) { Rack::Test::UploadedFile.new('spec/data/laptop.pdf', 'application/pdf') }
  let!(:custom_report) { build(:custom_report, report: attachment_pdf, company_id: company.id, creator_id: company_user.id, workspace_id: company.default_workspace.id) }

  describe 'is created' do
    it 'will return encrypted and expirable url' do
      allow(custom_report.report).to receive(:url).and_return(
        'https://gogenuity-test.s3.amazonaws.com/custom_report/wqfgujgflpghzhrugbthjzku144n/laptop.pdf?
        response-content-disposition=inline%3B%20filename%3D%22laptop.pdf%22%3B%20filename%2A%3DUTF-8%27%27laptop.pdf&response-content-type=image%2Fjpeg&
        X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=mock_amz_credentional%2F20230516%2Fus-east-1%2Fs3%2Faws4_request&
        X-Amz-Date=20230516T164325Z&X-Amz-Expires=28800&X-Amz-SignedHeaders=host&X-Amz-Signature=59b10a9ef294fec7d0a3b1af5e0eb6c3bdb832d6bb15d8d591616d7ca7940702'
      )

      query_params = CGI.parse(URI.parse(custom_report.report.url).query)
      expect(query_params.key?('X-Amz-Signature')).to be_truthy
      expect((Time.at(query_params['X-Amz-Expires'].first.to_i).utc.strftime "%H").to_i).to eq(8)
    end
  end
end
