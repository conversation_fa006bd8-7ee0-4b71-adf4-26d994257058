require 'rails_helper'

describe CompanyAssetType do
  let!(:phone) { described_class.create(name: 'Phone') }
  let(:computer) { described_class.new(name: 'Desktop') }
  let(:router) { described_class.new(name: 'Router') }
  let(:mobile) { described_class.new(name: 'Mobile') }
  let(:printer) { described_class.new(name: 'Printer') }
  let(:other) { described_class.new(name: 'Other') }
  let(:pc) { described_class.new(name: 'PC') }
  let(:virtual_machine) { described_class.new(name: 'virtual_machine') }
  let(:image) { Rack::Test::UploadedFile.new('spec/data/laptop.jpg', 'image/jpg') }
  let!(:laptop) { described_class.new(name: 'Laptop', image: image) }

  describe 'in model' do
    it 'contains phone hadrware detail and asset fields' do
      expect(phone.hardware_detail_class).to eq(PhoneDetail)
      expect(CompanyAssetType.get_asset_fields(phone.name)[:fields].length).to eq(3)
    end

    it 'contains computer hadrware detail and asset fields' do
      expect(computer.hardware_detail_class).to eq(ComputerDetail)
      expect(CompanyAssetType.get_asset_fields(computer.name)[:fields].length).to eq(17)
    end

    it 'contains networking device hadrware detail and asset fields' do
      expect(router.hardware_detail_class).to eq(NetworkingDeviceDetail)
      expect(CompanyAssetType.get_asset_fields(router.name)[:fields].length).to eq(9)
    end
    it 'contains mobile hadrware detail and asset fields' do
      expect(mobile.hardware_detail_class).to eq(MobileDetail)
      expect(CompanyAssetType.get_asset_fields(mobile.name)[:fields].length).to eq(3)
    end
    it 'contains printer hadrware detail and asset fields' do
      expect(printer.hardware_detail_class).to eq(PrinterDetail)
      expect(CompanyAssetType.get_asset_fields(printer.name)[:fields].length).to eq(21)
    end
    it 'contains other devices detail' do
      expect(other.hardware_detail_class).to eq(OtherDetail)
      expect(CompanyAssetType.get_asset_fields(other.name)[:fields].length).to eq(2)
    end

    it 'contain other devices details which not exist in list' do
      expect(pc.hardware_detail_class).to eq(OtherDetail)
    end

    it 'will update the name into xls friendly name ' do
      expect(virtual_machine.xls_friendly_name).to eq('Virtual Machine')
    end

    it 'will return default types of comapany assets' do
      expect(CompanyAssetType.default_types.length).to eq(26)
    end

    it 'will check the name asset type name validation' do
      asset_type = described_class.new(name: 'phone')
      asset_type.should_not be_valid
      asset_type.errors[:name].should include('has already been taken')
    end
  end

  describe 'image url' do
    it 'will return encrypted and expirable url' do
      allow(laptop.image).to receive(:url).and_return(
        'https://gogenuity-test.s3.amazonaws.com/company_asset_type/wqfgujgflpghzhrugbthjzku144n/image/jpg?
        response-content-disposition=inline%3B%20filename%3D%22image/jpg%22%3B%20filename%2A%3DUTF-8%27%27image/jpg&response-content-type=image%2Fjpeg&
        X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=mock_amz_credentional%2F20230516%2Fus-east-1%2Fs3%2Faws4_request&
        X-Amz-Date=20230516T164325Z&X-Amz-Expires=28800&X-Amz-SignedHeaders=host&X-Amz-Signature=59b10a9ef294fec7d0a3b1af5e0eb6c3bdb832d6bb15d8d591616d7ca7940702'
      )

      query_params = CGI.parse(URI.parse(laptop.image.url).query)
      expect(query_params.key?('X-Amz-Signature')).to be_truthy
      expect((Time.at(query_params['X-Amz-Expires'].first.to_i).utc.strftime "%H").to_i).to eq(8)
    end
  end
end
