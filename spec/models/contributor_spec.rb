require 'rails_helper'

describe Contributor do
  let(:company) { create(:company) }
  let(:custom_form) { company.custom_forms.find_by(default: true, company_module: 'company_user') }
  let!(:company_user) { create(:company_user, company: company, custom_form: custom_form) }
  let!(:company_user2) { create(:company_user, company: company) }
  let!(:company_user3) { create(:company_user, company: company) }
  let(:group) { create(:group, company: company) }
  let!(:member) { create(:group_member, group: group, contributor: company_user2.contributor) }

  let(:group1) { create(:group, name: 'Group1', company: company) }
  let(:group2) { create(:group, name: 'Group2', company: company) }
  let!(:member1) { create(:group_member, group: group1, contributor: company_user.contributor) }
  let!(:member2) { create(:group_member, group: group2, contributor: company_user2.contributor) }
  let!(:member3) { create(:group_member, group: group1, contributor: group2.contributor) }
  let!(:member4) { create(:group_member, group: group2, contributor: group1.contributor) }
  let!(:guest) { create(:guest) }

  let!(:phone_field) { custom_form.custom_form_fields.find_by(name: "mobile_phone") }

  let!(:phone_value) { create(:custom_form_value,
                              custom_form_field: phone_field,
                              value_str: 'US,(*************',
                              custom_form: custom_form,
                              module: company_user,
                              company: company) }

  context "#contributor_ids_only_users" do
    context "with just a company user" do
      it "returns just the contributor id" do
        ids = company_user.contributor.contributor_ids_only_users
        expect(ids).to eq([company_user.contributor_id])
      end
    end

    context "with just a group and a member" do
      it "returns just the contributor id" do
        ids = group.contributor.contributor_ids_only_users
        expect(ids).to eq([company_user2.contributor_id])
      end
    end

    context "with a circuluar group" do
      it "returns just the contributor id" do
        my_group = group1.reload
        ids = my_group.contributor.contributor_ids_only_users
        expect(ids).to eq([company_user.contributor_id, company_user2.contributor_id])
      end
    end

    context 'with just a guest user' do
      it 'returns just the contributor id' do
        guest_contribotr_id = guest.contributor.contributor_ids_only_users
        expect(guest_contribotr_id).to eq([guest.contributor_id])
      end
    end
  end

  it 'will return the group name' do
    expect(group.contributor.name).to eq('Grand Poobahs')
  end

  it 'will return group member full name' do
    expect(member1.contributor.name).to eq(company_user.user.full_name)
  end

  it 'will return name of the group' do
    expect(group.contributor.first_name).to eq('Grand Poobahs')
  end

  it 'will return first name of group member' do
    expect(member1.contributor.first_name).to eq(company_user.user.first_name)
  end

  it 'will return last name of group member' do
    expect(member1.contributor.last_name).to eq(company_user.user.last_name)
  end

  it 'will check email of the group member' do
    expect(member1.contributor.email).to eq(company_user.email)
  end

  it 'will return email of group' do
    expect(group.contributor.email).to eq('Group')
  end

  it 'will check avatar of the group' do
    expect(group.contributor.avatar).to eq(nil)
  end

  it 'will check group member avatar' do
    expect(member.contributor.avatar).to eq(nil)
  end

  it 'will check the phone number' do
    expect(group.contributor.phone).to eq(nil)
  end

  it 'will check the phone number of group member' do
    expect(member1.contributor.phone).to eq(company_user.mobile_phone)
  end

  it 'will return the contributor type of group' do
    expect(group.contributor.contributor_type).to eq('Group')
  end

  it 'will return the contributor type of group member' do
    expect(member.contributor.contributor_type).to eq('CompanyUser')
  end

  it 'will check group root id should be present' do
    expect(group.contributor.root_id).to eq(group.id)
  end

  it 'will check member root id should be present' do
    expect(member.contributor.root_id).to eq(company_user2.id)
  end

  it 'will check group avatar thumb url exist or not' do
    expect(group.contributor.avatar_thumb_url).to eq(nil)
  end

  it 'will check member avatar thumb url exist or not' do
    expect(member.contributor.avatar_thumb_url).to eq(nil)
  end

  it 'will check the group members ids should be present' do
    expect(group.contributor.group_members_ids).to eq([company_user2.contributor_id])
  end

  it 'will return the field type group contributor' do
    expect(group.contributor.field_type).to eq(['people_list', 'external_user'])
  end

  it 'will return group contributor as json abbreviated' do
    expect(group.contributor.as_json_abbreviated[:name]).to eq('Grand Poobahs')
    expect(group.contributor.as_json_abbreviated.length).to eq(6)
  end
end
