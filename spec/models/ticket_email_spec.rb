require 'rails_helper'

RSpec.describe TicketEmail, type: :model do
  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
    it "should have belongs_to relationship with help_ticket" do
      assoc = described_class.reflect_on_association(:help_ticket)
      expect(assoc.macro).to eq :belongs_to
    end
    it "should have has_many relationship with help_ticket_email_attachments" do
      assoc = described_class.reflect_on_association(:ticket_email_attachments)
      expect(assoc.macro).to eq :has_many
    end
  end

  context "with name" do
    let(:email_text) { '<PERSON> <<EMAIL>>' }
    context '#to_name' do
      subject { TicketEmail.new(to: email_text) }
      it "returns the name" do
        expect(subject.to_name).to eq("John Doe")
      end
    end

    context '#from_name' do
      subject { TicketEmail.new(from: email_text) }
      it "returns the name" do
        expect(subject.from_name).to eq("<PERSON>")
      end
    end

    context '#to_email' do
      subject { TicketEmail.new(to: email_text) }
      it "returns the email" do
        expect(subject.to_email).to eq("<EMAIL>")
      end
    end

    context '#from_email' do
      subject { TicketEmail.new(from: email_text) }
      it "returns the email" do
        expect(subject.from_email).to eq("<EMAIL>")
      end
    end
  end

  context "without name" do
    let(:email_text) { '<EMAIL>' }
    context '#to_name' do
      subject { TicketEmail.new(to: email_text) }
      it "returns the name" do
        expect(subject.to_name).to be_nil
      end
    end

    context '#from_name' do
      subject { TicketEmail.new(from: email_text) }
      it "returns the name" do
        expect(subject.from_name).to be_nil
      end
    end

    context '#to_email' do
      subject { TicketEmail.new(to: email_text) }
      it "returns the email" do
        expect(subject.to_email).to eq("<EMAIL>")
      end
    end

    context '#from_email' do
      subject { TicketEmail.new(from: email_text) }
      it "returns the email" do
        expect(subject.from_email).to eq("<EMAIL>")
      end
    end
  end
end
