require 'rails_helper'

RSpec.describe DiscoveredDataService, type: :model do
  let(:company) { create(:company) }
  let(:discovered_data_service_attributes) do
  {
    as: "AS17911 Brain Telecommunication Ltd.",
    city: "Lahore",
    country: "Pakistan",
    country_code: "PK",
    isp: "Brain Telecommunication",
    lat: 31.4888,
    lon: 74.3686,
    org: nil,
    query: "**************",
    region: "PB",
    region_name: "Punjab",
    status: "success",
    timezone: "Asia/Karachi",
    zip: 54000
  }
  end

  let(:discovered_data) {
    company.discovered_data_services.create(discovered_data_service_attributes)
  }

  context 'when creating a discovered data' do
    let(:provider) {
      TelecomProvider.find_by(company_id: company.id, name: discovered_data.isp)
    }

    it "create the discovered data service" do
      expect(discovered_data).to be_present
      expect(discovered_data.id).to be_present
    end

    it "creates the associated telecom provider" do
      expect(provider).to be_present
      expect(provider.id).to be_present
      expect(provider.name).to eq(discovered_data.isp)
      expect(provider.status).to eq('discovered')
    end

    context "if we create a second discovered data service" do
      let!(:new_service) {
        company.discovered_data_services.create(discovered_data_service_attributes)
      }

      it "only creates one telecom provider" do
        expect(TelecomProvider.where(name: discovered_data.isp).count).to eq(1)
      end
    end
  end

  context 'with valid attributes' do
    it "is valid" do
      expect(discovered_data.valid?).to eq(true)
    end
  end

  it "should have belongs_to relation with company" do
    relation = described_class.reflect_on_association(:company)
    expect(relation.macro).to eq :belongs_to
  end

  context "assocations" do
    it "belongs to company_users" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
  end
end
