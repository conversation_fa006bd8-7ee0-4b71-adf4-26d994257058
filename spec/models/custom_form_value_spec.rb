require 'rails_helper'
include CompanyUserHelper

describe CustomFormValue do
  include HelpTicketHelper

  create_company_and_user

  let(:ticket) { create(:help_ticket, company: company, workspace: company.default_workspace) }
  let(:form) { company.custom_forms.find_by(company_module: "helpdesk") }
  let(:value_str) { nil }
  let(:value_int) { nil }
  let(:custom_form_value) {
    value = build(:custom_form_value,
                  company: company,
                  custom_form: form,
                  custom_form_field: field,
                  module: ticket
          )
    value.value_int = value_int
    value.value_str = value_str
    value
  }
  let!(:help_ticket_params) {
    {
      'Subject' => 'Base Ticket',
      'Status' => 'Closed',
      'Priority' => 'low',
      'Description' => 'This is a test ticket.',
      workspace: company.workspaces.first
    }
  }
  let!(:help_ticket) { create_ticket(help_ticket_params) }

  context "#value_must_be_present" do
    context "with field type attachment" do
      let(:field) { form.custom_form_fields.find_by(name: 'attachments') }

      context "with no value" do
        it "should be valid" do
          expect(custom_form_value).to be_valid
        end
      end
    end

    context "with field type status" do
      let(:field) { form.custom_form_fields.find_by(name: 'status') }

      context "with both values being nill " do
        it "should be invalid" do
          expect(custom_form_value).not_to be_valid
        end
      end

      context "with a value_str value" do
        let(:value_str) { 'Open' }

        it "should be valid" do
          expect(custom_form_value).to be_valid
        end
      end

      context "with a value_int value" do
        let(:value_int) { 1 }

        it "should be invalid" do
          expect(custom_form_value).not_to be_valid
        end
      end
    end

    context "with field type people_list" do
      let(:field) { form.custom_form_fields.find_by(name: 'assigned_to') }

      it "should be invalid" do
        expect(custom_form_value).not_to be_valid
      end

      context "with a value_str value" do
        let(:value_str) { 'Open' }
        let(:value_int) { nil }

        it "should be invalid" do
          expect(custom_form_value).not_to be_valid
        end
      end

      context "with a value_int value" do
        let(:value_str) { nil }
        let(:value_int) { company.contributors.first.id }

        it "should be valid" do
          expect(custom_form_value).to be_valid
        end
      end

      context "updates status" do
        it "updates status and sets new ticket to false for help ticket" do
          status_field = help_ticket.custom_form.custom_form_fields.find_by(name: "status")
          updated_value = help_ticket.custom_form.custom_form_values.find_by(custom_form_field_id: status_field.id)
          updated_value.value_str = "In Progress"
          updated_value.save!
          help_ticket.reload
          expect(help_ticket.status).to eq("In Progress")
          expect(help_ticket.is_new).to eq(false)
        end
      end
    end
  end
end
