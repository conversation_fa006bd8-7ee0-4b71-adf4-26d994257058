require 'rails_helper'
require 'sidekiq/testing'
require Rails.root.join "spec/concerns/fiscal_year.rb"

Sidekiq::Testing.fake!

describe Contract do
  it_should_behave_like "fiscal_year"
  let(:company_user) { create(:company_user) }
  let(:user) { company_user.user }
  let(:contract) { create(:contract,  name: 'contract1', company: company) }
  let(:company) { company_user.company }
  let(:company2) { create(:company, fiscal_year: 'Fri, 01 Jan 2021 00:00:00 UTC +00:00') }
  let(:attributes) do
    {
      name: "Internet",
      start_date: Date.today,
      end_date: Date.today+1.year,
      contract_value_amount: '124',
      contract_type: "fixed_term"
    }
  end

  let!(:open_ended_attributes) do
    {
      name: "Internet",
      start_date: Date.today,
      contract_type: "open_ended",
      contract_value_amount: '124',
      monthly_cost: '124',
      notice_period: '30 days'
    }
  end

  context "On contract creation" do
    it "should call SendEventToEventingSystemWorker" do
      Events::SendEventToEventingSystemWorker.jobs.clear
      expect(Events::SendEventToEventingSystemWorker.jobs.size).to eq(0)
      subject = company.contracts.new(attributes)
      subject.creator_id = company_user.contributor_id
      subject.alert_dates << AlertDate.new(date: Date.today + 6.months)
      subject.save

      expect(Events::SendEventToEventingSystemWorker.jobs.size).to eq(1)
    end

    it "should set the category to uncategorized if a category is not set" do
      subject = company.contracts.new(attributes)
      subject.created_by = company_user
      subject.alert_dates << AlertDate.new(date: Date.today + 6.months)
      subject.save

      uncat = company.categories.find_by(name: "Uncategorized")
      expect(subject.category_id).to eq(uncat.id)
    end

    it "should return an end date a month later than the today or start date, whichever is later" do
      subject = company.contracts.new(open_ended_attributes)
      subject.created_by = company_user
      subject.save

      expect(subject.end_date.beginning_of_day).to eq(Date.today.next_month.beginning_of_month)

      subject.start_date = Date.today + 4.months
      subject.save

      expect(subject.end_date.beginning_of_day).to eq (Date.today + 5.months).beginning_of_month
    end
  end

  context "On contract create or update" do
    it "should not create or update if alert date is after end date" do
      subject = company.contracts.new(attributes)
      subject.created_by = company_user
      subject.alert_dates << AlertDate.new(date: attributes[:end_date] + 6.months)

      expect(subject).to be_invalid
      expect(subject.errors[:alert_dates]).to be_present
      expect(subject.errors[:alert_dates][0]).to eq("should have at least one alert before the end date")
    end

    it "should not create or update if no alert date present" do
      subject = company.contracts.new(attributes)
      subject.created_by = company_user

      expect(subject).to be_invalid
      expect(subject.errors[:alert_dates]).to be_present
      expect(subject.errors[:alert_dates][0]).to eq("should have at least one alert before the end date")
    end
  end

  context 'After contract create' do
    it 'should be with start date and opend ended' do
      subject = company.contracts.new(open_ended_attributes)
      expect(subject.end_date).to eq(Date.today.next_month.beginning_of_month)
    end

    it 'will check start date and opend ended' do
      subject = company.contracts.new(open_ended_attributes)
      subject.start_date = Date.today.next_month
      subject.save!
      expect(subject.end_date).to eq(subject.start_date.next_month.beginning_of_month)
    end
    it 'has contract value amount with start date & open ended' do
      subject = company.contracts.new(open_ended_attributes)
      expect(subject.contract_value_amount).to eq(124.0)
    end
  
    it 'has contract value amount without start date & open ended' do
      subject = company.contracts.new(attributes)
      expect(subject.contract_value_amount).to eq(124.0)
    end
  
    it 'has name' do
      subject = company.contracts.new(open_ended_attributes)
      expect(subject.display_name).to be_present
    end
  
    it 'check the contract length' do
      expect(contract.contract_length).to eq(12)
    end
  
    it 'will check total value with contract value amount' do
      subject = company.contracts.new(open_ended_attributes)
      expect(subject.total_value).to eq(open_ended_attributes[:contract_value_amount].to_f)
    end
  
    it 'will check total value with monthly cost' do
      contract.contract_value_amount = 0
      contract.monthly_cost = 124
      contract.save!
      expect(contract.total_value).to eq(1488.0)
    end
  
    it 'will check contract with insights' do
      subject = company2.contracts.new(attributes)
      expect(subject.contract_with_insights[:liability]).to eq(0.0)
      expect(subject.contract_with_insights[:asset]).to eq(124.0)
      expect(subject.contract_with_insights[:replacement_calc]).to eq(0)
      expect(subject.contract_with_insights[:spend_ytd]).to eq(0.0)
      expect(subject.contract_with_insights[:spend_qtd]).to eq(0.0)
      expect(subject.contract_with_insights[:breakdown]).to eq('100 / 0')
    end
  
    it 'will check liability' do
      subject = company2.contracts.new(attributes)
      expect(subject.liability).to eq(0.0)
    end
  
    it 'will check replacement' do
      subject = company2.contracts.new(attributes)
      expect(subject.replacement).to eq(0.0)
    end
  
    it 'will check spend ytd' do
      subject = company2.contracts.new(attributes)
      expect(subject.spend_ytd).to eq(0.0)
    end
  
    it 'will check spend_qtd' do
      subject = company2.contracts.new(attributes)
      expect(subject.spend_qtd).to eq(0.0)
    end
  
    it 'will check cost_to_date' do
      subject = company2.contracts.new(attributes)
      expect(subject.cost_to_date).to eq(124.0)
    end
  
    it 'will check breakdown' do
      subject = company2.contracts.new(attributes)
      expect(subject.breakdown).to eq('100 / 0')
    end
  end
end
