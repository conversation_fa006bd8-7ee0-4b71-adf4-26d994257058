require 'rails_helper'

RSpec.describe AppliedBuildDataSet, type: :model do
  let(:company) { create(:company) }
  let(:company_user) { create(:company_user, company: company) }
  let!(:company_build) { create(:company_build, company: company) }

  subject(:applied_build_data_set) do
    create(:applied_build_data_set, company_build: company_build, build_by: company_user)
  end

  describe 'associations' do
    it { is_expected.to belong_to(:company_build) }
    it { is_expected.to belong_to(:build_by).class_name('CompanyUser') }
  end

  describe 'delegations' do
    it { is_expected.to delegate_method(:company).to(:company_build) }
  end

  describe '#elements_count' do
    it 'calculates the total count of elements across specific fields' do
      expect(applied_build_data_set.elements_count).to eq(13) 
    end
  end

  describe 'enums' do
    it 'defines the enum for build_status with correct values' do
      expect(described_class.build_statuses).to eq({ 'in_progress' => 0, 'completed' => 1 })
    end
  end

  describe 'validations' do
    it 'is valid with valid attributes' do
      expect(applied_build_data_set).to be_valid
    end
  end
end
