require 'rails_helper'

RSpec.describe ArticleTag, type: :model do
  let(:company) { create(:company) }

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end

    it "should have belongs_to relationship with article" do
      assoc = described_class.reflect_on_association(:article)
      expect(assoc.macro).to eq :belongs_to
    end
  end
end
