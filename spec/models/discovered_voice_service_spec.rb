require 'rails_helper'

RSpec.describe DiscoveredVoiceService, type: :model do
  let(:discovered_voice_service_attributes) do
    {
      carrier_type: "BUSINESS",
      carrier_name: "SAN FRANCISCO TRANSIT - BUS LI",
      carrier_mobile_network_code: "415",
      carrier_mobile_country_code: "+1",
      national_format: "(*************",
      country_code: "US"
    }
  end

  let!(:company) { create(:company) }
  let!(:discovered_voice) {
    company.discovered_voice_services.create(discovered_voice_service_attributes)
  }

  context 'when creating a discovered voice' do
    let(:provider) {
      TelecomProvider.find_by(company_id: company.id, name: discovered_voice.carrier_name)
    }

    it "create the discovered voice service" do
      expect(discovered_voice).to be_present
      expect(discovered_voice.id).to be_present
    end

    it "creates the associated telecom provider" do
      expect(provider).to be_present
      expect(provider.id).to be_present
      expect(provider.name).to eq(discovered_voice.carrier_name)
      expect(provider.status).to eq('discovered')
    end

    context "if we create a second discovered voice service" do
      let!(:new_service) {
        company.discovered_voice_services.create(discovered_voice_service_attributes)
      }

      it "only creates one telecom provider" do
        expect(TelecomProvider.where(name: discovered_voice.carrier_name).count).to eq(1)
      end
    end
  end

  context 'with valid attributes' do
    it "is valid" do
      expect(discovered_voice.valid?).to eq(true)
    end
  end

  context 'correct relation with company' do
    it "should have belongs_to relation with company" do
      relation = described_class.reflect_on_association(:company)
      expect(relation.macro).to eq :belongs_to
    end

    it "should be the last discovered_voice_services of company after saving" do
      expect(discovered_voice.company.discovered_voice_services.last.carrier_name).to eq("SAN FRANCISCO TRANSIT - BUS LI")
    end

    it "it matches the company to be the same" do
      expect(discovered_voice.company.id).to be company.id
    end
  end

  context "assocations" do
    it "belongs to company_users" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
  end
end
