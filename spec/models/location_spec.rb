require 'rails_helper'
include CompanyUserHelper

describe Location do
  create_company_and_user

  let(:attributes) do
    {
      custom_form: company.custom_forms.where(company_module: "location").last
    }
  end

  context "with valid attributes" do
    it "is valid location" do
      location = company.locations.new(attributes)
      expect(location).to be_valid
    end
  end

  context "with no attributes" do
    it "will assign default avatar url" do
      location1 = company.locations.create()
      expect(location1.default_avatar_url).present?
    end
  end
end