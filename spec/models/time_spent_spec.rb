require 'rails_helper'
include CompanyUserHelper

RSpec.describe TimeSpent, type: :model do
  include HelpTicketHelper

  create_company_and_user

  let!(:help_ticket) do
    HelpTicket.destroy_all
    create_ticket(help_ticket_param)
    HelpTicket.all.first
  end

  let(:help_ticket_param) {
    {
      'Subject' => "Greatest Moment",
      'Created By' => company_user.contributor_id,
      'Status' => 'In Progress',
      'Description' => '<div>This is a wonderful card.</div>'
    }
  }
  describe "#time_spent" do
    let(:time_spent) { FactoryBot.create(:time_spent, company_user: company_user, help_ticket: help_ticket) }

    context "with h/m format" do
      it "assigns time correctly" do
        time_spent.time_spent = '1h 15m'
        expect(time_spent.hours_spent).to eq(1)
        expect(time_spent.minutes_spent).to eq(15)

        time_spent.time_spent = '2 hours 35 minutes'
        expect(time_spent.hours_spent).to eq(2)
        expect(time_spent.minutes_spent).to eq(35)

        time_spent.time_spent = '3hrs 22 mins'
        expect(time_spent.hours_spent).to eq(3)
        expect(time_spent.minutes_spent).to eq(22)
      end

      it "flattens time correctly" do
        time_spent.time_spent = '1hrs 122 mins'
        expect(time_spent.hours_spent).to eq(3)
        expect(time_spent.minutes_spent).to eq(2)
      end
    end

    context "with only minutes using h/m format" do
      it "assigns time correctly" do
        time_spent.time_spent = '15m'
        expect(time_spent.hours_spent).to eq(0)
        expect(time_spent.minutes_spent).to eq(15)
      end

      it "flattens time correctly" do
        time_spent.time_spent = '122m'
        expect(time_spent.hours_spent).to eq(2)
        expect(time_spent.minutes_spent).to eq(2)
      end
    end

    context "with decimal format" do
      it "assigns time correctly" do
        time_spent.time_spent = '1.25'
        expect(time_spent.hours_spent).to eq(1)
        expect(time_spent.minutes_spent).to eq(15)

        time_spent.time_spent = '0.5'
        expect(time_spent.hours_spent).to eq(0)
        expect(time_spent.minutes_spent).to eq(30)
      end
    end

    context "with colon format" do
      it "assigns time correctly" do
        time_spent.time_spent = '2:25'
        expect(time_spent.hours_spent).to eq(2)
        expect(time_spent.minutes_spent).to eq(25)

        time_spent.time_spent = '0:5'
        expect(time_spent.hours_spent).to eq(0)
        expect(time_spent.minutes_spent).to eq(5)
      end
    end

    context "with only integers" do
      it "assigns time correctly" do
        time_spent.time_spent = '2'
        expect(time_spent.hours_spent).to eq(2)
        expect(time_spent.minutes_spent).to eq(0)
      end
    end
  end
end
