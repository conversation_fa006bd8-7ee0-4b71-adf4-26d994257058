require 'rails_helper'
include CompanyUserHelper

RSpec.describe CompanyBuild, type: :model do
  create_company_and_user
  let(:valid_attributes) { { name: 'Test Build', company: company } }

  describe 'validations' do
    subject { CompanyBuild.new(valid_attributes) }

    it { is_expected.to be_valid }

    it 'is not valid without a name' do
      subject.name = nil
      is_expected.not_to be_valid
    end

    it 'is not valid with a duplicate name for the same company' do
      CompanyBuild.create!(valid_attributes)
      subject.name = 'Test Build'
      is_expected.not_to be_valid
    end

    it 'is valid with a unique name for a different company' do
      other_company = create(:company)
      CompanyBuild.create!(name: 'Unique Build', company: other_company)
      subject.name = 'Unique Build'
      is_expected.to be_valid
    end

    it 'is not valid if description is too long' do
      subject.description = 'a' * 51
      is_expected.not_to be_valid
    end
  end

  describe 'associations' do
    it { should belong_to(:company) }
    it { should have_one(:build_data_set).dependent(:destroy) }
    it { should have_many(:applied_build_data_sets).dependent(:destroy) }
    it { should have_many(:msp_asset_types).class_name('Msp::Templates::AssetType').dependent(:destroy) }
    it { should have_many(:msp_categories).class_name('Msp::Templates::Category').dependent(:destroy) }
    it { should have_many(:msp_templates_documents).class_name('Msp::Templates::Document').dependent(:destroy) }
    it { should have_many(:msp_snippets).class_name('Msp::Templates::Snippet').dependent(:destroy) }
    it { should have_many(:msp_templates_custom_forms).class_name('Msp::Templates::CustomForm').dependent(:destroy) }
    it { should have_many(:msp_templates_helpdesk_faqs).class_name('Msp::Templates::HelpdeskFaq').dependent(:destroy) }
    it { should have_many(:msp_automated_tasks).class_name('Msp::Templates::AutomatedTask').dependent(:destroy) }
    it { should have_many(:msp_blocked_keywords).class_name('Msp::Templates::BlockedKeyword').dependent(:destroy) }
  end

  describe '#set_params' do
    it 'sets current_user_id and params' do
      company_build = CompanyBuild.new
      company_build.set_params(1, { action: 'create' })
      expect(company_build.current_user_id).to eq(1)
      expect(company_build.params).to eq({ action: 'create' })
    end
  end

  describe '#as_json' do
    let(:build_data_set) { BuildDataSet.create(
      asset_types: [1, 2],
      automated_tasks: [3],
      blocked_keywords: [],
      categories: [4],
      custom_forms: [],
      documents: [],
      faqs: [],
      groups: [],
      people: [],
      responses: []
    )}
    
    let(:company_build) { CompanyBuild.create(name: "Sample Company Build", company: company, build_data_set: build_data_set) }

    context 'when options include add_count' do
      it 'includes total_count and build_data_set_id' do
        allow(company_build).to receive(:msp_categories).and_return(double(count: 2))
        allow(company_build).to receive(:msp_templates_custom_forms).and_return(double(count: 3))

        result = company_build.as_json(add_count: true)

        expect(result).to include(total_count: 5)
        expect(result).to include(build_data_set_id: company_build.build_data_set.id)
      end
    end

    context 'when options do not include add_count' do
      it 'includes only build_data_set_id' do
        result = company_build.as_json

        expect(result).to include(build_data_set_id: company_build.build_data_set.id)
        expect(result).not_to include(:total_count)
      end
    end
  end

  describe "#create_build_activity" do
    let(:current_user_id) { 1 }
    let(:params) { { 'action' => 'create' } }

    before do
      subject.set_params(current_user_id, params)
    end

    context "when current_user_id is present and action is 'create'" do
      it "calls create_new_build_activity on MspEventLogService" do
        event_log_service = instance_double(MspEventLogService)
        allow(MspEventLogService).to receive(:new).with(subject, current_user_id, "created").and_return(event_log_service)
        allow(event_log_service).to receive(:create_new_build_activity)

        subject.send(:create_build_activity)

        expect(MspEventLogService).to have_received(:new).with(subject, current_user_id, "created")
        expect(event_log_service).to have_received(:create_new_build_activity)
      end
    end

    context "when current_user_id is present and action is 'update'" do
      let(:params) { { 'action' => 'update' } }

      it "calls build_updated_activity on MspEventLogService" do
        event_log_service = instance_double(MspEventLogService)
        allow(MspEventLogService).to receive(:new).with(subject, current_user_id, "updated").and_return(event_log_service)
        allow(event_log_service).to receive(:build_updated_activity)

        subject.send(:create_build_activity)

        expect(MspEventLogService).to have_received(:new).with(subject, current_user_id, "updated")
        expect(event_log_service).to have_received(:build_updated_activity)
      end
    end

    context "when current_user_id is not present" do
      it "does not call MspEventLogService" do
        subject.current_user_id = nil

        expect(MspEventLogService).not_to receive(:new)

        subject.send(:create_build_activity)
      end
    end

    context "when params are missing" do
      it "does not call MspEventLogService" do
        subject.params = nil

        expect(MspEventLogService).not_to receive(:new)

        subject.send(:create_build_activity)
      end
    end
  end
end
