require 'rails_helper'
include Company<PERSON>ser<PERSON>elper

describe AnalyticsReportTemplate do
  create_company_and_user

  let(:template_1) do
    {
      'name': 'New Report',
      'description': 'This is the first report of v2.',
      'analytics_metrics': {
        'charts': [
          {
            'name': 'priority',
            'type': 'bar',
            'order': 1,
            'filters': {}
          }
        ]
      },
      'workspace_id': workspace.id,
    }
  end

  context "for validation of name and workspace" do
    it "should validate presence of name and workspace" do
      temp = AnalyticsReportTemplate.new
      temp.valid?
      expect(temp.errors[:name][0]).to eq("can't be blank")
      expect(temp.errors[:workspace][0]).to eq("can't be blank")
    end

    it "should validate uniqueness of name" do
      temp1 = AnalyticsReportTemplate.create!(template_1)
      temp2 = AnalyticsReportTemplate.new(template_1)
      temp2.valid?

      expect(temp2.errors[:name][0]).to eq 'has already been taken'
    end
  end

  context 'assocations' do
    it 'has many scheduled_reports' do
      assoc = described_class.reflect_on_association(:scheduled_reports)
      expect(assoc.macro).to eq :has_many
    end

    it 'belongs to workspace and default_analytics_report_template' do
      assoc = described_class.reflect_on_association(:workspace)
      expect(assoc.macro).to eq :belongs_to

      assoc = described_class.reflect_on_association(:default_analytics_report_template)
      expect(assoc.macro).to eq :belongs_to
    end
  end
end
