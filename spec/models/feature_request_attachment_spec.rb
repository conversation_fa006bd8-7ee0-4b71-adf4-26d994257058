require 'rails_helper'
include CompanyUserHelper

describe FeatureRequestAttachment do
  create_company_and_user

  let(:attached_file) { fixture_file_upload(File.open(Rails.root.join("README.md"))) }
  let(:feature_request) { create(:feature_request, creator_id: company_user.id, feature_state: 'recent') }
  let!(:feature_request_attachment) { build(:feature_request_attachment, attachment: attached_file, company_id: company.id, creator_id: company_user.id, feature_request_id: feature_request.id) }

  describe 'attachment upload' do
    it 'will return encrypted and expirable url' do
      allow(feature_request_attachment.attachment).to receive(:url).and_return(
        'https://gogenuity-test.s3.amazonaws.com/feature_request_attachment/wqfgujgflpghzhrugbthjzku144n/README.md?
        response-content-disposition=inline%3B%20filename%3D%22README.md%22%3B%20filename%2A%3DUTF-8%27%27README.md&response-content-type=image%2Fjpeg&
        X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=mock_amz_credentional%2F20230516%2Fus-east-1%2Fs3%2Faws4_request&
        X-Amz-Date=20230516T164325Z&X-Amz-Expires=28800&X-Amz-SignedHeaders=host&X-Amz-Signature=59b10a9ef294fec7d0a3b1af5e0eb6c3bdb832d6bb15d8d591616d7ca7940702'
      )

      query_params = CGI.parse(URI.parse(feature_request_attachment.attachment.url).query)
      expect(query_params.key?('X-Amz-Signature')).to be_truthy
      expect((Time.at(query_params['X-Amz-Expires'].first.to_i).utc.strftime "%H").to_i).to eq(8)
    end
  end
end
