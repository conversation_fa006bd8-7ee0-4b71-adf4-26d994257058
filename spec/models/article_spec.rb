require 'rails_helper'

RSpec.describe Article, type: :model do
  let(:company) { FactoryBot.create(:company) }
  let(:workspace) { company.workspaces.first }

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end

    it "should have belongs_to relationship with workspace" do
      assoc = described_class.reflect_on_association(:workspace)
      expect(assoc.macro).to eq :belongs_to
    end

    it "should have has_many relationship with article documents" do
      assoc = described_class.reflect_on_association(:article_documents)
      expect(assoc.macro).to eq :has_many
    end

    it "should have has_many relationship with library documents" do
      assoc = described_class.reflect_on_association(:library_documents)
      expect(assoc.macro).to eq :has_many
    end

    it "should have has_many relationship with article tags" do
      assoc = described_class.reflect_on_association(:article_tags)
      expect(assoc.macro).to eq :has_many
    end

    it "should have has_many relationship with article privileges" do
      assoc = described_class.reflect_on_association(:article_privileges)
      expect(assoc.macro).to eq :has_many
    end
  end

  describe 'validations' do
    it 'creates article title' do
      article = described_class.new()
      article.title = 'Test'
      expect(article).to be_invalid
    end

    it 'create article slug' do
      article = described_class.new()
      article.slug = 'Test'
      expect(article).to be_invalid
    end

    it 'create article body' do
      article = described_class.new()
      article.body = ''
      expect(article).to be_invalid
    end
  end
end
