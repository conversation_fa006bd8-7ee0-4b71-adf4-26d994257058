require 'rails_helper'

RSpec.describe Snippet, type: :model do
  let(:company) { FactoryBot.create(:company) }

  before(:all) do
    Rails.application.load_seed
  end

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
  end

  describe 'validations' do
    it { should validate_presence_of(:title) }
    it { should validate_presence_of(:description) }
  end
end
