require 'rails_helper'

RSpec.describe HelpTicketActivity, type: :model do
  let(:company) { create(:company, :with_locations) }
  let(:user) { create(:user) }
  let(:company_user) { create(:company_user, user: user, company: company, helpdesk_agent: true) }
  let(:asset) { create(:managed_asset, company: company, location: company.locations.first) }

  let(:help_ticket_attributes) do
      {
        company_id: company.id,
        creator_id: company_user.id,
        managed_asset_id: asset.id
      }
  end

  let(:user2_attributes) do
    {
      first_name:   "<PERSON>",
      last_name:    "<PERSON><PERSON><PERSON>",
      email:        "<EMAIL>",
      password:     "12312312"
    }
  end

  let(:user2) { create(:user, user2_attributes) }
  let(:company_user2) { create(:company_user, user: user2, company: company, helpdesk_agent: true) }

  let(:user3_attributes) do
    {
      first_name:   "<PERSON>",
      last_name:    "Las<PERSON>",
      email:        "<EMAIL>",
      password:     "12312312"
    }
  end

  let(:user3) { create(:user, user3_attributes) }
  let(:company_user3) { create(:company_user, user: user3, company: company, helpdesk_agent: false) }

  let(:help_ticket) { create(:help_ticket, help_ticket_attributes)}
  let(:help_ticket_activity_assignment) do
    activity = HelpTicketActivity.new(
                help_ticket: help_ticket,
                owner_id: company_user2.id,
                activity_type: HelpTicketActivity.activity_types["assignment"],
                activity_action: HelpTicketActivity.activity_actions["ticket_create"],
                data: { "previous_assigned_to" => nil }
              )
  end

  let(:help_ticket_activity_status) { HelpTicketActivity.new(
                                           help_ticket: help_ticket,
                                           owner_id: company_user2.id,
                                           activity_type: HelpTicketActivity.activity_types["status"],
                                           activity_action: HelpTicketActivity.activity_actions["ticket_update"],
                                           data: { "current_assigned_to" => company_user.id.to_s, "previous_assigned_to" => nil }
                                          )}

  let(:help_ticket_activity_priority) { HelpTicketActivity.new(
                                           help_ticket: help_ticket,
                                           owner_id: company_user2.id,
                                           activity_type: HelpTicketActivity.activity_types["priority"],
                                           activity_action: HelpTicketActivity.activity_actions["ticket_update"],
                                           data: { "current_priority"=>help_ticket.priority, "previous_priority"=>"low" }
                                          )}

  let(:help_ticket_activity_note) { HelpTicketActivity.new(
                                           help_ticket: help_ticket,
                                           owner_id: company_user2.id,
                                           activity_type: HelpTicketActivity.activity_types["note"],
                                           activity_action: HelpTicketActivity.activity_actions["ticket_update"],
                                           data: { "note_body"=>"<div>Body</div>" }
                                          )}

  let(:help_ticket_activity_impacted_device) { HelpTicketActivity.new(
                                               help_ticket: help_ticket,
                                               owner_id: company_user2.id,
                                               activity_type: HelpTicketActivity.activity_types["impacted_device"],
                                               activity_action: HelpTicketActivity.activity_actions["ticket_update"],
                                               data: { "current_managed_asset"=>nil, "previous_managed_asset"=>asset.name, "current_managed_asset_id"=>nil, "previous_managed_asset_id"=>asset.id.to_s }
                                              )}

  let(:help_ticket_activity_attachment) { HelpTicketActivity.new(
                                               help_ticket: help_ticket,
                                               owner_id: company_user2.id,
                                               activity_type: HelpTicketActivity.activity_types["attachment"],
                                               activity_action: HelpTicketActivity.activity_actions["ticket_update"],
                                               data: { "current_attachment"=>nil, "previous_attachment"=>"attachment.pdf" }
                                              )}

  before do
    company_user.user_roles << company.user_roles.last
    company_user2.user_roles << company.user_roles.last
  end
end