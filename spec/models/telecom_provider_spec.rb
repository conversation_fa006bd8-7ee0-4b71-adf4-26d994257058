require 'rails_helper'

describe TelecomProvider do
  let(:company) { create(:company) }

  let(:attributes) do
      {
        name: "AT&T",
        company: company,
      }
  end

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
  end

  context 'with valid attributes' do
    let(:provider) { described_class.create(attributes) }

    it "is saved" do
      expect(provider.id).to be_present
    end

    it "also create a vendor" do
      expect(provider.vendor).to be_present
      expect(provider.vendor.name).to eq("AT&T")
      expect(provider.vendor.category).to eq(company.categories.telecom)
    end
  end
end