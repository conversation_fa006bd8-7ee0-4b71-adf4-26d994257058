require 'rails_helper'

describe Workspace do
  include HelpTicketHelper

  let(:company) { create(:company, :with_locations) }
  let(:user) { create(:user) }
  let(:company_user) { create(:company_user, user: user, company: company, helpdesk_agent: true) }
  let(:contributor) { company_user.contributor }
  let(:workspace) { company.default_workspace }

  context "#destroy" do
    let!(:help_ticket) {
      ticket_params = {
        'Subject' => "Ticket",
        'Status' => 'In Progress',
        'Created By' => [ company_user.contributor_id ],
        workspace: workspace
      }
      ticket = create_ticket(ticket_params)
      ticket
    }
    let!(:project_task) { create(:project_task, help_ticket: help_ticket) }
    let!(:time_spent) { create(:time_spent, help_ticket: help_ticket) }
    let!(:closing_survey) { ClosingSurvey.create(help_ticket: help_ticket) }
    let!(:task_assignee) { TaskAssignee.create(project_task: project_task, contributor: contributor) }
    let!(:help_ticket_comment) { HelpTicketComment.create(help_ticket: help_ticket, contributor: contributor) }
    let!(:help_ticket_activity) { HelpTicketActivity.create(help_ticket: help_ticket, owner: company_user) }
    let!(:stop_watch_timers) { StopWatchTimer.create(help_ticket: help_ticket) }
    let!(:library_document) { LibraryDocument.create(workspace: workspace) }

    context "with a lot of data" do
      it "should successfully delete everything" do
        workspace_id = workspace.id
        expect(workspace.destroy).to be_truthy
        expect(workspace.help_tickets).to be_blank
      end
    end

    context 'assocations' do
      it 'has many execution_logs' do
        assoc = described_class.reflect_on_association(:execution_logs)
        expect(assoc.macro).to eq :has_many
      end

      it 'has many analytics_report_templates' do
        assoc = described_class.reflect_on_association(:analytics_report_templates)
        expect(assoc.macro).to eq :has_many
      end
    end
  end

  describe "#create" do
    it "should successfully create quick view filters" do
      new_workspace = FactoryBot.create(:workspace)
      expect(new_workspace.quick_view_filters.count).to eq(1)
    end
  end
end
