require 'rails_helper'

RSpec.describe ArticleDocument, type: :model do
  let(:company) { FactoryBot.create(:company) }

  context 'Association' do
    it "should have belongs to relationship with article" do
      assoc = described_class.reflect_on_association(:article)
      expect(assoc.macro).to eq :belongs_to
    end

    it "should have belongs to relationship with library document" do
      assoc = described_class.reflect_on_association(:library_document)
      expect(assoc.macro).to eq :belongs_to
    end
  end
end
