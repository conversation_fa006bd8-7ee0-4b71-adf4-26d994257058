require 'rails_helper'

RSpec.describe ArticlePrivilege, type: :model do
  let(:company) { FactoryBot.create(:company) }

  context 'Association' do
    it "should have belongs to relationship with article" do
      assoc = described_class.reflect_on_association(:article)
      expect(assoc.macro).to eq :belongs_to
    end

    it "should have belongs to relationship with contributor" do
      assoc = described_class.reflect_on_association(:contributor)
      expect(assoc.macro).to eq :belongs_to
    end
  end
end
