require 'rails_helper'

RSpec.describe ProjectTask, type: :model do
  subject { create(:project_task) }
  let(:company_user) { create(:company_user) }

  context "#clear_assignees" do
    before do
      subject.task_assignees << TaskAssignee.new(contributor: company_user.contributor)
    end

    it "removes all assignees" do
      subject.clear_assignees
      expect(TaskAssignee.all).to be_blank
    end
  end
end
