require 'rails_helper'
require 'sidekiq/testing'
Sidekiq::Testing.fake!

RSpec.describe CompanyMailer, type: :model do
  let!(:company) { create(:company) }
  let(:workspace) { company.workspaces.first }
  let(:default_mailer) { DefaultMailer.find_by(description: description) }

  subject { CompanyMailer.find_by(default_mailer: default_mailer, workspace: workspace) }

  describe "#opted_in=" do
    context "with a valid mailer" do
      let(:description) { "Notify all people and groups that are related to a ticket that an agent has been assigned to the ticket" }

      it "should create an automated task" do
        expect(subject.automated_task).to be_present
      end

      it "should set opted_in" do
        expect(subject.opted_in).to be_truthy
      end
    end

    context "with a bad mailer" do
      let(:description) { "The impacted device changed" }

      it "should not create an automated task" do
        expect(subject.automated_task).to be_blank
        subject.opted_in = true
        expect(subject.automated_task).to be_blank
      end

      it "should not create an automated task" do
        expect(subject.opted_in).to be_falsey
        subject.opted_in = true
        expect(subject.opted_in).to be_falsey
      end
    end
  end
end
