require 'rails_helper'

RSpec.describe ContractContact, type: :model do
  let(:company_user) { create(:company_user) }
  let(:user) { company_user.user }

  let(:company) { company_user.company }

  let(:attributes) do
    {
      name: "Internet",
      start_date: Date.today,
      end_date: Date.today+1.year,
      contract_value_amount: "124",
      company_user_ids: [company_user.id]
    }
  end

  context "On contract creation" do
    it "should create a new contract_contact" do
      subject = company.contracts.new(attributes)
      subject.save
      expect(subject.contract_contacts.size).to eq(1)
      expect(subject.contract_contacts.first.company_user.id).to eq(company_user.id)
    end
  end
end
