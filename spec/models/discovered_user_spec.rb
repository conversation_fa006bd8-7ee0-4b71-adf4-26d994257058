require 'rails_helper'

describe User do
  let(:company) { create(:company) }

  before do
    discovered_user = DiscoveredUser.create(email: "<EMAIL>", first_name: "user", company_id: company.id)
  end

  describe "Index" do
    let(:discovered_user_params) do
      {
        email: "<EMAIL>",
        first_name: "user",
        last_name: "",
        company_id: company.id
      }
    end

    let(:invalid_discovered_user_params) do
      {
        email: "user@genuity",
        first_name: "user",
        last_name: "",
        company_id: company.id
      }
    end

    it "tests uniqueness of discovered users" do
      discovered_user = company.discovered_users.create(discovered_user_params)
      expect(company.discovered_users.count).to eq(1)
      expect(discovered_user.errors.messages[:email][0]).to eq "has already been taken"
    end

    it "tests email format validation of discovered users" do
      discovered_user = company.discovered_users.create(invalid_discovered_user_params)
      expect(discovered_user.errors.messages[:email][0]).to eq "is invalid"
    end
  end
end
