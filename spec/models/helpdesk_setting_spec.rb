require 'rails_helper'

RSpec.describe HelpdeskSetting, type: :model do
  let(:company) { FactoryBot.create(:company) }

  before(:all) do
    Rails.application.load_seed
  end

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
    it "should have belongs_to relationship with default_helpdesk_setting" do
      assoc = described_class.reflect_on_association(:default_helpdesk_setting)
      expect(assoc.macro).to eq :belongs_to
    end
  end
end
