require 'rails_helper'

RSpec.describe Logs::TicketEmailLog, type: :model do
  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end

    it "should have belongs_to relationship with helpdesk_faq" do
      assoc = described_class.reflect_on_association(:help_ticket)
      expect(assoc.macro).to eq :has_one
    end
  end
end
