require 'rails_helper'
include CompanyUserHelper

describe DefaultVendor do
  create_company_and_user

  context "On default vendor creation" do
    it "should set the deafult category to uncategorized if a default category is not set" do
      default_vendor = described_class.create(name: "Test")
      default_vendor.save

      uncat = DefaultCategory.find_by(name: "Uncategorized")
      expect(default_vendor.default_category_id).to eq(uncat.id)
    end
  end
end
