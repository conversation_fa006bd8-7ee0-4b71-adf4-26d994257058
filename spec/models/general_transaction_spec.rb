require 'rails_helper'

RSpec.describe GeneralTransaction, type: :model do
  let(:company) { create(:company) }
  let(:vendor) { create(:vendor, company: company) }

  context "On general transaction creation" do
    let(:attributes) do
      {
        vendor_id: vendor.id,
        company_id: company.id,
        transaction_date: Date.today,
        amount: 21
      }
    end

    it "should assign the vendor default tags to the transaction" do
      transaction = GeneralTransaction.new(attributes)
      transaction.save!

      expect(transaction.general_transaction_tags).to be_present
      expect(transaction.general_transaction_tags.first.tag).to eq(vendor.default_tags[0])
      expect(transaction.general_transaction_tags.first.general_transaction_id).to eq(transaction.id)
    end

    it "should be active" do
      transaction = GeneralTransaction.new(attributes)
      transaction.save!

      expect(transaction.is_active).to eq(true)
    end
  end
end