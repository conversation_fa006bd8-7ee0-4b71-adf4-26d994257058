require 'rails_helper'

RSpec.describe TaskChecklist, type: :model do
  let(:company) { FactoryBot.create(:company) }

  before(:all) do
    Rails.application.load_seed
  end

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
  end

  describe 'validations' do
    it 'validates uniqueness of name within the scope of company' do
      checklist1 = FactoryBot.create(:task_checklist, name: 'Checklist 1', company: company)
      checklist2 = FactoryBot.build(:task_checklist, name: 'Checklist 1', company: company)

      expect(checklist2).not_to be_valid
      expect(checklist2.errors[:name]).to include('has already been taken')
    end
  end
end
