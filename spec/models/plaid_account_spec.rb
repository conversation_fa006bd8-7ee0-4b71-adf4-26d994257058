require 'rails_helper'
require 'sidekiq/testing'
Sidekiq::Testing.fake!

describe PlaidAccount do
  let(:company) { create(:company, :with_locations) }

  let(:attributes) do
    {
      account_id: "KwB0ybNy1nuMyXg53",
      name: "Business Platinum Card®",
      bank_name: "Bank of America",
      company_id: company.id,
      activated: Time.now,
      mask: "0000",
      subtype: "checking",
      account_type: "credit_card",
      institution_id: "ins_3"
    }
  end

  context "assocations" do
    it "has_many general_transactions" do
      assoc = described_class.reflect_on_association(:general_transactions)
      expect(assoc.macro).to eq :has_many
    end
  end

  context 'after create' do
    it "will generate guid for account" do
      plaid_account = described_class.new(attributes)
      expect(plaid_account).to be_valid
      plaid_account.save
      expect(plaid_account.guid).not_to be_nil
    end

    it "will create api event" do
      plaid_account = described_class.new(attributes)
      expect(Logs::ApiEvent.count).to eq(0)
      plaid_account.save
      expect(Logs::ApiEvent.count).to eq(1)
    end
  end
end
