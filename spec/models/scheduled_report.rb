require 'rails_helper'
include CompanyUserHelper

describe ScheduledReport do
  create_company_and_user

  let(:template_1) do
    {
      'name': 'New Report',
      'description': 'This is the first report of v2.',
      'analytics_metrics': {
        'charts': [
          {
            'name': 'priority',
            'type': 'bar',
            'order': 1,
            'filters': {}
          }
        ]
      },
      'workspace_id': workspace.id,
    }
  end

  context 'for validation of file type and analytics report template' do
    it 'should validate presence of analytics report template' do
      report = ScheduledReport.new
      report.valid?
      expect(report.errors[:analytics_report_template][0]).to eq("can't be blank")
    end

    it 'allows setting valid file_type' do
      temp = AnalyticsReportTemplate.create!(template_1)
      scheduled_report = ScheduledReport.new(analytics_report_template: temp, file_type: 'XLSX', email_delivery_frequency: 'once_a_week')
      expect(scheduled_report).to be_valid
    end
  end

  context 'assocations' do
    it 'belongs to analytics_report_template' do
      assoc = described_class.reflect_on_association(:analytics_report_template)
      expect(assoc.macro).to eq :belongs_to
    end
  end
end
