require 'rails_helper'

RSpec.describe Integrations::Microsoft::AppUser, type: :model do
  let(:company) { FactoryBot.create(:company) }
  let(:microsoft_credential) { FactoryBot.create(:microsoft_config, company_id: company.id) }
  let(:microsoft_user) { FactoryBot.create(:microsoft_user, company_id: company.id, config_id: microsoft_credential.id) }
  let(:microsoft_app) { FactoryBot.create(:microsoft_app, company_id: company.id, config_id: microsoft_credential.id) }

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:microsoft_user)
      expect(assoc.macro).to eq :belongs_to
    end
    it "should have has_many relationship with one login apps" do
      assoc = described_class.reflect_on_association(:microsoft_app)
      expect(assoc.macro).to eq :belongs_to
    end
  end

  context "Callbacks Existence" do
      it { should callback(:create_integration_app_user).after(:commit) }
  end

  describe "#create_integration_app_user" do
    it "create similar record in  integration appusers table" do
      Integrations::Microsoft::AppUser.find_or_create_by(microsoft_app_id: microsoft_app.id,microsoft_user_id: microsoft_user.id )
      expect(Integrations::AppUser.all.count).to eq(1)
    end
  end
end
