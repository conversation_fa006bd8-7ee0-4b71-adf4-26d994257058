require 'rails_helper'

RSpec.describe Integrations::Microsoft::App, type: :model do
  let(:company) { FactoryBot.create(:company) }
  let(:microsoft_credential) { FactoryBot.create(:microsoft_config, company_id: company.id) }
  let(:microsoft_app) { FactoryBot.create(:microsoft_app, company_id: company.id, config_id: microsoft_credential.id) }

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
    it "should have has_many relationship with one login users" do
      assoc = described_class.reflect_on_association(:users)
      expect(assoc.macro).to eq :has_many
    end
  end

  context "Callbacks Existence" do
      it { should callback(:create_integration_app).after(:commit) }
  end

  describe "#create_integration_apps" do
    it "create similar record in app integration table" do
      FactoryBot.create(:microsoft_app, company_id: company.id, config_id: microsoft_credential.id)
      expect(Integrations::App.all.count).to eq(1)
    end
  end
end
