require 'rails_helper'

RSpec.describe Integrations::Microsoft::User, type: :model do
  let(:company) { FactoryBot.create(:company) }
  let(:microsoft_credential) { FactoryBot.create(:microsoft_config, company_id: company.id) }
  let(:microsoft_user) { FactoryBot.create(:microsoft_user, company_id: company.id, config_id: microsoft_credential.id) }

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
    it "should have has_many relationship with one login apps" do
      assoc = described_class.reflect_on_association(:apps)
      expect(assoc.macro).to eq :has_many
    end
  end

  context "Callbacks Existence" do
    it { should callback(:create_integration_user).after(:commit) }
  end

  describe "#create_integration_user and discovered user" do
    it "create similar record in user integration table and discovered user" do
      FactoryBot.create(:microsoft_user, company_id: company.id, config_id: microsoft_credential.id)

      expect(Integrations::User.all.count).to eq(1)
      expect(Integrations::UserSource.all.count).to eq(1)

      expect(DiscoveredUser.joins(:discovered_user_sources).where(company_id: company.id)
        .where("discovered_user_sources.source = ?", 1 ).count).to eq(0)
    end
  end
end
