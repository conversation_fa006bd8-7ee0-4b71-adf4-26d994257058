require 'rails_helper'

RSpec.describe Integrations::AzureAssets::Config, type: :model do
  let(:company) { FactoryBot.create(:company) }

  before(:all) do
    Rails.application.load_seed
  end

  describe 'validations' do
    it { expect(subject).to validate_presence_of(:token) }
  end

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
  end

  context "Callbacks Existence" do
    it { should callback(:create_comp_intg_and_execute_job).after(:commit) }
  end

  describe "#create_company_integration" do
    it "create similar record in company integration table" do
      config = Integrations::AzureAssets::Config.create(token: "access_token",
                                                        expires_in: 2321,
                                                        refresh_token: "refresh_token",
                                                        company_id: company.id)
      azure_assets = Integration.find_by(name: 'azure_assets')
      expect(CompanyIntegration.where(integration_id: azure_assets.id, company_id: company.id).count ).to eq(1)
    end
  end
end
