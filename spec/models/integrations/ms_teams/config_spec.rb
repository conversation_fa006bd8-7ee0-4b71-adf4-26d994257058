require 'rails_helper'

describe Integrations::MsTeams::Config, type: :model do
  let(:company) { create(:company) }
  before { @config = Integrations::MsTeams::Config.new(company: company) }

  subject { @config }

  before(:all) do
    Rails.application.load_seed
  end

  context 'Callbacks Existence' do
    it { should callback(:destroy_associated_automated_tasks).after(:destroy) }
    it { should callback(:unlink_ms_teams_channel).after(:commit) }
  end

  context 'Association' do
    it 'should have belongs_to relationship with company' do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
  end

  describe 'validations' do
    it { should validate_uniqueness_of(:channel_id).scoped_to(:team_id) }
    it { should validate_presence_of(:channel_id) }
    it { should validate_presence_of(:team_id) }
    it { should validate_presence_of(:company_id) }
  end

  describe '#create company Microsoft Teams integration' do
    it 'will create company Microsoft Teams integration' do
      create(:ms_teams_config, company: company)

      expect(company.ms_teams_configs.count).to eq(1)
    end
  end

  # describe '#create default automated tasks for Microsoft Teams inetgration' do
  #   it 'will create default automated tasks' do
  #     expect(company.automated_tasks.count).to eq(8)
  #     create(:ms_teams_config, company: company)
  #     expect(company.automated_tasks.count).to eq(10)
  #     expect(company.automated_tasks.joins(task_action: :action_type).where("automated_tasks_action_types.name = 'send a [Microsoft Teams message]'").count).to eq(2)
  #   end
  # end
end
