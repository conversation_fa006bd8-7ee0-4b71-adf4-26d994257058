require "rails_helper"

RSpec.describe Integrations::AwsAssets::Config, type: :model do
  let(:company) { FactoryBot.create(:company) }

  before(:all) do
    Rails.application.load_seed
  end

  describe "Validates" do
    %w(access_key secret_key regions).each do |field|
      it "presence of #{field}" do
        allow_any_instance_of(described_class).to receive(:verify_keys).and_return(true)
        should validate_presence_of(field)
      end
    end
  end

  describe "Association" do
    it "belongs to company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
  end

  describe "After commit" do
    it { should callback(:create_comp_intg_and_execute_job).after(:commit) }
  end

  describe "Company Integration" do
    it "create similar record in company integration table" do
      params = {
        access_key: "utywerutoyewrtyrewuotb4378gbwet",
        secret_key: "4y32b234b43hb",
        regions: ["us-east-1"],
        company_id: company.id,
      }
      config = Integrations::AwsAssets::Config.new(params)
      config.save!(validate: false)
      integration = Integration.find_by(name: "aws_assets")
      expect(CompanyIntegration.where(integration_id: integration.id, company_id: company.id).count).to eq(1)
    end
  end
end
