require 'rails_helper'

RSpec.describe Integrations::Aws::Config, type: :model do
  let(:company) { FactoryBot.create(:company) }

  before(:all) do
    Rails.application.load_seed
  end

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
    it "should have has_one relationship with company_integration" do
      assoc = described_class.reflect_on_association(:company_integration)
      expect(assoc.macro).to eq :has_one
    end
  end

  context "Callbacks Existence" do
    it { should callback(:create_comp_intg_and_execute_job).after(:commit) }
  end

  describe "#create_company_integration" do
    it "create similar record in company integration table" do
      allow_any_instance_of(Integrations::Aws::FetchData).to receive(:authenticate?).and_return([true, "success"])
      config = Integrations::Aws::Config.create(
                id: nil,
                access_key: "utywerutoyewrtyrewuotb4378gbwet",
                secret_key: "4y32b234b43hb",
                region: "us-east-1",
                company_id: company.id)
      aws = Integration.find_by(name: 'aws')
      expect(CompanyIntegration.where(integration_id: aws.id, company_id: company.id).count).to eq(1)
    end
  end
end
