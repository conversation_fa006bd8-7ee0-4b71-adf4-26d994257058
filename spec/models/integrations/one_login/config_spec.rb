require 'rails_helper'

RSpec.describe Integrations::OneLogin::Config, type: :model do
  let(:company) { FactoryBot.create(:company) }

  let(:valid_company_credential) do
                          {
                            client_id: Rails.application.credentials.one_login[:client_id],
                            client_secret: Rails.application.credentials.one_login[:client_secret],
                            region: 'us',
                            company_id: company.id
                          }
  end
  let(:invalid_company_credential) do
                          {
                            client_secret: 'mock-client-secret',
                            region: 'us',
                            company_id: company.id
                          }
  end
  before(:all) do
    Rails.application.load_seed
  end

  context 'with valid attributes' do
    it "is valid" do
      allow_any_instance_of(Integrations::OneLogin::FetchData).to receive(:authenticate?).and_return(true)
      company_credential = described_class.new(valid_company_credential)
      expect(company_credential).to be_valid
    end
  end

  context 'with In valid attributes' do
    it "is invalid" do
      allow_any_instance_of(Integrations::OneLogin::FetchData).to receive(:authenticate?).and_return(true)
      company_credential = described_class.new(invalid_company_credential)
      expect(company_credential).to be_invalid
    end
  end

  context 'Valid But Duplicate' do
    it "is already exist" do
      company_credential1 = described_class.create(valid_company_credential)
      company_credential2 = described_class.new(valid_company_credential)
      expect(company_credential2.valid?).to eq(false)
    end
  end

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
  end

  context "Callbacks Existence" do
    it { should callback(:create_comp_intg_and_execute_job).after(:commit) }
  end

  describe "#create_company_integration" do
    it "create similar record in company integration table" do
      config = FactoryBot.create(:one_login_config, company_id: company.id)
      one_login = Integration.find_by(name: 'one_login')
      expect(CompanyIntegration.where(integration_id: one_login.id).count ).to eq(1)
    end
  end
end
