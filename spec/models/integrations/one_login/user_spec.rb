require 'rails_helper'

RSpec.describe Integrations::OneLogin::User, type: :model do
  let(:company) { FactoryBot.create(:company) }
  let(:one_login_config) { FactoryBot.create(:one_login_config, company_id: company.id) }
  let(:one_login_user) { FactoryBot.create(:one_login_user, company_id: company.id, config_id: one_login_config.id) }

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
    it "should have has_many relationship with one login apps" do
      assoc = described_class.reflect_on_association(:apps)
      expect(assoc.macro).to eq :has_many
    end
  end

  context "Callbacks Existence" do
    it { should callback(:create_integration_user).after(:commit) }
  end

  describe "#create_integration_user" do
    it "create similar record in user integration table" do
      FactoryBot.create(:one_login_user, company_id: company.id, config_id: one_login_config.id)
      expect(Integrations::User.all.count).to eq(1)
      expect(Integrations::UserSource.all.count).to eq(1)
    end
  end
end
