require 'rails_helper'

RSpec.describe Integrations::OneLogin::AppUser, type: :model do
  let(:company) { FactoryBot.create(:company) }

  let(:one_login_config) { FactoryBot.create(:one_login_config ,company_id: company.id) }
  let(:one_login_app) {FactoryBot.create(:one_login_app, company_id: company.id, config_id: one_login_config.id)}
  let(:one_login_user) { FactoryBot.create(:one_login_user ,company_id: company.id, config_id: one_login_config.id)}

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:one_login_user)
      expect(assoc.macro).to eq :belongs_to
    end
    it "should have has_many relationship with one login apps" do
      assoc = described_class.reflect_on_association(:one_login_app)
      expect(assoc.macro).to eq :belongs_to
    end
  end

  context "Callbacks Existence" do
      it { should callback(:create_integration_app_user).after(:commit) }
  end

  describe "#create_integration_app_user" do
    it "create similar record in  integration appusers table" do
      Integrations::OneLogin::AppUser.find_or_create_by(one_login_app_id: one_login_app.id,one_login_user_id: one_login_user.id )
      expect(Integrations::AppUser.all.count).to eq(1)
    end
  end
end
