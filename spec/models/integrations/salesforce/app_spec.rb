require 'rails_helper'

RSpec.describe Integrations::Salesforce::App, type: :model do
  let(:company) { FactoryBot.create(:company) }
  let(:salesforce_credential) { FactoryBot.create(:salesforce_config, company_id: company.id) }

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
    it "should have belongs_to relationship with config" do
      assoc = described_class.reflect_on_association(:config)
      expect(assoc.macro).to eq :belongs_to
    end
  end

  context "Callbacks Existence" do
      it { should callback(:create_integration_app).after(:commit) }
  end

  describe "#create_integration_apps" do
    it "create similar record in app integration table" do
      FactoryBot.create(:salesforce_app, company_id: company.id, config_id: salesforce_credential.id)
      expect(Integrations::App.all.count).to eq(1)
    end
  end
end
