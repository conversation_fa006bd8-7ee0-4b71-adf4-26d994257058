require 'rails_helper'

RSpec.describe Integrations::Salesforce::User, type: :model do
  let(:company) { FactoryBot.create(:company) }

  let(:salesforce_credential) {FactoryBot.create(:salesforce_config, company_id: company.id) }

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
    it "should have belongs_to relationship with config" do
      assoc = described_class.reflect_on_association(:config)
      expect(assoc.macro).to eq :belongs_to
    end
  end

  context "Callbacks Existence" do
    it { should callback(:create_integration_user).after(:commit) }
  end

  describe "#create_integration_user" do
    it "create similar record in user integration table" do
      FactoryBot.create(:salesforce_user, company_id: company.id, config_id: salesforce_credential.id)
      expect(Integrations::User.all.count).to eq(1)
      expect(Integrations::UserSource.all.count).to eq(1)
    end
  end
end
