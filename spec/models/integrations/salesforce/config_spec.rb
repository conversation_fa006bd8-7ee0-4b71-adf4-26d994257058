require 'rails_helper'

RSpec.describe Integrations::Salesforce::Config, type: :model do
  let(:company) { FactoryBot.create(:company) }

  before(:all) do
    Rails.application.load_seed
  end

  describe 'validations' do
    it { should validate_presence_of(:token) }
  end

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
    it "should have has_one relationship with company_integration" do
      assoc = described_class.reflect_on_association(:company_integration)
      expect(assoc.macro).to eq :has_one
    end
    it "should have has_many relationship with salesforce" do
      assoc = described_class.reflect_on_association(:apps)
      expect(assoc.macro).to eq :has_many
    end
    it "should have has_many relationship with salesforce" do
      assoc = described_class.reflect_on_association(:users)
      expect(assoc.macro).to eq :has_many
    end
    it "should have has_many relationship with app_sources" do
      assoc = described_class.reflect_on_association(:app_sources)
      expect(assoc.macro).to eq :has_many
    end
    it "should have has_many relationship with user_sources" do
      assoc = described_class.reflect_on_association(:user_sources)
      expect(assoc.macro).to eq :has_many
    end
  end

  context "Callbacks Existence" do
    it { should callback(:create_comp_intg_and_execute_job).after(:commit) }
    #We have removed this callback because now we call this method in company_integration before_destroy callback
    #it { should callback(:destroy_integration_data).after(:destroy) }
  end

  describe "#create_company_integration" do
    it "create similar record in company integration table" do
      config = FactoryBot.create(:salesforce_config, company_id: company.id)
      salesforce = Integration.find_by(name: 'salesforce')
      expect(CompanyIntegration.where(integration_id: salesforce.id, company_id: company.id).count ).to eq(1)
    end
  end
end
