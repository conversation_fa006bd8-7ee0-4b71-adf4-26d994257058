require 'rails_helper'

RSpec.describe Integrations::Kaseya::Config, type: :model do
  let(:company) { FactoryBot.create(:company) }
  let!(:kaseya_config) { FactoryBot.create(:kaseya_config, company: company)}

  describe 'validations' do
    xit { expect(subject).to validate_presence_of(:client_id) }
    xit { expect(subject).to validate_presence_of(:client_secret) }
  end

  let(:valid_config_attributes) do
    {
      access_token: SecureRandom.hex(32),
      refresh_token: SecureRandom.hex(32),
      company_id: company.id
    }
  end

  context "Company and token should be unique" do
    xit "does not allow user to integrate the account again" do
      config1 = described_class.create(valid_config_attributes)
      config2 = described_class.new(valid_config_attributes)
      expect(config2.valid?).to eq(false)
    end
  end

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
  end

  context "Callbacks Existence" do
    it { should callback(:create_comp_intg_and_execute_job).after(:commit) }
  end

  describe "#create_company_integration" do
    it "create similar record in company integration table" do
      kaseya = Integration.find_by(name: 'kaseya')
      expect(CompanyIntegration.where(integration_id: kaseya.id, company_id: company.id).count ).to eq(1)
    end
  end
end
