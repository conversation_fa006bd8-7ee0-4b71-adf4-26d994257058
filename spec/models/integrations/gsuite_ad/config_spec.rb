require 'rails_helper'

RSpec.describe Integrations::Gsuite::Config, type: :model do
  context "assocations" do
    it "should have has_one relationship with company" do
      assoc = described_class.reflect_on_association(:company_integration)
      expect(assoc.macro).to eq :has_one
    end
  end

  context "Callbacks Existence" do
    it { should callback(:create_comp_intg_and_execute_job).after(:commit) }
  end
end
