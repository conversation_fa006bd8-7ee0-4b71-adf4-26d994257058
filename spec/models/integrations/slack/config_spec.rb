require 'rails_helper'

describe Integrations::Slack::Config, type: :model do
  let(:company) { FactoryBot.create(:company) }
  subject { Integrations::Slack::Config.new(company: company) }

  before(:all) do
    Rails.application.load_seed
  end

  context "Callbacks Existence" do
    it { should callback(:destroy_associated_automated_tasks).after(:destroy) }
  end

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
  end

  describe 'on creating Slack configuration' do
    it 'will create its default automated tasks' do
      Integrations::Slack::Config.create!(team_id: 'B09VVKA1K8Q',
                                          channel_id: 'X12KMSC971Q',
                                          company: company,
                                          workspace: company.default_workspace)

      event_types = ['{a ticket} is created', '{a comment} is added to a ticket', '{a ticket} is updated']
      slack_automated_tasks = AutomatedTasks::AutomatedTask
                                .where(workspace_id: company.workspaces.ids)
                                .joins(task_actions: :action_type)
                                .includes(task_events: :event_type)
                                .where("automated_tasks_action_types.action_class = 'SendSlackMessage'")
                                .uniq

      expect(slack_automated_tasks.length).to eq(3)
      slack_automated_tasks.each do |at|
        expect(at.task_events.count).to eq(1)
        expect(event_types).to include(at.task_events.first.event_type.name)
        event_types.delete(at.task_events.first.event_type.name)
      end
      expect(event_types.length).to eq(0)
    end
  end

  describe 'validations' do
    it { should validate_uniqueness_of(:channel_id).scoped_to(:team_id) }
    it { should validate_presence_of(:channel_id) }
    it { should validate_presence_of(:team_id) }
    it { should validate_presence_of(:company_id) }
    it { should validate_presence_of(:workspace_id) }
  end

  describe "#create company Slack integration" do
    it "will create company Slack integration" do
      Integrations::Slack::Config.create!(team_id: 'B09VVKA1K8Q',
                                          channel_id: 'X12KMSC971Q',
                                          company: company,
                                          workspace: company.default_workspace)

      expect(company.slack_configs.count).to eq(1)
    end
  end
end
