require 'rails_helper'

RSpec.describe Integrations::Xero::Config, type: :model do
  let(:company) { create(:company) }

  let(:valid_config_attributes) do
    {
      token: SecureRandom.hex(32),
      refresh_token: SecureRandom.hex(32),
      company_id: company.id
    }
  end

  let(:invalid_config_attributes) do
    { company_id: company.id }
  end

  context "compnay, key and token should be unique" do
    it "does not allow user to integrate the account again" do
      config1 = described_class.create(valid_config_attributes)
      config2 = described_class.new(valid_config_attributes)
      expect(config2.valid?).to eq(false)
    end
  end

  context "token and key should be valid" do
    it "check valid token and name of config company" do
      config = described_class.create(invalid_config_attributes)
      expect(config.valid?).to eq(false)
    end
  end

  context "assocations" do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
  end

  context "Callbacks Existence" do
    it { should callback(:create_com_intg_and_execute_job).after(:commit) }
  end
end