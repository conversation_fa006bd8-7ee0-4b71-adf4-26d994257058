require 'rails_helper'

RSpec.describe Integrations::GoogleAssets::Config, type: :model do
  let(:company) { FactoryBot.create(:company) }

  before(:all) do
    Rails.application.load_seed
  end

  describe 'validations' do
    it { expect(subject).to validate_presence_of(:projects) }
  
    it "should validate projects duplication" do
      subject.projects = ["project-1", "project-1"]
      subject.valid?
      expect(subject.errors[:base]).to match_array('Please exclude duplicate project IDs')
    end 
  end

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end

    it "should have has_one relationship with company_integration" do
      assoc = described_class.reflect_on_association(:company_integration)
      expect(assoc.macro).to eq :has_one
    end
  end

  context "Callbacks Existence" do
    it { should callback(:create_comp_intg_and_execute_job).after(:commit) }
    it { should callback(:remove_blank_entries).before(:validation) }
    it { should callback(:destroy_integration_data).before(:destroy) }
  end

  describe "#create_company_integration" do
    it "create similar record in company integration table" do
      config = FactoryBot.create(:google_assets_config, company_id: company.id)
      google_assets = Integration.find_by(name: 'google_assets')
      expect(CompanyIntegration.where(integration_id: google_assets.id, company_id: company.id).count ).to eq(1)
    end
  end
end
