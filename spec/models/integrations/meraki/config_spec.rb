require 'rails_helper'
require 'ostruct'
require 'httparty'

RSpec.describe Integrations::Meraki::Config, type: :model do
  let(:company) { FactoryBot.create(:company) }

  before(:all) do
    Rails.application.load_seed
  end

  let(:response){
    OpenStruct.new(
      {parsed_response:
        [
          {"id"=>"695243192475320747",
            "name"=>"Stratum-Genuity",
            "url"=>"https://n235.meraki.com/o/l0-zbdRd/manage/organization/overview"
          },
          {"id"=>"622923",
            "name"=>"Stratum Networks",
            "url"=>"https://n235.meraki.com/o/KTMBqc/manage/organization/overview"
          }
        ],
        code: 200,
        success?: true,
        message: "OK",
      }
    )
  }

  let(:valid_config_attributes) do
    {
      token:   "mock-token",
      name:    "Stratum Networks",
      company_id: company.id
    }
  end

  let(:invalid_config_attributes) do
    {
      token:   "mock-token",
      name:    "Stratum Wendors",
      company_id: company.id
    }
  end

  context 'with valid attributes' do
    it "is valid token" do
      allow(HTTParty).to receive(:get).and_return(response)
      obj = described_class.new(valid_config_attributes)
      expect(obj.authenticate).to eq(nil)
    end
  end

  context 'with In valid attributes' do
    it "is invalid token" do
      allow(HTTParty).to receive(:get).and_return(response)
      obj = described_class.new(invalid_config_attributes)
      obj.authenticate
      expect(obj.errors.full_messages.first).to eq("Token or Organization name is invalid")
    end
  end

  context "compnay, name and token should be unique" do
    it "does not allow user to integrate the account again" do
      allow(HTTParty).to receive(:get).and_return(response)
      obj1 = described_class.create(valid_config_attributes)
      obj2 = described_class.new(valid_config_attributes)
      expect(obj2.valid?).to eq(false)
    end
  end

  context "assocations" do
    it "has one company integration" do
      assoc = described_class.reflect_on_association(:company_integration)
      expect(assoc.macro).to eq :has_one
    end

    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
  end

  context "Callbacks Existence" do
      it { should callback(:create_comp_intg_and_execute_job).after(:commit) }
      it { should callback(:destroy_integration_data).before(:destroy) }
  end

  describe "#create_comp_intg_and_execute_job" do
    it "create similar record in company integration table" do
      allow_any_instance_of(Integrations::Meraki::FetchData).to receive(:authenticate?).and_return(true)
      config = FactoryBot.create(:meraki_config, company_id: company.id)
      meraki = Integration.find_by(name: 'meraki')
      expect(CompanyIntegration.where(integration_id: meraki.id , company_id: company.id).count ).to eq(1)
    end
  end
end
