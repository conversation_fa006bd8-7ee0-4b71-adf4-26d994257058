require 'rails_helper'

RSpec.describe Integrations::Gsuite::App, type: :model do
  context "assocations" do
    it "has many gsuite_app_users" do
      assoc = described_class.reflect_on_association(:app_users)
      expect(assoc.macro).to eq :has_many
    end

    it "has many gsuite users" do
      assoc = described_class.reflect_on_association(:users)
      expect(assoc.macro).to eq :has_many
    end

    it "should have belongs_to relationship with config" do
      assoc = described_class.reflect_on_association(:config)
      expect(assoc.macro).to eq :belongs_to
    end
  end
  context 'Validations' do
    it 'should validate uniqueness of name scoped to config_id' do
      should validate_uniqueness_of(:name).scoped_to(:config_id, :external_client_id, :app_type)
    end
  end
end
