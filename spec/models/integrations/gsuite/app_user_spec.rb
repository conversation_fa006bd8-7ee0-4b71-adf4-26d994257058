require 'rails_helper'

RSpec.describe Integrations::Gsuite::AppUser, type: :model do
  context "assocations" do
    it "should have belongs_to relationship with user" do
      assoc = described_class.reflect_on_association(:user)
      expect(assoc.macro).to eq :belongs_to
    end

    it "should have belongs_to relationship with app" do
      assoc = described_class.reflect_on_association(:app)
      expect(assoc.macro).to eq :belongs_to
    end
  end

  context "Callbacks Existence" do
    it { should callback(:create_integration_app_user).after(:commit) }
  end
end
