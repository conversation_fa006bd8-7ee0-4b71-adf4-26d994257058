require 'rails_helper'

RSpec.describe Integrations::Gsuite::User, type: :model do
  context "assocations" do
    it "has many gsuite_app_users" do
      assoc = described_class.reflect_on_association(:user_apps)
      expect(assoc.macro).to eq :has_many
    end

    it "has many apps" do
      assoc = described_class.reflect_on_association(:apps)
      expect(assoc.macro).to eq :has_many
    end

    it "should have belongs_to relationship with config" do
      assoc = described_class.reflect_on_association(:config)
      expect(assoc.macro).to eq :belongs_to
    end
  end
  describe 'validations' do
    it { should allow_value("<EMAIL>").for(:email ) }
  end
end
