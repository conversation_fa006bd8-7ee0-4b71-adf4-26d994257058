require 'rails_helper'

RSpec.describe Integrations::GoogleWorkspace::Config, type: :model do
  let(:company) { FactoryBot.create(:company) }

  let(:valid_config_attributes) do
    {
      token: SecureRandom.hex(32),
      refresh_token: SecureRandom.hex(32),
      expires_at: DateTime.now + 1.hour,
      company_id: company.id
    }
  end

  describe 'validations' do
    subject { described_class.create(valid_config_attributes) }

    it { should validate_presence_of(:token) }

    it "does not allow duplicate integration for the same company" do
      config1 = described_class.create(valid_config_attributes)
      config2 = described_class.new(valid_config_attributes)
      expect(config2.valid?).to eq(false)
      expect(config2.errors[:company_id]).to include('google workspace already integrated')
    end
  end

  describe 'associations' do
    it "belongs to company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq(:belongs_to)
    end

    it "has one company_integration" do
      assoc = described_class.reflect_on_association(:company_integration)
      expect(assoc.macro).to eq(:has_one)
    end
  end

  describe 'callbacks' do
    it { is_expected.to callback(:create_comp_intg_and_execute_job).after(:commit) }
    it { is_expected.to callback(:destroy_integration_data).before(:destroy) }
  end

  describe "#create_comp_intg_and_execute_job" do
    it "creates a company integration and triggers the sync worker" do
      create(:integration, name: 'google_workspace')

      expect {
        config = described_class.create!(valid_config_attributes)
      }.to change { CompanyIntegration.count }.by(1)

      comp_intg = CompanyIntegration.last
      expect(comp_intg.status).to be_truthy
      expect(comp_intg.sync_status).to eq("pending")
    end

    it "does not run job if skip_callbacks is true" do
      config = described_class.new(valid_config_attributes.merge(skip_callbacks: true))
      expect(Integrations::GoogleWorkspace::SyncDataWorker).not_to receive(:perform_async)
      config.save!
    end
  end

  describe "#destroy_integration_data" do
    it "destroys discovered assets with certain statuses" do
      config = described_class.create!(valid_config_attributes)
      create(:integration, name: 'google_workspace')
      create(:discovered_asset, company: company, source: :google_workspace, status: "incomplete")
      create(:discovered_asset, company: company, source: :google_workspace, status: "ignored")
      create(:discovered_asset, company: company, source: :google_workspace, status: "ready_for_import")
      create(:discovered_asset, company: company, source: :google_workspace, status: "imported") # should not be deleted

      expect {
        config.destroy
      }.to change { company.discovered_assets.google_workspace.count }.from(4).to(1)
    end
  end
end
