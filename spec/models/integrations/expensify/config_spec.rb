require 'rails_helper'

RSpec.describe Integrations::Expensify::Config, type: :model do
  let(:company) { create(:company) }
  let(:user) { FactoryBot.create(:user) }

  let(:valid_config_attributes) do
    {
      user_id: user.id,
      user_secret: SecureRandom.hex(32),
      company_id: company.id
    }
  end

  let(:invalid_config_attributes) do
    {
      user_secret: SecureRandom.hex(32),
      company_id: company.id
    }
  end

  context "company and user should be unique" do
    it "does not allow user to integrate the account again" do
      allow_any_instance_of(Integrations::Expensify::FetchData).to receive(:authenticate?).and_return(true)
      config1 = described_class.create(valid_config_attributes)
      config2 = described_class.new(valid_config_attributes)
      expect(config2.valid?).to eq(false)
    end
  end

  context "user should be valid" do
    it "check valid user of config company" do
      allow_any_instance_of(Integrations::Expensify::FetchData).to receive(:authenticate?).and_return(true)
      config = described_class.create(invalid_config_attributes)
      expect(config.valid?).to eq(false)
    end
  end

  context "assocations" do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
  end

  context "Callbacks Existence" do
    it { should callback(:create_com_intg_and_execute_job).after(:commit) }
  end

  describe "#create_company_integration" do
    it "create similar record in company integration table" do
      allow_any_instance_of(Integrations::Expensify::FetchData).to receive(:authenticate?).and_return(true)
      config = FactoryBot.create(:expensify_config, company_id: company.id)
      expensify = Integration.find_by(name: 'expensify')
      expect(CompanyIntegration.where(integration_id: expensify.id, company_id: company.id).count).to eq(1)
    end
  end
end
