require 'rails_helper'

RSpec.describe Integrations::SageAccounting::Config, type: :model do
  let(:company) { FactoryBot.create(:company) }

  before(:all) do
    Rails.application.load_seed
  end

  describe 'validations' do
    it { should validate_presence_of(:token) }
    it "validates uniqueness of company either already exist or not" do
      company_credential1 = described_class.create(company_id: company.id)
      company_credential2 = described_class.new(company_id: company.id)
      expect(company_credential2.valid?).to eq(false)
    end
  end

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
    it "should have has_one relationship with company_integration" do
      assoc = described_class.reflect_on_association(:company_integration)
      expect(assoc.macro).to eq :has_one
    end
  end

  context "Callbacks Existence" do
    it { should callback(:create_comp_intg_and_execute_job).after(:create) }
  end

  describe "#create_company_integration" do
    it "create similar record in company integration table" do
      config = FactoryBot.create(:sage_accounting_config, company_id: company.id)
      sage_accounting = Integration.find_by(name: 'sage_accounting')
      expect(CompanyIntegration.where(integration_id: sage_accounting.id, company_id: company.id).count ).to eq(1)
    end
  end
end
