require 'rails_helper'

RSpec.describe Integrations::Okta::Config, type: :model do
  let(:company) { create(:company) }

  let(:valid_config_attributes) do
    {
      token:   "mock-token",
      name:    "lorean",
      company_id: company.id
    }
  end

  let(:invalid_config_attributes) do
    {
      token:   "mock-token1",
      name:    "loreansdw",
      company_id: company.id
    }
  end

  context "compnay, name and token should be unique" do
    it "does not allow user to integrate the account again" do
      allow_any_instance_of(Integrations::Okta::FetchData).to receive(:authenticate?).and_return(true)
      config1 = described_class.create(valid_config_attributes)
      config2 = described_class.new(valid_config_attributes)
      expect(config2.valid?).to eq(false)
    end
  end

  context "token and name should be valid" do
    it "check valid token and name of config company" do
      allow_any_instance_of(Integrations::Okta::FetchData).to receive(:authenticate?).and_return(false)
      config = described_class.create(invalid_config_attributes)
      expect(config.valid?).to eq(false)
    end
  end

  context "assocations" do
    it "has many okta_users" do
      assoc = described_class.reflect_on_association(:okta_users)
      expect(assoc.macro).to eq :has_many
    end

    it "has many okta_apps" do
      assoc = described_class.reflect_on_association(:okta_apps)
      expect(assoc.macro).to eq :has_many
    end

    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
  end
end
