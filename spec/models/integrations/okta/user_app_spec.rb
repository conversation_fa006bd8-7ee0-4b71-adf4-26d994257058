require 'rails_helper'

RSpec.describe Integrations::Okta::AppUser, type: :model do
  context "assocations" do
    it "should have belongs_to relationship with okta_user" do
      assoc = described_class.reflect_on_association(:okta_user)
      expect(assoc.macro).to eq :belongs_to
    end

    it "should have belongs_to relationship with okta_app" do
      assoc = described_class.reflect_on_association(:okta_app)
      expect(assoc.macro).to eq :belongs_to
    end
  end
end
