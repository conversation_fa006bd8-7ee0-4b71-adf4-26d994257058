require 'rails_helper'

RSpec.describe Integrations::Okta::App, type: :model do
  context "assocations" do
    it "has many okta_app_users" do
      assoc = described_class.reflect_on_association(:okta_app_users)
      expect(assoc.macro).to eq :has_many
    end

    it "has many okta_users" do
      assoc = described_class.reflect_on_association(:okta_users)
      expect(assoc.macro).to eq :has_many
    end

    it "should have belongs_to relationship with okta_config" do
      assoc = described_class.reflect_on_association(:okta_config)
      expect(assoc.macro).to eq :belongs_to
    end

    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
  end
end
