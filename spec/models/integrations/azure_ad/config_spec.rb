require 'rails_helper'

RSpec.describe Integrations::AzureAd::Config, type: :model do
  let(:company) { FactoryBot.create(:company) }

  describe 'validations' do
    it { should validate_presence_of(:token) }
  end

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
  end

  context "Callbacks Existence" do
    it { should callback(:create_comp_intg_and_execute_job).after(:commit) }
    it { should callback(:delete_discovered_users).before(:destroy) }
  end

  describe "#create_company_integration" do
    it "create similar record in company integration table" do
      config = FactoryBot.create(:azure_ad_config, company_id: company.id)
      azure_ad = Integration.find_by(name: 'azure_ad')
      expect(CompanyIntegration.where(integration_id: azure_ad.id, company_id: company.id).count).to eq(1)
    end
  end
end
