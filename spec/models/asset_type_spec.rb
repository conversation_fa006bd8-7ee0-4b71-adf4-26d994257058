require 'rails_helper'

describe AssetType do
  let(:phone) { described_class.new(name: 'Phone') }
  let(:computer) { described_class.new(name: 'Desktop') }
  let(:router) { described_class.new(name: 'Router') }
  let(:mobile) { described_class.new(name: 'Mobile') }
  let(:printer) { described_class.new(name: 'Printer') }
  let(:other) { described_class.new(name: 'Other') }
  let(:empty) { described_class.new(name: '') }

  describe 'in model' do
    it 'contains phone hadrware detail and asset fields' do
      expect(phone.hardware_detail_class).to eq(PhoneDetail)
      expect(AssetType.get_asset_fields(phone.name)[:fields].length).to eq(3)
    end

    it 'contain computer hadrware detail & asset fields' do
      expect(computer.hardware_detail_class).to eq(ComputerDetail)
      expect(AssetType.get_asset_fields(computer.name)[:fields].length).to eq(17)
    end

    it 'contains networking device hadrware detail and asset fields' do
      expect(router.hardware_detail_class).to eq(NetworkingDeviceDetail)
      expect(AssetType.get_asset_fields(router.name)[:fields].length).to eq(9)
    end

    it 'contains mobile hadrware detail and asset fields' do
      expect(mobile.hardware_detail_class).to eq(MobileDetail)
      expect(AssetType.get_asset_fields(mobile.name)[:fields].length).to eq(3)
    end

    it 'contains printer hadrware detail and asset fields' do
      expect(printer.hardware_detail_class).to eq(PrinterDetail)
      expect(AssetType.get_asset_fields(printer.name)[:fields].length).to eq(21)
    end

    it 'contains other devices detail' do
      expect(other.hardware_detail_class).to eq(OtherDetail)
    end

    it 'will check asset type exist in the default managed asset list' do
      expect(empty.hardware_detail_class).to eq(nil)
    end
  end
end
