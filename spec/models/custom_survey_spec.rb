require 'rails_helper'
include CompanyUserHelper

describe CustomSurvey do  
  create_company_and_user
  
  let(:custom_survey) { CustomSurvey.create!(title: "Custom Survey Spec Test", company: company, workspace: workspace) }

  context 'for validation of title under scope of company and workspace' do
    it 'should validate presence of title' do
      custom_survey.title = nil
      custom_survey.valid?
      expect(custom_survey.errors[:title][0]).to eq 'is required'
    end

    it 'should validate uniqueness of name' do
      custom_survey_dup = CustomSurvey.new(title: custom_survey.title, company: custom_survey.company, workspace: custom_survey.workspace)
      custom_survey_dup.valid?
      expect(custom_survey_dup.errors[:title][0]).to eq 'of this survey already exists. Please choose a unique title for your survey.'
    end
  end

  context 'for validation of company and workspace' do
    it 'should validate presence of company' do
      custom_survey.company_id = nil
      custom_survey.valid?
      expect(custom_survey.errors[:company_id][0]).to eq 'is required'
    end

    it 'should validate presence of workspace' do
      custom_survey.workspace_id = nil
      custom_survey.valid?
      expect(custom_survey.errors[:workspace_id][0]).to eq 'is required'
    end
  end

  context 'for valid belongs_to associations' do
    it 'should belong to company' do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end

    it 'should belong to workspace' do
      assoc = described_class.reflect_on_association(:workspace)
      expect(assoc.macro).to eq :belongs_to
    end
  end

  context 'for invalid belongs_to associations' do
    it 'should not belong to a non-existent association' do
      assoc = described_class.reflect_on_association(:non_existent)
      expect(assoc).to be_nil
    end
  
    it 'should not have has_many association with company' do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).not_to eq :has_many
    end
  
    it 'should not have has_many association with workspace' do
      assoc = described_class.reflect_on_association(:workspace)
      expect(assoc.macro).not_to eq :has_many
    end
  end

  context 'for has_one / has_many to associations' do
    it "should have one " do
      assoc = described_class.reflect_on_association(:trigger)
      expect(assoc.macro).to eq :has_one
    end

    it "should have many " do
      assoc = described_class.reflect_on_association(:questions)
      expect(assoc.macro).to eq :has_many
    end

    it "should have many " do
      assoc = described_class.reflect_on_association(:rules)
      expect(assoc.macro).to eq :has_many
    end

    it "should have many " do
      assoc = described_class.reflect_on_association(:responses)
      expect(assoc.macro).to eq :has_many
    end
  end

  context 'for invalid has_one / has_many associations' do
    it 'should not have has_many association with trigger' do
      assoc = described_class.reflect_on_association(:trigger)
      expect(assoc.macro).not_to eq :has_many
    end
  
    it 'should not have has_one association with questions' do
      assoc = described_class.reflect_on_association(:questions)
      expect(assoc.macro).not_to eq :has_one
    end
  
    it 'should not have has_one association with rules' do
      assoc = described_class.reflect_on_association(:rules)
      expect(assoc.macro).not_to eq :has_one
    end
  
    it 'should not have has_one association with responses' do
      assoc = described_class.reflect_on_association(:responses)
      expect(assoc.macro).not_to eq :has_one
    end
  
    it 'should not belong_to a trigger' do
      assoc = described_class.reflect_on_association(:trigger)
      expect(assoc.macro).not_to eq :belongs_to
    end
  
    it 'should not belong_to a question' do
      assoc = described_class.reflect_on_association(:questions)
      expect(assoc.macro).not_to eq :belongs_to
    end
  
    it 'should not belong_to a rule' do
      assoc = described_class.reflect_on_association(:rules)
      expect(assoc.macro).not_to eq :belongs_to
    end
  
    it 'should not belong_to a response' do
      assoc = described_class.reflect_on_association(:responses)
      expect(assoc.macro).not_to eq :belongs_to
    end
  end
end
