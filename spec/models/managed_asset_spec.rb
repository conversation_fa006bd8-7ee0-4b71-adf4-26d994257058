require 'rails_helper'

describe ManagedAsset do
  let(:company) { create(:company) }
  let(:user) { create(:user) }
  let(:user1) { create(:user) }
  let(:company_user) { create(:company_user, user: user, company: company) }
  let(:company_user1) { create(:company_user, user: user1, company: company) }

  let!(:asset_type) { CompanyAssetType.create() }
  let!(:location) { Location.create() }
  let!(:managed_asset2) { create(:managed_asset, company: company, mac_addresses: ['2C:53:B3 X2:W2:L2']) }
  let!(:cost) { create(:cost) }
  let(:managed_asset) { build(:managed_asset) }
  let!(:company_asset_tag) { CompanyAssetTag.create!(name: 'Test', company_id: company.id) }
  let!(:managed_asset_tag) { create(:managed_asset_tag, managed_asset_id: managed_asset2.id, company_asset_tag_id: company_asset_tag.id) }

  let(:image) { Rack::Test::UploadedFile.new('spec/data/laptop.jpg', 'image/jpg') }
  let(:managed_asset_with_image) { build(:managed_asset, image: image) }

  let(:managed_asset_attributes) do
    {
      name: "Laptop",
      company_id: company.id,
      operating_system: "Windows 10",
      company_asset_type_id: asset_type.id,
      location_id: location.id
    }
  end

  before(:each) do
    Company.current_id = company.id
  end

  context 'with valid attributes' do
    it "is valid" do
      asset = described_class.new(managed_asset_attributes)
      expect(asset.valid?).to eq(true)
      expect(asset.archived).to eq(false)
    end
  end

  context 'with invalid name' do
    it "is invalid" do
      managed_asset = described_class.new(managed_asset_attributes)
      managed_asset.name = ""
      expect(managed_asset).to be_invalid
    end
  end

  context 'with same name' do
    it "is invalid with duplicate name" do
      managed_asset = described_class.create(managed_asset_attributes)
      managed_asset_dup = described_class.new(managed_asset_attributes)
      expect(managed_asset_dup).to be_valid
    end
  end

  context 'with same name but case sensitive' do
    it "is invalid with duplicate name either upcase or downcase" do
      managed_asset = described_class.create(managed_asset_attributes)
      managed_asset_dup = described_class.new(managed_asset_attributes)
      managed_asset_dup.name = managed_asset.name.upcase
      expect(managed_asset_dup).to be_valid
    end
  end

  context 'with same name in different companies' do
    it "is valid with duplicate name in different companies" do
      managed_asset = described_class.create(managed_asset_attributes)
      managed_asset_attributes[:company_id] = nil
      company2 = create(:company, subdomain: "google")
      managed_asset_dup = company2.managed_assets.new(managed_asset_attributes)
      expect(managed_asset_dup).to be_valid
    end
  end

  context 'with invalid impact' do
    it "is invalid impact was not from the enum" do
      managed_asset = described_class.new(managed_asset_attributes)
      expect { managed_asset.impact = :highest }.to raise_error(ArgumentError)
      .with_message(/is not a valid impact/)
    end
  end

  context 'asset type must be present' do
    it "is invalid" do
      managed_asset = described_class.new(managed_asset_attributes)
      managed_asset.company_asset_type_id = nil
      expect(managed_asset).to be_invalid
    end
  end

  context '#mac_addresses' do
    let(:asset) { build(:managed_asset, mac_addresses_text: mac_addresses_text) }

    context 'with a value' do
      let(:mac_addresses_text) { '1:3:3 2:2:2' }

      it "should set the value" do
        expect(asset.mac_addresses.size).to eq(2)
        expect(asset.mac_addresses[0]).to eq('1:3:3')
        expect(asset.mac_addresses[1]).to eq('2:2:2')
      end

      it 'validate mac addresses' do
        managed_asset2.validate_mac_addresses
        expect(managed_asset2.errors.full_messages.first).to eq('mac addresses has already been taken')
      end

      it 'validate updated mac addresses' do
        expect(managed_asset2.validate_updated_mac_addresses).to eq(nil)
      end

      it 'validate the mac addresses text' do
        expect(managed_asset2.mac_addresses_text).to eq('2C:53:B3 X2:W2:L2')
      end
    end

    context 'with a blank value' do
      let(:mac_addresses_text) { '' }

      it "should set clear value" do
        expect(asset.mac_addresses.size).to eq(0)
      end
    end

    context 'with a nil value' do
      let(:mac_addresses_text) { nil }

      it "should set clear value" do
        expect(asset.mac_addresses.size).to eq(0)
      end
    end
  end

  # scopes
  context 'with user contributor id and expired warranty' do
    it 'will get asset info as json' do
      managed_asset2.assignment_information.managed_by_contributor_id = company_user1.contributor_id
      managed_asset2.warranty_expiration = DateTime.now
      managed_asset2.save!
      managed_asset_info = managed_asset2.get_asset_info_json
      expect(managed_asset_info['cost']['managed_asset_id']).to eq(managed_asset2.id)
      expect(managed_asset_info['asset_sources_data'].find{ |asset| asset['managed_asset_id'] ==  managed_asset2.id}).to be_present
      expect(managed_asset_info['id']).to eq(managed_asset2.id)
    end
  end

  context 'with user contributor id and in warranty' do
    it 'will get asset info as json' do
      managed_asset2.assignment_information.used_by_contributor_id = company_user.contributor_id
      managed_asset2.warranty_expiration = 3.months.from_now
      managed_asset2.save!
      managed_asset_info = managed_asset2.get_asset_info_json
      expect(managed_asset_info['cost']['managed_asset_id']).to eq(managed_asset2.id)
      expect(managed_asset_info['asset_sources_data'].find{ |asset| asset['managed_asset_id'] ==  managed_asset2.id}).to be_present
      expect(managed_asset_info['id']).to eq(managed_asset2.id)
    end
  end

  it 'will destroy associations related to managed asset' do
    expect(managed_asset2.nuke.id).to eq(managed_asset2.id)
  end
  
  it 'after remove os from asset' do
    response = managed_asset2.updated_asset_sources.first.asset_data.length
    expect(response).to eq(15)
  end

  it 'convert into hash' do
    expect(managed_asset2.to_hash.length).to eq(49)
  end

  context 'after asset create' do
    it 'check the source' do
      expect(managed_asset2.sources).to eq(['manually_added'])
    end

    it 'check the brand name' do
      expect(managed_asset2.brand=('Apple')).to eq('Apple')
    end

    it 'assign default manufacturer' do
      expect(managed_asset2.assign_default_manufacturer.length).to eq(3)
    end

    it 'contain tag value' do
      expect(managed_asset2.tags.first).to eq('Test')
    end

    it 'will check build hardware detail' do
      managed_asset_detail = managed_asset2.build_hardware_detail(
        {
          processor: 'i5',
          memory: '512mb',
          hard_drive: 'Hdd',
          managed_asset_id: managed_asset2.id
        })
      expect(managed_asset_detail.hard_drive).to eq('Hdd')
    end

    it 'will get asset info without any user and manager contibutor id' do
      managed_asset_info = managed_asset2.get_asset_info_json
      expect(managed_asset_info['cost']['managed_asset_id']).to eq(managed_asset2.id)
      expect(managed_asset_info['asset_sources_data'].find{ |asset| asset['managed_asset_id'] ==  managed_asset2.id}).to be_present
      expect(managed_asset_info['id']).to eq(managed_asset2.id)
    end
  end

  it 'will check managed assets description' do
    expect(managed_asset2.notes=('Testing')).to eq('Testing')
  end

  describe "scopes" do
    it ".computers returns all assets with asset type of computers" do
      desktop_asset_type = CompanyAssetType.create!(name: "Laptop")
      managed_asset_attributes[:company_asset_type_id] = desktop_asset_type.id
      managed_asset = described_class.create!(managed_asset_attributes)
      expect(described_class.computers.last).to eq(managed_asset)
    end

    it ".phones returns all assets with asset type of phones" do
      phone_asset_type = CompanyAssetType.create!(name: "Phone")
      managed_asset_attributes[:company_asset_type_id] = phone_asset_type.id
      managed_asset = described_class.create!(managed_asset_attributes)
      expect(described_class.phones.last).to eq(managed_asset)
    end

    it ".mobiles returns all assets with asset type of mobiles" do
      mobile_asset_type = CompanyAssetType.create!(name: "Mobile")
      managed_asset_attributes[:company_asset_type_id] = mobile_asset_type.id
      managed_asset = described_class.create!(managed_asset_attributes)
      expect(described_class.mobiles.last).to eq(managed_asset)
    end
  end

  describe 'image url' do
    it 'will return encrypted and expirable url' do
      allow(managed_asset_with_image.image).to receive(:url).and_return(
        'https://gogenuity-test.s3.amazonaws.com/managed_asset/wqfgujgflpghzhrugbthjzku144n/image/jpg?
        response-content-disposition=inline%3B%20filename%3D%22image/jpg%22%3B%20filename%2A%3DUTF-8%27%27image/jpg&response-content-type=image%2Fjpeg&
        X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=mock_amz_credentional%2F20230516%2Fus-east-1%2Fs3%2Faws4_request&
        X-Amz-Date=20230516T164325Z&X-Amz-Expires=28800&X-Amz-SignedHeaders=host&X-Amz-Signature=59b10a9ef294fec7d0a3b1af5e0eb6c3bdb832d6bb15d8d591616d7ca7940702'
      )

      query_params = CGI.parse(URI.parse(managed_asset_with_image.image.url).query)
      expect(query_params.key?('X-Amz-Signature')).to be_truthy
      expect((Time.at(query_params['X-Amz-Expires'].first.to_i).utc.strftime "%H").to_i).to eq(8)
    end
  end
end
