require 'rails_helper'
include CompanyUserHelper

describe Department do
  create_company_and_user
  let(:dept_1) { Department.create!(name: 'Customer Support', company: company) }

  context 'for validation of name under scope of one company' do
    it 'should validate presence of name' do
      dept_1.name = nil
      dept_1.valid?
      expect(dept_1.errors[:name][0]).to eq 'is too short (minimum is 1 character)'
    end

    it 'should validate uniqueness of name' do
      dept = dept_1.dup
      dept.valid?
      expect(dept.errors[:name][0]).to eq('has already been taken')
    end
  end

  context 'associations' do
    it 'belongs to company' do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end
  end
end
