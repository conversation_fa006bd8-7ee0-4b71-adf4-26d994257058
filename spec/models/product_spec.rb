require 'rails_helper'

RSpec.describe Product, type: :model do
  let(:vendor) { create(:vendor) }

  subject {
    described_class.new(
      name: "Foobar Prod",
      company_id: vendor.company_id,
      vendor: vendor
    )
  }

  let(:company) { subject.company }

  let(:general_transaction_a) { create(:general_transaction, amount: 100.0, product: subject, vendor: vendor, company: vendor.company, transaction_date: DateTime.now.last_month) }
  let(:general_transaction_b) { create(:general_transaction, amount: 200.0, product: subject, vendor: vendor, company: vendor.company, transaction_date: DateTime.now) }

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end

    it "should have belongs_to relationship with vendor" do
      assoc = described_class.reflect_on_association(:vendor)
      expect(assoc.macro).to eq :belongs_to
    end

    it "should have has_many relationship with one login apps" do
      assoc = described_class.reflect_on_association(:apps)
      expect(assoc.macro).to eq :has_many
    end

    it "should have has_many relationship with one login general_transactions" do
      assoc = described_class.reflect_on_association(:general_transactions)
      expect(assoc.macro).to eq :has_many
    end

    it "should have has_many relationship with managed assets" do
      assoc = described_class.reflect_on_association(:managed_assets)
      expect(assoc.macro).to eq :has_many
    end

    it "should have belongs_to relationship with default_product" do
      assoc = described_class.reflect_on_association(:default_product)
      expect(assoc.macro).to eq :belongs_to
    end

    it "should have has_one relationship to a contract" do
      expect(subject).to have_one(:contract).dependent(:nullify)
    end
  end

  context 'as_json' do
    let(:default_product) { create(:default_product) }

    it "should include logo_url from default product" do
      subject.default_product = default_product
      expect(subject.as_json["logo_url"]).to eq default_product.logo_url
    end
  end

  context '#is_base_product' do
    it "should return false when not a base product" do
      vendor.save!
      subject.save!
      expect(subject.is_base_product).to eq false
    end

    it "should return true when a base product" do
      subject.is_base_product = true
      subject.save!
      expect(subject.is_base_product).to eq true
    end
  end

  context '#last_transaction_cost' do
    it "should return the most recent transaction amount" do
      subject.general_transactions << general_transaction_a
      subject.save!
      expect(subject.last_transaction_cost).to eq 100.0
    end
  end

  context '#transactions_count' do
    it "should return total number of transactions" do
      subject.general_transactions << general_transaction_a
      subject.general_transactions << general_transaction_b
      expect(subject.transactions_count).to eq 2
    end
  end

  context '#total_spend' do
    it "should return the sum of all transactions" do
      subject.general_transactions << general_transaction_a
      subject.general_transactions << general_transaction_b
      expect(subject.total_spend).to eq 300.0
    end
  end

  context '#spend_ytd' do
    it "should return sum of this years transactions" do
      subject.general_transactions << general_transaction_a
      subject.general_transactions << general_transaction_b
      if Date.today.month == 1
        expect(subject.spend_ytd).to eq 200.0
      else
        expect(subject.spend_ytd).to eq 300.0
      end
    end
  end

  context '#spend_last_month' do
    it "should return sum of this months transactions" do
      subject.general_transactions << general_transaction_a
      subject.general_transactions << general_transaction_b
      expect(subject.spend_last_month).to eq 100.0
    end
  end

  context 'saving without a category being set' do
    it "should set the category to uncategorized if a category is not set" do
      subject.save!

      uncat = company.categories.find_by(name: "Uncategorized")
      expect(subject.category_id).to eq(uncat.id)
    end
  end
end
