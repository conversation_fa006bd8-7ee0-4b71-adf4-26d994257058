require 'rails_helper'
describe Group do
  let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
  let(:company) { create(:company) }
  let!(:company_user) {create(:company_user, company: company, custom_form_id: custom_form.id) }
  let!(:group) {
    g = Group.create(name: 'Group', company: company)
    g.group_members << GroupMember.new(contributor: company_user.contributor)
    g.contributor.privileges << Privilege.new(permission_type: 'read', name: 'CompanyUser')
    g
  }
  let!(:member) { create(:group_member, group: group, contributor: company_user.contributor) }

  context 'After assign group privileges' do
    it 'will check the contributor exists' do
      expect(group.contains?(company_user.contributor, [])).to eq(true)
    end

    it 'will remove all permissions related to group' do
      expect(group.contributor.privileges).to be_present
      group.clear_permissions
      expect(group.contributor.privileges.as_json).to eq([])
      group.reload
      expect(group.contributor.privileges).to_not be_present
    end

    it 'will remove all permissions related to group members' do
      expect(group.group_members).to be_present
      group.clear_members
      group.reload
      expect(group.group_members).to be_blank
    end

    it 'will check contributor not exists' do
      expect(group.contains?(nil, [])).to eq(false)
    end
  end
end
