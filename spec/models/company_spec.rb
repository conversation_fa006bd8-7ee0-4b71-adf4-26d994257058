require 'rails_helper'
require Rails.root.join "spec/concerns/validations.rb"
include CompanyUserHelper
include LoginHelper

describe Company do
  create_company_and_user

  let(:telecom_service) {
    telecom = TelecomService.new(attributes_for(:telecom_service))
    telecom.general_service = GeneralService.new(attributes_for(:general_service))
    telecom
  }
  context "for company name and number of employees validation" do
    it "should validate presence of name and number of employees" do
      company.name = nil
      company.valid?
      expect(company.errors[:name][0]).to eq "can't be blank"
    end
  end

  context "proper URL validation" do
    it_behaves_like "url_validations"
  end

  context "proper phone number validation" do
    it_behaves_like "phone_number_validations"
  end

  context "proper email validation" do
    it_behaves_like "email_validations"
  end

  context "for subdomain validation" do
    it "should validate uniqueness and format of subdomain" do
      set_subdomain "ab"
      expect(company.errors[:subdomain][0]).to eq "must have between 3 and 63 characters"
      set_subdomain "-asdf"
      expect(company.errors[:subdomain][0]).to eq "cannot start or end with a hyphen"
      set_subdomain "asd/asdf.fa"
      expect(company.errors[:subdomain][0]).to eq "must be only alphanumeric (or hyphen)"
    end
  end

  context "for guid creation" do
    let(:company_attributes) do
      {
        name: "Example Company",
        phone_number: "1234567890",
        subdomain: 'test1',
        default_logo_url: "default_logo_url"
      }
    end

    it "should create guid before save" do
      company = Company.new(company_attributes)
      guid = company.guid
      company.save
      expect(company.guid).not_to eq guid
    end
  end

  context "for company's default asset statuses" do
    let(:company_attributes) do
      {
        name: "House Fixers",
        phone_number: "03001234567",
        subdomain: 'housefixers',
        default_logo_url: "default_logo_url"
      }
    end

    it "should create company's default asset statuses" do
      company = Company.create!(company_attributes)
      expect(company.asset_statuses.length).to eq(4)
    end
  end

  context 'as json' do
    it 'should convert the company object into json' do
       company.name = 'test_company'
       company.logo.attach(
        io: File.open(Rails.root.join('spec', 'fixtures', 'avatar.jpeg')),
        filename: 'avatar.jpeg',
        content_type: 'image/jpeg'
      )
       company.logo_url = 'https://s3.amazonaws.com/nulodgic-development/companies/logos/000/002/012/medium/avatar.png?1640007483'
       company.original_logo_url = 'https://s3.amazonaws.com/nulodgic-development/companies/logos/000/002/012/thumb/avatar.png?1640007845'
       company.save!
       company_as_json = company.as_json(nil)
       expect(company.logo.filename.to_s).to eq(company_as_json['logo_file_name'])
    end

    it 'should convert the company object into json if logo is old' do
       company.name = 'test_company'
       company.logo.attach(
        io: File.open(Rails.root.join('spec', 'fixtures', 'avatar.jpeg')),
        filename: 'avatar.jpeg',
        content_type: 'image/jpeg'
      )
       company.logo_url = 'https://s3.amazonaws.com/nulodgic-development/companies/logos/000/002/012/medium/avatar.png?1640007483'
       company.original_logo_url = 'https://s3.amazonaws.com/nulodgic-development/companies/logos/000/002/012/thumb/avatar.png?1640007845'
       company.save!
       company_as_json = company.as_json(nil)
       expect(company.logo.filename.to_s).to eq(company_as_json['logo_file_name'])
    end
  end

  context 'subdomain unique?' do
    it 'should return true for unique subdomain' do
      expect(described_class.is_subdomain_unique?('new_test_company')).to eq(true)
    end

    it 'should return false for non unique subdomain' do
      expect(described_class.is_subdomain_unique?(company.subdomain)).to eq(false)
    end
  end

  context 'is reserved?' do
    it 'should return true if company domain contain reserved word' do
      expect(described_class.is_reserved?('genuity')).to eq(true)
    end

    it 'should return false if company domain does not contain reserved word' do
      expect(described_class.is_reserved?(company.subdomain)).to eq(false)
    end
  end

  context 'current id' do
    it 'sets the company id' do
      expect(described_class.current_id=(company.id)).to eq(company.id)
    end
  end

  context 'current id' do
    it 'gets the company id' do
      expect(described_class.current_id).to eq(described_class.current_id)
    end
  end

  context 'search path' do
    it 'restores the company search path' do
      expect(company.change_search_path).to eq("\"$user\", public")
    end
  end

  context 'ptrg credentials' do
    it 'resturns the prtg credentials ' do
      company.prtg_username = 'Alan'
      company.prtg_passhash = '12312312'
      company.save!()
      credential = company.prtg_credentials
    end

    it 'resturns the nil prtg credentials ' do
      credential = company.prtg_credentials
    end
  end

  context 'has active subscription or not' do
    it 'should check company has active subscription for true case' do
      Subscription.create!({
       company_id: company.id,
       subscription_plan_id: 2,
       status: 'active' })

      expect(company.has_active_subscription?).to eq(true)
    end

    it 'checks the company subscription for the false case' do
     expect(company.has_active_subscription?).to eq(false)
    end
  end

  context 'has expiring subscription' do
    it 'checks company has expiring subscription for true case' do
      Subscription.create!({
       company_id: company.id,
       subscription_plan_id: 2,
       end_date: Date.today + 1.day,
       status: 'active' })
      expect(company.has_expiring_subscription?).to eq(true)
    end

    it 'checks the company subscription for the false case' do
     expect(company.has_expiring_subscription?).to eq(false)
    end
  end

  context 'has expired subscription' do
    it 'checks company expired' do
      Subscription.create!({
       company_id: company.id,
       subscription_plan_id: 2,
       end_date: 2.day.ago,
       status: 'active' })
      expect(company.has_expired_subscription?).to eq(true)
    end
  end

  context 'has insolvent subscription' do
    it 'checks company has insolvent subscription for true case' do
      Subscription.create!({
       company_id: company.id,
       subscription_plan_id: 2,
       end_date: Date.today + 1.day,
       status: 'insolvent' })
      expect(company.has_insolvent_subscription?).to eq(true)
    end

    it 'checks the company has insolvents subscription for the false case' do
     expect(company.has_insolvent_subscription?).to eq(false)
    end
  end

  context 'has free trial' do
    it 'checks company has free trial' do
      expect(company.has_current_free_trial?).to eq(true)
    end
  end

  context 'subscription expired' do
    it 'checks company subscription not expired since 30 days' do
      Subscription.create!({
       company_id: company.id,
       subscription_plan_id: 2,
       end_date: Date.today + 10.days,
       status: 'active' })
      expect(company.expired_since_30_days?).to eq(false)
    end

    it 'checks subscription expired since 30 days' do
     company.created_at = 3.month.ago
     expect(company.expired_since_30_days?).to eq(true)
    end
  end

  context 'allow access' do
    it 'allows access to the active subscription companies' do
      Subscription.create!({
       company_id: company.id,
       subscription_plan_id: 2,
       end_date: Date.today + 10.days,
       status: 'active' })
      expect(company.allow_access?).to eq(true)
    end
  end

  context 'logo or default logo' do
    it 'checks for the logo' do
      logo = 'https//s3.amazonaws.com/nulodgic-development/companies/logos/000/002/012/thumb/avatar.png?1640007845'
      company.logo_url = 'https//s3.amazonaws.com/nulodgic-development/companies/logos/000/002/012/medium/avatar.png?1640007483'
      company.original_logo_url = 'https//s3.amazonaws.com/nulodgic-development/companies/logos/000/002/012/thumb/avatar.png?1640007845'
      expect(company.logo_or_default_logo).to eq(logo)
    end
  end

  context 'day left for subscription' do
    it 'returns the days left for the subscription ' do
      Subscription.create!({
       company_id: company.id,
       subscription_plan_id: 2,
       end_date: Date.today + 10.days,
       status: 'active' })
      expect(company.subscription_expiring_days_left).to eq(10)
    end
  end

  context "for model functions" do
    it "should return default prtg credentials" do
      expect(company.get_prtg_username).to eq company['prtg_username']
      expect(company.get_prtg_passhash).to eq company['prtg_passhash']
    end

    it "should return subdomain appended with hostname" do
      expect(company.hostname).to eq "#{company.subdomain}.#{Rails.application.credentials.root_domain}"
    end
  end

  context "new company creation" do
    let(:company_attributes) do
      {
        name: "Example Company",
        subdomain: 'test1',
        default_logo_url: "default_logo_url"
      }
    end

    it "should have the users' email and timezone, a default logo and the first locations' phone number" do
      company = Company.new(company_attributes)
      company.save!
      company.set_up_self(user)

      expect(company.email).to eq(user.email)
      expect(company.timezone).to eq(user.timezone)
      expect(company.default_logo_url).not_to be_nil
    end
  end

  context "Callbacks Existence" do
    it { should callback(:create_company_asset_statuses).after(:commit) }
  end

  context "associations" do
    it "has many departments" do
      assoc = described_class.reflect_on_association(:departments)
      expect(assoc.macro).to eq :has_many
    end

    it "has many company_users" do
      assoc = described_class.reflect_on_association(:company_users)
      expect(assoc.macro).to eq :has_many
    end

    it "has many users" do
      assoc = described_class.reflect_on_association(:users)
      expect(assoc.macro).to eq :has_many
    end

    it "has many locations" do
      assoc = described_class.reflect_on_association(:locations)
      expect(assoc.macro).to eq :has_many
    end

    it "has many contracts" do
      assoc = described_class.reflect_on_association(:contracts)
      expect(assoc.macro).to eq :has_many
    end

    it "has many help_ticket_email_logs" do
      assoc = described_class.reflect_on_association(:ticket_email_logs)
      expect(assoc.macro).to eq :has_many
    end

    it "has many managed_assets" do
      assoc = described_class.reflect_on_association(:managed_assets)
      expect(assoc.macro).to eq :has_many
    end

    it "has many help_tickets" do
      assoc = described_class.reflect_on_association(:help_tickets)
      expect(assoc.macro).to eq :has_many
    end

    it "has many telecom_services" do
      assoc = described_class.reflect_on_association(:telecom_services)
      expect(assoc.macro).to eq :has_many
    end

    it "has many asset statuses" do
      assoc = described_class.reflect_on_association(:asset_statuses)
      expect(assoc.macro).to eq :has_many
    end

    it "has many invoices" do
      assoc = described_class.reflect_on_association(:invoices)
      expect(assoc.macro).to eq :has_many
    end

    it "has many phone_numbers" do
      assoc = described_class.reflect_on_association(:phone_numbers)
      expect(assoc.macro).to eq :has_many
    end

    it "has many ip_addresses" do
      assoc = described_class.reflect_on_association(:ip_addresses)
      expect(assoc.macro).to eq :has_many
    end

    it "has many discovered_assets" do
      assoc = described_class.reflect_on_association(:discovered_assets)
      expect(assoc.macro).to eq :has_many
    end

    it "has many vendors" do
      assoc = described_class.reflect_on_association(:vendors)
      expect(assoc.macro).to eq :has_many
    end

    it "has many discovered_data_services" do
      assoc = described_class.reflect_on_association(:discovered_data_services)
      expect(assoc.macro).to eq :has_many
    end

    it "has many discovered_voice_services" do
      assoc = described_class.reflect_on_association(:discovered_voice_services)
      expect(assoc.macro).to eq :has_many
    end
  end

  context "#fiscal_year" do
    it "returns company's fiscal year start date" do
    end

    it "defaults to January 1st" do
    end
  end

  context "#nuke_it" do
    context "with general module info" do
      let!(:child_com) { create(:company, reseller_company: company) }
      let!(:vendor) { create(:vendor, name: 'AT&T', company: company) }
      let!(:plaid_account) { create(:plaid_account, company: company) }
      let!(:general_transaction1) { create(:general_transaction, product_name: '7Eleven', company: company) }
      let!(:product) { create(:product, vendor: vendor, company: company) }
      let!(:general_transaction2) { create(:general_transaction,
                                           vendor: vendor,
                                           plaid_account: plaid_account,
                                           status: :recognized,
                                           product: product,
                                           product_name: 'AT&T',
                                           company: company) }
      let!(:contract) { create(:contract, company: company) }
      let!(:telecom_service) { create(:telecom_service, company: company) }
      let!(:user_2) { create(:user, email: "<EMAIL>") }
      let!(:referral) { Referral.create(referrer_id: user.id, referred_id: user_2.id, referrer_email: user.email, referred_email: user_2.email) }

      xit "should remove the company but keep the referral record" do
        id = company.id
        company.groups.each { |g| g.save! }
        company.name = 'test_company'
        company.is_sample_company = true
        company.save!
        company.nuke_it!
        actual_company = Company.find_by(id: id)
        child_company = Company.find_by(reseller_company_id: id)
        expect(actual_company).to be_nil
        expect(child_company).to be_nil
        expect(Referral.count).to eq(1)
        expect(Referral.first.referred_email).to eq(user_2.email)
        expect(Referral.first.referrer_email).to eq(user.email)
      end
    end

    context "with integrations data" do
      let(:aws) { Integration.find_by(name: 'aws') }
      let(:azure) { Integration.find_by(name: 'azure') }
      let(:gsuite) { Integration.find_by(name: 'gsuite') }
      let(:microsoft) { Integration.find_by(name: 'microsoft') }
      let(:okta) { Integration.find_by(name: 'okta') }
      let(:one_login) { Integration.find_by(name: 'one_login') }
      let(:salesforce) { Integration.find_by(name: 'salesforce') }
      let(:quickbooks) { Integration.find_by(name: 'quickbooks') }
      let(:xero) { Integration.find_by(name: 'xero') }

      let!(:aws_config) do
        create_config(:aws_config, aws, 'aws')
      end

      let!(:azure_config) do
        create_config(:azure_config, azure, 'azure')
      end

      let!(:gsuite_config) do
        create_config(:gsuite_config, gsuite)
      end

      let!(:microsoft_config) do
        create_config(:microsoft_config, microsoft)
      end

      let!(:okta_config) do
        create_config(:okta_config, okta)
      end

      let!(:one_login_config) do
        create_config(:one_login_config, one_login)
      end

      let!(:quickbooks_config) do
        create_config(:quickbooks_config, quickbooks)
      end

      let!(:salesforce_config) do
        create_config(:salesforce_config, salesforce)
      end

      let!(:xero_config) do
        create_config(:xero_config, xero)
      end

      xit "should remove the company" do
        id = company.id
        company.nuke_it!
        actual_company = Company.find_by(id: id)
        expect(actual_company).to be_nil
      end
    end
  end

  context 'default_logo_url is nil' do
    it 'expects update to return false if default_logo_url is nil' do
      expect(company.update(default_logo_url: nil)).to be_falsey
    end
  end

  private

  def create_config(config_id, integration, vendor_name = nil)
    config = build(config_id, company: company, skip_callbacks: true)
    company_integration = CompanyIntegration.new(integration: integration, company: company)
    company_integration.general_transactions << build(:general_transaction, company: company)
    if vendor_name.present?
      cloud_usage = build(:cloud_usage_transaction, company: company)
      cloud_usage.product = build(:product, company: company)
      cloud_usage.vendor = build(:vendor, name: vendor_name, company: company)
      company_integration.cloud_usage_transactions << cloud_usage
    end
    app = build(:integrations_app, company: company)
    company_integration.app_usages << build(:app_usage,
                                            company_integration: company_integration,
                                            integration: integration,
                                            app: app)
    config.company_integration = company_integration
    config.save!(validate: false)
    config
  end

  def set_subdomain(subdomain_value)
    company["subdomain"] = subdomain_value
    company.valid?
  end
end
