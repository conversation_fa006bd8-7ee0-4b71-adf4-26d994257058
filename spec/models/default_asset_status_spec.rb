require 'rails_helper'

describe DefaultAssetStatus do
  context "for validation of name and icon" do
    let(:asset_status_1) { DefaultAssetStatus.create!(name: 'Donated', icon: '&times;') }
    let!(:asset_status_2) { DefaultAssetStatus.create!(name: 'Disposed', icon: '&times;') }
    let!(:asset_status_3) { DefaultAssetStatus.create!(name: 'For Sale', icon: 'genuicon-exclamation') }

    it "should validate presence of name" do
      asset_status_1.name = nil
      asset_status_1.valid?
      expect(asset_status_1.errors[:name][0]).to eq "can't be blank"
    end

    it "should validate presence of icon" do
      asset_status_1.icon = nil
      asset_status_1.valid?
      expect(asset_status_1.errors[:icon][0]).to eq "can't be blank"
    end

    it "should validate uniqueness of name" do
      asset_status_2.name = 'In Use'
      asset_status_2.valid?
      expect(asset_status_2.errors[:name][0]).to eq "has already been taken"
    end
  end
end
