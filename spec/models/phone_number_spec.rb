require 'rails_helper'
require Rails.root.join "spec/concerns/validations.rb"
include CompanyUserHelper

describe PhoneNumber do
  create_company_and_user

  context "proper phone number validation" do
    it_behaves_like "phone_number_validations"
  end

  context 'with valid attributes' do
    let(:attributes) do
      {
        number: "************",
        friendly_name: "Office Phone"
      }
    end

    it "is valid" do
      subject = PhoneNumber.new(attributes)
      subject.company = company
      expect(subject).to be_valid
    end
  end

  context 'with blank phone number' do
    let(:attributes) do
      {
        number: "",
        friendly_name: "New Number"
      }
    end

    it "is invalid with blank number" do
      subject = PhoneNumber.new(attributes)
      expect(subject).to be_invalid
    end
  end

  context 'with pre-existing valid phone number' do
    let(:attributes) do
      {
        number: "************",
        friendly_name: "New Number"
      }
    end

    it "is invalid" do
      subject1 = PhoneNumber.create(attributes)
      subject = PhoneNumber.new(attributes)
      expect(subject).to be_invalid
    end
  end
end
