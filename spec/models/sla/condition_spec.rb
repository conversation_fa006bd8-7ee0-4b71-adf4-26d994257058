require 'rails_helper'

describe Sla::Condition do
  let(:company) { FactoryBot.create(:company) }
  let(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "helpdesk") }
  let(:sla_policy) { FactoryBot.create(:sla_policy, company: company, custom_form: custom_form) }
  let(:valid_condition_attribute) do
    {
      "condition_type"=> "form_field",
      "field_type"=>"tag",
      "value"=>{"form_value"=>["Business Service"]}
    }
  end

  describe 'validations' do
    it { expect(subject).to validate_presence_of(:condition_type).with_message(/is required/) }
    it { expect(subject).to validate_presence_of(:field_type).with_message(/is required/) }
    it { expect(subject).to validate_presence_of(:value).with_message(/is required/) }
  end

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:policy)
      expect(assoc.macro).to eq :belongs_to
    end
  end

  context "field_type should be unique" do
    it "does not allow user to select same field_type again" do
      condition1 = described_class.create(valid_condition_attribute)
      condition2 = described_class.new(valid_condition_attribute)
      expect(condition2.valid?).to eq(false)
    end
  end
end
