require 'rails_helper'

describe Sla::Policy do
  let(:company) { FactoryBot.create(:company) }
  let(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "helpdesk") }
  let(:valid_policy_attributes) do
    {
      custom_form_id: custom_form.id,
      description: "This is some description",
      name: 'SLA Policy Name 1',
      company_id: company.id
    }
  end

  describe 'validations' do
    it { expect(subject).to validate_presence_of(:name).with_message(/is required/) }
  end

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end

    it "should have belongs_to relationship with custom form" do
      assoc = described_class.reflect_on_association(:custom_form)
      expect(assoc.macro).to eq :belongs_to
    end

    it "should have has_many relationship with details" do
      assoc = described_class.reflect_on_association(:details)
      expect(assoc.macro).to eq :has_many
    end

    it "should have has_many relationship with conditions" do
      assoc = described_class.reflect_on_association(:conditions)
      expect(assoc.macro).to eq :has_many
    end
  end
end
