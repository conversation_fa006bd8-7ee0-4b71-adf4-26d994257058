require 'rails_helper'

describe Sla::Detail do
  let(:company) { FactoryBot.create(:company) }
  let(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "helpdesk") }
  let(:sla_policy) { FactoryBot.create(:sla_policy, company: company, custom_form: custom_form) }
  let(:valid_detail_attributes) do
    {
      priority: "high",
      first_response_time: "0-0-15",
      resolution_time: "0-0-15",
      operational_hours: 1,
      escalation: false,
      sla_policy_id: sla_policy.id,
    }
  end

  describe 'validations' do
    it { expect(subject).to validate_presence_of(:priority).with_message(/is required/) }
  end

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:policy)
      expect(assoc.macro).to eq :belongs_to
    end
  end

  context "Company and token should be unique" do
    it "does not allow user to integrate the account again" do
      policy1 = described_class.create(valid_detail_attributes)
      policy2 = described_class.new(valid_detail_attributes)
      expect(policy2.valid?).to eq(false)
    end
  end
end
