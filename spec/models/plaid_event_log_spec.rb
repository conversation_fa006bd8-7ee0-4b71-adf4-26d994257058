require 'rails_helper'

RSpec.describe Logs::PlaidEventLog, type: :model do
  let(:company_user) { FactoryBot.create(:company_user) }

  it "creates a plaid event log" do
    Logs::PlaidEventLog.create!(company_user_id: company_user.id, event_name: 'ASDFASDF', metadata: "asfdasdfasdf")
  end

  context "after_create" do
    it "will create api event" do
      expect(Logs::ApiEvent.count).to eq(0)
      Logs::PlaidEventLog.create!(company_user_id: company_user.id, event_name: 'ASDFASDF', metadata: "asfdasdfasdf")
      expect(Logs::ApiEvent.count).to eq(1)
    end
  end

  it 'return company if it exist' do
    plaid_event_log = Logs::PlaidEventLog.create!(company_user_id: company_user.id, event_name: 'ASDFASDF', metadata: "asfdasdfasdf")
    expect(plaid_event_log.company.id).to eq(company_user.company.id)
  end
end
