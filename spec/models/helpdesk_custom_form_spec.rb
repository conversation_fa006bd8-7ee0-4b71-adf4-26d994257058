require 'rails_helper'

describe HelpdeskCustomForm do
  let(:company) { create(:company) }
  let(:custom_form_1) { create(:custom_form, company: company, form_name: 'Helpdesk Custom Form 1', company_module: 'helpdesk') }
  let(:custom_form_2) { create(:custom_form, company: company, form_name: 'Helpdesk Custom Form 2', company_module: 'helpdesk') }
  let(:email) { "helpdesk@#{company.subdomain}.gogenuity.com" }

  context "allow duplicate emails for different custom forms" do
    it "is valid" do
      helpdesk_form_1 = custom_form_1.helpdesk_custom_form
      helpdesk_form_2 = custom_form_2.helpdesk_custom_form
      
      helpdesk_form_1.update(email: email)
      helpdesk_form_2.email = email
      expect(helpdesk_form_2).to be_valid
    end
  end
end
