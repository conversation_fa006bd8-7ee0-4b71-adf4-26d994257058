require 'rails_helper'
require 'support/aws_stub'

include CompanyUserHelper

describe CustomFormAttachment do
  create_company_and_user

  before do
    Aws.config[:stub_responses] = true
  end

  let(:help_ticket) {
    t = FactoryBot.create(:help_ticket, company: company, creator_id: company_user.id, workspace: company.default_workspace)
    attachments = t.custom_form.custom_form_fields.find_by(name: 'attachments')
    value = CustomFormValue.create(module: t, custom_form_field: attachments)
    t
  }

  let(:attachment_attributes_as_upload) do
    {
      attachment: { io: URI("https://nulodgic-static-assets.s3.amazonaws.com/images/logo.png").open(), filename: 'logo/png' },
      company_id: company.id,
      custom_form_value_id: help_ticket.custom_form_values.first.id
    }
  end

  let!(:library_document_without_name) { LibraryDocument.create(
    attached_file: { io: URI("https://nulodgic-static-assets.s3.amazonaws.com/images/warning.png").open(), filename: 'warning/png' },
    company_id: company.id,
    name: 'Danger Image'
  )}

  let!(:library_document_with_name) { LibraryDocument.create(
    attached_file: { io: URI("https://nulodgic-static-assets.s3.amazonaws.com/images/office-desk.png").open(), filename: 'office-desk/png' },
    company_id: company.id
  )}

  context "Getting the right file_name and file_size" do
    it "should call the right name if it has attributes of an uploaded attachment" do
      subject = company.custom_form_attachments.new(attachment_attributes_as_upload)
      subject.save
      expect(subject.attachment.filename.to_s).to eq('logo-png')
    end

    it "should call the right name & size if it has attributes of a library document w/out name" do
      subject = company.custom_form_attachments.new(library_document_id: library_document_with_name.id)
      subject.save
      expect(subject.library_document.name).to_not be_present
      expect(subject.library_document.attached_file.byte_size).to eq(5826)
    end

    it "should call the right name & size if it has attributes of a library document w/ name" do
      subject = company.custom_form_attachments.new(library_document_id: library_document_with_name.id)
      subject.save
      expect(subject.library_document.name).to_not eq('Danger Image')
      expect(subject.library_document.attached_file.byte_size).to eq(5826)
    end

    it "should create a help ticket comment in response to the attachment created" do
      attachment = company.custom_form_attachments.new(attachment_attributes_as_upload)
      attachment.save
      comment = help_ticket.help_ticket_comments.first
      comment.comment_body = comment.comment_body.gsub('https:', '')
      comment.save!
      expect(comment.comment_body).to be_present
    end
  end
end

