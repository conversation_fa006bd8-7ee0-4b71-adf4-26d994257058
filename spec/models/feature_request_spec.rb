require 'rails_helper'
include CompanyUserHelper

describe FeatureRequest do
  create_company_and_user

  let(:user2) { create(:user)}

  let(:feature_request) { create(:feature_request, creator_id: company_user.id, feature_state: 'recent') }
  let!(:feature_request_comment) { create(:feature_request_comment, company_id: company.id, creator_id: company_user.id, feature_request_id: feature_request.id) }

  describe 'After create' do
    it 'will return the creator contributor id' do
      feature_req = feature_request.feature_object(company_user, user2)
      expect(feature_req[:id]).to eq(feature_request.id)
      expect(feature_req.symbolize_keys[:creator][:id]).to eq(company_user.contributor_id)
    end

    it 'will return the feature creator' do
      expect(feature_request.feature_creator(company_user)).to eq(true)
    end

    it 'will return the total added comments' do
      expect(feature_request.comments_count(company_user, user2)).to eq(1)
    end
  end
end
