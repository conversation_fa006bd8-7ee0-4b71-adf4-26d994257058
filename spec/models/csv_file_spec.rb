require 'rails_helper'

describe CsvFile do
  let(:company) { create(:company, :with_locations) }

  it "is valid with valid attributes" do
    record = {first_name: "<PERSON>", last_name: "<PERSON><PERSON>",
        email: "<EMAIL>", title: 'Owner', department: 'IT',
        work_phone: "1234567890", work_phone_extension: "112", location: 'HQ',
        mobile_phone: "1234567890"}.with_indifferent_access
    csv_file = CsvFile.new(record, company.id)
    expect(csv_file).to be_valid
  end

  context "with an existing user" do
    let!(:user) { create(:user, email: '<EMAIL>')}

    it "is valid if the user already exists" do
      record = {first_name: "<PERSON>", last_name: "<PERSON><PERSON>",
          email: "<EMAIL>", title: 'Owner', department: 'IT',
          work_phone: "1234567890", work_phone_extension: "112", location: 'HQ',
          mobile_phone: "1234567890"}.with_indifferent_access
      csv_file = CsvFile.new(record, company.id)
      expect(csv_file).to be_valid
    end

    it "is invalid if the user and company user already exist" do
      create(:company_user, company: company, user: user)
      record = {first_name: "<PERSON>", last_name: "Zuck",
          email: "<EMAIL>", title: 'Owner', department: 'IT',
          work_phone: "1234567890", work_phone_extension: "112", location: 'HQ',
          mobile_phone: "1234567890"}.with_indifferent_access
      csv_file = CsvFile.new(record, company.id)
      expect(csv_file).not_to be_valid
      expect(csv_file.errors.added?(:email, :already_exists)).to be_truthy
    end
  end
end
