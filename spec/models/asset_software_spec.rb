require 'rails_helper'

RSpec.describe AssetSoftware, type: :model do
  context "associations" do
    it "belongs to managed_asset" do
      assoc = described_class.reflect_on_association(:managed_asset)
      expect(assoc.macro).to eq :belongs_to
    end
  end

  context "Callbacks Existence" do
    it { should callback(:remove_unicode_control_codes).before(:save) }
  end

  context "create_asset_software" do
    it "should exclude unicode control codes from software name" do
      software = FactoryBot.create(:asset_software,
                                   name: "Black Ice TIFF DriverDisplayName¢{±€\u0004ÙtýÃqxïO\u0018`æO¹£{\fçO`ƒ^±€\u0004Ù@þy\u0002(þy\u0002\u0018–fxæOxæO\u0090æO\u0090æO\u0005 þy\u0002")
      expect(software.name).to eq("Black Ice TIFF DriverDisplayName¢{±€ ÙtýÃqxïO `æO¹£{ çO`ƒ^±€ Ù@þy (þy –fxæOxæO æO æO þy")
    end

    it "should not update the software name" do
      software = FactoryBot.create(:asset_software,
                                   name: "Genuity®")
      expect(software.name).to eq("Genuity®")
    end
  end
end
