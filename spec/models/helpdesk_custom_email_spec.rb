require 'rails_helper'

describe HelpdeskCustomEmail do
  let(:company) { create(:company, :with_locations) }
  let(:attributes) {
    {
      email: email,
      company_id: company.id,
      name: name,
    }
  }

  context 'with valid attributes' do
    let (:name) { "dna@123" }
    let (:email) { "<EMAIL>" }

    context "with a custom email" do
      it "is valid" do
        custom_email = described_class.new(attributes)
        expect(custom_email).to be_valid
      end
    end

    context "with a custom name" do
      it "is valid" do
        custom_name = described_class.new(attributes)
        expect(custom_name).to be_valid
      end
    end

    context "with the company\'s subdomain" do
      let (:email) { "dog@#{company.subdomain}.#{Rails.application.credentials.root_domain}" }
      it "is valid" do
        custom_email = described_class.new(attributes)
        expect(custom_email).to be_valid
      end
    end
  end

  context "with a duplicate email" do
    let (:name) { "Help Desk" }
    let (:email) { "<EMAIL>" }
    
    it "is invalid" do
      custom_email_1 = described_class.create(attributes)
      custom_email_2 = described_class.new(attributes)
      expect(custom_email_2).to be_valid
    end
  end

  context 'with invalid attributes' do
    let (:name) { "%dna@123" }
    let (:email) { "dog@dog.#{Rails.application.credentials.root_domain}" }

    context 'with another company subdomain' do
      it "is invalid" do
        custom_email = described_class.new(attributes)
        expect(custom_email).to_not be_valid
        expect(custom_email.errors[:email]).to eq(["must only use the current company Genuity domain"])
      end
    end

    context 'with invalid special character in name' do
      it "is invalid" do
        custom_name = described_class.new(attributes)
        expect(custom_name).to_not be_valid
        expect(custom_name.errors[:name]).to eq(["has an incorrect format."])
      end
    end
  end
end
