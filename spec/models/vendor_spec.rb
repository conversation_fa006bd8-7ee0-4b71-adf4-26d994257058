require 'rails_helper'
require Rails.root.join "spec/concerns/validations.rb"
require Rails.root.join "spec/concerns/fiscal.rb"

include CompanyUserHelper

describe Vendor do
  create_company_and_user

  let(:vendor) { create(:vendor, name: 'MyString', company:company) }
  let(:default_vendor) { create(:default_vendor) }
  let!(:product) { create(:product, vendor_id: vendor.id, default_tags:['Test', 'Test_tag'], company_id: company.id) }
  let(:vendor_contact) { create(:vendor_contact, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', phone_number:'2134567842', vendor_id: vendor.id) }
  let(:general_transaction) { create(:general_transaction, vendor_id: vendor.id, product_id: product.id, company_id: company.id, amount: 20000, transaction_date: 2.month.ago, status: 'recognized') }

  context "proper URL validation" do
    it_behaves_like "url_validations"
  end

  context "proper phone number validation" do
    it_behaves_like "phone_number_validations"
  end

  context "proper email validation" do
    it_behaves_like "email_validations"
  end

  context "fiscal validation" do
    it_behaves_like "fiscal"
  end

  context "after deletion" do
    it "removes associated module alerts" do
      vendor = described_class.create(name: "Test", company: company)
      transaction_date = Date.today - 1.month
      alert = VendorAlertService.new(vendor: vendor, date: transaction_date || Date.today).alert

      vendor.destroy
      expect(ModuleAlert.count).to eq(0)
    end
  end

  context "before save" do
    it "formats the phone number" do
      vendor = described_class.create(name: "Test", company: company, phone_number: "(*************")

      expect(vendor.phone_number).to eq("1234562345")
    end

    it "includes default US phone number country codes when none provided" do
      vendor = described_class.create(name: "Test", company: company, phone_number: "(*************")

      expect(vendor.phone_number_country_code).to eq("US")
      expect(vendor.phone_number_country_code_number).to eq("1")
    end

    it "sets the category to uncategorized if a category is not set" do
      vendor = described_class.create(name: "Test", company: company, phone_number: "(*************")

      uncat = vendor.company.categories.find_by(name: "Uncategorized")
      expect(vendor.category_id).to eq(uncat.id)
    end
  end

  context "on create" do
    before do
      described_class.create(name: "Test", company: company)
    end

    it "checks for name uniqueness regardless of casing" do
      expect { described_class.create(name: "teST", company: company) }.to raise_error(ActiveRecord::RecordNotUnique)
    end
  end

  context "when the category changes" do
    it "create a telecom provider when category is telecom" do
      category = company.categories.first
      expect(category).to be_present
      vendor = create(:vendor, category_id: category.id)
      vendor.category = company.categories.find_by(name: 'Telecom')
      vendor.save!
      telecom_provider = TelecomProvider.find_by(vendor_id: vendor.id)
      expect(telecom_provider).to be_present
    end
  end

  context "before create" do

    it "does not add a description if one is present" do
      vendor = described_class.create(name: "teST", company: company, description: "This is a description")

      expect(vendor.description).to eq("This is a description")
    end
  end

  it 'will return those vendors who provide the telecom service' do
    default_category = DefaultCategory.find_by_name("Telecom")
    default_vendor.default_category_id = default_category.id
    default_vendor.save!
    expect(Vendor.looks_like_telecom_from_default_vendor.count).to eq(1)
  end

  it 'will return those categories whose name is telecom' do
    category = Category.find_by(name: "Telecom", module_type: 'company')
    vendor.category_id = category.id
    vendor.save!
    expect(Vendor.looks_like_telecom_from_category.count).to eq(1)
  end

  it 'will check ignore telecom notification is null' do
    default_category = DefaultCategory.find_by_name("Telecom")
    default_vendor.default_category_id = default_category.id
    default_vendor.save!
    expect(Vendor.telecom_recommendations.count).to eq(1)
  end

  it 'will return vendor info as json' do
    expect(vendor.get_vendor_info_json['id']).to eq(vendor.id)
    expect(vendor.get_vendor_info_json['company_id']).to eq(vendor.company_id)
  end

  it 'will check the current vendor insight' do
    expect(vendor.current_vendor_insights[:id]).to eq(vendor.id)
  end

  it 'will return the phone contact of vendor' do
    expect(vendor_contact.vendor.contact_phone).to eq('************')
  end

  it 'will return the contact email of vendor' do
    expect(vendor_contact.vendor.contact_emails).to eq('<EMAIL>')
  end

  it 'will return the category name' do
    expect(vendor.category_name).to eq('Uncategorized')
  end

  it 'will return the full name of the vendor' do
    expect(vendor_contact.vendor.vendor_contacts_details).to eq('Alex Smith')
  end

  it 'will assign the tags to product' do
    product_value = product.vendor.assign_tags_to_products
    product_default_tags = product_value.pluck(:default_tags)
    expect(product_default_tags.flatten()[0]).to eq('Test')
    expect(product_default_tags.flatten()[1]).to eq('Test_tag')
    expect(product_default_tags.flatten()[2]).to eq('default')
  end

  context 'After transaction' do
    it 'will return the average monthly cost' do
      expect(general_transaction.vendor.average_monthly_cost).to eq(6666.67)
    end

    it 'will reuturn the last month spending' do
      general_transaction.transaction_date  = 1.month.ago
      general_transaction.save!
      expect(general_transaction.vendor.last_month_spending).to eq(general_transaction.amount)
    end

    it 'will return specific month spending' do
      expect(general_transaction.vendor.specific_month_spending(2.month.ago)).to eq(general_transaction.amount)
    end

    it 'will return this month spending' do
      general_transaction.transaction_date  = Date.today
      general_transaction.save!
      expect(general_transaction.vendor.this_month_spending).to eq(general_transaction.amount)
    end
  end

  it 'will create base product' do
    expect(product.vendor.create_base_product).to eq(true)
  end

  it 'will return the alert date of transaction' do
    general_transaction.transaction_date  = Date.today
    general_transaction.save!
    expect(general_transaction.vendor.alert_date.to_s).to eq(Date.today.to_s)
  end
end
