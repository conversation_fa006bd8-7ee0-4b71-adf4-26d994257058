require 'rails_helper'

describe HelpTicketComment do
  let(:company) { create(:company, :with_locations) }
  let(:user) { create(:user) }
  let(:company_user) { create(:company_user, user: user, company: company, helpdesk_agent: true) }

  context 'with active and merged ticket' do
    let!(:active_ticket) { create(:help_ticket, company: company, workspace: company.default_workspace) }
    let!(:merged_ticket) { create(:help_ticket, company: company, workspace: company.default_workspace, active_ticket_id: active_ticket.id) }

    it 'create comment in merged ticket will also add it in active ticket' do
      comment = HelpTicketComment.create(
        comment_body: "Comment posted in merged ticket",
        help_ticket_id: merged_ticket.id,
        contributor_id: company_user.contributor_id
      )
      expected_activity_data = {
        "email"=>nil,
        "source"=>"manually_added",
        "task_id"=>nil,
        "performer"=>nil,
        "ticket_number"=>active_ticket.ticket_number.to_s,
        "ticket_subject"=>nil,
        "new_comment_body"=>"Comment posted in merged ticket",
        "merged_ticket_data"=>"{\"id\":#{merged_ticket.id},\"ticket_number\":\"#{merged_ticket.ticket_number}\"}",
        "previous_comment_body"=>nil,
        "help_ticket_comment_id"=>active_ticket.help_ticket_comments.first.id.to_s
      }

      expect(merged_ticket.help_ticket_comments.count).to eq(1)
      expect(active_ticket.help_ticket_comments.count).to eq(1)
      expect(active_ticket.help_ticket_comments.first.merged_ticket_id).to eq(merged_ticket.id)
      expect(active_ticket.help_ticket_comments.first.comment_body).to eq(merged_ticket.help_ticket_comments.first.comment_body)
      help_ticket_activity = active_ticket.help_ticket_activities.find_by_activity_type('note')
      expect(help_ticket_activity.data).to eq(expected_activity_data)
    end
  end
end
