require 'rails_helper'

describe TelecomService do
  let(:company) { create(:company) }

  let(:attributes) do
      {
        connection: "test connection",
        circuit_id: "124",
        network_speed: "100MB/s",
        equipment: "test equipment",
        company_id: company.id
      }
  end

  context 'with valid attributes' do
    it "is valid" do
      telecom_service = described_class.new(attributes)
      expect(telecom_service).to be_valid
    end
  end

  context 'with invalid priority' do
    it "is invalid priority was not from the enum" do
      telecom_service = described_class.new(attributes)
      expect { telecom_service.priority = :last_choice }.to raise_error(ArgumentError)
      .with_message(/is not a valid priority/)
    end
  end

  context 'belong to same company' do
    it "it matches the company to be the same" do
      telecom_service = company.telecom_services.create(attributes)
      expect(telecom_service.company.id).to be company.id
    end
  end
end
