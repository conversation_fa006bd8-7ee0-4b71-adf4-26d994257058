require 'rails_helper'

describe User do
  let(:company) { create(:company) }
  let!(:company_user) { create(:company_user) }
  let(:company2) { create(:company, free_trial_days: 0) }
  let!(:company_user2) { create(:company_user, company: company2) }
  let(:user1) { create(:user, first_name: '<PERSON>', last_name: '<PERSON><PERSON><PERSON>') }
  let(:user2) { create(:user, first_name: '<PERSON>', last_name: '<PERSON><PERSON>') }
  let(:user3) { create(:user, first_name: '<PERSON>', last_name: '<PERSON>') }
  let!(:company_user3) { create(:company_user, user: user1, company: company) }
  let!(:company_user4) { create(:company_user, user: user2, company: company) }
  let!(:company_user5) { create(:company_user, user: user3, company: company) }

  subject {
    described_class.new( {
      first_name:   '<PERSON>',
      last_name:    '<PERSON>gan<PERSON>',
      email:        '<EMAIL>',
      super_admin:  false,
      password:     '12312312',
      guid:         'helloworld',
      avatar_file_name:   '',
      auth_token:         '',
      avatar_file_size:   nil
    })
  }

  let(:user_attributes) do
    {
      first_name:   '<PERSON>',
      last_name:    '<PERSON><PERSON><PERSON>',
      email:        '<EMAIL>',
      password:     '12312312',
      guid:         'helloworld',
      super_admin:   true,
      avatar_file_name:   'avatar.png',
      avatar_file_size:   5897

    }
  end

  let(:valid_user_attributes) do
    {
      email: Faker::Internet.email,
      password: 'pa$$word',
      first_name: 'John',
      last_name: 'Ball',
      guid: '7c2a931c-2w50-4e04-85df-5e8ty56l0bf79',
      terms_of_services: true
    }
  end

  context 'first_name, last_name and password should be present' do
    it 'does not allow user have blank last_name and password and allow blank first_name' do
      subject.full_name = ''
      subject.first_name = ''
      subject.last_name = ''
      subject.password = ''
      subject.valid?
      expect(subject.errors[:last_name].size).to eq(1)
      expect(subject.errors[:password].size).to eq(3)
    end
  end

  context 'password should have minimum length 8' do
    it 'does not allow password to be less than 8 charactes' do
      subject.password = '1231231'
      subject.valid?
      expect(subject.errors[:password].size).to eq(1)
    end
  end

  context 'guid and email should be unique' do
    it 'does not allow users to have same guid and email' do
      user1 = described_class.create!(user_attributes)
      user2 = described_class.new(user_attributes)
      user2.valid?
      expect(user2.errors[:guid].size).to eq(1)
      expect(user2.errors[:email].size).to eq(1)
    end
  end

  context 'user as_json' do
    it 'should convert the user object into json' do
      user1 = described_class.create!(user_attributes)
      user_json = user1.as_json
      expect(user1.avatar_file_name).to eq(user_json['avatar_file_name'])
      expect(user1.avatar_file_size).to eq(user_json['avatar_file_size'])
    end
  end

  context 'avatar color' do
    it 'gives the color of the avatar background' do
      expect(user1.avatar_color).to be_present
    end
  end

  context 'generate the authentication token' do
    it 'should generate the authentication token' do
      expect(user1.auth_token).to eq('')
      user1.generate_authentication_token!
      expect(user1.auth_token).to be_present
    end
  end

  context 'has active subscription or not' do
    it 'should check user has active subscription or not' do
      expect(company_user.user.has_active_subscription?).to eq(true)
      expect(company_user2.user.has_active_subscription?).to eq(false)
    end
  end

  context 'get the user initials' do
    it 'gives the initials for the user' do
      user_name_initial = user1.initials
      expect(user_name_initial).to eq("#{user1.first_name.first}#{user1.last_name.first}")
      user2 = described_class.create(email: '<EMAIL>', password:'12312312')
      user_email_initial = user2.initials
      expect(user_email_initial).to eq(user2.email.first)
      user3 = described_class.create()
      user_empty_initial = user3.initials
      expect(user_empty_initial).to eq('')
    end
  end

  context 'search users in company' do
    it 'gives the users on basis of term in company' do
       company_users = described_class.search(term: 'al', company: company)
       expect(company_users[:results].count).to eq (2)
    end

    it 'gives the users from all the users' do
      users= User.search(term: 'ala') 
      expect(users[:results].count).to eq (1)
    end
  end

  context 'user authentication' do
    it 'authenticets the user on basis of conditions' do
      warden_conditions={email: company_user.email, company_id: company_user.company_id}
      expect(User.find_for_authentication(warden_conditions)).to be_present
    end
  end

  context 'generate global token' do
    it 'should generate the global token' do
      global_auth_token = User.generate_authentication_token_global!
      expect(global_auth_token).to be_present
    end
  end

  context 'is staff' do
    it 'it should return true if staff' do
      expect(user1.staff?).to eq(true)
    end
  end

  context 'is admin' do
    it 'is a admin or super admin' do
      non_super_admin = user1.admin?(company_user.company_id)
      expect(non_super_admin).to eq(false)
      user2 = described_class.new(user_attributes)
      super_admin = user2.admin?(company_user.company_id)
      expect(super_admin).to eq(true)
    end
  end

  context 'referred by' do
    it 'return the referred by id if present' do
      expect(user1.referred_by).to eq(nil)
    end
  end

  context 'login code' do
    it 'returns the login code' do
      expect(user1.login_code).to be_present
    end
  end

  context 'generic email' do
    it 'tells email is generic' do
      expect(described_class.generic_email?("<EMAIL>")).to eq(false)
    end

    it 'tells email is not generic' do
      expect(described_class.generic_email?("<EMAIL>")).to eq(true)
    end
  end

  context 'companies having access' do
    it 'gives the companies having access granted' do
      company_access_granted = user1.companies_with_access_granted
      expect(company_access_granted.first.is_reseller_company).to eq(false)
      expect(company_access_granted.first.is_sample_company).to eq(false)
    end
  end

  context 'request permission' do
    it 'mail for requesting the permission' do
      response = UserMailer.request_permission(user1, company, 'module')
      allow_any_instance_of(ActionMailer::MessageDelivery).to receive(:deliver_now!).and_return(response)
      req_permission = user1.request_permission('module', company)
      expect(req_permission.subject).to eq('Permission Request')
    end
  end

  context 'assocations' do
    it 'has many company_users' do
      assoc = described_class.reflect_on_association(:company_users)
      expect(assoc.macro).to eq :has_many
    end

    it 'has many companies' do
      assoc = described_class.reflect_on_association(:companies)
      expect(assoc.macro).to eq :has_many
    end
  end

  context 'destroy local user and cognito user when cognito user is missing' do
    let!(:domain_validator) { 
      DomainValidator.any_instance.stub(:validate).and_return(true)
    }

    it 'destroy user successfully' do
      signed_user = OpenStruct.new({user: {username: SecureRandom.uuid}})
      allow_any_instance_of(CognitoService).to receive(:sign_up_user).and_return(signed_user)
      exception_class = Aws::CognitoIdentityProvider::Errors::ResourceNotFoundException
      allow_any_instance_of(CognitoService).to receive(:delete_cognito_user).and_raise(exception_class)
      allow_any_instance_of(CognitoService).to receive(:find_user_by_email).and_return(nil)

      #create new cognito user
      user = User.create!(valid_user_attributes)
      client = CognitoService.new(user)
      resp = client.sign_up_user
      expect(resp.user[:username]).to be_present

      #update cognito guid to user
      user.guid = resp.user[:username]
      user.save

      # destroy user from cognito
      expect(client.find_user_by_email(user.email)).to be_nil

      # destroy user from local database
      expect{user.destroy}.to change(User, :count).by(-1)
    end
  end

  context 'destroy local user and cognito user' do
    it 'destroy user successfully' do
      signed_user = OpenStruct.new({user: {username: SecureRandom.uuid}})
      allow_any_instance_of(CognitoService).to receive(:sign_up_user).and_return(signed_user)
      allow_any_instance_of(CognitoService).to receive(:delete_cognito_user).and_return({username: SecureRandom.uuid})
      allow_any_instance_of(CognitoService).to receive(:find_user_by_email).and_return(nil)

      #create new cognito user
      user = User.create(valid_user_attributes)
      client = CognitoService.new(user)
      resp = client.sign_up_user
      expect(resp.user[:username]).to be_present

      #update cognito guid to user
      user.guid = resp.user[:username]
      user.save

      # destroy user from cognito
      expect(client.delete_cognito_user).not_to be_nil
      expect(client.find_user_by_email(user.email)).to be_nil

      # destroy user from local database
      id = user.id
      user.destroy
      expect(User.find_by(id: id)).to be_blank
    end
  end

  context '#full_name=' do
    let(:user) { company_user.user }
    let(:admins) { company_user.company.groups.find_by(name: 'Admins') }
    let!(:admins_member) { GroupMember.create!(group_id: admins.id, contributor: company_user.contributor) }

    context 'when value is nil' do
      it 'should blank first_name' do
        user.full_name = nil
        user.save
        expect(user.first_name).to be_blank
      end

      it 'should blank last_name' do
        user.full_name = nil
        user.save
        expect(user.last_name).to be_blank
      end
    end

    context 'when value does not have a space' do
      it 'should blank first_name' do
        user.full_name = 'User'
        user.save
        expect(user.first_name).to be_blank
      end

      it 'should set last_name' do
        user.full_name = 'User'
        user.save
        expect(user.last_name).to eq('User')
      end
    end

    context 'when value does not have a space' do
      it 'should set first_name' do
        user.full_name = 'Test User'
        user.save
        expect(user.first_name).to eq('Test')
      end

      it 'should set last_name' do
        user.full_name = 'Test User'
        user.save
        expect(user.last_name).to eq('User')
      end
    end
  end
end
