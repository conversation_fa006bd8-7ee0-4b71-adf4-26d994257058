require 'rails_helper'
include CompanyUserHelper

describe CustomSurvey::Response do  
  create_company_and_user

  let(:response) { CustomSurvey::Response.create!(name: "Response Spec Test", status: "Response Status Spec Test", contributor: Contributor.last) }

  context 'for validation of presence' do
    it 'should validate presence of name' do
      response.name = nil
      response.valid?
      expect(response.errors[:name][0]).to eq 'is required'
    end

    it 'should validate presence of status' do
      response.status = nil
      response.valid?
      expect(response.errors[:status][0]).to eq 'is required'
    end

    it 'should validate presence of contributor' do
      response.contributor_id = nil
      response.valid?
      expect(response.errors[:contributor_id][0]).to eq 'is required'
    end
  end

  context 'for valid belongs_to associations' do
    it 'should belong to company' do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end

    it 'should belong to workspace' do
      assoc = described_class.reflect_on_association(:workspace)
      expect(assoc.macro).to eq :belongs_to
    end

    it 'should belong to help_ticket' do
      assoc = described_class.reflect_on_association(:help_ticket)
      expect(assoc.macro).to eq :belongs_to
    end

    it 'should belong to custom_survey' do
      assoc = described_class.reflect_on_association(:custom_survey)
      expect(assoc.macro).to eq :belongs_to
    end

    it 'should belong to contributor' do
      assoc = described_class.reflect_on_association(:contributor)
      expect(assoc.macro).to eq :belongs_to
    end
  end

  context 'for invalid belongs_to associations' do
    it 'should not belong to a non-existent association' do
      assoc = described_class.reflect_on_association(:non_existent)
      expect(assoc).to be_nil
    end
  
    it 'should not have has_many association with company' do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).not_to eq :has_many
    end
  
    it 'should not have has_many association with workspace' do
      assoc = described_class.reflect_on_association(:workspace)
      expect(assoc.macro).not_to eq :has_many
    end

    it 'should not have has_many association with help_ticket' do
      assoc = described_class.reflect_on_association(:help_ticket)
      expect(assoc.macro).not_to eq :has_many
    end
  
    it 'should not have has_many association with custom_survey' do
      assoc = described_class.reflect_on_association(:custom_survey)
      expect(assoc.macro).not_to eq :has_many
    end

    it 'should not have has_many association with contributor' do
      assoc = described_class.reflect_on_association(:contributor)
      expect(assoc.macro).not_to eq :has_many
    end
  end
end
