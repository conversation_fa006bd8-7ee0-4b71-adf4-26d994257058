require 'rails_helper'
require 'sidekiq/testing'
Sidekiq::Testing.fake!

RSpec.describe CompanyUser, type: :model do
  let!(:user) { subject.user }
  let(:company) { FactoryBot.create(:company) }
  let!(:custom_form) { company.custom_forms.find_by(default: true, company_module: 'company_user') }
  let!(:company_user) { create(:company_user, custom_form: custom_form) }

  let!(:phone_field) { custom_form.custom_form_fields.find_by(name: "mobile_phone") }

  let!(:phone_value) { create(:custom_form_value,
                              custom_form_field: phone_field,
                              value_str: 'US,(*************',
                              custom_form: custom_form,
                              module: company_user,
                              company: company) }

  let(:user_attributes) do
    {
      email: "<EMAIL>",
      password: "password",
      first_name: "Lucille",
      last_name: "Bluth",
      terms_of_services: true,
      sign_in_count: 5
    }
  end

  context "with various company users" do
    subject { create(:company_user, company_user_attributes) }

    let(:company_user_attributes) do
      {
        company_id: company.id,
        granted_access_at: DateTime.current
      }
    end

    it "will fetch company_users to whom access is granted" do
      company_user_without_access = company.company_users.new(granted_access_at: nil)
      user2 = User.create(user_attributes)
      company_user_without_access.user = user2
      company_user_without_access.save!
      company.company_users << subject
      expect(company.company_users.access_granted.first).to eq subject
    end
  end

  context "with an admin" do
    subject { create(:company_user, company: company) }
    let!(:admin) { company.groups.find_by(name: 'Admins').group_members << GroupMember.new(contributor: subject.contributor) }

    it "should return true if company_user is admin" do
      expect(subject.is_admin?).to eq true
    end

    it "should be included in the admins" do
      expect(company.admin_company_users.first).to eq(subject)
    end
  end

  context 'json abbreviated' do
    it 'abbravites the compnay users json' do
      abbreviation = company_user.as_json_abbreviated
      expect(abbreviation[:id]).to eq(company_user.id)
      expect(abbreviation[:name]).to eq(company_user.full_name)
    end
  end

  context 'network monitoring' do
    it 'checks privilege network monitoring for type nil' do
      expect(company_user.privilege_network_monitoring).to be_nil
    end

    it 'checks privilege network monitoring for type write' do
      allow_any_instance_of(CompanyUser).to receive(:privileged_write_network_monitoring).and_return(true)
      expect(company_user.privilege_network_monitoring).to eq ('write')
    end

    it 'checks privilege network monitoring for type read' do
      allow_any_instance_of(CompanyUser).to receive(:privileged_read_network_monitoring).and_return(true)
      expect(company_user.privilege_network_monitoring).to eq ('read')
    end
  end

  context 'invite user' do
    it 'invites the user' do
     expect(company_user.invite_user).to eq(true)
    end
  end

  context 'sms number' do
    it 'gives mobile number' do
     expect(company_user.sms_number).to be_present
    end
  end

  context 'helpdesk agent?' do
    it 'checks the agent present for the helpdesk' do
      expect(company_user.helpdesk_agent?).to eq(false)
    end
  end

  context 'archived' do
    it 'checks user is archieved?' do
      expect(company_user.archived?).to eq(false)
    end
  end

  context 'experience points' do
    it 'returns the experience point summary' do
      expect(company_user.experience_points_summary).to be_nil
    end
  end

  context 'verification required' do
    it 'enables the mfa' do
      expect(company_user.mfa_verification_required?).to eq(false)
    end
  end
end
