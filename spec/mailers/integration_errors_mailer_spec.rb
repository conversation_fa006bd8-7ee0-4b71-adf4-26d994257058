require "rails_helper"
include CompanyUserHelper

RSpec.describe IntegrationErrorsMailer, :type => :mailer do
  create_company_and_user

  describe "integration_errors" do
    let!(:mail) { IntegrationErrorsMailer.integration_errors }
    let(:attributes) do
      {
        event_name: "ERROR",
        metadata: "{\"error_code\":\"INVALID_CREDENTIALS\",\"error_message\":\"the provided credentials were not correct\",\"error_type\":\"ITEM_ERROR\",\"exit_status\":null,\"institution_id\":\"ins_3\",\"institution_name\":\"Chase\",\"institution_search_query\":null,\"link_session_id\":\"408d88d3-5071-4bbc-966b-868ff3ab52a1\",\"mfa_type\":null,\"view_name\":null,\"request_id\":null,\"timestamp\":\"2019-11-04T20:01:51.389Z\"}",
        company_user_id: CompanyUser.first.id,
        created_at: Time.now
      }
    end

    let!(:plaid_event_log) { Logs::PlaidEventLog.create(attributes) }

    it "renders the headers" do
      expect(mail.subject).to eq("Integration errors for #{Date.today.strftime("%m-%d-%Y")}")
      expect(mail.to).to eq(["<EMAIL>"])
    end

    it "should render errors from Plaid from the last day" do
      expect(mail.body).to include("ERROR")
      expect(mail.body).to include("INVALID_CREDENTIALS")
      expect(mail.body).to include("the provided credentials were not correct")
      expect(mail.body).to include("Chase")
      expect(mail.body).to include(CompanyUser.first.full_name)
      expect(mail.body).to include(CompanyUser.first.email)
      expect(mail.body).to include(CompanyUser.first.company.name)
      expect(mail.body).to include(plaid_event_log.created_at.strftime("%m-%d-%Y %I:%M%p"))
    end
  end
end