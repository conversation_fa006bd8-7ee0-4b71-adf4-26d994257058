require "rails_helper"
include CompanyUserHelper

RSpec.describe ErrorNotificationMailer, :type => :mailer do
  create_company_and_user

  describe "#send_error" do
    let!(:mail) { described_class.send_error(event).deliver_now }
    let(:event) { create(:api_event,
                         company: company,
                         class_name: "Warranty::DellService",
                         detail: "This is the detail",
                         error_detail: "Boom") }

    it "renders the headers" do
      expect(mail.subject).to eq("Error discovered in #{event.name}")
      expect(mail.to).to eq(["<EMAIL>"])
      expect(mail.from).to eq(["<EMAIL>"])
    end

    it "should render errors from Plaid from the last day" do
      expect(mail.body.encoded).to match("This is the detail")
      expect(mail.body.encoded).to match("Boom")
      expect(mail.body.encoded).to match(company.name)
      expect(mail.body.encoded).to match(event.status.to_s)
      expect(mail.body.encoded).to match(event.activity.to_s)
    end
  end
end