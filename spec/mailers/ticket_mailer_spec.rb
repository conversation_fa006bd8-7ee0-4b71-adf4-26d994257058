require "rails_helper"
include CompanyUserHelper

RSpec.describe TicketMailer, :type => :mailer do
  create_company_and_user(subdomain: 'test')

  let!(:custom_form) { workspace.custom_forms.first }

  describe "receive" do
    %w{
        6h37ds5qrtu0gdn1soar84f7aepeqgrk9r9k0vg1
        7p573dmak5s9b50ba8gajnt0tk65us2bd5rebmo1
        7vcnabmc1807es6n1suhfgt792kvdahl2m9g0fg1
        qs1o1f6hlnpiv0krpgha6728jdt2bobgoqjj82g1
        cg0hrbp0aglhtabhjr6g8kge2jqq9mteoe1h0bg1
        vdq87bkhj2oh8chfq12de6b9ilq2f336pa6hs801
        dpgc04lmq0o08skh9enhri3d5n69vcakfk097b81
        iqtkj3gmo3foab3oj9nqqogngli3ekf0414dnp81
      }.each do |id|
      context "with an email #{id}" do
        let(:email_text) { File.read(Rails.root.join("spec/data/#{id}")) }

        it "renders the headers" do
          s3_message_id = 'bkhj2oh8chfq12de6b9ilq2f336'
          ticket_email, attachments = TicketMailer.new.receive(email_text, s3_message_id)
          expect(ticket_email).to be_present
          expect(ticket_email.body_text).to be_present
          expect(ticket_email.body_text).not_to include("style")
          expect(ticket_email.body_text).not_to include("font-style")
          expect(ticket_email.body_text).not_to include("margin-top")
          if id == "vdq87bkhj2oh8chfq12de6b9ilq2f336pa6hs801"
            expect(ticket_email.body_html).to be_blank
          else
            expect(ticket_email.body_html).to be_present
          end
          expect(ticket_email.to).to be_present
          expect(ticket_email.company).to be_present
          expect(ticket_email.from).to be_present
        end
      end
    end
  end

  describe "ensure format isn't messed up" do
    context "with message up html" do
      it "should properly format the html" do
        text = """
          \n\n\n\n\n&lt;/head&gt;\n&lt;body lang=\"EN-US\" link=\"blue\" vlink=\"purple\"&gt;\n
          &lt;div class=\"WordSection1\"&gt;\nHi there&lt;/div&gt;
          &lt;/body&gt;\n&lt;/html&gt;
          """
        mailer = described_class.new
        formatted = mailer.help_paragraph_formatted(text)
        html = "<div class=\"WordSection1\">\nHi there</div>"
        expect(formatted.strip).to eq(html.strip)
      end
    end
  end

  describe "receive with attachment" do
    context "with an email 8totvqkqi8hji8u9h3dffpccqkq6f12te16tto01" do
      let(:id) { '0dmiu2ksgbjsd3ledl02kaj8ponnp7i51r846v81' }
      let(:email_text) { File.read(Rails.root.join("spec/data/#{id}")) }

      it "renders the headers" do
        s3_message_id = 'miu2ksgbjsd3ledl02kaj8'
        ticket_email, attachments = TicketMailer.new.receive(email_text, s3_message_id)
        expect(ticket_email).to be_present
        expect(ticket_email.message_id).to be_present
        expect(ticket_email.message_id).to include("@")
        expect(ticket_email.body_text).to be_present
        expect(ticket_email.to).to be_present
        expect(ticket_email.company).to be_present
        expect(ticket_email.from).to be_present
        attachments = ticket_email.ticket_email_attachments
        expect(attachments).to be_present
        expect(attachments.size).to eq(2)
        expect(attachments[0].name).to eq('image.png')
        expect(attachments[1].name).to eq('factura.pdf')
      end
    end
  end

  describe "receive with custom email that is a Genuity email address" do
    context "with an email ptg2c67bk4luop98ogdk8greaiqud1s1cmhj03g8" do
      let(:id) { 'ptg2c67bk4luop98ogdk8greaiqud1s1cmhj03g8' }
      let(:email_text) { File.read(Rails.root.join("spec/data/#{id}")) }
      let!(:custom_email) { 
        HelpdeskCustomEmail.create!(company: company, 
                                    email: "<EMAIL>",
                                    helpdesk_custom_form: custom_form.helpdesk_custom_form) 
      }

      it "renders the headers" do
        s3_message_id = 'tg2c67bk4luop98o'
        ticket_email, attachments = TicketMailer.new.receive(email_text, s3_message_id)
        expect(ticket_email).to be_present
        expect(ticket_email.to).to eq("<EMAIL>, <EMAIL>")
        expect(ticket_email.message_id).to be_present
        expect(ticket_email.message_id).to include("@")
        expect(ticket_email.body_text).to be_present
        expect(ticket_email.company).to be_present
        expect(ticket_email.from).to be_present
        expect(ticket_email.custom_form).to eq(custom_form)
        attachments = ticket_email.ticket_email_attachments
        expect(attachments).to be_present
        expect(attachments.size).to eq(1)
        expect(attachments[0].name).to eq('gregs-fishing-barge-Locations_Data_SAVE.xlsx')
      end
    end
  end

  describe "receive with normal email from a forward" do
    context "with an email ud4mb99sp7t6hjkh3rm87p7rce1c4e3462u2u481" do
      let(:id) { 'ud4mb99sp7t6hjkh3rm87p7rce1c4e3462u2u481' }
      let(:email_text) { File.read(Rails.root.join("spec/data/#{id}")) }
      let(:helpdesk_form) { 
        custom_form.helpdesk_custom_form&.helpdesk_custom_email&.destroy!
        custom_form.helpdesk_custom_form
      }
      let!(:custom_email) { HelpdeskCustomEmail.create!(company: company, email: "<EMAIL>", helpdesk_custom_form: helpdesk_form) }

      it "renders the headers" do
        s3_message_id = 'd4mb99sp7t6hjkh3rm87p7rce'
        ticket_email, attachments = TicketMailer.new.receive(email_text, s3_message_id)
        expect(ticket_email).to be_present
        expect(ticket_email.to).to eq("<EMAIL>")
        expect(ticket_email.message_id).to be_present
        expect(ticket_email.message_id).to include("@")
        expect(ticket_email.body_text).to be_present
        expect(ticket_email.company).to be_present
        expect(ticket_email.from).to be_present
        expect(ticket_email.custom_form).to eq(custom_form)
        attachments = ticket_email.ticket_email_attachments
        expect(attachments).to be_present
        expect(attachments.size).to eq(5)
      end
    end
  end

  describe "receive from a mailer daemon" do
    context "with an email cp5a9or9o8t2vpdek65a7tio6786tfljts1p1bg1" do
      let(:id) { 'cp5a9or9o8t2vpdek65a7tio6786tfljts1p1bg1' }
      let(:email_text) { File.read(Rails.root.join("spec/data/#{id}")) }
      let(:helpdesk_form) { 
        custom_form.helpdesk_custom_form&.helpdesk_custom_email&.destroy!
        custom_form.helpdesk_custom_form
      }
      let!(:custom_email) { HelpdeskCustomEmail.create!(company: company, email: "<EMAIL>", helpdesk_custom_form: helpdesk_form) }

      it "renders the headers" do
        s3_message_id = 'cp5a9or9o8t2vpdek6'
        ticket_email, attachments, is_auto_reply = TicketMailer.new.receive(email_text, s3_message_id)
        expect(is_auto_reply).to be_truthy
      end
    end
  end

  describe "receive from a mailer no reply" do
    context "with emails" do
      let(:ids) { ['cp5a9or9o8t2vpdek65a7tio6786tfljts1p1bg1mq1', 'cp5a9or9o8t2vpdek65a7tio6786tfljts1p1bg1mq2', 'cp5a9or9o8t2vpdek65a7tio6786tfljts1p1bg1mq3'] }
      let(:email_texts) { ids.map { |id| File.read(Rails.root.join("spec/data/#{id}")) } }
      let(:helpdesk_form) { 
        custom_form.helpdesk_custom_form&.helpdesk_custom_email&.destroy!
        custom_form.helpdesk_custom_form
      }
      let!(:custom_email) { HelpdeskCustomEmail.create!(company: company, email: "<EMAIL>", helpdesk_custom_form: helpdesk_form) }
    
      it "renders the headers" do
        s3_message_id = 'p5a9or9o8t2vpdek65a7t'
        email_texts.each do |email_text|
          ticket_email, attachments, is_auto_reply = TicketMailer.new.receive(email_text, s3_message_id)
          expect(is_auto_reply).to be_truthy
        end
      end
    end
  end

  describe "#to_custom_form" do
    let(:custom_form1) { CustomForm.create(company: company, company_module: 'helpdesk', form_name: 'Form1') }
    let(:custom_form2) { CustomForm.create(company: company, company_module: 'helpdesk', form_name: 'Form2') }
    let(:ticket_email) { TicketEmail.new }

    context "with a simple custom email and single form" do
      let(:helpdesk_form) { 
        custom_form.helpdesk_custom_form&.helpdesk_custom_email&.destroy!
        custom_form.helpdesk_custom_form
      }
      let!(:custom_email) { HelpdeskCustomEmail.create!(company: company, email: "<EMAIL>", helpdesk_custom_form: helpdesk_form) }
      let(:email_addresses) { [ '<EMAIL>', '<EMAIL>' ]}
      let(:ticket_email) { TicketEmail.new }

      subject { described_class.new.set_custom_form(email_addresses, ticket_email) }

      it "sets the correct custom form" do
        subject
        expect(ticket_email.custom_form).to eq(custom_form)
      end
    end

    context "with a default form" do
      let!(:helpdesk_form1) { 
        HelpdeskCustomForm.where(custom_form: custom_form1).destroy_all
        f = HelpdeskCustomForm.create!(custom_form: custom_form1, email: '<EMAIL>', helpdesk_custom_email: custom_email1) 
        f.update_column('created_at', 5.minutes.ago)
        f
      }
      let!(:helpdesk_form2) { 
        HelpdeskCustomForm.where(custom_form: custom_form2).destroy_all
        HelpdeskCustomForm.create!(custom_form: custom_form2, email: '<EMAIL>', helpdesk_custom_email: custom_email2, default: true) 
      }
      let(:email_addresses) { [ '<EMAIL>', '<EMAIL>', '<EMAIL>' ]}
      let!(:custom_email1) { HelpdeskCustomEmail.create!(company: company, name: "Alan") }
      let!(:custom_email2) { HelpdeskCustomEmail.create!(company: company, name: "Mohsin") }

      subject { described_class.new.set_custom_form(email_addresses, ticket_email) }

      it "sets the default custom form" do
        subject
        expect(ticket_email.custom_form.id).to eq(custom_form2.id)
      end
    end

    context "without default form" do
      let!(:helpdesk_form1) { 
        HelpdeskCustomForm.where(custom_form: custom_form1).destroy_all
        f = HelpdeskCustomForm.create!(custom_form: custom_form1, email: '<EMAIL>', helpdesk_custom_email: custom_email1) 
        f.update_column('created_at', 5.minutes.ago)
        f
      }
      let!(:helpdesk_form2) { 
        HelpdeskCustomForm.where(custom_form: custom_form2).destroy_all
        HelpdeskCustomForm.create!(custom_form: custom_form2, email: '<EMAIL>', helpdesk_custom_email: custom_email2) 
      }
      let(:email_addresses) { [ '<EMAIL>', '<EMAIL>', '<EMAIL>' ]}
      let!(:custom_email1) { HelpdeskCustomEmail.create!(company: company, name: "Alan") }
      let!(:custom_email2) { HelpdeskCustomEmail.create!(company: company, name: "Mohsin") }

      subject { described_class.new.set_custom_form(email_addresses, ticket_email) }

      it "sets the oldest custom form" do
        subject
        expect(ticket_email.custom_form.id).to eq(custom_form1.id)
      end
    end
  end
end
