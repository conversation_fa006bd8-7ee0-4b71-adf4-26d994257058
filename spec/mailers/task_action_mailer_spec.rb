require "rails_helper"
include CompanyUserHelper

RSpec.describe TaskActionMailer, :type => :mailer do
  create_company_and_user

  describe 'action_email' do
    let(:mail) { TaskActionMailer.action_email(action, help_ticket) }
    let(:help_ticket) { create(:help_ticket, company: company, priority: 'low') }
    let(:automated_task) { task = AutomatedTasks::AutomatedTask.create(workspace: company.default_workspace ) }
    let(:action) {
      action_type = AutomatedTasks::ActionType.find_by(name: "send an [email]")
      value_data = {
        recipients: "<EMAIL>",
        subject: 'Test Email',
        body: '<div>This is an email.</div>',
      }
      task_action = AutomatedTasks::TaskAction.create(
                      action_type: action_type,
                      value: value_data.to_json,
                      automated_task: automated_task
                    )
      task_action
    }
    let(:data) { JSON.parse(action.value) }
    let(:email_data) { AutomatedTasks::Actions::EmailActionService.new(help_ticket, action, ["<EMAIL>"]).email_data(data, "<EMAIL>") }
    let(:custom_form_attachments) { [{ "file_name" =>  'laptop.jpg', "file_url" => 'spec/data/laptop.jpg', "file" => "abc" }] }
    let(:email_data_with_attachments) { email_data.merge('custom_form_attachments': custom_form_attachments).as_json }

    subject { described_class.action_email(action, help_ticket, ['<EMAIL>'], email_data_with_attachments) }

    context "with good data" do
      let(:body) { '<div>This is an email.</div>' }

      it 'renders the subject' do
        expect(subject.subject).to eql('Test Email')
      end

      it 'renders the receiver email' do
        expect(subject.to).to eql(['<EMAIL>'])
      end

      it 'renders the sender email' do
        expect(subject.from).to eql(["helpdesk@#{company.subdomain}.#{Rails.application.credentials.root_domain}"])
      end

      it 'assigns @name' do
        expect(subject.body.encoded).to match('This is an email.')
      end
    end

    context "with no body" do
      let(:body) { '' }

      it 'never sends the email' do
        expect(ActionMailer::Base.deliveries.last).to be_blank
      end
    end

    context 'with attachments' do
      before do
        allow(action).to receive(:value).and_return({
          recipients: '<EMAIL>',
          subject: 'Test Attachment Email',
          body: 'This is an email. {ticket_attachments}',
        }.to_json)
        allow_any_instance_of(TaskActionMailer).to receive(:email_data).and_return(email_data_with_attachments)
      end

      it 'renders attachments and create email logs if attachments are less than 40MB' do
        allow_any_instance_of(TaskActionMailer).to receive(:is_size_limit_exceeded?).and_return(false)

        expect(subject.attachments.length).to eq(1)
        expect(subject.attachments[0].filename).to eq('laptop.jpg')
        expect(Logs::EmailLog.where(subject: 'Test Attachment Email', status: 'success')).to be_present
      end

      it 'never renders attachments and just create email logs if attachments are greater than 40MB' do
        allow_any_instance_of(TaskActionMailer).to receive(:is_size_limit_exceeded?).and_return(true)

        expect(subject.attachments).to eq(nil)
        expect(Logs::EmailLog.where(subject: 'Test Attachment Email', status: 'size_restriction')).to be_present
      end
    end
  end

  describe 'from_email' do
    let(:mail) { TaskActionMailer.action_email(action, help_ticket) }
    let(:help_ticket) { create(:help_ticket, company: company, priority: 'low') }
    let(:automated_task) { task = AutomatedTasks::AutomatedTask.create(workspace: company.default_workspace ) }
    let!(:custom_email) {
      helpdesk_form.helpdesk_custom_email ||= HelpdeskCustomEmail.create(company: company)
      helpdesk_form.save
      helpdesk_form.helpdesk_custom_email
    }
    let(:helpdesk_form) { help_ticket.custom_form.helpdesk_custom_form }
    let(:genuity_domain) { "#{company.subdomain}.#{Rails.application.credentials.root_domain}" }
    let(:action) {
      action_type = AutomatedTasks::ActionType.find_by(name: "send an [email]")
      value_data = {
        recipients: "<EMAIL>",
        subject: 'Test Email',
        body: '<div>This is an email.</div>',
      }
      task_action = AutomatedTasks::TaskAction.create(
                      action_type: action_type,
                      value: value_data.to_json,
                      automated_task: automated_task
                    )
      task_action
    }
    let(:data) { JSON.parse(action.value) }
    let(:email_data) { AutomatedTasks::Actions::EmailActionService.new(help_ticket, action, ["<EMAIL>"]).email_data(data, "<EMAIL>") }
    subject {
      subj = described_class.new
      subj.root = help_ticket
      subj.email_data = email_data
      email_data['from_email']
    }

    context "with a custom email and name but not verified" do
      before do
        custom_email.email = "<EMAIL>"
        custom_email.name = "BIG dogs"
        custom_email.verified = false
        custom_email.save
        helpdesk_form.email = "support@#{company.subdomain}.#{Rails.application.credentials.root_domain}"
        helpdesk_form.save
      end

      it "returns a good email" do
        expect(subject).to eq("BIG dogs <support@#{company.subdomain}.#{Rails.application.credentials.root_domain}>")
      end
    end

    context "with a custom email and name" do
      before do
        custom_email.email = "<EMAIL>"
        custom_email.name = "BIG dogs"
        custom_email.verified = true
        custom_email.save
      end

      it "returns a good email" do
        expect(subject).to eq("BIG dogs <<EMAIL>>")
      end
    end

    context "with just a custom email" do
      before do
        custom_email.email = "<EMAIL>"
        custom_email.name = nil
        custom_email.verified = true
        custom_email.save
      end

      it "returns a good email" do
        expect(subject).to eq("<EMAIL>")
      end
    end

    context "with just a default email" do
      before do
        custom_email.email = nil
        custom_email.name = nil
        custom_email.save
        helpdesk_form.email = "support@#{genuity_domain}"
        helpdesk_form.save
      end

      it "returns a good email" do
        expect(subject).to eq("support@#{genuity_domain}")
      end
    end

    context "with an erroneous default email" do
      before do
        custom_email.email = nil
        custom_email.name = nil
        custom_email.verified = nil
        custom_email.save
        helpdesk_form.email = "null@#{genuity_domain}"
        helpdesk_form.save
      end

      it "returns a good email" do
        expect(subject).to eq("helpdesk@#{genuity_domain}")
      end
    end

    context "with an erroneous default email" do
      before do
        custom_email.email = nil
        custom_email.name = nil
        custom_email.save
        helpdesk_form.email = "null@#{genuity_domain}"
        helpdesk_form.save
      end

      it "returns a good email" do
        expect(subject).to eq("helpdesk@#{genuity_domain}")
      end
    end

    context "with no from email" do
    end
  end
end

