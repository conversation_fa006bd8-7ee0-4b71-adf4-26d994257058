require 'rails_helper'

describe HelpTicket do
  include HelpTicketHelper
  let(:company) { create(:company, :with_locations) }
  let(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "helpdesk") }
  let(:user) { create(:user) }
  let(:company_user) { create(:company_user, user: user, company: company, helpdesk_agent: true) }
  let(:asset) { create(:managed_asset, company: company, location: company.locations.first, active_ticket_id: '1') }
  let(:help_ticket6) { create(:help_ticket, workspace: company.default_workspace) }
  let!(:time_spent) { create(:time_spent, help_ticket: help_ticket6, hours_spent: '2', minutes_spent: '15') }
  let(:help_ticket_comment) { create(:help_ticket_comment, help_ticket: help_ticket6) }
  let(:project_task) { create(:project_task, help_ticket_id: help_ticket6.id, due_at: Date.today) }
  let(:help_ticket5) { create(:help_ticket, workspace: company.default_workspace) }

  let(:attributes) do
    {
      email:  company_user.user.email,
      company_id: company.id,
      creator_id: company_user.id,
      workspace_id: company.default_workspace.id,
      custom_form_id: custom_form.id,
    }
  end

  let(:user2_attributes) do
    {
      first_name:   "Alan",
      last_name:    "Salganik",
      email:        "<EMAIL>",
      password:     "12312312"
    }
  end

  let(:user2) { create(:user, user2_attributes) }
  let(:company_user2) { create(:company_user, user: user2, company: company, helpdesk_agent: true) }

  let(:user3_attributes) do
    {
      first_name:   "Bob",
      last_name:    "Lasch",
      email:        "<EMAIL>",
      password:     "12312312"
    }
  end

  let(:user3) { create(:user, user3_attributes) }
  let(:company_user3) { create(:company_user, user: user3, company: company, helpdesk_agent: false) }

  let(:user4_attributes) do
    {
      first_name:   "John",
      last_name:    "Doe",
      email:        "<EMAIL>",
      password:     "12312312"
    }
  end

  let(:user4) { create(:user, user4_attributes) }
  let(:company_user4) { create(:company_user, user: user4, company: company, helpdesk_agent: false) }

  context 'with valid attributes' do
    it "is valid" do
      help_ticket = described_class.new(attributes)
      expect(help_ticket).to be_valid
    end
  end

  context 'with invalid company' do
    it "is invalid" do
      help_ticket = described_class.new(attributes)
      help_ticket.company_id = ''
      expect(help_ticket).to be_invalid
    end
  end

  context 'with ticket creator as email' do
    let!(:help_ticket4) {
      ticket = FactoryBot.create(:help_ticket, company: company, workspace: company.default_workspace)
      created_by = ticket.custom_form.custom_form_fields.find_by(name: 'created_by')
      value = CustomFormValue.create(module: ticket, custom_form_field: created_by, company: company, value_int: company_user4.contributor_id)
      ticket
    }
    it "should update ticket creator if company user with the same email exists" do
      company_user4.update_help_ticket_creator
      expect(help_ticket4.custom_form_values.first.value_int).to eq(company_user4.contributor_id)
    end
  end

  describe "save_with_ticket_number" do
    let(:help_ticket) { described_class.new(attributes) }

    it "assigns a good ticket number" do
      help_ticket.save!
      expect(help_ticket.ticket_number).to be_present
    end

    context "with a bad ticket number" do
      before do
        create(:help_ticket, company: company, workspace: company.default_workspace, ticket_number: 1, custom_form: custom_form)
        company.help_ticket_number = company.help_tickets.last.ticket_number
        company.save!
        help_ticket.save!
      end

      it "assigns a good number to the ticket" do
        expect(help_ticket.ticket_number).to be_present
      end
    end

    context "with a missing ticket number" do
      before do
        company.help_ticket_number = nil
        company.save!(validate: false)
        help_ticket.save!
      end

      it "assigns a good number to the ticket" do
        expect(help_ticket.ticket_number).to be_present
      end
    end
  end

  describe "scopes" do
    let!(:help_ticket1) {
      ticket_params = {
        'Subject' => "Ticket 1",
        'Status' => 'Open',
        workspace: company.default_workspace,
        custom_form_id: custom_form.id,
      }
      ticket = create_ticket(ticket_params)
      ticket.company = company
      ticket.save!
      ticket
    }
    let!(:help_ticket2) {
      ticket_params = {
        'Subject' => "Ticket 2",
        'Status' => 'In Progress',
        workspace: company.default_workspace,
        custom_form_id: custom_form.id,
      }
      ticket = create_ticket(ticket_params)
      ticket.company = company
      ticket.save!
      ticket
    }
    let!(:help_ticket3) {
      ticket_params = {
        'Subject' => "Ticket 3",
        'Status' => 'Closed',
        workspace: company.default_workspace,
        custom_form_id: custom_form.id,
      }
      ticket = create_ticket(ticket_params)
      ticket.archived = true
      ticket.company = company
      ticket.save!
      ticket
    }

    it ".unclosed returns only help tickets that are open and in progress" do
      expect(company.help_tickets.unclosed.count).to eq(2)
    end

    it ".unarchived returns only help tickets that are unarchived" do
      expect(company.help_tickets.unarchived.count).to eq(2)
      expect(company.help_tickets.unclosed.unarchived.count).to eq(2)
      expect(company.help_tickets.unarchived.last).to eq(help_ticket2)
    end
  end

  it 'will reutrn the last added comment date' do
    last_comment = help_ticket_comment.help_ticket.last_comment_at
    expect(last_comment.to_date).to eq(Date.today)
  end

  it 'will return the due date when the task complete' do
    due_date = project_task.help_ticket.due_at
    expect(due_date.to_date).to eq(Date.today)
  end

  it 'will return task completion without due date' do
    project_task.due_at = nil
    project_task.save!
    expect(project_task.help_ticket.due_at).to eq(nil)
  end

  it 'will return the total time without time spent' do
    expect(help_ticket5.total_time_spent).to eq('0 hr 0 min')
  end

  it 'will return the total time with time spent' do
    expect(help_ticket6.total_time_spent).to eq('2 hrs 15 mins')
  end

  it 'will return date if comment' do
    expect(help_ticket5.last_comment_at).to eq(nil)
  end

  it 'will return the help ticket response as json' do
    help_ticket_json = help_ticket6.as_json
    expect(help_ticket_json['id']).to eq(help_ticket6.id)
  end
end
