require 'rails_helper'
include CompanyUserHelper

RSpec.describe CreateSlackTicketWorker, type: :worker do
  create_company_and_user
  let(:config) { create(:slack_config, company: company, workspace: company.default_workspace) }
  let(:payload) {
    {
      'type' => 'view_submission',
      'user' => {
        'id' => 'U04AQ4933FT',
        'username' => 'john_liam',
        'name' => 'john_liam',
        'team_id' => config.team_id,
      },
      'view' => {
        'callback_id' => 'create_ticket',
        'private_metadata' => "{\"help_ticket_id\":null,\"channel_id\":\"D04AQ62708H\",\"thread_ts\":null,\"global_shortcut\":true,\"is_dm_ticket\":null}",
        'blocks' => [
          {
            'type' => 'input',
            'block_id' => 'KVH7',
            'label' =>  {
              'type' => 'plain_text',
              'text' => 'Subject',
            },
            'optional' => false,
            'dispatch_action' => false,
            'element' =>  {
              'type' => 'plain_text_input',
              'action_id' => 'plain_text_input-action',
              'initial_value' => '',
              'dispatch_action_config' =>  {
                'trigger_actions_on' =>  ['on_enter_pressed']
              }
            }
          },
          {
            'type' => 'input',
            'block_id' => 'V4b',
            'label' =>  {
              'type' => 'plain_text',
              'text' => 'Priority',
              'emoji' => true
            },
            'optional' => false,
            'dispatch_action' => false,
            'element' => {
              'type' => 'static_select',
              'action_id' => 'static_select-action',
              'placeholder' =>  {
                'type' => 'plain_text',
                'text' => 'Select any priority',
                'emoji' => true
              },
              'initial_option' =>  {
                'text' =>  {
                  'type' => 'plain_text',
                  'text' => 'Low',
                  'emoji' => true
                },
                'value' => 'low'
              },
              'options' =>  [
                {
                  'text' => {
                    'type' => 'plain_text',
                    'text' => 'Low',
                    'emoji' => true
                  },
                  'value' => 'low'
                }, {
                  'text' => {
                    'type' => 'plain_text',
                    'text' => 'Medium',
                    'emoji' => true
                  },
                  'value' => 'medium'
                }, {
                  'text' => {
                    'type' => 'plain_text',
                    'text' => 'High',
                    'emoji' => true
                  },
                  'value' => 'high'
                }
              ]
            }
          },
          {
            'type' => 'input',
            'block_id' => 'eAXZ',
            'label' => {
              'type' => 'plain_text',
              'text' => 'Description',
              'emoji' => true
            },
            'optional' => true,
            'dispatch_action' => false,
            'element' => {
              'type' => 'plain_text_input',
              'action_id' => 'plain_text_input-action',
              'initial_value' => '',
              'multiline' => true,
              'dispatch_action_config' => {
                'trigger_actions_on' => ['on_enter_pressed']
              }
            }
          }
        ],
        'state' => {
          'values' => {
            'KVH7' => {
              'plain_text_input-action' => {
                'type' => 'plain_text_input',
                'value' => 'First ticket from slack using genuity bot'
              }
            },
            'V4b' => {
              'static_select-action' => {
                'type' => 'static_select',
                'selected_option' => {
                  'text' => {
                    'type' => 'plain_text',
                    'text' => 'low',
                    'emoji' => true
                  },
                'value' => 'low'
                }
              }
            },
            'eAXZ' => {
              'plain_text_input-action' => {
                'type' => 'plain_text_input',
                'value' => 'Welcome to Genuity App'
              }
            }
          }
        },
      },
      'token' => config.access_token,
      'channel_id' => config.channel_id,
      'team_id' => config.team_id,
    }
  }

  describe '#perform' do
    before { allow_any_instance_of(Integrations::Slack::Client).to receive(:send_ephemeral_message).and_return(true) }
    it 'creates ticket for existing user' do
      allow_any_instance_of(Integrations::Slack::Client).to receive(:get_user_email).and_return(company_user.email)

      expect(company.help_tickets.count).to eq(0)
      subject.class.new.perform(config.id, payload)

      ticket = company.help_tickets.last
      priority = ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: 'priority'}).value_str
      description = ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: 'description'}).value_str

      expect(company.help_tickets.count).to eq(1)
      expect(priority).to eq('low')
      expect(description).to eq('Welcome to Genuity App')
      expect(ticket.subject).to eq('First ticket from slack using genuity bot')
      expect(ticket.creators[0].id).to eq(company_user.id)
    end

    it 'creates ticket for guests user' do
      allow_any_instance_of(Integrations::Slack::Client).to receive(:get_user_email).and_return('<EMAIL>')

      expect(company.help_tickets.count).to eq(0)
      subject.class.new.perform(config.id, payload)

      ticket = company.help_tickets.last
      guest_user = Guest.find_by(email: '<EMAIL>')
      priority = ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: 'priority'}).value_str
      description = ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: 'description'}).value_str

      expect(company.help_tickets.count).to eq(1)
      expect(priority).to eq('low')
      expect(description).to eq('Welcome to Genuity App')
      expect(ticket.subject).to eq('First ticket from slack using genuity bot')
      expect(ticket.creator_contributors[0].id).to eq(guest_user.contributor_id)
    end
  end
end
