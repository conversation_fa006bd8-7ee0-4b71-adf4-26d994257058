require 'rails_helper'

RSpec.describe Integrations::Kaseya::SyncDataWorker, type: :worker do
  describe '#assets' do
    before(:each) do
      @company =  FactoryBot.create(:company)
      @kaseya_config = FactoryBot.create(:kaseya_config, company: @company)

      @refresh_token = {
        'expires_in' => 3600,
        'access_token' => 'cvsdhbcdsbbhbn35h34j/sbcjhsd',
        'refresh_token' => 'jcbdhjsnxjhvc74yfbhjbc'
      }

      @device = {
        'DisplayName': 'HP78E7D1B3EFB7',
        'manufacturer': 'Microsoft',
        'IPAddresses': "***************",
        'OSName': 'Microsoft Windows',
        'AssetTypeId': "1002",
        'MACAddresses': "78:E7:D1:B3:EF:B7",
      }

      @asset_type = {
        'AssetTypeId': '1002',
        'AssetTypeName': 'Laptop'
      }

      @agents_data = {
        "DisplayName": "Hp.techs.workstations",
        'MacAddress': "74-46-A0-C0-38-5C",
        "IpAddress": "***************",
        "ProcessorFamily": "Intel(r) Core(tm) i5 processor",
        "CpuCount": '8',
        "RamMBytes": '7889',
        "FreeSpace": 126887,
        'OSType': '10',
        'OSInfo': 'Professional x64 Edition  Build 17134'
      }

      @agent_data = {
        "DisplayName": "Hp.techs.workstations",
        'MacAddress': "74-46-A0-C0-38-5C",
        "IpAddress": "***************",
        "ProcessorFamily": "Intel(r) Core(tm) i5 processor",
        "CpuCount": '8',
        "RamMBytes": '7889',
        "FreeSpace": 126887,
        'OSType': '10',
        'OSInfo': 'Professional x64 Edition  Build 17134',
        'BIOS': { 'BiosVersion': '1.2.3' }
      }

      @installed_application = {
        "ApplicationName": "-fb6pr51.exe",
        "Manufacturer": "Microsoft Corporation",
        "ProductName": "Microsoft SQL Server",
        "DirectoryPath": "C:\\Program Files (x86)\\MICROSOFT SQL SERVER\\120\\Setup Bootstrap\\Update Cache\\KB4505218\\GDR\\1033_enu_lp\\x86\\setup\\sqlsupport_msi\\pfiles\\sqlservr\\120\\setup\\ev9nqowo\\x86",
        "Version": "12.0.6108.1",
      }

      @audit_assets = {
        "AgentGuid": "100069879894961",
        "ChassisType": "Notebook",
      }
    end

    context 'Fetch Kaseya devices' do
      it 'calls Integrations::Kaseya::FetchData Service' do
        obj = Integrations::Kaseya::SyncDataWorker.new
        devices = OpenStruct.new({ parsed_response:
                                    { 'Result'=>
                                      [@device.deep_stringify_keys]
                                    }
                                  })
        asset_types = OpenStruct.new({ parsed_response:
                                        { 'Result'=>
                                          [@asset_type.deep_stringify_keys]
                                        }
                                      })
        agents_data = OpenStruct.new({ parsed_response:
                                        { 'Result'=>
                                          [@agents_data.deep_stringify_keys]
                                        }
                                      })
        agent_data = OpenStruct.new({ parsed_response: { 'Result'=>
                                        [@agent_data.deep_stringify_keys]
                                      }
                                    })
        installed_application = OpenStruct.new({ parsed_response:
                                                  { 'Result'=>
                                                    [@installed_application.deep_stringify_keys]
                                                  }
                                                })
        audit_assets = OpenStruct.new({ parsed_response:
                                          {
                                            'Result' =>
                                            [@audit_assets.deep_stringify_keys]
                                          }

                                       })
        allow(Integrations::Kaseya::SyncDataWorker).to receive(:new).and_return(obj)
        allow_any_instance_of(Integrations::Kaseya::FetchData).to receive(:refresh_token).and_return(@refresh_token)
        allow_any_instance_of(Integrations::Kaseya::FetchData).to receive(:get_kaseya_asset_types).and_return(asset_types)
        allow_any_instance_of(Integrations::Kaseya::FetchData).to receive(:get_kaseya_agent).and_return(agent_data)
        allow_any_instance_of(Integrations::Kaseya::FetchData).to receive(:get_installed_applications).and_return(installed_application)
        allow_any_instance_of(Integrations::Kaseya::SyncDataWorker).to receive(:audit_asset_types).and_return(audit_assets['parsed_response']['Result'])
        allow_any_instance_of(Integrations::Kaseya::SyncDataWorker).to receive(:assets).and_return(devices['parsed_response']['Result'])
        Integrations::Kaseya::SyncDataWorker.new.perform(@kaseya_config.id, true)
        disc_asset = DiscoveredAsset.ready_for_import.find_by(company_id: @company.id, source: 'kaseya')
        expect(disc_asset).to be_present
        expect(disc_asset.asset_sources.where(source: 'kaseya').count).to eq(1)
      end
    end
  end
end
