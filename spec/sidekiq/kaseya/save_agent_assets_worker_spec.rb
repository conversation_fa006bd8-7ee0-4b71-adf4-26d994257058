require 'rails_helper'

RSpec.describe Integrations::Kaseya::SaveAgentAssetsWorker, type: :worker do
  describe '#agents' do
    before(:each) do
      @company =  FactoryBot.create(:company)
      @kaseya_config = FactoryBot.create(:kaseya_config, company: @company)

      @device = {
        'DisplayName': 'HP78E7D1B3EFB7',
        'manufacturer': 'Microsoft',
        'IPAddresses': "***************",
        'OSName': 'Microsoft Windows',
        'AssetTypeId': "1002",
        'MACAddresses': "78:E7:D1:B3:EF:B7",
      }

      @agents_data = {
        "DisplayName": "Hp.techs.workstations",
        'MacAddress': "74-46-A0-C0-38-5C",
        "IpAddress": "***************",
        "ProcessorFamily": "Intel(r) Core(tm) i5 processor",
        "CpuCount": '8',
        "RamMBytes": '7889',
        "FreeSpace": 126887,
        'OSType': '10',
        'OSInfo': 'Professional x64 Edition  Build 17134'
      }

      @agent_data = {
        "DisplayName": "Hp.techs.workstations",
        'MacAddress': "74-46-A0-C0-38-5C",
        "IpAddress": "***************",
        "ProcessorFamily": "Intel(r) Core(tm) i5 processor",
        "CpuCount": '8',
        "RamMBytes": '7889',
        "FreeSpace": 126887,
        'OSType': '10',
        'OSInfo': 'Professional x64 Edition  Build 17134',
        'BIOS': { 'BiosVersion': '1.2.3' }
      }

      @audit_assets = {
        "AgentGuid": "100069879894961",
        "ChassisType": "Notebook",
      }
    end

    context 'Fetch Kaseya devices' do
      it 'calls Integrations::Kaseya::FetchData Service' do
        obj = Integrations::Kaseya::SaveAgentAssetsWorker.new
        devices = OpenStruct.new({ parsed_response: { 'Result'=> [@device.deep_stringify_keys] } })
        agents_data = OpenStruct.new({ parsed_response: { 'Result'=> [@agents_data.deep_stringify_keys] } })
        agent_data = OpenStruct.new({ parsed_response: { 'Result'=> @agent_data.deep_stringify_keys } })
        audit_assets = OpenStruct.new({ parsed_response: { 'Result'=> @audit_assets.deep_stringify_keys } })

        allow(Integrations::Kaseya::SaveAgentAssetsWorker).to receive(:new).and_return(obj)
        allow_any_instance_of(Integrations::Kaseya::FetchData).to receive(:get_kaseya_agent).and_return(agent_data)
        allow_any_instance_of(Integrations::Kaseya::FetchData).to receive(:get_kaseya_agents).and_return(agents_data)
        allow_any_instance_of(Integrations::Kaseya::SaveAgentAssetsWorker).to receive(:audit_asset_types).and_return(audit_assets['parsed_response']['Result'])
        allow_any_instance_of(Integrations::Kaseya::SaveAgentAssetsWorker).to receive(:agents).and_return(devices['parsed_response']['Result'])
        Integrations::Kaseya::SaveAgentAssetsWorker.new.perform(@kaseya_config.id, true)
        disc_asset = DiscoveredAsset.ready_for_import.find_by(company_id: @company.id, source: 'kaseya')
        expect(disc_asset).to be_present
        expect(disc_asset.asset_sources.where(source: 'kaseya').count).to eq(1)
      end
    end
  end
end
