require 'rails_helper'
RSpec.describe Integrations::Ubiquiti::SyncDataWorker, type: :worker do
  describe '#execute' do
    let(:company) { create(:company) }
    let(:ubiquiti_config) { create(:ubiquiti_config, company_id: company.id) }
    let(:ubiquiti_controller) { 
      ubiquiti_config.ubiquiti_controller.create(url: "demo.ubnt.com",
                                                 sites: ["default"],
                                                 username: "superadmin",
                                                 password: "superadmin"
                                            )
    } 
    before(:each) do
      next_client_id = nil
      allow_any_instance_of(Integrations::Ubiquiti::FetchData).to receive(:authenticate?).and_return(true)
    end

    context 'Sync Ubiquiti devices' do
      it 'calls Integrations::Ubiquiti::FetchData Service' do
        obj = Integrations::Ubiquiti::SyncDataWorker.new
        allow(Integrations::Ubiquiti::SyncDataWorker).to receive(:new).and_return(obj)
        Integrations::Ubiquiti::SyncDataWorker.new.perform(ubiquiti_config.id, true)
        expect(Integrations::Ubiquiti::Config.where(company_id: ubiquiti_config.company_id).count).to eq(1)
      end
    end
  end
end
