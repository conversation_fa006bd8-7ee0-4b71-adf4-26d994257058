require 'rails_helper'

RSpec.describe Integrations::AzureAd::SyncDataWorker, type: :worker do
  describe '#execute' do
    let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
    let!(:company) { FactoryBot.create(:company) }
    let(:discovered_user) { create(:discovered_user, company_id: company.id, email: "<EMAIL>") }
    let!(:new_user) { create(:user, email: '<EMAIL>') }
    let!(:company_user) { create(:company_user, user: new_user, company: company, granted_access_at: 1.day.ago, custom_form_id: custom_form.id) }
    let!(:azure_ad_config) { FactoryBot.create(:azure_ad_config, company_id: company.id) }
    let(:users) {
      OpenStruct.new({
        parsed_response: {
          "value"=> [{
            "businessPhones"=>[],
            "displayName"=>"User Name",
            "givenName"=>"User Name",
            "jobTitle"=>nil,
            "mail"=>nil,
            "mobilePhone"=>nil,
            "officeLocation"=>nil,
            "preferredLanguage"=>"en-GB",
            "surname"=>nil,
            "userPrincipalName"=>"<EMAIL>",
            "id"=>"ee331c9d-eaae-4fff-a0d4-12b2a769d88f",
            "accountEnabled" => true
          }]
        }
      })
    }
    let(:refresh_token) {
      {
        "expires_in" => 3600,
        "access_token" => "cvsdhbcdsbbhbn35h34j/sbcjhsd",
        "refresh_token" => "jcbdhjsnxjhvc74yfbhjbc"
      }
    }

    context 'Sync Azure Active Directory user' do
      it 'calls Integrations::AzureAd::FetchData Service' do
        obj = Integrations::AzureAd::SyncDataWorker.new
        allow_any_instance_of(Integrations::AzureAd::FetchData).to receive(:refresh_token).and_return(refresh_token)
        allow_any_instance_of(Integrations::AzureAd::FetchData).to receive(:get_users).and_return(users)
        Sidekiq::Testing.inline! do
          Integrations::AzureAd::SyncDataWorker.new.perform(azure_ad_config.id, true)
        end
        company_integration = azure_ad_config.company_integration.reload
        expect(company_integration.sync_status).to eq('successful')
      end

      it 'should create one Discovered User' do
        obj = Integrations::AzureAd::SyncDataWorker.new
        allow_any_instance_of(Integrations::AzureAd::FetchData).to receive(:refresh_token).and_return(refresh_token)
        allow_any_instance_of(Integrations::AzureAd::FetchData).to receive(:get_users).and_return(users)
        Sidekiq::Testing.inline! do
          Integrations::AzureAd::SyncDataWorker.new.perform(azure_ad_config.id, true, nil, true)
        end
        expect(DiscoveredUser.where(company_id: company.id).count).to eq(1)
      end

      it 'should not create Discovered User when azure user is disabled' do
        users.parsed_response["value"][0]["accountEnabled"] = false
        obj = Integrations::AzureAd::SyncDataWorker.new
        allow_any_instance_of(Integrations::AzureAd::FetchData).to receive(:refresh_token).and_return(refresh_token)
        allow_any_instance_of(Integrations::AzureAd::FetchData).to receive(:get_users).and_return(users)
        Sidekiq::Testing.inline! do
          Integrations::AzureAd::SyncDataWorker.new.perform(azure_ad_config.id, true, nil, true)
        end
        expect(DiscoveredUser.where(company_id: company.id).count).to eq(0)
      end
    end

    context 'remove users access' do
      it 'remove users granted access which are disabled' do
        users.parsed_response["value"][0]["userPrincipalName"] = "<EMAIL>"
        users.parsed_response["value"][0]["accountEnabled"] = false
        discovered_user

        obj = Integrations::AzureAd::SyncDataWorker.new
        allow_any_instance_of(Integrations::AzureAd::FetchData).to receive(:refresh_token).and_return(refresh_token)
        allow_any_instance_of(Integrations::AzureAd::FetchData).to receive(:get_users).and_return(users)
        Sidekiq::Testing.inline! do
          Integrations::AzureAd::SyncDataWorker.new.perform(azure_ad_config.id, true, nil, true)
        end
        staff_user = User.find_by_email(users.parsed_response["value"][0]["userPrincipalName"])
        company_user = CompanyUser.find_by(company_id: company.id, user_id: staff_user.id)

        expect(company_user.granted_access_at).to eq(nil)
      end

      it 'remove users granted access which are deleted' do
        users.parsed_response["value"][0]["userPrincipalName"] = "<EMAIL>"
        discovered_user

        obj = Integrations::AzureAd::SyncDataWorker.new
        allow_any_instance_of(Integrations::AzureAd::FetchData).to receive(:refresh_token).and_return(refresh_token)
        allow_any_instance_of(Integrations::AzureAd::FetchData).to receive(:get_users).and_return(users)
        allow_any_instance_of(Integrations::AzureAd::FetchData).to receive(:get_deleted_users).and_return(users)
        allow_any_instance_of(Integrations::AzureAd::FetchData).to receive(:ok?).and_return(true)

        Sidekiq::Testing.inline! do
          Integrations::AzureAd::SyncDataWorker.new.perform(azure_ad_config.id, false, nil, true)
        end
        staff_user = User.find_by_email("<EMAIL>")
        company_user = CompanyUser.find_by(company_id: company.id, user_id: staff_user.id)

        expect(company_user.granted_access_at).to eq(nil)
      end
    end
  end
end
