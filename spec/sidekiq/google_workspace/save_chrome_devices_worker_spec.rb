require 'rails_helper'

RSpec.describe Integrations::GoogleWorkspace::SaveChromeDevicesWorker, type: :worker do
  describe '#perform' do
    before(:each) do
      @company = FactoryBot.create(:company)
      @config = FactoryBot.create(:google_workspace_config, company: @company)
      @company_integration = @config.company_integration

      @device = OpenStruct.new(
        serial_number: 'CH123456',
        model: 'Chromebook Plus',
        os_version: 'ChromeOS 120.0',
        mac_address: '11:22:33:44:55:66',
        tpm_version_info: OpenStruct.new(manufacturer: 'Google Inc.')
      )

      @response = OpenStruct.new(
        chromeosdevices: [@device],
        next_page_token: nil
      )

      allow_any_instance_of(Integrations::GoogleWorkspace::FetchData).to receive(:fetch_all_chrome_os_devices).and_return(@response)
      allow_any_instance_of(Integrations::GoogleWorkspace::SaveChromeDevicesWorker).to receive(:add_source)
      allow_any_instance_of(Integrations::GoogleWorkspace::SaveChromeDevicesWorker).to receive(:is_higher_precedence?).and_return(true)
    end

    it 'saves a discovered ChromeOS asset and updates integration status' do
      expect {
        described_class.new.perform(@config.id, true)
      }.to change(DiscoveredAsset, :count).by(1)

      asset = DiscoveredAsset.last
      expect(asset.company_id).to eq(@company.id)
      expect(asset.machine_serial_no).to eq('CH123456')
      expect(asset.os_name).to eq('ChromeOS')
      expect(asset.source).to eq('google_workspace')

      @company_integration.reload
      expect(@company_integration.sync_status).to eq('successful')
      expect(@company_integration.status).to eq(true)
    end
  end
end
