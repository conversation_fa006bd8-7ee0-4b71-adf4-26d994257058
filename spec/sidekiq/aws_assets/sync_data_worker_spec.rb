require "rails_helper"

describe Integrations::AwsAssets::SyncDataWorker, type: :worker do
  before(:each) do
    Rails.application.load_seed
    @company = FactoryBot.create(:company)
    allow_any_instance_of(Integrations::AwsAssets::FetchData).to receive(:authenticate?).and_return([true, "success"])
    @aws_assets_config = FactoryBot.create(:aws_assets_config, company_id: @company.id)
  end

  describe "Sync AWS calls Integrations::AwsAssets::FetchData Service" do
    let(:worker) { Integrations::AwsAssets::SyncDataWorker.new }
    let(:client) { Integrations::AwsAssets::FetchData.new(@aws_assets_config) }

    before do
      allow(Integrations::AwsAssets::FetchData).to receive(:new).and_return(client)
    end

    it "creates does not show an error for the client" do
      expect(client.error).to be_blank
    end
  end

  describe "Fetch AWS Devices" do
    let(:worker) { Integrations::AwsAssets::SyncDataWorker.new }
    let(:client) { Integrations::AwsAssets::FetchData.new(@aws_assets_config) }

    it "calls Integrations::AwsAssets::FetchData Service" do
      instance_data = {
        :instance_id => "i-01b5e082e4b83bfe9",
        :name => "build-service-staging",
        :type => "t2.small", :manufacturer => "Amazon EC2",
        :architecture => "x86_64",
        :core_count => 1, :harddisk => "30GB",
        :hypervisor => "xen",
        :launch_time => "2021-06-15 09:47:38 UTC",
        :mac_address => "02:6e:7c:b4:59:30",
        :mac_addresses => ["02:6E:7C:B4:59:30"],
        :platform_description => nil,
        :platform_details => nil,
        :public_dns_name => "ec2-54-218-250-57.us-west-2.compute.amazonaws.com",
        :public_ip_address => "*************",
        :root_device_name => "/dev/sda1",
        :state => "running",
        :vpc_id => "vpc-dbde42b3",
        :threads_per_core => 1,
        :virtualization_type => "hvm",
        :memory => "2GB",
        :availability_zone => "us-west-2b",
        :region => "US West (Oregon)",
        :security_group => "SSH and HTTP In",
        :tags => { :Name => "build-service-staging" },
      }
      virtual_machine = OpenStruct.new({ parsed_response: { "value" => instance_data } })
      allow_any_instance_of(Integrations::AwsAssets::SyncDataWorker).to receive(:fetch_devices).and_return([virtual_machine.parsed_response["value"]])
      allow_any_instance_of(Integrations::AwsAssets::SyncDataWorker).to receive(:running_instances).and_return(virtual_machine.parsed_response["value"])

      worker.perform(@aws_assets_config.id, true)
      discovered_asset = DiscoveredAsset.ready_for_import.find_by(company_id: @company.id, asset_type: "Virtual Machine", source: "aws")
      expect(discovered_asset).to be_present
      expect(discovered_asset.asset_sources.where(source: "aws").count).to eq(1)
      expect(discovered_asset.cloud_asset_attributes.count).to eq(27)
      expect(discovered_asset.integration_location).to be_present
    end
  end
end
