require 'rails_helper'

RSpec.describe Integrations::AzureAssets::SyncDataWorker, type: :worker do
  describe '#execute' do
    before(:each) do
      @company =  FactoryBot.create(:company)
      @azure_assets_config = Integrations::AzureAssets::Config.create(
      token: "access_token",
      expires_in: 2321,
      refresh_token: "refresh_token",
      company_id: @company.id)
      
      @subscription = OpenStruct.new({parsed_response:
                                        {"value"=>
                                          [{"subscriptionId"=>"h1ukXt3AlUapQ-CtMS6fFjKiGmk9deJNrQHEEUA_Pv0",
                                            "SubscriptionName"=>"Pay As You Go",
                                          }]
                                        }
                                      })

      @refresh_token = {"expires_in" => 3600, 
                        "access_token" => "cvsdhbcdsbbhbn35h34j/sbcjhsd", 
                        "refresh_token" => "jcbdhjsnxjhvc74yfbhjbc"}
      @machine = {:name=>"Testing",
                  :id=>"/subscriptions/5cbccab6-30c8-44ba-901e-32d216bf72a1/resourceGroups/TESTING_VMS/providers/Microsoft.Compute/virtualMachines/Testing",
                  :type=>"Microsoft.Compute/virtualMachines",
                  :location=>"northcentralus",
                  :identity=>{:type=>"SystemAssigned",
                              :principalId=>"def17fd6-b474-44ad-81ef-dbced744c9a1",
                              :tenantId=>"51f6420c-47a1-4701-8bf9-e5b71795f17a"},
                  :properties=>{:vmId=>"a93f3673-df57-4786-9f9c-78c81f2ea227",
                                :hardwareProfile=>{:vmSize=>"Standard_DS1_v2"},
                  :storageProfile=>
                    {:imageReference=>
                      {:publisher=>"MicrosoftWindowsServer",
                       :offer=>"WindowsServer",
                       :sku=>"2019-Datacenter",
                       :version=>"latest",
                       :exactVersion=>"17763.1999.**********"},
                     :osDisk=>
                       {:osType=>"Windows",
                        :name=>"Testing_OsDisk_1_b522a3e378364ddea2c5c48fb6faae24",
                         :createOption=>"FromImage",
                         :caching=>"ReadWrite",
                         :managedDisk=>
                           {:storageAccountType=>"StandardSSD_LRS",
                            :id=>"/subscriptions/5cbccab6-30c8-44ba-901e-32d216bf72a1/resourceGroups/Testing_VMs/providers/Microsoft.Compute/disks/Testing_OsDisk_1_b522a3e378364ddea2c5c48fb6faae24"},
                            :diskSizeGB=>127},
                     :dataDisks=>[]},
                  :osProfile=>
                    {:computerName=>"Testing",
                     :adminUsername=>"genuityadmin",
                     :windowsConfiguration=>{:provisionVMAgent=>true, :enableAutomaticUpdates=>true, :patchSettings=>{:patchMode=>"AutomaticByOS", :assessmentMode=>"ImageDefault", :enableHotpatching=>false}},
                     :secrets=>[],
                     :allowExtensionOperations=>true,
                     :requireGuestProvisionSignal=>true},
                  :networkProfile=>{:networkInterfaces=>[{:id=>"/subscriptions/5cbccab6-30c8-44ba-901e-32d216bf72a1/resourceGroups/Testing_VMs/providers/Microsoft.Network/networkInterfaces/Test_NIC_1"}]},
                  :diagnosticsProfile=>{:bootDiagnostics=>{:enabled=>true}},
                  :licenseType=>"Windows_Server",
                  :provisioningState=>"Succeeded"}}

      @vm_size = {:name=>"Standard_DS1_v2",
                  :numberOfCores=>1,
                  :osDiskSizeInMB=>1047552,
                  :resourceDiskSizeInMB=>7168,
                  :memoryInMB=>3584,
                  :maxDataDiskCount=>4
                }
      @vm_location = {:id=>"/subscriptions/5cbccab6-30c8-44ba-901e-32d216bf72a1/locations/northcentralus",
                      :name=>"northcentralus",
                      :displayName=>"North Central US",
                      :regionalDisplayName=>"(US) North Central US"
                    }
      @instance_view = {:computerName=>"Testing",
                        :osName=>"Windows Server 2019 Datacenter",
                        :osVersion=>"Microsoft Windows NT 10.0.17763.0",
                        :statuses=>
                          [{:code=>"ProvisioningState/succeeded", :level=>"Info", :displayStatus=>"Provisioning succeeded", :time=>"2021-06-14T17:07:26.4436837+00:00"},
                           {:code=>"PowerState/running", :level=>"Info", :displayStatus=>"VM running"}]
                      }

      @disk_info = {:osType=>"Windows",
                    :hyperVGeneration=>"V1",
                    :diskSizeGB=>127,
                    :diskIOPSReadWrite=>500,
                    :diskMBpsReadWrite=>60,
                    :timeCreated=>"2021-06-14T17:05:16.5892918+00:00",
                    :provisioningState=>"Succeeded",
                    :diskSizeBytes=>136367308800
                  }

      @network_info = [{:properties=>
                        {:ipConfigurations=>
                          [{:properties=>
                            {:privateIPAddress=>"**********",
                              :privateIPAllocationMethod=>"Dynamic",
                              :publicIPAddress=>{
                                :id=>"/subscriptions/5cbccab6-30c8-44ba-901e-32d216bf72a1/resourceGroups/Testing_VMs/providers/Microsoft.Network/publicIPAddresses/Testing-ip"
                              },
                              :privateIPAddressVersion=>"IPv4"
                            },
                            :publicIPAddress=>{
                              :properties=>{
                                :ipAddress=>"***********",
                                :publicIPAddressVersion=>"IPv4",
                                :publicIPAllocationMethod=>"Dynamic",
                                :idleTimeoutInMinutes=>4
                              }
                            }}],
                          :macAddress=>"00-22-48-8D-50-8E",
                          :virtualMachine=>{
                            :id=>"/subscriptions/5cbccab6-30c8-44ba-901e-32d216bf72a1/resourceGroups/Testing_VMs/providers/Microsoft.Compute/virtualMachines/Testing"
                          }}}
                        ]
    end

    context 'Fetch Azure virtual machines' do
      it 'calls Integrations::AzureAssets::FetchData Service' do
        obj = Integrations::AzureAssets::SyncDataWorker.new
        virtual_machines = OpenStruct.new({ parsed_response: { "value"=>[@machine.deep_stringify_keys] }})
        allow(Integrations::AzureAssets::SyncDataWorker).to receive(:new).and_return(obj)
        allow_any_instance_of(Integrations::AzureAssets::FetchData).to receive(:refresh_token).and_return(@refresh_token)
        allow_any_instance_of(Integrations::AzureAssets::FetchData).to receive(:get_subscriptions_call).and_return(@subscription)
        allow_any_instance_of(Integrations::AzureAssets::FetchData).to receive(:get_virtual_machines_call).and_return(virtual_machines)
        allow_any_instance_of(Integrations::AzureAssets::FetchData).to receive(:find_vm_size).and_return(@vm_size)
        allow_any_instance_of(Integrations::AzureAssets::FetchData).to receive(:get_disk_info).and_return(@disk_info)
        allow_any_instance_of(Integrations::AzureAssets::FetchData).to receive(:get_network_info).and_return(@network_info)
        allow_any_instance_of(Integrations::AzureAssets::FetchData).to receive(:find_vm_location).and_return(@vm_location)
        allow_any_instance_of(Integrations::AzureAssets::FetchData).to receive(:get_instance_view).and_return(@instance_view)

        Integrations::AzureAssets::SyncDataWorker.new.perform(@azure_assets_config.id, true)
        disc_asset = DiscoveredAsset.ready_for_import.find_by(company_id: @company.id, asset_type: 'Virtual Machine', source: 'azure')
        expect(disc_asset).to be_present
        expect(disc_asset.asset_softwares.count).to eq(1)
        expect(disc_asset.asset_sources.where(source: 'azure').count).to eq(1)
        expect(disc_asset.integration_location).to be_present
      end
    end
  end
end
