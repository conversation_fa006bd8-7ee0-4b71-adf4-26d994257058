require 'rails_helper'

RSpec.describe Integrations::Microsoft::SyncDataWorker, type: :worker do
  describe '#execute' do
    class StubFetchData < Integrations::Microsoft::FetchData
      attr_accessor :canned_responses

      def initialize(company_id, canned_responses)
        super(company_id)
        self.canned_responses = canned_responses
      end

      def make_api_call(endpoint, api_type)
        response = canned_responses[api_type]
        if response && response.parsed_response["error"]
          self.error = response.parsed_response["error"]["message"]
        end
        response
      end
    end

    class TestSyncDataWorker < described_class
      attr_accessor :canned_responses

      def initialize(canned_responses)
        self.canned_responses = canned_responses
      end

      def client
        @stub_client ||= StubFetchData.new(company.id, canned_responses)
      end
    end

    let(:company) { FactoryBot.create(:company) }
    let(:microsoft_config) { FactoryBot.create(:microsoft_config, company_id: company.id) }

    let(:apps) {
      OpenStruct.new(
        {
          parsed_response: {
            "value"=> [
              {
                "id"=>"6e79fc05-c916-4d20-9fff-3bdc6d848f6b",
                "deletedDateTime"=>nil,
                "accountEnabled"=>true,
                "appDisplayName"=>"Slack",
                "appId"=>"da2ef37b-cf36-4f1b-85f1-be9710083459",
                "applicationTemplateId"=>nil,
                "appOwnerOrganizationId"=>"9188040d-6c67-4c5b-b112-36a304b66dad",
                "appRoleAssignmentRequired"=>false,
                "displayName"=>"Slack",
                "errorUrl"=>nil,
                "homepage"=>nil,
                "info"=>{"termsOfServiceUrl"=>"https://slack.com/terms-of-service",
                "supportUrl"=>nil,
                "privacyStatementUrl"=>"https://slack.com/privacy-policy",
                "marketingUrl"=>nil,
                "logoUrl"=>"https://secure.aadcdn.microsoftonline-p.com/dbd5a2dd-otrskziqqxxsssdghj7jjfrifoprytjmtmfdfhco0ss/appbranding/iztviocwtqcwf5rlh9mo47fj8qex4su0vbfyvkhthoe/1033/bannerlogo?ts=636271037506621951"},
                "logoutUrl"=>nil,
                "notificationEmailAddresses"=>[],
                "publishedPermissionScopes"=>[],
                "preferredSingleSignOnMode"=>nil,
                "preferredTokenSigningKeyEndDateTime"=>nil,
                "preferredTokenSigningKeyThumbprint"=>nil,
                "publisherName"=>"Microsoft Accounts",
                "replyUrls"=>["https://oauth2.slack.com/services/auth/microsoft"],
                "samlMetadataUrl"=>nil,
                "samlSingleSignOnSettings"=>nil,
                "servicePrincipalNames"=>["da2ef37b-cf36-4f1b-85f1-be9710083459"],
                "signInAudience"=>"AzureADandPersonalMicrosoftAccount",
                "tags"=>["WindowsAzureActiveDirectoryIntegratedApp"],
                "addIns"=>[],
                "appRoles"=>[],
                "keyCredentials"=>[],
                "passwordCredentials"=>[]
              }
            ]
          }
        }
      )
    }

    let(:app_users) {
      OpenStruct.new({parsed_response:
                      {"value"=>
                        [{"id"=>"h1ukXt3AlUapQ-CtMS6fFjKiGmk9deJNrQHEEUA_Pv0",
                          "creationTimestamp"=>"2017-05-23T14:01:38.7786461Z",
                          "appRoleId"=>"********-0000-0000-0000-********0000",
                          "principalDisplayName"=>"User Name",
                          "principalId"=>"5ea45b87-c0dd-4695-a943-e0ad312e9f16",
                          "principalType"=>"User",
                          "resourceDisplayName"=>"Slack",
                          "resourceId"=>"6e79fc05-c916-4d20-9fff-3bdc6d848f6b"
                        }]
                      }
                    })
    }

    let(:usage) {
      OpenStruct.new({parsed_response:
                      {"value"=>
                        [{
                          "createdDateTime"=>Time.now,
                          "userDisplayName"=>"User Name",
                          "userPrincipalName"=>"<EMAIL>",
                          "id"=>"ee331c9d-eaae-4fff-a0d4-12b2a769d88f",
                          "userId"=>"ee331c9d-eaae-4fff-a0d4-12b2a769d88f",
                          "appId"=>"570ca2ca-0b19-41f3-aad6-ac8359155082",
                          "appDisplayName"=>"Slack",
                          "ipAddress"=>"***************",
                          "clientAppUsed"=>"Browser",
                          "correlationId"=>"d2086930-b121-4b97-a96b-09c1fb2b9a24",
                          "conditionalAccessStatus"=>nil,
                          "originalRequestId"=>"98584db7-9128-406f-8e55-50b71d643e00",
                          "isInteractive"=>true,
                          "tokenIssuerName"=>""
                        }]
                      }
                    })
    }

    let(:bad_organization) {
      OpenStruct.new({parsed_response:
                      {"error"=>
                        {
                          "message"=> "Boom!"
                        }
                      }
                    })
    }

    let(:organization) {
      OpenStruct.new({parsed_response:
                      {"value"=>
                        [{
                          "id"=> "51f6420c-47a1-4701-8bf9-e5b71795f17a",
                          "deletedDateTime"=> nil,
                          "businessPhones"=> [
                              "************"
                          ],
                          "city"=> "Villa Park",
                          "country"=> nil,
                          "countryLetterCode"=> "US",
                          "createdDateTime"=> "2016-09-21T21:24:42Z",
                          "displayName"=> "NuLodgic",
                          "marketingNotificationEmails"=> [],
                          "onPremisesLastSyncDateTime"=> nil,
                          "onPremisesSyncEnabled"=> nil,
                          "postalCode"=> "60181",
                          "preferredLanguage"=> "en",
                          "privacyProfile"=> nil,
                          "securityComplianceNotificationMails"=> [],
                          "securityComplianceNotificationPhones"=> [],
                          "state"=> "IL",
                          "street"=> "721 E. Madison",
                          "technicalNotificationMails"=> [
                              "<EMAIL>"
                          ],
                          "assignedPlans"=> [
                            {
                              "assignedDateTime"=> "2019-08-08T14=>42=>46Z",
                              "capabilityStatus"=> "Deleted",
                              "service"=> "SCO",
                              "servicePlanId"=> "a420f25f-a7b3-4ff5-a9d0-5d58f73b537d"
                            },
                            {
                              "assignedDateTime"=> "2019-07-26T11:02:36Z",
                              "capabilityStatus"=> "Enabled",
                              "service"=> "AADPremiumService",
                              "servicePlanId"=> "eec0eb4f-6444-4f95-aba0-50c24d67f998"
                            }
                          ],
                          "provisionedPlans"=> [
                            {
                              "capabilityStatus"=> "Deleted",
                              "provisioningStatus"=> "Success",
                              "service"=> "SCO"
                            },
                            {
                              "capabilityStatus"=> "Enabled",
                              "provisioningStatus"=> "Success",
                              "service"=> "Adallom"
                            }
                          ],
                          "verifiedDomains"=> [
                            {
                              "capabilities"=> "Email, OfficeCommunicationsOnline, Intune",
                              "isDefault"=> false,
                              "isInitial"=> false,
                              "name"=> "gogenuity.com",
                              "type"=> "Managed"
                            },
                            {
                              "capabilities"=> "Email, OfficeCommunicationsOnline, Intune",
                              "isDefault"=> false,
                              "isInitial"=> false,
                              "name"=> "GENUITY.IO",
                              "type"=> "Managed"
                            }
                          ]
                        }
                      ]
                    }})
    }
    let(:bad_licenses) {
      OpenStruct.new({parsed_response:
                      {"error"=>
                        {
                          "message"=> "Licenses are invalid"
                        }
                      }
                    })
    }

    let(:licenses) {
      OpenStruct.new({parsed_response:
                    {"value"=>
                      [{
                      "capabilityStatus"=> "Enabled",
                      "consumedUnits"=> 0,
                      "id"=> "51f6420c-47a1-4701-8bf9-e5b71795f17a_6470687e-a428-4b7a-bef2-8a291ad947c9",
                      "skuId"=> "6470687e-a428-4b7a-bef2-8a291ad947c9",
                      "skuPartNumber"=> "WINDOWS_STORE",
                      "appliesTo"=> "Company",
                      "prepaidUnits"=> {
                          "enabled"=> 10000,
                          "suspended"=> 0,
                          "warning"=> 0
                      },
                      "servicePlans"=> [
                        {
                            "servicePlanId"=> "113feb6c-3fe4-4440-bddc-54d774bf0318",
                            "servicePlanName"=> "EXCHANGE_S_FOUNDATION",
                            "provisioningStatus"=> "Success",
                            "appliesTo"=> "Company"
                        },
                        {
                            "servicePlanId"=> "a420f25f-a7b3-4ff5-a9d0-5d58f73b537d",
                            "servicePlanName"=> "WINDOWS_STORE",
                            "provisioningStatus"=> "Success",
                            "appliesTo"=> "Company"
                        }
                      ]
                    }]
                  }})
    }

    let(:refresh_token) {
      {
        "expires_in" => 3600,
        "access_token" => "cvsdhbcdsbbhbn35h34j/sbcjhsd",
        "refresh_token" => "jcbdhjsnxjhvc74yfbhjbc"
      }
    }

    context 'Sync Microsoft apps' do
      context 'with good data' do
        let!(:canned_responses) do
          {
            "refresh_token" => refresh_token,
            "get_app_usage" => usage,
            "get_organization" => organization,
            "get_licenses" => licenses,
          }
        end

        let!(:run) { TestSyncDataWorker.new(canned_responses).perform(microsoft_config.id, true) }

        it "should create an app" do
          expect(Integrations::Microsoft::App.all.count).to eq(1)
          expect(Integrations::AppSource.all.count).to eq(1)
        end

        it "should create 2 services" do
          expect(Integrations::Microsoft::Service.all.count).to eq(2)
        end

        it "should remain the integration status to pending" do
          company_integration = microsoft_config.company_integration.reload
          expect(company_integration.sync_status).to eq('pending')
        end

        it "should create company integration" do
          company_integration = microsoft_config.company_integration.reload
          expect(company_integration.error_message).to be_blank
        end
      end

      context 'with bad licenses' do
        let!(:canned_responses) do
          {
            "refresh_token" => refresh_token,
            "get_app_usage" => usage,
            "get_organization" => organization,
            "get_licenses" => bad_licenses,
          }
        end

        let!(:run) do
          TestSyncDataWorker.new(canned_responses).perform(microsoft_config.id, true)
        end

        it "should create an app" do
          expect(Integrations::Microsoft::App.all.count).to eq(0)
        end

        it "should update the company integration with a failed sync_status" do
          company_integration = microsoft_config.company_integration.reload
          expect(company_integration.sync_status).to eq('failed')
        end

        it "should update the error message" do
          company_integration = microsoft_config.company_integration.reload
          expect(company_integration.error_message).to eq("Licenses are invalid")
        end

        it "should create 2 services" do
          expect(Integrations::Microsoft::Service.all.count).to eq(0)
        end
      end

      context 'with a bad organization call' do
        let!(:canned_responses) do
          {
            "refresh_token" => refresh_token,
            "get_app_usage" => usage,
            "get_organization" => bad_organization,
            "get_licenses" => licenses,
          }
        end

        let!(:run) do
          TestSyncDataWorker.new(canned_responses).perform(microsoft_config.id, true)
        end

        it "should not create any apps" do
          expect(Integrations::Microsoft::App.all.count).to eq(0)
        end

        it "should not create any services" do
          expect(Integrations::Microsoft::Service.all.count).to eq(0)
        end

        it "should update the company integration with a failed sync_status" do
          company_integration = microsoft_config.company_integration.reload
          expect(company_integration.sync_status).to eq('failed')
        end

        it "should update the error message" do
          company_integration = microsoft_config.company_integration.reload
          expect(company_integration.error_message).to eq("Boom!")
        end
      end
    end
  end
end
