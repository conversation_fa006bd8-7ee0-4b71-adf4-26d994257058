require 'rails_helper'

RSpec.describe Integrations::Microsoft::SaveUsersWorker, type: :worker do
  describe '#execute' do
    before do 
      fetch_data_class = Class.new(Integrations::Microsoft::FetchData) do
        attr_accessor :canned_responses

        def initialize(company_id, canned_responses)
          super(company_id)
          self.canned_responses = canned_responses
        end

        def make_api_call(endpoint, api_type)
          response = canned_responses[api_type]
          if response && response.parsed_response["error"]
            self.error = response.parsed_response["error"]["message"]
          end
          response
        end
      end
      stub_const('StubFetchData', fetch_data_class)

      user_worker_class = Class.new(described_class) do
        attr_accessor :canned_responses

        def initialize(canned_responses)
          self.canned_responses = canned_responses
        end

        def client
          @stub_client ||= StubFetchData.new(company.id, canned_responses)
        end
      end
      stub_const('StubDescribClass', user_worker_class)
    end

    let(:company) { FactoryBot.create(:company) }
    let(:microsoft_config) { FactoryBot.create(:microsoft_config, company_id: company.id) }
    let(:users) {
      OpenStruct.new({
        parsed_response: {
          "value"=> [{
            "businessPhones"=>[],
            "displayName"=>"User Name",
            "givenName"=>"User Name",
            "jobTitle"=>nil,
            "mail"=>nil,
            "mobilePhone"=>nil,
            "officeLocation"=>nil,
            "preferredLanguage"=>"en-GB",
            "surname"=>nil,
            "userPrincipalName"=>"<EMAIL>",
            "id"=>"ee331c9d-eaae-4fff-a0d4-12b2a769d88f"
          }]
        }
      })
    }
    let(:refresh_token) {
      {
        "expires_in" => 3600,
        "access_token" => "cvsdhbcdsbbhbn35h34j/sbcjhsd",
        "refresh_token" => "jcbdhjsnxjhvc74yfbhjbc"
      }
    }

    context 'Sync Microsoft user' do
      context 'with good data' do
        let!(:canned_responses) do
          {
            "refresh_token" => refresh_token,
            "get_users" => users,
          }
        end
        
        let!(:run) { StubDescribClass.new(canned_responses).perform(microsoft_config.id, true) }

        it "should create an user" do
          expect(Integrations::Microsoft::User.all.count).to eq(1)
        end

        it "should update company integration with success status" do
          company_integration = microsoft_config.company_integration.reload
          expect(company_integration.sync_status).to eq('successful')
        end
      end
    end
  end
end
