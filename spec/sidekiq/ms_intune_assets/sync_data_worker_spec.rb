require 'rails_helper'

RSpec.describe Integrations::MsIntuneAssets::SyncDataWorker, type: :worker do
  describe '#execute' do
    before(:each) do
      @company =  FactoryBot.create(:company)
      @intune_config = FactoryBot.create(:ms_intune_assets_config, company: @company)

      @refresh_token = {
        'expires_in' => 3600,
        'access_token' => 'cvsdhbcdsbbhbn35h34j/sbcjhsd',
        'refresh_token' => 'jcbdhjsnxjhvc74yfbhjbc'}

      @device = {
        'displayName'=>'User Genuity',
        'manufacturer'=>'Apple',
        'model'=>'MacBook Pro',
        'machine_serial_no' => '0000-0003-6421-4278-0245-7654-53',
        'os_name': 'Windows'}
    end

    context 'Fetch Intune devices' do
      it 'calls Integrations::MsIntuneAssets::FetchData Service' do
        obj = Integrations::MsIntuneAssets::SyncDataWorker.new
        devices = OpenStruct.new({ 'value' => [@device.deep_stringify_keys] })
        allow(Integrations::MsIntuneAssets::SyncDataWorker).to receive(:new).and_return(obj)
        allow_any_instance_of(Integrations::MsIntuneAssets::FetchData).to receive(:refresh_token).and_return(@refresh_token)
        allow_any_instance_of(Integrations::MsIntuneAssets::FetchData).to receive(:get_intune_devices).and_return(devices)
        Sidekiq::Testing.inline! do
          Integrations::MsIntuneAssets::SyncDataWorker.new.perform(@intune_config.id, true)
        end
        disc_asset = DiscoveredAsset.ready_for_import.find_by(company_id: @company.id, source: 'ms_intune')
        expect(disc_asset).to be_present
        expect(disc_asset.asset_sources.where(source: 'ms_intune').count).to eq(1)
      end
    end
  end
end
