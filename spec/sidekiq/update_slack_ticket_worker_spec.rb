require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

RSpec.describe UpdateSlackTicketWorker, type: :worker do
  create_company_and_user

  before { help_ticket }
  let(:config) { create(:slack_config, company: company) }

  let!(:ticket_params) {
    {
      'Subject' => 'Slack Ticket',
      'Created By' => company_user.contributor_id,
      'Status' => 'open',
      'Priority' => 'low',
      'Description' => 'What a great day!',
      'source' => 'slack',
      workspace: company.default_workspace,
    }
  }

  let(:help_ticket) { create_ticket(ticket_params) }
  let(:priority) { help_ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: 'priority'}) }
  let(:description) { help_ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: 'description'}) }

  let(:payload) {
    {
      'type' => 'view_submission',
      'user' => {
        'id' => 'U04AQ4933FT',
        'username' => 'john_liam',
        'name' => 'john_liam',
        'team_id' => config.team_id,
      },
      'view' => {
        'callback_id' => 'create_ticket',
        'private_metadata' => "{\"help_ticket_id\":#{help_ticket.id},\"channel_id\":\"D04AQ62708H\",\"thread_ts\":null,\"global_shortcut\":true,\"is_dm_ticket\":null}",
        'blocks' => [
          {
            'type' => 'input',
            'block_id' => 'KVH7',
            'label' =>  {
              'type' => 'plain_text',
              'text' => 'Subject',
            },
            'optional' => false,
            'dispatch_action' => false,
            'element' =>  {
              'type' => 'plain_text_input',
              'action_id' => 'plain_text_input-action',
              'initial_value' => '',
              'dispatch_action_config' =>  {
                'trigger_actions_on' =>  ['on_enter_pressed']
              }
            }
          },
          {
            'type' => 'input',
            'block_id' => 'vJykZ',
            'label' => {
              'type' => 'plain_text',
              'text' => 'Assigned to',
            },
            'optional' => true,
            'dispatch_action' => false,
            'element' => {
              'type' => 'multi_static_select',
              'action_id' => 'assigned_to_multiselect',
              'placeholder' => {
                'type' => 'plain_text',
                'text' => 'Select Staff'
              }
            }
          },
          {
            'type' => 'input',
            'block_id' => 'V4b',
            'label' =>  {
              'type' => 'plain_text',
              'text' => 'Priority',
              'emoji' => true
            },
            'optional' => false,
            'dispatch_action' => false,
            'element' => {
              'type' => 'static_select',
              'action_id' => 'static_select-action',
              'placeholder' =>  {
                'type' => 'plain_text',
                'text' => 'Select any priority',
                'emoji' => true
              },
              'initial_option' =>  {
                'text' =>  {
                  'type' => 'plain_text',
                  'text' => 'Low',
                  'emoji' => true
                },
                'value' => 'low'
              },
              'options' =>  [
                {
                  'text' => {
                    'type' => 'plain_text',
                    'text' => 'Low',
                    'emoji' => true
                  },
                  'value' => 'low'
                }, {
                  'text' => {
                    'type' => 'plain_text',
                    'text' => 'Medium',
                    'emoji' => true
                  },
                  'value' => 'medium'
                }, {
                  'text' => {
                    'type' => 'plain_text',
                    'text' => 'High',
                    'emoji' => true
                  },
                  'value' => 'high'
                }
              ]
            }
          },
          {
            'type' => 'input',
            'block_id' => 'eAXZ',
            'label' => {
              'type' => 'plain_text',
              'text' => 'Description',
              'emoji' => true
            },
            'optional' => true,
            'dispatch_action' => false,
            'element' => {
              'type' => 'plain_text_input',
              'action_id' => 'plain_text_input-action',
              'initial_value' => '',
              'multiline' => true,
              'dispatch_action_config' => {
                'trigger_actions_on' => ['on_enter_pressed']
              }
            }
          }
        ],
        'state' => {
          'values' => {
            'KVH7' => {
              'plain_text_input-action' => {
                'type' => 'plain_text_input',
                'value' => 'Updated Slack Ticket'
              }
            },
            'V4b' => {
              'static_select-action' => {
                'type' => 'static_select',
                'selected_option' => {
                  'text' => {
                    'type' => 'plain_text',
                    'text' => 'medium',
                    'emoji' => true
                  },
                'value' => 'medium'
                }
              }
            },
            'eAXZ' => {
              'plain_text_input-action' => {
                'type' => 'plain_text_input',
                'value' => 'What a great week!'
              }
            },
            'vJykZ' => {
              'assigned_to_multiselect' => {
                'type' => 'multi_static_select',
                'selected_options' => [
                  {
                    'text' => {
                      'type' => 'plain_text',
                      'text' => 'Help Desk Agents'
                    },
                    'value' => company.groups.find_by(name: 'Help Desk Agents').contributor_id
                  }
                ]
              }
            }
          }
        },
      },
      'token' => config.access_token,
      'channel_id' => config.channel_id,
      'team_id' => config.team_id,
    }
  }

  describe '#perform' do
    it 'will updates ticket successfully' do
      allow_any_instance_of(Integrations::Slack::Client).to receive(:send_updated_message).and_return(true)
      allow_any_instance_of(Integrations::Slack::Client).to receive(:send_ephemeral_message).and_return(true)
      allow_any_instance_of(Integrations::Slack::Client).to receive(:get_user_email).and_return(company_user.email)

      expect(company.help_tickets.count).to eq(1)

      #check custom form values
      expect(priority.value_str).to eq('low')
      expect(help_ticket.subject).to eq('Slack Ticket')
      expect(description.value_str).to eq('What a great day!')
      expect(help_ticket.creators[0].contributor_id).to eq(company_user.contributor_id)
      expect(help_ticket.assigned_users.length).to eq(0)
      subject.class.new.perform(config.id, help_ticket.id, payload, nil)

      help_ticket.reload
      expect(company.help_tickets.count).to eq(1)
      activity = help_ticket.help_ticket_activities.find_by(activity_type: 'text')

      #check updated custom form values
      expect(priority.reload.value_str).to eq('medium')
      expect(description.reload.value_str).to eq('What a great week!')
      expect(help_ticket.subject).to eq('Updated Slack Ticket')
      expect(help_ticket.creators[0].contributor_id).to eq(company_user.contributor_id)
      expect(help_ticket.assigned_users).to include(company.groups.find_by(name: 'Help Desk Agents').contributor_id)

      expect(activity.owner_id).to eq(company_user.id)
      expect(activity['data']['source']).to eq('slack')
      expect(activity['data']['current_value']).to eq('Updated Slack Ticket')
    end
  end
end
