require 'rails_helper'

RSpec.describe Integrations::AzureAdAssets::SyncDataWorker, type: :worker do
  describe '#execute' do
    before(:each) do
      @company =  FactoryBot.create(:company)
      @azure_ad_assets_config = Integrations::AzureAdAssets::Config.create(
        token: 'access_token',
        expires_in: 2321,
        refresh_token: 'refresh_token',
        company_id: @company.id)

      @refresh_token = {
        'expires_in' => 3600,
        'access_token' => 'cvsdhbcdsbbhbn35h34j/sbcjhsd',
        'refresh_token' => 'jcbdhjsnxjhvc74yfbhjbc'}

      @device = {
        'displayName'=>'User Genuity',
        'manufacturer'=>'Apple',
        'model'=>'MacBook Pro',
        'operatingSystem'=>'MAC',
        'operatingSystemVersion'=>'10.0.19042.631',
        'deviceId'=>'b059c627-5885-491a-b855-07147d25e397'}
    end

    context 'Fetch AzureAD devices' do
      it 'calls Integrations::AzureAdAssets::FetchData Service' do
        obj = Integrations::AzureAdAssets::SyncDataWorker.new
        devices = OpenStruct.new({ 'value' => [@device.deep_stringify_keys] })
        allow(Integrations::AzureAdAssets::SyncDataWorker).to receive(:new).and_return(obj)
        allow_any_instance_of(Integrations::AzureAdAssets::FetchData).to receive(:refresh_token).and_return(@refresh_token)
        allow_any_instance_of(Integrations::AzureAdAssets::FetchData).to receive(:get_devices).and_return(devices)
        Sidekiq::Testing.inline! do
          Integrations::AzureAdAssets::SyncDataWorker.new.perform(@azure_ad_assets_config.id, true)
        end
        disc_asset = DiscoveredAsset.ready_for_import.find_by(company_id: @company.id, source: 'azure_ad_devices')
        expect(disc_asset).to be_present
        expect(disc_asset.asset_sources.where(source: 'azure_ad_devices').count).to eq(1)
      end
    end
  end
end
