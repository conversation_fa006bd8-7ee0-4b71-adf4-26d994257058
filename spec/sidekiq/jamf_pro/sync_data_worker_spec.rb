require 'rails_helper'

RSpec.describe Integrations::JamfPro::SyncDataWorker, type: :worker do
  describe '#execute' do
    before(:each) do
      @company =  FactoryBot.create(:company)
      @jamf_pro_config = FactoryBot.create(:jamf_pro_config, company: @company)

      @refresh_token = {
        'expires' => DateTime.now + 30.minutes,
        'token' => 'cvsdhbcdsbbhbn35h34j/sbcjhsd',
      }

      @device = {
        "id": "1",
        "udid": "123",
        "general": {
          "name": "<PERSON>ali<PERSON>",
          "lastIpAddress": "**************",
          "lastReportedIp": "**************",
          "platform": "Mac",
          },
        "applications": [
          {
            "name": "Microsoft Word",
          }
        ],
        "userAndLocation": {
          "email": "<EMAIL>",
        },
        "hardware": {
          "make": "Apple",
          "model": "13-inch MacBook Pro (Mid 2012)",
          "modelIdentifier": "MacBookPro9,2",
          "serialNumber": "C02ZC2QYLVDL",
          "processorCount": 2,
          "processorType": "Intel Core i5",
          "processorArchitecture": "i386",
          "macAddress": "6A:2C:4B:B7:65:B5",
          "altMacAddress": "82:45:58:44:dc:01",
          "totalRamMegabytes": 4096,
        },
        "operatingSystem": {
          "name": "Mac OS X",
          "version": "10.9.5",
        },
      }
    end

    context 'Fetch Jamf Pro devices' do
      it 'calls Integrations::JamfPro::FetchData Service' do
        obj = Integrations::JamfPro::SyncDataWorker.new
        devices = OpenStruct.new({ 'results' => [@device.deep_stringify_keys] })
        allow(Integrations::JamfPro::SyncDataWorker).to receive(:new).and_return(obj)
        allow_any_instance_of(Integrations::JamfPro::FetchData).to receive(:token).and_return(@refresh_token)
        allow_any_instance_of(Integrations::JamfPro::FetchData).to receive(:get_computers_inventory).and_return(devices)

        Integrations::JamfPro::SyncDataWorker.new.perform(@jamf_pro_config.id, true)
        disc_asset = DiscoveredAsset.ready_for_import.find_by(company_id: @company.id, source: 'jamf_pro')
        expect(disc_asset).to be_present
        expect(disc_asset.asset_sources.where(source: 'jamf_pro').count).to eq(1)
      end
    end
  end
end
