require 'rails_helper'

RSpec.describe Integrations::Okta::FetchAppsWorker, type: :worker do
  describe '#save_apps' do
    before(:each) do
      @company =  FactoryBot.create(:company)
      @okta_config = FactoryBot.build(:okta_config, company: @company)
      @okta_config.save(validate: false)
    end

    context 'when apps exist and user assignments are found' do
      it 'saves apps and associated user assignments' do

        apps_data = [
          OpenStruct.new({ 'name' => 'App 1', 'id' => 'app_id_1' }),
          OpenStruct.new({ 'name' => 'App 2', 'id' => 'app_id_2' })
        ]

        users_data = [
          OpenStruct.new({ 'id' => 'user_id_1' }),
          OpenStruct.new({ 'id' => 'user_id_2' })
        ]

        allow(Integrations::Okta::FetchAppsWorker).to receive(:new).and_return(Integrations::Okta::FetchAppsWorker.new)
        allow(Integrations::Okta::SaveAppsWorker).to receive(:perform_in) do |schedule_in, *args|
          Integrations::Okta::SaveAppsWorker.new.perform(*args)
        end

        allow_any_instance_of(Integrations::Okta::FetchData).to receive(:fetch_apps).and_return([apps_data, 200, nil])
        allow_any_instance_of(Integrations::Okta::FetchData).to receive(:fetch_users_assigned_to_app).and_return([users_data, 200])

        args = {
          okta_config_id: @okta_config.id,
          first_time: true,
          starting_time: Time.now
        }.to_json

        Integrations::Okta::FetchAppsWorker.new.perform(args)
        expect(Logs::ApiEvent.count).to eq(2)
        expect(Integrations::Okta::App.count).to eq(2)
        expect(Integrations::Okta::App.first.name).to eq('App 1')
        expect(Integrations::AppSource.all.count).to eq(2)
      end
    end
  end
end
