require 'rails_helper'
include HelpTicketHelper

RSpec.describe AutomatedTasks::EventRouting, type: :worker do
  let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
  let!(:company) { FactoryBot.create(:company) }
  let(:workspace_id) { company.default_workspace.id }
  let!(:company_user) { FactoryBot.create(:company_user, company: company, custom_form_id: custom_form.id) }
  let!(:admins) { company.groups.find_by(name: 'Admins') }
  let!(:admins_member) { admins.group_members << GroupMember.new(contributor: company_user.contributor) }
  let!(:time_now) { Time.now }

  subject { described_class.new }

  describe "perform" do
    let(:help_ticket) {
      ticket_params = {
        'Subject' => "Ticket 1",
        'Status' => 'Open',
        company: company,
        workspace_id: workspace_id,
      }
      ticket = create_ticket(ticket_params)
      ticket
    }

    context "with duplicated calls" do
      it "should prevent circular execution" do
        allow(Time).to receive(:now).and_return(time_now)
        expect(AutomatedTasks::TaskExecutor).to receive(:new).once.and_return(AutomatedTasks::TaskExecutor.new({}, help_ticket))
        3.times { described_class.new.perform(help_ticket.id, 'HelpTicket', {}) }
        logs = AutomatedTasks::ExecutionLog.where(workspace_id: workspace_id)
        expect(logs.count).to eq(3)
        expect(logs.where("message like 'Circular execution detected%'").count).to eq(2)
        expect(logs.where(message: 'success', completed_at: time_now).count).to eq(1)
      end
    end
  end
end