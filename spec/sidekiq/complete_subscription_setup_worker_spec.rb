require 'rails_helper'

RSpec.describe CompleteSubscriptionSetupWorker, type: :worker do
  let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
  let!(:company) { FactoryBot.create(:company) }
  let!(:company_user) { FactoryBot.create(:company_user, company: company, custom_form_id: custom_form.id) }
  let!(:company_user2) { FactoryBot.create(:company_user, company: company, custom_form_id: custom_form.id) }
  let!(:subscription_plan) { SubscriptionPlan.create(name: "basic-plan", price: 2999, interval: "month", status: "active") }
  let!(:subscription) { FactoryBot.create(:subscription, company: company, subscription_plan: subscription_plan)}

  subject { CompleteSubscriptionSetupWorker.new }

  describe '#perform' do
    context 'adding subscription data without a referral' do
      before do
        subject.perform(company.id, company_user.id, subscription.id)
        company.reload
      end

      it "adds a Genuity vendor" do
        expect(company.vendors.count).to eq(1)
        expect(company.vendors.first.name).to eq("Genuity")
      end

      it "adds subscription activities" do
        expect(subscription.subscription_activities.count).to eq(2)
        expect(subscription.subscription_activities.first.activity_type).to eq("subscription")
        expect(subscription.subscription_activities.second.activity_type).to eq("payment_source")
      end

      it "should not create a referral record" do
        expect(Referral.count).to eq(0)
      end
    end

    context 'adding subscription data with a referral' do
      let!(:referral) { Referral.create(referrer_id: company_user2.user.id, referred_id: company_user.user.id, referrer_email: company_user2.email, referred_email: company_user.email) }

      before do
        subject.perform(company.id, company_user.id, subscription.id)
        company.reload
      end

      it "should create a referral record" do
        expect(Referral.count).to eq(1)
        expect(Referral.first.status).to eq("earned")
      end
    end
  end
end
