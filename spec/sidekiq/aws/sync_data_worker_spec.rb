require 'rails_helper'

RSpec.describe Integrations::Aws::SyncDataWorker, type: :worker do
  describe '#execute' do
    before(:each) do
      Rails.application.load_seed
      @company = FactoryBot.create(:company)
      allow_any_instance_of(Integrations::Aws::FetchData).to receive(:authenticate?).and_return([true, "success"])
      @aws_config = FactoryBot.create(:aws_config, company_id: @company.id)
      @aws_creds = Aws::Credentials.new(@aws_config.access_key, @aws_config.secret_key)
      metrics = OpenStruct.new(amount:"0.006301", unit:"USD")
      transaction  = OpenStruct.new(keys:["Amazon Elastic Load Balancing"], metrics:{"UnblendedCost"=>metrics})
      metrics1 = OpenStruct.new(amount:"221.232", unit:"USD")
      transaction1  = OpenStruct.new(keys:["Amazon Relational Database Service"], metrics:{"UnblendedCost"=>metrics1})
      groups = [transaction, transaction1]
      time_period = OpenStruct.new(start:"2019-10-21", end:"2019-10-22")
      results_by_time = OpenStruct.new(time_period: time_period, groups: groups)
      @service_transactions = OpenStruct.new(results_by_time: [results_by_time])
      forecast_results_by_time = OpenStruct.new(time_period: time_period, groups: groups)
      @predicted_transactions = OpenStruct.new(forecast_results_by_time: [forecast_results_by_time])
    end

    context 'Sync AWS Transactions' do
      context  'calls Integrations::Aws::FetchData Service' do
        let(:worker) { Integrations::Aws::SyncDataWorker.new }
        let(:client) { Integrations::Aws::FetchData.new(@aws_config) }
        let(:api_client) { Aws::CostExplorer::Client.new(region: @aws_config.region, credentials: @aws_creds) }

        before do
          allow(Integrations::Aws::SyncDataWorker).to receive(:new).and_return(worker)
          allow(Integrations::Aws::FetchData).to receive(:new).and_return(client)
          allow_any_instance_of(Integrations::Aws::FetchData).to receive(:api_client).and_return(api_client)
          allow_any_instance_of(Aws::CostExplorer::Client).to receive(:get_cost_and_usage).and_return(@service_transactions)
          allow_any_instance_of(Aws::CostExplorer::Client).to receive(:get_cost_forecast).and_return(@predicted_transactions)
          worker.perform(@aws_config.id, true)
        end

        it "creates cloud usage transactions" do
          expect(CloudUsageTransaction.where(company_integration_id: @aws_config.company_integration.id).count).to eq(2)
        end

        it "creates does not show an error for the client" do
          expect(client.error).to be_blank
        end

        it "failed the sync status of the integration" do
          integration = @aws_config.company_integration
          expect(integration.sync_status).to eq("successful")
        end
      end
    end

    context 'Sync AWS Transactions' do
      context 'calls Integrations::Aws::FetchData Service with errors' do
        let(:worker) { Integrations::Aws::SyncDataWorker.new }
        let(:client) { Integrations::Aws::FetchData.new(@aws_config) }
        let(:api_client) { Aws::CostExplorer::Client.new(region: @aws_config.region, credentials: @aws_creds) }

        before do
          allow(Integrations::Aws::SyncDataWorker).to receive(:new).and_return(worker)
          allow(Integrations::Aws::FetchData).to receive(:new).and_return(client)
          allow_any_instance_of(Integrations::Aws::FetchData).to receive(:api_client).and_return(api_client)
          allow_any_instance_of(Aws::CostExplorer::Client).to receive(:get_cost_and_usage).and_return(@service_transactions)
          allow_any_instance_of(Aws::CostExplorer::Client).to receive(:get_cost_forecast).and_raise(Aws::CostExplorer::Errors::ServiceError.new({}, 'Boom!'))
          worker.perform(@aws_config.id, true)
        end

        it "creates CloudUsageTransactions" do
          expect(CloudUsageTransaction.where(company_integration_id: @aws_config.company_integration.id).count).to eq(2)
        end

        it "creates an error message" do
          expect(client.error).to eq("Boom!")
        end

        it "failed the sync status of the integration" do
          integration = @aws_config.company_integration
          expect(integration.sync_status).to eq("failed")
        end

        it "assigns an error to the integration" do
          integration = @aws_config.company_integration
          expect(integration.error_message).to be_present
        end
      end
    end
  end
end
