require 'rails_helper'

RSpec.describe FetchTransa<PERSON>Worker, type: :worker do
  let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
  let!(:company) { FactoryBot.create(:company, :with_locations) }
  let!(:company_user) { FactoryBot.create(:company_user, company: company, custom_form_id: custom_form.id) } 
  let!(:admins) { company.groups.find_by(name: 'Admins') }
  let!(:admins_member) { admins.group_members << GroupMember.new(contributor: company_user.contributor) }
  let(:time_format) { '%Y-%M-%d %H:%M' }
  let(:plaid_account) { company.plaid_accounts.create(account_id: "vokyE5Rn6vHKqDLRXEn5fne7LwbKPLIXGK98d") }

  subject do
    worker = described_class.new
    worker.company_id = company.id
    worker
  end

  context "with two transactions" do
    let(:test_time) { Time.strptime('2017-04-01 4:00', time_format) }
    let(:later_time) { test_time + 8.hours }
    let(:tomorrow) { test_time + 1.day }
    let!(:general_transaction) { create(:general_transaction, company: company, transaction_date: tomorrow.to_date ) }
    let!(:general_transaction2) { create(:general_transaction, company: company, transaction_date: later_time ) }

    context "#duration" do
      let(:expected_duration) { Date.today.to_time - later_time.to_date.to_time + 1.week }

      it "equals the right date" do
        Timecop.freeze(test_time) do
          expect(subject.duration).to eq(expected_duration)
        end
      end
    end

    context "#last_transaction_date" do
      it "equals the right date" do
        Timecop.freeze(test_time) do
          expect(subject.last_transaction_date).to eq(test_time.to_date)
        end
      end
    end
  end

  context "with only a later time" do
    let(:test_time) { Time.strptime('2017-04-01 4:00', time_format) }
    let(:tomorrow) { test_time + 1.day }
    let!(:general_transaction) { create(:general_transaction, company: company, transaction_date: tomorrow.to_date ) }

    context "#duration" do
      let(:expected_duration) { Date.today.to_time - last_transaction_date.to_time + 1.week }

      it "equals the right date" do
        Timecop.freeze(test_time) do
          expect(subject.duration).to eq(nil)
        end
      end
    end

    context "#last_transaction_date" do
      it "equals the right date" do
        Timecop.freeze(test_time) do
          expect(subject.last_transaction_date).to eq(nil)
        end
      end
    end
  end
end