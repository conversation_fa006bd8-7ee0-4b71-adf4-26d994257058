require 'rails_helper'

RSpec.describe Integrations::Quickbooks::SyncDataWorker, type: :worker do
  describe '#execute' do
    before(:each) do
      Rails.application.load_seed
      @company = FactoryBot.create(:company)
      @quickbooks_config = FactoryBot.create(:quickbooks_config, company_id: @company.id)

      @company_integration = FactoryBot.create(:company_integration_true,
                                               company_id: @company.id,
                                               integration_id: Integration.find_by_name('quickbooks').id,
                                               integrable_type: @quickbooks_config.class.name,
                                               integrable_id: @quickbooks_config.id)

      @purchases = [
                {"Id"=>"24327787412", "TxnDate"=>"2019-07-24 13:15:23 +0500", "TotalAmt"=>0, "PaymentType"=>"CreditCard", "EntityRef"=>{"name"=>"Vendor Name"}, "MetaData"=> {"CreateTime"=>"2019-07-24 13:15:23 +0500", "LastUpdatedTime"=>"2019-07-24 13:15:23 +0500"}},
                {"Id"=>"24327787112", "TxnDate"=>"2019-07-24 13:15:23 +0500", "TotalAmt"=>13, "PaymentType"=>"CreditCard", "EntityRef"=>{"name"=>"Vendor Name"}, "MetaData"=> {"CreateTime"=>"2019-07-24 13:15:23 +0500", "LastUpdatedTime"=>"2019-07-24 13:15:23 +0500"}},
                {"Id"=>"24327787512", "TxnDate"=>"2019-07-24 13:15:23 +0500", "TotalAmt"=>12, "PaymentType"=>"CreditCard", "EntityRef"=>{"name"=>"Vendor Name"}, "MetaData"=> {"CreateTime"=>"2019-07-24 13:15:23 +0500", "LastUpdatedTime"=>"2019-07-24 13:15:23 +0500"}}
              ]

      # TODO: need to stub these request
      @vendors = [{"id" => 1}, {"id" => 2} ]
      @vendor_expenses = {"Columns"=>[{:value=>123}]}

      @tokens = {
        "access_token" => "vfsdn3yr4yr",
        "refresh_token" => "cvsjbdsjhcbsdbcbsdh"
      }
    end

    context 'SyncQuickbooks Transactions' do
      it 'calls Integrations::Quickbooks::FetchData Service' do
        obj = Integrations::Quickbooks::SyncDataWorker.new
        allow(Integrations::Quickbooks::SyncDataWorker).to receive(:new).and_return(obj)

        allow_any_instance_of(Integrations::Quickbooks::FetchData).to receive(:refresh_token).and_return(@tokens)
        # allow_any_instance_of(Integrations::Quickbooks::FetchData).to receive(:purchases).and_return(@purchases)
        allow_any_instance_of(Integrations::Quickbooks::FetchData).to receive(:get_vendors).and_return(@vendors)
        allow_any_instance_of(Integrations::Quickbooks::FetchData).to receive(:vendor_expense).and_return(@vendor_expenses)

        Integrations::Quickbooks::SyncDataWorker.new.perform(@quickbooks_config.id, false)

        # expect(GeneralTransaction.all.count).to eq(2)
        # expect(GeneralTransaction.where(transaction_type: "purchase").count).to eq(2)
      end
    end
  end
end
