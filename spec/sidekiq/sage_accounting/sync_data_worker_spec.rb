require 'rails_helper'

RSpec.describe Integrations::SageAccounting::SyncDataWorker, type: :worker do
  describe '#execute' do
    before(:each) do
      @company =  FactoryBot.create(:company)
      @sage_accounting_config = FactoryBot.create(:sage_accounting_config, company_id: @company.id)

      @get_all_transactions = OpenStruct.new({parsed_response:
                              {
                                "$total" => 2,
                                "$page" => 1,
                                "$next" => nil,
                                "$back" => nil,
                                "$itemsPerPage" => 20,
                                "$items" =>
                                  [
                                    {
                                      "id" => "240f62a174914e42b4dd7d15e70bf33f",
                                      "displayed_as" => "2020-01-27",
                                      "$path" => "/other_payments/240f62a174914e42b4dd7d15e70bf33f"
                                    },
                                    {
                                      "id" => "453ab4fa6c9740a2b75964ea7187d3e2",
                                      "displayed_as" => "2020-01-27",
                                      "$path" => "/other_payments/453ab4fa6c9740a2b75964ea7187d3e2"
                                    }
                                  ]
                               }
                            })

      @transaction = OpenStruct.new({parsed_response:
                      {
                        "id" => "453ab4fa6c9740a2b75964ea7187d3e2",
                        "displayed_as" => "2020-01-27",
                        "$path" => "/other_payments/453ab4fa6c9740a2b75964ea7187d3e2",
                        "created_at" => "2020-01-27T11:59:42Z",
                        "updated_at" => "2020-01-27T11:59:42Z",
                        "transaction" => {
                          "id" => "d6b6c26c1b0a46c0b2b1b5415ed8216c",
                          "displayed_as" => "",
                          "$path" => "/transactions/d6b6c26c1b0a46c0b2b1b5415ed8216c"
                        },
                        "transaction_type" => {
                          "id" => "OTHER_PAYMENT",
                          "displayed_as" => "Money out",
                          "$path" => "/transaction_types/OTHER_PAYMENT"
                        },
                        "payment_method" => {
                          "id" => "CHECK",
                          "displayed_as" => "Check",
                          "$path" => "/payment_methods/CHECK"
                        },
                        "contact" => {
                          "id" => "dcf1dfa05a49410b9f516ab5e78af1ec",
                          "displayed_as" => "Helo (*********)",
                          "$path" => "/contacts/dcf1dfa05a49410b9f516ab5e78af1ec"
                        },
                        "bank_account" => {
                          "id" => "ec8e797562bb47178e47108139681d62",
                          "displayed_as" => "Checking (1010)",
                          "$path" => "/bank_accounts/ec8e797562bb47178e47108139681d62"
                        },
                        "tax_address_region" => nil,
                        "date" => "2020-01-27",
                        "net_amount" => "30.0",
                        "tax_amount" => "0.0",
                        "total_amount" => "30.0",
                        "reference" => "",
                        "payment_lines" =>
                          [
                            {
                              "id" => "0dbcf03d06a84f94a87971fefb16ca6e",
                              "displayed_as" => "",
                              "ledger_account" =>
                                {
                                  "id" => "798f82003c3b11eaae730ee73a5c6c6b",
                                  "displayed_as" => "Prepaid Expenses (1400)",
                                  "$path" =>"/ledger_accounts/798f82003c3b11eaae730ee73a5c6c6b"
                                },
                              "details" => "",
                              "tax_rate" => nil,
                              "net_amount" => "30.0",
                              "tax_amount" => "0.0",
                              "total_amount" => "30.0",
                              "tax_breakdown" => [],
                              "is_purchase_for_resale" => false,
                              "trade_of_asset" => false,
                              "gst_amount" => nil,
                              "pst_amount" => nil
                            }
                        ],
                        "editable" => true,
                        "deletable" => true,
                        "withholding_tax_rate" => nil,
                        "withholding_tax_amount" => nil
                    }
                  })

      @refresh_token = OpenStruct.new(
                      {parsed_response:
                         {"token_type"=> "Bearer",
                          "scope" => "full_access",
                          "expires_in" => 300,
                          "refresh_token_expires_in" => 2678400,
                          "access_token"=>"eyJ0eXAiOiJKV",
                          "refresh_token"=> "mock_refresh_token",
                          },
                      })
    end

    context 'Sync Sagge Intacct transactions' do
      it 'calls Integrations::SageAccounting::FetchData Service' do
        obj = Integrations::SageAccounting::SyncDataWorker.new
        allow(Integrations::SageAccounting::SyncDataWorker).to receive(:new).and_return(obj)

        allow_any_instance_of(Integrations::SageAccounting::FetchData).to receive(:refresh_token).and_return(@refresh_token.parsed_response)
        allow_any_instance_of(Integrations::SageAccounting::FetchData).to receive(:transactions).and_return(@get_all_transactions.parsed_response)
        allow_any_instance_of(Integrations::SageAccounting::FetchData).to receive(:get_transaction).and_return(@transaction.parsed_response)

        Integrations::SageAccounting::SyncDataWorker.new.perform(@sage_accounting_config.id, true)
        expect(GeneralTransaction.all.count).to eq(1)
        expect(GeneralTransaction.where(primary_category: "Money out",
                                        company_integration_id: @sage_accounting_config.company_integration.id).count).to eq(1)
      end
    end
  end
end
