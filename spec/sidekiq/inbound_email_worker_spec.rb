require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

describe InboundEmailWorker, type: :worker do
  create_company_and_user(subdomain: "test", email: "<EMAIL>")

  class Aws::S3::Client
    def get_object(hash)
      key = hash[:key]
      if key =~ /\/(.+)$/
        message_id = $1
        if File.exist?(Rails.root.join("spec", "data", message_id))
          OpenStruct.new(body: File.open(Rails.root.join("spec", "data", message_id)))
        else
          raise Aws::S3::Errors::NoSuchKey.new(nil, nil)
        end
      end
    end
  end

  subject { described_class.new }

  let(:default_form) {
    company.custom_forms.where(company_module: 'helpdesk').first
  }

  describe "#create" do
    context "creating a new ticket email from a forwarded email" do
      let!(:company_user) { create(:company_user, company: company, user: user) }
      let!(:user) { create(:user, email: "<EMAIL>") }
      let!(:message_id) { "cg0hrbp0aglhtabhjr6g8kge2jqq9mteoe1h0bg1" }
      let!(:email_from) { "<EMAIL>" }

      it "creates a new ticket" do
        existing_ticket_count = HelpTicket.count
        subject.perform(message_id, email_from)
        company = Company.find_by(subdomain: 'test')
        expect(company.help_tickets).to be_present
        ticket_email = company.ticket_emails.first
        help_ticket = company.help_tickets.first
        ticket_subject = help_ticket.custom_form.custom_form_fields.find_by(name: "subject")
        expect(help_ticket.custom_form_values.find_by(custom_form_field_id: ticket_subject.id).value_str).to eq("FW: 52BU0763/0764 Canvas version")
        expect(help_ticket.custom_form_values.count).to eq(7)
        file = File.open(Rails.root.join("spec", "data", "cg0hrbp0aglhtabhjr6g8kge2jqq9mteoe1h0bg1-body"))
        message_body = file.read
        subject.instance_variable_set(:@ticket_email, nil)
        subject.perform(message_id, email_from)
        ticket_count = HelpTicket.count
        ticket = HelpTicket.first
        expect(ticket_count).to eq(existing_ticket_count + 1)
        description = help_ticket.custom_form.custom_form_fields.find_by(name: "description")
        expect(help_ticket.custom_form_values.find_by(custom_form_field_id: description.id).value_str).to include("The information contained in this communication is confidential")
      end
    end

    context "creating a new ticket email from a troubled email" do
      create_company_and_user(subdomain: "leftbank-art", email: "<EMAIL>")
      let!(:message_id) { "vtlt1j4dn8fr2docfpa307289mmefnjsmvumsk01" }
      let!(:custom_form) { company.default_workspace.custom_forms.first }
      let!(:email_from) { "<EMAIL>" }
      let!(:helpdesk_email) {
        HelpdeskCustomEmail.create(email: "<EMAIL>",
                                   company: company,
                                   helpdesk_custom_form: custom_form.helpdesk_custom_form)
      }

      it "creates a new ticket" do
        existing_ticket_count = HelpTicket.count
        subject.perform(message_id, email_from)
        company = Company.find_by(subdomain: 'leftbank-art')
        expect(company.help_tickets).to be_present
        ticket_email = company.ticket_emails.first
        help_ticket = company.help_tickets.first
        expect(help_ticket.email).to eq('<EMAIL>')
        ticket_subject = help_ticket.custom_form.custom_form_fields.find_by(name: "subject")
        expect(help_ticket.custom_form_values.find_by(custom_form_field_id: ticket_subject.id).value_str).to eq("Terminate Access")
        expect(help_ticket.custom_form_values.count).to eq(5)
        file = File.open(Rails.root.join("spec", "data", "vtlt1j4dn8fr2docfpa307289mmefnjsmvumsk01"))
        message_body = file.read
        subject.instance_variable_set(:@ticket_email, nil)
        subject.perform(message_id, email_from)

        ticket_count = HelpTicket.count
        ticket = HelpTicket.first
        expect(ticket_count).to eq(existing_ticket_count + 1)
        description = help_ticket.custom_form.custom_form_fields.find_by(name: "description")
        expect(help_ticket.custom_form_values.find_by(custom_form_field_id: description.id).value_str).not_to match(/body/)
        expect(help_ticket.custom_form_values.find_by(custom_form_field_id: description.id).value_str).not_to match(/head/)
      end
    end

    context "creating a new email response with attachment" do
      context "with a user that exists" do
        let!(:message_id) { "ptg2c67bk4luop98ogdk8greaiqud1s1cmhj03g2" }
        let!(:email_from) { "<EMAIL>" }

        it "creates a new comment" do
          company = Company.find_by(subdomain: 'test')

          ticket_params = {
            'Subject' => "Boom boom",
            'Created By' => company_user.contributor_id,
            'Status' => 'Open',
            'Priority' => "medium",
            'Description' => "The red robin sits in the tree.",
            'ticket_number' => 4,
            'message_id' => "ptg2c67bk4luop98ogdk8greaiqud1s1cmhj03g2",
            workspace: company.workspaces.first
          }

          ticket_params[:skip_save_ticket_number] = true
          ticket = create_ticket(ticket_params)

          subject.perform(message_id, email_from)
          company = Company.find_by(subdomain: 'test')
          existing_ticket = HelpTicket.find_by(ticket_number: 4)
          expect(existing_ticket).to be_present
          expect(existing_ticket.help_ticket_activities).to be_present
          expect(existing_ticket.help_ticket_comments).to be_present
          attachment = existing_ticket.help_ticket_comments.where.not(custom_form_attachment_id: nil)&.last
          expect(attachment&.comment_body).to include('A new attachment was added')
          expect(Logs::TicketEmailLog.find_by(s3_message_id: message_id).present?).to eq(true)
        end
      end

      context "with a missing ticket number in the subject" do
        let!(:message_id) { "8l60nj148tok84jf659cnj0e86gea2jg9jdkgtg3" }
        let!(:email_from) { "<EMAIL>" }

        it "creates a new comment" do
          company = Company.find_by(subdomain: 'test')
          ticket_params = {
            'Subject' => "Boom boom",
            'Created By' => '<EMAIL>',
            'Status' => 'Open',
            'Priority' => "medium",
            'Description' => "The red robin sits in the tree.",
            'ticket_number' => 4,
            'message_id' => "<EMAIL>",
            workspace: company.workspaces.first
          }

          ticket_params[:skip_save_ticket_number] = true
          ticket = create_ticket(ticket_params)
          subject.perform(message_id, email_from)

          company = Company.find_by(subdomain: 'test')
          expect(company.ticket_emails).to be_blank
          existing_ticket = HelpTicket.find_by(ticket_number: 4)
          expect(existing_ticket.help_ticket_activities).to be_present
          expect(existing_ticket.help_ticket_comments.size).to eq(1)
        end
      end

      context "with a user that does not exist" do
        let!(:message_id) { "ptg2c67bk4luop98ogdk8greaiqud1s1cmhj03g2" }
        let!(:email_from) { "<EMAIL>" }

        it "creates a new comment" do
          company = Company.find_by(subdomain: 'test')
          ticket_params = {
            'Subject' => "Boom boom",
            'Created By' => '<EMAIL>',
            'Status' => 'Open',
            'Priority' => "medium",
            'Description' => "The red robin sits in the tree.",
            'ticket_number' => 4,
            'message_id' => "ptg2c67bk4luop98ogdk8greaiqud1s1cmhj03g1",
            workspace: company.workspaces.first
          }

          ticket_params[:skip_save_ticket_number] = true
          ticket = create_ticket(ticket_params)
          subject.perform(message_id, email_from)

          company = Company.find_by(subdomain: 'test')
          expect(company.ticket_emails).to be_blank
          existing_ticket = HelpTicket.find_by(ticket_number: 4)
          expect(existing_ticket.help_ticket_activities).to be_present
          expect(existing_ticket.help_ticket_comments.size).to eq(2)
        end
      end

      context "with helpdesk settings set to true" do
        let!(:message_id) { "ptg2c67bk4luop98ogdk8greaiqud1s1cmhj03g1" }
        let!(:from) { "<EMAIL>" }

        it "creates a new comment" do
          company = Company.find_by(subdomain: 'test')
          company.helpdesk_settings.first.update(enabled: true)
          ticket_params = {
            'Subject' => "Boom boom",
            'Created By' => company_user.contributor_id,
            'Status' => 'Open',
            'Priority' => "medium",
            'Description' => "The red robin sits in the tree.",
            'ticket_number' => 4,
            'message_id' => "ptg2c67bk4luop98ogdk8greaiqud1s1cmhj03g1",
            workspace: company.workspaces.first
          }

          ticket_params[:skip_save_ticket_number] = true
          ticket = create_ticket(ticket_params)
          subject.perform( message_id, from)
          company = Company.find_by(subdomain: 'test')
          expect(company.ticket_emails).to be_blank
          existing_ticket = HelpTicket.find_by(ticket_number: 4)
          expect(existing_ticket).to be_present
          expect(existing_ticket.help_ticket_activities).to be_present
        end
      end
    end

    context "adding CC'd users as followers when commenting on a ticket from email" do
      let(:cc_user_attributes) do
        {
          email: "<EMAIL>",
          password: "password2",
          first_name: "Test",
          last_name: "User",
          terms_of_services: true
        }
      end
    
      let!(:cc_user) { create(:user, cc_user_attributes) }
      let!(:company_user_1) { CompanyUser.create(company_id: company.id, user_id: cc_user.id) }
      let!(:message_id) { "ptg2c67bk4luop98ogdk8greaiqud1s1cmhj03g2" }
      let!(:email_from) { "<EMAIL>" }
      let!(:incoming_helpdesk_setting) { DefaultHelpdeskSetting.find_by(setting_type: "allow_incoming_requests") }
      let!(:followers) {
        company.custom_forms.where(company_module: 'helpdesk').first.custom_form_fields.find_or_create_by(
          name: 'followers',
          field_attribute_type: CustomFormFieldTemplate.field_attribute_types["people_list"],
          label: "Followers"
        )
      }
      let!(:impacted_helpdesk_setting) { DefaultHelpdeskSetting.find_by(setting_type: "allow_making_email_comment_recipients_ticket_followers") }
    
      it "should add CC'd users to followers when adding a comment" do
        company.helpdesk_settings.find_by(default_helpdesk_setting_id: incoming_helpdesk_setting.id).update(enabled: true)
        company.helpdesk_settings.find_by(default_helpdesk_setting_id: impacted_helpdesk_setting.id).update(enabled: true)
    
        ticket_params = {
          'Subject' => "Boom boom",
          'Created By' => company_user_1.contributor_id,
          'Status' => 'Open',
          'Priority' => "medium",
          'Description' => "The red robin sits in the tree.",
          'ticket_number' => 4,
          'message_id' => message_id,
          workspace: company.workspaces.first
        }
    
        ticket_params[:skip_save_ticket_number] = true
        ticket = create_ticket(ticket_params)
        subject.perform(message_id, email_from)
    
        ticket.reload
        followers_field = ticket.custom_form.custom_form_fields.find_by(name: "followers")
    
        expect(followers_field).to be_present
    
        followers_value = ticket.custom_form_values.find_by(custom_form_field_id: followers_field.id).value_int
        expect(followers_value).to eq(company_user_1.contributor_id)
      end
    end
  
    context "creating a new ticket email from email" do
      let!(:message_id) { "7vcnabmc1807es6n1suhfgt792kvdahl2m9g0fg1" }
      let!(:email_from) { "<EMAIL>" }

      it "creates a new ticket email when allow_incoming_requests setting is false" do
        subject.perform(message_id, email_from)
        company = Company.find_by(subdomain: 'test')
        expect(company.ticket_emails).to be_present
        expect(company.ticket_emails.count).to eq(1)
        ticket_email = company.ticket_emails.first
        expect(ticket_email.subject).to eq("TEST SUBJ")
        expect(ticket_email.body_text).to include("TEST MESS")
        expect(ticket_email.to).to eq("<EMAIL>")
        expect(ticket_email.from).to eq("<EMAIL>")
        expect(ticket_email.company_id).to eq(company.id)
        expect(ticket_email.workspace_id).to eq(company.workspaces.first.id)
        expect(ticket_email.body_html).to eq("<div dir=\"ltr\">TEST MESS<br>\n</div>")
      end

      it "does not create a new ticket email when allow_incoming_requests setting is true" do
        company = Company.find_by(subdomain: 'test')
        helpdesk_setting = DefaultHelpdeskSetting.find_by(setting_type: "allow_incoming_requests")
        company.helpdesk_settings.find_by(default_helpdesk_setting_id: helpdesk_setting.id).update(enabled: true)

        subject.perform(message_id, email_from)

        expect(company.ticket_emails.last.ticket_created).to eq(true)
        expect(company.ticket_emails.count).to eq(1)
      end
    end

    context "creating a new ticket from email" do
      let!(:message_id_1) { "6h37ds5qrtu0gdn1soar84f7aepeqgrk9r9k0vg1" }
      let!(:message_id_2) { "7vcnabmc1807es6n1suhfgt792kvdahl2m9g0fg1" }
      let!(:email_from_1) { "<EMAIL>" }
      let!(:email_from_2) { "<EMAIL>" }

      it "creates a new ticket when user email address is that of the company domain and allow_incoming_requests setting is false" do
        subject.perform(message_id_1, email_from_1)

        ticket = HelpTicket.last
        company = Company.find_by(subdomain: 'test')
        ticket_s3_id = Logs::TicketEmailLog.find_by(help_ticket_id: ticket.id)&.s3_message_id
        expect(ticket_s3_id).to eq(message_id_1)

        ticket_subject = ticket.custom_form.custom_form_fields.find_by(name: "subject")
        expect(ticket).to be_present
        expect(ticket.custom_form_values.find_by(custom_form_field_id: ticket_subject.id).value_str).to eq("testing")
        expect(ticket.company).to eq(company)
        body_text =<<~END
          this is a test message

          - Bob
        END
        description = ticket.custom_form.custom_form_fields.find_by(name: "description")
        expect(ticket.custom_form_values.find_by(custom_form_field_id: description.id).value_str).to eq("<div dir=\"ltr\">this is a test message<div><br></div>\n<div>- Bob</div>\n</div>")
      end

      it "creates a new ticket when user email address is different than company domain and allow_incoming_requests setting is true " do
        company = Company.find_by(subdomain: 'test')
        helpdesk_setting = DefaultHelpdeskSetting.find_by(setting_type: "allow_incoming_requests")
        company.helpdesk_settings.find_by(default_helpdesk_setting_id: helpdesk_setting.id).update(enabled: true)
        subject.perform(message_id_2, email_from_2)

        ticket = HelpTicket.last
        expect(ticket).to be_present
        subject = ticket.custom_form.custom_form_fields.find_by(name: "subject")
        expect(ticket.custom_form_values.find_by(custom_form_field_id: subject.id).value_str).to eq("TEST SUBJ")

        expect(ticket.company).to eq(company)
        description = ticket.custom_form.custom_form_fields.find_by(name: "description")
        expect(ticket.custom_form_values.find_by(custom_form_field_id: description.id).value_str).to eq("<div dir=\"ltr\">TEST MESS<br>\n</div>")
        expect(Logs::TicketEmailLog.find_by(s3_message_id: message_id_2).present?).to eq(true)
      end
    end

    context "creating a new ticket from email with blocked entities" do
      let!(:message_id) { "6h37ds5qrtu0gdn1soar84f7aepeqgrk9r9k0vg1" }
      let(:from_email) { "<EMAIL>" }

      before do
        HelpTicket.destroy_all
        TicketEmail.destroy_all
      end

      before(:each) do
        BlockedEntity.destroy_all
      end

      it "does create a new ticket email for non-blocked domain" do
        BlockedEntity.create(company_id: company.id, workspace_id: company.default_workspace.id, entity: "@ple.com", entity_type: 1)
        subject.perform(message_id, from_email)

        ticket = HelpTicket.find_by(email: "<EMAIL>")
        expect(Logs::TicketEmailLog.find_by(s3_message_id: message_id).log_type).to eq("valid_email")
      end

      it "does not create a new ticket email if blocked email address" do
        BlockedEntity.create(company_id: company.id, workspace_id: company.default_workspace.id, entity: "<EMAIL>", entity_type: 0)
        subject.perform(message_id, from_email)

        ticket = HelpTicket.find_by(email: "<EMAIL>")
        expect(ticket).to_not be_present
        expect(Logs::TicketEmailLog.find_by(s3_message_id: message_id).log_type).to eq("email_blocked")
      end

      it "does not create a new ticket email if blocked domain" do
        BlockedEntity.create(company_id: company.id, workspace_id: company.default_workspace.id, entity: "example.com", entity_type: 1)
        subject.perform(message_id, from_email)

        ticket = HelpTicket.find_by(email: "<EMAIL>")
        expect(ticket).to_not be_present
      end
    end

    context "adding a comment from email" do
      context "for a user" do
        before(:example) do
          ticket_params = {
            'Subject' => "Boom boom",
            'Created By' => company_user.contributor_id,
            'Assigned To' => company_user.contributor_id,
            'Followers' => company_user.contributor_id,
            'Status' => 'Open',
            'Priority' => "medium",
            'Description' => "The red robin sits in the tree.",
            'ticket_number' => 122,
            'message_id' => "8l60nj148tok84jf659cnj0e86gea2jg9jdkgtg1",
            workspace: company.workspaces.first
          }
          ticket_params[:skip_save_ticket_number] = true
          ticket = create_ticket(ticket_params)

          @message_id = "8l60nj148tok84jf659cnj0e86gea2jg9jdkgtg1"
          from_email = "<EMAIL>"
          email_path = Rails.root.join('spec/data', "8l60nj148tok84jf659cnj0e86gea2jg9jdkgtg1")
          email_text = File.read(email_path)
          s3_message_id = "tok84jf659cnj0e86gea2jg9jdkgt"
          comment_email, attatched_files_paths = TicketMailer.new.receive(email_text, s3_message_id)
          allow(subject).to receive(:download_ticket_email).and_return(comment_email)

          subject.perform(@message_id, from_email)
        end

        it "creates a new comment" do
          ticket = HelpTicket.find_by(ticket_number: 122)
          comment = ticket.help_ticket_activities.find_by_activity_type('note')
          expect(comment.data['new_comment_body']).to include("Inserted image")
          expect(Logs::TicketEmailLog.find_by(s3_message_id: @message_id).log_type).to eq("reply")

          #Todo: Need to fix Paperclip::Errors::NotIdentifiedByImageMagickError
          # expect(AttachmentUpload.find_by(attachment_file_name: "Selection_037.png")).to be_present
          # expect(AttachmentUpload.find_by(attachment_file_name: "Selection_036.png")).to be_present
        end
      end

      context "for an external email" do
        let!(:message_id) { "8l60nj148tok84jf659cnj0e86gea2jg9jdkgtg2" }
        let!(:email_from) { "<EMAIL>" }
        before(:example) do
          ticket_params = {
            'Subject' => "Boom boom",
            'Created By' => '<EMAIL>',
            'Assigned To' => company_user.contributor_id,
            'Followers' => company_user.contributor_id,
            'Status' => 'Open',
            'Priority' => "medium",
            'Description' => "The red robin sits in the tree.",
            'ticket_number' => 122,
            'message_id' => "8l60nj148tok84jf659cnj0e86gea2jg9jdkgtg2",
            workspace: company.workspaces.first
          }
          ticket_params[:skip_save_ticket_number] = true
          ticket = create_ticket(ticket_params)

          email_path = Rails.root.join('spec/data', "8l60nj148tok84jf659cnj0e86gea2jg9jdkgtg2")
          email_text = File.read(email_path)
          s3_message_id = '60nj148tok84jf659cnj0e86gea2j'
          comment_email, attatched_files_paths = TicketMailer.new.receive(email_text, s3_message_id)
          allow(subject).to receive(:download_ticket_email).and_return(comment_email)

          subject.perform(message_id, email_from)
        end

        it "creates a new comment" do
          ticket = HelpTicket.find_by(ticket_number: 122)
          comment = ticket.help_ticket_activities.find_by_activity_type('note')
          expect(comment.data['new_comment_body']).to include("Inserted image")
          expect(Logs::TicketEmailLog.find_by(s3_message_id: message_id).log_type).to eq("reply")

          #Todo: Need to fix Paperclip::Errors::NotIdentifiedByImageMagickError
          # expect(AttachmentUpload.find_by(attachment_file_name: "Selection_037.png")).to be_present
          # expect(AttachmentUpload.find_by(attachment_file_name: "Selection_036.png")).to be_present
        end
      end
    end

    context "add a comment from email and change ticket status to open" do
      let!(:message_id) { "qs1o1f6hlnpiv0krpgha6728jdt2bobgoqjj82g1" }
      let!(:email_from) { "<EMAIL>" }
      before(:example) do
        ticket_params = {
          'Subject' => "Boom boom",
          'Created By' => company_user.contributor_id,
          'Status' => 'Closed',
          'Priority' => "medium",
          'Description' => "The red robin sits in the tree.",
          'ticket_number' => 122,
          'message_id' => "qs1o1f6hlnpiv0krpgha6728jdt2bobgoqjj82g1",
          workspace: company.workspaces.first
        }

        ticket_params[:skip_save_ticket_number] = true
        ticket = create_ticket(ticket_params)
        email_path = Rails.root.join('spec/data', "qs1o1f6hlnpiv0krpgha6728jdt2bobgoqjj82g1")
        email_text = File.read(email_path)
        s3_message_id = 's1o1f6hlnpiv0krpgha6728j'
        comment_email, attatched_files_paths = TicketMailer.new.receive(email_text, s3_message_id)
        allow(subject).to receive(:download_ticket_email).and_return(comment_email)
        DefaultHelpdeskSetting.find_each do |setting|
          helpdesk_setting = company.helpdesk_settings.find_or_initialize_by(
            company_id: company.id,
            default_helpdesk_setting_id: setting.id
          )
          if setting.setting_type == "response_to_closed_ticket"
            helpdesk_setting.assign_attributes( selected_option: "add_comment_and_help_center")
          end
          helpdesk_setting.save!
        end

        subject.perform(message_id, email_from)
      end

      describe "" do
        it "ticket created successfully" do
          ticket = HelpTicket.last
          ticket_s3_id = Logs::TicketEmailLog.find_by(help_ticket_id: ticket.id)&.s3_message_id
          expect(ticket_s3_id).to eq(message_id)
        end

        it "ticket created via mail has a comment" do
          expect(HelpTicket.last.help_ticket_comments.count).to eq(1)
        end

        it "creates a new comment" do
          ticket = HelpTicket.find_by(ticket_number: 122)
          comment = ticket.help_ticket_activities.find_by_activity_type('note')
          expect(comment.data['new_comment_body']).to include("I hope this turns out ok.")
        end
      end
    end

    describe "adding email twice" do
      before(:example) do
        id1 = '0dmiu2ksgbjsd3ledl02kaj8ponnp7i51r846v81'
        id2 = '0dmiu2ksgbjsd3ledl02kaj8ponnp7i51r846v82'
        email_1 = '<EMAIL>'
        email_2 = '<EMAIL>'
        params1 = { message_id:  id1 }
        params2 = { message_id:  id2 }
        subject.perform(id1, email_1)
        subject.instance_variable_set(:@ticket_email, nil)
        subject.instance_variable_set(:@ticket, nil)
        subject.perform(id2, email_2)
      end

      describe "with the first request" do
        it "creates a help ticket email" do
          emails = TicketEmail.where(message_id: 'CA+MGk8Sn6P3xFEYkEDbykuA_D=<EMAIL>')
          expect(emails.count).to eq(1)
          email = emails.first
          expect(email).to be_present
          expect(email.to).to be_present
          expect(email.to).to eq("<EMAIL>")
          expect(email.from).to be_present
          expect(email.from).to eq("<EMAIL>")
          expect(email.subject).to be_present
          expect(email.ticket_email_attachments).to be_present
        end
      end

      describe "with the second request" do
        it "creates a help ticket" do
          emails = TicketEmail.where(message_id: 'CA+MGk8Sn6P3xFEYkEDbykuA_D=<EMAIL>')
          expect(emails.count).to eq(1)
        end
      end
    end

    describe "adding ticket twice" do
      before(:example) do
        id1 = 'ptg2c67bk4luop98ogdk8greaiqud1s1cmhj03g9'
        id2 = 'ptg2c67bk4luop98ogdk8greaiqud1s1cmhj03g9'
        from_email = '<EMAIL>'
        subject.perform(id1, from_email)
        subject.instance_variable_set(:@ticket_email, nil)
        subject.instance_variable_set(:@ticket, nil)
        subject.perform(id2, from_email)
      end

      describe "with the first request" do
        it "creates a help ticket" do
          tickets = HelpTicket.where(message_id: 'CA+MGk8QNHpOZ9-KwkPUdhpCESucXYkBi_mcQO=<EMAIL>')
          expect(tickets.count).to eq(1)
          ticket = tickets.first
          expect(ticket).to be_present
          subject = ticket.custom_form.custom_form_fields.find_by(name: "subject")
          description = ticket.custom_form.custom_form_fields.find_by(name: "description")
          expect(ticket.custom_form_values.find_by(custom_form_field_id: subject.id).value_str).to be_present
          expect(ticket.custom_form_values.find_by(custom_form_field_id: description.id)).to be_present
          expect(ticket.custom_form_values).to be_present
        end
      end

      describe "with the second request" do
        it "creates a help ticket" do
          tickets = HelpTicket.where(message_id: 'CA+MGk8QNHpOZ9-KwkPUdhpCESucXYkBi_mcQO=<EMAIL>')
          expect(tickets.count).to eq(1)
        end
      end
    end

    describe "adding comment twice" do
      before do
        company.helpdesk_settings.first.update(enabled: true)
        ticket_params = {
          'Subject' => "Boom boom",
          'Created By' => company_user.contributor_id,
          'Status' => 'Open',
          'Priority' => "low",
          'Description' => "The red robin sits in the tree.",
          'ticket_number' => 4,
          'message_id' => "ptg2c67bk4luop98ogdk8greaiqud1s1cmhj03g1",
          workspace: company.workspaces.first
        }

        ticket_params[:skip_save_ticket_number] = true
        ticket = create_ticket(ticket_params)

        id1 = 'ptg2c67bk4luop98ogdk8greaiqud1s1cmhj03g1' # <AUTHOR> <EMAIL>
        id2 = 'ptg2c67bk4luop98ogdk8greaiqud1s1cmhj03g2'
        email_1 = '<EMAIL>'
        email_2 = '<EMAIL>'
        subject.perform(id1, email_1)
        subject.perform(id2, email_2)
      end

      context "with the first request" do
        it "creates a response" do
          tickets = HelpTicket.where(ticket_number: 4)
          expect(tickets.count).to eq(1)
          ticket = tickets.first
          attachment_activity = ticket.help_ticket_activities.where(activity_type: 'note')
          expect(ticket).to be_present
          expect(attachment_activity).to be_present
          expect(attachment_activity.length).to eq(1)
        end
      end

      context "with the second request" do
        it "creates a help ticket" do
          tickets = HelpTicket.where(ticket_number: 4)
          expect(tickets.count).to eq(1)
          ticket = tickets.first
          attachment_activity = ticket.help_ticket_activities.where(activity_type: 'note')

          expect(ticket).to be_present
          expect(attachment_activity).to be_present
          expect(attachment_activity.length).to eq(1)
        end
      end
    end

    context "creating a new ticket email from a bounced email" do
      let!(:message_id) { "jpctbieleu8ol8er6ivrkuetsk24obsmd8bo9eg1" }
      let!(:email_from) { "<EMAIL>" }

      it "creates a new ticket email" do
        existing_ticket_count = HelpTicket.count
        subject.perform(message_id, email_from)
      end
    end

    context "when sending multiple emails with the same from and subject" do
      let!(:company_user) { create(:company_user, company: company, user: user) }
      let!(:user) { create(:user, email: "<EMAIL>") }
      let!(:message_ids) do
        [
          "0dmiu2ksgbjsd3ledl02kaj8ponnp7i51r846v81",
          "0dmiu2ksgbjsd3ledl02kaj8ponnp7i51r846v83",
          "0dmiu2ksgbjsd3ledl02kaj8ponnp7i51r846v84",
          "0dmiu2ksgbjsd3ledl02kaj8ponnp7i51r846v85",
          "0dmiu2ksgbjsd3ledl02kaj8ponnp7i51r846v86"
        ]
      end
      let(:message_id) { "0dmiu2ksgbjsd3ledl02kaj8ponnp7i51r846v87" }
      let!(:email_from) { "<EMAIL>" }
      it "should throttle creation after the first 5 emails received" do
        company.help_tickets.destroy_all
        message_ids.each do |id|
          subject.instance_variable_set(:@ticket_email, nil)
          subject.instance_variable_set(:@ticket, nil)
          params = { message_id:  id }
          existing_ticket_count = HelpTicket.count
          company = Company.find_by(subdomain: 'test')
          subject.perform(id, email_from)
          expect(company.help_tickets).to be_present
        end
        subject.instance_variable_set(:@ticket_email, nil)
        subject.instance_variable_set(:@ticket, nil)
        existing_ticket_count = HelpTicket.count
        subject.perform(message_id, email_from)

        company = Company.find_by(subdomain: 'test')
        expect(company.help_tickets).to be_present
      end
    end

    context "not creating auto replied/out of office emails" do
      let!(:message_id) { "ks5saqei2ud4bpk0sr0sv2ngdm6ckqriqgtaj5o1" }
      let!(:file) { File.open(Rails.root.join("spec", "data", message_id)) }
      let!(:email_from) { "<EMAIL>" }
      let!(:email_body) { file.read }

      it "should not create new email for auto replied mails" do
        subject.perform(message_id, email_from)
        expect(Logs::TicketEmailLog.find_by(s3_message_id: message_id).present?).to eq(true)
        expect(Logs::TicketEmailLog.find_by(s3_message_id: message_id).log_type).to eq("auto_replied_email")
      end
    end

    context "making CC'd users the followers for help tickets" do
      let(:user_attributes) do
        {
          email: "<EMAIL>",
          password: "password2",
          first_name: "Test",
          last_name: "User",
          terms_of_services: true
        }
      end

      let!(:user) { create(:user, user_attributes) }
      let!(:company_user_1) { CompanyUser.create(company_id: company.id, user_id: user.id) }
      let!(:message_id) { "davl6cjbel366kk29p8qahq71vnaju23npverko1" }
      let!(:email_from) { "<EMAIL>" }
      let!(:guest_email) { "<EMAIL>" }
      let!(:message_id_1) { "7gqsovjisgd4ncd35po5n8c6n5c1ujmo6h0t5r01" }
      let!(:incoming_helpdesk_setting) { DefaultHelpdeskSetting.find_by(setting_type: "allow_incoming_requests") }
      let!(:followers) {
        company.custom_forms.where(company_module: 'helpdesk').first.custom_form_fields.find_or_create_by(
          name: 'followers',
          field_attribute_type: CustomFormFieldTemplate.field_attribute_types["people_list"],
          label: "Followers"
        )
      }
      let!(:impacted_helpdesk_setting) { DefaultHelpdeskSetting.find_by(setting_type: "allow_making_email_recipients_ticket_followers") }

      it "should make the followers of ticket when allow_making_email_recipients_ticket_followers setting in true" do
        company.helpdesk_settings.find_by(default_helpdesk_setting_id: incoming_helpdesk_setting.id).update(enabled: true)
        company.helpdesk_settings.find_by(default_helpdesk_setting_id: impacted_helpdesk_setting.id).update(enabled: true)

        subject.perform(message_id, email_from)

        expect(company.help_tickets.count).to eq(1)
        ticket = company.help_tickets.last
        followers_field = ticket.custom_form.custom_form_fields.find_by(name: "followers")
        expect(followers_field).to be_present
        expect(ticket.custom_form_values.find_by(custom_form_field_id: followers_field.id).value_int).to eq(company_user_1.contributor_id)
      end

      it "should not make the followers of ticket when allow_making_email_recipients_ticket_followers setting in false" do
        company.helpdesk_settings.find_by(default_helpdesk_setting_id: incoming_helpdesk_setting.id).update(enabled: true)
        company.helpdesk_settings.find_by(default_helpdesk_setting_id: impacted_helpdesk_setting.id).update(enabled: false)

        subject.perform(message_id, email_from)

        expect(company.help_tickets.count).to eq(1)
        ticket = company.help_tickets.last
        followers_field = ticket.custom_form.custom_form_fields.find_by(name: "followers")
        expect(followers_field).to be_present
        expect(ticket.custom_form_values.find_by(custom_form_field_id: followers_field.id)).to eq(nil)
      end

      it "should make the followers of ticket when allow_making_email_recipients_ticket_followers setting in true but CC'd user doesn't exists" do
        company.helpdesk_settings.find_by(default_helpdesk_setting_id: incoming_helpdesk_setting.id).update(enabled: true)
        company.helpdesk_settings.find_by(default_helpdesk_setting_id: impacted_helpdesk_setting.id).update(enabled: true)

        subject.perform(message_id_1, email_from)

        expect(company.help_tickets.count).to eq(1)
        ticket = company.help_tickets.last
        followers_field = ticket.custom_form.custom_form_fields.find_by(name: "followers")
        expect(followers_field).to be_present
        guest = Guest.find_by(email: guest_email)
        expect(ticket.custom_form_values.find_by(custom_form_field_id: followers_field.id).value_int).to eq(guest.contributor_id)
      end
    end

    context "creating a ticket with a missing description field" do
      before do
        HelpTicket.destroy_all
        TicketEmail.destroy_all
      end

      let!(:message_id) { "cg0hrbp0aglhtabhjr6g8kge2jqq9mteoe1h0bg1" }
      let!(:email_from) { "<EMAIL>" }

      it "should create a ticket from email with email body mapped onto the first rich_text field" do
        helpdesk_setting = DefaultHelpdeskSetting.find_by(setting_type: "allow_incoming_requests")
        company.helpdesk_settings.find_by(default_helpdesk_setting_id: helpdesk_setting.id).update(enabled: true)

        subject.perform(message_id, email_from)

        expect(company.help_tickets.count).to eq(1)
        ticket = company.help_tickets.last
        text_field = ticket.custom_form.custom_form_fields.where(name: 'description').first
        expect(ticket.custom_form_values.find_by(custom_form_field_id: text_field.id).value_str).to include("The information contained in this communication is confidential")
      end
    end
  end
end
