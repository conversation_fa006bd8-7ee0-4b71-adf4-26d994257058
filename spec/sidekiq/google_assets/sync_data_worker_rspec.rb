require 'rails_helper'

RSpec.describe Integrations::GoogleAssets::SyncDataWorker, type: :worker do
  describe '#execute' do
    before(:all) do
      Rails.application.load_seed
      @company = FactoryBot.create(:company)
      @google_assets_config = FactoryBot.create(:google_assets_config, company_id: @company.id)

      @company_integration = FactoryBot.create(
        :company_integration_true,
        company_id: @company.id,
        integration_id: Integration.find_by_name('google_assets').id,
        integrable_type: @google_assets_config.class.name,
        integrable_id: @google_assets_config.id)
      @instances = [{:can_ip_forward=>false,
                     :cpu_platform=>"Intel Broadwell",
                     :creation_timestamp=>"2021-06-28T07:06:20.044-07:00",
                     :deletion_protection=>false,
                     :description=>"",
                     :disks=>[{:auto_delete=>true, :device_name=>"instance-1"}],
                     :machine_type=>"https://www.googleapis.com/compute/v1/projects/genuity-project/zones/us-west1-b/machineTypes/e2-micro",
                     :network_interfaces=>[:access_configs=>[{:name=>"External NAT", :nat_ip=>"************"}]],
                     :name=>"nic0",
                     :network_ip=>"**********",
                     :shielded_instance_config=>{:enable_integrity_monitoring=>true,
                     :enable_secure_boot=>false,
                     :enable_vtpm=>true},
                     :shielded_instance_integrity_policy=>{:update_auto_learn_policy=>true},
                     :start_restricted=>false,
                     :status=>"RUNNING",
                     :zone=>"us-west1-b",
                     :project_id=>"genuity-project"}]
      @machine_info = {:cores=>2, :memory=>"1GB", :is_shared_cpu=>true}
    end

    context 'Sync Google Cloud Virtual Machines' do
      it 'calls Integrations::GoogleAssets::FetchData Service' do
        obj = Integrations::GoogleAssets::SyncDataWorker.new
        allow(Integrations::GoogleAssets::SyncDataWorker).to receive(:new).and_return(obj)
        allow_any_instance_of(Integrations::GoogleAssets::FetchData).to receive(:list_instances).and_return(@instances)
        allow_any_instance_of(Integrations::GoogleAssets::FetchData).to receive(:get_disk).and_return(10)
        allow_any_instance_of(Integrations::GoogleAssets::FetchData).to receive(:get_machine_info).and_return(@machine_info)

        Integrations::GoogleAssets::SyncDataWorker.new.perform(@google_assets_config.id, true)

        disc_asset = DiscoveredAsset.ready_for_import.find_by(company_id: @company.id, asset_type: 'Virtual Machine', source: 'google')
        expect(disc_asset).to be_present
        expect(disc_asset.asset_sources.where(source: 'google').count).to eq(1)
        expect(disc_asset.integration_location).to be_present
      end
    end
  end
end
