require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

RSpec.describe CreateSlackTicketCommentWorker, type: :worker do
  create_company_and_user

  before { help_ticket }
  let(:config) { create(:slack_config, company: company) }

  let!(:ticket_params) {
    {
      'Subject' => 'Slack Ticket',
      'Created By' => company_user.contributor_id,
      'Status' => 'open',
      'Priority' => 'low',
      'Description' => 'What a great day!',
      workspace: company.default_workspace,
    }
  }

  let(:help_ticket) { create_ticket(ticket_params) }
  let(:payload) {
    {
      'type' => 'view_submission',
      'user' => {
        'id' => 'U04AQ4933FT',
        'username' => 'john_liam',
        'name' => 'john_liam',
        'team_id' => config.team_id,
      },
      'view' => {
        'callback_id' => 'add_comment',
        'private_metadata' => "{\"help_ticket_id\":#{help_ticket.id},\"channel_id\":\"D04AQ62708H\",\"thread_ts\":null,\"global_shortcut\":true,\"is_dm_ticket\":false}",
        'blocks' => [
          {
            'type' => 'input',
            'block_id' => 'cl7kw',
            'label' => {
              'type' => 'plain_text',
              'text' => 'Comment'
            },
            'element' => {
              'type' => 'plain_text_input',
              'action_id' => 'plain_text_input-action',
              'dispatch_action_config' => {
                'trigger_actions_on' => ['on_enter_pressed']
              }
            }
          }
        ],
        'state' => {
          'values' => {
            'cl7kw' => {
              'plain_text_input-action' => {
                'type' => 'plain_text_input',
                'value' => 'comment for slack ticket'
              }
            }
          }
        },
      },
      'private_comment' => false,
      'token' => config.access_token,
      'channel_id' => config.channel_id,
      'team_id' => config.team_id,
    }
  }

  let(:payload2) {
    {
      'type' => 'view_submission',
      'user' => {
        'id' => 'U04AQ4933FT',
        'username' => 'john_liam',
        'name' => 'john_liam',
        'team_id' => config.team_id,
      },
      'view' => {
        'callback_id' => 'add_comment',
        'private_metadata' => "{\"help_ticket_id\":#{help_ticket.id},\"channel_id\":\"D04AQ62708H\",\"thread_ts\":null,\"global_shortcut\":true,\"is_dm_ticket\":false}",
        'blocks' => [
          {
            'type' => 'input',
            'block_id' => 'cl7kw',
            'label' => {
              'type' => 'plain_text',
              'text' => 'Comment'
            },
            'element' => {
              'type' => 'plain_text_input',
              'action_id' => 'plain_text_input-action',
              'dispatch_action_config' => {
                'trigger_actions_on' => ['on_enter_pressed']
              }
            }
          }
        ],
        'state' => {
          'values' => {
            'cl7kw' => {
              'plain_text_input-action' => {
                'type' => 'plain_text_input',
                'value' => 'private slack comment'
              }
            }
          }
        },
      },
      'private_comment' => true,
      'token' => config.access_token,
      'channel_id' => config.channel_id,
      'team_id' => config.team_id,
    }
  }

  describe '#perform' do
    before {
      allow_any_instance_of(Integrations::Slack::Client).to receive(:send_ephemeral_message).and_return(true)
      allow_any_instance_of(Integrations::Slack::Client).to receive(:get_user_email).and_return(company_user.email)
    }

    it 'will add a comment to ticket' do
      expect(company.help_tickets).to be_present

      subject.class.new.perform(config.id,nil, false, nil, nil, help_ticket.id, payload)

      comment = help_ticket.help_ticket_comments.last
      expect(help_ticket.help_ticket_comments).to be_present
      expect(comment.comment_body).to eq('<div><!--block-->comment for slack ticket</div>')
      expect(comment.email).to eq(company_user.email)
      expect(comment.private_flag).to eq(false)
      expect(comment.source).to eq('slack')
    end

    it 'will add a private comment to ticket' do
      expect(company.help_tickets).to be_present

      subject.class.new.perform(config.id,nil, false, nil, nil, help_ticket.id, payload2)
      expect(help_ticket.help_ticket_comments).to be_present
      comment = help_ticket.help_ticket_comments.last
      expect(comment.private_contributor_ids).to include(company_user.contributor_id.to_s)
      expect(comment.comment_body).to eq('<div><!--block-->private slack comment</div>')
      expect(comment.email).to eq(company_user.email)
      expect(comment.private_flag).to eq(true)
      expect(comment.source).to eq('slack')
    end
  end
end
