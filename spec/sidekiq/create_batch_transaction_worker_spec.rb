#<GeneralTransaction
#     id: 2514207,
#     product_name: "ADP WAGE PAY WAGE PAY",
#     amount: 132888.31,
#     created_at: "2019-09-27 00:11:20",
#     updated_at: "2019-09-27 00:11:20",
#     company_user_id: 54985,
#     plaid_account_id: 2306,
#     company_id: 2700,
#     category_id: "********",
#     transaction_date: "2019-09-26",
#     transaction_id: "mOnQQEzNVETv5RA1mAvVF84koojQDnfMbmkVo",
#     transaction_type: "special",
#     vendor_id: nil,
#     is_active: true,
#     primary_category: "Transfer",
#     secondary_category: "Payroll",
#     tertiary_category: nil,
#     is_manual: false,
#     status: "archived",
#     recommended_match: "LeadPages",
#     percent_match: 0.***************,
#     import_guid: "8c55f340-2438-4646-89de-2706cdbb3f8e",
#     recurring: false,
#     recurrance_end_date: nil,
#     recurred: false,
#     company_integration_id: 324,
#     product_id: nil>

require 'rails_helper'

RSpec.describe CreateBatchTransactionWorker, type: :worker do
  let!(:company) { FactoryBot.create(:company, :with_locations) }
  let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
  let!(:company_user) { FactoryBot.create(:company_user, company: company,custom_form_id: custom_form.id) }
  let!(:admins) { company.groups.find_by(name: 'Admins') }
  let!(:admins_member) { admins.group_members << GroupMember.new(contributor: company_user.contributor) }
  let(:import_guid) { SecureRandom.uuid }
  let(:plaid_account) { company.plaid_accounts.create(account_id: "vokyE5Rn6vHKqDLRXEn5fne7LwbKPLIXGK98d") }

  subject { CreateBatchTransactionWorker.new }

  describe "perform" do
    context "with a transaction that is a payroll transaction under 2k" do
      let(:json_payload) {
        json_string =<<END
        [{
            "account_id": "vokyE5Rn6vHKqDLRXEn5fne7LwbKPLIXGK98d",
            "amount": 1307.21,
            "iso_currency_code": "USD",
            "unofficial_currency_code": null,
            "category": [
              "Transfer",
              "Payroll"
            ],
            "category_id": "********",
            "date": "2017-01-29",
            "location": {
              "address": "300 Post St",
              "city": "San Francisco",
              "region": "CA",
              "postal_code": "94108",
              "country": "US",
              "lat": null,
              "lon": null
            },
            "name": "ADP WAGE PAY WAGE PAY",
            "payment_meta": {},
            "pending": false,
            "pending_transaction_id": null,
            "account_owner": null,
            "transaction_id": "lPNjeW1nR6CDn5okmGQ6hEpMo4lLNoSrzqDje",
            "transaction_type": "place"
          }
        ]
END
      }

      it "does save the transaction" do
        CreateBatchTransactionWorker.new.perform(json_payload, import_guid, plaid_account.id, company_user.id)
        new_company = Company.find(company.id)
        expect(new_company.general_transactions).to_not be_blank
      end
    end

    context "with a transaction that is a payroll transaction over 3k" do
      let(:json_payload) {
        json_string =<<END
        [{
            "account_id": "vokyE5Rn6vHKqDLRXEn5fne7LwbKPLIXGK98d",
            "amount": 3004.31,
            "iso_currency_code": "USD",
            "unofficial_currency_code": null,
            "category": [
              "Transfer",
              "Payroll"
            ],
            "category_id": "********",
            "date": "2017-01-29",
            "location": {
              "address": "300 Post St",
              "city": "San Francisco",
              "region": "CA",
              "postal_code": "94108",
              "country": "US",
              "lat": null,
              "lon": null
            },
            "name": "ADP WAGE PAY WAGE PAY",
            "payment_meta": {},
            "pending": false,
            "pending_transaction_id": null,
            "account_owner": null,
            "transaction_id": "lPNjeW1nR6CDn5okmGQ6hEpMo4lLNoSrzqDje",
            "transaction_type": "place"
          }
        ]
END
      }

      it "does not save the transaction" do
        CreateBatchTransactionWorker.new.perform(json_payload, import_guid, plaid_account.id, company_user.id)
        new_company = Company.find(company.id)
        expect(new_company.general_transactions).to be_blank
      end
    end
  end
end