require 'rails_helper'

RSpec.describe Integrations::GsuiteAd::SyncDataWorker, type: :worker do
  describe '#execute' do
    before(:each) do
      Rails.application.load_seed
      @company = FactoryBot.create(:company)
      @gsuite_config = FactoryBot.create(:gsuite_ad_config, company_id: @company.id)
      @refresh_token = OpenStruct.new({token: "mock-refresh-token"})

      @company_integration = FactoryBot.create(
        :company_integration_true,
        company_id: @company.id,
        integration_id: Integration.find_by_name('gsuite_ad').id,
        integrable_type: @gsuite_config.class.name,
        integrable_id: @gsuite_config.id)

      @authorizer =OpenStruct.new({
        id: Rails.application.credentials.gsuite[:client_id],
        secret: Rails.application.credentials.gsuite[:client_secret],
        scope: ["https://www.googleapis.com/auth/admin.reports.usage.readonly", "https://www.googleapis.com/auth/admin.directory.user.readonly", "https://www.googleapis.com/auth/admin.directory.user.security", "https://www.googleapis.com/auth/admin.directory.resource.calendar.readonly"],
        token_store: "mock_token_store",
        store: "8943u3498h934h6n934n",
        filename: "token.yaml",
        abort: false,
        u0ltra_safe: false,
        thread_safe: false,
        callback_uri: "/oauth2callback",
        })

      user_1 = OpenStruct.new({
                primary_email: "<EMAIL>",
                name:  OpenStruct.new({fullname: "user2 last"}),
                creation_time: "Wed, 14 Mar 2018 03:41:50 +0000",
                customer_id: "C014vjuq6",
                id: "10078456892758223370445",
                is_admin: false,
                org_unit_path: "/Development",
                last_login_time: "Wed, 26 Jun 2019 09:18:15 +0000",
                archived: false,
                suspended: false
              })

      user_2 = OpenStruct.new({
                primary_email: "<EMAIL>",
                name: OpenStruct.new({fullname: "user last"}),
                creation_time: "Wed, 14 Mar 2018 03:41:50 +0000",
                customer_id: "C014vjuq6",
                id: "100784568927582370445",
                is_admin: false,
                org_unit_path: "/",
                last_login_time: "Wed, 26 Jun 2019 09:18:15 +0000",
                archived: false,
                suspended: false
              })

      @users = OpenStruct.new(users: [user_1, user_2])

      building_1 = OpenStruct.new(
        building_id: "Test-build 2",
        building_name: "Test X building",
        description: "ABC Building",
        address: OpenStruct.new(
          address_lines: ["Test street address", "Test street address 2"],
          locality: "NY",
          postal_code: "45000"
        ),
        floor_names: ["1"]
      )

      building_2 = OpenStruct.new(
        building_id: "Test-build",
        building_name: "Test Y building",
        description: "XYZ Building",
        address: OpenStruct.new(
          address_lines: ["Test street address", "Test street address 2"],
          locality: "NY",
          postal_code: "54000"
        ),
        floor_names: ["2"]
      )

      @buildings = OpenStruct.new(buildings: [building_1, building_2])

      @groups_ids = ["10da4das4das5"]

      @groups_members_ids = OpenStruct.new({
        members: [
          OpenStruct.new({
            id: "100784568927582370445",
            name: "user last",
            email: "<EMAIL>",
          })
        ]
      })

      @organization_units_paths = ["/Development"]

      parameters = OpenStruct.new(msg_value: [ {"client_id"=>"22378802832-klpcj5dosalhnu0vshg3hjm9qgidmp8j.apps.googleusercontent.com", "client_name"=>"Truecaller", "num_users"=>"1"}, {"client_id"=>"705229005811-2fdpup66d8aefq4qs2ru1n8qiosuq4fb.apps.googleusercontent.com", "client_name"=>"Honey", "num_users"=>"1"}, {"client_id"=>"946018238758-aht63nue21hvlj2d6maf07vvdjisckhh.apps.googleusercontent.com", "client_name"=>"macOS", "num_users"=>"1"}, {"client_id"=>"224054973946.apps.googleusercontent.com", "client_name"=>"Mailtrack", "num_users"=>"1"} ])
    end

    context 'Sync Gsuite user and apps' do
      it 'calls Integrations::GsuiteAd::FetchData Service' do
        obj = Integrations::GsuiteAd::SyncDataWorker.new
        allow(Integrations::GsuiteAd::SyncDataWorker).to receive(:new).and_return(obj)
        allow_any_instance_of(Google::Auth::UserAuthorizer).to receive(:get_credentials).and_return(@authorizer)
        allow_any_instance_of(Integrations::GsuiteAd::FetchData).to receive(:refresh_token).and_return(@refresh_token)
        allow_any_instance_of(Integrations::GsuiteAd::FetchData).to receive(:get_users).and_return(@users)
        allow_any_instance_of(Integrations::GsuiteAd::SaveUsersWorker).to receive(:fetch_buildings).and_return(@buildings)
        Sidekiq::Testing.inline! do
          Integrations::GsuiteAd::SyncDataWorker.new.perform(@gsuite_config.id, true, [], [], true)
        end
        expect(DiscoveredUser.where(company_id: @company.id).count).to eq(2)
      end

      it 'should create one Discovered User' do
        obj = Integrations::GsuiteAd::SyncDataWorker.new
        allow(Integrations::GsuiteAd::SyncDataWorker).to receive(:new).and_return(obj)
        allow_any_instance_of(Google::Auth::UserAuthorizer).to receive(:get_credentials).and_return(@authorizer)
        allow_any_instance_of(Integrations::GsuiteAd::FetchData).to receive(:refresh_token).and_return(@refresh_token)
        allow_any_instance_of(Integrations::GsuiteAd::FetchData).to receive(:get_users).and_return(@users)
        allow_any_instance_of(Integrations::GsuiteAd::SaveUsersWorker).to receive(:fetch_buildings).and_return(@buildings)
        Sidekiq::Testing.inline! do
          Integrations::GsuiteAd::SyncDataWorker.new.perform(@gsuite_config.id, true, [], @organization_units_paths, false)
        end
        expect(DiscoveredUser.where(company_id: @company.id).count).to eq(1)
      end

      it 'should not create any Discovered User' do
        obj = Integrations::GsuiteAd::SyncDataWorker.new
        allow(Integrations::GsuiteAd::SyncDataWorker).to receive(:new).and_return(obj)
        allow_any_instance_of(Google::Auth::UserAuthorizer).to receive(:get_credentials).and_return(@authorizer)
        allow_any_instance_of(Integrations::GsuiteAd::FetchData).to receive(:refresh_token).and_return(@refresh_token)
        allow_any_instance_of(Integrations::GsuiteAd::FetchData).to receive(:get_users).and_return(@users)
        allow_any_instance_of(Integrations::GsuiteAd::SaveUsersWorker).to receive(:fetch_buildings).and_return(@buildings)
        Sidekiq::Testing.inline! do
          Integrations::GsuiteAd::SyncDataWorker.new.perform(@gsuite_config.id, true, [], [], false)
        end
        expect(DiscoveredUser.where(company_id: @company.id).count).to eq(0)
      end

      it 'should create one Discovered User' do
        obj = Integrations::GsuiteAd::SyncDataWorker.new
        allow(Integrations::GsuiteAd::SyncDataWorker).to receive(:new).and_return(obj)
        allow_any_instance_of(Google::Auth::UserAuthorizer).to receive(:get_credentials).and_return(@authorizer)
        allow_any_instance_of(Integrations::GsuiteAd::FetchData).to receive(:refresh_token).and_return(@refresh_token)
        allow_any_instance_of(Integrations::GsuiteAd::FetchData).to receive(:get_users).and_return(@users)
        allow_any_instance_of(Integrations::GsuiteAd::FetchData).to receive(:get_group_members).and_return(@groups_members_ids)
        allow_any_instance_of(Integrations::GsuiteAd::SaveUsersWorker).to receive(:fetch_buildings).and_return(@buildings)
        Sidekiq::Testing.inline! do
          Integrations::GsuiteAd::SyncDataWorker.new.perform(@gsuite_config.id, true, @groups_ids, [], false)
        end
        expect(DiscoveredUser.where(company_id: @company.id).count).to eq(1)
      end

      it 'should create two Discovered User' do
        obj = Integrations::GsuiteAd::SyncDataWorker.new
        allow(Integrations::GsuiteAd::SyncDataWorker).to receive(:new).and_return(obj)
        allow_any_instance_of(Google::Auth::UserAuthorizer).to receive(:get_credentials).and_return(@authorizer)
        allow_any_instance_of(Integrations::GsuiteAd::FetchData).to receive(:refresh_token).and_return(@refresh_token)
        allow_any_instance_of(Integrations::GsuiteAd::FetchData).to receive(:get_users).and_return(@users)
        allow_any_instance_of(Integrations::GsuiteAd::FetchData).to receive(:get_group_members).and_return(@groups_members_ids)
        allow_any_instance_of(Integrations::GsuiteAd::SaveUsersWorker).to receive(:fetch_buildings).and_return(@buildings)
        Sidekiq::Testing.inline! do
          Integrations::GsuiteAd::SyncDataWorker.new.perform(@gsuite_config.id, true, @groups_ids, @organization_units_paths, false)
        end
        expect(DiscoveredUser.where(company_id: @company.id).count).to eq(2)
      end

      it 'should not create Discovered User when google user is disabled' do
        @users.users.first["suspended"] = true
        obj = Integrations::GsuiteAd::SyncDataWorker.new
        allow(Integrations::GsuiteAd::SyncDataWorker).to receive(:new).and_return(obj)
        allow_any_instance_of(Google::Auth::UserAuthorizer).to receive(:get_credentials).and_return(@authorizer)
        allow_any_instance_of(Integrations::GsuiteAd::FetchData).to receive(:refresh_token).and_return(@refresh_token)
        allow_any_instance_of(Integrations::GsuiteAd::FetchData).to receive(:get_users).and_return(@users)
        allow_any_instance_of(Integrations::GsuiteAd::FetchData).to receive(:get_group_members).and_return(@groups_members_ids)
        allow_any_instance_of(Integrations::GsuiteAd::SaveUsersWorker).to receive(:fetch_buildings).and_return(@buildings)
        Sidekiq::Testing.inline! do
          Integrations::GsuiteAd::SyncDataWorker.new.perform(@gsuite_config.id, true, @groups_ids, @organization_units_paths, false)
        end
        expect(DiscoveredUser.where(company_id: @company.id).count).to eq(1)
      end
    end

    context 'remove users access' do
      let!(:custom_form) { FactoryBot.create(:custom_form, company: @company, company_module: "company_user", description: "Test Description") }
      let(:discovered_user) { create(:discovered_user, company_id: @company.id, email: "<EMAIL>") }
      let!(:new_user) { create(:user, email: '<EMAIL>') }
      let!(:company_user) { create(:company_user, user: new_user, company: @company, granted_access_at: 1.day.ago, custom_form_id: custom_form.id) }

      it 'remove users granted access which are disabled' do
        @users.users.first["primary_email"] = "<EMAIL>"
        @users.users.first["suspended"] = true
        discovered_user

        obj = Integrations::GsuiteAd::SyncDataWorker.new
        allow(Integrations::GsuiteAd::SyncDataWorker).to receive(:new).and_return(obj)
        allow_any_instance_of(Google::Auth::UserAuthorizer).to receive(:get_credentials).and_return(@authorizer)
        allow_any_instance_of(Integrations::GsuiteAd::FetchData).to receive(:refresh_token).and_return(@refresh_token)
        allow_any_instance_of(Integrations::GsuiteAd::FetchData).to receive(:get_users).and_return(@users)
        allow_any_instance_of(Integrations::GsuiteAd::SaveUsersWorker).to receive(:fetch_buildings).and_return(@buildings)
        allow_any_instance_of(Integrations::GsuiteAd::FetchData).to receive(:get_group_members).and_return(@groups_members_ids)
        Sidekiq::Testing.inline! do
          Integrations::GsuiteAd::SyncDataWorker.new.perform(@gsuite_config.id, true, @groups_ids, @organization_units_paths, false)
        end
        staff_user = User.find_by_email(@users.users.first["primary_email"])
        company_user = CompanyUser.find_by(company_id: @company.id, user_id: staff_user.id)

        expect(company_user.granted_access_at).to eq(nil)
      end
    end
  end
end
