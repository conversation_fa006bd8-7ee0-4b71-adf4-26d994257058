require 'rails_helper'

RSpec.describe Integrations::SageIntacct::SyncDataWorker, type: :worker do
  describe '#execute' do
    before(:each) do
      Rails.application.load_seed
      @company = FactoryBot.create(:company)
      @sage_intacct_config = FactoryBot.create(:sage_intacct_config, company_id: @company.id)
    end

    context 'SyncSageIntacct Transactions' do
      it 'calls Integrations::SageIntacct::FetchData Service' do
        
        bills = {
          "vendors" => [
            {"NAME" => "Ali", "EntityId" => "accountant"},
            {"NAME" => "Ahmad", "EntityId" => "unkown"}
          ],
    
          "vendorBills" => [
            {"VENDORID" => "123458", "WHENCREATED" => "10/10/2020", "TOTALENTERED" => "500", "VENDORNAME" => "Test VEndor"},
            {"VENDORID" => "987642", "WHENCREATED" => "10/20/2020", "TOTALENTERED" => "600", "VENDORNAME" => "Task"},
            {"VENDORID" => "985832", "WHENCREATED" => "10/30/2020", "TOTALENTERED" => "900", "VENDORNAME" => "Task"}
          ]
        }
        
        response = 'http://s3.amazonaws.com/hotglue-logs/test/subdomain-intacct-**********.json'
        allow_any_instance_of(Integrations::SageIntacct::SyncDataWorker).to receive(:uploaded_file_path).and_return(response)
        obj = Integrations::SageIntacct::SyncDataWorker.new
        allow(Integrations::SageIntacct::SyncDataWorker).to receive(:new).and_return(obj)
        allow_any_instance_of(Integrations::SageIntacct::FetchData).to receive(:get_data_from_s3).and_return(bills)
        Integrations::SageIntacct::SyncDataWorker.new.perform(@sage_intacct_config.id, true)
        expect(GeneralTransaction.all.count).to eq(3)
        expect(GeneralTransaction.where(transaction_type: "expenses").count).to eq(3)
      end
    end
  end
end
