require 'rails_helper'

RSpec.describe Integrations::Netsuite::SyncDataWorker, type: :worker do
  describe '#execute' do
    before(:each) do
      Rails.application.load_seed
      @company = FactoryBot.create(:company)
      @netsuite_config = FactoryBot.create(:netsuite_config, company_id: @company.id)
      @company_integration = FactoryBot.create(:company_integration_true,
                                               company_id: @company.id,
                                               integration_id: Integration.find_by_name('netsuite').id,
                                               integrable_type: @netsuite_config.class.name,
                                               integrable_id: @netsuite_config.id)
    end

    context 'SyncNetSuite Transactions' do
      it 'calls Integrations::NetSuite::FetchData Service' do
        obj = Integrations::Netsuite::SyncDataWorker.new
        bills = {
          "vendors"=> [
            {"name" => "John", "EntityId"=> "accountant"},
            {"name"=> "Billi","EntityId"=> "unkown"}
          ],
            
          "vendorBills" => [
            {"Id" => "123458", "TranDate" => "2020-01-08", "Total" => "500", "EntityRef" => "{\"name\": \"Test VEndor\"}"},
            {"Id" => "987642", "TranDate" => "2020-01-01", "Total" => "600", "EntityRef" => "{\"name\": \"Task\"}"},
            {"Id" => "985832", "TranDate" => "2020-01-02", "Total" => "900", "EntityRef" => "{\"name\": \"Task\"}"}
          ]
         }
        
        allow(Integrations::Netsuite::SyncDataWorker).to receive(:new).and_return(obj)
        allow_any_instance_of(Integrations::Netsuite::FetchData).to receive(:get_data_from_webhook).and_return(bills)
        Integrations::Netsuite::SyncDataWorker.new.perform(@netsuite_config.id, false)

         expect(GeneralTransaction.all.count).to eq(3)
        expect(GeneralTransaction.where(transaction_type: "expenses").count).to eq(3)
      end
    end
  end
end
