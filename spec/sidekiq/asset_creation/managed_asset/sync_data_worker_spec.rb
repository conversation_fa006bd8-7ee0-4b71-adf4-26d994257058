require 'rails_helper'
include CompanyUserHelper

RSpec.describe AssetCreation::ManagedAsset::SyncDataWorker, type: :worker do
  create_company_and_user
  let!(:window_app_release) do
    AppRelease.create(
      app_name: "agent",
      app_enabled: true,
      version: "*******",
      is_admin_app: true,
      app_type: "Windows"
    )
  end
  let!(:mac_app_release) do
    AppRelease.create(
      app_name: "agent",
      app_enabled: true,
      version: "*******",
      is_admin_app: true,
      app_type: "MAC"
    )
  end
  let(:json_for_windows) do
    [
      {
        "app_version" => "*******",
        "managedAsset" => {
          "companyUserGuid" => company_user.guid,
          "companyGuid" => company.guid,
          "source" => "agent",
          "macAddresses" => ["d8:f8:83:d4:6d:e0"],
          "secMacAddresses" => ["E4:B9:7A:53:BB:D1", "1A:1D:EA:57:B8:18", "18:1D:EA:57:B8:19"],
          "macAddressesInfo" => [
            { "macAddress" => "E4:B9:7A:53:BB:D1", "machType" => "Intel(R) Ethernet Connection (4) I219-LM", "networkName" => "Ethernet", "networkType" => "Ethernet", "hasGateway" => false },
            { "macAddress" => "1A:1D:EA:57:B8:18", "machType" => "Microsoft Wi-Fi Direct Virtual Adapter #3", "networkName" => "Local Area Connection* 12", "networkType" => "Wireless80211", "hasGateway" => false },
            { "macAddress" => "18:1D:EA:57:B8:19", "machType" => "Microsoft Wi-Fi Direct Virtual Adapter #5", "networkName" => "Local Area Connection* 6", "networkType" => "Wireless80211", "hasGateway" => false }
          ],
          "applications" => [
            { "displayName" => "Microsoft Edge", "version" => "128.0.2739.54", "installDate" => "2024-09-04", "uninstallString" => "\"C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\128.0.2739.54\\Installer\\setup.exe\" --uninstall --msedge --channel=stable --system-level --verbose-logging", "installLocation" => "C:\\Program Files\\Microsoft\\Edge\\Application" },
            { "displayName" => "Microsoft Edge Update", "version" => "**********" },
            { "displayName" => "Microsoft Edge WebView2 Runtime", "version" => "128.0.2739.63", "installDate" => "2024-09-04", "uninstallString" => "\"C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\128.0.2739.63\\Installer\\setup.exe\" --uninstall --msedgewebview --system-level --verbose-logging", "installLocation" => "C:\\Program Files\\Microsoft\\EdgeWebView\\Application" },
            { "displayName" => "TeamViewer Host", "version" => "15.57.3", "uninstallString" => "\"C:\\Program Files (x86)\\TeamViewer\\uninstall.exe\"", "installLocation" => "C:\\Program Files\\TeamViewer" }
          ],
          "userAccountsInfo" => [],
          "diskInfo" => [
            { "model" => "Micron 2200S NVMe 512GB", "name" => "\\\\.\\PHYSICALDRIVE0", "partitions" => "2", "type" => "SCSI", "index" => "0", "pnpId" => "SCSI\\DISK&VEN_NVME&PROD_MICRON_2200S_NVM\\5&25A182E9&0&000000", "serialNumber" => "0000_0000_0000_0001_00A0_7520_2809_B5C5.", "id" => "\\\\.\\PHYSICALDRIVE0", "description" => "Disk drive", "manufacturer" => "(Standard disk drives)", "space" => "477 GB", "systemId" => "96DA24A6-9F74-49D3-8D57-97570C090A6B", "collectionTime" => "10/09/2024 10:34 AM" }
          ],
          "logicalDiskInfo" => [
            { "name" => "C:", "space" => "238 GB", "freeSpace" => "123 GB (51.8)%" }
          ],
          "driveEncryptionInfo" => [
            { "drive" => "C:", "encryptionStatus" => "Decrypted", "encryptionMethod" => "None" }
          ],
          "patchInfo" => [
            { "id" => "KB5030650", "description" => "Update", "installedOn" => "10/18/2023" },
            { "id" => "KB5028856", "description" => "Update", "installedOn" => "7/12/2023" }
          ],
          "scheduledTasksInfo" => [
            { "name" => "AteraAgentServiceWatchdog", "lastExecuted" => "10/09/2024 10:24 AM", "status" => "Ready" },
            { "name" => "MicrosoftEdgeUpdateTaskMachineCore", "lastExecuted" => "10/09/2024 10:24 AM", "status" => "Ready" }
          ],
          "systemServicesInfo" => [
            { "name" => "Acronis Remote Agent Service", "status" => "Running", "startType" => "Automatic" },
            { "name" => "Acronis File Server Service", "status" => "Running", "startType" => "Automatic" },
            { "name" => "AllJoyn Router Service", "status" => "Stopped", "startType" => "Manual" },
            { "name" => "Application Layer Gateway Service", "status" => "Stopped", "startType" => "Manual" }
          ],
          "startupItemsInfo" => [
            { "name" => "TeamsMachineInstaller", "status" => "Enabled", "type" => "Startup Item" },
            { "name" => "SecurityHealth", "status" => "Enabled", "type" => "Startup Item" }
          ],
          "sharedResourcesInfo" => [
            { "name" => "ADMIN$", "description" => "Remote Admin", "status" => "OK" },
            { "name" => "C$", "description" => "Default share", "status" => "OK" }
          ],
          "certificatesInfo" => [
            { "name" => "DigiCert Global Root G2", "expirationDate" => "01/15/2038 07:00 AM" },
            { "name" => "DigiCert Trusted Root G4", "expirationDate" => "01/15/2038 07:00 AM" }
          ],
          "systemTpmInfo" => [
            { "version" => "2.0, 0, 1.16", "activationStatus" => "Activated", "ownershipStatus" => "Owned", "enabledStatus" => "Enabled" }
          ],
          "chromeExtensionsInfo" => [],
          "networkInterfacesInfo" => [
            { "name" => "Intel(R) Dual Band Wireless-AC 8265", "type" => "Ethernet 802.3", "index" => "9", "macAddress" => "18:1D:EA:57:B8:18", "speed" => "87.8 Mbps", "connectionStatus" => "Connected (2)", "ipv4Address" => "*************", "ipv6Address" => "fe80::789a:5b32:4b93:b5a6" }
          ],
          "etcHostsInfo" => [],
          "systemInfo" => {
            "hostName" => "RR-2389.WORKGROUP",
            "computerName" => "RR-2389",
            "domainName" => "WORKGROUP",
            "hardwareModel" => "Pro Book",
            "hardwareVersion" => "1.33.0",
            "hardwareSerial" => "GWX7JR2",
            "hardwareVendor" => "HP Inc.",
            "cpuLogicalCores" => "8 Cores",
            "cpuPhysicalCores" => "4 Cores",
            "cpuType" => "x64",
            "collectionTime" => "10/09/2024 10:34 AM"
          },
          "kernelInfo" => {
            "version" => "10.0.22000",
            "device" => "\\Device\\HarddiskVolume3",
            "systemPath" => "C:\\WINDOWS\\system32",
            "arguments" => "NOEXECUTE=OPTIN FVEBOOT=2674688",
            "systemId" => "96DA24A6-9F74-49D3-8D57-97570C090A6B",
            "collectionTime" => "10/09/2024 10:34 AM"
          },
          "domainName" => "WORKGROUP",
          "processorName" => "Intel(R) Core(TM) i5-8350U CPU @ 1.70GHz",
          "processorCores" => "4 Cores",
          "processorLogicalCores" => "8 Cores",
          "processorArchitecture" => "x64",
          "memory" => "16 GB",
          "os" => "Windows",
          "osName" => "Microsoft Windows 11 Pro",
          "osVersion" => "10.0.22000",
          "osSerialNo" => "00330-51572-14199-AAOEM",
          "osBuildNo" => "22000",
          "systemUpTime" => "00 Hours",
          "ipAddress" => "*************",
          "remoteIpAddress" => "*************",
          "windowsInstallationDate" => "2022/09/18-21:09:08",
          "diskEncryption" => "Off",
          "diskSpace" => "477 GB",
          "diskFreeSpace" => "124 GB",
          "screenSize" => "13.9",
          "manufacturer" => "Dell Inc.",
          "model" => "Latitude 5490",
          "displayName" => "RR-2389",
          "pcSerialNo" => "GWX7JR2",
          "assetType" => "Laptop",
          "systemUuid" => "4C4C4544-0057-5810-8037-C7C04F4A5232",
          "deviceId" => "96DA24A6-9F74-49D3-8D57-97570C090A6B",
          "appId" => "",
          "hardwareVersion" => "1.33.0",
          "secureBootStatus" => { "enabled" => "Disabled", "collectionTime" => "10/09/2024 10:34 AM" },
          "hostname" => "RR-2389.WORKGROUP"
        }
      }
    ]
  end

  let(:json_for_mac) do
    [
      {
        "app_version" => "*******",
        "managedAsset" => {
          "networkInterfacesInfo" => [
            { "ipv4Address" => ["127.0.0.1"], "macAddress" => [], "ipv6Address" => ["::1", "fe80::1%lo0"], "connectionStatus" => "", "name" => "lo0" },
            { "ipv4Address" => [], "macAddress" => ["82:15:94:e2:c4:00"], "ipv6Address" => [], "connectionStatus" => "inactive", "name" => "en2" },
            { "ipv4Address" => [], "macAddress" => [], "ipv6Address" => ["fe80::d852:6631:4c08:3c88%utun0"], "connectionStatus" => "", "name" => "utun0" },
            { "ipv4Address" => [], "macAddress" => ["82:15:94:e2:c4:01"], "ipv6Address" => [], "connectionStatus" => "inactive", "name" => "bridge0" },
            { "ipv4Address" => [], "macAddress" => [], "ipv6Address" => [], "connectionStatus" => "", "name" => "gif0" },
            { "ipv4Address" => [], "macAddress" => [], "ipv6Address" => ["fe80::c2b5:9288:f4b0:6727%utun1"], "connectionStatus" => "", "name" => "utun1" },
            { "ipv4Address" => [], "macAddress" => ["ea:9c:76:cc:c8:00"], "ipv6Address" => ["fe80::e89c:76ff:fecc:c800%awdl0"], "connectionStatus" => "active", "name" => "awdl0" },
            { "ipv4Address" => [], "macAddress" => ["ea:9c:76:cc:c8:00"], "ipv6Address" => ["fe80::e89c:76ff:fecc:c800%llw0"], "connectionStatus" => "active", "name" => "llw0" },
            { "ipv4Address" => [], "macAddress" => [], "ipv6Address" => ["fe80::ce81:b1c:bd2c:69e%utun2"], "connectionStatus" => "", "name" => "utun2" },
            { "ipv4Address" => ["*************"], "macAddress" => ["8c:85:90:6d:c4:dc"], "ipv6Address" => ["fe80::6e:bd62:7a1b:e123%en0"], "connectionStatus" => "active", "name" => "en0" },
            { "ipv4Address" => [], "macAddress" => [], "ipv6Address" => [], "connectionStatus" => "", "name" => "stf0" },
            { "ipv4Address" => [], "macAddress" => ["82:15:94:e2:c4:01"], "ipv6Address" => [], "connectionStatus" => "inactive", "name" => "en1" }
          ],
          "osName" => "MAC OS",
          "assetType" => "Laptop",
          "displayName" => "devos MacBook Pro",
          "osVersion" => "Version 13.1 (Build 22C65)",
          "pcSerialNo" => "FVFVT0GCHV22",
          "windowsInstallationDate" => "2023-01-06 10:24:49 +0000",
          "processorName" => "Intel(R) Core(TM) i5-7360U CPU @ 2.30GHz",
          "processorLogicalCores" => 4,
          "company_id" => company.id,
          "companyGuid" => company.guid,
          "applications" => [
            { "displayName" => "Postgres.app", "version" => "2.5.12", "installDate" => "2022-11-10 10:22:44 +0000", "lastOpened" => "2022-11-10 15:22:44", "installSize" => "96" },
            { "displayName" => "Visual Studio Code.app", "version" => "1.81.1", "installDate" => "2023-08-09 21:55:36 +0000", "lastOpened" => "2023-08-10 02:55:36", "installSize" => "96" },
            { "displayName" => "Google Chrome.app", "version" => "118.0.5993.117", "installDate" => "2022-09-09 23:35:48 +0000", "lastOpened" => "2023-10-24 04:50:32", "installSize" => "96" },
            { "displayName" => "Xcode.app", "version" => "14.0", "installDate" => "2022-08-13 04:42:44 +0000", "lastOpened" => "2022-09-19 13:55:19", "installSize" => "96" },
            { "displayName" => "Spotify.app", "version" => "1.2.21.1104.g42cf0a50", "installDate" => "2023-09-22 11:09:38 +0000", "lastOpened" => "2023-09-22 16:09:38", "installSize" => "96" },
            { "displayName" => "Notion Web Clipper.app", "version" => "1.0.3", "installDate" => "2021-04-22 00:24:28 +0000", "lastOpened" => "2023-09-28 20:11:46", "installSize" => "96" }
          ],
          "printerDetails" => [],
          "screenSize" => "Unknown size",
          "manufacturer" => "Apple inc.",
          "hardwareVersion" => "499.********",
          "crashReports" => [
            { "name" => "CloudServicesTopic_2023-10-24-011851_devos-MacBook-Pro.diag" },
            { "name" => "CloudServicesTopic_2023-10-20-191710_devos-MacBook-Pro.diag" },
            { "name" => "KeySyncTopic_2023-10-24-011858_devos-MacBook-Pro.diag" },
            { "name" => "NetworkingTopic_2023-10-24-011849_devos-MacBook-Pro.diag" }
          ],
          "processorCores" => 2,
          "systemUpTime" => 162,
          "diskEncryption" => "Off",
          "diskSpace" => "120.88 GB",
          "diskFreeSpace" => "7.06 GB",
          "chromeExtensionsInfo" => [
            { "profileName" => "Ali", "version" => "0.2", "name" => "Web Store" },
            { "profileName" => "Ali", "version" => "1.3.21", "name" => "Google Hangouts" },
            { "profileName" => "Ali", "version" => "1", "name" => "Chrome PDF Viewer" },
            { "profileName" => "Ali", "version" => "1.0", "name" => "Google Network Speech" },
            { "profileName" => "Jahanzaib", "version" => "1", "name" => "Chrome PDF Viewer" }
          ],
          "os" => "MAC OS",
          "cpuBrandName" => { "name" => "Intel(R) Core(TM) i5-7360U CPU @ 2.30GHz" },
          "source" => "agent",
          "macAddressesInfo" => [
            { "network_type" => "Bridge", "network_name" => "Thunderbolt Bridge", "mac_address" => "82:15:94:E2:C4:01", "mac_type" => "bridge0" },
            { "network_type" => "IEEE80211", "mac_address" => "8C:85:90:6D:C4:DC", "mac_type" => "en0", "network_name" => "Wi-Fi" },
            { "network_type" => "Ethernet", "network_name" => "Thunderbolt 2", "mac_type" => "en2", "mac_address" => "82:15:94:E2:C4:00" }
          ],
          "launchdInfo" => [
            { "label" => "com.apple.SafariHistoryServiceAgent", "status" => "Enabled" },
            { "label" => "com.apple.progressd", "status" => "Enabled" },
            { "label" => "com.google.keystone.user.xpcservice", "status" => "Enabled" }
          ],
          "usbDevices" => [
            { "productId" => "32775", "name" => "AppleUSBXHCI Root Hub Simulation", "vendorId" => "1452" }
          ],
          "batteryInfo" => [
            {
              "BatteryHealthCondition" => "",
              "BatteryProvidesTimeRemaining" => "true",
              "LpmActive" => "false",
              "MaxCapacity" => "100 %",
              "Current" => "2045",
              "PowerSourceId" => "7209059",
              "Source" => "powerSource1",
              "CurrentCapacity" => "79 %",
              "TransportType" => "Internal",
              "TimeToFullCharge" => "68 minutes",
              "Type" => "InternalBattery",
              "IsFinishingCharge" => "false",
              "HardwareSerialNumber" => "D868036C13QH06FA6",
              "OptimizedBatteryChargingEngaged" => "false",
              "IsPresent" => "true",
              "TimeToEmpty" => "0 minutes",
              "BatteryHealth" => "Good",
              "DesignCycleCount" => "1000",
              "Name" => "InternalBattery-0",
              "IsCharging" => "true",
              "PowerSourceState" => "AC Power"
            }
          ],
          "etcHostsInfo" => [
            { "host_name" => "localhost", "address" => "127.0.0.1" },
            { "host_name" => "broadcasthost", "address" => "***************" },
            { "host_name" => "localhost", "address" => "::1" }
          ],
          "domainName" => "local",
          "ipAddress" => "*************",
          "remoteIpAddress" => "**************",
          "secMacAddresses" => ["82:15:94:e2:c4:01", "8c:85:90:6d:c4:dc", "82:15:94:e2:c4:00"],
          "memory" => "8.0",
          "processorArchitecture" => "x86_64",
          "hostname" => "devos-macbook-pro.local",
          "userAccountsInfo" => [{ "lastLogin" => "", "fullName" => "devos", "name" => "dev" }],
          "model" => "MacBook Pro (13-inch, 2017, Two Thunderbolt 3 ports)",
          "kernelInfo" => { "name" => "Darwin", "release" => "22.2.0: Fri Nov 11 02:08:47 PST 2022;", "version" => "22.2.0", "type" => "i386" },
          "macAddresses" => ["d8:f8:83:d4:6d:e0"],
          "deviceId" => "7FAE77CF-0317-5CEA-A46D-BEA056522835",
          "systemUuid" => "209D639B-5BCB-4A0A-8A56-B806AD4D81D3",
          "applicationLayerFirewallStatus" => { "status" => "disabled" },
          "mountDetails" => [
            { "mountType" => "APFS", "volumeName" => "/" },
            { "mountType" => "Mac OS Extended", "volumeName" => "Firefox" }
          ],
          "sharedFolders" => [
            { "name" => "SC Info" },
            { "name" => ".localized" }
          ],
          "startupItemsInfo" => [
            { "name" => "com.google.keystone.user.agent", "status" => "enabled" },
            { "name" => "homebrew.mxcl.postgresql@13", "status" => "enabled" }
          ],
          "userSshKeysState" => [
            { "path" => "/Users/<USER>/.ssh/private-key.pem", "name" => "private-key", "encrypted" => "false", "system_id" => "16777222" }
          ],
          "cumulativeStorageInfo" => {
            "/path1" => "18 MB",
            "/path2" => "4 Bytes"
          }
        }
      }
    ]
  end
  describe '#perform for Windows' do
    it 'calls the CreateManagedAssetService to create managed assets' do
      AssetCreation::ManagedAsset::SyncDataWorker.new.perform(json_for_windows)

      expect(ManagedAsset.count).to eq(1)
      asset = ManagedAsset.find_by(company_id: company.id)

      expect(asset.name).to eq("RR-2389")
      expect(asset.mac_addresses).to eq(["D8:F8:83:D4:6D:E0"])
      expect(asset.machine_serial_number).to eq("GWX7JR2")
      expect(asset.os_version).to eq("10.0.22000")
    end
  end
  describe '#perform for MAC OS' do
    it 'calls the CreateManagedAssetService to create managed assets' do
      AssetCreation::ManagedAsset::SyncDataWorker.new.perform(json_for_mac)

      expect(ManagedAsset.count).to eq(1)
      asset = ManagedAsset.find_by(company_id: company.id)

      expect(asset.name).to eq("devos MacBook Pro")
      expect(asset.mac_addresses).to eq(["D8:F8:83:D4:6D:E0"])
      expect(asset.machine_serial_number).to eq("FVFVT0GCHV22")
      expect(asset.os_version).to eq("Version 13.1 (Build 22C65)")
    end
  end
end
