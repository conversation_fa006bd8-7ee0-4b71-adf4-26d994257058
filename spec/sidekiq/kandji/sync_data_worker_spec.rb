require 'rails_helper'

RSpec.describe Integrations::Kandji::SyncDataWorker, type: :worker do
  describe '#execute' do
    let(:company) { FactoryBot.create(:company) }
    let(:kandji_config) { FactoryBot.create(:kandji_config, company: company) }
    let(:list_devices) {
      [
        [
          {
            "device_id": "f25a87de-61e8-4ab2-9032-02f6678914a1",
            "device_name": "Test MacBook Air",
            "model": "MacBook Air (M1, 2020)",
            "serial_number": "AAAANNAAAA",
            "platform": "Mac",
            "os_version": "10.15.6",
            "mac_address": "00:0c:29:05:43:b6",
            "last_check_in": "2021-03-30T17:46:31.864498Z",
            "user": {
              "email": "<EMAIL>",
              "name": "Test User",
              "id": 23,
              "is_archived": false
            },
            "asset_tag": "",
            "blueprint_id": "2118bda6-8772-4c5b-ae0a-bb506b2b9516",
            "mdm_enabled": true,
            "agent_installed": true,
            "is_missing": false,
            "is_removed": false,
            "agent_version": "2.7.3 (1253)",
            "first_enrollment": "2021-03-29 21:46:13.552931+00:00",
            "last_enrollment": "2021-03-29 21:46:13.552931+00:00",
            "blueprint_name": "All Staff"
          }
        ]
      ]
    }

    let(:device_details) {
      {
        "general": {
          "device_id": "f25a87de-61e8-4ab2-9032-02f6678914a1",
          "device_name": "Test MacBook Airr",
          "last_enrollment": "2021-05-15 03:46:05.255702+00:00",
          "first_enrollment": "2021-05-15 03:46:05.255702+00:00",
          "model": "MacBook Air (Retina, 13-inch, 2020)",
          "platform": "Mac",
          "os_version": "11.0.1",
          "system_version": "11.0.1 (20B29)",
          "boot_volume": "Macintosh HD",
          "time_since_boot": "1:13",
          "last_user": "nick",
          "asset_tag": "",
          "assigned_user": { "email": "<EMAIL>" },
          "blueprint_name": "All Staff",
          "blueprint_uuid": "29f82bfb-7710-4c07-9830-0043747a4560"
        },
        "mdm": {
          "mdm_enabled": "True",
          "install_date": "2021-05-15 03:46:05.000616+00:00",
          "last_check_in": "2021-05-15 03:46:17.668358+00:00",
          "mdm_enabled_user": [
            "nick"
          ]
        },
        "activation_lock": {
          "bypass_code_failed": false,
          "user_activation_lock_enabled": false,
          "device_activation_lock_enabled": false,
          "activation_lock_allowed_while_supervised": false,
          "activation_lock_supported": true
        },
        "filevault": {
          "filevault_enabled": false,
          "filevault_recoverykey_type": "",
          "filevault_prk_escrowed": false,
          "filevault_next_rotation": "",
          "filevault_regen_required": false
        },
        "kandji_agent": {
          "agent_installed": "True",
          "install_date": "2021-05-15 03:46:18.836271+00:00",
          "last_check_in": "2021-05-15T03:47:04.405782Z",
          "agent_version": "2.9.0 (1492)"
        },
        "hardware_overview": {
          "model_name": "MacBook Air",
          "model_identifier": "MacBookAir9,1",
          "processor_name": "Dual-Core Intel Core i3",
          "processor_speed": "1.1 GHz",
          "number_of_processors": "1",
          "total_number_of_cores": "2",
          "memory": "8 GB 3733 MHz LPDDR4X"
        },
        "volumes": [
          {
            "name": "Macintosh HD",
            "format": "APFS",
            "percent_used": "7%",
            "identifier": "disk1s5s1",
            "capacity": "250.69 GB",
            "available": "231.33 GB",
            "encrypted": "No"
          },
          {
            "name": "Macintosh HD - Data",
            "format": "APFS",
            "percent_used": "7%",
            "identifier": "disk1s1",
            "capacity": "250.69 GB",
            "available": "231.33 GB",
            "encrypted": "No"
          }
        ],
        "network": {
          "local_hostname": "nicks-MacBook-Air.local",
          "mac_address": "3c:22:fb:9a:1d:5c",
          "ip_address": "***************",
          "public_ip": ""
        },
        "users": {
          "regular_users": [
            {
              "username": "nick",
              "uid": "501",
              "path": "/Users/<USER>",
              "admin": "Yes",
              "name": ""
            }
          ],
          "system_users": [
            {
              "username": "_lp",
              "uid": "26",
              "path": "/var/spool/cups",
              "admin": "No"
            },
            {
              "username": "_ard",
              "uid": "67",
              "path": "/var/empty",
              "admin": "No"
            }
          ],
        },
        "installed_profiles": [
          {
            "name": "MDM Profile",
            "uuid": "5b7c1b37-b501-4a29-ba32-207f7a53f023",
            "verified": "verified",
            "identifier": "com.kandji.profile.mdmprofile",
            "organization": "Kandji, Inc.",
            "payload_types": [
              "com.apple.mdm",
              "com.apple.security.scep"
            ],
            "install_date": "2021-05-15 03:46:01 +0000"
          },
          {
            "name": "Kandji Agent Settings",
            "uuid": "f394d288-612f-404e-a7b5-50639a3b4712",
            "verified": "verified",
            "identifier": "com.kandji.profile.pppc",
            "organization": "Kandji, Inc.",
            "payload_types": [
              "com.apple.TCC.configuration-profile-policy",
              "com.apple.notificationsettings"
            ],
            "install_date": "2021-05-15 03:46:09 +0000"
          }
        ]
      }
    }

    let(:device_apps) {
      {
        "device_id": "f25a87de-61e8-4ab2-9032-02f6678914a1",
        "apps": [
          {
            "app_id": "11012",
            "app_name": "Activity Monitor",
            "bundle_id": "com.apple.ActivityMonitor",
            "bundle_size": "4802065",
            "app_store_vendable": "",
            "device_based_vpp": "",
            "source": "Apple",
            "process": "Activity Monitor",
            "signature": "",
            "creation_date": "2021-03-25T18:33:21.720616Z",
            "modification_date": "2021-03-25T18:33:21.720644Z",
            "path": "/System/Applications/Utilities/Activity Monitor.app",
            "version": "10.14"
          },
          {
            "app_id": "11013",
            "app_name": "AirPort Utility",
            "bundle_id": "com.apple.airport.airportutility",
            "bundle_size": "47505998",
            "app_store_vendable": "",
            "device_based_vpp": "",
            "source": "Apple",
            "process": "AirPort Utility",
            "signature": "",
            "creation_date": "2021-03-25T18:33:21.720753Z",
            "modification_date": "2021-03-25T18:33:21.720761Z",
            "path": "/System/Applications/Utilities/AirPort Utility.app",
            "version": "6.3.9"
          },
          {
            "app_id": "11014",
            "app_name": "App Store",
            "bundle_id": "com.apple.AppStore",
            "bundle_size": "18300985",
            "app_store_vendable": "",
            "device_based_vpp": "",
            "source": "Apple",
            "process": "App Store",
            "signature": "",
            "creation_date": "2021-03-25T18:33:21.720815Z",
            "modification_date": "2021-03-25T18:33:21.720822Z",
            "path": "/System/Applications/App Store.app",
            "version": "3.0"
          },
          {
            "app_id": "11015",
            "app_name": "Audio MIDI Setup",
            "bundle_id": "com.apple.audio.AudioMIDISetup",
            "bundle_size": "5159142",
            "app_store_vendable": "",
            "device_based_vpp": "",
            "source": "Apple",
            "process": "Audio MIDI Setup",
            "signature": "",
            "creation_date": "2021-03-25T18:33:21.720883Z",
            "modification_date": "2021-03-25T18:33:21.720891Z",
            "path": "/System/Applications/Utilities/Audio MIDI Setup.app",
            "version": "3.5"
          },
          {
            "app_id": "11016",
            "app_name": "Automator",
            "bundle_id": "com.apple.Automator",
            "bundle_size": "6915571",
            "app_store_vendable": "",
            "device_based_vpp": "",
            "source": "Apple",
            "process": "Automator",
            "signature": "",
            "creation_date": "2021-03-25T18:33:21.720941Z",
            "modification_date": "2021-03-25T18:33:21.720948Z",
            "path": "/System/Applications/Automator.app",
            "version": "2.10"
          },
          {
            "app_id": "11017",
            "app_name": "BBEdit",
            "bundle_id": "com.barebones.bbedit",
            "bundle_size": "55003874",
            "app_store_vendable": "",
            "device_based_vpp": "",
            "source": "Identified Developer",
            "process": "BBEdit",
            "signature": "",
            "creation_date": "2021-03-25T18:33:21.720996Z",
            "modification_date": "2021-03-25T18:33:21.721002Z",
            "path": "/Applications/BBEdit.app",
            "version": "13.5.5"
          }
        ]
      }
    }

    before(:each) do
      allow_any_instance_of(Integrations::Kandji::FetchData).to receive(:authenticate?).and_return(true)
    end

    context 'Fetch Kandji devices' do
        it 'Calls Integrations::Kandji::FetchData' do
          allow_any_instance_of(Integrations::Kandji::FetchData).to receive(:list_devices).and_return(list_devices)
          allow_any_instance_of(Integrations::Kandji::FetchData).to receive(:get_device_details).and_return(device_details)
          allow_any_instance_of(Integrations::Kandji::FetchData).to receive(:get_device_apps).and_return(device_apps)
          Sidekiq::Testing.inline! do
            Integrations::Kandji::SyncDataWorker.new.perform(kandji_config, true)
          end
          disc_asset = DiscoveredAsset.ready_for_import.find_by(company_id: company.id, source: 'kandji')
          expect(disc_asset).to be_present
          expect(disc_asset.asset_sources.where(source: 'kandji').count).to eq(1)
        end
    end
  end
end
