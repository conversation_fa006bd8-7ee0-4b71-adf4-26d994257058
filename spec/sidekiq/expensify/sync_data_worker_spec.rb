require 'rails_helper'

RSpec.describe Integrations::Expensify::SyncDataWorker, type: :worker do
  describe '#execute' do
    before(:each) do
      Rails.application.load_seed
      @company = FactoryBot.create(:company)
      allow_any_instance_of(Integrations::Expensify::FetchData).to receive(:authenticate?).and_return(true)
      @expensify_config = FactoryBot.create(:expensify_config, company_id: @company.id)
      @company_integration = FactoryBot.create(:company_integration_true,
                                               company_id: @company.id,
                                               integration_id: Integration.find_by_name('expensify').id,
                                               integrable_type: @expensify_config.class.name,
                                               integrable_id: @expensify_config.id)
      @payments = [
        ["1234545", "Hudson News", "124434", "Labor", "2020-01-08"],
        ["1239836", "Google", "124400", "Fees", "2020-01-01"],
        ["4535447", "Microsoft", "*********", "Benefits", "2020-01-03"],
        ["7665534", "Skype", "1234567800", "Uncategorized", "2020-01-03"]
      ]

      @templates = [OpenStruct.new({ "parsed_response": "myExportad62b04e-041c-4965-bb03-33283e183223.csv" })]
    end

    context 'SyncExpensify Transactions' do
      it 'calls Integrations::Expensify::FetchData Service' do
        obj = Integrations::Expensify::SyncDataWorker.new
        allow(Integrations::Expensify::SyncDataWorker).to receive(:new).and_return(obj)

        allow_any_instance_of(Integrations::Expensify::FetchData).to receive(:download_expensify_payments).and_return(@payments)
        allow_any_instance_of(Integrations::Expensify::FetchData).to receive(:fetch_expensify_templates).and_return(@templates)
        Integrations::Expensify::SyncDataWorker.new.perform(@company.id, true)

        expect(GeneralTransaction.all.count).to eq(4)
        expect(GeneralTransaction.where(transaction_type: "expenses").count).to eq(4)
      end
    end
  end
end
