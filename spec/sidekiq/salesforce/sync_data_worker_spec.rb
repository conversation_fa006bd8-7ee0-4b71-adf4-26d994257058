require 'rails_helper'

RSpec.describe Integrations::Salesforce::SyncDataWorker, type: :worker do
  describe '#execute' do
    before(:each) do
      @company =  FactoryBot.create(:company)
      @salesforce_config = FactoryBot.create(:salesforce_config, company_id: @company.id)

      @users = OpenStruct.new({parsed_response:
                  {"totalSize"=>5,
                    "done"=>true,
                    "records"=>[
                      {"attributes"=>{"type"=>"User", "url"=>"/services/data/v42.0/sobjects/User/0053i0000028seRAAQ"}, "Id"=>"0053i0000028seRAAQ", "Name"=>"Automated Process", "Email"=>"autoproc@00d3i000000txr3eae", "Country"=>"US", "IsActive"=>true, "FirstName"=>"Automated", "LastName"=>"Process", "CompanyName"=>"GoGenuity", "Username"=>"autoproc@00d3i000000txr3eae", "CreatedDate"=>"2019-08-21T07:08:21.000+0000", "UserType"=>"AutomatedProcess", "ContactId"=>nil, "AccountId"=>nil, "CallCenterId"=>nil, "LastLoginDate"=>nil},
                      {"attributes"=>{"type"=>"User", "url"=>"/services/data/v42.0/sobjects/User/0053i000001cfPMAAY"}, "Id"=>"0053i000001cfPMAAY", "Name"=>"User Name", "Email"=>"<EMAIL>", "Country"=>"US", "IsActive"=>true, "FirstName"=>"User", "LastName"=>"Name", "CompanyName"=>"GoGenuity", "Username"=>"<EMAIL>", "CreatedDate"=>"2019-08-21T07:08:21.000+0000", "UserType"=>"Standard", "ContactId"=>nil, "AccountId"=>nil, "CallCenterId"=>nil, "LastLoginDate"=>"2019-08-29T07:46:31.000+0000"},
                      {"attributes"=>{"type"=>"User", "url"=>"/services/data/v42.0/sobjects/User/0053i0000028seQAAQ"}, "Id"=>"0053i0000028seQAAQ", "Name"=>"Integration User", "Email"=>"<EMAIL>", "Country"=>"US", "IsActive"=>true, "FirstName"=>"Integration", "LastName"=>"User", "CompanyName"=>"GoGenuity", "Username"=>"<EMAIL>", "CreatedDate"=>"2019-08-21T07:08:21.000+0000", "UserType"=>"Standard", "ContactId"=>nil, "AccountId"=>nil, "CallCenterId"=>nil, "LastLoginDate"=>nil},
                      {"attributes"=>{"type"=>"User", "url"=>"/services/data/v42.0/sobjects/User/0053i0000028seUAAQ"}, "Id"=>"0053i0000028seUAAQ", "Name"=>"Security User", "Email"=>"<EMAIL>", "Country"=>"US", "IsActive"=>true, "FirstName"=>"Security", "LastName"=>"User", "CompanyName"=>"GoGenuity", "Username"=>"<EMAIL>", "CreatedDate"=>"2019-08-21T07:08:21.000+0000", "UserType"=>"Standard", "ContactId"=>nil, "AccountId"=>nil, "CallCenterId"=>nil, "LastLoginDate"=>nil},
                      {"attributes"=>{"type"=>"User", "url"=>"/services/data/v42.0/sobjects/User/0053i0000028seTAAQ"}, "Id"=>"0053i0000028seTAAQ", "Name"=>"Chatter Expert", "Email"=>"<EMAIL>", "Country"=>"US", "IsActive"=>true, "FirstName"=>nil, "LastName"=>"Chatter Expert", "CompanyName"=>"GoGenuity", "Username"=>"<EMAIL>", "CreatedDate"=>"2019-08-21T07:08:21.000+0000", "UserType"=>"CsnOnly", "ContactId"=>nil, "AccountId"=>nil, "CallCenterId"=>nil, "LastLoginDate"=>nil}
                    ]
                  },
                })

      @apps = OpenStruct.new({parsed_response:
                  {"apps"=>[
                    {"appId"=>"06m3i0000016tXnAAI", "description"=>"Manage customer service with accounts, contacts, cases, and more", "developerName"=>"Service", "eTag"=>"75a987cdf4750589c9f4369ec792c344", "formFactors"=>["Large"], "headerColor"=>"#0070D2", "iconUrl"=>"https://na112.salesforce.com/logos/Salesforce/ServiceCloud/icon.png", "isNavAutoTempTabsDisabled"=>false, "isNavPersonalizationDisabled"=>false, "label"=>"Service", "logoUrl"=>"https://na112.salesforce.com/logos/Salesforce/ServiceCloud/logo.png", "mobileStartUrl"=>nil, "navItems"=>[], "selected"=>false, "startUrl"=>"https://na112.salesforce.com/home/<USER>", "uiType"=>"Classic", "userNavItems"=>[]},
                    {"appId"=>"06m3i0000016tXoAA", "description"=>"Best-in-class on-demand marketing automation", "developerName"=>"Marketing", "eTag"=>"2e37eac7e032c89412531a509b922c07", "formFactors"=>["Large"], "headerColor"=>"#0070D2", "iconUrl"=>"https://na112.salesforce.com/logos/Salesforce/MarketingCloud/icon.png", "isNavAutoTempTabsDisabled"=>false, "isNavPersonalizationDisabled"=>false, "label"=>"Marketing", "logoUrl"=>"https://na112.salesforce.com/logos/Salesforce/MarketingCloud/logo.png", "mobileStartUrl"=>nil, "navItems"=>[], "selected"=>false, "startUrl"=>"https://na112.salesforce.com/home/<USER>", "uiType"=>"Classic", "userNavItems"=>[]},
                    {"appId"=>"06m3i0000016tXtA", "description"=>"App Launcher tabs", "developerName"=>"AppLauncher", "eTag"=>"223d9e277d0ce4af75f47565326f8086", "formFactors"=>["Large"], "headerColor"=>"#0070D2", "iconUrl"=>"https://na112.salesforce.com/logos/Salesforce/AppLauncher/icon.png", "isNavAutoTempTabsDisabled"=>false, "isNavPersonalizationDisabled"=>false, "label"=>"App Launcher", "logoUrl"=>"https://na112.salesforce.com/logos/Salesforce/AppLauncher/logo.png", "mobileStartUrl"=>nil, "navItems"=>[], "selected"=>false, "startUrl"=>"https://na112.salesforce.com/app/mgmt/applauncher/appLauncher.apexp?tsid=02u3i0000016tXt", "uiType"=>"Classic", "userNavItems"=>[]},
                    {"appId"=>"06m3i0000016tXu", "description"=>"Salesforce CRM Communities", "developerName"=>"Community", "eTag"=>"06a106a0de002074ed4d9aa6a210ac5a", "formFactors"=>["Large"], "headerColor"=>"#0070D2", "iconUrl"=>"https://na112.salesforce.com/logos/Salesforce/Community/icon.png", "isNavAutoTempTabsDisabled"=>false, "isNavPersonalizationDisabled"=>false, "label"=>"Community", "logoUrl"=>"https://na112.salesforce.com/logos/Salesforce/Community/logo.png", "mobileStartUrl"=>nil, "navItems"=>[], "selected"=>false, "startUrl"=>"https://na112.salesforce.com/home/<USER>", "uiType"=>"Classic", "userNavItems"=>[]}
                  ]
                }
              })

      @licenses = OpenStruct.new({parsed_response:
                    {"totalSize"=>27,
                      "done"=>true,
                      "records"=>[
                        {"attributes"=>{"type"=>"UserLicense", "url"=>"/services/data/v42.0/sobjects/UserLicense/1003i0000014pF2AAI"}, "Id"=>"1003i0000014pF2Azsxs", "Name"=>"Salesforce", "TotalLicenses"=>2, "UsedLicenses"=>1, "Status"=>"Active"},
                        {"attributes"=>{"type"=>"UserLicense", "url"=>"/services/data/v42.0/sobjects/UserLicense/1003i0000014pF3AAI"}, "Id"=>"1003i0000014pFye", "Name"=>"Cloud Integration User", "TotalLicenses"=>1, "UsedLicenses"=>0, "Status"=>"Active"},
                        {"attributes"=>{"type"=>"UserLicense", "url"=>"/services/data/v42.0/sobjects/UserLicense/1003i0000014pF4AAI"}, "Id"=>"1003i0000014pF4vdcdv", "Name"=>"Salesforce Platform", "TotalLicenses"=>3, "UsedLicenses"=>0, "Status"=>"Active"},
                        {"attributes"=>{"type"=>"UserLicense", "url"=>"/services/data/v42.0/sobjects/UserLicense/1003i0000014pF5AAI"}, "Id"=>"1003i0000014pF5Adxgghbv", "Name"=>"Customer Community Login", "TotalLicenses"=>5, "UsedLicenses"=>0, "Status"=>"Active"}
                      ]
                    }
                  })
       @license_user = OpenStruct.new({parsed_response:
                        {"totalSize"=>8,
                          "done"=>true,
                          "records"=>[
                            {"attributes"=>{"type"=>"User", "url"=>"/services/data/v42.0/sobjects/User/0054T0000017WSlQAM"}, "Id"=>"0054T0000017WSlQAM", "Name"=>"Automated Process", "Profile"=>nil},
                            {"attributes"=>{"type"=>"User", "url"=>"/services/data/v42.0/sobjects/User/0054T0000017WSXQA2"}, "Id"=>"0054T0000017WSXQA2", "Name"=>"Gogenuity Support", "Profile"=>{"attributes"=>{"type"=>"Profile", "url"=>"/services/data/v42.0/sobjects/Profile/00e4T0000015PjAQAU"}, "Name"=>"System Administrator", "UserLicense"=>{"attributes"=>{"type"=>"UserLicense", "url"=>"/services/data/v42.0/sobjects/UserLicense/1004T000000oGtfQAE"}, "Name"=>"Salesforce", "Id"=>"1004T000000oGtfQAE"}}},
                            {"attributes"=>{"type"=>"User", "url"=>"/services/data/v42.0/sobjects/User/0054T0000017WSkQAM"}, "Id"=>"0054T0000017WSkQAM", "Name"=>"Integration User", "Profile"=>{"attributes"=>{"type"=>"Profile", "url"=>"/services/data/v42.0/sobjects/Profile/00e4T0000015PjCQAU"}, "Name"=>"Analytics Cloud Integration User", "UserLicense"=>{"attributes"=>{"type"=>"UserLicense", "url"=>"/services/data/v42.0/sobjects/UserLicense/1004T000000oGu5QAE"}, "Name"=>"Analytics Cloud Integration User", "Id"=>"1004T000000oGu5QAE"}}},
                            {"attributes"=>{"type"=>"User", "url"=>"/services/data/v42.0/sobjects/User/0054T0000017WSoQAM"}, "Id"=>"0054T0000017WSoQAM", "Name"=>"Security User", "Profile"=>{"attributes"=>{"type"=>"Profile", "url"=>"/services/data/v42.0/sobjects/Profile/00e4T0000015PjDQAU"}, "Name"=>"Analytics Cloud Security User", "UserLicense"=>{"attributes"=>{"type"=>"UserLicense", "url"=>"/services/data/v42.0/sobjects/UserLicense/1004T000000oGu5QAE"}, "Name"=>"Analytics Cloud Integration User", "Id"=>"1004T000000oGu5QAE"}}},
                            {"attributes"=>{"type"=>"User", "url"=>"/services/data/v42.0/sobjects/User/0054T000001DpHDQA0"}, "Id"=>"0054T000001DpHDQA0", "Name"=>"User 1 u1", "Profile"=>{"attributes"=>{"type"=>"Profile", "url"=>"/services/data/v42.0/sobjects/Profile/00e4T0000015PjJQAU"}, "Name"=>"Cross Org Data Proxy User", "UserLicense"=>{"attributes"=>{"type"=>"UserLicense", "url"=>"/services/data/v42.0/sobjects/UserLicense/1004T000000oGtjQAE"}, "Name"=>"XOrg Proxy User", "Id"=>"1004T000000oGtjQAE"}}},
                            {"attributes"=>{"type"=>"User", "url"=>"/services/data/v42.0/sobjects/User/0054T000001DpHXQA0"}, "Id"=>"0054T000001DpHXQA0", "Name"=>"User 3123 u323", "Profile"=>{"attributes"=>{"type"=>"Profile", "url"=>"/services/data/v42.0/sobjects/Profile/00e4T0000015PjJQAU"}, "Name"=>"Cross Org Data Proxy User", "UserLicense"=>{"attributes"=>{"type"=>"UserLicense", "url"=>"/services/data/v42.0/sobjects/UserLicense/1004T000000oGtjQAE"}, "Name"=>"XOrg Proxy User", "Id"=>"1004T000000oGtjQAE"}}},
                            {"attributes"=>{"type"=>"User", "url"=>"/services/data/v42.0/sobjects/User/0054T0000017WSnQAM"}, "Id"=>"0054T0000017WSnQAM", "Name"=>"Chatter Expert", "Profile"=>{"attributes"=>{"type"=>"Profile", "url"=>"/services/data/v42.0/sobjects/Profile/00e4T0000015PjBQAU"}, "Name"=>"Chatter Free User", "UserLicense"=>{"attributes"=>{"type"=>"UserLicense", "url"=>"/services/data/v42.0/sobjects/UserLicense/1004T000000oGu1QAE"}, "Name"=>"Chatter Free", "Id"=>"1004T000000oGu1QAE"}}},
                            {"attributes"=>{"type"=>"User", "url"=>"/services/data/v42.0/sobjects/User/0054T000001DpHhQAK"}, "Id"=>"0054T000001DpHhQAK", "Name"=>"u1234567890 u1234567890", "Profile"=>{"attributes"=>{"type"=>"Profile", "url"=>"/services/data/v42.0/sobjects/Profile/00e4T0000015PjbQAE"}, "Name"=>"Chatter Moderator User", "UserLicense"=>{"attributes"=>{"type"=>"UserLicense", "url"=>"/services/data/v42.0/sobjects/UserLicense/1004T000000oGu1QAE"}, "Name"=>"Chatter Free", "Id"=>"1004T000000oGu1QAE"}}}
                          ]
                        }
                      })
    end
    context 'Sync Salesforce user and apps' do
      it 'calls Integrations::Salesforce::FetchData Service' do
        obj = Integrations::Salesforce::SyncDataWorker.new
        allow(Integrations::Salesforce::SyncDataWorker).to receive(:new).and_return(obj)
        allow_any_instance_of(Integrations::Salesforce::FetchData).to receive(:get_apps).and_return(@apps)
        allow_any_instance_of(Integrations::Salesforce::FetchData).to receive(:get_users).and_return(@users)
        allow_any_instance_of(Integrations::Salesforce::FetchData).to receive(:get_licenses).and_return(@licenses)
        allow_any_instance_of(Integrations::Salesforce::FetchData).to receive(:get_license_users).and_return(@license_user)

        Integrations::Salesforce::SyncDataWorker.new.perform(@salesforce_config.id, true)
        expect(Integrations::Salesforce::App.all.count).to eq(8)
        expect(Integrations::Salesforce::User.all.count).to eq(5)
        expect(Integrations::AppUsage.all.count).to eq(4)
        expect(Integrations::AppSource.all.count).to eq(8)
      end
    end
  end
end
