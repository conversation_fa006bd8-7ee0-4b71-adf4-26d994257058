require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

RSpec.describe SendCustomSurveyEmailWorker, type: :worker do
  create_company_and_user

  let!(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let!(:company_user2) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let!(:help_ticket_params) {
    {
      'Subject' => 'Base Ticket',
      'Created By' => company_user.contributor_id.to_s,
      'Assigned To' => company_user.contributor_id.to_s,
      'Status' => 'Open',
      'Priority' => 'low',
      'Description' => 'This is a test ticket.',
      company: company,
      workspace: workspace
    }
  }
  let!(:help_ticket) { create_ticket(help_ticket_params) }

  let!(:initial_count) { Logs::EmailLog.count }

  subject { SendCustomSurveyEmailWorker.new }

  describe '#perform' do
    context 'send custom survey email' do
      it "does not occur and return early if status is true" do
        subject.perform(help_ticket.id)

        expect(Logs::EmailLog.count).to eq(initial_count)
      end

      it "does not occur if the service option status is false" do
        service_option = ServiceOption.find_by(service_type: "feature", service_name: "help_tickets/surveys")
        service_option.update!(status: false)

        subject.perform(help_ticket.id)

        expect(Logs::EmailLog.count).to eq(initial_count)
      end

      it "does not occur help ticket id does not exist" do
        service_option = ServiceOption.find_by(service_type: "feature", service_name: "help_tickets/surveys")
        service_option.update!(status: false)

        expect {
          subject.perform(99999)
        }.to raise_error(NameError, /undefined local variable or method `company'/)
      end

      it "does happen with valid conditions met" do
        service_option = ServiceOption.find_by(service_type: "feature", service_name: "help_tickets/surveys")
        service_option.update!(status: false)

        last_survey = workspace.custom_surveys.last
        last_survey.update!(visible: true)
        last_survey.trigger.conditions.first["end"] = "low"
        last_survey.trigger.conditions.last["end"] = "Open"
        last_survey.trigger.save!

        help_ticket.update!(creator_ids: [company_user.contributor.id])
        help_ticket.reload

        subject.perform(help_ticket.id)

        expect(Logs::EmailLog.count).to eq(initial_count + 1)
        expect(CustomSurvey::Response.last.name).to eq("High Priority Tickets Survey")
      end
    end
  end
end
