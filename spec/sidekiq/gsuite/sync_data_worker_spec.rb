require 'rails_helper'

RSpec.describe Integrations::Gsuite::SyncDataWorker, type: :worker do
  describe '#execute' do
    before(:each) do
      Rails.application.load_seed
      @company = FactoryBot.create(:company)
      @gsuite_config = FactoryBot.create(:gsuite_config, company_id: @company.id)

      @company_integration = FactoryBot.create(
        :company_integration_true,
        company_id: @company.id,
        integration_id: Integration.find_by_name('gsuite').id,
        integrable_type: @gsuite_config.class.name,
        integrable_id: @gsuite_config.id)

      @authorizer =OpenStruct.new({
        id: Rails.application.credentials.gsuite[:client_id],
        secret: Rails.application.credentials.gsuite[:client_secret],
        scope: ["https://www.googleapis.com/auth/admin.reports.usage.readonly", "https://www.googleapis.com/auth/admin.directory.user.readonly", "https://www.googleapis.com/auth/admin.directory.user.security"],
        token_store: "mock_token_store",
        store: "8943u3498h934h6n934n",
        filename: "token.yaml",
        abort: false,
        u0ltra_safe: false,
        thread_safe: false,
        callback_uri: "/oauth2callback",
        })

      user_1 = OpenStruct.new({
                primary_email: "<EMAIL>",
                name:  OpenStruct.new({fullname: "user2 last"}),
                creation_time: "Wed, 14 Mar 2018 03:41:50 +0000",
                customer_id: "C014vjuq6",
                id: "10078456892758223370445",
                is_admin: false,
                last_login_time: "Wed, 26 Jun 2019 09:18:15 +0000"})

      user_2 = OpenStruct.new({
                primary_email: "<EMAIL>",
                name: OpenStruct.new({fullname: "user last"}),
                creation_time: "Wed, 14 Mar 2018 03:41:50 +0000",
                customer_id: "C014vjuq6",
                id: "100784568927582370445",
                is_admin: false,
                last_login_time: "Wed, 26 Jun 2019 09:18:15 +0000"})

      @users = OpenStruct.new(users: [user_1, user_2])

      parameters = OpenStruct.new(msg_value: [ {"client_id"=>"22378802832-klpcj5dosalhnu0vshg3hjm9qgidmp8j.apps.googleusercontent.com", "client_name"=>"Truecaller", "num_users"=>"1"}, {"client_id"=>"705229005811-2fdpup66d8aefq4qs2ru1n8qiosuq4fb.apps.googleusercontent.com", "client_name"=>"Honey", "num_users"=>"1"}, {"client_id"=>"946018238758-aht63nue21hvlj2d6maf07vvdjisckhh.apps.googleusercontent.com", "client_name"=>"macOS", "num_users"=>"1"}, {"client_id"=>"224054973946.apps.googleusercontent.com", "client_name"=>"Mailtrack", "num_users"=>"1"} ])
      usage_reports = OpenStruct.new(parameters: [parameters])
      @app_response = OpenStruct.new(usage_reports: [usage_reports])

      user_app_1 = OpenStruct.new({
                    anonymous: false,
                    client_id: "***********.apps.googleusercontent.com",
                    display_text: "SoundCloud",
                    etag: "\"zPBZh0mDALCYqI7HMkUiX8qQjpg/qh1H9Snq8T8efIwEfuC8Rj3kYOw\"",
                    kind: "admin#directory#token",
                    native_app: false,
                    scopes: ["https://www.google.com/accounts/OAuthLogin"],
                    user_key: "109601430506775666744"})

      user_app_2 = OpenStruct.new({
                    anonymous: false,
                    client_id: "***********.apps.googleusercontent.com",
                    display_text: "Honey",
                    etag: "\"zPBZh0mDALCYqI7HMkUiX8qQjpg/qh1H9Snq8T8efIwEfuC8Rj3kYOw\"",
                    kind: "admin#directory#token",
                    native_app: false,
                    scopes: ["https://www.google.com/accounts/OAuthLogin"],
                    user_key: "109601430506775666744"})

      @user_apps =OpenStruct.new({items: [user_app_1, user_app_2]})
      license_1 = OpenStruct.new({
                  product_id: "Google-Apps",
                  product_name: "G Suite",
                  sku_name: "G Suite Basic",
                  user_id: "<EMAIL>",
                  sku_id: "Google-Apps-For-Business"
                })
      license_2 = OpenStruct.new({
                    product_id: "Google-Apps",
                    product_name: "G Suite",
                    sku_name: "G Suite Basic",
                    user_id: "<EMAIL>",
                    sku_id: "Google-Apps-For-Business"
                  })
      @licenses = [OpenStruct.new({items: [license_1, license_2]})]
    end

    context 'Sync Gsuite user and apps' do
      it 'calls Integrations::Gsuite::FetchData Service' do
        obj = Integrations::Gsuite::SyncDataWorker.new
        allow(Integrations::Gsuite::SyncDataWorker).to receive(:new).and_return(obj)
        objt = Integrations::Gsuite::SaveUserApps.new
        allow(Integrations::Gsuite::SaveUserApps).to receive(:new).and_return(objt)
        allow_any_instance_of(Google::Auth::UserAuthorizer).to receive(:get_credentials).and_return(@authorizer)
        allow_any_instance_of(Integrations::Gsuite::FetchData).to receive(:get_apps).and_return(@app_response)
        allow_any_instance_of(Integrations::Gsuite::FetchData).to receive(:get_users).and_return(@users)
        allow_any_instance_of(Integrations::Gsuite::FetchData).to receive(:get_user_apps).and_return(@user_apps)
        allow_any_instance_of(Integrations::Gsuite::FetchData).to receive(:get_apps_usage).and_return(@app_response)
        allow_any_instance_of(Integrations::Gsuite::FetchData).to receive(:get_gsuite_licenses).and_return(@licenses)

        Integrations::Gsuite::SyncDataWorker.new.perform(@gsuite_config.id, true)
# This will be fixed by Hassan
=begin
        expect(Integrations::Gsuite::App.all.count).to eq(5)
        expect(Integrations::Gsuite::User.all.count).to eq(2)

        Integrations::Gsuite::SaveUserApps.new.perform(@gsuite_config.id, true, true)
        expect(Integrations::Gsuite::User.first.apps.all.count).to eq(1)
=end
      end
    end
  end
end
