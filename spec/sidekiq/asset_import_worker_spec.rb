require 'rails_helper'
include ImportHelper

RSpec.describe ImportWorker, type: :worker do
  let(:company_asset_type) {FactoryBot.create(:company_asset_type, company_id: company.id)}
  let(:company) { FactoryBot.create(:company, :with_locations) }
  let(:managed_by_user) { FactoryBot.create(:user, email: '<EMAIL>')}
  let(:used_by_user) { FactoryBot.create(:user, email: '<EMAIL>')}
  let(:location) { company.locations.first }
  let(:desktop) { CompanyAssetType.find_by(name: 'Desktop') }
  let!(:company_user1) { FactoryBot.create(:company_user, company: company, user: managed_by_user) }
  let!(:company_user2) { FactoryBot.create(:company_user, company: company, user: used_by_user) }
  let!(:depreciation1) { create(:depreciation, name: "straight line", depreciation_type: :straight_line, company_id: company.id) }
  let!(:depreciation2) { create(:depreciation, name: "Declining Balance, 5 years", depreciation_type: :declining_balance, company_id: company.id) }
  let!(:depreciation3) { create(:depreciation, name: "sum of years", depreciation_type: :sum_of_years_digits, company_id: company.id) }

  describe "perform" do
    context "creating managed assets" do
      path = "spec/services/import_export/assets/test_assets_upload.xlsx"
      file = File.open(Rails.root.join(path))

      subject do
        ImportExport::AssetImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }

      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should create managed assets" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user1.contributor_id, "assets", xls_import_id, false)
        end
        expect(company.managed_assets.count).to eq(14)
      end
    end
  end
end
