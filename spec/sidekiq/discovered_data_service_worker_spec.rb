require 'rails_helper'

RSpec.describe DiscoveredDataServiceWorker, type: :worker do
  let(:company) { create(:company) }

  describe "perform" do
    context "without an existing IP" do
      before do
        allow(LookupIpInfoService).to receive_message_chain(:new, :fetch_data).and_return({})
      end

      it "does not create a new discovered data service" do
        DiscoveredDataServiceWorker.new.perform(company.id, "**************")
        expect(company.discovered_data_services.count).to eq(1)
      end
    end

    context "with an existing IP that is the same" do
      before do
        IpAddress.create(company: company, ip: "**************")
      end

      it "does not create a new discovered data service" do
        DiscoveredDataServiceWorker.new.perform(company.id, "**************")
        expect(company.discovered_data_services.count).to eq(0)
      end
    end
  end
end