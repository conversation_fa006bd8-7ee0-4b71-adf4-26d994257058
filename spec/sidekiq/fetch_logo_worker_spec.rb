require 'rails_helper'

RSpec.describe FetchLogoWorker, type: :worker do

  let(:company_attributes_valid_name) do
    {
      name: "<PERSON>",
      default_logo_url: "default_logo_url"
    }
  end
  let(:valid_company) { Company.create(company_attributes_valid_name) }

  let(:company_attributes_invalid_name) do
    {
      name: "NotAbleToFindLogoCompanyName",
      default_logo_url: "default_logo_url"
    }
  end

  describe '#execute' do
    context 'Adding a logo to a company' do
      it 'adds a logo to a company if clearbit finds one' do
        logo = "https://nulodgic-static-assets.s3.amazonaws.com/images/email-signature-logo.jpg"
        allow_any_instance_of(Company).to receive(:fetch_logo).and_return(logo)
        FetchLogoWorker.new.perform(valid_company.id)
        valid_company.reload
        expect(valid_company.logo).to be_present
        expect(valid_company.original_logo_url).to be_present
      end

      it 'does not add a logo to a company if clearbit does not find one' do
        invalid_company = Company.create(company_attributes_invalid_name)
        logo = "https://nulodgic-static-assets.s3.amazonaws.com/images/this-doesnt-exist.jpg"
        allow_any_instance_of(Company).to receive(:fetch_logo).and_return(logo)
        FetchLogoWorker.new.perform(invalid_company.id)
        invalid_company.reload
        expect(invalid_company.logo).to_not be_present
        expect(invalid_company.original_logo_url).to_not be_present

      end
    end
  end
end
