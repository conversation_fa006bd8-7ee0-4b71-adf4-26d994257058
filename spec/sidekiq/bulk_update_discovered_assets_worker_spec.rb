require 'rails_helper'

RSpec.describe BulkUpdateDiscoveredAssetsWorker, type: :worker do
  describe "perform" do
    context "update all discovered assets" do
      let(:company1) { create(:company) }
      let(:user) { create(:user) }
      let(:company_user) { create(:company_user, user: user, company: company1) }
      let!(:discovered_asset1) { create(:discovered_asset, company: company1, display_name: "192.88.77", mac_addresses: ['2C:53:B3 X2:W2:L2'], machine_serial_no: "CND5033423", manufacturer:"hp", source: 1) }
      let!(:discovered_asset2) { create(:discovered_asset, company: company1, display_name: "iphone asset", mac_addresses: ['2C:53:B3 X2:W2:L3'], machine_serial_no: "CND5339876", manufacturer: "dells", source: 1) }
      let!(:assets_list) { [discovered_asset1, discovered_asset2] }

      it "will bulk import all discovered assets" do
        BulkUpdateDiscoveredAssetsWorker.new.perform(company1.id, company_user.id, "incomplete", "import", company1.discovered_assets)
        discovered_assets = company1.discovered_assets
        managed_assets = company1.managed_assets
        expect(managed_assets.count).to eq(2)
        expect(discovered_assets.first.display_name).to eq(managed_assets.first.name)
        expect(discovered_assets.second.display_name).to eq(managed_assets.second.name)
      end

      it "will bulk delete discovered assets to managed assets" do
        asset_ids = company1.discovered_assets.pluck(:id)
        BulkUpdateDiscoveredAssetsWorker.new.perform(company1.id, company_user.id, "incomplete", "delete", asset_ids)
        asset = company1.discovered_assets
        expect(asset.first).not_to be_present
        expect(asset.last).not_to be_present
      end

      it "will bulk archive discovered assets to managed assets" do
        asset_ids = company1.discovered_assets.pluck(:id)
        BulkUpdateDiscoveredAssetsWorker.new.perform(company1.id, company_user.id, "incomplete", "archive", asset_ids)
        asset = company1.discovered_assets
        expect(asset.first.status).to eq("ignored")
        expect(asset.last.status).to eq("ignored")
      end

      it "will bulk unarchive discovered assets to managed assets" do
        asset_ids = company1.discovered_assets.pluck(:id)
        BulkUpdateDiscoveredAssetsWorker.new.perform(company1.id, company_user.id, "incomplete", "unarchive", asset_ids)
        asset = company1.discovered_assets
        expect(asset.first.status).to eq("ready_for_import")
        expect(asset.last.status).to eq("ready_for_import")
      end
    end
  end
end
