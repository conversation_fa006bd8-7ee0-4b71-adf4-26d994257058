require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

RSpec.describe HelpTicketExportWorker, type: :worker do
  create_company_and_user

  let(:custom_form) { company.default_workspace.custom_forms.first }
  let!(:help_ticket_params) {
    {
      'Subject' => 'Base Ticket',
      'Created By' => company_user.contributor_id,
      'Assigned To' => company_user.contributor_id,
      'Status' => 'Closed',
      'Priority' => 'Low',
      workspace: company.default_workspace,
      custom_form: custom_form
    }
  }
  let!(:help_ticket) { create_ticket(help_ticket_params) }

  describe "perform" do
    context "with one help ticket" do
      it "should create xlsx report" do
        type = ['status']
        HelpTicketExportWorker.new.perform(company.id, company.default_workspace.id, company_user.id, nil, type)

        expect(CustomReport.first.report_content_type).to eq("xlsx")
        expect(CustomReport.first.report_file_name).to eq("test-company_help_tickets_data.xlsx")
      end
    end
  end
end
