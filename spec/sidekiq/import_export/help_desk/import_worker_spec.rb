require 'rails_helper'
include ImportHelper

RSpec.describe ImportWorker, type: :worker do
  let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
  let!(:company) { FactoryBot.create(:company, :with_locations, :with_assets) }
  let!(:workspace) { company.workspaces.first }
  let!(:user) { create(:user, email: "<EMAIL>") }
  let!(:company_user) { create(:company_user, company: company, user: user, custom_form_id: custom_form.id) }
  let!(:custom_form) { create(:custom_form,
                              company_module: 'helpdesk',
                              company: company,
                              workspace_id: workspace.id) }

  let!(:subj) { create(:custom_form_field,
                       custom_form: custom_form,
                       field_attribute_type: CustomFormField.field_attribute_types[:text],
                       label: 'Subject',
                       name: 'subject') }

  let!(:priority) { create(:custom_form_field,
                           custom_form: custom_form,
                           field_attribute_type: CustomFormField.field_attribute_types[:priority],
                           options: CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS,
                           label: 'Priority',
                           name: 'priority') }

  let!(:status) { create(:custom_form_field,
                         custom_form: custom_form,
                         field_attribute_type: CustomFormField.field_attribute_types[:status],
                         options: CustomFormFieldTemplate::DEFAULT_STATUS_OPTIONS,
                         label: 'Status',
                         name: 'status') }

  let!(:creator) { create(:custom_form_field,
                          custom_form: custom_form,
                          field_attribute_type: CustomFormField.field_attribute_types[:external_user],
                          label: 'Created By',
                          name: 'created_by') }

  let!(:description) { create(:custom_form_field,
                              custom_form: custom_form,
                              field_attribute_type: CustomFormField.field_attribute_types[:rich_text],
                              label: 'Description',
                              name: 'description') }

  describe "perform" do
    context "creating help tickets" do
      path = "spec/services/import_export/help_tickets/test_helpdesk_upload.xlsx"
      file = File.open(Rails.root.join(path))

      subject do
        ImportExport::CompanyModuleImport.new(file, company.id, options: { company_module: "helpdesk", class: "HelpTicket" }, workspace: workspace)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should create all help tickets" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "help_tickets", xls_import_id, false, { "company_module" => "helpdesk", "class" => "HelpTicket" }, workspace.id)
        end
        expect(company.help_tickets.count).to eq(2)
      end
    end

    context "not creating duplicate help tickets" do
      path = "spec/services/import_export/help_tickets/test_helpdesk_duplicates.xlsx"
      file = File.open(Rails.root.join(path))

      subject do
        ImportExport::CompanyModuleImport.new(file, company.id, options: { company_module: "helpdesk", class: "HelpTicket" }, workspace: workspace)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should not create help ticket if it already exists" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "help_tickets", xls_import_id, false, { "company_module" => "helpdesk", "class" => "HelpTicket" }, workspace.id)
        end
        expect(company.help_tickets.count).to eq(1)
      end
    end

    context "not creating invalid data help tickets" do
      path = "spec/services/import_export/help_tickets/test_helpdesk_upload_with_invalid_data.xlsx"
      file = File.open(Rails.root.join(path))

      subject do
        ImportExport::CompanyModuleImport.new(file, company.id, options: { company_module: "helpdesk", class: "HelpTicket" }, workspace: workspace)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should not create any help tickets if file uploaded contains invalid data" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "help_tickets", xls_import_id, false, { "company_module" => "helpdesk", "class" => "HelpTicket" }, workspace.id)
        end
        expect(company.help_tickets.count).to eq(0)
      end
    end

    context "creating contracts for only valid data" do
      path = "spec/services/import_export/help_tickets/test_helpdesk_upload_with_mix_data.xlsx"
      file = File.open(Rails.root.join(path))

      subject do
        ImportExport::CompanyModuleImport.new(file, company.id, options: { company_module: "helpdesk", class: "HelpTicket" }, workspace: workspace)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should only create help tickets for valid data in file uploaded" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "help_tickets", xls_import_id, false, { "company_module" => "helpdesk", "class" => "HelpTicket" }, workspace.id)
        end
        expect(company.help_tickets.count).to eq(2)
      end
    end

    context "not creating help tickets if required fields are missing" do
      path = "spec/services/import_export/help_tickets/test_helpdesk_upload_missing-required_fields.xlsx"
      file = File.open(Rails.root.join(path))

      subject do
        ImportExport::CompanyModuleImport.new(file, company.id, options: { company_module: "helpdesk", class: "HelpTicket" }, workspace: workspace)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should not create any help tickets if required fields are missing" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "help_tickets", xls_import_id, false, { "company_module" => "helpdesk", "class" => "HelpTicket" }, workspace.id)
        end
        expect(company.help_tickets.count).to eq(0)
      end
    end

    context "creating help tickets from multiple sheets" do
      path = "spec/services/import_export/help_tickets/test_multiple_helpdesk_upload.xlsx"
      file = File.open(Rails.root.join(path))

      subject do
        ImportExport::CompanyModuleImport.new(file, company.id, options: { company_module: "helpdesk", class: "HelpTicket" }, workspace: workspace)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should create all help tickets from all sheets" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "help_tickets", xls_import_id, false, { "company_module" => "helpdesk", "class" => "HelpTicket" }, workspace.id)
        end
        expect(company.help_tickets.count).to eq(2)
      end
    end
  end
end
