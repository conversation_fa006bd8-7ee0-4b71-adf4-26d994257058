require 'rails_helper'
include LocationHelper
include ImportHelper

RSpec.describe ImportWorker, type: :worker do
  let!(:domain_validator) { 
    DomainValidator.any_instance.stub(:validate).and_return(true)
  }
  let!(:contract) { 
    FactoryBot.create(:contract,
                      name: "<PERSON>'s desktop",
                      company: company) 
  }
  let!(:location) { 
    location_params = {
      "Name" => "Main",
      "Address" => "123 Main St.",
      "City" => 'Glen Ellyn',
      "State" => 'IL',
      "Country Name" => 'US',
      "Zip" =>  '60050',
      company: company,
    }
    create_location(location_params)
  }
  let(:company) { FactoryBot.create(:company, :with_locations) }
  let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
  let!(:company_user) { create(:company_user, company: company, custom_form_id: custom_form.id) } 

  describe "perform" do
    context "creating telecom services" do
      let!(:path) { "spec/services/import_export/telecom_services/test_telecom_services_upload.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::TelecomServiceImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }
      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should create all telecom services" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "telecom_services", xls_import_id, false)
        end
        expect(company.telecom_services.count).to eq(10)
      end
    end

    context "not creating duplicate services" do
      let!(:path) { "spec/services/import_export/telecom_services/test_telecom_services_upload_duplicate.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::TelecomServiceImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should not create telecom services with same name if service type or telecom provider are also same" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "telecom_services", xls_import_id, false)
        end
        expect(company.telecom_services.count).to eq(1)
      end
    end

    context "creating services with same name" do
      let!(:path) { "spec/services/import_export/telecom_services/test_telecom_services_upload_with_different_type_and_providers.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::TelecomServiceImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should create telecom service with same name if service type or telecom provider are different" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "telecom_services", xls_import_id, false)
        end
        expect(company.telecom_services.count).to eq(4)
      end
    end

    context "not creating services if required fields are missing" do
      let!(:path) { "spec/services/import_export/telecom_services/test_telecom_services_upload_missing_required_fields.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::TelecomServiceImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should not create any telecom service if required fields are missing" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "telecom_services", xls_import_id, false)
        end
        expect(company.telecom_services.count).to eq(0)
      end
    end

    context "services with formatting errors" do
      let!(:path) { "spec/services/import_export/telecom_services/test_telecom_services_upload_with_formatting_errors.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::TelecomServiceImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should notify monthly cost formatting error" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "telecom_services", xls_import_id, false)
        end
        row = XlsImportRow.last
        row.data.each_with_index do |row_data|
          if row_data["monthly_cost"]
            expect(row_data["valid"]).to be_falsey
            expect(row_data["error"]).to eq("Monthly cost amount is not a number")
          else
            expect(row_data["valid"]).to be_truthy
          end
        end
      end
    end
  end
end
