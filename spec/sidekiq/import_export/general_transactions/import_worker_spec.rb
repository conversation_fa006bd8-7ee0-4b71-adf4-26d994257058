require 'rails_helper'
include ImportHelper

RSpec.describe ImportWorker, type: :worker do
  let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
  let(:company) { FactoryBot.create(:company, :with_locations) }
  let!(:company_user) { create(:company_user, company: company, custom_form_id: custom_form.id) } 
  let!(:options) do
    {
      columns: %w{
        vendor transaction_name product_description amount start_date tags location
      },
      attributes: %w{
        vendor product_name product amount transaction_date general_transaction_tags location_name
      },
      transaction_type: 'non_recurring_transaction'
    }
  end

  let(:recurring_options) do
    {
      columns: %w{
        vendor transaction_name product_description amount start_date end_date tags billing_frequency location
      },
      attributes: %w{
        vendor product_name product amount transaction_date end_date general_transaction_tags billing_frequency location_name
      },
      transaction_type: 'recurring_transaction'
    }
  end

  describe "perform" do
    context "creating non-recurring transactions" do
      path = "spec/services/import_export/general_transactions/non_recurring_transaction_upload.xlsx"
      file = File.open(Rails.root.join(path))

      subject do
        ImportExport::GeneralTransactionImport.new(file, company.id, options: options)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should create all transactions with valid data" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "general_transactions", xls_import_id, false, options)
        end
        expect(company.general_transactions.count).to eq(4)
      end
    end

    context "creating recurring transactions" do
      let!(:path) { "spec/services/import_export/general_transactions/recurring_transaction_upload.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::GeneralTransactionImport.new(file, company.id, options: recurring_options)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should create all general_transactions with valid data" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "general_transactions", xls_import_id, false, recurring_options.as_json)
        end
        expect(company.general_transactions.count).to eq(5)
      end
    end

    context "not creating transactions with missing required fields" do
      path = "spec/services/import_export/general_transactions/test_general_transactions_upload_without_required_fields.xlsx"
      file = File.open(Rails.root.join(path))

      subject do
        ImportExport::GeneralTransactionImport.new(file, company.id, options: options)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should not create any transactions if file uploaded does not required fields filled" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "general_transactions", xls_import_id, false, options.as_json)
        end
        expect(company.general_transactions.count).to eq(0)
      end
    end

    context "with formatting errors" do
      path = "spec/services/import_export/general_transactions/test_general_transactions_upload_with_invalid_data.xlsx"
      file = File.open(Rails.root.join(path))

      subject do
        ImportExport::GeneralTransactionImport.new(file, company.id, options: options)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should notify about transaction amount, start_date, billing_cycle formatting issues" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "general_transactions", xls_import_id, false, options.as_json)
        end
        expect(company.general_transactions.count).to eq(0)
        row = XlsImportRow.last
        row.data.each_with_index do |row_data|
          if row_data["start_date"] || row_data["amount"] || row_data["billing_frequency"]
            expect(row_data["valid"]).to be_falsey
            if row_data["start_date"]
              expect(row_data["error"]).to eq("Transaction date must have format of (yyyy-mm-dd).")
            elsif row_data["amount"]
              expect(row_data["error"]).to eq("Transaction amount is not valid.")
            else
              expect(row_data["error"]).to eq("Invalid Billing cycle")
            end
          else
            expect(row_data["valid"]).to be_truthy
          end
        end
      end
    end
  end
end
