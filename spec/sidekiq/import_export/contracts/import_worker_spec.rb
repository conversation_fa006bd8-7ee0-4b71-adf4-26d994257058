require 'rails_helper'
include ImportHelper

RSpec.describe ImportWorker, type: :worker do
  let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
  let(:company) { FactoryBot.create(:company, :with_locations) }
  let(:category) { FactoryBot.create(:category) }
  let!(:company_user) { create(:company_user, company: company, custom_form_id: custom_form.id) } 
  let!(:options) do
    {
      columns: %w{
        vendor name start_date end_date notice_period alert_dates monthly_cost total_cost category locations departments staff_contacts notes contract_tags status
      },
      attributes: %w{
        vendor_name name start_date_formatted alert_dates_formatted monthly_cost contract_value_amount category_name locations_name departments_name staff_contacts notes contract_tags status
      },
      contract_type: 'fixed_term_contract'
    }
  end

  let(:open_ended_options) do
    {
      columns: %w{
        vendor name start_date alert_dates monthly_cost total_cost category locations departments staff_contacts notes contract_tags status
      },
      attributes: %w{
        vendor_name name start_date_formatted alert_dates_formatted monthly_cost contract_value_amount category_name locations_name departments_name staff_contacts notes contract_tags status
      },
      contract_type: 'open_ended_contract'
    }
  end

  describe "perform" do
    context "creating fixed contracts" do
      let!(:path) { "spec/services/import_export/contracts/fixed_term_contract_upload.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::ContractImport.new(file, company.id, options: options)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should create all contracts with valid data" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "contracts", xls_import_id, false, options.as_json)
        end
        expect(company.contracts.count).to eq(8)
      end
    end

    context "creating open ended contracts" do
      let!(:path) { "spec/services/import_export/contracts/open_ended_contract_upload.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::ContractImport.new(file, company.id, options: open_ended_options)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should create all contracts with valid data" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "contracts", xls_import_id, false, open_ended_options.as_json)
        end
        expect(company.contracts.count).to eq(3)
      end
    end

    context "not creating duplicate contracts" do
      let!(:path) { "spec/services/import_export/contracts/test_contracts_duplicates.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::ContractImport.new(file, company.id, options: options)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should not create contracts if it already exists" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "contracts", xls_import_id, false, options.as_json)
        end
        expect(company.contracts.count).to eq(1)
      end
    end

    context "not creating invalid data contracts" do
      let!(:path) { "spec/services/import_export/contracts/test_contracts_upload_with_invalid_data.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::ContractImport.new(file, company.id, options: options)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should not create any contracts if file uploaded contains invalid data" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "contracts", xls_import_id, false, options.as_json)
        end
        expect(company.contracts.count).to eq(0)
      end
    end

    context "creating contracts for only valid data" do
      let!(:path) { "spec/services/import_export/contracts/test_contracts_upload_with_mix_data.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::ContractImport.new(file, company.id, options: options)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should only create contracts for valid data in file uploaded" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "contracts", xls_import_id, false, options.as_json)
        end
        expect(company.contracts.count).to eq(3)
      end
    end

    context "with formatting errors" do
      let!(:path) { "spec/services/import_export/contracts/test_contracts_with_formatting_errors.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::ContractImport.new(file, company.id, options: options)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should notify start date & monthly cost formatting error" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "contracts", xls_import_id, false, options.as_json)
        end
        row = XlsImportRow.last
        row.data.each_with_index do |row_data|
          if row_data["start_date"] || row_data["monthly_cost"]
            expect(row_data["valid"]).to be_falsey
            if row_data["start_date"]
              expect(row_data["error"]).to eq("Start date must have format of (mm/dd/yyyy) OR (yyyy-mm-dd).")
            else
              expect(row_data["error"]).to eq("Monthly cost amount is not a number.")
            end
          else
            expect(row_data["valid"]).to be_truthy
          end
        end
      end
    end

    context 'creating and updating contracts accroding to monthly cost and total cost' do
      let!(:path) { 'spec/services/import_export/contracts/test_contract_monthly_cost_and_total_cost_calculation.xlsx' }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject { ImportExport::ContractImport.new(file, company.id, options: options) }

      let!(:import) { subject.process_file }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      let!(:contract_7) { FactoryBot.create(:contract, name: 'Contract 7', company: company, monthly_cost: 100.17, contract_value_amount: 500.85) }
      let!(:contract_8) { FactoryBot.create(:contract, name: 'Contract 8', company: company, monthly_cost: 157.96, contract_value_amount: 789.8) }
      let!(:contract_9) { FactoryBot.create(:contract, name: 'Contract 9', company: company, monthly_cost: 578.15, contract_value_amount: 2890.75) }
      let!(:contract_10) { FactoryBot.create(:contract, name: 'Contract 10', company: company, monthly_cost: 100, contract_value_amount: 500) }
      let!(:contract_11) { FactoryBot.create(:contract, name: 'Contract 11', company: company, monthly_cost: 100.1, contract_value_amount: 500.5) }
      let!(:contract_12) { FactoryBot.create(:contract, name: 'Contract 12', company: company, monthly_cost: 20.59, contract_value_amount: 102.95) }
      let!(:contract_13) { FactoryBot.create(:contract, name: 'Contract 13', company: company, monthly_cost: 19.17, contract_value_amount: 95.87) }
      let!(:contract_14) { FactoryBot.create(:contract, name: 'Contract 14', company: company, monthly_cost: 150, contract_value_amount: 750) }
      let!(:contract_15) { FactoryBot.create(:contract, name: 'Contract 15', company: company, monthly_cost: 500, contract_value_amount: 2500) }
      let!(:contract_20) { FactoryBot.create(:contract, name: 'Contract 20', company: company, monthly_cost: 593.79, contract_value_amount: 4750.33) }
      let!(:contract_21) { FactoryBot.create(:contract, name: 'Contract 21', company: company, monthly_cost: 22.73, contract_value_amount: 500) }
      let!(:contract_22) { FactoryBot.create(:contract, name: 'Contract 22', company: company, monthly_cost: 496.57, contract_value_amount: 6455.4) }

      let!(:execute_import_worker) do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "contracts", xls_import_id, false, options.as_json)
        end
      end

      it 'should create contract whose monthly cost and total cost is given correctly' do
        contract_1 = company.contracts.find_by_name('Contract 1')
        expect(contract_1).to be_present
      end

      it 'should not create contract whose total cost is not numeric' do
        contract_2 = company.contracts.find_by_name('Contract 2')
        expect(contract_2).to be_blank
        contract_2_row = XlsImportRow.find { |row| row.raw_data['name'] == 'Contract 2' }
        expect(contract_2_row.data.find { |r| r['error'] == 'Total cost is not a number.' }).to be_present
      end

      it 'should not create contract whose monthly cost is not numeric' do
        contract_3 = Contract.find_by_name('Contract 3')
        expect(contract_3).to be_blank
        contract_3_row = XlsImportRow.find { |row| row.raw_data['name'] == 'Contract 3' }
        expect(contract_3_row.data.find { |r| r['error'] == 'Monthly cost amount is not a number.' }).to be_present
      end

      it 'should create contract with zero values whose monthly cost and total cost fields are empty' do
        contract_4 = company.contracts.find_by_name('Contract 4')
        expect(contract_4).to be_present
        expect(contract_4.monthly_cost).to eq(0.0)
        expect(contract_4.contract_value_amount).to eq(0.0)
      end

      it 'should create contract with zero values whose given monthly cost is zero and total cost field is empty' do
        contract_5 = company.contracts.find_by_name('Contract 5')
        expect(contract_5).to be_present
        expect(contract_5.monthly_cost).to eq(0.0)
        expect(contract_5.contract_value_amount).to eq(0.0)
      end

      it 'should create contract with zero values whose monthly cost field is empty and total cost is zero' do
        contract_6 = company.contracts.find_by_name('Contract 6')
        expect(contract_6).to be_present
        expect(contract_6.monthly_cost).to eq(0.0)
        expect(contract_6.contract_value_amount).to eq(0.0)
      end

      it 'should not update contract whose given monthly cost and total cost are same' do
        contract_7.reload
        expect(contract_7.monthly_cost).to eq(100.17)
        expect(contract_7.contract_value_amount).to eq(500.85)
        audit = contract_7.audits.find do |audit|
          audit.audited_changes['contract_value_amount'].present? || audit.audited_changes['monthly_cost'].present?
        end
        expect(audit).to be_blank
      end

      it 'should update monthly cost and total cost according to monthly cost if monthly cost is different' do
        contract_8.reload
        expect(contract_8.monthly_cost).to eq(200.79)
        expect(contract_8.contract_value_amount).to eq(1003.95)
        audit = contract_8.audits.find do |audit|
          audit.audited_changes['contract_value_amount'].present? || audit.audited_changes['monthly_cost'].present?
        end
        expect(audit).to be_present
        expect(audit.audited_changes['monthly_cost']).to eq([157.96, 200.79])
        expect(audit.audited_changes['contract_value_amount']).to eq([789.8, 1003.95])
      end

      it 'should return error if given total cost is changed and monthly cost is same and new calculated total cost difference was more than 1' do
        contract_9.reload
        contract_9_row = XlsImportRow.find { |row| row.raw_data['name'] == contract_9.name }
        expect(contract_9_row.data.find { |r| r['error'] == "The total cost should be \"2890.75\", as per your monthly cost." }).to be_present
        expect(contract_9.monthly_cost).to eq(578.15)
        expect(contract_9.contract_value_amount).to eq(2890.75)
        audit = contract_9.audits.find do |audit|
          audit.audited_changes['contract_value_amount'].present? || audit.audited_changes['monthly_cost'].present?
        end
        expect(audit).to be_blank
      end

      it 'should return error if given monthly cost and total cost both are different and difference of new calculated total cost difference was more than 1' do
        contract_10.reload
        contract_10_row = XlsImportRow.find { |row| row.raw_data['name'] == contract_10.name }
        expect(contract_10_row.data.find { |r| r['error'] == "The total cost should be \"1501.25\", as per your monthly cost." }).to be_present
        expect(contract_10.monthly_cost).to eq(100)
        expect(contract_10.contract_value_amount).to eq(500)
        audit = contract_10.audits.find do |audit|
          audit.audited_changes['contract_value_amount'].present? || audit.audited_changes['monthly_cost'].present?
        end
        expect(audit).to be_blank
      end

      it 'should not update contract if given monthly cost was same and given total cost field was empty' do
        contract_11.reload
        expect(contract_11.monthly_cost).to eq(100.1)
        expect(contract_11.contract_value_amount).to eq(500.5)
        audit = contract_11.audits.find do |audit|
          audit.audited_changes['contract_value_amount'].present? || audit.audited_changes['monthly_cost'].present?
        end
        expect(audit).to be_blank
      end

      it 'should update contract if given monthly cost is diferent and given total cost field is empty' do
        contract_12.reload
        expect(contract_12.monthly_cost).to eq(600.08)
        expect(contract_12.contract_value_amount).to eq(3000.4)
        audit = contract_12.audits.find do |audit|
          audit.audited_changes['contract_value_amount'].present? || audit.audited_changes['monthly_cost'].present?
        end
        expect(audit.audited_changes['monthly_cost']).to eq([20.59, 600.08])
        expect(audit.audited_changes['contract_value_amount']).to eq([102.95, 3000.4])
      end

      it 'should not update contract if given total cost is same and given monthly cost field is empty' do
        contract_13.reload
        expect(contract_13.monthly_cost).to eq(19.17)
        expect(contract_13.contract_value_amount).to eq(95.87)
        audit = contract_13.audits.find do |audit|
          audit.audited_changes['contract_value_amount'].present? || audit.audited_changes['monthly_cost'].present?
        end
        expect(audit).to be_blank
      end

      it 'should update contract if given total cost is different and given monthly cost field is empty ' do
        contract_14.reload
        expect(contract_14.monthly_cost).to eq(600)
        expect(contract_14.contract_value_amount).to eq(3000)
        audit = contract_14.audits.find do |audit|
          audit.audited_changes['contract_value_amount'].present? || audit.audited_changes['monthly_cost'].present?
        end
        expect(audit.audited_changes['monthly_cost']).to eq([150.0, 600.0])
        expect(audit.audited_changes['contract_value_amount']).to eq([750.0, 3000.0])
      end

      it 'should update monthly cost and total cost of contracts to zero if both fields are given empty' do
        contract_15.reload
        expect(contract_15.monthly_cost).to eq(0.0)
        expect(contract_15.contract_value_amount).to eq(0.0)
        audit = contract_15.audits.find do |audit|
          audit.audited_changes['contract_value_amount'].present? || audit.audited_changes['monthly_cost'].present?
        end
        expect(audit.audited_changes['monthly_cost']).to eq([500.0, 0.0])
        expect(audit.audited_changes['contract_value_amount']).to eq([2500.0, 0.0])
      end

      it 'should not create contract if start date of contract is greater than end date ' do
        contract_16 = company.contracts.find_by_name('Contract 16')
        expect(contract_16).to be_blank
        contract_16_row = XlsImportRow.find { |row| row.raw_data['name'] == 'Contract 16' }
        expect(contract_16_row.data.reject { |r| r['error'] == '' }.count).to eq(2)
        expect(contract_16_row.data.find { |r| r['error'] == 'End date must be after start date.' }).to be_present
        expect(contract_16_row.data.find { |r| r['error'] == "Contract value amount can't be calculated. Because, contract dates are not correct." }).to be_present
      end

      it 'should not create contract if start date is not given' do
        contract_17 = company.contracts.find_by_name('Contract 17')
        expect(contract_17).to be_blank
        contract_17_row = XlsImportRow.find { |row| row.raw_data['name'] == 'Contract 17' }
        expect(contract_17_row.data.find { |r| r['error'] == 'Start date cannot be empty.' }).to be_present
      end

      it 'should not create contract if end date is not given' do
        contract_18 = company.contracts.find_by_name('Contract 18')
        expect(contract_18).to be_blank
        contract_18_row = XlsImportRow.find { |row| row.raw_data['name'] == 'Contract 18' }
        expect(contract_18_row.data.find { |r| r['error'] == 'End date cannot be empty.' }).to be_present
      end

      it 'should not update contract if monthly cost is same and total cost difference is betweem -1 and 1' do
        contract_20.reload
        expect(contract_20.monthly_cost).to eq(593.79)
        expect(contract_20.contract_value_amount).to eq(4750.33)
        audit = contract_20.audits.find do |audit|
          audit.audited_changes['contract_value_amount'].present? || audit.audited_changes['monthly_cost'].present?
        end
        expect(audit).to be_blank

        contract_21.reload
        contract_21.reload
        expect(contract_21.monthly_cost).to eq(22.73)
        expect(contract_21.contract_value_amount).to eq(500)
        audit = contract_21.audits.find do |audit|
          audit.audited_changes['contract_value_amount'].present? || audit.audited_changes['monthly_cost'].present?
        end
        expect(audit).to be_blank

        contract_22.reload
        expect(contract_22.monthly_cost).to eq(496.57)
        expect(contract_22.contract_value_amount).to eq(6455.4)
        audit = contract_22.audits.find do |audit|
          audit.audited_changes['contract_value_amount'].present? || audit.audited_changes['monthly_cost'].present?
        end
        expect(audit).to be_blank
      end
    end
  end
end
