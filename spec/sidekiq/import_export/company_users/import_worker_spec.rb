require 'rails_helper'
include ImportHelper

RSpec.describe ImportWorker, type: :worker do
  let!(:domain_validator) { 
    DomainValidator.any_instance.stub(:validate).and_return(true)
  }
  let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
  let(:company) { FactoryBot.create(:company, :with_locations) }
  let!(:company_user) { create(:company_user, company: company, custom_form_id: custom_form.id) } 
  let(:options) { { company_module: "company_user", class: "CompanyUser" } }

  describe "perform" do
    context "creating staff" do
      path = "spec/services/import_export/company_users/test_staff_upload.xlsx"
      file = File.open(Rails.root.join(path))

      subject do
        ImportExport::CompanyUserImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should create all staff members" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "company_users", xls_import_id, false, options)
        end
        expect(company.company_users.count).to eq(3)
      end
    end

    context "not creating duplicate staff members" do
      path = "spec/services/import_export/company_users/test_staff_upload_duplicate.xlsx"
      file = File.open(Rails.root.join(path))

      subject do
        ImportExport::CompanyUserImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should not create staff if it already exists" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "company_users", xls_import_id, false, options)
        end
        expect(company.company_users.count).to eq(2)
      end
    end

    context "not creating staff if required fields are missing" do
      path = "spec/services/import_export/company_users/test_staff_upload_missing-required_fields.xlsx"
      file = File.open(Rails.root.join(path))

      subject do
        ImportExport::CompanyUserImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should not create any staff member if required fields are missing" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "company_users", xls_import_id, false, options)
        end
        expect(company.company_users.count).to eq(1)
      end
    end

    context "not creating staff for invalid" do
      path = "spec/services/import_export/company_users/test_staff_upload_invalid.xlsx"
      file = File.open(Rails.root.join(path))

      subject do
        ImportExport::CompanyUserImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should not create any staff member if data uploaded is invalid" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "company_users", xls_import_id, false, options)
        end
        expect(company.company_users.count).to eq(1)
      end
    end

    context "staff data with formatting errors" do
      path = "spec/services/import_export/company_users/test_staff_upload_with_formatting_errors.xlsx"
      file = File.open(Rails.root.join(path))

      subject do
        ImportExport::CompanyUserImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should notify phone number formatting error" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "company_users", xls_import_id, false, options)
        end
        row = XlsImportRow.last
        row.data.each_with_index do |row_data|
          if row_data["mobile_phone"]
            expect(row_data["valid"]).to be_falsey
            expect(row_data["error"]).to eq("Phone Number is invalid")
          else
            expect(row_data["valid"]).to be_truthy
          end
        end
      end
    end
  end
end
