require 'rails_helper'
include CompanyUserHelper

RSpec.describe ManagedAssetExportWorker, type: :worker do
  create_company_and_user
  let!(:managed_asset) { create(:managed_asset, company: company, mac_addresses: ['2C:53:B3 X2:W2:L2']) }

  describe "perform" do
    it "should create xlsx report" do
      ManagedAssetExportWorker.new.perform(company.id, company_user.id, {})
      report = CustomReport.find_by(company_id: company.id, creator_id: company_user.id, module_name: "managed_assets")
      expect(report).to be_present
      expect(report.report_content_type).to eq("xlsx")
      expect(report.report_file_name).to eq("test-company_managed_assets_data.xlsx")
    end

    it "should create xlsx report from managed assets reporting-analytics tab" do
      analytics_params = {
        analytics_export: true,
        analytics_filters: {
          fetch_all_status_data: true,
          fetch_all_warranty_data: true,
        }
      }
      ManagedAssetExportWorker.new.perform(company.id, company_user.id, analytics_params.to_json)
      report = CustomReport.find_by(company_id: company.id, creator_id: company_user.id, module_name: "managed_assets")
      expect(report).to be_present
      expect(report.report_content_type).to eq("xlsx")
      expect(report.report_file_name).to eq("test-company_managed_assets_data.xlsx")
    end
  end
end
