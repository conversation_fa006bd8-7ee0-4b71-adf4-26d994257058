require 'rails_helper'
include ImportHelper

RSpec.describe ImportWorker, type: :worker do
  let(:company) { FactoryBot.create(:company, :with_locations) }
  let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
  let!(:telecom_provider) { FactoryBot.create(:telecom_provider,
                                              name: 'Sample Telecom Provider',
                                              company: company) }
  let!(:company_user) { create(:company_user, company: company, custom_form_id: custom_form.id) }
  let!(:telecom_service1) { FactoryBot.create(:telecom_service,
                                              name: 'Sample Telecom Service',
                                              company: company,
                                              service_type: "voice_service",
                                              locations: company.locations,
                                              telecom_provider: telecom_provider) }
  let!(:telecom_service2) { FactoryBot.create(:telecom_service,
                                              name: 'New Telecom Service',
                                              company: company,
                                              service_type: "data_service",
                                              locations: company.locations,
                                              telecom_provider: telecom_provider) }
  describe "perform" do
    context "creating numbers" do
      let!(:path) { "spec/services/import_export/phone_numbers/test_phone_numbers_upload.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::PhoneNumberImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should create all phone numbers" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "phone_numbers", xls_import_id, false)
        end
        expect(company.phone_numbers.count).to eq(4)
      end
    end

    context "not creating duplicate numbers" do
      let!(:path) { "spec/services/import_export/phone_numbers/test_phone_numbers_upload_duplicate.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::PhoneNumberImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should not create data with same phone numbers" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "phone_numbers", xls_import_id, false)
        end
        expect(company.phone_numbers.count).to eq(1)
      end
    end

    context "not creating numbers if required fields are missing" do
      let!(:path) { "spec/services/import_export/phone_numbers/test_phone_numbers_upload_with_missing_required_fields.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::PhoneNumberImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should not create any phone number if required fields are missing" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "phone_numbers", xls_import_id, false)
        end
        expect(company.phone_numbers.count).to eq(0)
      end
    end

    context "numbers with no telecom provider for given service" do
      let!(:path) { "spec/services/import_export/phone_numbers/test_phone_numbers_upload_missing_provider_for_service.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::PhoneNumberImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should notify error of missing telecom provider for telecom service" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "phone_numbers", xls_import_id, false)
        end
        row = XlsImportRow.last
        row.data.each_with_index do |row_data|
          if row_data["column"] == "telecom_provider"
            expect(row_data["telecom_provider"].nil?).to be_truthy
            expect(row_data["valid"]).to be_falsey
            expect(row_data["error"]).to eq("Telecom provider must be present for given telecom service")
          else
            expect(row_data["valid"]).to be_truthy
          end
        end
      end
    end

    context "numbers with invalid service for telecom provider" do
      let!(:path) { "spec/services/import_export/phone_numbers/test_phone_numbers_upload_with_invalid_service_for_provider.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::PhoneNumberImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should notify error of invalid telecom service for telecom provider" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "phone_numbers", xls_import_id, false)
        end
        row = XlsImportRow.last
        row.data.each_with_index do |row_data|
          if row_data["telecom_service"]
            expect(row_data["valid"]).to be_falsey
            expect(row_data["error"]).to eq("Invalid telecom service \"Telecom Service\" for the telecom provider \"Sample Telecom Provider\"")
          else
            expect(row_data["valid"]).to be_truthy
          end
        end
      end
    end

    context "invalid service type for phone numbers" do
      let!(:path) { "spec/services/import_export/phone_numbers/test_phone_numbers_upload_with_invalid_service_type.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::PhoneNumberImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should notify error of invalid service type for ip addresses" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "phone_numbers", xls_import_id, false)
        end
        row = XlsImportRow.last
        row.data.each_with_index do |row_data|
          if row_data["telecom_service"]
            expect(row_data["valid"]).to be_falsey
            expect(row_data["error"]).to eq("Service type \"data_service\" for telecom service \"New Telecom Service\" is invalid for phone numbers")
            expect(row_data["suggestions"].length).to eq(telecom_provider.telecom_services.where(service_type: "voice_service").length + 1)
          else
            expect(row_data["valid"]).to be_truthy
          end
        end
      end
    end

    context "numbers with invalid service locations" do
      let!(:path) { "spec/services/import_export/phone_numbers/test_phone_numbers_upload_with_invalid_service_location.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::PhoneNumberImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should notify error of invalid location for given telecom service" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "phone_numbers", xls_import_id, false)
        end
        row = XlsImportRow.last
        row.data.each_with_index do |row_data|
          if row_data["location"]
            expect(row_data["valid"]).to be_falsey
            expect(row_data["error"]).to eq("Invalid location \"Main\" for telecom service \"Sample Telecom Service\"")
            expect(row_data["suggestions"].length).to eq(telecom_service1.locations.length + 1)
          else
            expect(row_data["valid"]).to be_truthy
          end
        end
      end
    end

    context "numbers with formatting errors" do
      let!(:path) { "spec/services/import_export/phone_numbers/test_phone_numbers_upload_with_formatting_error.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::PhoneNumberImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should notify phone number formatting error" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "phone_numbers", xls_import_id, false)
        end
        row = XlsImportRow.last
        row.data.each_with_index do |row_data|
          if row_data["number"]
            expect(row_data["valid"]).to be_falsey
            expect(row_data["error"]).to eq("Phone Number is invalid")
          else
            expect(row_data["valid"]).to be_truthy
          end
        end
      end
    end
  end
end
