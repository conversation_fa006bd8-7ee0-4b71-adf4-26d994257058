require 'rails_helper'
include ImportHelper

RSpec.describe ImportWorker, type: :worker do
  let!(:domain_validator) { 
    DomainValidator.any_instance.stub(:validate).and_return(true)
  }
  let(:company) { FactoryBot.create(:company, :with_locations) }
  let(:category) { FactoryBot.create(:category) }
  let!(:user) { create(:user, email: "<EMAIL>") }
  let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
  let!(:company_user) { create(:company_user, company: company, user: user, custom_form_id: custom_form.id) }

  describe "perform" do
    context "creating vendors" do
      path = "spec/services/import_export/vendors/test_vendors_upload.xlsx"
      file = File.open(Rails.root.join(path))

      subject do
        ImportExport::VendorImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should create all vendors" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "vendors", xls_import_id, false)
        end
        expect(company.vendors.count).to eq(1)
      end
    end

    context "not creating duplicate vendors" do
      path = "spec/services/import_export/vendors/test_vendors_upload_duplicate_data.xlsx"
      file = File.open(Rails.root.join(path))

      subject do
        ImportExport::VendorImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should not create vendors if it already exists" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "vendors", xls_import_id, false)
        end
        expect(company.vendors.count).to eq(1)
      end
    end

    context "not creating vendors with missing required data" do
      path = "spec/services/import_export/vendors/test_vendors_upload_bad_data.xlsx"
      file = File.open(Rails.root.join(path))

      subject do
        ImportExport::VendorImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should not create any vendors if file uploaded contains missing required data" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "vendors", xls_import_id, false)
        end
        expect(company.vendors.count).to eq(0)
      end
    end

    context "not creating vendors with managed by email of user that does not exist" do
      path = "spec/services/import_export/vendors/test_vendors_upload_no_managed_by_user.xlsx"
      file = File.open(Rails.root.join(path))

      subject do
        ImportExport::VendorImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should not create any vendors if file uploaded contains missing required data" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "vendors", xls_import_id, false)
        end
        expect(company.vendors.count).to eq(0)
        row = XlsImportRow.last
        row.data.each_with_index do |row_data|
          if row_data["managed_by"]
            expect(row_data["valid"]).to be_falsey
            expect(row_data["error"]).to eq("User <NAME_EMAIL> does not exist")
          else
            expect(row_data["valid"]).to be_truthy
          end
        end
      end
    end

    context "with formatting errors" do
      path = "spec/services/import_export/vendors/test_vendors_upload_bad_formatted_data.xlsx"
      file = File.open(Rails.root.join(path))

      subject do
        ImportExport::VendorImport.new(file, company.id)
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should notify about URL and phone number formatting errors" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "vendors", xls_import_id, false)
        end
        expect(company.vendors.count).to eq(0)
        row = XlsImportRow.last
        row.data.each_with_index do |row_data|
          if row_data["url"] || row_data["phone_number"]
            expect(row_data["valid"]).to be_falsey
            if row_data["url"]
              expect(row_data["error"]).to eq("URL is invalid")
            else
              expect(row_data["error"]).to eq("Phone number is invalid")
            end
          else
            expect(row_data["valid"]).to be_truthy
          end
        end
      end
    end
  end
end
