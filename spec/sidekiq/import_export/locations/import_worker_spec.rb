require 'rails_helper'
include ImportHelper

RSpec.describe ImportWorker, type: :worker do
  let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
  let(:company) { FactoryBot.create(:company, :with_locations) }
  let!(:company_user) { create(:company_user, company: company, custom_form_id: custom_form.id) } 

  describe "perform" do
    context "creating locations" do
      let!(:path) { "spec/services/import_export/locations/test_locations_upload.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::CompanyModuleImport.new(file, company.id, options: { company_module: "location", class: "Location" })
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should create all locations" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "locations", xls_import_id, false, { "company_module" => "location", "class" => "Location" })
        end
        expect(company.locations.count).to eq(4)
      end
    end

    context "not creating duplicate locations" do
      let!(:path) { "spec/services/import_export/locations/test_locations_duplicates.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::CompanyModuleImport.new(file, company.id, options: { company_module: "location", class: "Location" })
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should not create locations if it already exists" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "locations", xls_import_id, false, { "company_module" => "location", "class" => "Location" })
        end
        expect(company.locations.count).to eq(2)
      end
    end

    context "not creating locations with missing data" do
      let!(:path) { "spec/services/import_export/locations/test_locations_upload_with_missing_data.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::CompanyModuleImport.new(file, company.id, options: { company_module: "location", class: "Location" })
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should not create any locations if file uploaded contains missing data" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "locations", xls_import_id, false, { "company_module" => "location", "class" => "Location" })
        end
        expect(company.locations.count).to eq(2)
      end
    end

    context "with formatting errors" do
      let!(:path) { "spec/services/import_export/locations/test_locations_with_formatting_errors.xlsx" }
      let!(:file) { File.open(Rails.root.join(path)) }

      subject do
        ImportExport::CompanyModuleImport.new(file, company.id, options: { company_module: "location", class: "Location" })
      end

      let!(:import) {
        subject.process_file
      }

      let(:xls_import_id) { subject.xls_import_id }
      let(:xls_import_obj) { XlsImport.find(xls_import_id) }

      it "should notify phone number formatting error" do
        xls_import_obj.xls_sheets.each do |sheet|
          trigger_import_worker(sheet, company.id, company_user.contributor_id, "locations", xls_import_id, false, { "company_module" => "location", "class" => "Location" })
        end
        row = XlsImportRow.last
        row.data.each_with_index do |row_data|
          if row_data["state"]
            expect(row_data["valid"]).to be_falsey
            expect(row_data["error"]).to eq("State MNY not found.")
          else
            expect(row_data["valid"]).to be_truthy
          end
        end
      end
    end
  end
end
