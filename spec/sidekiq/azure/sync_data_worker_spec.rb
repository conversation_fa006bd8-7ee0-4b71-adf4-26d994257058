require 'rails_helper'

RSpec.describe Integrations::Azure::SyncDataWorker, type: :worker do
  describe '#execute' do
    before(:each) do
      @company =  FactoryBot.create(:company)
      @azure_config = Integrations::Azure::Config.create(
      token: "access_token",
      expires_in: 2321,
      refresh_token: "refresh_token",
      company_id: @company.id)
      
      @subscription = OpenStruct.new({parsed_response:
                                        {"value"=>
                                          [{"id"=>"h1ukXt3AlUapQ-CtMS6fFjKiGmk9deJNrQHEEUA_Pv0",
                                            "SubscriptionName"=>"Pay As You Go",
                                          }]
                                        }
                                      })

      @get_subscription_usage = [
                                  [
                                    {"id"=>"h1ukXt3AlUapQ-CtMS6fFjKiGmk9deJNrQHEEUA",
                                     "properties"=>
                                      {
                                        "consumedService"=>"power.shell",
                                        "billingPeriodStartDate"=>"2017-05-23T14:01:38.7786461Z",
                                        "billingPeriodEndDate"=>"2017-05-23T14:01:38.7786461Z",
                                        "quantity"=>"2",
                                        "costInBillingCurrency"=>"0.012"
                                      }
                                    }
                                  ]
                                ]
      @refresh_token = {
                        "expires_in" => 3600, 
                        "access_token" => "cvsdhbcdsbbhbn35h34j/sbcjhsd", 
                        "refresh_token" => "jcbdhjsnxjhvc74yfbhjbc"
                      }
    end

    context 'Sync Azure Subscription usage' do
      it 'calls Integrations::Azure::FetchData Service' do
        obj = Integrations::Azure::SyncDataWorker.new
        allow(Integrations::Azure::SyncDataWorker).to receive(:new).and_return(obj)
        
        allow_any_instance_of(Integrations::Azure::FetchData).to receive(:refresh_token).and_return(@refresh_token)
        allow_any_instance_of(Integrations::Azure::FetchData).to receive(:get_subscriptions).and_return(@subscription)
        allow_any_instance_of(Integrations::Azure::FetchData).to receive(:get_subscription_usage).and_return(@get_subscription_usage)

        Sidekiq::Testing.inline! do
          Integrations::Azure::SyncDataWorker.new.perform(@azure_config.id, true)
        end
        expect(CloudUsageTransaction.where(company_integration_id: @azure_config.company_integration.id).count).to eq(1)
      end
    end
  end
end
