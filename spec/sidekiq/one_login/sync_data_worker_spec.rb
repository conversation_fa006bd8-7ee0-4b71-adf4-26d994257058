require 'rails_helper'

RSpec.describe Integrations::OneLogin::SyncDataWorker, type: :worker do
  let(:company) { FactoryBot.create(:company) }
  let(:one_login_config) { FactoryBot.create(:one_login_config, company_id: company.id)}
  describe '#execute' do
    before(:each) do
      user_1 = OpenStruct.new({
                  company_id: 159,
                  email: "<EMAIL>",
                  firstname: "user",
                  lastname: "last",
                  openid_name: 'openidName',
                  state: 12,
                  status: 1
                })
      user_2 = OpenStruct.new({
                company_id: 159,
                email: "<EMAIL>",
                firstname: "user2",
                lastname: "last",
                openid_name: 'openidName2',
                state: 1,
                status: 2
              })
      @users = [user_1, user_2]
      app_1 = OpenStruct.new({
                connectorId: 101,
                extension: true,
                icon: "https://s3.amazonaws.com/onelogin-assets/images/icons/twitter.png",
                id: 941502,
                name: "Twitter",
                provisioning: false,
                visible: true,
              })
      app_2 = OpenStruct.new({
                connectorId: 101,
                extension: true,
                icon: "https://s3.amazonaws.com/onelogin-assets/images/icons/twitter.png",
                id: 941512,
                name: "Facebook",
                provisioning: false,
                visible: true,
              })
      @apps = [app_1, app_2]
      user_app = OpenStruct.new({
                connectorId: 101,
                extension: true,
                icon: "https://s3.amazonaws.com/onelogin-assets/images/icons/twitter.png",
                id: 941502,
                name: "Twitter",
                provisioning: false,
                visible: true,
        })
      @user_app_names = [user_app.name]
    end
    context 'Sync one login user and apps' do
      it 'calls Integrations::OneLogin::FetchData Service' do
        obj = Integrations::OneLogin::SyncDataWorker.new
        allow(Integrations::OneLogin::SyncDataWorker).to receive(:new).and_return(obj)
        allow_any_instance_of(Integrations::OneLogin::FetchData).to receive(:authenticate?).and_return(true)
        allow_any_instance_of(Integrations::OneLogin::FetchData).to receive(:get_apps).and_return(@apps)
        allow_any_instance_of(Integrations::OneLogin::FetchData).to receive(:get_users).and_return(@users)
        allow_any_instance_of(Integrations::OneLogin::FetchData).to receive(:get_user_apps).and_return(@user_app_names)
        Integrations::OneLogin::SyncDataWorker.new.perform(one_login_config.id, true)
        expect(Integrations::OneLogin::App.all.count).to eq(2)
        expect(Integrations::OneLogin::User.all.count).to eq(2)
        expect(Integrations::OneLogin::User.first.apps.all.count).to eq(1)
        expect(Integrations::AppSource.all.count).to eq(2)
      end
    end
  end
end
