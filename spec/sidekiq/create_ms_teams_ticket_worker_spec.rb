require 'rails_helper'
include CompanyUserHelper

RSpec.describe CreateMsTeamsTicketWorker, type: :worker do
  create_company_and_user
  let(:ms_teams_config) { FactoryBot.create(:ms_teams_config, company_id: company.id, workspace: company.default_workspace) }
  let(:client) { Integrations::MsTeams::Client.new(:ms_teams_config) }
  let(:help_ticket_data) do 
    { 
      'subject' => 'Teams ticket',
      'status' => 'Open',
      'priority' => 'high',
      'created_by' => company_user.contributor_id
    }
  end
  let(:guest_help_ticket_data) do 
    { 
      'subject' => 'Teams ticket by guest',
      'status' => 'Open',
      'priority' => 'high',
      'created_by' => '<EMAIL>'
    }
  end

  describe '#perform' do
    context 'creating ticket from Microsoft Teams' do
      it 'will start the worker for ticket creation' do
        expect(company.help_tickets.count).to eq(0)

        subject.class.new.perform(ms_teams_config.id, company_user.id, help_ticket_data)
        expect(company.help_tickets.count).to eq(1)
        expect(company.help_tickets.last.subject).to eq('Teams ticket')
      end
    end
  end

  describe '#perform' do
    context 'creating ticket from Microsoft Teams with guest' do
      it 'will start the worker for ticket creation' do
        expect(company.help_tickets.count).to eq(0)
        expect(company.guests.count).to eq(0)

        subject.class.new.perform(ms_teams_config.id, nil, guest_help_ticket_data)
        expect(company.help_tickets.count).to eq(1)
        expect(company.help_tickets.last.subject).to eq('Teams ticket by guest')
        expect(company.guests.count).to eq(1)
        expect(company.guests.last.email).to eq('<EMAIL>')
      end
    end
  end
end
