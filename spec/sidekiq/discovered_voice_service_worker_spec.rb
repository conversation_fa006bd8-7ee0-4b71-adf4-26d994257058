require 'rails_helper'

RSpec.describe DiscoveredVoiceServiceWorker, type: :worker do
  let(:company) { create(:company) }

  describe "perform" do
    context "without an existing number" do
      before do
        allow(PhoneNumberLookupService).to receive_message_chain(:new, :call).and_return(OpenStruct.new(success?: true, number_info: {}))
      end

      it "does not create a new discovered data service" do
        DiscoveredVoiceServiceWorker.new.perform(company.id, "************")
        expect(company.discovered_voice_services.count).to eq(1)
      end
    end

    context "with an existing number that is the same" do
      before do
        PhoneNumber.create(company: company, number: "************")
      end

      it "does not create a new discovered data service" do
        DiscoveredVoiceServiceWorker.new.perform(company.id, "************")
        expect(company.discovered_voice_services.count).to eq(0)
      end
    end
  end
end