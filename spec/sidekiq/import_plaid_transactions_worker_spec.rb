require 'rails_helper'

RSpec.describe ImportPlaidTransactionsWorker, type: :worker do
  let(:plaid_account) { create(:plaid_account) }

  describe '#execute' do
    context 'Import Plaid Transactions Worker on call' do
      it 'calls GeneralTransactionService Service' do
        obj = ImportPlaidTransactionsWorker.new
        allow(ImportPlaidTransactionsWorker).to receive(:new).and_return(obj)
        allow(obj).to receive(:perform)
        ImportPlaidTransactionsWorker.new.perform(plaid_account.id)
        expect(ImportPlaidTransactionsWorker).to have_received(:new)
        expect(obj).to have_received(:perform)
      end
    end
  end
end
