require 'rails_helper'
include CompanyUserHelper

RSpec.describe ExistingCompaniesInvitationsController, type: :controller do
  create_company_and_user

  let!(:child_company) { FactoryBot.create(:company, subdomain: 'child-company') }
  let!(:company_invitation) { CompanyInvitation.create(invite_token: SecureRandom.hex(32), parent_company_id: company.id, child_company_id: child_company.id) }

  describe 'Link Existing Companies' do
    it 'will link two existing companies' do
      params = { id: company_invitation.invite_token }
      get :link_company, params: params, as: :json
      expect(child_company.reload.reseller_company_id).to eq(company.id)
      expect(company.reload.is_reseller_company).to eq(true)
    end

    it "will send email to the parent admin contact about linking" do
      params = { id: company_invitation.invite_token }
      get :link_company, params: params, as: :json
      expect(ActionMailer::Base.deliveries.last.subject).to eq("Your company is successfully linked with #{child_company.name}.")
    end
  end
end
