require 'sidekiq/testing'
Sidekiq::Testing.fake!
require 'rails_helper'
include CompanyUserHelper

RSpec.describe PlaidAccountImportsController, type: :controller do
  create_company_and_user

  let(:attributes) do
    {
      account_id: "KwB0ybNy1nuMyXg53",
      name: "Business Platinum Card®",
      bank_name: "Bank of America",
      company_id: company.id,
      activated: Time.now,
      mask: "0000",
      subtype: "checking",
      account_type: "credit_card"
    }
  end

  describe "POST #create" do
    context "with a valid existing Plaid account" do
      let(:plaid_account) {PlaidAccount.create(attributes)}

      it "will perform ImportPlaidTransactionsWorker" do
        expect(ImportPlaidTransactionsWorker.jobs.size).to eq(0)
        expect(plaid_account).to be_valid

        post :create, params: { plaid_account_guid: plaid_account.guid}

        expect(response).to have_http_status(:ok)
        expect(ImportPlaidTransactionsWorker.new).to respond_to(:perform)
        expect(ImportPlaidTransactionsWorker.jobs.size).to eq(2)
      end
    end

    context "without a valid existing Plaid account" do
      it "will not perform ImportPlaidTransactionsWorker" do
        expect(ImportPlaidTransactionsWorker.jobs.size).to eq(0)

        post :create, params: { plaid_account_guid: nil }

        expect(response).to have_http_status(:not_found)
        expect(ImportPlaidTransactionsWorker.jobs.size).to eq(0)
      end
    end
  end
end
