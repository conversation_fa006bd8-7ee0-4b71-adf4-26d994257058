require 'rails_helper'
include Company<PERSON>ser<PERSON>elper

describe FeatureRequestsController, type: :controller do
  create_company_and_user

  let!(:feature_1_attributes) do
    {
      title: "First feature",
      description: "Feature description",
      module_type: "other",
      feature_state: "pending",
      creator_id: company_user.id,
    }
  end

  describe "#index" do
    context "with zero feature request" do
      it "will return blank array" do
        get :index, format: :json
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['feature_requests']).to eq([])
      end
    end

    context "with pending features" do
      let!(:feature_request_1) { FactoryBot.create(:feature_request, title: "First feature", creator_id: company_user.id) }
      let!(:feature_request_2) { FactoryBot.create(:feature_request, title: "Second feature", creator_id: company_user.id) }
      let!(:feature_request_3) { FactoryBot.create(:feature_request, title: "Third feature", creator_id: company_user.id) }

      it "will return records" do
        get :index, :params => { feature_state: "pending", format: :json }

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)["feature_requests"].count).to eq(3)
        request_titles = JSON.parse(response.body)["feature_requests"].pluck('title')
        expect(request_titles).to include("First feature")
        expect(request_titles).to include("Second feature")
        expect(request_titles).to include("Third feature")
      end
    end

    context "with requested features" do
      let!(:feature_request_1) { FactoryBot.create(:feature_request, title: "First feature", creator_id: company_user.id, feature_state: "requested") }
      let!(:feature_request_2) { FactoryBot.create(:feature_request, title: "Second feature", creator_id: company_user.id, feature_state: "requested") }

      it "will return records" do
        get :index, :params => { feature_state: "requested", format: :json }

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)["feature_requests"].count).to eq(2)
        request_titles = JSON.parse(response.body)["feature_requests"].pluck('title')
        expect(request_titles).to include("First feature")
        expect(request_titles).to include("Second feature")
      end
    end

    context "with upcoming features" do
      let!(:feature_request_1) { FactoryBot.create(:feature_request, title: "First feature", creator_id: company_user.id, feature_state: "upcoming") }
      let!(:feature_request_2) { FactoryBot.create(:feature_request, title: "Second feature", creator_id: company_user.id, feature_state: "upcoming") }

      it "will return records" do
        get :index, :params => { feature_state: "upcoming", format: :json }

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)["feature_requests"].count).to eq(2)
        request_titles = JSON.parse(response.body)["feature_requests"].pluck('title')
        expect(request_titles).to include("First feature")
        expect(request_titles).to include("Second feature")
      end
    end

    context "with recent features" do
      let!(:feature_request_1) { FactoryBot.create(:feature_request, title: "First feature", creator_id: company_user.id, feature_state: "recent") }
      let!(:feature_request_2) { FactoryBot.create(:feature_request, title: "Second feature", creator_id: company_user.id, feature_state: "recent") }

      it "will return records" do
        get :index, :params => { feature_state: "recent", format: :json }

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)["feature_requests"].count).to eq(2)
        request_titles = JSON.parse(response.body)["feature_requests"].pluck('title')
        expect(request_titles).to include("First feature")
        expect(request_titles).to include("Second feature")
      end
    end
  end

  describe "#destroy" do
    context "with a bad id" do
      it "will return a 404" do
        delete :destroy, params: { id: 'THISISNOTANID' }
        expect(response).to have_http_status(:not_found)
      end
    end

    context "with a good id" do
      let!(:feature_request_record) { FactoryBot.create(:feature_request, title: "First feature", creator_id: company_user.id, feature_state: "pending") }

      it "will remove the feature request" do
        delete :destroy, params: { id: feature_request_record.id }

        expect(FeatureRequest.where(id: feature_request_record.id)).to be_blank
      end

      it "will return a 200" do
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe "#update" do
    let!(:feature_request) { FactoryBot.create(:feature_request, title: "First feature", creator_id: company_user.id, feature_state: "pending") }
    let(:feature_params) {
      {
        id: feature_request.id,
        title: 'Updated feature',
        description: 'Update description'
      }
    }

    context "with good params" do
      it "will update feature request attributes" do
        put :update, params: { id: feature_request.id, feature_request: feature_params }

        updated_feature = FeatureRequest.find_by(id: feature_request.id)
        expect(response).to have_http_status(:ok)
        expect(updated_feature.title).to eq("Updated feature")
        expect(updated_feature.description).to eq("Update description")
      end
    end
  end

  describe "#create" do
    let!(:subject) { post :create, params: { feature_request: feature_1_attributes } }

    context "with some attributes" do
      it "will create new feature request" do
        expect(response).to have_http_status(:ok)

        feature = FeatureRequest.last
        expect(feature.title).to eq("First feature")
        expect(feature.module_type).to eq("other")
      end
    end
  end
end
