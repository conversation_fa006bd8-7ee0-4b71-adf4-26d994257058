require 'rails_helper'
include CompanyUserHelper

RSpec.describe LookupsController, type: :controller do
  create_company_and_user

  before do
    @request.host = "secure.#{Rails.application.credentials.root_domain}"
  end

  describe "GET #index" do
    it "returns to root_url if user is not super_admin with secure subdomain" do
      get :index
      expect(response).to have_http_status(:found)
      expect(response).to redirect_to(root_url)
    end
  end

  describe "GET #index" do
    it "returns http success if user is super_admin with secure subdomain" do
      user.super_admin = true
      user.save
      get :index
      expect(response).to have_http_status(:ok)
    end
  end
end
