require 'rails_helper'
include CompanyUserHelper

describe PaymentSourcesController, type: :controller do
  create_company_and_user

  describe "#create" do
    let(:results) { OpenStruct.new(id: 123,
                                   last4: '1234',
                                   brand: "AMEX",
                                   exp_month: 10,
                                   exp_year: 2020) }

    context "without a stipe_token" do
      before(:each) do
        post :create, params: params
      end

      let(:stripe_response) { [:ok, results] }
      let(:params) { {} }

      it "will return a 400 status" do
        expect(response.status).to eq(400)
      end

      it "will not create a new credit card" do
        credit_card = CompanyCreditCard.first
        expect(credit_card).to be_blank
      end

      it "will not create a new subscription activity" do
        # it won't create a subscription activity without a subscription, and that
        # doesn't created until after the payment_source#create
        activity = SubscriptionActivity.last
        expect(activity).to be_blank
      end
    end

    context "with a valid response from Stripe" do
      let(:params) { {stripe_token: "AABBCC"} }
      let(:stripe_response) { [:ok, results] }

      before(:each) do
        expect(controller).to receive(:add_credit_card).and_return(stripe_response)
        allow(Stripe::Customer).to receive(:retrieve).and_return(true)
        company.stripe_id = "AABBCCDD"
        company.save!
        post :create, params: params
      end

      it "will return a 200 status" do
        expect(response.status).to eq(200)
      end

      it "will create a new credit card" do
        credit_card = CompanyCreditCard.first
        expect(credit_card).to be_present
      end

      it "will not create a new subscription activity" do
        # it won't create a subscription activity without a subscription, and that
        # doesn't created until after the payment_source#create
        activity = SubscriptionActivity.last
        expect(activity).to be_blank
      end
    end

    context "with an error response from Stripe" do
      let(:params) { {stripe_token: "AABBCC"} }
      let(:stripe_response) { [:bad, "Boom!"] }

      before(:each) do
        expect(controller).to receive(:add_credit_card).and_return(stripe_response)
        expect(Stripe::Customer).to receive(:retrieve).and_return(false)
        company.stripe_id = "AABBCCDD"
        company.save!
        customer = OpenStruct.new(id: 'ASDFASFDASDF')
        expect(Stripe::Customer).to receive(:create).and_return(customer)
        post :create, params: params
      end

      it "will return a 402 status" do
        expect(response.status).to eq(402)
      end

      it "will not create a new credit card" do
        credit_card = CompanyCreditCard.first
        expect(credit_card).to be_blank
      end

      it "will return valid JSON response" do
        json = JSON.parse(response.body)
        expect(json["message"]).to eq("Boom!")
      end

      it "will not create a new subscription activity" do
        # it won't create a subscription activity without a subscription, and that
        # doesn't created until after the payment_source#create
        activity = SubscriptionActivity.last
        expect(activity).to be_blank
      end
    end
  end

  describe "#destroy" do
    let(:results) { OpenStruct.new(id: 123,
                                   last4: '1234',
                                   brand: "AMEX",
                                   exp_month: 10,
                                   exp_year: 2020) }

    context "with an error from Stripe" do
      let!(:credit_card) { CompanyCreditCard.create(company_id: company.id,
                                                    stripe_card_id: results.id,
                                                    last4: results.last4,
                                                    brand: results.brand,
                                                    exp_month: results.exp_month,
                                                    exp_year: results.exp_year) }

      before(:each) do
        expect(controller).to receive(:remove_stripe_credit_card).and_return([:bad, "Boom!"])
        delete :destroy, params: {id: 1} # the id is need to complete the route
      end

      it "will return a 422 status" do
        expect(response.status).to eq(422)
      end

      it "will return valid JSON response" do
        json = JSON.parse(response.body)
        expect(json["message"]).to eq("Boom!")
      end

      it "will not destroy the existing credit card" do
        credit_card = company.company_credit_card
        expect(credit_card).to be_present
      end

      it "will not create a new subscription activity" do
        # it won't create a subscription activity without a subscription, and that
        # doesn't created until after the payment_source#create
        activity = SubscriptionActivity.last
        expect(activity).to be_blank
      end
    end

    context "with an existing company credit card" do
      let!(:credit_card) { CompanyCreditCard.create(company_id: company.id,
                                                    stripe_card_id: results.id,
                                                    last4: results.last4,
                                                    brand: results.brand,
                                                    exp_month: results.exp_month,
                                                    exp_year: results.exp_year) }

      before(:each) do
        expect(controller).to receive(:remove_stripe_credit_card).and_return([:ok, results])
        delete :destroy, params: {id: 1} # the id is need to complete the route
      end

      it "will return a 200 status" do
        expect(response.status).to eq(200)
      end

      it "will destroy the existing credit card" do
        credit_card = company.company_credit_card
        expect(credit_card).to be_blank
      end

      it "will not create a new subscription activity" do
        # it won't create a subscription activity without a subscription, and that
        # doesn't created until after the payment_source#create
        activity = SubscriptionActivity.last
        expect(activity).to be_blank
      end
    end

    context "without an existing company credit card" do
      before(:each) do
        expect(controller).not_to receive(:remove_stripe_credit_card)
        delete :destroy, params: {id: 1} # the id is need to complete the route
      end

      it "will return a 200 status" do
        expect(response.status).to eq(404)
      end
    end
  end

  describe "#update" do
    let(:results) { OpenStruct.new(last4: '1234',
                                   brand: "AMEX",
                                   exp_month: 10,
                                   exp_year: 2026) }
    let(:credit_card) { CompanyCreditCard.create(last4: '5678', brand: "AMEX", exp_month: 2, exp_year: 2024)}

    let(:restart_subscription_results) { OpenStruct.new(id: "1234") }
    let(:stripe_response) { [:ok, results] }
    let(:restart_susbcription_response) { [:ok, restart_subscription_results] }
    let(:subscription_plan_monthly) { SubscriptionPlan.find_or_create_by(name: "Basic Plan") }

    before(:each) do
      expect(controller).to receive(:add_credit_card).and_return(stripe_response)
      allow_any_instance_of(StripeSubscriptionService).to receive(:restart_subscription).and_return(restart_susbcription_response)
      allow(Stripe::Customer).to receive(:retrieve).and_return(true)
      allow(Stripe::Subscription).to receive(:retrieve).and_return(stripe_subscription)
      company.stripe_id = "AABBCCDD"
      company.save!
      Subscription.create(company: company, stripe_subscription_id: "12345", start_date: Date.today, status: Subscription.statuses[:insolvent], subscription_plan_id: subscription_plan_monthly.id)
      put :update, params: {id: credit_card.id, stripe_token: "AABBCC"}
    end
    
    context "restart the subscription when it is ended on stripe" do
      let(:stripe_subscription) { 
        {
          "id" => "sub_123",
          "ended_at" => 1683635944,
        }
      }

      xit "will activate the company subscription" do
        expect(response.status).to eq(200)
        company.subscription.reload
        expect(company.subscription.status).to eq("active")
        expect(company.subscription.stripe_subscription_id).to eq(restart_subscription_results["id"])
      end
    end

    context "doesn't restart the subscription when it is active on stripe" do
      let(:stripe_subscription) { 
        {
          "id": "sub_123",
        }
      }

      xit "will not activate the company subscription" do
        expect(response.status).to eq(200)
        company.subscription.reload
        expect(company.subscription.status).to eq("insolvent")
      end
    end
  end
end