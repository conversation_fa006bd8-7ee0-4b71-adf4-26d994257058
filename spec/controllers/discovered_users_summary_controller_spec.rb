require 'rails_helper'
include CompanyUserHelper

RSpec.describe DiscoveredUsersSummaryController, type: :controller do
  create_company_and_user

  describe "Index" do
    let(:ready_for_import_params) do
      {
        email: "<EMAIL>",
        first_name: "<PERSON>",
        last_name: "hardy",
        principal_name: "abc",
        account_name: "tom"
      }
    end
    let(:imported_params) do
      {
        email: "<EMAIL>",
        first_name: "<PERSON>",
        last_name: "hardy",
        principal_name: "abc",
        account_name: "tom",
        status: 1
      }
    end
    it "test summary of discovered users" do
      discovered_user = company.discovered_users.create(ready_for_import_params)
      discovered_user = company.discovered_users.create(imported_params)
      response = get :index, format: :json
      result = JSON.parse(response.body)
      expect(result["total_ready_for_import"]).to eq(1)
      expect(result["total_imported"]).to eq(1)
    end
  end
end
