require 'rails_helper'
include CompanyUserHelper
include ActiveSupport::Testing::TimeHelpers

describe SubscriptionsController, type: :controller do
  create_company_and_user

  let(:results) { OpenStruct.new(id: "1234", current_period_start: Time.now.to_i) }
  let(:stripe_customer) { OpenStruct.new(email: "<EMAIL>") }
  let(:stripe_response) { [:ok, results] }
  let(:subscription_retrieval) { OpenStruct.new(id: "1234", current_period_end: (Time.now + 4.days).to_i, cancel_at: (Time.now + 4.days).to_i) }
  let(:subscription_plan_monthly) { SubscriptionPlan.find_or_create_by(name: "Basic Plan") }
  let(:subscription_plan_yearly) { SubscriptionPlan.find_or_create_by(name: "Yearly Plan") }

  describe "#create without referral present" do
    before(:each) do
      expect(controller).to receive(:create_stripe_subscription).and_return(stripe_response)
      expect(CompleteSubscriptionSetupWorker).to receive(:perform_async)
      allow(Stripe::Customer).to receive(:retrieve).and_return(stripe_customer)
      post :create
    end

    context "creating a subscription" do
      xit "should create a subscription" do
        expect(response.status).to eq(200)
        expect(company.subscription).to be_present
      end
    end
  end

  describe "#update" do
    before do
      Subscription.create(company: company, stripe_subscription_id: "12345", start_date: Date.today, status: Subscription.statuses[:active], subscription_plan_id: subscription_plan_monthly.id)
    end

    let(:canceled_results) { OpenStruct.new(id: "12345", current_period_end: (Date.today+1.month).to_time.to_i, cancel_at_period_end: true, cancel_at: (Date.today+1.month).to_time.to_i) }
    let(:restarted_results) { OpenStruct.new(id: "12345", current_period_end: nil, cancel_at_period_end: false) }
    let(:canceled_stripe_response) { [:ok, canceled_results] }
    let(:restarted_stripe_response) { [:ok, restarted_results] }

    context "canceling an active subscription" do
      before do
        expect(controller).to receive(:cancel_stripe_subscription).and_return(canceled_stripe_response)
        expect(controller).to receive(:retrieve_stripe_subscription).and_return(subscription_retrieval)
        allow(Stripe::Customer).to receive(:retrieve).and_return(stripe_customer)
        put :update, params: { id: company.subscription.id, canceling: true }
      end

      xit "sends cancel_stripe_subscription to the stripe subscription service" do
        expect(response.status).to eq(200)
        subscription = Subscription.find_by(company_id: company.id)
        expect(subscription.status).to eq("canceled")
      end
    end

    context "restarting a canceled subscription before the active month is up" do
      before do
        company.subscription.update(status: "canceled", end_date: Date.today + 4.days)
        expect(controller).to receive(:restart_stripe_subscription).and_return(restarted_stripe_response)
        expect(controller).to receive(:retrieve_stripe_subscription).and_return(subscription_retrieval)
        allow(Stripe::Customer).to receive(:retrieve).and_return(stripe_customer)
        put :update, params: { id: company.subscription.id }
      end

      xit "sends restart_stripe_subscription to the stripe subscription service" do
        expect(response.status).to eq(200)
        subscription = Subscription.find_by(company_id: company.id)
        expect(subscription.status).to eq("active")
      end
    end

    context "restarting a canceled subscription after the active month is up" do
      before do
        company.subscription.update(status: "canceled", end_date: Date.today + 4.days)
        expect(controller).to receive(:restart_stripe_subscription).and_return(stripe_response)
        expect(controller).to receive(:retrieve_stripe_subscription).and_return(subscription_retrieval)
        allow(Stripe::Customer).to receive(:retrieve).and_return(stripe_customer)
        put :update, params: { id: company.subscription.id }
      end

      xit "sends restart_stripe_subscription to the stripe subscription service but creates a new one" do
        expect(response.status).to eq(200)
        subscription = Subscription.find_by(company_id: company.id)
        expect(subscription.stripe_subscription_id).to eq("1234")
        expect(subscription.status).to eq("active")
      end
    end

    context "upgrading an active monthly subscription" do
      before do
        expect(controller).to receive(:upgrade_stripe_subscription).and_return(stripe_response)
        expect(controller).to receive(:retrieve_stripe_subscription).and_return(subscription_retrieval)
        Stripe::Customer.stub(:retrieve).and_return(stripe_customer)
        put :update, params: { id: company.subscription.id, upgrading: true, subscription_type: "year" }
      end

      xit "upgrades active subscription to yearly" do
        expect(response.status).to eq(200)
        subscription = Subscription.find_by(company_id: company.id)
        expect(subscription.status).to eq("active")
        expect(subscription.subscription_plan_id).to eq(subscription_plan_yearly.id)
      end
    end

    context "updating a subscription to monthly type" do
      before do
        expect(controller).to receive(:upgrade_stripe_subscription).and_return(stripe_response)
        expect(controller).to receive(:retrieve_stripe_subscription).and_return(subscription_retrieval)
        Stripe::Customer.stub(:retrieve).and_return(stripe_customer)
      end

      xit "will read and write data to cache, then clear the cache when subscription is updated" do
        subscription = Subscription.find_by(company_id: company.id)
        key = [name: 'check_active_subscription', company_id: company.id]

        Rails.cache.write(key, subscription)
        expect(Rails.cache.fetch(key)).to be_present
        put :update, params: { id: company.subscription.id, upgrading: true, subscription_type: "monthly" }
        expect(Rails.cache.fetch(key)).to be_nil
      end
    end

    context "adding secondary emails to subscription" do
      before do
        expect(controller).to receive(:retrieve_stripe_subscription).and_return(subscription_retrieval)
        allow(Stripe::Customer).to receive(:retrieve).and_return(stripe_customer)
      end

      xit "adds emails of company users as secondary emails to receive billing notifications" do
        put :update, params: { id: company.subscription.id, secondary_emails: [{"id"=>40574, "email"=>"<EMAIL>"}, {"id"=>40585, "email"=>"<EMAIL>"}] }
        subscription = JSON.parse(response.body)['subscription']
        expect(response.status).to eq(200)
        expect(subscription['secondary_emails']).to eq([{"id"=>"40574", "email"=>"<EMAIL>"}, {"id"=>"40585", "email"=>"<EMAIL>"}])
      end

      xit "adds custom email as secondary email to receive billing notifications" do
        put :update, params: { id: company.subscription.id, secondary_emails: [{"id"=>nil, "email"=>"<EMAIL>"}] }
        subscription = JSON.parse(response.body)['subscription']
        expect(response.status).to eq(200)
        expect(subscription['secondary_emails']).to eq([{"id"=>"", "email"=>"<EMAIL>"}])
      end
    end

    context "deleting secondary email from subscription" do
      before do
        expect(controller).to receive(:retrieve_stripe_subscription).twice.and_return(subscription_retrieval)
        Stripe::Customer.stub(:retrieve).and_return(stripe_customer)
        put :update, params: { id: company.subscription.id, secondary_emails: [{"id"=>nil, "email"=>"<EMAIL>"}, {"id"=>"40202", "email"=>"<EMAIL>"}] }
      end

      xit "deletes secondary email from subscription" do
        put :update, params: { id: company.subscription.id, deleted_email: ("<EMAIL>") }
        subscription = JSON.parse(response.body)['subscription']
        expect(response.status).to eq(200)
        expect(subscription['secondary_emails']).to eq([{"id"=>"40202", "email"=>"<EMAIL>"}])
      end
    end
  end

  describe "#index" do
    context "retrieving company subscription and renewal when yearly" do
      before do
        Subscription.create(company: company, start_date: Date.today, status: Subscription.statuses[:active], subscription_plan_id: subscription_plan_yearly.id)
      end

      xit "should get the right subscription data with a start_date of today" do
        get :index

        expect(response.status).to eq(200)
        subscription = JSON.parse(response.body)['subscription']
        expect(subscription['subscription_plan_id']).to eq(subscription_plan_yearly.id)
        expect(subscription['renewal_date']).to eq((Date.today + 1.year).to_s)
        expect(subscription['subscription_plan']['interval']).to eq("year")
        expect(subscription['subscription_plan']['price']).to eq(35988)
      end

      xit "should get the right subscription data with a start_date of 3 months ago" do
        subscription = company.subscription
        subscription.update(start_date: Date.today.beginning_of_month - 3.months)
        get :index

        expect(response.status).to eq(200)
        subscription = JSON.parse(response.body)['subscription']
        expect(subscription['renewal_date']).to eq((Date.today.beginning_of_month + 1.year - 3.months).to_s)
      end

      xit "should get the right subscription data with a start_date of 23 months ago" do
        subscription = company.subscription
        subscription.update(start_date: Date.today - 23.months)
        get :index

        expect(response.status).to eq(200)
        subscription = JSON.parse(response.body)['subscription']
        expect(subscription['renewal_date']).to eq((Date.today + 1.month).to_s)
      end
    end

    context "retrieving company subscription and renewal when monthly" do
      before do
        Subscription.create(company: company, start_date: Date.today, status: Subscription.statuses[:active], subscription_plan_id: subscription_plan_monthly.id)
      end

      xit "should get the right subscription data" do
        get :index

        expect(response.status).to eq(200)
        subscription = JSON.parse(response.body)['subscription']
        expect(subscription['subscription_plan_id']).to eq(subscription_plan_monthly.id)
        expect(subscription['renewal_date']).to eq((Date.today + 1.month).to_s)
        expect(subscription['subscription_plan']['interval']).to eq("month")
        expect(subscription['subscription_plan']['price']).to eq(2999)
      end
    end
  end
end