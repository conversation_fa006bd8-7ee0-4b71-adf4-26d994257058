require 'rails_helper'
include CompanyUserHelper

RSpec.describe ApplicationController, type: :controller do
  create_company_and_user

  controller do
    def index
      raise Exceptions::AccessDenied
    end

    def new
      @current_company_user = current_user.company_users.first
      render body: nil
    end

    def after_sign_in_path_for(resource)
      super resource
    end

    def after_sign_out_path_for(resource)
      super resource
    end
  end

  let(:company_attributes) do
    {
      name: "Example Company1",
      phone_number: "1234567890",
      subdomain: 'test1',
      default_logo_url: "default_logo_url"
    }
  end

  describe "handling AccessDenied exceptions" do
    context "exception for html format" do
      it "renders the layouts/access_denied page" do
        get :index, format: 'html'
        expect(response).to render_template("layouts/access_denied")
      end
    end

    context "exception for json format" do
      it "returns warning message" do
        get :index, format: 'json'
        expect(JSON.parse(response.body)['message']).to eq "Sorry, you're not authorized to perform this action."
      end
    end
  end

  describe "#current_company" do
    context "user default company" do
      it "validates company of logged in user" do
        @request.host = "#{company.subdomain}.#{Rails.application.credentials.root_domain}"
        get :new
        expect(assigns(:current_company)).to eq company
      end
    end

    context "getting company from params" do
      it "validates company set by passing company_subdomain" do
        @request.host = "#{company.subdomain}.#{Rails.application.credentials.root_domain}"
        get :new
        expect(assigns(:current_company)).to eq company
      end
    end
  end

  describe "#current_company_user" do
    it "validates current_company_user of logged in user" do
      @request.host = "#{company.subdomain}.#{Rails.application.credentials.root_domain}"
      get :new
      expect(assigns(:current_company_user)).to eq user.company_users.first
    end
  end

  describe "#subdomain" do
    it "validates subdomain of current_company" do
      @request.host = "#{company.subdomain}.#{Rails.application.credentials.root_domain}"
      get :new
      expect(assigns(:subdomain)).to eq company.subdomain
    end
  end

  describe "#after_sign_out_path_for" do
    it "should redirect to home url" do
      expect(controller.after_sign_out_path_for(user)).to eq("#{Rails.application.credentials.aws[:cognito][:base_uri]}/logout?client_id=#{Rails.application.credentials.aws[:cognito][:client_id]}&logout_uri=http://localhost:3000/users/sign_in")
    end
  end

  describe "#after_sign_in_path_for" do
    context "user belongs to multiple companies" do
      it "should redirect user to select his company" do
        @request.host = "secure.#{Rails.application.credentials.root_domain}"
        second_company =  Company.create(company_attributes)
        user.company_users << create(:company_user, user: user, company: second_company, granted_access_at: 1.month.ago)
        expect(controller.after_sign_in_path_for(user)).to eq select_company_url
      end
    end

    context "user login to a single company with appropriate session stored" do
      it "should redirect user to appropriate location" do
        @request.host = "secure.#{Rails.application.credentials.root_domain}"
        session[:current_company_id] = company.id
        expect(controller.after_sign_in_path_for(user)).to eq user_access_url(id: UserAccess.last.auth_token,
                                                                              domain: Rails.application.credentials.root_domain,
                                                                              subdomain: company.subdomain)
      end
    end
  end
end
