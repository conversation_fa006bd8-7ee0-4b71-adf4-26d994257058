require 'sidekiq/testing'
require 'rails_helper'
include CompanyUserHelper

RSpec.describe PlaidEventLogsController, type: :controller do
  create_company_and_user

  let(:attributes) do
    {
      event_name: "INSTITUTION",
      metadata: "{hi: 'there'}",
      company_user_id: CompanyUser.first.id,
    }
  end

  describe "POST #create" do
    it "will create Plaid event log" do
      post :create, params: { plaid_event_log: attributes}

      expect(response).to have_http_status(:ok)
      expect(Logs::PlaidEventLog.count).to eq(1)
      attributes.keys.each do |key|
        expect(Logs::PlaidEventLog.first.send(key)).to eq(attributes[key])
      end
    end
  end
end
