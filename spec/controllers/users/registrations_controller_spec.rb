require 'rails_helper'

describe Users::RegistrationsController, type: :controller do
  let!(:domain_validator) {
    allow_any_instance_of(DomainValidator).to receive(:validate).and_return(true)
  }

  let!(:company) { create(:company) }

  before(:each) do
    @request.env['devise.mapping'] = Devise.mappings[:user]
    @request.host = "secure.#{Rails.application.credentials.root_domain}"
    Company.current_id = company.id
    allow(request.env['warden']).to receive(:authenticate!)
      .and_throw(:warden, scope: :user)
    allow(controller).to receive(:current_user).and_return(nil)
    allow(User).to receive(:generic_email?).and_return(false)
  end

  context '#create' do
    it 'creates a user properly' do
      params = good_params
      company_params = good_company_params
      email = params[:email]
      allow_any_instance_of(CognitoService).to receive(:find_user_by_email).and_return(false)

      post :create, :params => { :user => params, :company => company_params }

      saved_user = User.find_by_email(email)
      expect(saved_user).to be_present

      saved_company = saved_user.companies.first
      expect(saved_company).to be_present
      expect(Referral.count).to eq(0)
    end
  end

  context 'with invalid data' do
    it 'will not update user and will not create cognito user' do
      allow_any_instance_of(Users::RegistrationsController).to receive(:require_no_authentication).and_return(false)
      allow_any_instance_of(Users::RegistrationsController).to receive(:sign_up_user).and_return(false)
      user = User.create(email: "<EMAIL>", first_name: "Jane", last_name: "Doe", password: "abc@12345")
      params = good_params
      params.delete(:password) # making invalid data
      post :update, params: { user: params, format: :json }

      expect(response.status).to eq(422)
    end
  end

  context 'with valid data' do
    it 'will update user attributes, create cognito user' do
      email = Faker::Internet.email
      user = User.create(email: email, first_name: "Jane", last_name: "Doe", password: "abc@12345")
      params = good_params
      params[:email] = email

      post :update, params: { user: params }
      user.reload
      expect(user.encrypted_password).to eq(nil) #user created to cognito and local password is set to nil.
    end
  end

  context 'with referral params' do
    it 'will create a referral record' do
      email = Faker::Internet.email
      user = User.create(email: email, first_name: "Jane", last_name: "Doe", password: "abc@12345")
      post :create, :params => { :user => good_params, :company => good_company_params, :referred_by => user.id}
      user2 = User.find_by_email(good_params[:email])

      expect(Referral.count).to eq(1)
      expect(Referral.first.referred_id).to eq(user2.id)
      expect(Referral.first.referrer_id).to eq(user.id)
      expect(Referral.first.referred_email).to eq(user2.email)
      expect(Referral.first.referrer_email).to eq(user.email)
    end
  end

  def good_params
    {
      first_name: "John",
      last_name: "Test",
      email: '<EMAIL>',
      password: 'OneWithTheForce123'
    }
  end

  def good_company_params
    {
      name: 'The Office',
      subdomain: 'mordor'
    }
  end
end
