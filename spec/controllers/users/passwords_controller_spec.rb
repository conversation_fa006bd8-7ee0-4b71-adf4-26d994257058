require 'rails_helper'
include Company<PERSON>serHelper

describe Users::PasswordsController, type: :controller do
  create_company_and_user

  let(:company_two) { Company.create(name: "testco2", phone_number: "1234567890", subdomain: 'testco', default_logo_url: "default_logo_url") }

  before(:each) do
    @request.env['devise.mapping'] = Devise.mappings[:user]
    @request.host = 'secure.host'
    user.update!(reset_password_token: "a1b2c3d4e5f6g7h8")
    allow(request.env['warden']).to receive(:authenticate!)
      .and_throw(:warden, scope: :user)
  end

  describe '#create' do
    before do
      request.env["HTTP_REFERER"] = new_user_password_url
    end

    it 'sends password reset instructions when valid email html request' do
      user2 = User.create!(email: Faker::Internet.email, first_name: "<PERSON>", last_name: "<PERSON><PERSON>", password: "abc@12345")
      allow_any_instance_of(CognitoService).to receive(:forgot_password).and_return(true)
      post :create, params: { user: { email: user2.email } }

      user2.reload
      expect(response).to redirect_to(new_user_session_url)
      expect(flash[:notice]).to include(I18n.t('devise.passwords.send_paranoid_instructions'))
    end

    it 'does not send password instructions when invalid email' do
      post :create, params: { user: { email: '<EMAIL>' } }

      expect(response).to redirect_to(new_user_session_url)
      expect(flash[:notice]).to include(I18n.t('devise.passwords.send_paranoid_instructions'))
    end

    it 'sends password reset instructions when valid email json request' do
      user2 = User.create!(email: Faker::Internet.email, first_name: "Jane", last_name: "Doe", password: "abc@12345")
      allow_any_instance_of(CognitoService).to receive(:forgot_password).and_return(true)
      post :create, params: { user: { email: user2.email }, format: :json }

      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)['success']).to eq(true)
    end

    it 'does not send password instructions when invalid email and json request' do
      post :create, params: { user: { email: '<EMAIL>' }, format: :json }

      expect(response).to have_http_status(:not_found)
      expect(JSON.parse(response.body)['success']).to eq(false)
    end
  end

  describe '#update' do
    before do
      @old_password = user.encrypted_password
    end

    it 'updates the password with valid credentials' do
      new_password = 'newpassword'
      allow_any_instance_of(CognitoService).to receive(:confirm_forgot_password).and_return(true)
      put :update, params: { user: { guid: user.guid, password: new_password, password_confirmation: new_password, reset_password_token: user.reset_password_token } }
      user.reload

      expect(user.encrypted_password).to eq(nil)
      expect(response).to be_redirect
      expect(@old_password).to_not eq(user.encrypted_password)
      expect(flash[:notice]).to include(I18n.t('devise.passwords.updated'))
    end

    it 'does not update the password if password_confirmation is empty' do
      new_password = 'newpassword'
      put :update, params: { user: { guid: user.guid, password: new_password, password_confirmation: '', reset_password_token: user.reset_password_token } }

      user.reload

      expect(assigns(:user)).to be_present
      expect(assigns(:user).errors.full_messages.first).to eq("Password confirmation doesn't match Password")
    end

    it 'redirects to user access path when successful password update and belongs to 1 company' do
      new_password = 'newpassword'
      @request.host = "#{company.subdomain}.#{Rails.application.credentials.root_domain}"
      allow_any_instance_of(CognitoService).to receive(:confirm_forgot_password).and_return(true)
      put :update, params: { user: { guid: user.guid, password: new_password, password_confirmation: new_password, reset_password_token: user.reset_password_token } }
      user.reload

      expect(user.encrypted_password).to eq(nil)
      expect(session[:current_company_id]).to eq(company.id)
      expect(response).to redirect_to(user_access_url(id: UserAccess.last.auth_token).gsub("secure", "test"))
    end

    context 'when belongs to multiple companies' do
      before do
        user.company_users << create(:company_user, user: user, company: company_two)
      end

      it 'redirects to select company path when successful password update and belongs to multiple companies' do
        new_password = 'newpassword'
        allow_any_instance_of(CognitoService).to receive(:confirm_forgot_password).and_return(true)
        put :update, params: { user: { guid: user.guid, password: new_password, password_confirmation: new_password, reset_password_token: user.reset_password_token } }

        expect(session[:current_company_id]).to eq(nil)
        expect(response).to redirect_to(select_company_path)
      end
    end
  end
end
