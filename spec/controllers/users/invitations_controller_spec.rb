require 'rails_helper'

describe Users::InvitationsController do
  let(:company_attributes) do
    {
      name: "Company",
      phone_number: "1234567890",
    }
  end
  let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
  let!(:company_user) { create(:company_user, company: company, custom_form_id: custom_form.id) }
  let!(:company) { create(:company, company_attributes) }

  before(:each) do
    @request.env['devise.mapping'] = Devise.mappings[:user]
    @request.host = 'secure.host'
    allow(@controller).to receive(:current_company).and_return(company)
    allow(request.env['warden']).to receive(:authenticate!)
      .and_throw(:warden, scope: :user)
    allow(controller).to receive(:current_user).and_return(nil)
  end

  it 'redirects if password is too short' do
    user = User.create!(first_name: 'Someone',
                        last_name: '<PERSON>',
                        email: '<EMAIL>',
                        password: 'abc@123!',
                        password_confirmation: 'abc@123!',
                        terms_of_services: true)
    user.invite!

    put :update, :params => { :user => { :password => '123', :password_confirmation => '123', :invitation_token => user.raw_invitation_token }}

    expect(response).to be_redirect
  end

  it 'does not redirect if password is empty' do
    user = User.create!(first_name: 'Someone',
                        last_name: 'Smith',
                        email: '<EMAIL>',
                        password: 'abc@123!',
                        password_confirmation: 'abc@123!',
                        invitation_token: '1234',
                        terms_of_services: true)
    user.invite!

    put :update, :params => { :user => { :invitation_token => user.raw_invitation_token } }

    expect(response).to be_redirect
  end

  it 'redirects notifying the token is not valid if no matching user' do
    put :update, :params => { :user => { :password => '12345678', :password_confirmation => '12345678', :invitation_token => 'invalid', :terms_of_services => true } }

    expect(response).to be_redirect
    expect(flash[:error]).to include(I18n.t('devise.invitations.invitation_token_invalid'))
  end

  it 'updates the user with new password' do
    user = User.create!(first_name: 'Someone',
                        last_name: 'Smith',
                        email: '<EMAIL>',
                        password: 'abc@123!',
                        password_confirmation: 'abc@123!',
                        invitation_token: '1234')

    user.invite!

    put :update, :params => { :user => { :password => '12345678', :password_confirmation => '12345678', :invitation_token => user.raw_invitation_token, :terms_of_services => '1' } }

    found_user = User.find_by_email(user.email)
    expect(response).to be_redirect
    expect(flash[:notice]).to_not be_empty
    expect(found_user.encrypted_password).to_not eq(user.encrypted_password)
  end

  it 'updates the invitation to accepted' do
    user = User.create!(first_name: 'Someone',
                        last_name: 'Smith',
                        email: '<EMAIL>',
                        password: 'abc@123!',
                        password_confirmation: 'abc@123!',
                        invitation_token: '1234')
    user.invite!

    put :update, :params => { :user => { :password => '12345678', :password_confirmation => '12345678', :invitation_token => user.raw_invitation_token, :terms_of_services => '1' } }

    found_user = User.find_by_email(user.email)
    expect(found_user.has_confirmed_email).to eq(true)
    expect(found_user.invitation_token).to be_nil
    expect(found_user.invitation_accepted_at).to_not be_nil
  end
end