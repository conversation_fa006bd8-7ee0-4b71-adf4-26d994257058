require 'rails_helper'
include CompanyUserHelper
include <PERSON>ginHelper

describe Users::SessionsController, type: :controller do
  create_company_and_user

  let!(:domain_validator) {
    allow_any_instance_of(DomainValidator).to receive(:validate).and_return(true)
  }

  before(:each) do
    @request.env['devise.mapping'] = Devise.mappings[:user]
    @request.host = "secure.#{Rails.application.credentials.root_domain}"
    allow(request.env['warden']).to receive(:authenticate!)
      .and_throw(:warden, scope: :user)
  end

  let(:company_two) { Company.create(name: "testco2", phone_number: "1234567890", subdomain: 'testco', default_logo_url: "default_logo_url") }

  let(:user_2_attributes) do
    {
      confirmed_at: Time.now,
      sign_in_count: 5,
      email: Faker::Internet.email,
      first_name: "<PERSON>",
      last_name: "<PERSON><PERSON>",
      password: "abc@12345"
    }
  end

  describe '#create' do
    context 'in any situation' do
      it 'redirects to login page with invalid email' do
        post :create, :params => { :user_params => { :email => "<EMAIL>" } }

        expect(response.status).to eq(404)
        expect(session["warden.user.user.key"].present?).to eq(false)
        expect(JSON.parse(response.body)["message"]).to eq("Sorry, no user exists.")
      end

      it 'does not create a session with local user credentials' do
        user_2 = User.create!(user_2_attributes)
        post :create, :params => { :user_params => { :email => user_2.email, :password => user_2.password } }

        expect(response.status).to eq(401)
        expect(session["warden.user.user.key"].present?).to eq(false)
        expect(JSON.parse(response.body)["message"]).to eq("Sorry, user does not have access.")
      end
    end

    context 'sign_in user' do
      it 'redirect the user with secure subdomain' do
        @request.host = "#{Rails.application.credentials.root_domain}"
        get :new

        expect(response).to redirect_to(new_user_session_url(host: "secure.#{Rails.application.credentials.root_domain}"))
      end
    end
  end

  describe '#destroy' do
    before do
      login_user
    end

    it 'ends the session' do
      delete :destroy

      expect(session["warden.user.user.key"].present?).to eq(false)
    end
  end
end
