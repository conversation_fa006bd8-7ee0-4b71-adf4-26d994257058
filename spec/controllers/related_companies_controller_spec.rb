require 'rails_helper'
include CompanyUserHelper
include <PERSON><PERSON>Helper

describe RelatedCompaniesController, type: :controller do
  create_company_and_user
  let(:user_one) { create(:company_user, company: company) }

  describe '#new' do
    context 'user without module permissions' do
      before do
        login_user(user_one.user)
        get :new, format: :json
      end
      it 'will not allow user to access the page' do
        expect(response.status).to eq(401)
        expect(JSON.parse(response.body)['message']).to eq("Sorry, you're not authorized to perform this action.")
      end
    end

    context 'user with module permissions' do
      before do
        login_user(company_user.user)
        get :new, format: :html
      end

      it 'will allow user to access the page' do
        expect(response.status).to eq(200)
      end
    end
  end

  describe '#index' do
    context 'when user has access to related companies' do
      before do
        login_user(company_user.user)
        allow(controller).to receive(:child_companies).and_return([company.id])
        allow(Company).to receive(:where).with(id: [company.id]).and_return(Company.all)
        get :index, format: :json
      end

      it 'returns a success response with company options' do
        expect(response.status).to eq(200)
        expect(JSON.parse(response.body)).to have_key("company_options")
      end
    end

    context 'when user does not have access to related companies' do
      before do
        login_user(user_one.user)
        get :index, format: :json
      end
      it 'returns an unauthorized message' do
        expect(response.status).to eq(401)
        expect(JSON.parse(response.body)['message']).to eq("Sorry, you're not authorized to perform this action.")
      end
    end
  end

  describe '#show' do
    context 'when company exists' do
      let!(:child_company) { create(:company, subdomain: "child-company") }

      before do
        allow(controller).to receive(:child_companies).and_return([child_company.id])
        get :show, params: { subdomain: "child-company" }, format: :json
      end

      it 'returns the company details' do
        expect(response.status).to eq(200)
        parsed_response = JSON.parse(response.body)
        expect(parsed_response).to include("id" => child_company.id, "name" => child_company.name)
      end
    end

    context 'when company does not exist' do
      before do
        get :show, params: { subdomain: 'nonexistent' }, format: :json
      end

      it 'returns not found message' do
        expect(response).to have_http_status(:not_found)
        expect(JSON.parse(response.body)).to include("message" => "Company not found.")
      end
    end

    context 'when company is not a child company' do
      let!(:non_child_company) { create(:company, subdomain: "non-child-company") }

      before do
        allow(controller).to receive(:child_companies).and_return([])
        get :show, params: { subdomain: "non-child-company" }, format: :json
      end

      it 'returns company is not a child company message' do
        expect(response).to have_http_status(:not_found)
        expect(JSON.parse(response.body)).to include("message" => "Company is not a child company.")
      end
    end
  end
end
