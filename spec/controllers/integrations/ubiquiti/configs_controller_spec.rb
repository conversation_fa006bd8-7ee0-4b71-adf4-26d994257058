require 'rails_helper'
include CompanyUserHelper
include LoginHelper

RSpec.describe Integrations::Ubiquiti::ConfigsController, type: :controller do
  create_company_and_user

  before do
    controller.send(:set_privilege)
  end

  describe "Post #create" do
    it "returns http success" do
      allow_any_instance_of(Integrations::Ubiquiti::FetchData).to receive(:authenticate?).and_return(true)
      post :integrate_ubiquiti, params: { ubiquiti_config: [{ selected: [{name: "default"}],
                                                             configs: { url: "demo.ubnt.com",
                                                             username: "superadmin",
                                                             password: "superadmin" }}], format: :json }
      result = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(result["message"]).to eq("Ubiquiti integrated successfully")
    end
  end

  describe "Post #resync" do
    it "returns http success" do
      allow_any_instance_of(Integrations::Ubiquiti::FetchData).to receive(:authenticate?).and_return(true)
      config = FactoryBot.create(:ubiquiti_config, company_id: company.id)
      resync_params = {
        id: config.id,
        integration_name: 'ubiquiti'
      }
      post :resync, params: resync_params
      expect(response).to have_http_status(:ok)
    end
  end

  describe "Delete #destroy" do
    it "Delete Ubiquiti config" do
      allow_any_instance_of(Integrations::Ubiquiti::FetchData).to receive(:authenticate?).and_return(true)
      config = create(:ubiquiti_config, company_id: company.id)
      delete :destroy, params: {  id: config.id }
      expect(response.status).to eq 200
    end
  end

  context "with a user with just one module" do
    before { login_user(simple_user) }
    let(:simple_user) { create(:company_user, company: company).user }

    it "returns 403 unauthorized" do
      allow_any_instance_of(Integrations::Ubiquiti::FetchData).to receive(:authenticate?).and_return(true)
      post :integrate_ubiquiti, params: { ubiquiti_config: [{ selected: [{name: "default"}],
                                                             configs: { url: "abc.ubnt.com",
                                                             username: "superadmin",
                                                             password: "superadmin" }}], format: :json }
      result = JSON.parse(response.body)
      expect(response).to have_http_status(:unauthorized)
      expect(result["message"]).to eq("Sorry, you're not authorized to perform this action.")
    end
  end
end
