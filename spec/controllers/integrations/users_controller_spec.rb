require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::UsersController, type: :controller do
  create_company_and_user

  let!(:integration)       { Integration.find_by(name: 'okta') }
  let!(:app)               { FactoryBot.create(:integrations_app, name: 'geunity', company_id: company.id)}
  let!(:manual_app)        { FactoryBot.create(:integrations_app, name: 'geunity app', company_id: company.id, source: "manual" )}
  let!(:app_source)        { FactoryBot.create(:integrations_app_source, app_id: app.id, integration_id: integration.id)}
  let!(:integrations_user) { FactoryBot.create(:integrations_user, 
                                               name: 'user name',
                                               email: '<EMAIL>',
                                               active: 1,
                                               last_login_at: Time.now,
                                               company_id: company.id )}
  let!(:user_source) {FactoryBot.create(:integrations_user_source, user_id: integrations_user.id, integration_id: integration.id)}
  let!(:app_user) {FactoryBot.create(:integrations_app_user, app_id: app.id, user_id: integrations_user.id)}

  let(:selected_user_params) do
    {
      name: company_user.name,
      email: company_user.email,
      root_id: company_user.id
    }
  end

  let(:saas_app_params) do
    {
      id: app.id,
      name: app.name
    }
  end

  describe "GET #get_users" do
    it "returns http success" do
      allow_any_instance_of(described_class).to receive(:current_company).and_return(company)
      get :index, params: { id: app.id }
      result = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(result["users"].first["name"]).to eq("user name")
    end
  end

  describe '#Create' do
    it 'will create integration user with company user' do
      post :create, params: { selected_user: selected_user_params, saas_app: saas_app_params }
      expect(response).to have_http_status(:ok)
    end

    it 'will not create integration user' do
      post :create, params: { selected_user: selected_user_params, saas_app: app }
      result = JSON.parse(response.body)
      expect(response).to have_http_status(:unprocessable_entity)
      expect(result['message']).to eq("Validation failed: App can't be blank")
    end
  end

  describe '#Destroy' do
    it 'will destroy the integration app user when app source is "manual"' do
      integ_user = company.integrations_users.find_or_create_by(name: company_user.name, 
                                                                email: company_user.email,
                                                                company_user_id: company_user.id)
      app_user = Integrations::AppUser.find_or_create_by(app_id: manual_app.id, user_id: integ_user.id)
      delete :destroy, params: { id: app_user.id }
      expect(response).to have_http_status(:ok)
      expect(Integrations::AppUser.exists?(app_user.id)).to be false
    end

    it 'will set company_user_id to nil when app source is not "manual"' do
      integ_user = company.integrations_users.find_or_create_by(name: company_user.name, 
                                                                email: company_user.email,
                                                                company_user_id: company_user.id)
      app_user = Integrations::AppUser.find_or_create_by(app_id: app.id, user_id: integ_user.id)
      delete :destroy, params: { id: app_user.id }
      expect(response).to have_http_status(:ok)
      expect(Integrations::AppUser.exists?(app_user.id)).to be true
      expect(integ_user.reload.company_user_id).to be_falsey
    end
  end
end
