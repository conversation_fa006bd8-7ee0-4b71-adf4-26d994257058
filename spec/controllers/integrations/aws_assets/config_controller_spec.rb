require "rails_helper"
include CompanyUserHelper

RSpec.describe Integrations::AwsAssets::ConfigsController, type: :controller do
  create_company_and_user

  before(:each) do
    # Assigning authorization for CanCan.
    @config_params = {
      access_key: "mock-access-key",
      secret_key: "mock-secret-key",
      regions: ["us-west-2"],
      company_id: company.id,
    }
    controller.send(:set_privilege)
  end

  describe "Post #create" do
    it "returns http success" do
      allow_any_instance_of(Integrations::AwsAssets::Config).to receive(:verify_keys).and_return(true)
      post :create, params: { aws_credentials: @config_params, format: :json }
      result = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(result["message"]).to eq("AWS integrated successfully")
    end
  end

  describe "Post #resync" do
    it "returns http success" do
      allow_any_instance_of(Integrations::AwsAssets::Config).to receive(:verify_keys).and_return(true)
      config = FactoryBot.create(:aws_assets_config, company_id: company.id)
      resync_params = {
        id: config.id,
        integration_name: 'aws_assets'
      }
      post :resync, params: resync_params
      expect(response).to have_http_status(:ok)
    end
  end

  describe 'Delete #destroy' do
    it 'return http success' do
      allow_any_instance_of(Integrations::AwsAssets::Config).to receive(:verify_keys).and_return(true)
      config = FactoryBot.create(:aws_assets_config, company_id: company.id)
      delete :destroy, params: { id: config.id }
      result = JSON.parse(response.body)
      expect(result['message']).to eq('Integration deleted successfully')
      expect(Integrations::AwsAssets::Config.where(company_id: company.id).count).to eq(0)
      expect(response.status).to eq(200)
    end
  end
end
