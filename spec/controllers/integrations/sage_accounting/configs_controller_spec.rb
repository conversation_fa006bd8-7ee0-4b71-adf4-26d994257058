require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::SageAccounting::ConfigsController, type: :controller do
  create_company_and_user

  before(:each) do
    @microsoft_service = Integrations::SageAccounting::FetchData.new(company.id)
  end

  describe "Get #consent_callback" do
    it "authenticate and save credentials and redirect_to sync accounts page" do
      token_detail = {parsed_response: {"token_type":"Bearer",
        "expires_in"=>300,
        "access_token"=>"mock-access-token",
        "refresh_token"=>"mock-refresh-token",
        "refresh_token_expires_in" => 2678400,
        "scope" => "full_access",
        "requested_by_id" => "53efe70c-d2ce-49a0-a7e9-4e00f99c7f28"},
        response_status: true,
      }

        allow_any_instance_of(Integrations::SageAccounting::FetchData).to receive(:token).and_return(token_detail)
        get :consent_callback , params: {"code"=>"US/b0afbaab-fd7a-4330-a4c3-17baf0ad23a6",
                                       "country"=>"US",
                                        state: company.id}
        expect(response).to redirect_to("http://#{company.subdomain}.#{Rails.application.credentials.root_domain}:3000/vendors/sync_accounts?financial_accounts=true")
        expect(Integrations::SageAccounting::Config.where(company_id: company.id).count).to eq(1)
    end
  end

  describe "Delete #destroy" do
    it "Delete sage accounting config" do
      config = FactoryBot.create(:sage_accounting_config, company_id: company.id)
      delete :destroy, params: { id: config.id}
      expect(response.status).to eq 200
    end
  end
end
