require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::OneLogin::ConfigsController, type: :controller do
  create_company_and_user

  describe "Post #create" do
    it "returns http success" do
      allow_any_instance_of(Integrations::OneLogin::FetchData).to receive(:authenticate?).and_return(true)
      post :create, params: {one_login_config: { client_id: "mock-client-id", client_secret: "lor84684783474389ean", region: 'us'}, format: :json }
      result = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(result["message"]).to eq("OneLogin integrated successfully")
    end
  end

  describe "Delete #destroy" do
    it "Delete onelogin config" do
      allow_any_instance_of(Integrations::OneLogin::FetchData).to receive(:authenticate?).and_return(true)
      config = FactoryBot.create(:one_login_config, company_id: company.id)
      delete :destroy, params: {  id: config.id}
      expect(response.status).to eq 200
    end
  end
end

