require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::Quickbooks::ConfigsController, type: :controller do
  create_company_and_user

  describe "Get #authenticate" do
    it "redirect_to microsoft authorization page" do
      get :authenticate
      client_id = Rails.application.credentials.quickbooks[:client_id]
      url = "#{Rails.application.credentials.domain_with_port}"
      expect(response).to redirect_to("https://appcenter.intuit.com/connect/oauth2?access_type=offline&client_id=#{client_id}&redirect_uri=#{url}/integrations/quickbooks/oauth_callback&response_type=code&scope=com.intuit.quickbooks.accounting&state=#{company.id}")
    end
  end

  describe "Get #oauth_callback" do
    it "authenticate and save credentials and redirect_to sync accounts page " do
      token_detail = {
        "access_token"=>"00D3i000000tXr3!AQcAQJWzM8.7dmAqyNDd8uHYvhX4rtoYE1.tZ_2z6h4xpKYUQ7j.Gt.vGkGidbaOnAc4cq_5BGU3BZxP_bcAI7bTwkxz8uIJ",
        "refresh_token"=>"mock_refresh_token",
        "scope"=>"refresh_token full",
        "token_type"=>"Bearer",
        "issued_at"=>"*************"}
      vendor_array = [
        {'Id'=> 1, 'DisplayName'=> 'Vendor Name'},
        {'Id'=> 2, 'DisplayName'=> 'Vendor Name two'}
      ]

      allow_any_instance_of(Integrations::Quickbooks::FetchData).to receive(:token).and_return(token_detail)
      allow_any_instance_of(Integrations::Quickbooks::FetchData).to receive(:get_vendors).and_return(vendor_array)
      get :oauth_callback , params: {:action=>"token",
                            :code=>"aPrx9YW5jCZKCNwG6uGQPDKtj8e6HAooTLk0oCrFa9a8lEquMwjuDsMYWIaZscRuW_n.NbZZNA==",
                            :controller=>"integrations/quickbooks/configs",
                            :realm_id=>12,
                            :state=>company.id}
      expect(Integrations::Quickbooks::Config.where(company_id: company.id).count ).to eq(1)
      expect(response).to redirect_to("http://#{company.subdomain}.#{Rails.application.credentials.root_domain}:3000/vendors/sync_accounts?financial_accounts=true&show_modal=quickbooks")
    end
  end

  describe "Get #destroy" do
    it "delete quickbooks config" do
      config = FactoryBot.create(:quickbooks_config, company_id: company.id)
      Sidekiq::Testing.inline! do
        delete :destroy, params: {id: config.id}
      end
      expect(Integrations::Quickbooks::Config.where(id: config.id, company_id: company.id).count).to eq(0)
    end
  end
end
