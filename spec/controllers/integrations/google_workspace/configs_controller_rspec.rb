require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::GoogleWorkspace::ConfigsController, type: :controller do
  create_company_and_user

  before(:each) do
    @credentials = OpenStruct.new({
      access_token: "abc123",
      refresh_token: "refresh123",
      expires_at: Time.now + 3600
    })
  end

  describe "GET #consent" do
    it "returns consent URL" do
      mock_service = instance_double(Integrations::GoogleWorkspace::FetchData)
      allow_any_instance_of(described_class).to receive(:google_workspace_service).and_return(mock_service)
      allow(mock_service).to receive(:get_credentials).and_return("http://example.com/auth")

      get :consent
      expect(response.status).to eq(200)
      expect(JSON.parse(response.body)["url"]).to eq("http://example.com/auth")
    end
  end

  describe "GET #callback" do
    it "stores credentials and redirects" do
      mock_service = instance_double(Integrations::GoogleWorkspace::FetchData)
      mock_authorizer = instance_double(Google::Auth::WebUserAuthorizer)

      allow_any_instance_of(described_class).to receive(:google_workspace_service).and_return(mock_service)
      allow(mock_service).to receive(:get_authorizer).and_return(mock_authorizer)
      allow(mock_authorizer).to receive(:get_credentials_from_code).and_return(@credentials)

      get :callback, params: { code: "mockcode", state: company.id }

      config = Integrations::GoogleWorkspace::Config.find_by(company_id: company.id)
      expect(config).not_to be_nil
      expect(config.token).to eq("abc123")
      expect(config.refresh_token).to eq("refresh123")
      expect(response).to redirect_to("http://#{Company.find(company.id).subdomain}.#{Rails.application.credentials.root_domain}:3000/managed_assets/discovery_tools/connectors")
    end
  end

  describe "DELETE #destroy" do
    it "destroys the integration config" do
      config = FactoryBot.create(:google_workspace_config, company_id: company.id)
      delete :destroy, params: { id: config.id }
      expect(response.status).to eq(200)
    end

    it "returns error if config not found" do
      delete :destroy, params: { id: -1 }
      expect(response.status).to eq(422)
    end
  end

  describe "POST #deactivate" do
    it "Deactivate google workspace config" do
      config = FactoryBot.create(:google_workspace_config, company_id: company.id)

      post :deactivate, params: { id: config.id }
      expect(response.status).to eq(200)
      expect(config.company_integration.status).to eq(false)
      expect(config.company_integration.active).to eq(false)
    end
  end

  describe "Post #resync" do
    it "returns http success" do
      config = FactoryBot.create(:google_workspace_config, company_id: company.id)
      resync_params = {
        id: config.id,
        integration_name: 'google_workspace'
      }
      post :resync, params: resync_params
      expect(response).to have_http_status(:ok)
    end
  end
end
