require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::AppsController, type: :controller do
  create_company_and_user

  let!(:integration)      { Integration.find_by(name: 'okta') }
  let!(:app)              { FactoryBot.create(:integrations_app, company_id: company.id, app_type: 0) }
  let!(:app2)             { FactoryBot.create(:integrations_app, company_id: company.id, app_type: 0, name: "New Name") }
  let!(:app_source)       { FactoryBot.create(:integrations_app_source, app_id: app.id, integration_id: integration.id) }
  let!(:integration_user) { FactoryBot.create(:integrations_user,
                                              name: 'ulfat iqbal',
                                              email: '<EMAIL>',
                                              active: 1,
                                              last_login_at: Time.now,
                                              company_id: company.id )}
  let!(:user_source)  { FactoryBot.create(:integrations_user_source, user_id: integration_user.id, integration_id: integration.id) }
  let!(:app_user)  { FactoryBot.create(:integrations_app_user, app_id: app.id, user_id: integration_user.id) }
  let!(:app_usage)  { FactoryBot.create(:app_usage, app_id: app.id, month: Time.now.at_beginning_of_month) }
  let!(:vendor) { create(:vendor, name: 'Microsoft') }
  let!(:app3) { create(:integrations_app, company_id: company.id, app_type: 0, name: "New manual app", status: 1, vendor_id: vendor.id ) }
  let(:metered_app_params) {
    { 
      "name"=>"Metered App",
      "status"=>"discovered",
      "app_type"=>"metered",
      "access_type"=>"email",
      "mfa_enabled"=>false,
      "source"=>"manual",
      "friendly_name"=>""
    }
  }

  let(:licensed_app_params) {
    { 
      "name"=>"Licensed App",
      "status"=>"discovered",
      "app_type"=>"metered",
      "total_users"=>20,
      "used"=>10,
      "access_type"=>"email",
      "mfa_enabled"=>false,
      "source"=>"manual",
      "friendly_name"=>""
    }
  }

  describe "GET #index" do
    it "returns http success" do
      allow_any_instance_of(described_class).to receive(:current_company).and_return(company)
      get :index, params: { group_by: 'apps' }
      result = JSON.parse(response.body)
      saas_app = result["saas_apps"].find { |saas_app| saas_app['name'].downcase == app.name.downcase }
      expect(response).to have_http_status(:ok)
      expect(saas_app).to be_present
      expect(saas_app['total_users']).to eq(1)
    end

    context "with linked vendor" do
      it 'will return saas apps with vendor detail' do
        allow_any_instance_of(described_class).to receive(:current_company).and_return(company)
        get :index, params: { group_by: 'vendors' }
        result = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(result['saas_apps'].first['vendor_id']).to be_present
        expect(result["saas_apps"].first["vendor_name"]).to eq(vendor.name)
        expect(result['saas_apps'].first['saas_apps'].first['name']).to eq(app3.name)
      end
    end
  end

  describe "PUT #bulk_update" do
    let(:vendor) { create(:vendor, company: company) }
    let(:product) { create(:product, company: company, vendor: vendor) }

    context "with multiple apps" do
      let(:app_params) {
        {
          integrations_apps: {
            apps_ids: [app.id, app2.id],
            status: 'linked',
            vendor_id: vendor.id,
            product_id: product.id,
          }
        }
      }

      it "returns http success" do
        put :bulk_update, params: app_params
        expect(response).to have_http_status(:ok)
      end

      it "updates" do
        put :bulk_update, params: app_params
        expected = Integrations::App.find(app.id)
        expect(expected.status).to eq('linked')
        expect(expected.vendor_id).to eq(vendor.id)
        expect(expected.product_id).to eq(product.id)

        expected2 = Integrations::App.find(app2.id)
        expect(expected.status).to eq('linked')
        expect(expected.vendor_id).to eq(vendor.id)
        expect(expected.product_id).to eq(product.id)
      end
    end

    context "with a single apps" do
      let(:app_params) {
        {
          integrations_apps: {
            apps_ids: [app.id],
            status: 'linked',
            vendor_id: vendor.id,
            product_id: product.id,
          }
        }
      }

      it "returns http success" do
        put :bulk_update, params: app_params
        expect(response).to have_http_status(:ok)
      end

      it "updates" do
        put :bulk_update, params: app_params
        expected = Integrations::App.find(app.id)
        expect(expected.status).to eq('linked')
        expect(expected.vendor_id).to eq(vendor.id)
        expect(expected.product_id).to eq(product.id)
      end
    end
  end

  describe '#create' do
    it 'will create new metered app and return success' do
      params = { app: metered_app_params }
      post :create, params: params
      metered_app = Integrations::App.find_by(name: 'Metered App')

      expect(response.status).to eq(200)
      expect(metered_app).to be_present
    end

    it 'will create new licensed app and return success' do
      params = { app: licensed_app_params }
      post :create, params: params
      licensed_app = Integrations::App.find_by(name: 'Licensed App')

      expect(response.status).to eq(200)
      expect(licensed_app).to be_present
    end
  end

  describe '#update' do
    it 'will update the app name' do
      params = { app: { id: app3.id, name: 'updated manual app' } }
      put :update, params: params
  
      expected = Integrations::App.find(app3.id)
      expect(response.status).to eq(200)
      expect(expected.name).to eq('updated manual app')
    end
  end
end
