require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::Okta::ConfigsController, type: :controller do
  create_company_and_user

  describe "Post #create" do
    it "returns http success" do
      allow_any_instance_of(Integrations::Okta::FetchData).to receive(:authenticate?).and_return(true)
      post :create, params: {okta_credentials: { token: "mock-token", name: "lorean"}, format: :json }
      result = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(result["message"]).to eq("Okta integrated successfully")
    end
  end

  describe "Delete #destroy" do
    it "Delete okta config" do
      allow_any_instance_of(Integrations::Okta::FetchData).to receive(:authenticate?).and_return(true)
      config = FactoryBot.create(:okta_config, company_id: company.id)
      delete :destroy, params: {  id: config.id}
      expect(response.status).to eq 200
    end
  end
end
