require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::SageIntacct::ConfigsController, type: :controller do
  create_company_and_user

  let!(:config) do
    FactoryBot.create(:sage_intacct_config, company_id: company.id)
  end

  let!(:tenant) { config.tenant }

  describe "Post #create" do
    it "returns sage intacct link success" do
      post :create , params: { :sage_intacct => {:tenant => tenant, :company_id => company.id } }
      result = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(result["message"]).to eq("Sage intacct linked successfully.")
    end
  end

  describe "Delete #destroy" do
    it "run deletion worker and delete sage Intacct config" do
      allow_any_instance_of(Integrations::SageIntacct::Config).to receive(:delete_hotglue_tenant).and_return(:res => 200)

      Sidekiq::Testing.inline! do
        delete :destroy, params: { id: config.id }
      end

      expect(Integrations::SageIntacct::Config.where(id: config.id, company_id: company.id).count).to eq(0)
      expect(response.body).to eq("{\"message\":\"Integration deleted successfully\"}")
      expect(response.status).to eq(200)
    end
  end
end
