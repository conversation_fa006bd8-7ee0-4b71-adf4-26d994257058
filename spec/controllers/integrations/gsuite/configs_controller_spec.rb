require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::Gsuite::ConfigsController, type: :controller do
  create_company_and_user

  before(:each) do
    @credentials = OpenStruct.new({
                    client_id: *********,
                    client_secret: "3453849",
                    access_token: "qwerty",
                    scope: "user.read",
                    refresh_token: "qwerty"})

    @empty_credentials = nil
    @authorizer = {
        CLIENT_ID: Rails.application.credentials.gsuite[:application_name],
        TOKEN_STORE: "@credentials",
        SCOPE: "@credentials"}
  end

  describe "Get #consume" do
    it "redirect_to vendor sync accounts page" do
      allow_any_instance_of(Google::Auth::UserAuthorizer).to receive(:get_credentials).and_return(@credentials)
      allow_any_instance_of(Google::Auth::UserAuthorizer).to receive(:get_and_store_credentials_from_code).and_return(@credentials)
      get :consume, params: {  code: "qwerty", state: company.id}
      expect(response).to redirect_to("http://#{Company.find(company.id).subdomain}.#{Rails.application.credentials.root_domain}:3000/vendors/sync_accounts?direct_integration=true")
      expect(Integrations::Gsuite::Config.all.count).to eq(1)
      gsuite = Integration.find_by(name: 'gsuite')
      expect(CompanyIntegration.where(integration_id: gsuite.id).count ).to eq(1)
    end
  end

  describe "Get #authorize" do
    it "if credential are  empty redirect_to authorization uri" do
      allow_any_instance_of(Google::Auth::UserAuthorizer).to receive(:get_credentials).and_return(@empty_credentials)
      get :authorize
      expect(response.status).to eq 302
    end
  end

  describe "Get #authorize" do
    it "if credential are not empty redirect_to authorization uri" do
      allow_any_instance_of(Google::Auth::UserAuthorizer).to receive(:get_credentials).and_return(@credentials)
      get :authorize
      expect(response).to redirect_to("http://#{Company.find(company.id).subdomain}.#{Rails.application.credentials.root_domain}:3000/vendors/sync_accounts")
    end
  end

  describe "Delete #destroy" do
    it "Delete gsuite config" do
      config = FactoryBot.create(:gsuite_config, company_id: company.id)
      delete :destroy, params: {  id: config.id}
      expect(response.status).to eq 200
    end
  end
end
