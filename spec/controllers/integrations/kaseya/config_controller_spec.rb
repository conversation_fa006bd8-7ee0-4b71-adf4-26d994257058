require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::Kaseya::ConfigsController, type: :controller do
  create_company_and_user

  before(:each) do
    FactoryBot.create(:kaseya_config, company: company)
    @config ||= Company.find(company.id).kaseya_config
    controller.send(:set_privilege)
  end

  describe 'Get #consent_callback' do
    token_detail = OpenStruct.new({ parsed_response: {
                                      'token_type':'Bearer',
                                      'expires_in':3600,
                                      'access_token':'eyJ0eXAiOiJKV',
                                      'refresh_token':'mock_refresh_token',
                                    },
                                    code: 200,
                                    success?: true,
                                    message: 'OK',
                                    header: 'OK',
                                    request: {},
                                  })

    consent_params = {
      'session_state'=>'da3d747e-cbe2-4fb7-9476-c93ed8d089e5',
      'controller'=>'integrations/kaseya/configs',
      'action'=>'consent_callback',
      'code'=>'7e-cbe2-4fb'
    }

    unauthorized_params = {
      'controller'=>'integrations/kaseya/configs',
      'action'=>'consent_callback',
      'error'=>'unauthorized_client'
    }

    it 'authenticates and saves credentials and redirects to sync accounts page' do
      allow(HTTParty).to receive(:post).and_return(token_detail)
      consent_params['state'] = company.id
      get :consent_callback , params: consent_params
      response.should redirect_to("http://#{company.subdomain}.localhost:3000/managed_assets/discovery_tools/connectors")
      expect(Integrations::Kaseya::Config.where(company_id: company.id).count).to eq(1)
    end

    it 'does not authenticate and fails create integration' do
      Integrations::Kaseya::Config.find_by(company_id: company.id).destroy
      allow(HTTParty).to receive(:get).and_return(unauthorized_params)
      get :consent_callback , params: unauthorized_params
      expect(Integrations::Kaseya::Config.where(company_id: company.id).count).to eq(0)
    end
  end

  describe "Post #resync" do
    it "returns http success" do
      resync_params = {
        id: @config.id,
        integration_name: 'kaseya'
      }
      post :resync, params: resync_params
      expect(response).to have_http_status(:ok)
    end
  end

  describe 'Delete #destroy' do
    it 'Deletes Kaseyta config' do
      delete :destroy, params: { id: @config.id }
      expect(response.status).to eq(200)
      expect(company.discovered_assets.joins(:asset_sources).where(asset_sources: { source: 'kaseya' }).count).to eq(0)
    end
  end

  describe 'Disable Kaseya' do
    it 'Disables Kaseya' do
      post :deactivate, params: { id: @config.id}
      expect(response.status).to eq(200)
      expect(@config.company_integration.active).to eq(false)
      expect(@config.company_integration.status).to eq(false)
    end
  end
end
