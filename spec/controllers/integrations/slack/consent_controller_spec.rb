require 'rails_helper'

describe Integrations::Slack::<PERSON>sentController, type: :controller do
  
  let(:company) { create(:company) }
  let(:workspace) { create(:workspace) }

  before do
    allow(controller).to receive(:current_company).and_return(company)
    allow(controller).to receive(:scoped_workspace).and_return(workspace)
  end

  describe '#build_authorization_uri' do
    let(:state) { { company_id: company.id, workspace_id: workspace.id }.to_json }
    let(:auth_uri_base) { "https://slack.com/oauth/v2/authorize?client_id=#{Rails.application.credentials.slack[:client_id]}" }

    context 'when feature access is pending' do
      before do
        allow(controller).to receive(:slack_feature_access_pending?).with(company.id).and_return(true)
      end

      it 'generates the correct authorization URI with limited scope' do
        limited_scope = 'chat:write,chat:write.public,commands,incoming-webhook,users:read,users:read.email'
        expected_uri = "#{auth_uri_base}&scope=#{limited_scope}&user_scope=&state=#{state}"

        actual_uri = controller.send(:build_authorization_uri)
        expect(actual_uri).to eq(expected_uri)
      end
    end

    context 'when feature access is not pending' do
      before do
        allow(controller).to receive(:slack_feature_access_pending?).with(company.id).and_return(false)
      end

      it 'generates the correct authorization URI with full scope' do
        full_scope = 'chat:write,chat:write.public,commands,incoming-webhook,users:read,users:read.email,channels:history,channels:manage,im:history'
        expected_uri = "#{auth_uri_base}&scope=#{full_scope}&user_scope=&state=#{state}"

        actual_uri = controller.send(:build_authorization_uri)
        expect(actual_uri).to eq(expected_uri)
      end
    end
  end
end
