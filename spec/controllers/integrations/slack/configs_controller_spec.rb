require 'rails_helper'
include CompanyUserHelper

describe Integrations::Slack::ConfigsController, type: :controller do
  create_company_and_user
  let(:company_2) { create(:company) }
  let(:config) { create(:slack_config, company: company) }

  before do
    @token_detail = { 'access_token'=>'mock_access_token',
                     'team'=>{ 'id'=>'W02KMSC771Q', 'name'=>'GoGenuity Help Desk' },
                     'authed_user'=>{ 'id'=>'U90KCS03R20' },
                     'incoming_webhook'=>{ 'channel_id'=>'F02VVKA0K8Q',
                                           'channel'=>'#helpdesk',
                                           'configuration_url'=>'https://gogenuity-help-desk.slack.com/services/72LR33SAQT',
                                           'url'=>'*********************************************************************************' }}
  end

  describe "#index" do
    it "will return all the company Slack integrations" do
      Integrations::Slack::Config.create!(team_id: @token_detail['team']['id'],
                                          channel_id: @token_detail['incoming_webhook']['channel_id'],
                                          company: company,
                                          workspace_id: company.default_workspace.id,
                                          team_name: @token_detail['team']['name'],
                                          channel_name: @token_detail['incoming_webhook']['channel'])
      get :index
      parsed_response = JSON.parse(response.body)
      expect(parsed_response.count).to eq(1)
      expect(parsed_response.first['team_name']).to eq(@token_detail['team']['name'])
      expect(parsed_response.first['channel_name']).to eq(@token_detail['incoming_webhook']['channel'])
    end
  end

  describe "#destroy" do
    it "will destroy company Slack integration" do
      slack_config = Integrations::Slack::Config.create!(
                       team_id: @token_detail['team']['id'],
                       channel_id: @token_detail['incoming_webhook']['channel_id'],
                       company: company,
                       workspace_id: company.default_workspace.id,
                       team_name: @token_detail['team']['name'],
                       channel_name: @token_detail['incoming_webhook']['channel'])

      expect_any_instance_of(Integrations::Slack::Client).to receive(:client).twice.and_return(nil)
      delete :destroy, params: { id: slack_config.id }
      parsed_response = JSON.parse(response.body)
      expect(parsed_response['message']).to eq('Integration deleted successfully')
      expect(response.status).to eq(200)
    end

    it "will raise error on company Slack integration deletion" do
      delete :destroy, params: { id: 1 }
      expect(response.status).to eq(422)
    end
  end

  describe "#create" do
    it "will authenticate and save credentials then redirect_to Slack connectors page" do
      allow_any_instance_of(Integrations::Slack::ConfigsController).to receive(:get_token_details).and_return(@token_detail)

      get :create , params: {
                              'code'=>'OAQABAAIAAAAP0wLlqdLVToOpA4kwzSnxGu6c1Erydyxmy_E6SpKwPa3zRHTfdxzsSUWmz4lB8fwg8u',
                              state: { company_id: company.id, workspace_id: company.default_workspace.id }.to_json
                            }
      response.should redirect_to("http://#{company.subdomain}.#{Rails.application.credentials.root_domain}:3000/help_tickets/settings/connectors/slack")
      expect(Integrations::Slack::Config.where(company_id: company.id, workspace_id: company.default_workspace.id).count).to eq(1)
    end

    it "will redirect_to Slack connectors page with flash error" do
      allow_any_instance_of(Integrations::Slack::ConfigsController).to receive(:get_token_details).and_return(@token_detail)

      Integrations::Slack::Config.create!(team_id: @token_detail['team']['id'],
                                          channel_id: @token_detail['incoming_webhook']['channel_id'],
                                          company: company_2,
                                          workspace_id: company_2.default_workspace.id)

      get :create , params: {'code'=>'OAQABAAIAAAAP0wLlqdLVToOpA4kwzSnxGu6c1Erydyxmy_E6SpKwPa3zRHTfdxzsSUWmz4lB8fwg8u',
                              state: { company_id: company.id, workspace_id: company.default_workspace.id }.to_json
                            }
      response.should redirect_to("http://#{company.subdomain}.#{Rails.application.credentials.root_domain}:3000/help_tickets/settings/connectors/slack?flash={\"error\":\"Sorry, this Slack channel is already integrated in #{company_2.name}\"}")
      expect(Integrations::Slack::Config.where(company_id: company_2.id).count).to eq(1)
    end
  end

  describe '#update' do
    it 'will deactivate the active integrated channel of Slack' do
      allow_any_instance_of(Integrations::Slack::Client).to receive(:send_message).and_return({ 'ok' => true })
      params = { 'id' => config.id }
      put :update, params: params

      config.reload
      expect(config.active).to eq(false)
    end
  end
end
