require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::Kandji::ConfigsController, type: :controller do
  create_company_and_user

  before do
    controller.send(:set_privilege)
  end

  describe 'Post #create' do
    it 'Creates the Kandji config' do
      allow_any_instance_of(Integrations::Kandji::FetchData).to receive(:authenticate?).and_return(true)
      post :create, params: { kandji_credentials: { api_url: 'https://sub_domain.clients.us-1.kandji.io', api_token: '09876543212345678900987654321', format: :json } }
      result = JSON.parse(response.body)
      config = Integrations::Kandji::Config.find_by(api_token: '09876543212345678900987654321')
      expect(config).to be_present
      expect(config.company_integration.status).to eq(true)
      expect(config.company_integration.active).to eq(true)
      expect(response).to have_http_status(:ok)
      expect(result['message']).to eq('Kandji integrated successfully')
    end
  end

  describe "Post #resync" do
    it "returns http success" do
      allow_any_instance_of(Integrations::Kandji::FetchData).to receive(:authenticate?).and_return(true)
      config = FactoryBot.create(:kandji_config, company_id: company.id)
      resync_params = {
        id: config.id,
        integration_name: 'kandji'
      }
      post :resync, params: resync_params
      expect(response).to have_http_status(:ok)
    end
  end

  describe 'Delete #destroy' do
    it 'Deletes Kandji config' do
      allow_any_instance_of(Integrations::Kandji::FetchData).to receive(:authenticate?).and_return(true)
      config = FactoryBot.create(:kandji_config, company: company)
      delete :destroy, params: {  id: config.id }
      result = JSON.parse(response.body)
      expect(Integrations::Kandji::Config.find_by(api_token: '09876543212345678900987654321')).to be_nil
      expect(response.status).to eq 200
      expect(result['message']).to eq('Integration deleted successfully')
    end
  end

  describe 'Disable Kandji' do
    it 'Disables Kandji' do
      allow_any_instance_of(Integrations::Kandji::FetchData).to receive(:authenticate?).and_return(true)
      config = FactoryBot.create(:kandji_config, company: company)
      post :deactivate, params: { id: config.id}
      expect(response.status).to eq(200)
      expect(config.company_integration.active).to eq(false)
      expect(config.company_integration.status).to eq(false)
    end
  end
end
