require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::Meraki::ConfigsController, type: :controller do
  create_company_and_user

  before do
    controller.send(:set_privilege)
  end

  describe "Post #create" do
    it "returns http success" do
      allow_any_instance_of(Integrations::Meraki::FetchData).to receive(:authenticate?).and_return(true)
      allow_any_instance_of(Integrations::Meraki::FetchData).to receive(:organizations).and_return([{ "id" => "622923", "name" => "Stratum Networks", "url" => "https://n235.meraki.com/o/KTMBqc/manage/organization/overview"}, { "id" => "695243192475320747", "name" => "Stratum-Genuity", "url" => "https://n235.meraki.com/o/l0-zbdRd/manage/organization/overview"}, { "id" => "695243192475320731", "name" => "LineDrive Unlimited", "url" => "https://n235.meraki.com/o/ClIrIbRd/manage/organization/overview"}, {"id"=>"695243192475320862", "name"=>"Duggan <PERSON>sch", "url"=>"https://n235.meraki.com/o/xvUyCcRd/manage/organization/overview"}])
      post :save_config, params: { meraki_config: { name: "Stratum Networks", token: "09876543212345678900987654321", save_cameras: false, sync_networks: "N_664280945037197269" }, config: {}, format: :json }
      result = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(result["message"]).to eq("Cisco Meraki integrated successfully")
      expect(company.meraki_config.sync_networks).to eq ("N_664280945037197269")
    end
  end

  describe "Put #customize" do
    it "returns http success" do
      allow_any_instance_of(Integrations::Meraki::FetchData).to receive(:authenticate?).and_return(true)
      allow_any_instance_of(Integrations::Meraki::FetchData).to receive(:organizations).and_return([{ "id" => "622923", "name" => "Stratum Networks", "url" => "https://n235.meraki.com/o/KTMBqc/manage/organization/overview"}, { "id" => "695243192475320747", "name" => "Stratum-Genuity", "url" => "https://n235.meraki.com/o/l0-zbdRd/manage/organization/overview"}, { "id" => "695243192475320731", "name" => "LineDrive Unlimited", "url" => "https://n235.meraki.com/o/ClIrIbRd/manage/organization/overview"}, {"id"=>"695243192475320862", "name"=>"Duggan Bertsch", "url"=>"https://n235.meraki.com/o/xvUyCcRd/manage/organization/overview"}])
      meraki_config = FactoryBot.create(:meraki_config, company_id: company.id)
      post :customize, params: { sync_by: "networks", selected_networks: "", deleted_networks: "N_664280945037197269", save_cameras: "false", save_sm_devices: "false", meraki_config: meraki_config.as_json}
      result = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(company.meraki_config.sync_networks).to eq ("")
    end
  end

  describe "Post #resync" do
    it "returns http success" do
      allow_any_instance_of(Integrations::Meraki::FetchData).to receive(:authenticate?).and_return(true)
      config = FactoryBot.create(:meraki_config, company_id: company.id)
      resync_params = {
        id: config.id,
        integration_name: 'meraki'
      }
      post :resync, params: resync_params
      expect(response).to have_http_status(:ok)
    end
  end

  describe "Delete #destroy" do
    it "Delete meraki config" do
      allow_any_instance_of(Integrations::Meraki::FetchData).to receive(:authenticate?).and_return(true)
      config = FactoryBot.create(:meraki_config, company_id: company.id)
      delete :destroy, params: {  id: config.id}
      expect(response.status).to eq 200
    end
  end
end
