require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::Expensify::ConfigsController, type: :controller do
  create_company_and_user

  let!(:config) do
    allow_any_instance_of(Integrations::Expensify::FetchData).to receive(:authenticate?).and_return(true)
    FactoryBot.create(:expensify_config, company_id: company.id)
  end

  let!(:user_secret) { SecureRandom.hex(32) }

  let!(:user_id) { user.id }

  describe "Post #create" do
    it "authenticate and save credentials and redirect_to sync accounts page " do
      post :create , params: { :expensify_credentials => {:user_id => user_id, :user_secret => user_secret, :company_id => company.id } }
      expect(Integrations::Expensify::Config.where(user_id: user_id, user_secret: user_secret, company_id: company.id).count ).to eq(1)
      expect(response.body).to eq("{\"message\":\"Expensify integrated successfully\"}")
      expect(response.status).to eq(200)
    end
  end

  describe "Delete #destroy" do
    it "delete expensify config" do
      Sidekiq::Testing.inline! do
        delete :destroy, params: {id: config.id}
      end
      expect(Integrations::Expensify::Config.where(id: config.id, company_id: company.id).count).to eq(0)
      expect(response.body).to eq("{\"message\":\"Integration deleted successfully\"}")
      expect(response.status).to eq(200)
    end
  end
end
