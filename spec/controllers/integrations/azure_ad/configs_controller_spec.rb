require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::AzureAd::ConfigsController, type: :controller do
  create_company_and_user
  let(:azure_ad_data) do
    {
      "groups" => [
        {"name" => "Gen1", "email" => "<EMAIL>", "desc" => "test1"},
        {"name" => "Gen2", "email" => "<EMAIL>", "desc" => "test2"},
        {"name" => "Ben3", "email" => "<EMAIL>", "desc" => "test2"},
        {"name" => "Ben4", "email" => "<EMAIL>", "desc" => "test4"}
      ]
    }
  end
  let(:config) { FactoryBot.create(:azure_ad_config, company_id: company.id, data: azure_ad_data) }

  describe "Get #consent_callback" do
    it "authenticate and save credentials and redirect_to sync accounts page" do
      token_detail = { "token_type"=>"Bearer",
        "scope"=>"offline_access User.Read.All",
        "expires_in"=>3600,
        "ext_expires_in"=>3600,
        "access_token"=>"mock_access_token",
        "refresh_token"=>"OAQABAAAAAAAP0wLlqdLVToOpA4kwzSnxiAMsfekyjT2BNGLdRqx53n2CUQ5k_C6oxO90HZoxNcoXTqYECpEeycr5oD7ojV7FqQ4bkTLlpt0QPX8AlEksXamxcVNr0jmhguXKABvZwlA4gCDQLCP7dWCWWCofnWsWDYZHpXn2NhqMo5DwjygySB18GR3UlwuvLGFZhke_agTePQe_xP3To1N7kSzSN4Nwd2psywRpLvAPjZOdoL253YvnxDUtZnNoS3omr6QoVOEGhotvGMEuWd9GryZfuGLxfblY6XoG-9ECOAEhKOcD8e2arULzYs4DkewviHYeYebl79WiOwyKY8FevW6o_Vn8uZbukTMsBi3_u7rirqTSmxGe15DVr9VNzR56TOrvk6uPwTQfAb_DpsiWdwv8MfmxXhyr9mPm4NsudSDNKRgX-tCIdyIhnKp9QrrDg9utdMktUCNIz-puWZHJkkGXH87R4-LAOPugLZcYcA4MQ6X-i54bDhuQo0FkjCvy_C4eIdxPabRohoL5IlkDQZtKwvGqgp23QxYcWCrOs-dJhwl546zh17MzcvtsKMALAr6oMAnLcjC-p9VQlp3KExzMqaK_8JFlhNEIyjCiBA66yn8G0gLoQBeTOo0nfz_Zr7d9UDyb5KWlhnHMgKl-6c40SqJuVR3EpKuF5GnIZ9S5NWAn8Qod9fUIL3iGcuskCH2ArbIEz0JLLPlPIUA1RHhYnGlZVy2blMFzYRjALoHOwUCf5FmIKTit5nXsAQA9-hx6nwnkGU78H2kT1N0uPKky6sk9d7J6HQnQiM9bweL7lT4uAEsMDouHIXIEwdWZ5FFiEbogAA",
        "id_token"=>"mock_id_token" }
      allow_any_instance_of(Integrations::AzureAd::FetchData).to receive(:token).and_return(token_detail)
      get :consent_callback , params: { "code"=>"OAQABAAIAAAAP0wLlqdLVToOpA4kwzSnxGu6c1Erydyxmy_E6SpKwPa3zRHTfdxzsSUWmz4lB8fwg8u-OAwjhG1xPYhw-qVJQ_7cYuSzvqhCWDfJ2DLPgi9gCTCyiPzMevbZmM1WgHWWOA9JqHYoCGeuGyBAtR_CfFVnGsJy1N3XWPYx1vVbvu5_MCDJxc66y2tJWTjO636AW8Agf8Euor7P8pzrvoWxU-f94eipUHNrf8y8MHYr3oIkc0B-q-moUAbBXNAUaTyslUoj7y60580sXk5Dy7Gn3AfYKjUPhNbojjJMjTqfwSGxsK8kdjhQLYeDtBN1r4_0JWsEF6_zybojmNcANKYrwUnE4V77lSgnXL9fsZ5y6L4Xxu2Ky1XBa3VZE-rElCCZUtSxaGNSMa-dIf8WnUisjC200SYiT2_UPhPV-7Z2Ikef7bJuQxKdumne5AAZb9GuFthKbpkzXKPiUi-MI4z0bGymmFagSvzvTlP_StMXBf8Z-p1U2xAYoX0Wur2q69i1RJ3k_uUXca90mjcxDbqlSU8FMDihORkqCMBZHtR0jSOlqIw8dcma8_4M__t4D0zQAMuAaqRhBmMy6_tJ9BADo3WXYE1e0x7GB5c-surDB1kbkUlC23OAuGGyCHaWdqH0GgXX10J2r39sNtR9MXSlU3ubKDi11LX7Wpug1qc4xZt3RnYNMeRR17_n5u9HzyKySk6QVQIQi2LrChqhzV2SJhURtV47tnkgMJSBhK_0PYWhVPBW89VMAMIpH7cB6jMvpWwlt7CGVNx-6NAA8RNBlypl0U_HULJcqhvi2t_nxRnU2AVCRqrZQ7YIc6Uxc5JImoBkPSquYY1oI713l5CBXIAA",
                                        state: company.id,
                                        "session_state"=>"da3d747e-cbe2-4fb7-9476-c93ed8d089e5",
                                        "controller"=>"integrations/azure_ad/configs",
                                        "action"=>"consent_callback" }
      expect(response).to redirect_to("http://#{company.subdomain}.#{Rails.application.credentials.root_domain}:3000/company/users/sync_and_download?show_azure_ad_groups_modal=true")
      expect(Integrations::AzureAd::Config.where(company_id: company.id).count).to eq(1)
    end
  end

  describe "Delete #destroy" do
    it "Delete azure ad config" do
      delete :destroy, params: { id: config.id }
      expect(response.status).to eq 200
    end
  end

  describe "Get #sync_azure_ad_data" do
    it 'when no search term is provided all 4 groups returned' do
      get :sync_azure_ad_data, params: { config_id: config.id, page: 0, format: :json }

      result = JSON.parse(response.body)  
      expect(response).to have_http_status(:success)
      expect(result["azure_ad"]["groups"].count).to eq(4)
    end
    it 'when search term "Gen" is provided 2 groups returned' do
      get :sync_azure_ad_data, params: { config_id: config.id, page: 0, searchTerm: 'Gen', format: :json }
    
      result = JSON.parse(response.body)  
      expect(response).to have_http_status(:success)
      expect(result["azure_ad"]["groups"].count { |group| group["name"].include?("Gen") }).to eq(2)
    end
  end 
end
