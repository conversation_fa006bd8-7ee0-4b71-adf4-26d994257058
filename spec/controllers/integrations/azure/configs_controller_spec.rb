require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::Azure::ConfigsController, type: :controller do
  create_company_and_user

  before(:each) do
    @azure_service = Integrations::Azure::FetchData.new(company.id)
  end

  describe "Get #consent_callback" do
    it "authenticate and save credentials and redirect_to sync accounts page" do
      token_detail={"token_type":"Bearer",
        "scope"=>"Directory.AccessAsUser.All Directory.Read.All Directory.ReadWrite.All email openid profile Reports.Read.All User.Read User.ReadBasic.All User.ReadWrite User.ReadWrite.All",
        "expires_in"=>3600,
        "ext_expires_in"=>3600,
        "access_token"=>"mock_access_token",
        "refresh_token"=>"OAQABAAAAAAAP0wLlqdLVTHIXIEwdWZ5FFiEbogAA",
        "id_token"=>"mock_id_token"}
      allow_any_instance_of(Integrations::Azure::FetchData).to receive(:token).and_return(token_detail)
      get :consent_callback , params: {"code"=>"OAQABAAIAAAAP0wLlqdLVToOpA4kwzSnxGu6c1Erydyxmy_E6SpKwPa3zRHTfdxzsSUWmz4lB8fwg8u",
                                        state: company.id,
                                        "session_state"=>"da3d747e-cbe2-4fb7-9476-c93ed8d089e5",
                                        "controller"=>"integrations/azure/configs",
                                        "action"=>"consent_callback"}
      #Using `should` from rspec-expectations' old `:should` syntax without explicitly enabling the syntax is deprecated.
      #Use the new `:expect` syntax or explicitly enable `:should` with `config.expect_with(:rspec) { |c| c.syntax = :should }` instead
      expect(response).to redirect_to("http://#{company.subdomain}.#{Rails.application.credentials[:vendors_connectors_path]}?direct_integration=true")
      expect(Integrations::Azure::Config.where(company_id: company.id).count).to eq(1)
    end
  end

  describe 'Delete #destroy' do
    it 'will remove the integration from company and return http success' do
      config = FactoryBot.create(:azure_config, company_id: company.id)
      Sidekiq::Testing.inline! do
        delete :destroy, params: { id: config.id }
      end
      result = JSON.parse(response.body)
      expect(result['message']).to eq('Integration deleted successfully')
      expect(Integrations::Azure::Config.where(company_id: company.id).count).to eq(0)
      expect(response.status).to eq(200)
    end
  end
end
