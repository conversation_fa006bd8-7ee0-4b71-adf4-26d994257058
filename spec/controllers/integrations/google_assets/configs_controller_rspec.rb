require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::GoogleAssets::ConfigsController, type: :controller do
  create_company_and_user

  describe "Create #create" do
    it "Create google assets config" do
      post :create, params: { projects: ["project-gogen"] }
      expect(response.status).to eq 200
    end
  end

  describe "Create #create" do
    it "Create google assets config with wrong data " do
      post :create, params: { projects: ["project-gogen", "project-gogen"] }
      expect(response.status).to eq 409
    end
  end

  describe "Show #show" do
    it "Show google assets config" do
      config = FactoryBot.create(:google_assets_config, company_id: company.id)
      get :show, params: { id: config.id }
      expect(JSON.parse(response.body)["config"]).to be_present
      expect(response.status).to eq 200
    end
  end

  describe "Post #resync" do
    it "returns http success" do
      config = FactoryBot.create(:google_assets_config, company_id: company.id)
      resync_params = {
        id: config.id,
        integration_name: 'google_assets'
      }
      post :resync, params: resync_params
      expect(response).to have_http_status(:ok)
    end
  end

  describe "Delete #destroy" do
    it "Delete google assets config" do
      config = FactoryBot.create(:google_assets_config, company_id: company.id)
      delete :destroy, params: { id: config.id }
      expect(response.status).to eq 200
    end
  end

  describe "Deactivate #deactivate" do
    it "Deactivate google assets config" do
      config = FactoryBot.create(:google_assets_config, company_id: company.id)
      post :deactivate, params: { id: config.id }
      expect(response.status).to eq 200
      expect(config.company_integration.status).to be false
      expect(config.company_integration.active).to be false
    end
  end
end
