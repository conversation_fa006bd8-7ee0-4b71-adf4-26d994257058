require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::Salesforce::ConfigsController, type: :controller do
  create_company_and_user

  before(:each) do
    @salesforce_service = Integrations::Salesforce::FetchData.new(company.id)
    @config = FactoryBot.create(:salesforce_config, company_id: company.id)
  end

  describe "Get #authenticate" do
    it "redirect_to microsoft authorization page" do
      get :authenticate
      client_id = Rails.application.credentials.salesforce[:client_id]
      redirect_url = "#{Rails.application.credentials.domain_with_port}/integrations/salesforce/authentication/callback"
      expect(response).to redirect_to("https://login.salesforce.com/services/oauth2/authorize?access_type=offline&client_id=#{client_id}&redirect_uri=#{redirect_url}&response_type=code&state=#{company.id}")
    end
  end

  describe "Get #token" do
    it "authenticate and save credentials and redirect_to sync accounts page " do
      token_detail = {
        "access_token"=>"00D3i000000tXr3!AQcAQJWzM8.7dmAqyNDd8uHYvhX4rtoYE1.tZ_2z6h4xpKYUQ7j.Gt.vGkGidbaOnAc4cq_5BGU3BZxP_bcAI7bTwkxz8uIJ",
        "refresh_token"=>"mock_refresh_token",
        "signature"=>"0o+RB2zLSbNGunlgKkJvkZds62oTQ1hnRkGmV7C+8Mc=",
        "scope"=>"refresh_token full",
        "id_token"=>"mock-id-token", "instance_url"=>"https://na112.salesforce.com", "id"=>"https://login.salesforce.com/id/00D3i000000tXr3EAE/0053i000001cfPMAAY",
        "token_type"=>"Bearer",
        "issued_at"=>"*************"}
      allow_any_instance_of(Integrations::Salesforce::FetchData).to receive(:token).and_return(token_detail)
      get :oauth_callback , params: {
        "code"=>"aPrx9YW5jCZKCNwG6uGQPDKtj8e6HAooTLk0oCrFa9a8lEquMwjuDsMYWIaZscRuW_n.NbZZNA==",
        "state"=>company.id,
        "controller"=>"integrations/salesforce/configs",
        "action"=>"token"}
      expect(response).to redirect_to("http://#{company.subdomain}.#{Rails.application.credentials.root_domain}:3000/vendors/sync_accounts?direct_integration=true")
      expect(Integrations::Salesforce::Config.where(company_id: company.id).count).to eq(1)
    end
  end

  describe "Get #destroy" do
    it "Delete salesforce config" do
      FactoryBot.create(:salesforce_app, company_id: company.id, config_id: @config.id)
      FactoryBot.create(:salesforce_user, company_id: company.id, config_id: @config.id)
      Sidekiq::Testing.inline! do
        delete :destroy, params: {id: @config.id}
      end
      expect(Integrations::Salesforce::Config.where(id: @config.id, company_id: company.id).count).to eq(0)
      expect(Integrations::Salesforce::App.where(id: @config.id, company_id: company.id).count).to eq(0)
      expect(Integrations::Salesforce::User.where(id: @config.id, company_id: company.id).count).to eq(0)
    end
  end
end
