require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::Netsuite::ConfigsController, type: :controller do
  create_company_and_user

  let!(:config) do
    FactoryBot.create(:netsuite_config, company_id: company.id)
  end

  let!(:tenant) { config.tenant }

  describe "Post #create" do
    it "returns http success" do
      post :create , params: { :netsuite => {:tenant => tenant, :company_id => company.id } }
      result = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(result["message"]).to eq("Netsuite linked successfully.")
    end
  end

  describe "Delete #destroy" do
    it "delete netsuite config" do
      allow_any_instance_of(Integrations::Netsuite::Config).to receive(:delete_hotglue_tenant).and_return(:res => 200)
      Sidekiq::Testing.inline! do
        delete :destroy, params: {id: config.id}
      end
      expect(Integrations::Netsuite::Config.where(id: config.id, company_id: company.id).count).to eq(0)
      expect(response.body).to eq("{\"message\":\"Integration deleted successfully\"}")
      expect(response.status).to eq(200)
    end
  end
end
