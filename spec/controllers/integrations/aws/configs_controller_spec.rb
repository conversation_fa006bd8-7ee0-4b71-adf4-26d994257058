require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::Aws::ConfigsController, type: :controller do
  create_company_and_user

  describe "Post #create" do
    it "returns http success" do
      allow_any_instance_of(Integrations::Aws::FetchData).to receive(:authenticate?).and_return([true, "success"])
      post :create, params: {aws_credentials: { access_key: "mock-access-key", secret_key: "mock-secret-key", region: "us-east-2"}, format: :json }
      result = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(result["message"]).to eq("AWS integrated successfully")
    end
  end

  describe 'Delete #destroy' do
    it 'returns http success' do
      allow_any_instance_of(Integrations::Aws::FetchData).to receive(:authenticate?).and_return([true, 'success'])
      config = FactoryBot.create(:aws_config, company_id: company.id)
      Sidekiq::Testing.inline! do
        delete :destroy, params: { id: config.id }
      end
      result = JSON.parse(response.body)
      expect(result['message']).to eq('Integration deleted successfully')
      expect(Integrations::Aws::Config.where(company_id: company.id).count).to eq(0)
      expect(response.status).to eq(200)
    end
  end
end
