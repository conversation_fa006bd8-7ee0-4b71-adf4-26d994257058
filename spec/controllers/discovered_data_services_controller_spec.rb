require 'rails_helper'
include CompanyUserHelper

RSpec.describe DiscoveredDataServicesController, type: :controller do
  create_company_and_user

  let!(:discovered_data_service) { create(:discovered_data_service, company_id: company.id) }

  describe "#index" do
    it "should return the discovered_data_service when number is blank" do
      get :index, format: :json

      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)["service"]).to be_present
      expect(JSON.parse(response.body)["status"]).to eq(true)
    end
  end

  describe "#create" do
    it "should create a new telecom service and provider" do
      post :create, params: { telecom_provider: { name: "RC<PERSON>", query: "234.234.234.325" }}, format: :json

      expect(response).to have_http_status(:ok)
      telcos = company.telecom_services
      expect(telcos.count).to eq(1)
      expect(telcos.first.name).to eq("RCN Service")
      expect(telcos.first.service_type).to eq("data_service")
      expect(telcos.first.telecom_provider).to be_present
      expect(telcos.first.telecom_provider.name).to eq("RCN")
    end
  end
end