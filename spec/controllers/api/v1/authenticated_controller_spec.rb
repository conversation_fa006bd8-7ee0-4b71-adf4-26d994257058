# frozen_string_literal: true

require 'rails_helper'

include CompanyUserHelper
include MobileLoginHelper

RSpec.describe Api::V1::AuthenticatedController, type: :controller do
  create_company_and_user

  controller do
    def index
    end
  end

  describe 'before_action callbacks', :skip_login do
    before do
      allow(controller).to receive(:current_user).and_return(user)
      allow(controller).to receive(:scoped_company).and_return(company)
      allow(controller).to receive(:scoped_company_user).and_return(company_user)
    end

    describe '#authenticate_request' do
      before do
        allow(controller).to receive(:verify_mfa).and_return(true)
        allow(controller).to receive(:ensure_company_agent).and_return(true)
        allow(controller).to receive(:ensure_confirmed_email).and_return(true)
        allow(controller).to receive(:ensure_free_trial_or_subscribed).and_return(true)
      end

      context 'when there is no current_user' do
        it 'returns unauthorized status with an error message' do
          allow(controller).to receive(:current_user) do
            controller.instance_variable_set(:@authorization_error, {:token=>["authenticate_request"]})
            nil
          end

          get :index

          expect(response).to have_http_status(:unauthorized)
          expect(JSON.parse(response.body)).to eq({ "error" => "Unable to authorize user", "error_type" => "authenticate_request" })
        end
      end

      context 'when there is a current_user' do
        it 'does not return unauthorized status' do
          get :index
          expect(response).not_to have_http_status(:unauthorized)
        end
      end
    end

    describe '#verify_mfa' do
      before do
        allow(controller).to receive(:authenticate_request).and_return(true)
        allow(controller).to receive(:ensure_company_agent).and_return(true)
        allow(controller).to receive(:ensure_confirmed_email).and_return(true)
        allow(controller).to receive(:ensure_free_trial_or_subscribed).and_return(true)
      end

      context 'when MFA is not enabled' do
        it 'does not return an error' do
          allow_any_instance_of(Company).to receive(:mfa_enabled).and_return(false)

          get :index
          expect(response).not_to have_http_status(:internal_server_error)
        end
      end

      context 'when MFA is enabled and verification is not required' do
        it 'does not return an error' do
          allow_any_instance_of(Company).to receive(:mfa_enabled).and_return(true)
          allow(controller).to receive(:mfa_verification_required?).and_return(false)

          get :index
          expect(response).not_to have_http_status(:internal_server_error)
        end
      end

      context 'when MFA is enabled and verification is required' do
        before do
          allow_any_instance_of(Company).to receive(:mfa_enabled).and_return(true)
          allow(controller).to receive(:mfa_verification_required?).and_return(true)
        end

        context 'when current_user is a super_admin' do
          it 'does not return an error' do
            user.update(super_admin: true)

            get :index
            expect(response).not_to have_http_status(:internal_server_error)
          end
        end

        context 'when verify_mfa' do
          it 'returns an error' do
            get :index

            expect(response).to have_http_status(:forbidden)
            expect(JSON.parse(response.body)).to eq({ "error" => "Please verify MFA", "error_type" => "verify_mfa" })
          end
        end
      end
    end

    describe '#ensure_free_trial_or_subscribed' do
      before do
        allow(controller).to receive(:verify_mfa).and_return(true)
        allow(controller).to receive(:authenticate_request).and_return(true)
        allow(controller).to receive(:ensure_company_agent).and_return(true)
        allow(controller).to receive(:ensure_confirmed_email).and_return(true)
      end

      context 'when scoped_company and current_user are both blank' do
        it 'does not return an error' do
          allow(controller).to receive(:current_user).and_return(nil)
          allow(controller).to receive(:scoped_company).and_return(nil)

          get :index
          expect(response).not_to have_http_status(:internal_server_error)
        end
      end

      context 'when current_user is super_admin' do
        it 'does not return an error' do
          user.update(super_admin: true)

          get :index
          expect(response).not_to have_http_status(:internal_server_error)
        end
      end

      context 'when scoped_company is not sample_company and has allow_access?' do
        it 'does not return an error' do
          allow_any_instance_of(Company).to receive(:allow_access?).and_return(true)

          get :index
          expect(response).not_to have_http_status(:internal_server_error)
        end
      end

      context 'when scoped_company is sample_company and current_user has_active_subscription' do
        before(:each) do
          allow_any_instance_of(Company).to receive(:is_sample_company?).and_return(true)
          allow_any_instance_of(User).to receive(:has_active_subscription?).and_return(true)
        end

        it 'does not return an error' do
          get :index
          expect(response).not_to have_http_status(:internal_server_error)
        end
      end

      context 'when ensure_free_trial_or_subscribed' do
        it 'does not return an error' do
          allow_any_instance_of(Company).to receive(:allow_access?).and_return(false)

          get :index

          expect(response).to have_http_status(:payment_required)
          expect(JSON.parse(response.body)).to eq({ "error" => "Please allow company access", "error_type" => "ensure_free_trial_or_subscribed" })
        end
      end
    end

    describe '#ensure_confirmed_email' do
      before do
        allow(controller).to receive(:verify_mfa).and_return(true)
        allow(controller).to receive(:authenticate_request).and_return(true)
        allow(controller).to receive(:ensure_company_agent).and_return(true)
        allow(controller).to receive(:ensure_free_trial_or_subscribed).and_return(true)
      end

      context 'when current_user is not present' do
        it 'does not return an error' do
          allow(controller).to receive(:current_user).and_return(nil)

          get :index
          expect(response).not_to have_http_status(:internal_server_error)
        end
      end

      context 'when current_user has confirmed email' do
        it 'does not return an error' do
          allow_any_instance_of(User).to receive(:has_confirmed_email?).and_return(true)

          get :index
          expect(response).not_to have_http_status(:internal_server_error)
        end
      end

      context 'when current_user created_at date is greater than unconfirmed_email_days_allowed' do
        it 'does not return an error' do
            allow_any_instance_of(User).to receive(:has_confirmed_email?).and_return(false)
          allow_any_instance_of(User).to receive(:created_at).and_return(4.to_i.days.ago)

          get :index
          expect(response).not_to have_http_status(:internal_server_error)
        end
      end

      context 'when ensure_confirmed_email' do
        it 'does not return an error' do
          allow_any_instance_of(User).to receive(:has_confirmed_email?).and_return(false)
          allow_any_instance_of(User).to receive(:created_at).and_return(6.to_i.days.ago)

          get :index

          expect(response).to have_http_status(:bad_request)
          expect(JSON.parse(response.body)).to eq({"error"=>"Please ensure confirmed email", "error_type"=>"ensure_confirmed_email"})
        end
      end
    end

    describe '#ensure_company_agent' do
      before do
        allow(controller).to receive(:verify_mfa).and_return(true)
        allow(controller).to receive(:authenticate_request).and_return(true)
        allow(controller).to receive(:ensure_confirmed_email).and_return(true)
        allow(controller).to receive(:ensure_free_trial_or_subscribed).and_return(true)
      end

      context 'when current_user is super_admin' do
        it 'does not return an error' do
          user.update(super_admin: true)

          get :index
          expect(response).not_to have_http_status(:internal_server_error)
        end
      end

      context 'when company_user is a member of Admin group' do
        it 'does not return an error' do
          get :index
          expect(response).not_to have_http_status(:internal_server_error)
        end
      end

      context 'when company_user is a member of Help Desk Agent group' do
        it 'does not return an error' do
          company.groups.find_by(name: 'Help Desk Agents').group_members.create(contributor: company_user.contributor)

          get :index
          expect(response).not_to have_http_status(:internal_server_error)
        end
      end

      context 'when ensure_company_agent' do
        it 'does not return an error' do
          company.groups.find_by(name: 'Admins').group_members.destroy_all

          get :index

          expect(response).to have_http_status(:unauthorized)
          expect(JSON.parse(response.body)).to eq({"error"=>"Not a company agent", "error_type"=>"ensure_company_agent"})
        end
      end
    end
  end

  describe '#current_user', :skip_login do
    before do
      allow(controller).to receive(:verify_mfa).and_return(true)
      allow(controller).to receive(:ensure_company_agent).and_return(true)
      allow(controller).to receive(:ensure_confirmed_email).and_return(true)
      allow(controller).to receive(:ensure_free_trial_or_subscribed).and_return(true)
    end

    context 'with correct request headers' do
      it 'does not return an error' do
        request.headers['Authorization'] = token_generator(user.id)

        get :index
        expect(response).not_to have_http_status(:internal_server_error)
      end
    end
  end

  describe '#mfa_verification_required?', :skip_login do
    let(:controller) { described_class.new }

    before do
      allow(controller).to receive(:current_user).and_return(user)
      allow(controller).to receive(:scoped_company_user).and_return(company_user)
    end

    context 'when device token is not present' do
      it 'returns nil' do
        request.headers['device-token'] = nil
        allow(controller).to receive(:request).and_return(request)

        expect(controller.send(:mfa_verification_required?)).to eq(nil)
      end
    end

    context 'when device token is present and mfa session is not verified' do
      let(:device_token) { 'abc123' }
      let(:user_device) { create(:user_device, token: device_token, user: user) }
      let(:mfa_session) { create(:mfa_session, company_user: company_user, user_device: user_device) }

      it 'returns true' do
        user_device
        mfa_session

        request.headers['device-token'] = device_token
        allow(controller).to receive(:request).and_return(request)
        allow(company_user).to receive(:mfa_enabled).and_return(true)

        expect(controller.send(:mfa_verification_required?)).to eq(true)
      end
    end

    context 'when mfa is not enabled for scoped company user' do
      let(:device_token) { 'abc123' }
      let(:user_device) { create(:user_device, token: device_token, user: user) }
      let(:mfa_session) { create(:mfa_session, company_user: company_user, user_device: user_device) }

      it 'returns false' do
        user_device
        mfa_session

        request.headers['device-token'] = device_token
        allow(controller).to receive(:request).and_return(request)
        allow_any_instance_of(CompanyUser).to receive(:mfa_enabled).and_return(false)

        expect(controller.send(:mfa_verification_required?)).to eq(false)
      end
    end
  end
end
