require 'rails_helper'

include CompanyUserHelper
include MobileLoginHelper

RSpec.describe Api::V1::UserDevicesController, type: :controller do
  let(:token) { "123456" }
  let(:valid_attributes) { { device_info: { token: token, os: 'iOS', device_type: 'iPhone', screen_size: '5.5' } } }

  create_company_and_user

  before do
    request.headers['Authorization'] = token_generator(user.id)
  end

  describe 'POST #create', :skip_login do
    context 'with valid params' do
      it 'creates a new user device' do
        expect { post :create, params: valid_attributes }.to change(UserDevice, :count).by(1)
      end

      it 'returns a success response with the device token' do
        post :create, params: valid_attributes

        expect(response).to have_http_status(:success)
        expect(JSON.parse(response.body)).to eq({ "fcm_device_token" => token })
      end
    end

    context 'with invalid params' do
      it 'returns a server error response' do
        allow_any_instance_of(UserDevice).to receive(:save).and_return(false)
        post :create, params: valid_attributes

        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe 'Update #update', :skip_login do
    let!(:user_device) { create(:user_device, user: user) }

    context 'when the device exists' do
      it 'update the device' do
        valid_update_params = { id: user_device.token, device_info: { app_version: "2.0.0" } }

        expect { put :update, params: valid_update_params }.to change {
          user_device.reload.app_version
        }.from(user_device.app_version).to('2.0.0')
      end

      it 'returns a internal_server_error response' do
        put :update, params: { id: "nonexistent_id", device_info: { app_version: "2.0.0" } }

        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe 'DELETE #destroy', :skip_login do
    let!(:user_device) { create(:user_device, user: user) }

    context 'when the device exists' do
      it 'destroys the device' do
        expect { delete :destroy, params: { id: user_device.token } }.to change(UserDevice, :count).by(-1)
      end

      it 'returns a no content response' do
        delete :destroy, params: { id: user_device.token }

        expect(response).to have_http_status(:ok)
      end
    end

    context 'when the device does not exist' do
      it 'returns a not found response' do
        delete :destroy, params: { id: 'nonexistent_id' }

        expect(response).to have_http_status(:not_found)
      end
    end
  end
end
