require 'rails_helper'
require 'support/aws_stub'

include HelpTicketHelper
include CompanyUserHelper
include MobileLoginHelper

describe Api::V1::TicketCommentsController, type: :controller do
  create_company_and_user

  before do
    request.headers['Authorization'] = token_generator(user.id)
    Aws.config[:stub_responses] = true
  end

  let(:company_user) { company.company_users.first }
  let(:author) { user.company_users.first }

  let(:help_ticket) {
    t = FactoryBot.create(:help_ticket, company: company, creator_id: company_user.id, workspace: company.workspaces.first)
    created_by = t.custom_form.custom_form_fields.find_by(name: 'created_by')
    value = CustomFormValue.create(module: t, custom_form_field: created_by, value_int: author.contributor_id)
    t
  }

  let(:comment_attributes) do
    {
      comment_body: "<div>This comment has some STYLE!!!</div>",
      help_ticket_id: help_ticket.id,
      private_flag: true,
      private_contributor_ids: [company_user.contributor_id],
      contributor_id: company_user.contributor_id
    }
  end

  let(:comment_attributes_with_attachment) do
    {
      comment_body: "<div>This comment has an attachment. <figure data-trix-attachment='{\"attachmentId\":99,\"contentType\":\"image/jpeg\",\"filename\":\"clement-h-95YRwf6CNw8-unsplash.jpg\",\"filesize\":2038885,\"height\":4000,\"target\":\"_blank\",\"url\":\"https://#{attachment_upload.attachment.url}\",\"width\":6000}' data-trix-content-type=\"image/jpeg\" data-trix-attributes='{\"caption\":\"The laptop in question\",\"presentation\":\"gallery\"}' class=\"attachment attachment--preview attachment--jpg\"><img src=\"https://s3.amazonaws.com/nulodgic-development/attachment_uploads/attachments/000/000/099/original/clement-h-95YRwf6CNw8-unsplash.jpg?1602872702\" width=\"1280\" height=\"853\"><figcaption class=\"attachment__caption small font-italic\">The laptop in question</figcaption></figure></div>",
      help_ticket_id: help_ticket.id,
      private_flag: true,
      private_contributor_ids: [company_user.contributor_id],
      contributor_id: company_user.contributor_id
    }
  end

  let(:comment_attributes_with_pdf_attachment) do
    {
      comment_body: "<div>This comment has a pdf attachment. <a href=#{attachment_upload2.attachment.url}>#{attachment_upload2.attachment_file_name}</a></div>",
      help_ticket_id: help_ticket.id,
      private_flag: true,
      private_contributor_ids: [company_user.contributor_id],
      contributor_id: company_user.contributor_id
    }
  end

  describe "#index", :skip_login do
    let!(:privilege) { Privilege.create(company_user: company_user, name: 'HelpTicket', permission_type: 'write')}
    let!(:help_ticket_comment1) { HelpTicketComment.create(help_ticket: help_ticket, contributor: company_user.contributor, comment_body: "<div>Comment</div>") }
    let!(:help_ticket_comment2) { HelpTicketComment.create(help_ticket: help_ticket, contributor: company_user.contributor, comment_body: "<div>Comment 2</div>") }

    context "within the context of a help ticket" do
      before do
        get :index, params: {
          page: 1,
          per_page: 10,
          ticket_id: help_ticket.id,
          privilege_name: 'HelpTicket',
          format: :json
        }
      end

      it "should return a 200 http status" do
        expect(response).to have_http_status(:ok)
      end

      it "should return the time spent" do
        data = JSON.parse(response.body)
        comment_data = data["comments"]

        expect(data).to be_present
        expect(data.length).to eq(3)

        expect(comment_data).to be_present
        expect(comment_data.length).to eq(2)

        expect(comment_data.pluck("id")).to include(help_ticket_comment1.id)
        expect(comment_data.pluck("id")).to include(help_ticket_comment2.id)
      end
    end
  end

  describe "#create", :skip_login do
    context "without time spent" do
      before do
        post :create, params: {
          ticket_id: help_ticket.id,
          privilege_name: 'HelpTicket',
          help_ticket_comment: comment_attributes,
          }, format: :json
      end

      it "returns an http status" do
        expect(response).to have_http_status(:ok)
      end

      it "creates a comment" do
        comment = HelpTicketComment.find_by(help_ticket_id: help_ticket.id)
        expect(comment).to be_present
      end

      it "creates an activity" do
        activity = HelpTicketActivity.find_by(help_ticket_id: help_ticket.id)
        expect(activity).to be_present
      end

      it "will check to add automatically current user to private users" do
        comment = HelpTicketComment.find_by(help_ticket_id: help_ticket.id, comment_body: "<div>This comment has some STYLE!!!</div>")
        expect(comment.private_flag).to eq(true)
        expect(comment.private_contributor_ids).to include(company_user.contributor_id.to_s)
      end
    end

    context "with time spent" do
      let(:started_at) { 3.days.ago }
      let(:comment_attributes_with_time_spent) do
        {
          comment_body: "<div>New Comment.</div>",
          help_ticket_id: help_ticket.id,
          private_flag: true,
          private_contributor_ids: [author.contributor_id],
          contributor_id: author.contributor_id,
          time_spent: {
            time_spent: '1h 30m',
            started_at: started_at.strftime("%Y-%m-%d"),
            company_user_id: company_user.id,
          }
        }
      end

      let(:comment) { HelpTicketComment.find_by(help_ticket_id: help_ticket.id, comment_body: "<div>New Comment.</div>") }

      before do
        post :create, params: {
          privilege_name: 'HelpTicket',
          help_ticket_id: help_ticket.id,
          help_ticket_comment: comment_attributes_with_time_spent,
        }, format: :json
      end

      it "returns an http status" do
        expect(response).to have_http_status(:ok)
      end

      it "creates an activity and a comment" do
        activity = HelpTicketActivity.find_by_activity_type('note')
        data = activity.data

        expect(comment).to be_present
        expect(activity).to be_present
        expect(activity).to be_present
        expect(activity.activity_type).to eq("note")
        expect(activity.help_ticket_id).to eq(help_ticket.id)
        expect(activity.data["previous_comment_body"]).to be_blank
        expect(activity.data["new_comment_body"]).to eq("<div>New Comment.</div>")
        expect(activity.data["help_ticket_comment_id"]).to eq(comment.id.to_s)
      end

      it "creates a time spent" do
        time_spent = TimeSpent.find_by(help_ticket_id: help_ticket.id)

        expect(TimeSpent.count).to eq(1)
        expect(time_spent.hours_spent).to eq(1)
        expect(time_spent.minutes_spent).to eq(30)
        expect(time_spent.started_at).to be_present
      end
    end

    context "with time spent and starting/ending times" do
      let(:single_time_spent_params) do
        {
          comment_body: "",
          help_ticket_id: help_ticket.id,
          contributor_id: author.contributor_id,
          time_spent: {
            start_time: '02:04',
            end_time: '04:08',
            time_spent: '2h 4m',
            started_at: Date.today,
            company_user_id: company_user.id
          }
        }
      end

      let(:double_time_spent_params) do
        {
          comment_body: "",
          help_ticket_id: help_ticket.id,
          contributor_id: author.contributor_id,
          time_spent: {
            start_time: '22:04',
            end_time: '02:06',
            time_spent: '4h 2m',
            started_at: Date.today,
            company_user_id: company_user.id
          }
        }
      end

      let(:multiple_time_spent_params) do
        {
          comment_body: "",
          help_ticket_id: help_ticket.id,
          contributor_id: author.contributor_id,
          time_spent: {
            start_time: '22:04',
            end_time: '05:00',
            time_spent: '30h 56m',
            started_at: Date.today,
            company_user_id: company_user.id
          }
        }
      end

      it "creates a single time spent entry" do
        post :create, params: { help_ticket_comment: single_time_spent_params, privilege_name: 'HelpTicket' }, format: :json

        time_spent = TimeSpent.find_by(help_ticket_id: help_ticket.id)
        expect(time_spent.time_spent_entries.count).to eq(1)
        expect(time_spent.time_spent_entries[0].hours_spent).to eq(2)
        expect(time_spent.time_spent_entries[0].minutes_spent).to eq(4)
      end

      it "creates 2 time spent entries because day changed between start and end times" do
        post :create, params: { help_ticket_comment: double_time_spent_params, privilege_name: 'HelpTicket' }, format: :json

        time_spent = TimeSpent.find_by(help_ticket_id: help_ticket.id)
        expect(time_spent.time_spent_entries.count).to eq(2)
        expect(time_spent.time_spent_entries[0].hours_spent).to eq(1)
        expect(time_spent.time_spent_entries[0].minutes_spent).to eq(56)
        expect((time_spent.time_spent_entries[0].started_at).to_date).to eq(Date.today)
        expect(time_spent.time_spent_entries[1].hours_spent).to eq(2)
        expect(time_spent.time_spent_entries[1].minutes_spent).to eq(6)
        expect((time_spent.time_spent_entries[1].started_at).to_date).to eq(Date.today + 1.days)
      end

      it "created 3 time spent entries by dividing the total time on the basis of days" do
        post :create, params: { help_ticket_comment: multiple_time_spent_params, privilege_name: 'HelpTicket' }, format: :json

        time_spent = TimeSpent.find_by(help_ticket_id: help_ticket.id)
        expect(time_spent.time_spent_entries.count).to eq(3)
        expect(time_spent.time_spent_entries[0].hours_spent).to eq(1)
        expect(time_spent.time_spent_entries[0].minutes_spent).to eq(56)
        expect((time_spent.time_spent_entries[0].started_at).to_date).to eq(Date.today)
        expect(time_spent.time_spent_entries[1].hours_spent).to eq(24)
        expect(time_spent.time_spent_entries[1].minutes_spent).to eq(0)
        expect((time_spent.time_spent_entries[1].started_at).to_date).to eq(Date.today + 1.days)
        expect(time_spent.time_spent_entries[2].hours_spent).to eq(5)
        expect(time_spent.time_spent_entries[2].minutes_spent).to eq(0)
        expect((time_spent.time_spent_entries[2].started_at).to_date).to eq(Date.today + 2.days)
      end
    end

    context "with an attachment" do
      CustomFormValue.skip_callback(:validation, :before, :set_guest_from_email)
      let!(:attachment_field) { help_ticket.custom_form.custom_form_fields.find_by(name: 'attachments') }
      let!(:attachment_custom_form_value) { help_ticket.custom_form_values.create( module_id: help_ticket.id, module_type: "HelpTicket", custom_form_field_id: attachment_field.id) }
      let(:custom_form_attachment) { create(:custom_form_attachment, attachment: attachment, attachment_file_name: "laptop.jpg", custom_form_value: attachment_custom_form_value, auto_add: true, company: help_ticket.company) }

      let(:attachment) { Rack::Test::UploadedFile.new('spec/data/laptop.jpg', 'image/jpeg') }
      let(:attachment_upload) { company.attachment_uploads.create(attachment: attachment, attachable_type: "HelpTicketComment") }
      let(:comment) { HelpTicketComment.where('help_ticket_id = ? AND comment_body ILIKE ?', help_ticket.id, '%This comment has an attachment.%').first }
      let(:attachment_upload) { company.attachment_uploads.create(attachment: attachment, attachable_type: "HelpTicketComment")}

      before do
        allow_any_instance_of(HelpDesk::SyncTicketAttachments).to receive(:sync_attachments).and_return(custom_form_attachment)
        post :create, params: {
          privilege_name: 'HelpTicket',
          help_ticket_id: help_ticket.id,
          attachment_ids: [attachment_upload.id],
          help_ticket_comment: comment_attributes_with_attachment,
        }, format: :json
      end

      it "returns an http status" do
        expect(response).to have_http_status(:ok)
      end

      it "creates a comment" do
        comment = HelpTicketComment.find_by(help_ticket_id: help_ticket.id)
        expect(comment).to be_present
      end

      it "creates an activity" do
        activity = HelpTicketActivity.find_by(help_ticket_id: help_ticket.id)
        expect(activity).to be_present
      end

      it "adds an attachment" do
        expect(comment.attachments).to be_present
        expect(comment.attachments.count).to eq(1)
      end

      it "associates the attachment to the comment" do
        expect(attachment_upload.reload.attachable_id).to eq(comment.id)
      end

      it "syncs the comment added attachment to custom form attachments" do
        form_attachment = CustomFormAttachment.find_by(attachment_file_name: attachment_upload.attachment.filename.to_s)
        expect(form_attachment.auto_add).to eq(true)
        expect(attachment_upload.reload.attachable.help_ticket_id).to eq(form_attachment.custom_form_value.module_id)
      end

      it "resizes the attachment HTML img attribute for the comment body" do
        # The change will be in the <img> attribute height and width dimensions which
        #   is currently set at a maximum of 600px while laptop.jpg has a width of 1280px
        doc = Nokogiri::HTML::DocumentFragment.parse <<-END
          #{comment.comment_body}
        END
        img = doc.at_css("img")

        expect(comment.comment_body.include?("data-trix-attachment")).to eq(true)
        expect(img.attributes['width'].value).to eq("100%")
        expect(img.attributes['height'].value).to eq("100%")
      end

      it "resizes the attachment HTML img attribute for the activity data" do
        # The change will be in the <img> attribute height and width dimensions which
        #   is currently set at a maximum of 300px while laptop.jpg has a width of 1280px
        activity = HelpTicketActivity.find_by(help_ticket_id: help_ticket.id, activity_type: 'note')
        doc = Nokogiri::HTML::DocumentFragment.parse <<-END
          #{activity.data["new_comment_body"]}
        END
        img = doc.at_css("img")

        expect(activity.data["new_comment_body"].include?("data-trix-attachment")).to eq(true)
        expect(img.attributes['width'].value).to eq("100%")
        expect(img.attributes['height'].value).to eq("100%")
      end
    end

    context "with a pdf attachment" do
      let!(:attachment_field2) { help_ticket.custom_form.custom_form_fields.find_by(name: 'attachments') }
      let!(:attachment_custom_form_value2) { help_ticket.custom_form_values.create( module_id: help_ticket.id, module_type: "HelpTicket", custom_form_field_id: attachment_field2.id) }
      let(:custom_form_attachment2) { create(:custom_form_attachment, company: help_ticket.company, attachment: attachment2, attachment_file_name: "laptop.jpg", custom_form_value: attachment_custom_form_value2, auto_add: true) }
      let(:attachment2) { Rack::Test::UploadedFile.new('spec/data/laptop.pdf', 'application/pdf') }
      let(:attachment_upload2) { company.attachment_uploads.create(attachment: attachment2, attachable_type: "HelpTicketComment") }
      let(:comment2) { HelpTicketComment.where('help_ticket_id = ? AND comment_body ILIKE ?', help_ticket.id, '%This comment has a pdf attachment.%').first }

      before do
        allow_any_instance_of(HelpDesk::SyncTicketAttachments).to receive(:sync_attachments).and_return(custom_form_attachment2)
        post :create, params: {
          privilege_name: 'HelpTicket',
          help_ticket_id: help_ticket.id,
          attachment_ids: [attachment_upload2.id],
          help_ticket_comment: comment_attributes_with_pdf_attachment,
        }, format: :json
      end

      it "allows pdf files to be uploaded successfully through comments" do
        attachment = AttachmentUpload.find_by(attachable_id: comment2.id)
        expect(attachment.attachment.content_type).to eq("application/pdf")
      end
    end
  end

  describe "#show", :skip_login do
    let(:help_ticket_comment) { HelpTicketComment.create(help_ticket: help_ticket, contributor: company_user.contributor, comment_body: "<div>Comment</div>") }

    let!(:subject) { get :show, params: {
        id: help_ticket_comment.id,
        ticket_id: help_ticket.id,
        privilege_name: 'HelpTicket',
      }
    }

    context "with good params" do
      it "will return the ticket comment" do
        parsed_response = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(parsed_response["id"]).to eq(help_ticket_comment.id)
        expect(parsed_response["comment_body"]).to eq(help_ticket_comment.comment_body)
      end
    end
  end

  describe "#destroy", :skip_login do
    context "with a bad id" do
      it "will return a 404" do
        delete :destroy, params: {
          id: 'THISISNOTANID',
          ticket_id: help_ticket.id,
          privilege_name: 'HelpTicket',
        }

        expect(response).to have_http_status(:not_found)
      end
    end

    context "with a good id" do
      let(:help_ticket_comment) { HelpTicketComment.create(help_ticket: help_ticket, contributor: company_user.contributor, comment_body: "<div>Comment</div>") }

      it "will remove the ticket comment" do
        delete :destroy, params: {
          ticket_id: help_ticket.id,
          id: help_ticket_comment.id,
          privilege_name: 'HelpTicket',
        }

        expect(response).to have_http_status(:ok)
        expect(HelpTicketComment.where(id: help_ticket_comment.id)).to be_blank
      end
    end
  end

  describe "#update", :skip_login do
    let(:original_comment) { "<div>Comment</div>" }
    let(:updated_comment) { "<div>Updated Comment</div>" }
    let(:help_ticket_comment) { HelpTicketComment.create(help_ticket: help_ticket, contributor: company_user.contributor, comment_body: original_comment) }

    context "edit comment" do
      before do
        params = {
          help_ticket_comment: {
            private_flag: true,
            comment_body: updated_comment,
            help_ticket_id: help_ticket.id,
            contributor_id: author.contributor_id,
            private_contributor_ids: [author.contributor_id],
          },
          ticket_id: help_ticket.id,
          id: help_ticket_comment.id,
          privilege_name: 'HelpTicket',
        }

        put :update, params: params
      end

      it "returns an http status" do
        expect(response).to have_http_status(:ok)
      end

      it "updates comment" do
        help_ticket_comment.reload
        expect(help_ticket_comment.comment_body).to eq("<div>Updated Comment</div>")
      end

      it "creates an activity" do
        activity = HelpTicketActivity.find_by_activity_type('note')
        data = activity.data

        expect(activity).to be_present
        expect(activity.activity_type).to eq("note")
        expect(activity.help_ticket_id).to eq(help_ticket.id)
        expect(activity.data["new_comment_body"]).to eq(updated_comment)
        expect(activity.data["previous_comment_body"]).to eq(original_comment)
        expect(activity.data["help_ticket_comment_id"]).to eq(help_ticket_comment.id.to_s)
      end
    end

    context "when an error occurs" do
      before do
        params = {
          help_ticket_comment: {
            private_flag: true,
            comment_body: updated_comment,
            help_ticket_id: help_ticket.id,
            contributor_id: author.contributor_id,
            private_contributor_ids: [author.contributor_id],
          },
          ticket_id: help_ticket.id,
          id: help_ticket_comment.id,
          privilege_name: 'HelpTicket',
        }

        allow_any_instance_of(HelpTicketComment).to receive(:save).and_return(false)

        put :update, params: params
      end

      it "returns a JSON error message with a 422 status code" do
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end
end
