require 'rails_helper'

include CompanyUserHelper
include MobileLoginHelper

describe Api::V1::WorkspaceSelectionsController, type: :controller do
  create_company_and_user

  before do
    request.headers['Authorization'] = token_generator(user.id)
  end

  describe 'GET #index', :skip_login do
    context 'when params[:company] is not present' do
      before { get :index }

      it 'returns an error response' do
        expect(response).to have_http_status(:not_found)
      end
    end

    context 'when params[:company] is present' do
      before { get :index, params: { "id" =>  workspace.id, "company" => { "id" => company.id } } }

      it 'returns a successful response' do
        expect(response).to have_http_status(:ok)
      end

      it 'returns the login response' do
        parsed_reponse = JSON.parse(response.body)

        expect(parsed_reponse["multiple"]).to eq(false)
        expect(parsed_reponse["current_company"]).to be_present
        expect(parsed_reponse["current_workspace"]).to be_present
        expect(parsed_reponse["company_user"]["id"]).to eq(company_user.id)
      end
    end
  end
end
