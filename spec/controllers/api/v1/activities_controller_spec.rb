require 'rails_helper'

include HelpTicketHelper
include CompanyUserHelper
include MobileLoginHelper

describe Api::V1::ActivitiesController, type: :controller do
  create_company_and_user
  let(:company_user) { company.company_users.first }

  before do
    request.headers['Authorization'] = token_generator(user.id)
  end

  let(:author) { user.company_users.first }

  describe "#index", :skip_login do
    let(:help_ticket) {
      ticket_params = {
        'Subject' => "Subject",
        'Created By' => company_user.contributor_id,
        'Assigned To' => [company_user.contributor_id],
        'Followers' => [company_user.contributor_id],
        'Impacted Devices' => [],
        'Status' => 'Open',
        'Priority' => "medium",
        'Description' => "The red robin sits in the tree.",
        company: company
      }
      create_ticket(ticket_params)
    }
    let!(:help_ticket_comment) { FactoryBot.create(:help_ticket_comment, help_ticket: help_ticket, private_flag: true) }
    let!(:help_ticket_activity) { FactoryBot.create(:help_ticket_activity, help_ticket: help_ticket, help_ticket_comment: help_ticket_comment, owner: company_user, data: {"help_ticket_comment_id"=>help_ticket_comment.id}) }

    context "with some data" do
      it "returns an http status" do
        get :index, params: {
          ticket_id: help_ticket.id,
          privilege_name: 'HelpTicket',
          format: :json
        }

        expect(response).to have_http_status(:ok)
      end

      it "returns company activities" do
        get :index, params: {
          "search_terms" => "",
          "per_page" => "30",
          "page" => "1",
          "ticket_id" => help_ticket.id,
          "privilege_name" => "HelpTicket",
          "controller" => "help_ticket_activities",
          "action" => "index",
          "format" => "json"
        }

        expect(response).to have_http_status(:ok)
        activities = JSON.parse(response.body)
        expect(activities.length).to eq(3)
      end

      it "returns activities" do
        get :index, params: {
          ticket_id: help_ticket.id,
          privilege_name: 'HelpTicket',
          format: :json
        }

        activities = JSON.parse(response.body)
        expect(activities).to be_present
        expect(activities['total']).to eq(1)
      end

      it "returns activity with private flag" do
        get :index, params: {
          ticket_id: help_ticket.id,
          privilege_name: 'HelpTicket',
          format: :json
        }

        activities = JSON.parse(response.body)
        expect(activities).to be_present
        ticket_activity = activities['help_ticket_activities'].find { |activity| activity['activity_type'] == 'note' }
        expect(ticket_activity['private_flag']).to eq(true)
      end
    end
  end
end
