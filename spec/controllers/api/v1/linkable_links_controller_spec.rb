require 'rails_helper'

include HelpTicketHelper
include CompanyUserHelper
include MobileLoginHelper

RSpec.describe Api::V1::LinkableLinksController, type: :controller do
  create_company_and_user

  before do
    request.headers['Authorization'] = token_generator(user.id)

    ticket_params = {
      company: company,
      'Status' => 'Open',
      'Priority' => "medium",
      'Subject' => "First one",
      'Impacted Devices' => [],
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Description' => "The red robin sits in the tree."
    }

    @help_ticket = create_ticket(ticket_params)
  end

  let!(:asset_1) { FactoryBot.create(:managed_asset, :laptop, company: company) }
  let!(:asset_2) { FactoryBot.create(:managed_asset, :laptop, company: company) }

  describe "#create" do
    it "add related item into help ticket" do
      post :create, params:  { source_id: @help_ticket.linkable.id, target_id: [asset_1.linkable.id, asset_2.linkable.id, company_user.linkable.id]}

      expect(response).to have_http_status(:ok)
      expect(asset_1.linkable.target_linkable_links.count).to eq(1)
      expect(asset_2.linkable.target_linkable_links.count).to eq(1)
      expect(company_user.linkable.target_linkable_links.count).to eq(1)
      expect(@help_ticket.linkable.source_linkable_links.count).to eq(3)
    end
  end
end
