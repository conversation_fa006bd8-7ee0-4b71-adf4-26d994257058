require 'rails_helper'

include HelpTicketHelper
include CompanyUserHelper
include MobileLoginHelper

describe Api::V1::TimeSpentsController, type: :controller do
  create_company_and_user

  before do
    request.headers['Authorization'] = token_generator(user.id)
  end

  let(:help_ticket) {
    ticket_params = {
      'Subject' => "First one",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      company: company
    }
    create_ticket(ticket_params)
  }

  let(:time_spent_attributes) do
    {
      hours_spent: "10",
      minutes_spent: "30",
      started_at: 2.days.ago,
    }
  end

  describe "#index", :skip_login do
    let!(:time_spent) { FactoryBot.create(:time_spent, company_user: company_user, help_ticket: help_ticket) }
    let!(:time_spent2) { FactoryBot.create(:time_spent, company_user: company_user, help_ticket: help_ticket) }
    let!(:privilege) { Privilege.create(company_user: company_user, name: 'HelpTicket', permission_type: 'write')}

    let!(:company_user_2) { FactoryBot.create(:company_user) }
    let!(:privilege) { Privilege.create(company_user: company_user_2, name: 'HelpTicket', permission_type: 'write')}
    let!(:time_spent3) { FactoryBot.create(:time_spent, company_user: company_user_2, help_ticket: help_ticket) }

    context "within the context of a help ticket" do
      before do
        company_user_2.destroy

        get :index, params: {
          page: 1,
          per_page: 10,
          ticket_id: help_ticket.id,
          privilege_name: 'HelpTicket',
          format: :json
        }
      end

      it "should return a 200 http status" do
        expect(response).to have_http_status(:ok)
      end

      it "should return the time spent" do
        data = JSON.parse(response.body)
        time_spent_data = data["time_spents"]

        expect(data).to be_present
        expect(data.length).to eq(4)
        expect(time_spent_data).to be_present
        expect(time_spent_data.pluck("id")).to include(time_spent.id)
        expect(time_spent_data.pluck("id")).to include(time_spent2.id)
        expect(data["total_time_spent"]).to eq(help_ticket.total_time_spent)
        expect(time_spent_data.pluck('hours_spent')).to include(time_spent.hours_spent)
        expect(time_spent_data.pluck('hours_spent')).to include(time_spent2.hours_spent)
        expect(time_spent_data.pluck('user_name')).to include(company_user.user.full_name)
        expect(time_spent_data.pluck('minutes_spent')).to include(time_spent.minutes_spent)
        expect(time_spent_data.pluck('minutes_spent')).to include(time_spent2.minutes_spent)

        started_at = time_spent_data.pluck('started_at').map{ |time| time.to_date}
        expect(started_at).to include(time_spent.started_at.to_date)
        expect(started_at).to include(time_spent2.started_at.to_date)
      end
    end
  end

  describe "#create", :skip_login do
    context "with valid params" do
      before do
        time_spent_attributes[:company_user_id] = company_user.id
      end
      let(:subject) { post :create, params: { ticket_id: help_ticket.id, privilege_name: 'HelpTicket', time_spent: time_spent_attributes, format: :json } }

      let(:time_spent) {
        ticket = HelpTicket.find_by(id: help_ticket.id)
        time_spents = ticket.time_spents
        expect(time_spents).to be_present
        expect(time_spents.length).to eq(1)
        time_spents.first
      }

      it "should return a 200 http status" do
        subject
        expect(response).to have_http_status(:ok)
      end

      it "creates a new time spent" do
        subject

        expect(time_spent.hours_spent).to eq(10)
        expect(time_spent.minutes_spent).to eq(30)
        expect(time_spent.started_at.to_date).to eq(time_spent_attributes[:started_at].to_date)
      end

      it "implicitly sets it to be the current user" do
        subject
        expect(time_spent.company_user_id).to eq(company_user.id)
      end

      it "should return a 422 http status" do
        allow_any_instance_of(TimeSpent).to receive(:save).and_return(false)

        subject
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe "#show", :skip_login do
    context "with valid params" do
      let(:time_spent) { FactoryBot.create(:time_spent, company_user: company_user, help_ticket: help_ticket) }

      let(:subject) { get :show, params: { id: time_spent.id, ticket_id: help_ticket.id, privilege_name: 'HelpTicket', format: :json } }

      it "should return a 200 http status" do
        subject
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe "#update", :skip_login do
    let(:time_spent) { FactoryBot.create(:time_spent, company_user: company_user, help_ticket: help_ticket) }

    context "with valid params" do
      let(:subject) { put :update, params: { id: time_spent.id, ticket_id: help_ticket.id, privilege_name: 'HelpTicket', time_spent: time_spent_attributes, format: :json } }

      it "returns a 200 http response" do
        subject
        expect(response).to have_http_status(:ok)
      end

      it "updates the existing ticket" do
        subject

        spent = TimeSpent.find_by(id: time_spent.id)
        expect(spent).to be_present
        expect(spent.hours_spent).to be_present
      end

      it "returns a 422 http response" do
        allow_any_instance_of(TimeSpent).to receive(:update).and_return(false)

        subject
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe "#destroy", :skip_login do
    let(:time_spent) { FactoryBot.create(:time_spent, company_user: company_user, help_ticket: help_ticket) }

    context "with valid params" do
      let(:subject) { delete :destroy, params: { id: time_spent.id, ticket_id: help_ticket.id, privilege_name: 'HelpTicket', format: :json } }

      it "returns a 200 http response" do
        subject
        expect(response).to have_http_status(:ok)
      end

      it "destroys the existing time entry" do
        subject

        spent = TimeSpent.find_by(id: time_spent.id)
        expect(spent).to be_blank
      end

      it "returns a 422 http response" do
        allow_any_instance_of(TimeSpent).to receive(:destroy).and_return(false)

        subject
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end
end
