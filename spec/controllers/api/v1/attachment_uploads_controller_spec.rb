require 'rails_helper'
require 'support/aws_stub'

include CompanyUserHelper
include MobileLoginHelper

describe Api::V1::AttachmentUploadsController, type: :controller do
  create_company_and_user

  before do
    request.headers['Authorization'] = token_generator(user.id)
    Aws.config[:stub_responses] = true
  end

  let(:ticket) { create(:help_ticket, company: company, workspace: workspace) }
  let(:attachment) { Rack::Test::UploadedFile.new('spec/data/laptop.jpg', 'image/jpg') }
  let(:ticket_attachment) { ticket.attachment_uploads.create(attachment: attachment, company_id: company.id) }

  describe "#create", :skip_login do
    context "with valid params" do
      subject { post :create, params: { attachable_type: "HelpTicket",  attachable_id: ticket.id,  attachment: attachment }, format: :json }

      it "creates a new attachment upload" do
        expect do
          subject
        end.to change(AttachmentUpload, :count).by(1)
      end

      it "returns a success response" do
        subject
        expect(response).to have_http_status(:ok)
      end
    end

    context 'with invalid params' do
      before(:each) do
        allow_any_instance_of(AttachmentUpload).to receive(:save).and_return(false)
        post :create, params: {}, format: :json
      end

      it 'does not create a new attachment upload' do
        expect do
          subject
        end.not_to change(AttachmentUpload, :count)
      end

      it 'returns an error response' do
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe "#destroy", :skip_login do
    context "with valid params" do
      subject { delete :destroy, params: { id: ticket_attachment.id }, format: :json }

      it "will return a success status" do
        subject
        expect(response).to have_http_status(:ok)
      end

      it "will have successfully attach the file" do
        subject
        expect(ticket.reload.attachment_uploads).to be_blank
      end
    end

    context "with invalid params" do
      it "will return a failure status" do
        delete :destroy, params: { id: 12 }, format: :json

        expect(response).to have_http_status(:not_found)
      end

      it "will returns an error response" do
        allow_any_instance_of(AttachmentUpload).to receive(:destroy).and_return(false)

        delete :destroy, params: { id: ticket_attachment.id }, format: :json

        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end
end
