require 'rails_helper'

include CompanyUserHelper
include MobileLoginHelper

RSpec.describe Api::V1::Options::StatusOptionsController, type: :controller do
  create_company_and_user

  before do
    request.headers['Authorization'] = token_generator(user.id)
  end

  describe "GET #index", :skip_login do
    let(:custom_form){ FactoryBot.create(:custom_form, company: company, form_name: 'Blank State Copy1',company_module: 'helpdesk') }
    let(:second_custom_form) { create(:custom_form, form_name: 'Blank State Copy2', company_module: 'helpdesk', company: company) }

    let!(:status) { create(:custom_form_field, custom_form: custom_form, options: "[{\"name\":\"Open\",\"color\":\"#2ECC71\"},{\"name\":\"In Progress\",\"color\":\"#5DADE2\"},{\"name\":\"Closed\",\"color\":\"#626567\"}]", field_attribute_type: CustomFormField.field_attribute_types[:status], label: 'Status', name: 'status') }
    let!(:status2) { create(:custom_form_field, custom_form: second_custom_form, options: "[{\"name\":\"Open\",\"color\":\"#2ECC71\"},{\"name\":\"In Progress\",\"color\":\"#5DADE2\"},{\"name\":\"Closed\",\"color\":\"#626567\"},{\"name\":\"Resolved\",\"color\":\"#FFD700\"},{\"name\":\"Not Started\",\"color\":\"#FFA500\"}]", field_attribute_type: CustomFormField.field_attribute_types[:status], label: 'Status', name: 'status') }

    it "returns a JSON response with the status options" do
      get :index

      options = JSON.parse(response.body)

      expect(response).to have_http_status(:ok)
      expect(response.body).to be_present
      expect(options).to be_present
      expect(options.length).to eq(5)
      expect(JSON.parse(response.body)).to eq([
        {"color" => "#2ECC71", "name" => "Open"},
        {"color" => "#5DADE2", "name" => "In Progress"},
        {"color" => "#626567", "name" => "Closed"},
        {"color" => "#FFD700", "name" => "Resolved"},
        {"color" => "#FFA500", "name" => "Not Started"}
      ])
    end
  end
end
