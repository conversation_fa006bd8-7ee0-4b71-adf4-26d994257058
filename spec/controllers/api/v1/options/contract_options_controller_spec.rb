require 'rails_helper'

include CompanyUserHelper
include MobileLoginHelper

RSpec.describe Api::V1::Options::ContractOptionsController, type: :controller do
  create_company_and_user

  before do
    FactoryBot.create(:contract, name: 'contract1', company: company)
    FactoryBot.create(:contract, name: 'contract2', company: company)

    request.headers['Authorization'] = token_generator(user.id)
  end

  describe "#index", :skip_login do
    context "with contracts created" do
      it "will return contracts" do
        get :index, format: :json

        options = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(response.body).to be_present
        expect(options).to be_present
        expect(options.length).to eq(2)
        expect(options.map{|o| o["name"]}.sort).to eq(%w{contract1 contract2})
      end
    end
  end
end
