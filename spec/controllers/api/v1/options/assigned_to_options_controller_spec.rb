require 'rails_helper'

include CompanyUserHelper
include MobileLoginHelper

RSpec.describe Api::V1::Options::AssignedToOptionsController, type: :controller do
  create_company_and_user

  before do
    request.headers['Authorization'] = token_generator(user.id)
  end

  describe "GET #index", :skip_login do
    let(:custom_form){ FactoryBot.create(:custom_form, company: company, form_name: 'Blank State Copy1',company_module: 'helpdesk') }
    let(:people_list_field) { create(:custom_form_field, custom_form: custom_form, field_attribute_type: CustomFormField.field_attribute_types[:people_list], label: 'Assigned To', name: 'assigned_to') }
    let!(:assigned_staff_value) { create(:custom_form_value,custom_form_field: people_list_field, value_int: company_user.contributor_id, custom_form: custom_form, module: assigned_ticket,  company: company) }
    let!(:assigned_ticket) { create(:help_ticket, custom_form: custom_form, company: company) }

    let(:custom_form1){ FactoryBot.create(:custom_form, company: company, form_name: 'Blank State Copy2',company_module: 'helpdesk') }
    let(:people_list_field1) { create(:custom_form_field, custom_form: custom_form1, field_attribute_type: CustomFormField.field_attribute_types[:people_list], label: 'Assigned To', name: 'assigned_to') }
    let!(:assigned_staff_value1) { create(:custom_form_value,custom_form_field: people_list_field1, value_int: company_user.contributor_id, custom_form: custom_form1, module: assigned_ticket,  company: company) }
    let!(:assigned_ticket1) { create(:help_ticket, custom_form: custom_form1, company: company) }

    it "returns a JSON response with the assigned to options" do
      get :index

      options_array = JSON.parse(response.body)["options"]
      options_count = JSON.parse(response.body)["count"]

      expect(options_count).to eq(1)
      expect(options_array).to eq([{ "id" => company_user.contributor_id, "name" => company_user.full_name, "entity" => 'CompanyMember' }])
    end
  end
end
