require 'rails_helper'

include HelpTicketHelper
include CompanyUserHelper
include MobileLoginHelper

RSpec.describe Api::V1::Options::CreatedByOptionsController, type: :controller do
  create_company_and_user

  before do
    request.headers['Authorization'] = token_generator(user.id)
  end

  describe "GET #index", :skip_login do
    let(:custom_form_1) { FactoryBot.create(:custom_form, company: company, form_name: 'Blank State Copy3', company_module: 'helpdesk') }
    let(:people_list_field) { create(:custom_form_field, custom_form: custom_form_1, field_attribute_type: CustomFormField.field_attribute_types[:people_list], label: 'Created By', name: 'created_by') }
    let!(:created_staff_value) { create(:custom_form_value, custom_form_field: people_list_field, value_int: company_user.contributor_id, custom_form: custom_form_1, module: assigned_ticket, company: company) }
    let(:assigned_ticket) { create(:help_ticket, custom_form: custom_form_1, company: company) }

    let(:custom_form_2) { FactoryBot.create(:custom_form, company: company, form_name: 'Blank State Copy4', company_module: 'helpdesk') }
    let(:people_list_field_2) { create(:custom_form_field, custom_form: custom_form_2, field_attribute_type: CustomFormField.field_attribute_types[:people_list], label: 'Created By', name: 'created_by') }
    let!(:created_staff_value_2) { create(:custom_form_value, custom_form_field: people_list_field_2, value_int: company_user.contributor_id, custom_form: custom_form_2, module: assigned_ticket_2, company: company) }
    let(:assigned_ticket_2) { create(:help_ticket, custom_form: custom_form_2, company: company) }

    context "when requesting options for custom_form_1 with limits 1" do
      it "returns a JSON response with the assigned to options" do
        get :index, params: { limit: 1, offset: 0 }

        options_array = JSON.parse(response.body)["options"]
        options_count = JSON.parse(response.body)["count"]

        expect(options_count).to eq(1)
        expect(options_array).to eq([{ "id" =>  company_user.contributor_id, "name" => company_user.full_name }])
      end
    end

    context "when requesting options for custom_form_2 with limits 10" do
      it "returns a JSON response with the assigned to options" do
        get :index, params: { limit: 10, offset: 0 }

        options_array = JSON.parse(response.body)["options"]
        options_count = JSON.parse(response.body)["count"]

        expect(options_count).to eq(1)
        expect(options_array).to eq([{ "id" =>  company_user.contributor_id, "name" => company_user.full_name }])
      end
    end
  end
end
