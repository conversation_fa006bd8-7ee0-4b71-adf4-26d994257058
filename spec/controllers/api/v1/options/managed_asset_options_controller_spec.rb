require 'rails_helper'

include CompanyUserHelper
include MobileLoginHelper

RSpec.describe Api::V1::Options::ManagedAssetOptionsController, type: :controller do
  create_company_and_user

  before do
    FactoryBot.create(:managed_asset, name: 'Asset1', company: company)
    FactoryBot.create(:managed_asset, name: 'Asset2', company: company)
    FactoryBot.create(:managed_asset, name: 'Asset3', company: company)
    FactoryBot.create(:managed_asset, name: 'Asset4', company: company)

    request.headers['Authorization'] = token_generator(user.id)
  end

  describe '#index', :skip_login do
    context 'with managed assets created' do
      it 'returns managed assets' do
        get :index, format: :json

        options = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(response.body).to be_present
        expect(options).to be_present
        expect(options.length).to eq(4)
        expect(options.map { |o| o['name'] }.sort).to eq(%w[Asset1 Asset2 Asset3 Asset4])
      end
    end
  end
end
