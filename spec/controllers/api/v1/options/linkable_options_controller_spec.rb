require 'rails_helper'

include CompanyUserHelper
include MobileLoginHelper

RSpec.describe Api::V1::Options::LinkableOptionsController, type: :controller do
  create_company_and_user

  before do
    request.headers['Authorization'] = token_generator(user.id)
  end

  describe "GET #index", :skip_login do
    it 'returns a successful response with filtered and paginated options' do
      get :index, params: { search: 'Everyone', limit: 5, offset: 0 }

      parsed_response = JSON.parse(response.body)

      expect(response).to have_http_status(:success)
      expect(parsed_response.size).to eq(1)

      parsed_response.each do |option|
        expect(option.keys).to contain_exactly('id', 'linkable_id', 'linkable_type', 'name', 'detail', 'image_url')
      end
    end

    it 'returns all options when no search, limit, or offset parameters are provided' do
      get :index

      parsed_response = JSON.parse(response.body)

      expect(response).to have_http_status(:success)
      expect(parsed_response.size).to eq(5)

      parsed_response.each do |option|
        expect(option.keys).to contain_exactly('id', 'linkable_id', 'linkable_type', 'name', 'detail', 'image_url')
      end
    end
  end
end
