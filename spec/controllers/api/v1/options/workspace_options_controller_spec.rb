# frozen_string_literal: true

require 'rails_helper'

include CompanyUserHelper
include MobileLoginHelper

RSpec.describe Api::V1::Options::WorkspaceOptionsController, type: :controller do
  create_company_and_user

  before do
    request.headers['Authorization'] = token_generator(user.id)
  end

  describe 'GET #options', :skip_login do
    context 'when there is a valid company_id parameter' do
      it 'returns the workspace options for the given company' do
        get :index, params: { company_id: company.id, format: :json }

        parsed_response = JSON.parse(response.body)

        expect(parsed_response.length).to eq(1)
        expect(response).to have_http_status(:ok)
      end
    end

    context 'when there is an invalid company_id parameter' do
      it 'raise an error' do
        expect {
          get :index, params: { company_id: 'invalid_id', format: :json }
        }.to raise_error(RuntimeError, 'Missing scoped company')
      end
    end
  end
end
