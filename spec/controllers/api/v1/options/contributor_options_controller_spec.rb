require 'rails_helper'

include GroupHelper
include CompanyUserHelper
include MobileLoginHelper

describe Api::V1::Options::ContributorOptionsController, type: :controller do
  create_company_and_user

  before do
    request.headers['Authorization'] = token_generator(user.id)
  end

  let!(:default_form) { company.custom_forms.helpdesk.first}
  let!(:first_last_asc_field) { FactoryBot.create(:custom_form_field, custom_form_id: default_form.id, field_attribute_type: 'people_list', label: 'User field 1', name: 'user_field_1', sort_list: "[\"Ascending\", \"First Name, Last Name\"]")}
  let!(:first_last_desc_field) { FactoryBot.create(:custom_form_field, custom_form_id: default_form.id, field_attribute_type: 'people_list', label: 'User field 1', name: 'user_field_2', sort_list: "[\"Descending\", \"First Name, Last Name\"]")}
  let!(:last_first_asc_field) { FactoryBot.create(:custom_form_field, custom_form_id: default_form.id, field_attribute_type: 'people_list', label: 'User field 1', name: 'user_field_3', sort_list: "[\"Ascending\", \"Last Name, First Name\"]")}
  let!(:last_first_desc_field) { FactoryBot.create(:custom_form_field, custom_form_id: default_form.id, field_attribute_type: 'people_list', label: 'User field 1', name: 'user_field_4', sort_list: "[\"Descending\", \"Last Name, First Name\"]")}

  let!(:first_user) {
    user = FactoryBot.create(:user, first_name: 'First', last_name: 'User', email: '<EMAIL>')
    user.skip_validations = true
    user.save!(validate: false)
    user
  }

  let!(:second_user) {
    user = FactoryBot.create(:user, first_name: 'User', last_name: 'Second', email: '<EMAIL>')
    user.skip_validations = true
    user.save!(validate: false)
    user
  }
  let!(:first_company_user) { company.company_users.create(user: first_user)&.contributor_id }
  let!(:second_company_user) { company.company_users.create(user: second_user)&.contributor_id }

  describe "#index", :skip_login do
    let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
    let!(:company_user) { create(:company_user, company: company, custom_form_id: custom_form.id) }
    let!(:company_user2) { create(:company_user, company: company, custom_form_id: custom_form.id) }

    before do
      company_user2.update_columns(archived_at: Time.zone.now)
    end

    context "when fetching all contributors" do
      let(:response) { get :index, params: { field_id: first_last_asc_field.id, format: :json } }

      it "should have a 200 status" do
        expect(response).to have_http_status(:ok)
      end

      it "should return contributors" do
        contributors = JSON.parse(response.body)
        expect(contributors.find{ |c| c["id"] == company_user.contributor_id }).to be_present
        expect(contributors.find{ |c| c["id"] == company_user2.contributor_id }).not_to be_present
      end
    end

    context "when fetching all contributors including archived" do
      let(:response) { get :index, params: { archived: "all" }, format: :json }

      it "should return contributors" do
        contributors = JSON.parse(response.body)
        expect(contributors.find{ |c| c["id"] == company_user.contributor_id }).to be_present
        expect(contributors.find{ |c| c["id"] == company_user2.contributor_id }).to be_present
      end
    end
  end

  describe "#index with params", :skip_login do
    let!(:company_user1) { create(:company_user, company: company) }
    let!(:company_user2) { create(:company_user, company: company) }
    let!(:group) {
      group_params = {
        name: 'Group',
        company_id: company.id,
        members: [ company_user1.contributor_id, company_user2.contributor_id ]
      }
      create_group(group_params)
    }

    context "when fetching specific group members" do
      let(:response) { get :index, params: { offset: "0", limit: "20", group_contributor_ids: [group.contributor_id], field_id: first_last_asc_field.id, } }

      it "should have a 200 status" do
        expect(response).to have_http_status(:ok)
      end

      it "should return contributors" do
        contributors = JSON.parse(response.body)
        expect(contributors.length).to eq(2)
      end
    end
  end

  describe "#index with field params", :skip_login do
    context "when fetching First name, Last name sort ascending" do
      let(:response) { get :index, params: { offset: "0", limit: "20", field_id: first_last_asc_field.id, includes: [first_company_user, second_company_user] } }

      it "should have a 200 status" do
        expect(response).to have_http_status(:ok)
      end

      it "should return contributors" do
        contributors = JSON.parse(response.body)
        expect(contributors.pluck("id")).to eq([first_company_user, second_company_user])
      end
    end

    context "when fetching First name, Last name sort descending" do
      let(:response) { get :index, params: { offset: "0", limit: "20", field_id: first_last_desc_field.id, includes: [first_company_user, second_company_user] } }

      it "should have a 200 status" do
        expect(response).to have_http_status(:ok)
      end

      it "should return contributors" do
        contributors = JSON.parse(response.body)
        expect(contributors.pluck("id")).to eq([second_company_user, first_company_user])
      end
    end

    context "when fetching Last name, First name sort ascending" do
      let(:response) { get :index, params: { offset: "0", limit: "20", field_id: last_first_asc_field.id, includes: [first_company_user, second_company_user] } }

      it "should have a 200 status" do
        expect(response).to have_http_status(:ok)
      end

      it "should return contributors" do
        contributors = JSON.parse(response.body)
        expect(contributors.pluck("id")).to eq([second_company_user, first_company_user])
      end
    end

    context "when fetching Last name, First name sort descending" do
      let(:response) { get :index, params: { offset: "0", limit: "20", field_id: last_first_desc_field.id, includes: [first_company_user, second_company_user] } }

      it "should have a 200 status" do
        expect(response).to have_http_status(:ok)
      end

      it "should return contributors" do
        contributors = JSON.parse(response.body)
        expect(contributors.pluck("id")).to eq([first_company_user, second_company_user])
      end
    end
  end
end
