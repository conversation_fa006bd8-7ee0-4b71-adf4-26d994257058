require 'rails_helper'

include CompanyUserHelper
include MobileLoginHelper

RSpec.describe Api::V1::Options::CustomFormOptionsController, type: :controller do
  create_company_and_user

  before do
    FactoryBot.create(:custom_form, company: company, form_name: 'Blank State Copy1',company_module: 'helpdesk')
    FactoryBot.create(:custom_form, company: company, form_name: 'Blank State Copy2',company_module: 'helpdesk')
    FactoryBot.create(:custom_form, company: company, form_name: 'Blank State Copy3',company_module: 'helpdesk')

    request.headers['Authorization'] = token_generator(user.id)
  end

  describe '#index', :skip_login do
    context 'with custom forms created' do
      it 'returns custom forms' do
        get :index, format: :json

        options = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(response.body).to be_present
        expect(options).to be_present
        expect(options.length).to eq(4)
        expect(options.map { |o| o['name'] }.sort).to eq(['Blank State Copy1', 'Blank State Copy2','Blank State Copy3','IT General Request'])
      end
    end
  end
end
