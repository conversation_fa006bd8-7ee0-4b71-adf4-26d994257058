# frozen_string_literal: true

require 'rails_helper'

include CompanyUserHelper
include MobileLoginHelper

RSpec.describe Api::V1::Options::LocationOptionsController, type: :controller do
  create_company_and_user

  before do
    request.headers['Authorization'] = token_generator(user.id)
  end

  describe 'GET #options', :skip_login do
    it 'returns the location options' do
      get :index, format: :json

      parsed_response = JSON.parse(response.body)

      expect(parsed_response.length).to eq(1)
      expect(response).to have_http_status(:ok)
      expect(parsed_response[0]["id"]).to eq(company.locations.first.id)
    end
  end
end
