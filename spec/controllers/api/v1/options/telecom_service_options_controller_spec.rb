require 'rails_helper'

include CompanyUserHelper
include MobileLoginHelper

RSpec.describe Api::V1::Options::TelecomServiceOptionsController, type: :controller do
  create_company_and_user

  let(:telecom_provider1) { FactoryBot.create(:telecom_provider, name: 'My Telecom Provider1', company: company) }
  let(:telecom_provider2) { FactoryBot.create(:telecom_provider, name: 'My Telecom Provider2', company: company) }
  let(:telecom_provider3) { FactoryBot.create(:telecom_provider, name: 'My Telecom Provider3', company: company) }
  let(:telecom_provider4) { FactoryBot.create(:telecom_provider, name: 'My Telecom Provider4', company: company) }

  before do
    FactoryBot.create(:telecom_service, name: 'telecom1', company: company, telecom_provider: telecom_provider1)
    FactoryBot.create(:telecom_service, name: 'telecom2', company: company, telecom_provider: telecom_provider2)
    FactoryBot.create(:telecom_service, name: 'telecom3', company: company, telecom_provider: telecom_provider3)
    FactoryBot.create(:telecom_service, name: 'telecom4', company: company, telecom_provider: telecom_provider4)

    request.headers['Authorization'] = token_generator(user.id)
  end

  describe '#index', :skip_login do
    context 'when there are telecom services created' do
      it 'returns a JSON response with the list of telecom services' do
        get :index, format: :json

        options = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(response.body).to be_present
        expect(options).to be_present
        expect(options.length).to eq(4)
        expect(options.map { |option| option['name'] }.sort).to eq(%w[telecom1 telecom2 telecom3 telecom4])
      end
    end
  end
end
