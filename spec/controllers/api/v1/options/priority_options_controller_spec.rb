require 'rails_helper'

include CompanyUserHelper
include MobileLoginHelper

RSpec.describe Api::V1::Options::PriorityOptionsController, type: :controller do
 create_company_and_user

  before do
   request.headers['Authorization'] = token_generator(user.id)
  end

  describe "GET #index", :skip_login do
    let(:custom_form){ FactoryBot.create(:custom_form, company: company, form_name: 'Blank State Copy1',company_module: 'helpdesk') }

    let!(:priority) { create(:custom_form_field, custom_form: custom_form, options: "[{\"name\":\"acceptable\",\"color\":\"#FFD700\"},{\"name\":\"danger\",\"color\":\"#FFA500\"},{\"name\":\"critical\",\"color\":\"#FFD700\"},{\"name\":\"normal\",\"color\":\"#FFA500\"}]", field_attribute_type: CustomFormField.field_attribute_types[:status], label: 'Priority', name: 'priority') }

    it "returns a JSON response with the priority options" do
      get :index

      options = JSON.parse(response.body)

      expect(response).to have_http_status(:ok)
      expect(response.body).to be_present
      expect(options).to be_present
      expect(options.length).to eq(7)
      expect(JSON.parse(response.body)).to eq([{"name"=>"low", "color"=>"#0d6efd"}, {"name"=>"medium", "color"=>"#fd7e14"}, {"name"=>"high", "color"=>"#e14144"}, {"name"=>"acceptable", "color"=>"#FFD700"}, {"name"=>"danger", "color"=>"#FFA500"}, {"name"=>"critical", "color"=>"#FFD700"}, {"name"=>"normal", "color"=>"#FFA500"}])
    end
  end
end
