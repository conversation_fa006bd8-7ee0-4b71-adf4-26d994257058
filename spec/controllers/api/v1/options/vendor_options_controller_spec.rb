# frozen_string_literal: true

require 'rails_helper'

include CompanyUserHelper
include MobileLoginHelper

describe Api::V1::Options::VendorOptionsController, type: :controller do
  create_company_and_user

  before do
    request.headers['Authorization'] = token_generator(user.id)
  end

  describe "GET #options", :skip_login do
    let!(:vendor) { FactoryBot.create(:vendor, name: 'AT&T', company: company) }
    let!(:vendor1) { FactoryBot.create(:vendor, name: 'Verizon', archived: true, company: company) }

    context "when fetching all company vendors" do
      it "should fetch all company vendors in alphabetical order" do
        get :index, params: { format: :json }

        vendors = JSON.parse(response.body)

        expect(vendors.length).to eq(1)
        expect(response).to have_http_status(:ok)
        expect(vendors.first['name']).to eq('AT&T')
      end
    end
  end
end
