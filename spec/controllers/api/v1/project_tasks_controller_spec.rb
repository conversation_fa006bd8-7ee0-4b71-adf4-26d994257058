require 'rails_helper'

include HelpTicketHelper
include CompanyUserHelper
include MobileLoginHelper

describe Api::V1::ProjectTasksController, type: :controller do
  create_company_and_user

  before do
    request.headers['Authorization'] = token_generator(user.id)
  end

  let!(:help_ticket) {
    ticket_params = {
      company: company,
      'Status' => 'Open',
      'Subject' => "Subject",
      'Priority' => "medium",
      'Impacted Devices' => [],
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Description' => "The red robin sits in the tree."
    }

    create_ticket(ticket_params)
  }

  describe "#index", :skip_login do
    let!(:privilege) { Privilege.create(company_user: company_user, name: 'HelpTicket', permission_type: 'write')}
    let!(:project_task1) { create(:project_task, company: company, help_ticket_id: help_ticket.id, task_assignees: []) }
    let!(:project_task2) { create(:project_task, company: company, help_ticket_id: help_ticket.id, task_assignees: [TaskAssignee.new(contributor: company_user.contributor)]) }

    context "within the context of a help ticket" do
      before do
        get :index, params: {
          page: 1,
          page_size: 10,
          ticket_id: help_ticket.id,
          privilege_name: 'HelpTicket',
          format: :json
        }
      end

      it "should return a 200 http status" do
        expect(response).to have_http_status(:ok)
      end

      it "should return the time spent" do
        data = JSON.parse(response.body)
        task_data = data["tasks"]

        expect(data).to be_present
        expect(data.length).to eq(4)

        expect(task_data).to be_present
        expect(task_data.length).to eq(2)

        expect(task_data.pluck("id")).to include(project_task1.id)
        expect(task_data.pluck("id")).to include(project_task2.id)
      end
    end
  end

  describe "#show", :skip_login do
    let(:project_task) { create(:project_task, company: company, help_ticket_id: help_ticket.id, task_assignees: [TaskAssignee.new(contributor: company_user.contributor)]) }

    let!(:subject) { get :show, params: {
        id: project_task.id,
        ticket_id: help_ticket.id,
        privilege_name: 'HelpTicket',
      }
    }

    context "with good params" do
      it "will return the project task" do
        parsed_response = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(parsed_response["id"]).to eq(project_task["id"])
        expect(parsed_response["description"]).to eq(project_task["description"])
      end
    end
  end

  describe "#create", :skip_login do
    let(:subject) { post :create, params: { project_task: project_task_params, ticket_id: help_ticket.id, privilege_name: 'HelpTicket', } }

    let!(:company_user1) { create(:company_user, company: company) }
    let!(:company_user2) { create(:company_user, company: company) }

    context "with assignees" do
      let(:new_project_task) {  ProjectTask.find_by(description: 'New Name') }
      let(:project_task_params) do
        {
          description: 'New Name',
          assignees: [
            {
              name: company_user1.full_name,
              id: company_user1.contributor_id,
            },
            {
              name: company_user2.full_name,
              id: company_user2.contributor_id,
            }
          ]
        }
      end

      before(:each) { subject }

      it "will create a new project task" do
        expect(response).to have_http_status(:ok)
        expect(new_project_task).to be_present
      end

      it "will create company users for project task" do
        expect(new_project_task.task_assignees.count).to eq(2)
      end

      it "will create a activity" do
        expect(new_project_task.help_ticket.help_ticket_activities.find_by_activity_type('create_task')).to be_present
      end
    end

    context "without assignees" do
      let(:new_project_task) {  ProjectTask.find_by(description: 'New Name') }
      let(:project_task_params) do
        {
          description: 'New Name',
          assignees: [],
        }
      end

      before(:each) { subject }

      it "will create a new project task" do
        expect(response).to have_http_status(:ok)
        expect(new_project_task).to be_present
      end

      it "will create company users for project task" do
        expect(new_project_task.task_assignees.count).to eq(0)
      end

      it "will create a activity" do
        expect(new_project_task.help_ticket.help_ticket_activities.find_by_activity_type('create_task')).to be_present
      end
    end

    context "with bad assignees" do
      let(:new_project_task) {  ProjectTask.find_by(description: 'New Name') }
      let(:project_task_params) do
        {
          description: 'New Name',
          assignees: [
            {
              name: nil,
              id: nil,
            }
          ]
        }
      end

      before(:each) { subject }

      it "will create a new project task" do
        expect(response).to have_http_status(:ok)
        expect(new_project_task).to be_present
      end

      it "will create company users for project task" do
        expect(new_project_task.task_assignees.count).to eq(0)
      end

      it "will create a activity" do
        expect(new_project_task.help_ticket.help_ticket_activities.find_by_activity_type('create_task')).to be_present
      end
    end

    context "when an error occurs" do
      let(:new_project_task) {  ProjectTask.find_by(description: 'New Name') }
      let(:project_task_params) do
        {
          description: 'New Name',
          assignees: [
            {
              name: nil,
              id: nil,
            }
          ]
        }
      end

      it "returns a JSON error message with a 422 status code" do
        allow_any_instance_of(HelpTickets::ProjectTaskService).to receive(:create_assignees).and_raise(StandardError)
        subject

        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe "#destroy", :skip_login do
    context "with a bad id" do
      it "will return a 404" do
        delete :destroy, params: {
          id: 'THISISNOTANID',
          ticket_id: help_ticket.id,
          privilege_name: 'HelpTicket',
        }

        expect(response).to have_http_status(:not_found)
      end
    end

    context "with a good id" do
      let!(:project_task1) { create(:project_task, company: company, help_ticket_id: help_ticket.id) }
      let!(:project_task2) { create(:project_task, company: company, help_ticket_id: help_ticket.id) }

      let(:project_params) { {
        id: project_task1.id,
        ticket_id: help_ticket.id,
        privilege_name: 'HelpTicket',
      } }

      it "will remove the project task" do
        delete :destroy, params: project_params

        parsed_response = JSON.parse(response.body)

        expect(parsed_response["total_count"]).to eq(1)
        expect(parsed_response["completed_count"]).to eq(0)
        expect(ProjectTask.where(id: project_task1.id)).to be_blank
      end

      it "will return a 200" do
        delete :destroy, params: project_params

        parsed_response = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(parsed_response["total_count"]).to eq(1)
        expect(parsed_response["completed_count"]).to eq(0)
      end

      it "will return a 422 if uanble to destroy task" do
        allow_any_instance_of(ProjectTask).to receive(:destroy).and_return(false)

        delete :destroy, params: project_params
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe "#update", :skip_login do
    let(:project_task) { create(:project_task, company: company, help_ticket_id: help_ticket.id, task_assignees: [TaskAssignee.new(contributor: company_user.contributor)]) }
    let(:project_task_params) {
      {
        id: project_task.id,
        description: 'New Name',
        assignees: [],
      }
    }

    let(:subject) { put :update, params: {
        id: project_task.id,
        ticket_id: help_ticket.id,
        privilege_name: 'HelpTicket',
        project_task: project_task_params,
      }
    }

    let(:subject1) { put :update, params: {
        id: project_task.id,
        is_completing: true,
        ticket_id: help_ticket.id,
        privilege_name: 'HelpTicket',
        project_task: project_task_params,
      }
    }

    context "with good params" do
      before(:each) { subject }

      it "will update project_task's attributes" do
        updated_task = ProjectTask.find(project_task.id)

        expect(response).to have_http_status(:ok)
        expect(updated_task.description).to eq("New Name")
        expect(updated_task.help_ticket.help_ticket_activities.find_by_activity_type('task_description')).to be_present
      end

      it "will create a activity" do
        updated_task = ProjectTask.find(project_task.id)
        expect(updated_task.help_ticket.help_ticket_activities.find_by_activity_type('task_description')).to be_present
      end
    end

    context "with good params having is_completing flag to true" do
      before(:each) { subject1 }

      it "will update project_task's attributes" do
        updated_task = ProjectTask.find(project_task.id)
        data = JSON.parse(response.body)
        total_count = data["total_count"]
        completed_count = data["completed_count"]

        expect(total_count).to eq(1)
        expect(completed_count).to eq(0)
        expect(response).to have_http_status(:ok)
        expect(updated_task.description).to eq("New Name")
        expect(updated_task.help_ticket.help_ticket_activities.find_by_activity_type('task_description')).to be_present
      end

      it "will create a activity" do
        updated_task = ProjectTask.find(project_task.id)
        expect(updated_task.help_ticket.help_ticket_activities.find_by_activity_type('task_description')).to be_present
      end
    end

    context "when an error occurs" do
      it "returns a JSON error message with a 422 status code" do
        allow_any_instance_of(HelpTickets::ProjectTaskService).to receive(:create_assignees).and_raise(StandardError)
        subject

        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe "#project_task_options", :skip_login do
    let(:project_task) { create(:project_task, company: company, help_ticket_id: help_ticket.id) }
    let(:project_task_options_response) { JSON.parse(response.body)["task_options"] }

    before do
      get :project_task_options, params: {
        ticket_id: help_ticket.id,
        id: project_task.id,
        privilege_name: 'HelpTicket',
        format: :json
      }
    end

    context "with good params" do
      it "should return a 200 http status" do
        expect(response).to have_http_status(:ok)
      end

      it "should return the task options" do
        expect(project_task_options_response).to be_present
      end
    end
  end
end
