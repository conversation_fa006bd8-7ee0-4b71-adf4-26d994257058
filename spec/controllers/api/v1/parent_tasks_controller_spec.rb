require 'rails_helper'
include CompanyUserHelper
include MobileLoginHelper

RSpec.describe Api::V1::ParentTasksController, type: :controller do
  create_company_and_user

  before do
    request.headers['Authorization'] = token_generator(user.id)
  end

  let(:help_ticket) { FactoryBot.create(:help_ticket, company: company, workspace: company.workspaces.first ) }
  let(:parent) { FactoryBot.create(:project_task, help_ticket: help_ticket) }
  let(:child) { FactoryBot.create(:project_task, help_ticket: help_ticket) }

  describe "#create", :skip_login do
    it "create super task" do
      sub_task_ids =  [ child.id ]
      params = {
        privilege_name: 'HelpTicket',
        "parent_task_id" => parent.id,
        "ticket_id" => help_ticket.id,
        "sub_task_array" => sub_task_ids,
      }
      post :create, params: params
      expect(help_ticket.project_tasks.where(parent_project_task_id: parent.id).count).to eq(1)
    end
  end

  describe "#destroy", :skip_login do
    it "unembed task" do
      child.update_columns(parent_project_task_id: parent.id )
      params = {
        "id" => child.id,
        privilege_name: 'HelpTicket',
        "ticket_id" => help_ticket.id,
      }
      delete :destroy, params: params

      expect(ProjectTask.where(parent_project_task_id: parent.id)).to be_blank
    end
  end
end
