require 'rails_helper'

include CompanyUserHelper
include MobileLoginHelper

RSpec.describe Api::V1::CategoriesController, type: :controller do
  create_company_and_user
  let(:workspace) { company.workspaces.first }

  before do
    request.headers['Authorization'] = token_generator(user.id)
  end

  describe "#index", :skip_login do
    it "returns a list of all company categories" do
      get :index

      parsed_response = JSON.parse(response.body)

      expect(parsed_response.size).to eq(19)
      expect(response).to have_http_status(:ok)
    end

    it "returns a list of all company categories with some new categories" do
      category_1 = Category.create(name: "Category 1", company_id: company.id, workspace_id: workspace.id)
      category_2 = Category.create(name: "Category 2", company_id: company.id, workspace_id: workspace.id)

      get :index

      parsed_response = JSON.parse(response.body)

      expect(parsed_response.size).to eq(21)
      expect(response).to have_http_status(:ok)
    end
  end
end
