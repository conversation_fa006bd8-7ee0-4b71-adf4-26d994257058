require 'rails_helper'

RSpec.describe Api::V1::ForgotPasswordsController, type: :controller do
  describe 'POST #send_reset_password_code' do
    context 'when user exists' do
      let(:user) { create(:user) }

      before do
        allow(User).to receive(:find_by).and_return(user)
        allow_any_instance_of(ForgotPasswordsService).to receive(:send_reset_password_code).and_return({ success: true })
      end

      it 'returns a 200 status code' do
        post :send_reset_password_code, params: { email: '<EMAIL>' }
        expect(response).to have_http_status(:ok)
      end

      it 'returns an empty JSON response' do
        post :send_reset_password_code, params: { email: '<EMAIL>' }
        expect(response.body).to eq('{}')
      end
    end

    context 'when user does not exist' do
      before do
        allow(User).to receive(:find_by).and_return(nil)
      end

      it 'returns a 404 status code' do
        post :send_reset_password_code, params: { email: '<EMAIL>' }
        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns a JSON response with the error message' do
        post :send_reset_password_code, params: { email: '<EMAIL>' }
        expect(response.body).to eq({ message: 'User does not exist.' }.to_json)
      end
    end

    context 'when there is an error sending the email' do
      let(:user) { create(:user) }

      before do
        allow(User).to receive(:find_by).and_return(user)
        allow_any_instance_of(ForgotPasswordsService).to receive(:send_reset_password_code).and_return({ error: 'An error occurred' })
      end

      it 'returns a 422 status code' do
        post :send_reset_password_code, params: { email: '<EMAIL>' }
        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns a JSON response with the error message' do
        post :send_reset_password_code, params: { email: '<EMAIL>' }
        expect(response.body).to eq({ message: 'An error occurred' }.to_json)
      end
    end
  end

  describe 'POST #update_password' do
    context 'when user exists' do
      let(:user) { create(:user) }
      let(:params) { { user: { email: '<EMAIL>' } } }

      before do
        allow(User).to receive(:find_by).and_return(user)
        allow_any_instance_of(ForgotPasswordsService).to receive(:update_password).and_return({ success: true })
      end

      it 'returns a 200 status code' do
        post :update_password, params: params
        expect(response).to have_http_status(:ok)
      end

      it 'returns an empty JSON response' do
        post :update_password, params: params
        expect(response.body).to eq('{}')
      end
    end

    context 'when user does not exist' do
      before do
        allow(User).to receive(:find_by).and_return(nil)
      end

      it 'returns a 404 status code' do
        post :update_password, params: { user: { email: '<EMAIL>' } }
        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns a JSON response with the error message' do
        post :update_password, params: { user: { email: '<EMAIL>' } }
        expect(response.body).to eq({ message: 'User does not exist.' }.to_json)
      end
    end

    context 'when there is an error updating the password' do
      let(:user) { create(:user) }
      let(:params) { { user: { email: '<EMAIL>' } } }

      before do
        allow(User).to receive(:find_by).and_return(user)
        allow_any_instance_of(ForgotPasswordsService).to receive(:update_password).and_return({ error: 'An error occurred' })
      end

      it 'returns a 422 status code' do
        post :update_password, params: params
        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns a JSON response with the error message' do
        post :update_password, params: params
        expect(response.body).to eq({ message: 'An error occurred' }.to_json)
      end
    end
  end
end
