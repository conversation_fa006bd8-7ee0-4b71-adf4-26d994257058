require 'rails_helper'

include HelpTicketHelper
include CompanyUserHelper
include MobileLoginHelper

describe Api::V1::AbbreviatedTicketsController, type: :controller do
  create_company_and_user

  before do
    request.headers['Authorization'] = token_generator(user.id)
  end

  let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
  let(:company_user) { company.company_users.first }
  let!(:company_user2) { create(:company_user, company: company_user.company, custom_form_id: custom_form.id) }
  let(:asset) { FactoryBot.create(:managed_asset, company: company) }
  let(:contract) { FactoryBot.create(:contract, name: 'contract1', company: company) }
  let(:vendor) { FactoryBot.create(:vendor, company_id: company.id, category_id: company.categories.first.id, name: "Microsoft", url: "www.microsoft.com") }

  let(:author) { user.company_users.first }

  let(:help_ticket_attributes) do
    {
      creator_id: author.id,
      subject: "Test Ticket",
      email: user.email,
      help_paragraph: "Help Me",
      company_id: company.id,
      priority: "low",
      ticket_status_id: TicketStatus.find_by(name: 'Open', company_id: nil).id,
      links_array: {
        "0" => { "id" => asset.id, "linkable_id" => asset.linkable_id, "name" => asset.name, "type" => "ManagedAsset" },
        "1" => { "id" => contract.id, "linkable_id" => contract.linkable_id, "name" => contract.name, "type" => "Vendor" },
        "2" => { "id" => vendor.id, "linkable_id" => vendor.linkable_id, "name" => vendor.name, "type" => "Contract" }
      },
      assigned_contributors: {
        '0' => {
          "id" => CompanyUser.first.contributor_id
        },
        "1" => {
          "id" => CompanyUser.last.contributor_id
        }
      },
      impacted_contributors: {
        "0" => {
          "id" => CompanyUser.first.contributor_id
        },
        "1" => {
          "id" => CompanyUser.last.contributor_id
        }
      }
    }
  end

  let(:help_ticket_attributes_with_agent) do
    {
      creator_id: author.id,
      subject: "Test Ticket 2",
      email: user.email,
      help_paragraph: "Help Me",
      company_id: company.id,
      priority: "low",
      ticket_status_id: TicketStatus.find_by(name: 'Open', company_id: nil).id,
      assigned_contributors: {
        "0" => {
          id: company_user.contributor_id,
          name: company_user.full_name,
        },
      },
      impacted_contributors: {
        "0" => {
          "id" => CompanyUser.first.contributor_id
        },
        "1" => {
          "id" => CompanyUser.last.contributor_id
        },
      }
    }
  end

  describe "#index", :skip_login do
    let!(:user) { create(:user, email: "<EMAIL>") }
    let(:admins) { company.groups.find_by(name: 'Admins') }

    let!(:company_user) {
      user.company_users << build(:company_user, user: user, company: company)
      admins.group_members << GroupMember.new(contributor: user.company_users.first.contributor)
      user.company_users.first
    }

    let!(:privilege) {
      p = Privilege.create(company_user: company_user, name: 'HelpTicket', permission_type: 'read')
      ExpandedPrivileges::Populate.new(company_user.contributor).call
      p
    }

    context "with a more complete ticket" do
      let!(:help_ticket) {
        ticket_params = {
          'Subject' => "First one",
          'Created By' => company_user.contributor_id,
          'Assigned To' => [company_user.contributor_id],
          'Impacted Devices' => [asset.id],
          'Status' => 'Open',
          'Priority' => "medium",
          'Description' => "The red robin sits in the tree.",
          workspace: company.workspaces.first
        }
        create_ticket(ticket_params)
      }
      let!(:help_ticket_2) {
        ticket_params = {
          'Subject' => "Second but merged One",
          'Created By' => company_user.contributor_id,
          'Assigned To' => [company_user.contributor_id],
          'Impacted Devices' => [asset.id],
          'Status' => 'Open',
          'Priority' => "medium",
          'Description' => "The red robin sits in the tree.",
          workspace: company.workspaces.first
        }
        ticket = create_ticket(ticket_params)
        ticket.update_columns(active_ticket_id: help_ticket.id)
      }
      let!(:help_ticket_3) {
        ticket_params = {
          'Subject' => "Third one",
          'Created By' => company_user.contributor_id,
          'Assigned To' => [company_user.contributor_id],
          'Impacted Devices' => [asset.id],
          'Status' => 'Open',
          'Priority' => "medium",
          'Description' => "The red robin sits in the tree.",
          workspace: company.workspaces.first
        }
        create_ticket(ticket_params)
      }

      let(:asset) { FactoryBot.create(:managed_asset, company: company) }

      before do
        get :index, params: { managed_asset_id: asset.id, format: :json }
      end

      it "should return a http status 200" do
        expect(response).to have_http_status(:ok)
      end

      it "should return the impacted ticket" do
        expect(assigns(:tickets).uniq).not_to be_nil
        expect(assigns(:tickets).uniq.length).to eq(2)
        ids = assigns(:tickets).uniq.pluck(:id)
        expect(ids).to include(help_ticket_3.id)
        expect(ids).to include(help_ticket.id)
        subjects = assigns(:tickets).uniq.map { |t| t[:fields][:subject][:value] }
        expect(subjects).to include(help_ticket_3.subject)
        expect(subjects).to include(help_ticket.subject)
      end
    end

    context "with more than one company id" do
      let(:child_company_params) do
        {
          name: "Child company 1",
          subdomain: "child-company-1",
          is_reseller_company: false,
          reseller_company_id: company.id
        }
      end

      let(:child_company) do
        create(:company, child_company_params)
      end

      let(:child_company_user) do
        child_user = create(:company_user, company: child_company, user: company_user.user)
        admins.group_members << GroupMember.new(contributor: child_user.contributor)
        ExpandedPrivileges::Populate.new(child_user.contributor).call
        child_user
      end

      let(:help_ticket_params) {
        [
          {
            'Subject' => "Greatest Moment",
            'Created By' => company_user.contributor_id,
            'Status' => 'Open',
            company: company,
            workspace: company.workspaces.first
          },
          {
            'Subject' => "Working Hard",
            'Created By' => child_company_user.contributor_id,
            'Status' => 'Open',
            company: child_company,
            workspace: child_company.workspaces.first
          }
        ]
      }

      let!(:help_tickets) do
        HelpTicket.destroy_all
        help_ticket_params.each_with_index.map{ |params| create_ticket(params) }
      end

      before do
        child_company_user
        get :index, params: { company_ids: [company.id, child_company.id], format: :json }
      end

      it "should return a successful response" do
        expect(response.status).to eq(200)
      end

      it "should return tickets" do
        expect(assigns(:tickets).uniq).not_to be_nil
        expect(assigns(:tickets).uniq.length).to eq(2)
        expect(assigns(:tickets).uniq.pluck(:id)).to include(company.help_tickets.first.id)
        expect(assigns(:tickets).uniq.pluck(:id)).to include(child_company.help_tickets.first.id)
      end
    end
  end
end
