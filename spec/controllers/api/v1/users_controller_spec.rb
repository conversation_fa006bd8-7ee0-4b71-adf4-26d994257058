require 'rails_helper'

include CompanyUserHelper
include MobileLoginHelper

describe Api::V1::UsersController, type: :controller do
  create_company_and_user

  before do
    request.headers['Authorization'] = token_generator(user.id)
  end

  describe "GET #user_detail", :skip_login do
    it "returns the user's details" do
      get :user_detail

      expect(response).to have_http_status(:ok)
      expect(response.body).to eq(controller.send(:user_detail_json, user).to_json)
    end
  end

  describe "POST #send_email_confirmation", :skip_login do
    context "when the user is associated with a company" do
      it "sends a confirmation email to the user" do
        expect {
          post :send_email_confirmation
        }.to change(ActionMailer::Base.deliveries, :count).by(1)

        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe "PATCH #update_pinned_company_status", :skip_login do
    let(:company) { create(:company) }
    let!(:user_pinned_company) { create(:user_pinned_company, user: user, company: company) }

    context "when the company is already pinned" do
      it "removes the pinned status for the company" do
        expect {
          patch :update_pinned_company_status, params: { company_id: company.id }
        }.to change(UserPinnedCompany, :count).by(-1)

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)["pinned_company_ids"]).to_not include(company.id)
      end
    end

    context "when the company is not already pinned" do
      it "adds the pinned status for the company" do
        expect {
          patch :update_pinned_company_status, params: { company_id: create(:company).id }
        }.to change(UserPinnedCompany, :count).by(1)

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)["pinned_company_ids"]).to include(company.id)
      end
    end
  end

  describe "#user_json", :skip_login do
    it "returns the user's details as a hash" do
      expect(controller.send(:user_detail_json, user)).to eq({
        full_name: user.full_name || "Missing name",
        first_name: user.first_name,
        last_name: user.last_name,
        email: user.email,
        avatar_thumb_url: company_user.avatar,
      })
    end
  end
end
