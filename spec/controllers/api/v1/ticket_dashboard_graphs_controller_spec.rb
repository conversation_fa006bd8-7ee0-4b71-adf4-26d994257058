require 'rails_helper'

include HelpTicketHelper
include CompanyUserHelper
include MobileLoginHelper

RSpec.describe Api::V1::TicketDashboardGraphsController, type: :controller do
  create_company_and_user

  before do
    request.headers['Authorization'] = token_generator(user.id)
  end

  let(:activity_type) { HelpTicketActivity.activity_types[:status] }

  let!(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
        'Assigned To' => company_user.contributor_id,
        'Status' => 'In Progress',
        'Priority' => "low",
        'source' => 'slack'
      },
      {
        'Subject' => "Boom boom",
        'Created By' => company_user.contributor_id,
        'Status' => 'Open',
        'Priority' => "medium",
        'source' => 'slack'
      },
      {
        'Subject' => "Second Ticket",
        'Created By' => company_user.contributor_id,
        'Status' => 'In Progress',
        'Priority' => "low",
        'source' => 'auto_generated'
      },
      {
        'Subject' => "Test Ticket",
        'Created By' => company_user.contributor_id,
        'Status' => 'Closed',
        'Priority' => "low",
        'source' => 'splitted',
      },
      {
        'Subject' => "Last Ticket...  almost",
        'Created By' => company_user.contributor_id,
        'Status' => 'Closed',
        'Priority' => "low",
        'source' => 'splitted',
      },
      {
        'Subject' => "Last Ticket",
        'Created By' => company_user.contributor_id,
        'Status' => 'Closed',
        'Priority' => "low",
        'source' => 'splitted',
      }
    ]
  }

  let!(:help_tickets) { help_ticket_params.map{ |p| create_ticket(p) } }

  let!(:closing_survey) do
    create(:closing_survey, help_ticket: help_tickets.first, positive: true)
    create(:closing_survey, help_ticket: help_tickets.third, positive: true)
    create(:closing_survey, help_ticket: help_tickets.second, positive: false)
  end

  let!(:closed_time_response1) do
    ticket = help_tickets.second_to_last
    ticket.update_column(:created_at, 5.days.ago)

    a = HelpTicketActivity.create(help_ticket: ticket,
                                  owner_id: company_user.id,
                                  activity_type: activity_type,
                                  data:
                                      {
                                        previous_value: "Open",
                                        current_value: "Closed",
                                        activity_label: "Status",
                                        ticket_subject: "Waaa",
                                        ticket_number: ticket.ticket_number
                                      }
                                )
    a.update_column(:created_at, 4.days.ago)

    a = HelpTicketActivity.create(help_ticket: ticket,
                                  owner_id: company_user.id,
                                  activity_type: activity_type,
                                  data:
                                      {
                                        previous_value: "Closed",
                                        current_value: "Open",
                                        activity_label: "Status",
                                        ticket_subject: "Waaa",
                                        ticket_number: ticket.ticket_number
                                      }
                                )
    a.update_column(:created_at, 3.days.ago)

    a = HelpTicketActivity.create(help_ticket: ticket,
                                  owner_id: company_user.id,
                                  activity_type: activity_type,
                                  data:
                                      {
                                        previous_value: "Open",
                                        current_value: "Closed",
                                        activity_label: "Status",
                                        ticket_subject: "Waaa",
                                        ticket_number: ticket.ticket_number
                                      }
                                )
    a.update_column(:created_at, 2.days.ago)
  end

  let!(:closed_time_response2) do
    ticket = help_tickets.last
    ticket.update_column(:created_at, 2.days.ago)

    a = HelpTicketActivity.create(help_ticket: ticket,
                                  owner_id: company_user.id,
                                  activity_type: activity_type,
                                  data:
                                      {
                                        previous_value: "Open",
                                        current_value: "Closed",
                                        activity_label: "Status",
                                        ticket_subject: "Waaa",
                                        ticket_number: ticket.ticket_number
                                      }
                                )

    a.update_column(:created_at, 1.day.ago)
  end

  describe '#index', :skip_login do
    it 'will get the ticket dashboard graphs data' do
      get :index, format: 'json'

      parsed_response = JSON.parse(response.body)

      ticket_by_source = parsed_response["ticket_by_source"]
      ticket_closed_times = parsed_response["ticket_closed_times"]
      ticket_by_assignment = parsed_response["ticket_by_assignment"].first
      ticket_by_status = parsed_response["ticket_by_status"]["counts"].first
      ticket_by_satisfaction = parsed_response["ticket_by_satisfaction"].first

      expect(ticket_by_status['open']).to eq(1)
      expect(ticket_by_status['in_progress']).to eq(2)
      expect(ticket_by_status['closed']).to eq(3)

      expect(ticket_by_source["sources_count"]['slack']).to eq(2)
      expect(ticket_by_source["sources_count"]['auto_generated']).to eq(1)
      expect(ticket_by_source["sources_count"]['split']).to eq(3)
      expect(ticket_by_source["sources_count"]['microsoft_teams']).to eq(nil)

      expect(ticket_by_assignment['assigned']).to eq(1)
      expect(ticket_by_assignment['unassigned']).to eq(5)

      expect(ticket_by_satisfaction["great"]).to eq(2)
      expect(ticket_by_satisfaction["not_so_good"]).to eq(1)

      expect(ticket_closed_times['dates'].count).to eq(2)
      expect(ticket_closed_times['closed_avg_duration'].count).to eq(2)
      expect(ticket_closed_times['overall_closed_average'].to_i).to eq(2)
      expect(ticket_closed_times['closed_avg_duration'].first.to_i).to eq(1)
      expect(ticket_closed_times['closed_avg_duration'].second.to_i).to eq(3)
      expect(ticket_closed_times['dates'].first.to_date).to eq (Date.today - 1.days)
      expect(ticket_closed_times['dates'].second.to_date).to eq (Date.today - 2.days)
    end
  end
end
