require 'json'
require 'rails_helper'

include HelpTicketHelper
include CompanyUserHelper
include MobileLoginHelper

describe Api::V1::CustomFormTicketsController, type: :controller do
  create_company_and_user

  let!(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let!(:company_user2) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let!(:company) { company_user.company }
  let!(:default_form) { company.custom_forms.helpdesk.first }
  let!(:subject) { default_form.custom_form_fields.find_by(name: 'subject') }
  let!(:status) { default_form.custom_form_fields.find_by(name: 'status') }
  let!(:priority) { default_form.custom_form_fields.find_by(name: 'priority') }
  let!(:created_by) { default_form.custom_form_fields.find_by(name: 'created_by') }

  let!(:help_center_form) { FactoryBot.create(:custom_form, company: company, company_module: "helpdesk") }
  let!(:open_created_by) { FactoryBot.create(:custom_form_field, custom_form_id: help_center_form.id, field_attribute_type: 'external_user', label: 'Created By', options:nil, required:true, note:nil, default_value: nil, private:false, permit_view_default:true, permit_edit_default:true, name: 'created_by')}
  let!(:open_subject) { FactoryBot.create(:custom_form_field, custom_form_id: help_center_form.id, field_attribute_type: 'text', label: 'Subject', options:nil, required:true, note:nil, default_value: 'tick subject', private:false, permit_view_default:true, permit_edit_default:true, name: 'subject')}
  let!(:open_assigned_to) { FactoryBot.create(:custom_form_field, custom_form_id: help_center_form.id, field_attribute_type: 'people_list', order_position: 5, label: 'Assigned To', name: 'assigned_to', sort_list: "[\"Ascending\", \"First Name, Last Name\"]", options: nil, required: nil, note: nil, permit_view_default: true, permit_edit_default: true, private: true, default_value: "[#{company_user.contributor_id}, #{company_user2.contributor_id}]")}

  let!(:help_ticket_params) {
    {
      'Subject' => 'Base Ticket',
      'Created By' => company_user.contributor_id.to_s,
      'Assigned To' => company_user.contributor_id.to_s,
      'Status' => 'Open',
      'Priority' => 'low',
      'Description' => 'This is a test ticket.',
      company: company,
      workspace: workspace
    }
  }
  let!(:help_ticket) { create_ticket(help_ticket_params) }
  let!(:comment) { FactoryBot.create(:help_ticket_comment, contributor_id: company_user.contributor_id, help_ticket_id: help_ticket.id) }

  before do
    request.headers['Authorization'] = token_generator(user.id)
    @request.host = "#{company.subdomain}.#{Rails.application.credentials.root_domain}"
    @request.env['HTTP_REFERER'] = "#{company.subdomain}.#{Rails.application.credentials.root_domain}/help_center"
  end

  context "with an comment id", :skip_login do
    it "will create ticket successfully" do
      post :create, params: {"json"=>"{\"customFormId\":#{default_form.id},\"commentId\":#{comment.id},\"values\":[{\"customFormFieldId\":#{subject.id},\"valueStr\":\"This is a test comment\"},{\"customFormFieldId\":#{priority.id},\"valueStr\":\"low\"},{\"customFormFieldId\":#{status.id},\"valueStr\":\"Open\"},{\"customFormFieldId\":#{created_by.id},\"valueInt\":#{comment.contributor_id}}]}", "custom_form_id"=>"#{default_form.id}", "format"=>"json"}

      comment_id = JSON.parse(response.body)['entity']['comment_id']
      expect(comment_id).to eq(comment.id)
    end
  end

  context "from open ticket portal", :skip_login do
    it "will create default value for private subject field successfully", :skip_login do
      post :create, params: {"json"=>"{\"customFormId\":#{help_center_form.id},\"values\":[{\"customFormFieldId\":#{open_created_by.id},\"valueInt\":#{company_user2.contributor_id}}]}", "custom_form_id"=>"#{help_center_form.id}", "format"=>"json"}

      help_ticket = HelpTicket.find(JSON.parse(response.body)['entity']['id'])
      subject_value = help_ticket.custom_form_values.find_by(custom_form_field_id: open_subject.id)
      expect(subject_value).to be_present
      expect(subject_value.value_str).to eq("tick subject")
    end

    it "will create default values for private assigned_to field successfully", :skip_login do
      post :create, params: {"json"=>"{\"customFormId\":#{help_center_form.id},\"values\":[{\"customFormFieldId\":#{open_created_by.id},\"valueInt\":#{company_user2.contributor_id}}]}", "custom_form_id"=>"#{help_center_form.id}", "format"=>"json"}

      help_ticket = HelpTicket.find(JSON.parse(response.body)['entity']['id'])
      assigned_values = help_ticket.custom_form_values.where(custom_form_field_id: open_assigned_to.id).pluck(:value_int)
      expect(assigned_values).to be_present
      expect(assigned_values).to include(company_user.contributor_id, company_user2.contributor_id)
    end
  end
end
