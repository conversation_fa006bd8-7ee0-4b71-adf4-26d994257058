require 'rails_helper'

include CompanyUserHelper

RSpec.describe Api::V1::AuthenticateUsersController, type: :controller do
  create_company_and_user

  describe "#authenticate", :skip_login do
    context "with valid params" do
      subject { post :authenticate, params: { email: user.email, password: user.password, format: :json } }

      it "returns a success response with token, current company, workspace, and company user" do
        allow(AuthenticateMobileUser).to receive(:call).with(user.email, user.password).and_return(
          double('Command', success?: true, result: ['token', false, 'refreshToken'], current_user: user)
        )

        subject

        json_response = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(json_response["token"]).to be_present
        expect(json_response["multiple"]).to eq(false)
        expect(json_response["current_company"]).to be_present
        expect(json_response["current_workspace"]).to be_present
        expect(json_response["company_user"]["id"]).to eq(company_user.id)
      end

      it "returns a success response with token and multiple true" do
        allow(AuthenticateMobileUser).to receive(:call).with(user.email, user.password).and_return(
          double('Command', success?: true, result: ['token', true, 'refreshToken'], current_user: user)
        )

        subject

        json_response = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(json_response["token"]).to be_present
        expect(json_response["multiple"]).to eq(true)
      end
    end

    context "with invalid params" do
      subject { post :authenticate, params: { email: "#{user.email}#{user.email}", password: "#{user.password}#{user.password}", format: :json } }

      before do
        allow(AuthenticateMobileUser).to receive(:call).with("#{user.email}#{user.email}", "#{user.password}#{user.password}").and_return(
          double('Command', success?: false, errors: ['invalid credentials'], current_user: user)
        )
      end

      it "returns an unauthorized response with an error message" do
        subject

        json_response = JSON.parse(response.body)

        expect(response).to have_http_status(:unauthorized)
        expect(json_response["error"]).to eq(["invalid credentials"])
        expect(json_response["error_type"]).to eq("authenticate_user")
      end
    end
  end
end
