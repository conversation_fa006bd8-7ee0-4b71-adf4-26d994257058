require 'rails_helper'
include CompanyUserHelper
include MobileLoginHelper

RSpec.describe Api::V1::ProjectTaskPositionsController, type: :controller do
  create_company_and_user

  before do
    request.headers['Authorization'] = token_generator(user.id)

    @help_ticket = FactoryBot.create(:help_ticket, company: company, workspace: company.default_workspace)
    FactoryBot.create_list(:project_task, 5, company: company, help_ticket: @help_ticket)
  end

  describe "#update_positions", :skip_login do
    it "update helpticket task positions" do
      params = {
        "tasks" => [
          {"id" => @help_ticket.project_tasks.first.id, "position" => 2},
          {"id" => @help_ticket.project_tasks.second.id, "position" => 1}
        ],
        "help_ticket_id"=>@help_ticket.id,
        privilege_name: 'HelpTicket' }
      post :update_positions, params: params

      res = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(@help_ticket.project_tasks.first.position).to eq(2)
      expect(@help_ticket.project_tasks.count).to eq(5)
    end
  end
end
