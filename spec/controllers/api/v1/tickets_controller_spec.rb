require 'rails_helper'

include HelpTicketHelper
include CompanyUserHelper
include MobileLoginHelper

describe Api::V1::TicketsController, type: :controller do
  create_company_and_user

  before do
    request.headers['Authorization'] = token_generator(user.id)
  end

  let(:company_user) { company.company_users.first }
  let!(:company_user2) { create(:company_user, company: company_user.company) }
  let(:asset) { FactoryBot.create(:managed_asset, company: company) }
  let(:contract) { FactoryBot.create(:contract, name: 'contract1', company: company) }
  let(:vendor) { FactoryBot.create(:vendor, company_id: company.id, category_id: company.categories.first.id, name: "Microsoft", url: "www.microsoft.com") }

  let(:author) { user.company_users.first }

  let!(:help_ticket_params) {
    {
      'Subject' => "Boom boom",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [asset.id],
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      company: company
    }
  }

  let!(:help_ticket1) { create_ticket(help_ticket_params) }

  let(:help_ticket_attributes) do
    {
      creator_id: author.id,
      subject: "Test Ticket",
      email: user.email,
      help_paragraph: "Help Me",
      company_id: company.id,
      priority: "low",
      links_array: {
        "0" => { "id" => asset.id, "linkable_id" => asset.linkable_id, "name" => asset.name, "type" => "ManagedAsset" },
        "1" => { "id" => contract.id, "linkable_id" => contract.linkable_id, "name" => contract.name, "type" => "Vendor" },
        "2" => { "id" => vendor.id, "linkable_id" => vendor.linkable_id, "name" => vendor.name, "type" => "Contract" }
      }
    }
  end

  let(:help_ticket_attributes_with_agent) do
    {
      creator_id: author.id,
      subject: "Test Ticket 2",
      email: user.email,
      help_paragraph: "Help Me",
      company_id: company.id,
      priority: "low"
    }
  end

  describe "#show", :skip_login do
    context "Show Help Ticket" do
      let!(:help_ticket) {
        ticket_params = {
          'Subject' => "Boom boom",
          'Created By' => company_user.contributor_id,
          'Assigned To' => [company_user.contributor_id],
          'Followers' => [company_user.contributor_id],
          'Impacted Devices' => [asset.id],
          'Status' => 'Open',
          'Priority' => "medium",
          'Description' => "The red robin sits in the tree.",
          company: company
        }
        create_ticket(ticket_params)
      }

      it "will return success response" do
        get :show, params: { "id" => help_ticket.id, "privilege_name" => "HelpTicket", "format"=>"json" }

        expect(response).to have_http_status(:ok)
      end

      it "Form value delete with deleting asset" do
        cfv_count = help_ticket.custom_form_values.count
        id = asset.id
        asset.destroy
        Sidekiq::Testing.inline! do
          UpdateCustomFormValuesWorker.perform_async(id, ManagedAsset.new.field_type)
        end
        expect(response.status).to eq(200)
      end

      it "Form value delete with deleting contributor" do
        id = company_user.contributor_id
        company_user.contributor.destroy
        Sidekiq::Testing.inline! do
          UpdateCustomFormValuesWorker.perform_async(id, Contributor.new.field_type)
        end
        expect(response.status).to eq(200)
      end
    end
  end

  describe "#show", :skip_login do
    context "Show Help Ticket" do
      let!(:help_ticket) {
        ticket_params = {
          'Subject' => "Boom boom",
          'Created By' => "<EMAIL>",
          'Assigned To' => [company_user.contributor_id],
          'Followers' => [company_user.contributor_id],
          'Impacted Devices' => [asset.id],
          'Status' => 'Open',
          'Priority' => "medium",
          'Description' => "The red robin sits in the tree.",
          company: company
        }
        create_ticket(ticket_params)
      }

      it "will return success response" do
        get :show, params: { "id" => help_ticket.id, "privilege_name" => "HelpTicket", "format"=>"json" }

        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe "#show", :skip_login do
    context "Show Help Ticket with Radio Button" do
      let(:custom_form) { company.custom_forms.helpdesk.first }
      let!(:radio_button) {
        radio_button = CustomFormField.new(label: "Radio Button",
                                           field_attribute_type: "radio_button",
                                           order_position: 10,
                                           options: "[\"First\",\"Second\",\"Third\"]",
                                           required: false,
                                           note: nil,
                                           default_value: "First",
                                           private: false,
                                           permit_view_default: true,
                                           permit_edit_default: true,
                                           name: "radio_button",
                                           custom_form_id: custom_form.id
                                          )
        radio_button.field_position = FieldPosition.new(custom_form_field_id: radio_button.id, position: "right")
        custom_form.custom_form_fields << radio_button
        radio_button
      }
      let!(:checkbox) {
        checkbox = CustomFormField.new(label: "Checkbox",
                                       field_attribute_type: "checkbox",
                                       order_position: 11,
                                       options: "[\"Desktop\",\"Laptop\",\"Router\"]",
                                       required: false,
                                       private: false,
                                       permit_view_default: true,
                                       permit_edit_default: true,
                                       name: "checkbox",
                                       custom_form_id: custom_form.id
                                      )
        checkbox.field_position = FieldPosition.new(custom_form_field_id: checkbox.id, position: "right")
        custom_form.custom_form_fields << checkbox
        checkbox
      }
      let!(:help_ticket) {
        ticket_params = {
          'Subject' => "Boom boom",
          'Created By' => "<EMAIL>",
          'Assigned To' => [company_user.contributor_id],
          'Followers' => [company_user.contributor_id],
          'Impacted Devices' => [asset.id],
          'Status' => 'Open',
          'Priority' => "medium",
          'Description' => "The red robin sits in the tree.",
          'Radio Button' => "First",
          'Checkbox' => "Desktop"
        }
        create_ticket(ticket_params)
      }

      it "will return success response" do
        get :show, params: { "id" => help_ticket.id, "privilege_name" => "HelpTicket", "format"=>"json" }

        expect(response).to have_http_status(:ok)
      end

      it "will check radio button field" do
        value_str = help_ticket.custom_form_values.find_by(value_str: "First").value_str
        expect(value_str).to eq("First")
        expect(JSON.parse(help_ticket.custom_form_values.find_by(value_str: "First").custom_form_field.options).count).to eq(3)
        expect(help_ticket.custom_form_values.find_by(value_str: "First").custom_form_field.default_value).to eq("First")
      end

      it "will check checkbox field" do
        value_str = help_ticket.custom_form_values.find_by(custom_form_field_id: checkbox.id).value_str
        expect(value_str).to eq("Desktop")
      end
    end
  end

  describe "#notification_status", :skip_login do
    context "#update ticket" do
      let!(:help_ticket) {
        ticket_params = {
          'Subject' => "Boom boom",
          'Created By' => company_user.contributor_id,
          'Assigned To' => [company_user.contributor_id],
          'Followers' => [company_user.contributor_id],
          'Impacted Devices' => [asset.id],
          'Status' => 'Open',
          'Priority' => "medium",
          'Description' => "The red robin sits in the tree.",
          mute_notification: false,
          company: company
        }
        create_ticket(ticket_params)
      }

      it "will check if status is updated" do
        help_ticket.mute_notification = !help_ticket.mute_notification
        put :update_notification_status, params: { id: help_ticket.id, ticket: help_ticket.as_json, privilege_name: 'HelpTicket' }, format: :json
        ht = HelpTicket.find(help_ticket.id)
        expect(ht.mute_notification).to eq(true)
      end
    end

    context "#create ticket" do
      let(:ticket_params) {
        {
          'Subject' => "Mute notification 01",
          'Created By' => company_user.contributor_id,
          'Assigned To' => [company_user.contributor_id],
          'Followers' => [company_user.contributor_id],
          'Status' => 'Open',
          'Priority' => "low",
          'Description' => "When mute notification setting is on",
          mute_notification: true,
          company: company
        }
      }

      let(:ticket_params_02) {
        {
          'Subject' => "Mute notification 02",
          'Created By' => company_user.contributor_id,
          'Assigned To' => [company_user.contributor_id],
          'Followers' => [company_user.contributor_id],
          'Status' => 'Open',
          'Priority' => "low",
          'Description' => "When mute notification setting is off",
          mute_notification: false,
          company: company
        }
      }

      it "will check if status is true for ticket with mute notification setting is on" do
        ticket = create_ticket(ticket_params)
        expect(ticket.mute_notification).to eq(true)
      end

      it "will check if status is false for ticket with mute notification setting is off" do
        ticket = create_ticket(ticket_params_02)
        expect(ticket.mute_notification).to eq(false)
      end
    end

    describe "#destroy", :skip_login do
      context "Destroy Help Ticket" do
        it "will delete the help ticket" do
          expect {
            delete :destroy, params: { "id" => help_ticket1.id, "format"=>"json" }
          }.to change(HelpTicket, :count).by(0)

          expect(response).to have_http_status(:not_found)
          expect(JSON.parse(response.body)["message"]).to eq("Help ticket was not found.")
        end
      end
    end

    describe "#archive", :skip_login do
      context "Archive Help Ticket" do
        it "will archive the help ticket" do
          help_ticket1.update(archived: true)
          put :archive, params: { id: help_ticket1.id, ticket: help_ticket1.as_json, privilege_name: 'HelpTicket' }, format: :json
          ht = HelpTicket.find(help_ticket1.id)
          expect(ht.archived).to eq(true)
          expect(response).to have_http_status(:ok)
        end

        it "will unarchive the help ticket" do
          # Archive the help ticket first
          help_ticket1.update(archived: true)
          expect {
            put :unarchive, params: { id: help_ticket1.id, ticket: help_ticket1.as_json, privilege_name: 'HelpTicket' }, format: :json
          }.to change { help_ticket1.reload.archived }.from(true).to(false)

          ht = HelpTicket.find(help_ticket1.id)
          expect(ht.archived).to eq(false)
          expect(response).to have_http_status(:ok)
        end
      end
    end
  end
end
