require 'rails_helper'
include CompanyUserHelper

# TODO: Needs to revamp the spec of this file by adding spec of AssetCreation::DiscoveredAsset::SyncDataWorker
describe Api::V3::DiscoveredAssetsController, type: :controller do
  create_company_and_user
  AppEnabled.create(flag: true)

  describe "POST create" do
    let!(:window_app_release) do
      AppRelease.create(
        app_name: "network_discovery",
        app_enabled: true,
        version: "*******",
        is_admin_app: true
      )
    end
    let(:valid_params) do
      {
        discovered_assets: [
          {
            managedAsset: {
              Id: "00000000-0000-0000-0000-000000000000",
              companyGuid: company.guid,
              isLocalSystem: true,
              displayName: "DESKTOP-R83LFQL",
              formattedDisplayName: "DESKTOP-R83LFQL",
              macAddresses: ["A4:97:B1:AA:C1:B7"],
              secMacAddresses: ["00:15:5D:CE:DB:9C", "A6:97:B1:AA:C1:B7", "B6:97:B1:AA:C1:B7"],
              macAddressesInfo: [
                {
                  macAddress: "00:15:5D:CE:DB:9C",
                  machType: "Hyper-V Virtual Ethernet Adapter",
                  networkName: "vEthernet (WSL)",
                  networkType: "Ethernet",
                  hasGateway: false
                },
                {
                  macAddress: "A6:97:B1:AA:C1:B7",
                  machType: "Microsoft Wi-Fi Direct Virtual Adapter",
                  networkName: "Local Area Connection* 1",
                  networkType: "Wireless80211",
                  hasGateway: false
                }
              ],
              diskDetail: nil,
              applications: [
                {
                  displayName: "AnyDesk",
                  version: "ad 7.1.7",
                  installDate: nil,
                  installSource: nil,
                  uninstallString: "\"C:\\Program Files (x86)\\AnyDesk\\AnyDesk.exe\" --uninstall",
                  installLocation: "\"C:\\Program Files\\AnyDesk\""
                },
                {
                  displayName: "Google Chrome",
                  version: "108.0.5359.125",
                  installDate: "2022-12-16",
                  installSource: nil,
                  uninstallString: "\"C:\\Program Files\\Google\\Chrome\\Application\\108.0.5359.125\\Installer\\setup.exe\" --uninstall --channel=stable --system-level --verbose-logging",
                  installLocation: "C:\\Program Files\\Google\\Chrome\\Application"
                }
              ],
              ipAddress: "**************",
              networks: ["************", "**************"],
              deviceType: "Laptop",
              protocol: nil,
              os: "Windows",
              manufacturer: "Dell Inc.",
              formattedManufacturer: "Dell Inc.",
              pcSerialNo: "20S1B63",
              osName: "Microsoft Windows 10 Pro",
              osVersion: "10.0.19045",
              osSerialNo: "00330-80000-00000-AA863",
              firmware: nil,
              memory: "24 GB",
              diskSpace: "477 GB",
              processorCores: "4 Cores",
              processorLogicalCores: "8 Cores",
              processorArchitecture: "x64",
              processorName: "11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz",
              domainName: "WORKGROUP",
              username: nil,
              systemUuid: "4C4C4544-0030-5310-8031-B2C04F423633",
              password: nil,
              community: nil,
              isMacDuplicate: false,
              ssh: true,
              wmi: true,
              snmp: true,
              optionalDetails: "",
              xml: "",
              newAssetType: "",
              model: "Inspiron 5502",
              systemUpTime: "52 Hours",
              osBuildNo: "19045",
              deviceId: "8F41D00A-23DC-40C0-9156-B21E23B44486",
              diskEncryption: "Off",
              diskFreeSpace: "106GB",
              hardwareVersion: "1.18.0",
              userAccountsInfo: [
                {
                  fullName: "",
                  name: "ccops",
                  lastLogin: nil
                }
              ],
              remoteIpAddress: "**************",
              hostname: "DESKTOP-LCG7AN0.WORKGROUP",
              HardwareDetail: nil
            }
          }
        ],
        company_guid: company.guid,
        mac_addresses: ["D4:1B:81:14:0B:1B"],
        sec_mac_addresses: ["00:15:5D:CE:DB:9C", "A6:97:B1:AA:C1:B7", "B6:97:B1:AA:C1:B7"],
        mac_add_details: [
          {
            macAddress: "00:15:5D:CE:DB:9C",
            machType: "Hyper-V Virtual Ethernet Adapter",
            networkName: "vEthernet (WSL)",
            networkType: "Ethernet",
            hasGateway: false
          }
        ],
        computer_name: "My postman system",
        pc_serial_no: "DL75T93",
        system_uuid: "4C4C4544-004C-3710-8035-C4C04F543933",
        ip_address: "************",
        app_version: "*******",
        bg_mode: false
      }
    end
    it "creates and updates discovered_asset and probe_location" do
      post :create, params: valid_params, as: :json
      asset = DiscoveredAsset.find_by(company_id: company.id)

      expect(asset.mac_addresses).to include("A4:97:B1:AA:C1:B7")
      expect(asset.os_version).to eq("10.0.19045")
      expect(asset.os_name).to eq("Microsoft Windows 10 Pro")
      expect(asset.protocols).to eq({ "ssh" => true, "wmi" => true, "snmp" => true })

      company = asset.company
      expect(company.discovered_assets.count).to eq(1)
    end
  end
end
