require 'rails_helper'
include CompanyUserHelper

describe Api::V3::DiscoveredUsersController, type: :controller do
  create_company_and_user

  describe "POST create" do
    let(:valid_params) do
      {
        data: [
          {
            email: "<EMAIL>",
            first_name: "<PERSON>",
            last_name: "hardy",
            principal_name: "abc",
            account_name: "tom",
            telephone_numbers: { "mobile": "*********", "work": "*********" },
            organization: { "department": "hr", "title": "manager" }
          }
        ],
        company_guid: company.guid
      }
    end
    it "creates and updates discovered user" do
      expect { post :create, params: valid_params }.to change{ DiscoveredUser.count }.by(1)
      expect { post :create, params: valid_params }.to change{ DiscoveredUser.count }.by(0)
      user = company.discovered_users.find_by(email: "<EMAIL>")
      expect(user.principal_name).to eq("abc")
      expect(user.account_name).to eq("tom")
      expect(user.department).to eq("hr")
      expect(user.work_phone).to eq("*********")
      expect(user.mobile_phone).to eq("*********")
      expect(user.title).to eq("manager")
    end
  end
end
