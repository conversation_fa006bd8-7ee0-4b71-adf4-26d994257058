require 'rails_helper'
include CompanyUserHelper

RSpec.describe AssetTypeSummariesController, type: :controller do
  create_company_and_user

  let!(:laptop_asset) { create(:managed_asset, :laptop, company_id: company.id, location_id: company.locations.first.id, company_asset_status_id: ready_to_use_status.id) }
  let!(:mobile_asset) { create(:managed_asset, :mobile, company_id: company.id, location_id: company.locations.first.id, archived: true, warranty_expiration: Date.today + 4.months) }
  let!(:phone_asset) { create(:managed_asset, :phone, company_id: company.id, warranty_expiration: Date.today + 9.months) }
  let!(:printer_asset) { create(:managed_asset, :printer, company_id: company.id, location_id: company.locations.first.id, warranty_expiration: Date.today - 9.months) }
  let!(:router_asset) { create(:managed_asset, :router, company_id: company.id, archived: true, warranty_expiration: Date.today - 4.months) }
  let(:ready_to_use_status) { company.asset_statuses.find_by(name: "Ready to Use") }

  describe "GET #index" do
    it "returns managed assets located in specific location" do
      get :index, :params => { location_id: company.locations.first.id, format: :json }

      json_response = JSON.parse(response.body)
      asset_count = ManagedAsset.where(location_id: company.locations.first.id).count
      response_count = json_response.map { |res| res['count'] }.sum
      expect(response).to have_http_status(:ok)
      expect(response_count).to eq(asset_count)
    end

    it "returns managed assets located in specific location with type" do
      get :index, :params => { type: "Laptop", location_id: company.locations.first.id, format: :json }

      json_response = JSON.parse(response.body)
      assets_count = ManagedAsset.where(location_id: company.locations.first.id).joins(:asset_type).where("company_asset_types.name = (?)", "Laptop").count
      expect(response).to have_http_status(:ok)
      expect(json_response.first['count']).to eq(assets_count)
    end

    it "returns managed assets with type" do
      get :index, :params => { type: "Laptop", location_id: company.locations.first.id, format: :json }

      json_response = JSON.parse(response.body)
      assets_count = ManagedAsset.joins(:asset_type).where("company_asset_types.name = (?)", "Laptop").count
      expect(response).to have_http_status(:ok)
      expect(json_response.first['count']).to eq(assets_count)
    end

    it "returns managed assets with archived param true" do
      get :index, :params => { archived: true, format: :json }

      json_response = JSON.parse(response.body)
      assets_count = ManagedAsset.where(archived: true).count
      response_count = json_response.map { |res| res['count'] }.sum
      expect(response).to have_http_status(:ok)
      expect(response_count).to eq(assets_count)
    end

    it 'returns managed assets with archived param false' do
      get :index, :params => { archived: false, format: :json }

      json_response = JSON.parse(response.body)
      assets_count = ManagedAsset.unarchived.count
      response_count = json_response.map { |res| res['count'] }.sum
      expect(response).to have_http_status(:ok)
      expect(response_count).to eq(assets_count)
    end

    it 'returns managed assets with expired warranty params' do
      get :index, :params => { warranty_status: 'expired_warranty', format: :json }

      json_response = JSON.parse(response.body)
      assets_count = ManagedAsset.expired_warranty.count
      response_count = json_response.map { |res| res['count'] }.sum
      expect(response).to have_http_status(:ok)
      expect(response_count).to eq(assets_count)
    end

    it 'returns managed assets with in_warranty params' do
      get :index, :params => { warranty_status: 'in_warranty', format: :json }

      json_response = JSON.parse(response.body)
      assets_count = ManagedAsset.in_warranty.count
      response_count = json_response.map { |res| res['count'] }.sum
      expect(response).to have_http_status(:ok)
      expect(response_count).to eq(assets_count)
    end

    it 'returns managed assets with expiring warranty params' do
      get :index, :params => { warranty_status: 'expiring_warranty', format: :json }

      json_response = JSON.parse(response.body)
      assets_count = ManagedAsset.expiring_warranty.count
      response_count = json_response.map { |res| res['count'] }.sum
      expect(response).to have_http_status(:ok)
      expect(response_count).to eq(assets_count)
    end

    it 'returns managed assets with no warranty params' do
      get :index, :params => { warranty_status: 'no_warranty', format: :json }

      json_response = JSON.parse(response.body)
      assets_count = ManagedAsset.no_warranty.count
      response_count = json_response.map { |res| res['count'] }.sum
      expect(response).to have_http_status(:ok)
      expect(response_count).to eq(assets_count)
    end

    it "returns assets with search term" do
      get :index, :params => { search_term: "all", search: "AssetP", format: :json }

      json_response = JSON.parse(response.body)
      assets_count = ManagedAsset.where("managed_assets.name ilike ?", "%AssetP%").count
      response_count = json_response.map { |res| res['count'] }.sum
      expect(response).to have_http_status(:ok)
      expect(response_count).to eq(assets_count)
    end

    it 'return managed assets with in ready to use status params' do
      get :index, :params => { status: ready_to_use_status.id, format: :json }
      json_response = JSON.parse(response.body)
      response_count = json_response.map { |res| res['count'] }.sum
      expect(response).to have_http_status(:ok)
      expect(response_count).to eq(1)
    end

    it 'return managed assets with all source params' do
      get :index, :params => { source: 'manually_added', format: :json }

      json_response = JSON.parse(response.body)
      assets_count = ManagedAsset.manually_added.count
      response_count = json_response.map { |res| res['count'] }.sum
      expect(response).to have_http_status(:ok)
      expect(response_count).to eq(assets_count)
    end

    it 'return managed assets with company asset type id params' do
      asset_type_id = CompanyAssetType.find_by(name: 'Laptop', company_id: company.id).id
      get :index, :params => { company_asset_type_id: asset_type_id, format: :json }

      json_response = JSON.parse(response.body)

      assets_count = ManagedAsset.where(asset_type: asset_type_id).count
      response_count = json_response.map { |res| res['count'] }.sum
      expect(response).to have_http_status(:ok)
      expect(response_count).to eq(assets_count)
    end

    it 'return managed assets with asset type params' do
      company_asset_tag = CompanyAssetTag.create(name: 'new tag', company: company)
      tag = laptop_asset.managed_asset_tags.create(company_asset_tag_id: company_asset_tag.id, company: company)
      get :index, :params => { tag: company_asset_tag.id, format: :json }

      json_response = JSON.parse(response.body)
      assets_count = ManagedAssetTag.where(company_asset_tag_id: company_asset_tag.id).count
      response_count = json_response.map { |res| res['count'] }.sum
      expect(response).to have_http_status(:ok)
      expect(response_count).to eq(assets_count)
    end
  end
end
