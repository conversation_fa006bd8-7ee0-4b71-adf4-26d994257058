require 'rails_helper'
include CompanyUserHelper

RSpec.describe BulkHelpTicketEmailsController, type: :controller do
  create_company_and_user

  before do
    FactoryBot.create_list :ticket_email, 3, company: company, workspace: workspace
  end

  describe "Get #bulk_delete" do
    let(:ids) {
      workspace.ticket_emails.order(created_at: 'DESC').limit(3).pluck(:id)
    }

    it "Delete incoming emails" do
      post :bulk_delete, params: {
        help_ticket_email_ids: ids,
        bulk_help_ticket_email: {
          help_ticket_email_ids: ids
        }
      }
      expect(response.status).to eq(200)
    end
  end
end

