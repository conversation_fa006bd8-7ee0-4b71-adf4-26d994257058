require 'rails_helper'
include CompanyUserHelper
include <PERSON><PERSON>Helper

describe TelecomProvidersController, type: :controller do
  create_company_and_user

  let(:category) { company.categories.find_by(name: 'Telecom') }

  before do
    login_user
  end

  describe "#index" do
    let!(:vendor) { FactoryBot.create(:telecom_provider,
                                      name: 'AT&T',
                                      company: company,
                                      telecom_services: [voice_service]) }
    let!(:vendor1) { FactoryBot.create(:telecom_provider,
                                       name: 'Verizon',
                                       archived: true,
                                       company: company,
                                       telecom_services: [data_service]) }
    let!(:vendor2) { FactoryBot.create(:telecom_provider,
                                       name: 'Spectrum',
                                       archived: true,
                                       company: company,
                                       telecom_services: [consolidated_service]) }

    let!(:voice_service) { FactoryBot.create(:telecom_service,
                                             name: 'Main Voice',
                                             locations: [company.locations.first],
                                             company: company,
                                             service_type: "voice_service") }
    let!(:data_service) { FactoryBot.create(:telecom_service,
                                            name: 'Main Data',
                                            locations: [company.locations.last],
                                            company: company,
                                            service_type: "data_service") }
    let!(:consolidated_service) { FactoryBot.create(:telecom_service,
                                                    name: 'Secondary',
                                                    company: company,
                                                    service_type: "consolidated_service") }

    context "when fetching all company telecom providers" do
      it "should fetch all company telecom providers in alphanumeric order" do
        get :index, params: { format: :json }

        expect(response).to have_http_status(:ok)
        providers = JSON.parse(response.body)['providers']
        expect(providers.length).to eq(3)
        expect(providers[0]['name']).to eq('AT&T')
        expect(providers[1]['name']).to eq('Spectrum')
        expect(providers[2]['name']).to eq('Verizon')
      end
    end

    context "when fetching active company telecom providers" do
      it "should fetch active telecom providers" do
        get :index, params: { status: 'active', format: :json }

        expect(response).to have_http_status(:ok)
        providers = JSON.parse(response.body)['providers']
        expect(providers.length).to eq(1)
        expect(providers.first['name']).to eq('AT&T')
      end
    end

    context "when fetching voice telecom providers" do
      it "should fetch telecom providers" do
        get :index, params: { service_type: 'voice_service', format: :json }

        expect(response).to have_http_status(:ok)
        providers = JSON.parse(response.body)['providers']
        expect(providers.length).to eq(1)
        expect(providers.first['name']).to eq('AT&T')
      end
    end

    context "when fetching archived company telecom providers" do
      it "should fetch telecom providers" do
        get :index, params: { service_type: 'data_service', format: :json }

        expect(response).to have_http_status(:ok)
        providers = JSON.parse(response.body)['providers']
        expect(providers.length).to eq(1)
        expect(providers.first['name']).to eq('Verizon')
      end
    end

    context "when fetching consolidated company telecom providers" do
      it "should fetch telecom providers" do
        get :index, params: { service_type: 'consolidated_service', format: :json }

        expect(response).to have_http_status(:ok)
        providers = JSON.parse(response.body)['providers']
        expect(providers.length).to eq(1)
        expect(providers.first['name']).to eq('Spectrum')
      end
    end

    context "when fetching consolidated company telecom providers" do
      it "should fetch telecom providers" do
        get :index, params: { location_id: company.locations.first, format: :json }

        expect(response).to have_http_status(:ok)
        providers = JSON.parse(response.body)['providers']
        expect(providers.length).to eq(2)
        expect(providers.first['name']).to eq('AT&T')
      end
    end
  end

  describe "#destroy" do
    context "when a telecom provider has a corresponding vendor" do
      let!(:vendor) { FactoryBot.create(:vendor,
                                        name: 'AT&T',
                                        category: category,
                                        company: company
                                        ) }
      let(:telecom_provider) { vendor.telecom_provider }
      let!(:voice_service) { FactoryBot.create(:telecom_service,
                                               name: 'Main Voice',
                                               locations: [company.locations.first],
                                               company: company,
                                               telecom_provider: telecom_provider,
                                               service_type: "voice_service") }

      before do
        delete :destroy, params: { id: telecom_provider.id, format: :json }
      end

      it "should have status 200" do
        expect(response).to have_http_status(:ok)
      end

      it "should destroy the telecom provider" do
        expect(TelecomProvider.find_by(id: telecom_provider.id)).to be_blank
      end

      it "should not destroy the vendor" do
        expect(Vendor.find_by(id: vendor.id)).to be_present
      end

      it "should create a new recommended telecom provider" do
        provider = TelecomProvider.find_by(name: 'AT&T')
        expect(provider).to be_present
        expect(provider.status).to eq("recommended")
        expect(provider.archived).to be_truthy
      end
    end
  end

  describe "#update" do
    # put meaningful tests here
  end

  describe '#new' do
    context 'user without module permissions' do
      before do
        login_user(user_one.user)
        get :new, format: :json
      end

      let(:user_one) { create(:company_user, company: company) }

      it 'will not allow user to access the page' do
        expect(response.status).to eq(401)
        expect(JSON.parse(response.body)['message']).to eq("Sorry, you're not authorized to perform this action.")
      end
    end

    context 'user with module permissions' do
      before do
        login_user(company_user.user)
        get :new, format: :html
      end

      it 'will allow user to access the page' do
        expect(response.status).to eq(200)
      end
    end
  end
end
