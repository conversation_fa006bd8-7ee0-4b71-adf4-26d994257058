require 'rails_helper'
include CompanyUserHelper

describe ContractTagsController, type: :controller do
  create_company_and_user

  let!(:tag1) { ContractTag.create(company_id: company.id, tag: "tag1") }
  let!(:tag2) { ContractTag.create(company_id: company.id, tag: "tag2") }
  let!(:tag3) { ContractTag.create(company_id: company.id, tag: "tag2") }

  describe "#index" do
    it "will return all unique tags" do
      get :index, format: :json

      expect(response).to have_http_status(:ok)
      res = JSON.parse(response.body)
      expect(res.length).to eq(2)
      expect(res.first["tag"]).to eq("tag1")
      expect(res.second["tag"]).to eq("tag2")
    end
  end

  describe "#destroy" do
    it "will destroy the tag" do
      delete :destroy, params: { id: tag1.id, format: :json }

      expect(response).to have_http_status(:ok)
      expect(company.contract_tags.length).to eq(2)
    end
  end
end