require 'rails_helper'

include CompanyUserHelper

RSpec.describe HelpdeskCategoriesController, type: :controller do
  create_company_and_user
  let(:workspace) { company.workspaces.first }
  let(:category_for_faq) { FactoryBot.create(:category, name: 'Category for FAQ', workspace_id: workspace.id) }
  let(:create_faq) { FactoryBot.create(:helpdesk_faq, company: company, workspace: workspace, order: 2, category_id: category_for_faq.id) }

  describe "#index" do
    it "returns a list of all helpdesk categories" do
      get :index

      parsed_response = JSON.parse(response.body)
      expect(parsed_response.size).to eq(19)
      expect(response).to have_http_status(:ok)
    end

    it "returns a list of all helpdesk categories with some new categories" do
      category_1 = Category.create(name: "Category 1", workspace_id: workspace.id)
      category_2 = Category.create(name: "Category 2", workspace_id: workspace.id)
      get :index

      parsed_response = JSON.parse(response.body)
      expect(parsed_response.size).to eq(21)
      expect(response).to have_http_status(:ok)
    end
  end

  describe "#create" do
    it "creates customs categories for helpdesk" do
      post :create, params: { helpdesk_category: { name: 'Custom Category 1'} }

      parsed_response = JSON.parse(response.body)
      expect(parsed_response['categories'].size).to eq(20)
      expect(response).to have_http_status(:ok)
    end

    it "does not create duplicate categories" do
      post :create, params: { helpdesk_category: { name: 'HR'} }

      parsed_response = JSON.parse(response.body)
      expect(parsed_response['message']).to eq("Name has already been taken")
      expect(response).to have_http_status(:unprocessable_entity)
    end
  end

  describe "#destroy" do
    it "destroy categories for helpdesk" do
      category_1 = Category.create(name: "Category 1", workspace_id: workspace.id)
      delete :destroy, params: { id: category_1.id }

      parsed_response = JSON.parse(response.body)
      expect(parsed_response['categories'].size).to eq(19)
      expect(response).to have_http_status(:ok)
    end

    it "does not destroy categories with associated records for helpdesk" do
      create_faq
      delete :destroy, params: { id: category_for_faq.id }

      parsed_response = JSON.parse(response.body)
      expect(parsed_response['associations'].present?).to eq(true)
      expect(workspace.categories.size).to eq(20)
      expect(response).to have_http_status(:ok)
    end
  end
end
