require 'rails_helper'
include CompanyUserHelper

RSpec.describe AssetDepreciationBreakdownsController, type: :controller do
  create_company_and_user

  describe "#show" do
    context "getting the right depreciation dataset and breakdown" do
      let!(:managed_asset1) { FactoryBot.create(:managed_asset, company: company, name: "asset1",cost: nil) }
      let!(:managed_asset2) { FactoryBot.create(:managed_asset, company: company, name: "asset2",cost: nil) }
      let!(:managed_asset3) { FactoryBot.create(:managed_asset, company: company, name: "asset3",cost: nil) }

      let!(:cost1) { Cost.create(managed_asset_id: managed_asset1.id, purchase_price: 1500, salvage: 300, useful_life: 5)}
      let!(:cost2) { Cost.create(managed_asset_id: managed_asset2.id, purchase_price: 1500, salvage: 300, useful_life: 5)}
      let!(:cost3) { Cost.create(managed_asset_id: managed_asset3.id, purchase_price: 1500, salvage: 300, useful_life: 5)}

      it "should return the correct calculations using straight line depreciation" do
        get :show, params: { asset_id: managed_asset1.id, format: :json }

        json_response = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(json_response["yearly_values"]).to eq([1500, 1260, 1020, 780, 540, 300])
      end

      # it "should return the correct calculations using declining balance" do
      #   get :show, params: { asset_id: managed_asset2.id, format: :json }

      #   json_response = JSON.parse(response.body)
      #   expect(response).to have_http_status(:ok)
      #   expect(json_response["yearly_values"]).to eq([1500, 1050, 735, 515, 360, 300])
      # end

      # it "should return the correct calculations using sum of years digits" do
      #   get :show, params: { asset_id: managed_asset3.id, format: :json }

      #   json_response = JSON.parse(response.body)
      #   expect(response).to have_http_status(:ok)
      #   expect(json_response["yearly_values"]).to eq([1500, 1100, 780, 540, 380, 300])
      # end
    end
  end
end
