require 'rails_helper'
include CompanyUserHelper

RSpec.describe ManagedAssetManufacturersController, type: :controller do
  create_company_and_user

  let!(:asset1) { FactoryBot.create(:managed_asset, company: company, manufacturer: 'Dell') }
  let!(:asset2) { FactoryBot.create(:managed_asset, company: company, manufacturer: 'HP') }
  let!(:asset3) { FactoryBot.create(:managed_asset, company: company, manufacturer: 'Lenovo') }

  describe 'GET #index' do
    it 'return all manufacturers of a assets in a company' do
      get :index

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)
      company.managed_assets.each do |asset|
        expect(result['manufacturers'].find { |manuf| manuf['name'] == asset['manufacturer'] }).to be_present
      end
      expect(result['count'] == company.managed_assets.pluck(:manufacturer).count).to be_present
    end
  end
end
