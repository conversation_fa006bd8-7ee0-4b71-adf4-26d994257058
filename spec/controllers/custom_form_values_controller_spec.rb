require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper
include GroupHelper

describe CustomFormValuesController, type: :controller do
  create_company_and_user

  let!(:custom_form) { company.custom_forms.helpdesk.first }
  let!(:user_custom_form) { company.custom_forms.company_user.first }
  let!(:location_field) { user_custom_form.custom_form_fields.find_by(name: 'location') }
  let!(:supervisor_field) { user_custom_form.custom_form_fields.find_by(name: 'supervisor') }

  let!(:company_user1) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago, company: company, custom_form: user_custom_form) }
  let!(:location) { Location.create(custom_form: company.custom_forms.last) }
  let!(:group) {
    group_params = {
      name: 'Group',
      company_id: company.id,
      members: [ company_user1.contributor_id ]
    }
    create_group(group_params)
  }
  let!(:group2) {
    group_params = {
      name: 'Group2',
      company_id: company.id,
      members: [ company_user1.contributor_id ]
    }
    create_group(group_params)
  }
  let!(:help_ticket_params) {
    {
      'Subject' => 'Base Ticket',
      'Created By' => company_user1.contributor_id,
      'Assigned To' => company_user1.contributor_id,
      'Status' => 'Closed',
      'Priority' => 'low',
      'Description' => 'This is a test ticket.',
      'Location' => nil,
      'Attachment' => '1.png',
      'Followers' => nil,
      'Impacted Devices' => nil,
      workspace: company.workspaces.first
    }
  }
  let!(:help_ticket) { create_ticket(help_ticket_params) }
  let!(:subject_field) { custom_form.custom_form_fields.find_by_name('subject') }

  describe "#destroy" do
    it "will return http status 422" do
      assigned_to = custom_form.custom_form_fields.find_by(name: 'assigned_to')
      assigned_to.update(required_to_close: true)
      value = assigned_to.custom_form_values.find_by(module_type: 'HelpTicket', module_id: help_ticket.id)
      json = {
        custom_form_field_id: assigned_to.id,
        value: company_user1.contributor_id
      }.to_json
      delete :destroy, params: {  ticket_id: help_ticket.id, id: value.id, json: json }
      expect(response).to have_http_status(:unprocessable_entity)
    end
  end

  describe "#update" do
    it "will update location form field" do
      params = {
        json: { custom_form_field_id: location_field.id, value: location.id }.to_json,
        company_user_id: company_user1.id,
        format: "json",
        custom_form_value: {}
      }
      request.env['HTTP_REFERER'] = "http://test-company.localhost:3000/company_users/#{company_user1.id}/custom_form_values.json"
      put :create, params: params

      expect(JSON.parse(response.body)['form_value']['value_int']).to eq(location.id)
      activity = company_user1.activities.find_by(activity_type: 'added')
      expect(activity).to be_present
      expect(activity.data['current_value']).to eq(location.id)
      expect(activity.data['activity_label']).to eq("Location")
    end

    it "will update ticket is seen status when a current user assign ticket to himself" do
      assigned_to = help_ticket.custom_form.custom_form_fields.find_by(name: 'assigned_to')
      help_ticket.custom_form_values.find_by(custom_form_field_id: assigned_to.id)&.destroy
      params = {
        json: { custom_form_field_id: assigned_to.id, value: company_user.contributor_id }.to_json,
        ticket_id: help_ticket.id,
        format: "json",
        custom_form_value: {}
      }
      request.env['HTTP_REFERER'] = "http://test-company.localhost:3000/help_tickets/#{help_ticket.id}/custom_form_values.json"
      put :create, params: params

      expect(JSON.parse(response.body)['form_value']['value_int']).to eq(company_user.contributor_id)
      help_ticket.reload
      expect(help_ticket.is_seen).to eq(true)
    end

    it "will update supervisor" do
      params = {
        json: { custom_form_field_id: supervisor_field.id, value: group.contributor_id }.to_json,
        company_user_id: company_user1.id,
        format: "json",
        custom_form_value: {}
      }
      request.env['HTTP_REFERER'] = "http://test-company.localhost:3000/company_users/#{company_user1.id}/custom_form_values.json"
      put :create, params: params

      expect(JSON.parse(response.body)['form_value']['value_int']).to eq(group.contributor_id)
      activity = company_user1.activities.find_by(activity_type: 'added')
      expect(activity).to be_present
      expect(activity.data['current_value']).to eq(group.name)
      expect(activity.data['activity_label']).to eq("Supervisor")
    end

    it "will read and write data to cache, when cache is enabled and then clear the cache when help ticket is updated" do
      key = [name: "ticket", workspace_id: workspace.id, params: "{'page'=>'1', 'per_page'=>'25'}"]

      Rails.cache.write(key, help_ticket)
      cached_value = Rails.cache.read(key)
      expect(Rails.cache.fetch(key)).to be_present

      workspace.company_cache_keys.find_or_create_by(
        key: key,
        workspace_id: workspace.id,
        company_id: company.id
      )
      request.env['HTTP_REFERER'] = "http://test-company.localhost:3000/help_tickets/#{help_ticket.id}/custom_form_values.json"
      put :create, params: {
        json: { custom_form_field_id: subject_field.id, value: "UpdatedTic", name: "subject" }.to_json,
        ticket_id: help_ticket.id,
        format: "json",
        custom_form_value: {},
      }
      expect(Rails.cache.fetch(key)).to be_nil
    end
  end

  describe "#destroy" do
    it "will remove location form field" do
      company.custom_form_values.create!(custom_form: user_custom_form, value_int: location.id, custom_form_field: location_field, module_type: "CompanyUser", company_user: company_user1)
      params = {
        json: { custom_form_field_id: location_field.id, value: location.id }.to_json,
        company_user_id: company_user1.id,
        format: "json",
        custom_form_value: {},
        id: "X"
      }
      delete :destroy, params: params

      activity = company_user1.activities.find_by(activity_type: 'removed')
      expect(activity).to be_present
      expect(activity.data['current_value']).to eq(nil)
      expect(activity.data['activity_label']).to eq("Location")
      expect(location_field.custom_form_values).to eq([])
    end

    it "will remove supervisor" do
      company.custom_form_values.create!(custom_form: user_custom_form, value_int: group2.contributor_id, custom_form_field: supervisor_field, module_type: "CompanyUser", company_user: company_user1)
      params = {
        json: { custom_form_field_id: supervisor_field.id, value: group2.contributor_id }.to_json,
        company_user_id: company_user1.id,
        format: "json",
        id: "X"
      }
      delete :destroy, params: params

      activity = company_user1.activities.find_by(activity_type: 'removed')
      expect(activity).to be_present
      expect(activity.data['current_value']).to eq(nil)
      expect(activity.data['activity_label']).to eq("Supervisor")
      expect(supervisor_field.custom_form_values).to eq([])
    end
  end
end
