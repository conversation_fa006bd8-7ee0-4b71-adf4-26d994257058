require 'rails_helper'
include CompanyUserHelper

RSpec.describe WarrantySummariesController, type: :controller do
  create_company_and_user

  let!(:laptop_asset) { create(:managed_asset, :laptop, company_id: company.id, location_id: company.locations.first.id, company_asset_status_id: ready_to_use_status.id) }
  let!(:mobile_asset) { create(:managed_asset, :mobile, company_id: company.id, location_id: company.locations.first.id, archived: true, warranty_expiration: Date.today + 4.months) }
  let!(:phone_asset) { create(:managed_asset, :phone, company_id: company.id, warranty_expiration: Date.today + 9.months) }
  let!(:printer_asset) { create(:managed_asset, :printer, company_id: company.id, location_id: company.locations.first.id, warranty_expiration: Date.today - 9.months) }
  let!(:router_asset) { create(:managed_asset, :router, company_id: company.id, archived: true, warranty_expiration: Date.today - 4.months) }
  let(:ready_to_use_status) { company.asset_statuses.find_by(name: "Ready to Use") }

  describe "GET #index" do
    it "returns managed assets with expired warranty" do
      get :index, :params => { warranty_status: "expired_warranty", format: :json }

      json_response = JSON.parse(response.body)
      assets_count = ManagedAsset.expired_warranty.count
      expect(response).to have_http_status(:ok)
      expect(json_response['expired_warranty']).to eq(assets_count)
    end

    it "returns managed assets with warranty expiring soon" do
      get :index, :params => { warranty_status: "expiring_warranty", format: :json }

      json_response = JSON.parse(response.body)
      assets_count = ManagedAsset.expiring_warranty.count
      expect(response).to have_http_status(:ok)
      expect(json_response['expiring_warranty']).to eq(assets_count)
    end

    it "returns managed assets with expired warranty and type" do
      get :index, :params => { type: "Printer", warranty_status: "expired_warranty", format: :json }

      json_response = JSON.parse(response.body)
      assets_count = ManagedAsset.expired_warranty.joins(:asset_type).where("company_asset_types.name = (?)", "Printer").count
      expect(response).to have_http_status(:ok)
      expect(json_response['expired_warranty']).to eq(assets_count)
    end

    it "returns managed assets with expired warranty and archived" do
      get :index, :params => { archived: true, warranty_status: "expired_warranty", format: :json }

      json_response = JSON.parse(response.body)
      assets_count = ManagedAsset.archived.expired_warranty.count
      expect(response).to have_http_status(:ok)
      expect(json_response['expired_warranty']).to eq(assets_count)
    end

    it "returns managed assets with expired warranty and search" do
      get :index, :params => { search: "AssetP", warranty_status: "expired_warranty", format: :json }

      json_response = JSON.parse(response.body)
      assets_count = ManagedAsset.expired_warranty.where("managed_assets.name ilike ?", "%AssetP%").count
      expect(response).to have_http_status(:ok)
      expect(json_response['expired_warranty']).to eq(assets_count)
    end

    it 'return managed assets with ready to use status params' do
      get :index, :params => { status: ready_to_use_status.id, format: :json }

      json_response = JSON.parse(response.body)
      expect(json_response['total_warranty']).to eq(1)
    end
  end
end
