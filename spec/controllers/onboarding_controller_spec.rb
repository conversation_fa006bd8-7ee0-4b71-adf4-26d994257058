require 'rails_helper'
require 'json'
include CompanyUserHelper

RSpec.describe OnboardingController, type: :controller do
  create_company_and_user(email: "<EMAIL>")
  let(:company_user) { user.company_users.first }

  describe "#user_validate" do
    context "user already exists" do
      it "should reject the email registration" do
        get :user_validate, params: { email: "<EMAIL>" }
        expect(response).to have_http_status(:conflict)
        expect(JSON.parse(response.body)["reason"]).to eq("exists")
      end 
    end

    context "generic email is attempted" do
      it "should reject the email for registration" do
        get :user_validate, params: { email: "<EMAIL>" }
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)["reason"]).to eq("generic")
      end
    end

    context "success" do
      it "should accept the email for registration" do
        get :user_validate, params: { email: "<EMAIL>" }
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)["id"]).to eq(RegistrationEmails.where(email: "<EMAIL>").pluck(:id).first)
      end
    end
  end
end