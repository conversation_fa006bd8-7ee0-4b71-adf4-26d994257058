require 'rails_helper'
include CompanyUserHelper

describe IntegratedVendorsController, type: :controller do
  create_company_and_user
  let!(:config) { FactoryBot.create(:quickbooks_config, company_id: company.id) }

  let!(:integrated_vendor1) {
    FactoryBot.create(
      :integrated_vendor,
      integrable_id: config.id,
      integrable_type: config.class.name,
      sync_status: false,
      is_new: true
    )
  }
  let!(:integrated_vendor2) {
    FactoryBot.create(
      :integrated_vendor,
      integrable_id: config.id,
      integrable_type: config.class.name,
      sync_status: false,
      is_new: true
    )
  }
  let!(:integrated_vendor3) {
    FactoryBot.create(
      :integrated_vendor,
      integrable_id: config.id,
      integrable_type: config.class.name,
      sync_status: true,
      is_new: false
    )
  }
  let!(:integrated_vendor4) {
    FactoryBot.create(
      :integrated_vendor,
      integrable_id: config.id,
      integrable_type: config.class.name,
      sync_status: true,
      is_new: false
    )
  }
  describe "#index" do
    it "should return ordered integrated vendors" do
      params = { 'integration_name' => 'quickbooks', 'page' => 1 }
      get :index, format: 'json', params: params
      res = JSON.parse(response.body)

      expect(response).to have_http_status(:ok)
      expect(res['integrated_vendors'].first(2).pluck('is_new').uniq).to eq([true])
      expect(res['integrated_vendors'].last(2).pluck('is_new').uniq).to eq([false])
      expect(res['synced_integrated_vendors_ids'].first).to eq(integrated_vendor3.id)
      expect(res['synced_integrated_vendors_ids'].last).to eq(integrated_vendor4.id)
    end
  end
end
