require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

describe BulkHelpTicketsController do
  create_company_and_user

  let!(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
        'Status' => 'In Progress',
        'Priority' => "low",
        'Description' => "What a great day!",
        workspace: company.default_workspace,
      },
      {
        'Subject' => "Boom boom",
        'Created By' => company_user.contributor_id,
        'Status' => 'Open',
        'Priority' => "medium",
        'Description' => "The red robin sits in the tree.",
        workspace: company.default_workspace,
      }
    ]
  }

  describe "#bulk_delete" do
    context 'with existing tickets' do
      context "with ticket ids given" do
        let!(:help_tickets) { help_ticket_params.map{ |p| create_ticket(p) } }

        it "is will bulk delete some help tickets" do
          ticket_ids = HelpTicket.where(company_id: company.id).pluck(:id)
          delete :bulk_delete, params: { ticket_ids: ticket_ids, company_ids: [company.id] }
          expect(response.status).to eq(200)
          expect(HelpTicket.count).to eq(0)
        end
      end

      context "with no ticket ids given" do
        let!(:help_tickets) { help_ticket_params.map{ |p| create_ticket(p) } }

        it "is will bulk delete some help tickets" do
          expect(BulkHelpTicketsDeleteWorker).to receive(:perform_async).with({ "workspace_id"=> company.default_workspace.id })
          post :bulk_delete
          expect(response.status).to eq(200)
        end
      end
    end
  end
end
