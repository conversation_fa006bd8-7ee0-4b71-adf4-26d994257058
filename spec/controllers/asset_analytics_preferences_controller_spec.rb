require 'rails_helper'
include Company<PERSON>ser<PERSON>elper

describe AssetAnalyticsPreferencesController, type: :controller do
  create_company_and_user

  let(:request_data) do
    {
      updated_columns: [
        { name: "company_asset_type_id", friendly_name: "Type", id: 1, active: false },
        { name: "display_name", friendly_name: "Asset", type: "string", id: 2, active: false },
        { name: "source", friendly_name: "Source", type: "integer", id: 3, active: false },
        { name: "asset_tag", friendly_name: "Tags", type: "string", id: 4, active: false },
        { name: "warranty_expiration", friendly_name: "Warranty", type: "string", id:5, active: false }
      ]
    }
  end

  describe "#index" do
    it "will get the customize asset analytics columns" do
      get :index, params: { format: :json }
      parsed_response = JSON.parse(response.body)
      expect(parsed_response["analytics_preference"]).to be_present
    end
  end

  describe "#update" do
    it "will update asset preference columns" do
      put :update, params: { selected_columns: request_data[:updated_columns], format: :json }
      expect(company.asset_preference.analytics_preference).to be_present
      expect(company.asset_preference.analytics_preference.count).to eq(5)
    end
  end
end
