require 'rails_helper'
include CompanyUserHelper

describe GeneralTransactionStatusesController, type: :controller do
  create_company_and_user

  let(:vendor_attributes) do
    {
      company: company
    }
  end

  let!(:vendor) do FactoryBot.create(
    :vendor,
    vendor_attributes.merge(
      category_id: company.categories.first.id,
      name: "Microsoft",
      url: "www.microsoft.com"
    )
    )
  end

  let!(:product) { Product.create(name: "new Product", vendor: vendor, company: company) }

  let!(:general_transaction) { GeneralTransaction.create(product_name: 'new transaction', company: company, is_active: true, amount: 110, transaction_date: Date.today-1.month, status: 0) }

  describe "#update" do
    context "to archive the transaction" do
      before do
        general_transaction.update(vendor: vendor, product: product)
      end

      it "updates the vendor" do
        params = { transactions_ids: [general_transaction.id], status: "archived", transaction_archival_preference: 0, format: :json }
        post :update, params: params

        json_response = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        general_transaction.reload
        expect(general_transaction.status).to eq('archived')
        expect(general_transaction.vendor_id).to eq(nil)
        expect(general_transaction.product_id).to eq(nil)
      end
    end

    context "to unrecognize the transaction" do
      before do
        general_transaction.update(vendor: vendor, product: product)
      end

      it "updates the vendor" do
        params = { transactions_ids: [general_transaction.id], status: "unrecognized", transaction_archival_preference: 0, format: :json }
        post :update, params: params

        json_response = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        general_transaction.reload
        expect(general_transaction.status).to eq('unrecognized')
        expect(general_transaction.vendor_id).to eq(nil)
        expect(general_transaction.product_id).to eq(nil)
      end
    end

    context "to unarchive the transaction" do
      before do
        general_transaction.update(vendor: vendor, product: product)
      end

      it "updates the vendor" do
        params = { transactions_ids: [general_transaction.id], status: "unconfirmed", transaction_archival_preference: 0, format: :json }
        post :update, params: params

        json_response = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        general_transaction.reload
        expect(general_transaction.status).to eq('unconfirmed')
        expect(general_transaction.vendor_id).to eq(nil)
        expect(general_transaction.product_id).to eq(nil)
      end
    end
  end
end