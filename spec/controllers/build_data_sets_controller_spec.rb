require 'rails_helper'
include CompanyUserHelper

RSpec.describe BuildDataSetsController, type: :controller do
  let(:reseller_company) { create(:company) }
  let(:company_build) { create(:company_build, company: reseller_company) }
  let(:build_data_set) { create(:build_data_set, company_build: company_build) }
  create_company_and_user

  before do
    allow(controller).to receive(:scoped_company).and_return(reseller_company)
    allow(controller).to receive(:current_user).and_return(user)
  end

  describe '#show' do
    context 'when the build data set exists' do
      before do
        allow(controller).to receive(:comp_build_data_set).and_return(build_data_set)
      end

      it 'returns the build data set attributes' do
        get :show, params: { id: company_build.id }
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to include(build_data_set.attributes.except('people', 'groups', 'created_at', 'updated_at', 'company_build_id').stringify_keys)
      end
    end

    context 'when the build data set does not exist' do
      before do
        allow(controller).to receive(:comp_build_data_set).and_return(nil)
      end

      it 'returns null' do
        get :show, params: { id: company_build.id }
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(nil)
      end
    end
  end

  describe '#create' do
    let(:valid_params) do 
      {
        build_data_sets: {
          people: ['John Doe'],  
          groups: [],
          categories: [],
          custom_forms: [],
          automated_tasks: [],
          asset_types: [],
          responses: [],
          documents: [],
          faqs: [],
          blocked_keywords: []
        },
        company_build_id: company_build.id 
      }
    end

    context 'with valid parameters' do
      it 'creates a new build data set' do
        expect {
          post :create, params: valid_params
        }.to change(BuildDataSet, :count).by(1)
        expect(response).to have_http_status(:ok)
        expect(response.body).to eq(BuildDataSet.last.id.to_s)
      end
    end
  end

  describe '#update' do
    let(:build_params) { { build_data_sets: { people: ['new_person'] } } }

    context 'when the build data set exists' do
      before do
        allow(controller).to receive(:comp_build_data_set).and_return(build_data_set)
      end

      it 'updates the build data set' do
        patch :update, params: { id: build_data_set.id }.merge(build_params)
        expect(response).to have_http_status(:ok)
      end

      it 'returns errors when update fails' do
        allow(controller).to receive(:comp_build_data_set).and_return(build_data_set)
        allow(build_data_set).to receive(:update).and_return(false)
        allow(build_data_set).to receive(:errors).and_return(double(full_messages: ['Update failed']))

        patch :update, params: { id: build_data_set.id }.merge(build_params)
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end
end
