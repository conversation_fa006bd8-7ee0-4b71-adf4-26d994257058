require 'rails_helper'
include CompanyUserHelper

RSpec.describe ArchivedVendorsController, type: :controller do
  create_company_and_user

  describe "#index" do
    context "with no archived vendors present" do
      let!(:unarchived_vendor) { FactoryBot.create(:vendor, company: company, name: "Vendor1") }

      it "does not return a vendor when the name does not match any archived vendors" do
        get :index, params: { name: "vendor1", format: :json }

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)["present"]).to eq(false)
      end
    end

    context "with unarchived vendors present" do
      let!(:archived_vendor) { FactoryBot.create(:vendor, company: company, name: "Vendor2", archived: true) }

      it "returns a vendor when the name matches an archived vendor regardless of casing" do
        get :index, params: { name: "vEnDoR2", format: :json }

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)["present"]).to eq(true)
        expect(JSON.parse(response.body)["id"]).to eq(archived_vendor.id)
      end
    end
  end
end