require 'rails_helper'
include Company<PERSON>ser<PERSON>elper

describe GeneralTransactionLocationsController, type: :controller do
  create_company_and_user

  let!(:general_transaction1) { create(:general_transaction, transaction_date: Date.today - 2.days, company_id: company.id)}
  let!(:general_transaction2) { create(:general_transaction, transaction_date: Date.today - 4.days, company_id: company.id)}

  describe "#update" do
    context "when updating several transactions to an existing location" do
      it "will update the all of their locations" do
        params = { transaction_ids: [general_transaction1.id, general_transaction2.id], location_id: company.locations.first.id }
        put :update, params: params

        expect(response).to have_http_status(:ok)
        transaction1 = company.general_transactions.find_by(id: general_transaction1.id)
        transaction2 = company.general_transactions.find_by(id: general_transaction2.id)
        expect(transaction1.location_id).to eq(company.locations.first.id)
        expect(transaction2.location_id).to eq(company.locations.first.id)
      end
    end

    context "when updating several transactions to a blank location" do
    before do
        general_transaction1.update(location_id: company.locations.first.id)
        general_transaction2.update(location_id: company.locations.first.id)
    end

      it "will update the all of their locations to nil" do
        params = { transaction_ids: [general_transaction1.id, general_transaction2.id], location_id: "null" }
        put :update, params: params

        expect(response).to have_http_status(:ok)
        transaction1 = company.general_transactions.find_by(id: general_transaction1.id)
        transaction2 = company.general_transactions.find_by(id: general_transaction2.id)
        expect(transaction1.location_id).to eq(nil)
        expect(transaction2.location_id).to eq(nil)
      end
    end
  end
end