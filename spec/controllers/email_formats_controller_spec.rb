require 'rails_helper'
include CompanyUserHelper

RSpec.describe EmailFormatsController, type: :controller do
  create_company_and_user

  let(:header_attachment1) { Rack::Test::UploadedFile.new('spec/data/laptop.jpg', 'image/jpeg') }
  let(:footer_attachment1) { Rack::Test::UploadedFile.new('spec/data/laptop.jpg', 'image/jpeg') }

  before do
    @email_format = EmailFormat.create(
      header_image: { io: header_attachment1, filename: 'header1.jpeg' },
      header_text: '<h1><!--block-->Header abc</h1>',
      footer_image: footer_attachment1,
      footer_text: '<h1><!--block-->footer</h1>',
      company_id: company.id
    )
  end

  describe "Post #create" do
    let(:header_attachment2) { Rack::Test::UploadedFile.new('spec/data/laptop.jpg', 'image/jpeg') }
    let(:footer_attachment2) { Rack::Test::UploadedFile.new('spec/data/laptop.jpg', 'image/jpeg') }
    it "create email_format" do
      EmailFormat.destroy_all
      post :create, params: { 'email_format' => { 'header_image' => header_attachment2,
                              'header_text' => '<div><!--block-->Salesforce</div>', 'footer_image' => footer_attachment2,
                              'footer_text' => "<div><!--block-->Didn't request this email?&nbsp;<br>&nbsp;Sorry about that! Just ignore this message.</div>" } }
      expect(EmailFormat.where(header_text: '<div><!--block-->Salesforce</div>').count).to eq(1)
    end

    it "does not create email formats email_format" do
      post :create, params: { 'email_format' => { 'header_text' => '<div><!--block-->Salesforce</div>',
                              'footer_text' => "<div><!--block-->Didn't request this email?&nbsp;<br>&nbsp;Sorry about that! Just ignore this message.</div>" } }
      resp = JSON.parse(response.body)
      expect(resp["message"]).to eq("Email format already exists")
    end
  end

  describe "Update #update" do
    let(:new_header_attachment) { Rack::Test::UploadedFile.new('spec/data/laptop.jpg', 'image/jpeg') }
    let(:new_footer_attachment) { Rack::Test::UploadedFile.new('spec/data/laptop.jpg', 'image/jpeg') }
    it "Update email_format" do
      patch :update, params: { 'email_format' => { 'id' => EmailFormat.first.id,
                               'header_image' => new_header_attachment,
                               'header_text' => '<div><!--block-->Salesforce<strong> </strong>Updated</div>',
                               'footer_image' => new_footer_attachment,
                               'footer_text' => "<div><!--block-->Didn't request this email?&nbsp;<br>&nbsp;Sorry about that! Just ignore this message.</div>" },
                               'id' => EmailFormat.first.id }
      expect(EmailFormat.first.header_text).to eq('<div><!--block-->Salesforce<strong> </strong>Updated</div>')
    end
  end

  describe "Get #index" do
    it "get email format" do
      get :index
      expect(response.status).to eq(200)
    end
  end

  describe "preview" do
    it "preview" do
      get :preview, format: 'html'
      expect(response).to render_template("email_formats/preview")
    end
  end
end
