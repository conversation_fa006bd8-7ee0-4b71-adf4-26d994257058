require 'rails_helper'
include Company<PERSON>ser<PERSON>elper

describe UsersController, type: :controller do
  create_company_and_user

  let!(:domain_validator) {
    allow_any_instance_of(DomainValidator).to receive(:validate).and_return(true)
  }

  let(:user_2_attributes) do
    {
      email: "<EMAIL>",
      password: "password2",
      first_name: "<PERSON>",
      last_name: "<PERSON>",
      terms_of_services: true
    }
  end

  let(:user_3_attributes) do
    {
      email: "<EMAIL>",
      password: "password2",
      first_name: "Abc",
      last_name: "<PERSON>y<PERSON>",
      terms_of_services: true,
      confirmation_token: "mock-confirmation-token",
      temp_email: "<EMAIL>"
    }
  end

  let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
  let!(:user_2) { create(:user, user_2_attributes) }
  let!(:company_user_2) { CompanyUser.create(company_id: company.id, user_id: user_2.id, custom_form_id: custom_form.id) }

  let!(:user_3) { create(:user, user_3_attributes) }
  let!(:company_user_3) { CompanyUser.create(company_id: company.id, user_id: user_3.id, custom_form_id: custom_form.id) }

  describe "#index" do
    context "with format json" do
      it "responds with all users of current company" do
        get :index, :params => { format: :json, :company_id => company.id}

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body).length).to eq(3)
        json = JSON.parse(response.body)
        ids = [
          json[0]['id'],
          json[1]['id']
        ]
        expect(ids).to include(user_2.id)
        expect(ids).to include(user.id)
      end
    end
  end

  describe "#validate_email" do
    context "with format json for proper form validation response" do
      it "responds with false when user is present" do
        get :validate_email, :params => { format: :json, :email => user.email }

        expect(response).to have_http_status(:ok)
        expect(response.body).to eq("false")
      end

      it "responds with true when user is not present" do
        get :validate_email, :params => { format: :json, :email => '<EMAIL>' }

        expect(response).to have_http_status(:ok)
        expect(response.body).to eq("true")
      end
    end
  end

  describe "#update_email" do
    context "with params" do
      it "should update the user's email address" do
        allow_any_instance_of(Aws::CognitoIdentityProvider::Client).to receive(:admin_get_user).and_return(user_3.email)
        put :confirm_new_email, :params => { format: :json, :confirmation_token => user_3['confirmation_token'] }
        user_3.reload
        expect(user_3['email']).to eq("<EMAIL>")
      end
    end
  end
end
