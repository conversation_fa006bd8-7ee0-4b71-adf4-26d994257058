require 'rails_helper'
include CompanyUserHelper

RSpec.describe ManagedAssetOperatingSystemsController, type: :controller do
  create_company_and_user

  let!(:laptop_asset) { FactoryBot.create(:managed_asset, :laptop, company: company ) }
  let!(:phone_asset) { FactoryBot.create(:managed_asset, :phone, company: company ) }

  describe 'GET #index' do
    it 'return all operating system type softwares of assets in a company' do
      get :index, params: { limit: 30, offset: 0 }

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)
      expect(result['count']).to eq(2)
      expected_names = ["MacOS", "PhoneOS"]
      os_names = result['operating_systems'].pluck('name')
      expect(os_names).to include(*expected_names)
    end
  end
end
