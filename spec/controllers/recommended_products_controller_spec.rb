require 'rails_helper'
include CompanyUserHelper

describe RecommendedProductsController, type: :controller do
  create_company_and_user

  context "with a matching product" do
    let!(:product) {
      FactoryBot.create(:product,
                        name: '<PERSON>lack',
                        company: company,
                        vendor: vendor)
    }

    let!(:vendor) {
      FactoryBot.create(:vendor,
                        name: '<PERSON>lack',
                        company: company)
    }

    let(:app) { FactoryBot.create(:integrations_app,
                                  name: 'Slack',
                                  company: company)
    }

    describe "#show" do
      it "should return a product" do
        get :show, params: { id: app.id, format: :json }

        expect(response).to have_http_status(:ok)
        actual = JSON.parse(response.body)
        expect(actual['id']).to eq(product.id)
        expect(actual['name']).to eq(product.name)
      end
    end
  end

  context "without a matching anything" do
    let(:app) { FactoryBot.create(:integrations_app,
                                  name: 'ABCDEDFG',
                                  company: company)
    }

    describe "#show" do
      it "should return a product" do
        get :show, params: { id: app.id, format: :json }

        expect(response).to have_http_status(:ok)
        actual = JSON.parse(response.body)
        expect(actual).to eq({})
      end
    end
  end

  context "with a matching default products" do
    context "with just a method default vendor" do
      let(:app) { FactoryBot.create(:integrations_app,
                                    name: 'O365_BUSINESS_PREMIUM',
                                    company: company)
      }

      let(:default_vendor) { default_product.default_vendor }
      let(:default_product) { DefaultProduct.find_by(name: "Microsoft 365 Business Standard") }

      describe "#show" do
        it "should return a product" do
          get :show, params: { id: app.id, format: :json }

          expect(response).to have_http_status(:ok)
          actual = JSON.parse(response.body)
          expect(actual['id']).to eq(default_product.id)
          expect(actual['name']).to eq(default_product.name)
          expect(actual['vendor']['id']).to eq(default_vendor.id)
          expect(actual['vendor']['type']).to eq('DefaultVendor')
        end
      end
    end

    context "with just a default vendor and a vendor" do
      let(:app) { FactoryBot.create(:integrations_app,
                                    name: 'O365_BUSINESS_PREMIUM',
                                    company: company)
      }

      let!(:vendor) {
        FactoryBot.create(:vendor,
                          name: 'Microsoft',
                          company: company)
      }

      let(:default_product) { DefaultProduct.find_by(name: "Microsoft 365 Business Standard") }
      let(:default_vendor) { default_product.default_vendor }

      describe "#show" do
        it "should return a product" do
          get :show, params: { id: app.id, format: :json }

          expect(response).to have_http_status(:ok)
          actual = JSON.parse(response.body)
          expect(actual['id']).to eq(default_product.id)
          expect(actual['name']).to eq(default_product.name)
          expect(actual['vendor']['id']).to eq(vendor.id)
          expect(actual['vendor']['type']).to eq('Vendor')
        end
      end
    end

    context "with a vendor, product, default vendor and a default product" do
      let(:app) { FactoryBot.create(:integrations_app,
                                    name: 'O365_BUSINESS_PREMIUM',
                                    company: company)
      }

      let!(:vendor) {
        FactoryBot.create(:vendor,
                          name: 'Microsoft',
                          company: company)
      }

      let!(:product) {
        FactoryBot.create(:product,
                          vendor: vendor,
                          name: 'Microsoft 365 Business Standard',
                          default_product: default_product,
                          company: company)
      }

      let(:default_product) { DefaultProduct.find_by(name: "Microsoft 365 Business Standard") }
      let(:default_vendor) { default_product.default_vendor }

      describe "#show" do
        it "should return a product" do
          get :show, params: { id: app.id, format: :json }

          expect(response).to have_http_status(:ok)
          actual = JSON.parse(response.body)
          expect(actual['id']).to eq(product.id)
          expect(actual['name']).to eq(product.name)
          expect(actual['vendor']['id']).to eq(vendor.id)
          expect(actual['vendor']['type']).to eq('Vendor')
        end
      end
    end
  end
end