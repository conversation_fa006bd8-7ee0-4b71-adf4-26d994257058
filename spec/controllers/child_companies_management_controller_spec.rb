require 'rails_helper'
include CompanyUserHelper
include LoginHelper

RSpec.describe ChildCompaniesManagementController, type: :controller do
  create_company_and_user
  let!(:child_company) { Company.create!(name: "Child Company 1", default_logo_url: "http://example.com/child_logo.png", subdomain: "child-company-1", reseller_company_id: company.id) }
  let!(:access_group) { Msp::Templates::Group.create!(name: "Access Group 1", company: company) }
  let!(:company_user) { create(:company_user, company: company) }

  before do
    sign_in user
  end

  describe '#index' do
    it 'returns a list of child companies' do
      get :index, format: :json
      expect(response).to have_http_status(:ok)
      
      json_response = JSON.parse(response.body)
      expect(json_response).to include("child_companies")
      expect(json_response["child_companies"].length).to eq(1)
    end
  end

  describe '#msp_permissions_options' do
    it 'returns the permissions options with members and access groups' do
      get :msp_permissions_options, format: :json
      
      expect(response).to have_http_status(:ok)
      
      json_response = JSON.parse(response.body)
      expect(json_response).to include("permissions_options")
      expect(json_response["permissions_options"]).to be_an(Array)

      member_options = json_response["permissions_options"].select { |option| option["id"] == company_user.id }
      expect(member_options.size).to eq(1)
    end
  end
end
