require 'rails_helper'
include CompanyUserHelper

RSpec.describe VendorInsightsController, type: :controller do
  create_company_and_user

  let!(:vendor1) { create(:vendor, company_id: company.id, category_id: company.categories.first.id, name: "Vendor 1", url: "www.slack.com", is_cloud_platform: true) }
  let!(:vendor2) { create(:vendor, company_id: company.id, category_id: company.categories.second.id, name: "Vendor 2", url: "www.slack2.com", is_cloud_platform: true) }

  let!(:product1) { Product.create(name: "Product 1", vendor: vendor1, company: company) }

  let!(:transaction1) { CloudUsageTransaction.create(company: company, vendor: vendor1, product: product1, transaction_date: Time.now, amount: 100, name: "R<PERSON>", is_manual: false) }
  let!(:transaction2) { CloudUsageTransaction.create(company: company, vendor: vendor1, product: product1, transaction_date: Time.now - 1.month, amount: 200, name: "R<PERSON>", is_manual: false) }
  let!(:transaction3) { CloudUsageTransaction.create(company: company, vendor: vendor1, product: product1, transaction_date: Time.now - 1.month, amount: 150, name: "Elastic Beanstalk", is_manual: false) }
  let!(:transaction4) { CloudUsageTransaction.create(company: company, vendor: vendor1, product: product1, transaction_date: Time.now - 2.months, amount: 570, name: "Elastic Beanstalk", is_manual: false) }

  before do
    allow(controller).to receive(:scoped_company).and_return(company)
  end

  describe 'GET #cloud_usage_graph_data' do
    before do
      get :cloud_usage_graph_data
      @json_response = JSON.parse(response.body)
    end

    it 'should return the correct labels' do
      expect(response).to have_http_status(:ok)
      received_labels = @json_response['labels']
      current_date = Date.current

      expect(received_labels[0]).to eq((current_date - 12.month).strftime("%b '%y"))
      expect(received_labels[1]).to eq((current_date - 11.month).strftime("%b '%y"))
      expect(received_labels[2]).to eq((current_date - 10.month).strftime("%b '%y"))
      expect(received_labels[3]).to eq((current_date - 9.month).strftime("%b '%y"))
      expect(received_labels[4]).to eq((current_date - 8.month).strftime("%b '%y"))
      expect(received_labels[5]).to eq((current_date - 7.month).strftime("%b '%y"))
      expect(received_labels[6]).to eq((current_date - 6.month).strftime("%b '%y"))
      expect(received_labels[7]).to eq((current_date - 5.month).strftime("%b '%y"))
      expect(received_labels[8]).to eq((current_date - 4.month).strftime("%b '%y"))
      expect(received_labels[9]).to eq((current_date - 3.month).strftime("%b '%y"))
      expect(received_labels[10]).to eq((current_date - 2.month).strftime("%b '%y"))
      expect(received_labels[11]).to eq((current_date - 1.month).strftime("%b '%y"))
      expect(received_labels[12]).to eq("This month")
    end

    it 'should return the correct cloud usage data in the response' do
      expect(response).to have_http_status(:ok)
      cloud_usage = @json_response['cloud_usage'][0]

      expect(cloud_usage['label']).to eq(vendor1.name)
      expect(cloud_usage['data'][0]).to eq(transaction1.amount)
      expect(cloud_usage['data'][12]).to eq(transaction1.amount)
      expect(cloud_usage['data'][11]).to eq(transaction2.amount + transaction3.amount)
      expect(cloud_usage['data'][10]).to eq(transaction4.amount)
    end

    it 'should returns the correct cloud insights data in the response' do
      expect(response).to have_http_status(:ok)
      cloud_insights = @json_response['summary_data'][0]

      expect(cloud_insights['id']).to eq(vendor1.id)
      expect(cloud_insights['name']).to eq(vendor1.name)
      expect(cloud_insights['total_cost']).to eq(transaction1.amount + transaction2.amount + transaction3.amount + transaction4.amount)
      expect(cloud_insights["spend_mtd"]).to eq(transaction1.amount)
      expect(cloud_insights["spend_last_month"]).to eq(transaction2.amount + transaction3.amount)
      expect(cloud_insights['products'][0]['id']).to eq(product1.id)
      expect(cloud_insights['products'][0]['name']).to eq(product1.name)
      expect(cloud_insights['products'][0]['vendor_id']).to eq(vendor1.id)
    end
  end

  describe 'GET #subscriptions_summary' do
    let!(:app1) { create(:integrations_app, company_id: company.id, app_type: "licensed", name: "New app 1", status: "linked", vendor_id: vendor1.id) }
    let!(:app2) { create(:integrations_app, company_id: company.id, app_type: "licensed", name: "New app 2", status: "linked", vendor_id: vendor2.id, meta_data: {total_licenses: 1000}) }

    before do
      get :subscriptions_summary
      @json_response = JSON.parse(response.body)
    end

    it 'should returns the correct structure for app1 and empty for app 2' do
      expect(response).to have_http_status(:ok)
      license_usage = @json_response['summary_data'].first["license_usage"]

      expect(license_usage[0]['app']['id']).to eq (app1.id)
      expect(license_usage[0]['app']['name']).to eq (app1.name)
      expect(license_usage[0]['app']['licensed_app']).to eq (true)
      expect(license_usage[0]['app']['status']).to eq (app1.status)
      expect(license_usage[0]['app']['vendor']['id']).to eq (vendor1.id)
      expect(license_usage[0]['app']['vendor']['name']).to eq (vendor1.name)
      expect(license_usage[0]['app']['vendor']['company_id']).to eq (company.id)
      expect(license_usage[0]['app']['vendor']['is_cloud_platform']).to eq (true)
      expect(license_usage[1]).to be_nil
    end
  end

  describe 'GET #apps_insights_summary' do
    let!(:app3) { create(:integrations_app, company_id: company.id, app_type: "licensed", name: "New app 3", status: "linked", vendor_id: vendor1.id, product_id: product1.id) }

    before do
      get :apps_insights_summary
      @json_response = JSON.parse(response.body)
    end

    it 'should returns the summary_data for the app' do
      expect(response).to have_http_status(:ok)
      summary_data = @json_response['summary_data'][0]

      expect(summary_data['id']).to eq (vendor1.id)
      expect(summary_data['name']).to eq (vendor1.name)
      expect(summary_data['apps'][0]['name']).to eq (app3.name)
      expect(summary_data['apps'][0]['licensed_app']).to eq (true)
      expect(summary_data['apps'][0]['status']).to eq (app3.status)
      expect(summary_data["spend_mtd"]).to eq (transaction1.amount)
      expect(summary_data['apps'][0]["vendor"]["id"]).to eq (vendor1.id)
      expect(summary_data['apps'][0]["product"]["id"]).to eq (product1.id)
      expect(summary_data['apps'][0]["product"]["name"]).to eq (product1.name)
      expect(summary_data['apps'][0]["product"]["billing_cycle"]).to eq ("monthly")
      expect(summary_data['apps'][0]["product"]["category_id"]).to eq (product1.category_id)
      expect(summary_data['apps'][0]["app_spend_last_month"]).to eq (transaction2.amount + transaction3.amount)
      expect(summary_data["total_cost"]).to eq (transaction1.amount + transaction2.amount + transaction3.amount + transaction4.amount)
    end
  end
end
