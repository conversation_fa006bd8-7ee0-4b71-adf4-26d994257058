require 'rails_helper'
include Company<PERSON>ser<PERSON>elper

describe FeatureRequestCommentsController, type: :controller do
  create_company_and_user

  let!(:feature_comments_attributes) do
    {
      comment_body: "<div>New comment</div>",
      company_id: company.id,
      creator_id: company_user.id,
      feature_request_id: feature_request.id,
    }
  end

  let(:feature_request) { FactoryBot.create(:feature_request, title: "First feature", creator_id: company_user.id) }

  let!(:company_user_0) { user.company_users.find_by(company_id: company.id) }

  describe "#index" do
    context "with zero feature request comments" do
      it "will return blank array" do
        get :index, params: { feature_request_id: "#{feature_request.id}", format: 'json' }

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['comments']).to eq([])
      end
    end

    context "with some comment's" do
      let!(:feature_request_comment_1) { FactoryBot.create(:feature_request_comment, comment_body: "<div>Comment 1</div>", creator_id: company_user.id, company_id: company.id, feature_request_id: feature_request.id) }
      let!(:feature_request_comment_2) { FactoryBot.create(:feature_request_comment, comment_body: "<div>Comment 2</div>", creator_id: company_user.id, company_id: company.id, feature_request_id: feature_request.id) }

      it "will return records" do
        get :index, params: { feature_request_id: "#{feature_request.id}", format: 'json' }

        expect(response).to have_http_status(:ok)
        comments = JSON.parse(response.body)["comments"]
        expect(comments.count).to eq(2)
        expect(comments.find { |com| com['comment_body'] == '<div>Comment 2</div>' }).to be_present
        expect(comments.find { |com| com['comment_body'] == '<div>Comment 1</div>' }).to be_present
      end
    end

    context "with some comment's" do
      let!(:feature_request_comment_1) { FactoryBot.create(:feature_request_comment, comment_body: "<div>Comment 1</div>", creator_id: company_user.id, company_id: company.id, feature_request_id: feature_request.id) }
      let!(:feature_request_comment_2) { FactoryBot.create(:feature_request_comment, comment_body: "<div>Comment 2</div>", creator_id: company_user.id, company_id: company.id, feature_request_id: feature_request.id) }
      let!(:feature_request_comment_3) { FactoryBot.create(:feature_request_comment, comment_body: "<div>Comment 3</div>", creator_id: company_user_0.id, company_id: company.id, feature_request_id: feature_request.id) }

      it "will return records" do
        get :index, params: { feature_request_id: "#{feature_request.id}", format: 'json' }

        expect(response).to have_http_status(:ok)
        comments = JSON.parse(response.body)["comments"]
        expect(comments.count).to eq(3)
        expect(comments.find { |com| com['comment_body'] == '<div>Comment 3</div>' }).to be_present
        expect(comments.find { |com| com['comment_body'] == '<div>Comment 2</div>' }).to be_present
        expect(comments.find { |com| com['comment_body'] == '<div>Comment 1</div>' }).to be_present
      end
    end
  end

  describe "#create" do
    let!(:subject) { post :create, params: { feature_request_comment: feature_comments_attributes } }

    context "with some attributes" do
      it "will create new feature request comment" do
        expect(response).to have_http_status(:ok)

        feature_comment = FeatureRequestComment.last
        expect(feature_comment.comment_body).to eq("<div>New comment</div>")
      end
    end
  end
end
