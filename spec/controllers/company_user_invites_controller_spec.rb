require 'rails_helper'
require 'sidekiq/testing'
Sidekiq::Testing.fake!
include CompanyUserHelper

describe CompanyUserInvitesController, type: :controller do
  create_company_and_user

  let(:user_1_attributes) do
    {
      email: "<EMAIL>",
      password: "password2",
      first_name: "<PERSON>",
      last_name: "<PERSON>",
      terms_of_services: true
    }
  end

  let(:user_2_attributes) do
    {
      email: "<EMAIL>",
      password: "password2",
      first_name: "<PERSON>",
      last_name: "<PERSON><PERSON>",
      terms_of_services: true
    }
  end

  let(:user_3_attributes) do
    {
      email: "<EMAIL>",
      password: "password2",
      first_name: "<PERSON>",
      last_name: "<PERSON><PERSON>",
      terms_of_services: true
    }
  end

  let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
  let!(:user_1) { create(:user, user_1_attributes) }
  let!(:user_2) { create(:user, user_2_attributes) }
  let!(:company_user_1) { CompanyUser.create(company_id: company.id, user_id: user_1.id, custom_form_id: custom_form.id) }
  let!(:company_user_2) { CompanyUser.create(company_id: company.id, user_id: user_2.id, custom_form_id: custom_form.id) }

  describe "#index" do
    it "sends a mailer to company users" do
      expect(InviteEmailWorker.jobs.size).to eq(0)
      get :index, params: { ids: [company_user_1.id, company_user_2.id], format: :json }

      expect(response).to have_http_status(:ok)
      # Need to uncomment back when we invite user asynchoronously
      # expect(InviteEmailWorker.jobs.size).to eq(1)
    end
  end

  describe "#show" do
    let!(:user_3) { create(:user, user_3_attributes) }
    let!(:company_user_3) { CompanyUser.create(company_id: company.id, user_id: user_3.id, custom_form_id: custom_form.id) }

    it "sends a mailer to single company user" do
      expect(InviteEmailWorker.jobs.size).to eq(0)
      get :show, params: { id: company_user_3.id, format: :json }

      expect(response).to have_http_status(:ok)
      activity = company_user_3.activities.find_by(activity_type: 'invited')
      expect(activity).to be_present
      expect(activity.owner_id).to eq(company_user.id)
      # Need to uncomment back when we invite user asynchoronously
      # expect(InviteEmailWorker.jobs.size).to eq(1)
    end
  end
end
