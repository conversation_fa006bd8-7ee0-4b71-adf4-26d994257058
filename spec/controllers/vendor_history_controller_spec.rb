require 'rails_helper'
include CompanyUserHelper

describe VendorHistoryController, type: :controller do
  create_company_and_user

  before do
    user.company_users.first.update(granted_access_at: Time.local(2018, 9, 1, 0, 5, 0))
  end

  13.times do |idx|
    let("transaction#{idx}") do
      Timecop.freeze(test_time) do
        FactoryBot.create(:general_transaction,
                          amount: 100,
                          vendor: vendor,
                          transaction_date: idx.months.ago,
                          company: company)
      end
    end
  end

  let!(:vendor) { FactoryBot.create(:vendor, name: 'AT&T', company: company) }

  describe "when it is the first of the month" do
    let(:test_time) { Time.local(2019, 9, 1, 7, 0, 0) }
    context "#show" do
      context "when fetching vendor transactions for last 12 months" do
        it "should fetch all company vendors in alphabetical order" do
          Timecop.freeze(test_time) do
            get :show, params: { format: :json, id: vendor.id, filter_type: 'months', date_filter: '12'}
          end

          expect(response).to have_http_status(:ok)
          json_response = JSON.parse(response.body)
          history = json_response['history']
          expect(history.length).to eq(13)
        end
      end

      context "when fetching vendor transactions for 1 calendar year" do
        it "should fetch all company vendors in alphabetical order" do
          Timecop.freeze(test_time) do
            get :show, params: { format: :json, id: vendor.id, filter_type: 'calendar_year', date_filter: '1'}
          end

          expect(response).to have_http_status(:ok)
          json_response = JSON.parse(response.body)
          history = json_response['history']
          expect(history.length).to eq(9)
        end
      end
    end
  end

  describe "when it is in the middle of the month" do
    let(:test_time) { Time.local(2019, 9, 15, 0, 5, 0) }

    context "#show" do
      context "when fetching vendor transactions for last 12 months" do
        it "should fetch all company vendors in alphabetical order" do
          Timecop.freeze(test_time) do
            get :show, params: { format: :json, id: vendor.id, filter_type: 'months', date_filter: '12'}
          end

          expect(response).to have_http_status(:ok)
          json_response = JSON.parse(response.body)
          history = json_response['history']
          expect(history.length).to eq(13)
        end
      end

      context "when fetching vendor transactions for 1 calendar year" do
        it "should fetch all company vendors in alphabetical order" do
          Timecop.freeze(test_time) do
            get :show, params: { format: :json, id: vendor.id, filter_type: 'calendar_year', date_filter: '1'}
          end

          expect(response).to have_http_status(:ok)
          json_response = JSON.parse(response.body)
          history = json_response['history']
          expect(history.length).to eq(9)
        end
      end
    end
  end
end