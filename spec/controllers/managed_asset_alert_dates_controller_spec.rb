require 'rails_helper'
include CompanyUserHelper
include ActiveSupport::Testing::TimeHelpers

RSpec.describe ManagedAssetAlertDatesController, type: :controller do
  create_company_and_user

  let(:test_time) { Time.zone.parse("2022-7-15") }
  let!(:update_user) { company_user.update_column(:granted_access_at, test_time - 1.minute) }
  let!(:managed_asset) {
    travel_to(test_time - 2.minutes) do
      FactoryBot.create(:managed_asset, company: company, alert_dates: [ AlertDate.new(date: Date.today + 6.months, recipients: [company_user.contributor_id]) ] )
    end
  }

  let!(:managed_asset) { FactoryBot.create(:managed_asset, company: company,alert_dates: [AlertDate.new(date: Date.today+6.months, recipients: [company_user.contributor_id])]) }

  before do
    controller.send(:set_privilege)
  end

  describe "#update" do
    context "with alert dates created" do
      it "will not update alert dates if they are before current data" do
        travel_to(test_time) do
          put :update, params: {
            managed_asset:
            { alert_dates_attributes: [
                { date: Date.yesterday },
                { date: Date.today + 3.days },
                { date: Date.today + 5.days }
              ]
            },
            id: managed_asset.id,
            format: :json
          }

          expect(response).to have_http_status(:unprocessable_entity)
          options = JSON.parse(response.body)
          expect(managed_asset.alert_dates.length).to eq(1)
        end
      end

      it "will update alert dates if they are greater than or equal to current date" do
        travel_to(test_time) do
          put :update, params: {
            managed_asset:
            { alert_dates_attributes: [
                { id: managed_asset.alert_dates[0].id, date: Date.today + 2.days, recipients: [company_user.contributor_id] },
                { date: Date.today + 5.days, recipients: [company_user.contributor_id] }
              ]
            },
            id: managed_asset.id,
            format: :json
          }
          expect(response).to have_http_status(:ok)
          expect(managed_asset.alert_dates.count).to eq(2)
          expect(managed_asset.alert_dates.last.recipients).to eq([company_user.contributor_id])
        end
      end
    end
  end
end
