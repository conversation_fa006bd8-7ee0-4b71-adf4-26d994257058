require 'rails_helper'
include Company<PERSON>serHelper

describe GroupsController, type: :controller do
  create_company_and_user

  before do
    Privilege.create(company_user: company_user, name: 'CompanyUser', permission_type: 'write')
    ExpandedPrivileges::Populate.new(company_user.contributor).call
  end

  describe "#destroy" do
    context "with a bad id" do
      it "will return a 404" do
        delete :destroy, params: {id: 'THISISNOTANID'}
        expect(response).to have_http_status(:not_found)
      end
    end

    context "with a good id" do
      let(:group) { create(:group, company: company) }
      let!(:subject) { delete :destroy, params: { id: group.id } }

      it "will remove the company user" do
        expect(Group.where(id: group.id)).to be_blank
      end

      it "will return a 200" do
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe "#update" do
    let(:group) { create(:group, company: company) }
    let(:custom_form) { company.custom_forms.company_user.first }
    let!(:company_user) { create(:company_user, company: company, custom_form: custom_form) }
    let!(:company_user_1) { create(:company_user, company: company, custom_form: custom_form) }

    context "with good params" do
      let(:group_params) {
        {
          id: group.id,
          name: 'New Name',
          permissions: [],
          company_users: [],
          members: [
            {
              name: company_user.full_name,
              id: company_user.contributor_id,
            }
          ]
        }
      }
      let!(:subject) { put :update, params: { id: group.id, group: group_params } }
      it "will update group's attributes" do
        updated_group = Group.find(group.id)
        expect(response).to have_http_status(:ok)
        expect(updated_group.name).to eq("New Name")
      end
    end

    context "copy to child company" do
      let(:group_params) {
        {
          id: group.id,
          name: 'New Name',
          permissions: [],
          company_users: [],
          members: [
            {
              name: company_user.full_name,
              id: company_user.contributor_id,
            },
            {
              name: company_user_1.full_name,
              id: company_user_1.contributor_id,
            }
          ]
        }
      }

      it "will update group's attributes" do
        new_group = Group.create(name: "new group", parent_company_group_id: group.id, company_id: company.id)
        expect(new_group.group_members.count).to eq(0)

        put :update, params: { id: group.id, group: group_params }

        updated_group = Group.find(group.id)
        updated_child_group = Group.find(new_group.id)
        expect(response).to have_http_status(:ok)
        expect(updated_group.group_members.count).to eq(2)
        expect(updated_child_group.group_members.count).to eq(2)
      end
    end
  end

  describe "#create" do
    let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
    let!(:subject) { post :create, params: { group: group_params } }
    let!(:company_user1) { create(:company_user, company: company, custom_form_id: custom_form.id) }
    let!(:company_user2) { create(:company_user, company: company, custom_form_id: custom_form.id) }

    context "with good params" do
      let(:new_group) {  Group.find_by(name: 'New Name') }
      let(:group_params) do
        {
          name: 'New Name',
          permissions: [
            {
              name: 'HelpTicket',
              workspace.id => [ "write" ]
            },
            {
              name: 'CompanyUser',
              permission_types: [ "write" ],
            }
          ],
          members: [
            {
              name: company_user1.full_name,
              id: company_user1.contributor_id,
            },
            {
              name: company_user2.full_name,
              id: company_user2.contributor_id,
            }
          ]
        }
      end

      it "will create a new group" do
        expect(response).to have_http_status(:ok)
        expect(new_group).to be_present
      end

      it "will create company users for group" do
        expect(new_group.group_members.count).to eq(2)
      end

      it "will create privileges for group" do
        expect(new_group.group_members.count).to eq(2)

        priv = new_group.contributor.privileges.find_by(workspace: workspace)
        expect(priv).to be_present
        expect(priv.permission_type).to eq("write")
        expect(priv.name).to eq("HelpTicket")

        priv = new_group.contributor.privileges.find_by(workspace: nil)
        expect(priv).to be_present
        expect(priv.permission_type).to eq("write")
        expect(priv.name).to eq("CompanyUser")
      end
    end

    context "with good params" do
      let(:group_params) {
        {
          name: ""
        }
      }

      it "will create a new group" do
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end
end
