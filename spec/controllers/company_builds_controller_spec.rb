require 'rails_helper'
include CompanyUserHelper

RSpec.describe CompanyBuildsController, type: :controller do
  create_company_and_user

  let(:valid_attributes) { { name: "Test Build", description: "Build Description", company_id: company.id, build_color: "blue" } }
  let(:invalid_attributes) { { name: "", description: "Build Description", company_id: company.id, build_color: "blue" } }
  let!(:company_build) { create(:company_build, valid_attributes) }

  before do
    sign_in user
  end

  describe '#index' do
    it 'returns a success response with company builds' do
      get :index
      expect(response).to have_http_status(:ok)
      parsed_response = JSON.parse(response.body)
      expect(parsed_response).to have_key("company_builds")
      expect(parsed_response["company_builds"].count).to eq(1)
    end
  end

  describe '#create' do
    context 'with valid params' do
      it 'creates a new company build' do
        expect {
          post :create, params: { company_builds: { name: "Test Build #{SecureRandom.hex(4)}", description: "Build Description", company_id: company.id, build_color: "blue" } }
        }.to change(CompanyBuild, :count).by(1)
  
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to include("name", "description")
      end
    end

    context 'with invalid params' do
      it 'does not create a new company build' do
        expect {
          post :create, params: { company_builds: invalid_attributes }
        }.to change(CompanyBuild, :count).by(0)

        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)).to include("message" => include("Name can't be blank"))
      end
    end
  end

  describe '#show' do
    it 'returns the requested company build' do
      get :show, params: { id: company_build.id }
      expect(response).to have_http_status(:ok)
      parsed_response = JSON.parse(response.body)
      expect(parsed_response).to include("name" => company_build.name, "description" => company_build.description)
    end
  end

  describe '#update' do
    context 'with valid params' do
      it 'updates the requested company build' do
        patch :update, params: { id: company_build.id, company_builds: { name: "Updated Build" } }
        company_build.reload
        expect(company_build.name).to eq("Updated Build")
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to include("name" => "Updated Build")
      end
    end

    context 'with invalid params' do
      it 'returns an error message' do
        patch :update, params: { id: company_build.id, company_builds: { name: "" } }
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe '#destroy' do
    it 'destroys the requested company build' do
      company_build
      expect {
        delete :destroy, params: { id: company_build.id }
      }.to change(CompanyBuild, :count).by(-1)

      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)).to be_empty
    end

    it 'returns an error if the build cannot be destroyed' do
      allow_any_instance_of(CompanyBuild).to receive(:destroy).and_return(false)
      delete :destroy, params: { id: company_build.id }
      expect(response).to have_http_status(:unprocessable_entity)
    end
  end
end
