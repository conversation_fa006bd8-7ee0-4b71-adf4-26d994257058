require 'rails_helper'
include <PERSON><PERSON><PERSON><PERSON>elper

describe ProfilesController, type: :controller do
  create_company_and_user

  describe "#update" do
    it "should update user's password with good params" do
      allow_any_instance_of(CognitoService).to receive(:confirm_forgot_password).and_return(true)
      put :update, params: {user: { guid: user.guid, 
                                    password: "abc@12345", 
                                    reset_password_token: "ABC123XYZ", 
                                    confirm_password: "abc@12345"
                                  }, id: user.guid, format: :json }

      expect(response).to  have_http_status(:ok)
      expect(JSON.parse(response.body)['success']).to eq(true)
    end

    it "should not update user's password if password and confirm_password not match" do
      put :update, params: {user: { guid: user.guid, 
                                    password: "abc@12345", 
                                    reset_password_token: "ABC123XYZ", 
                                    confirm_password: "abc@123"
                                  }, id: user.guid, format: :json }

      expect(response).to  have_http_status(:ok)
      expect(JSON.parse(response.body)['success']).to eq(false)
    end

    it "should not update user's password if password or confirm_password are empty" do
      put :update, params: {user: { guid: user.guid,
                                    password: "",
                                    reset_password_token: "ABC123XYZ",
                                    confirm_password: "abc@123"
                                  }, id: user.guid, format: :json }

      expect(response).to  have_http_status(:ok)
      expect(JSON.parse(response.body)['success']).to eq(false)
    end

    it "should not update user's password if reset_password_token is empty" do
      allow_any_instance_of(CognitoService).to receive(:confirm_forgot_password).and_return(false)
      put :update, params: {user: { guid: user.guid,
                                    password: "",
                                    reset_password_token: "ABC123XYZ",
                                    confirm_password: "abc@123"
                                  }, id: user.guid, format: :json }

      expect(response).to  have_http_status(:ok)
      expect(JSON.parse(response.body)['success']).to eq(false)
    end
  end
end
