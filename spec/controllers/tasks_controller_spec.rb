require 'rails_helper'
include CompanyUserHelper

RSpec.describe HelpTickets::TasksController, type: :controller do
  create_company_and_user

  describe "#create" do
    it "creates task" do
      post :create, params: { "task"=>{ "description" => "Task1", "priority" => "low" }}
      expect(workspace.tasks.count).to eq(17)
    end
  end

  describe "#index" do
    before(:each) do
      @task_1 = FactoryBot.create(:task, description: "task_1", priority: "low", company: company, workspace: workspace)
      @task_2 = FactoryBot.create(:task, description: "task_2", priority: "low", company: company, workspace: workspace)
    end

    it "lists tasks" do
      get :index, params: {"page" => "1", "per_page" => "25"}
      expect(response.status).to eq(200)
      result = JSON.parse(response.body)
      tasks = result["tasks"]
      expect(tasks).to be_present
      expect(tasks.count).to eq(18)
    end
  end

  describe "#destroy" do
    before(:each) do
      @task = FactoryBot.create(:task, company: company, workspace: workspace)
    end

    it "destroys task" do
      delete :destroy, params: {"id" => @task.id}
      expect(workspace.tasks.find_by(id: @task.id)).to eq(nil)
      expect(response.status).to eq(200)
    end
  end

  describe "#update" do
    before(:each) do
      @task = FactoryBot.create(:task, company: company, workspace: workspace)
    end

    it "updates task" do
      task_params = {
        "task" => {
          "description" => "Updated Description",
          "priority" => "medium",
          "company_id" => company.id,
          "workspace_id" => workspace.id
        },
        "id"=> @task.id
      }
      put :update, params: task_params
      expect(@task.reload.description).to eq("Updated Description")
    end
  end

  describe "#show" do
    before(:each) do
      @task = FactoryBot.create(:task, company: company, workspace: workspace)
    end

    it "shows task" do
      get :show, params: {"id" => @task.id}
      expect(response.status).to eq(200)
      result = JSON.parse(response.body)
      expect(result["id"]).to eq(@task.id)
      expect(result["description"]).to eq(@task.description)
    end
  end

  describe "#update_order" do
    before(:each) do
      @task_1 = FactoryBot.create(:task, description: "task_1", priority: "low", company: company, workspace: workspace)
      @task_2 = FactoryBot.create(:task, description: "task_2", priority: "low", company: company, workspace: workspace)
    end

    it "updates the order of tasks in checklist" do
      put :order_tasks, params: { "tasks"=>[{"id"=>@task_1, "order"=>1}, {"id"=>@task_2.id, "order"=>0}] }
      expect(response.status).to eq(200)
      expect(@task_2.reload.order).to eq(0)
      expect(@task_1.reload.order).to eq(1)
    end
  end
end
