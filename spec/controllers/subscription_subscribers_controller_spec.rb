require 'rails_helper'
include Company<PERSON>serHelper

describe SubscriptionSubscribersController, type: :controller do
  create_company_and_user

  before do
    SubscriptionPlan.create(name: "Basic Plan", price: 2999, interval: "month", status: "active", stripe_identifier: 'basic-plan')
  end

  let(:results) { OpenStruct.new(id: "1234") }
  let(:stripe_customer) { OpenStruct.new(email: "<EMAIL>") }
  let(:subscription_plan) { SubscriptionPlan.create(name: "Basic Plan", price: 2999, interval: "month", status: "active") }
  let(:stripe_response) { [:ok, results] }

  describe "#update" do
    before do
      Subscription.create(company: company, stripe_subscription_id: "12345", start_date: Date.today, status: Subscription.statuses[:active], subscriber_id: nil, subscription_plan_id: subscription_plan.id)
    end

    let(:updated_results) { OpenStruct.new(id: "12345") }
    let(:updated_stripe_response) { [:ok, updated_results] }

    context "updating the email address of a subscription in Stripe" do
      before do
        expect(controller).to receive(:update_stripe_subscription).and_return(updated_stripe_response)
        allow(Stripe::Customer).to receive(:retrieve).and_return(stripe_customer)
        put :update, params: { subscription_subscriber: { subscriber_id: user.company_users.first.id }, id: company.subscription.id }, format: :json
      end

      xit "sends update_stripe_subscription StripeSubscriptionService" do
        expect(response.status).to eq(200)
        subscription = Subscription.find_by(company_id: company.id)
        expect(subscription.subscriber_id).to eq(user.company_users.first.id)
      end
    end
  end
end