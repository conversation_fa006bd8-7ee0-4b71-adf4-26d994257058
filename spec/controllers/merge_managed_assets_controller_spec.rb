require 'rails_helper'
include CompanyUserHelper

RSpec.describe MergeManagedAssetsController, type: :controller do
  create_company_and_user

  before do
    controller.send(:set_privilege)
  end

  let!(:managed_asset_primary) { FactoryBot.create(:managed_asset, company: company) }
  let!(:managed_asset_secondary) { FactoryBot.create(:managed_asset, company: company) }
  let!(:managed_asset_tertiary) { FactoryBot.create(:managed_asset, company: company) }

  describe 'Selects two assets to merge' do
    let(:params) {
      {
        selected_ids: [managed_asset_primary.id, managed_asset_secondary.id],
        format: :json
      }
    }

    it 'is a successful http request' do
      get :assets_info, :params => params
      expect(response).to have_http_status(:ok)
    end

    it 'returns the right information' do
      get :assets_info, :params => params
      json_response = JSON.parse(response.body)
      expect(json_response['assets_data'].count).to eq(2)
    end
  end

  describe 'No asset is selected' do
    let(:params) { { format: :json } }

    it 'returns an error status' do
      get :assets_info, :params => params
      expect(response).to have_http_status(:unprocessable_entity)
    end

    it 'returns a bad response' do
      get :assets_info, :params => params
      expect(response.message).to eq('Unprocessable Entity')
    end
  end

  describe "Asset that doesn't exist is selected" do
    let(:params) {
      {
        selected_ids: [(SecureRandom.random_number(9e5) + 1e5).to_i, (SecureRandom.random_number(9e5) + 1e5).to_i],
        format: :json
      }
    }

    it 'returns an error status' do
      get :assets_info, :params => params
      expect(response).to have_http_status(:unprocessable_entity)
    end

    it 'returns a bad response' do
      get :assets_info, :params => params
      expect(response.message).to eq('Unprocessable Entity')
    end
  end

  describe 'Assets are merged' do
    let(:params) {
      {
        managed_asset: managed_asset_primary.as_json,
        merged_ids: [managed_asset_primary.id, managed_asset_secondary.id, managed_asset_tertiary.id],
        primary_asset_id: managed_asset_primary.id,
        selected_attributes: [],
        format: :json
      }
    }

    it 'is a successful http request' do
      post :create, :params => params
      expect(response).to have_http_status(:ok)
    end

    it 'returns a merged Asset' do
      post :create, :params => params
      response_data = JSON.parse(response.body)['merged_asset']
      expect(response_data['name']).to eq(managed_asset_primary.name)
      expect(response_data['manufacturer']).to eq(managed_asset_primary.manufacturer)
      expect(response_data['cost']['purchase_price']).to eq(managed_asset_primary.cost.purchase_price)
    end
  end

  describe 'When bad params are sent' do
    let(:params) {
      {
        managed_asset: {},
        merged_ids: [],
        primary_asset_id: nil,
        selected_attributes: [],
        format: :json
      }
    }

    it 'is a 422 http request' do
      post :create, :params => params
      expect(response).to have_http_status(:unprocessable_entity)
    end

    it 'returns an error response' do
      post :create, :params => params
      expect(response.message).to eq('Unprocessable Entity')
    end
  end

  describe 'When assets are not merged' do
    let(:params) { { format: :json } }

    it 'is a 422 http request' do
      post :create, :params => params
      expect(response).to have_http_status(:unprocessable_entity)
    end

    it 'returns an error response' do
      post :create, :params => params
      expect(response.message).to eq('Unprocessable Entity')
    end
  end
end
