require 'rails_helper'
include CompanyUserHelper

describe GamificationController, type: :controller do
  create_company_and_user

  context "#index" do
    context "with a current user" do
      it "should return 200" do
        get :index
        expect(response.status).to eq(200)
      end
    end

    context "without a current user" do
      before do
        expect(controller).to receive(:present_company_user).at_least(:once).and_return(nil)
      end

      it "should return 302" do
        get :index
        expect(response.status).to eq(400)
      end
    end
  end
end