require 'rails_helper'
include Company<PERSON>serHelper

describe Webhooks::Integrations::Helpdesk::MsTeams::HelpTicketCommentsController, type: :controller do
  create_company_and_user

  let!(:ticket) { create(:help_ticket, company: company, ms_teams_message_ids: ['1639404901212'], workspace: company.default_workspace) }
  let!(:config) { create(:ms_teams_config, company: company) }

  describe '#create' do
    it 'will return 404 response', :skip_login do
      params = { help_ticket_comment:
                  { id: '1635408520',
                    body: { content: 'test comment' },
                    reply_to_id: '1639404901212'
                  }
               }

      post :create

      expect(ticket.help_ticket_comments.count).to eq(0)
      expect(response).to have_http_status(:not_found)
    end

    it 'will return 404 response due to missing reply_to_id', :skip_login do
      params = { help_ticket_comment:
                  { id: '1635408520',
                    body: { content: 'test comment' },
                    reply_to_id: '1'
                  },
                 team: { id: config.team_id },
                 channel: { id: config.channel_id },
                 current_user: user.email
               }

      post :create, params: params

      expect(response).to have_http_status(:not_found)
      expect(ticket.help_ticket_comments.count).to eq(0)
      expect(JSON.parse(response.body)['message']).to eq('Sorry, no help ticket found to perform this action.')
    end

    it 'will not create comment due to missing reply_to_id', :skip_login do
      params = { help_ticket_comment:
                  { id: '1635408520',
                    body: { content: 'test comment' },
                    reply_to_id: '1639404901212'
                  },
                 team: { id: config.team_id },
                 channel: { id: config.channel_id },
                 current_user: user.email,
                 privilege_name: 'HelpTicket',
               }

      post :create, params: params

      expect(response).to have_http_status(:ok)
      expect(ticket.help_ticket_comments.count).to eq(1)
      expect(ticket.help_ticket_comments.last.comment_body).to eq('<div><!--block-->test comment</div>')
    end
  end
end
