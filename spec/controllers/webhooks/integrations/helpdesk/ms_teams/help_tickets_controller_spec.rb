require 'rails_helper'
include CompanyUserHelper

describe Webhooks::Integrations::Helpdesk::MsTeams::HelpTicketsController, type: :controller do
  create_company_and_user

  let!(:config) { create(:ms_teams_config, company: company) }
  let(:ticket) { create(:help_ticket, company: company, source: :ms_teams) }

  describe '#create' do
    it 'will create ticket' do
      params = { team: { id: config.team_id },
                 channel: { id: config.channel_id },
                 current_user: user.email,
                 help_ticket: {
                  subject: 'Teams test ticket',
                  status: 'Open',
                  priority: 'high'
                 }
               }
      post :create, params: params
      response_json = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response_json['message']).to eq('Ticket creation has been initiated.')
    end
  end

  # describe '#update' do
  #   it 'will update ticket and create activity' do
  #     params = { team: { id: config.team_id },
  #                channel: { id: config.channel_id },
  #                current_user: user.email,
  #                id: ticket.id,
  #                help_ticket: {
  #                 status: 'Closed',
  #                 priority: 'low',
  #                 assigned_to: company_user.contributor.id,
  #                }
  #              }
  #     put :update, params: params
  #     response_json = JSON.parse(response.body)

  #     expect(response).to have_http_status(:ok)
  #     expect(response_json.keys - ['help_ticket', 'notification_messages']).to eq([])
  #     expect(response_json['help_ticket'].keys - ['priority', 'status', 'assigned_to_name', 'assigned_to_id', 'help_ticket_id', 'ticket_number', 'url', 'fields_options']).to eq([])
  #     expect(response_json['help_ticket']['assigned_to_name']).to eq(user.full_name)
  #     expect(response_json['help_ticket']['assigned_to_id']).to eq(company_user.contributor.id)
  #     expect(response_json['help_ticket']['priority']).to eq('low')
  #     expect(response_json['help_ticket']['status']).to eq('Closed')
  #     expect(response_json['help_ticket']['help_ticket_id']).to eq(company.help_tickets.last.id)
  #     expect(response_json['help_ticket']['ticket_number']).to eq(company.help_tickets.last.ticket_number)
  #     expect(response_json['help_ticket']['fields_options'].keys - ['priority_options', 'status_options', 'contributor_options']).to eq([])
  #     expect(response_json['notification_messages'].keys - ['added_comment', 'updated_fields']).to eq([])
  #     expect(response_json['notification_messages']['added_comment'].present?).to eq(false)
  #     expect(response_json['notification_messages']['updated_fields'].present?).to eq(true)
  #     expect(ticket.help_ticket_activities.count).to eq(3)
  #   end

  #   it 'will add comment on ticket' do
  #     params = { team: { id: config.team_id },
  #                channel: { id: config.channel_id },
  #                current_user: user.email,
  #                id: ticket.id,
  #                help_ticket: { comment: 'Hello from MS Teams' }
  #              }
  #     put :update, params: params
  #     response_json = JSON.parse(response.body)

  #     expect(response).to have_http_status(:ok)
  #     expect(response_json.keys - ['help_ticket', 'notification_messages']).to eq([])
  #     expect(response_json['notification_messages']['added_comment']).to eq('Hello from MS Teams')
  #     expect(response_json['notification_messages']['updated_fields']).to eq('')
  #     expect(ticket.help_ticket_comments.count).to eq(1)
  #   end
  # end
end
