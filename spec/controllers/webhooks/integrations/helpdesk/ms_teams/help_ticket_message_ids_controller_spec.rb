require 'rails_helper'
include CompanyUserHelper

describe Webhooks::Integrations::Helpdesk::MsTeams::HelpTicketMessageIdsController, type: :controller do
  create_company_and_user

  let!(:config) { create(:ms_teams_config, company: company) }
  let(:ticket) { create(:help_ticket, company: company, source: :ms_teams, workspace: company.default_workspace) }

  describe '#update' do
    it 'will update ticket\'s ms_teams_message_ids' do
      params = { team: { id: config.team_id },
                 channel: { id: config.channel_id },
                 id: ticket.id,
                 help_ticket: { message_id: '12345' }
               }
      put :update, params: params
      ticket.reload

      expect(response).to have_http_status(:ok)
      expect(ticket.ms_teams_message_ids).to eq(['12345'])
    end
  end
end
