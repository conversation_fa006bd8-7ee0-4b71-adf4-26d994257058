require 'rails_helper'
include Company<PERSON>ser<PERSON>elper

describe Webhooks::Integrations::Helpdesk::Slack::InteractivityController, type: :controller do
  create_company_and_user

  before {
    allow_any_instance_of(Webhooks::Integrations::Helpdesk::Slack::BaseController).to receive(:authorize_request).and_return(true)
    allow_any_instance_of(Integrations::Slack::Client).to receive(:get_user_email).and_return('<EMAIL>')
    allow_any_instance_of(Slack::Web::Client).to receive(:chat_postEphemeral).and_return(true)
    allow_any_instance_of(Slack::Web::Client).to receive(:auth_test).and_return(true)
  }

  let(:config) { create(:slack_config, company: company, workspace: workspace ) }
  let(:help_ticket) { create(:help_ticket, company: company, workspace: workspace) }
  let(:params) {
    {
      type: 'block_actions',
      user: {
        id: 'U04AQ4933FT',
        username: 'john_liam',
        name: 'john_liam',
        team_id: config.team_id,
      },
      actions: [{
        action_id: 'help_message',
        text: {
          type: 'plain_text',
          text: 'Contact Support'
        },
        value: {
          type: 'help_message',
          company_subdomain: company.subdomain
        },
        type: 'button'
      }],
      message: {
        text: 'Genuity help message'
      },
      token: config.access_token,
      channel_id: config.channel_id,
      team_id: config.team_id,
    }
  }

  describe '#call' do
    context 'In absence of action' do
      it 'will return 204 response, will run successfully' do
        post :index, params: params
        expect(response.status).to eq(200)
      end
    end

    context 'create ticket' do
      it 'will return 204 response, when help ticket creation form is requested' do
        params[:actions] = [{
          action_id: 'open_ticket_form',
          text: {
            type: 'plain_text',
            text: 'Create New Ticket',
          },
          value: "{\"type\":\"help_message\",\"company_subdomain\":\"#{company.subdomain}\"}"
        }]
        params[:message] = { text: 'Welcome to Genuity App' }

        post :index, params: { payload: params.to_json }
        expect(response.status).to eq(204)
      end

      let(:ticket_param) {
        {
          type: 'view_submission',
          user: {
            username: 'john_liam',
            name: 'john_liam',
            team_id: config.team_id,
          },
          view: {
            callback_id: 'create_ticket',
            private_metadata: "{\"help_ticket_id\":null,\"channel_id\":\"D04AQ62708H\",\"thread_ts\":null,\"global_shortcut\":true,\"is_dm_ticket\":null}",
            state: {
              values: {
                tYyZx: {
                  plain_text_input_action: {
                    type: 'plain_text_input',
                    value: 'First Ticket from Genuity App'
                  }
                },
                G56: {
                  static_select_action: {
                    type: 'static_select',
                    selected_option: {
                      text: {
                        type: 'plain_text',
                        text: 'low',
                        emoji: true
                      },
                    value: 'low'
                    }
                  }
                },
                cjWo: {
                  plain_text_input_action: {
                    type: 'plain_text_input',
                    value: 'Welcome to Genuity App'
                  }
                }
              }
            },
          },
          token: config.access_token,
          channel_id: config.channel_id,
          team_id: config.team_id,
        }
      }

      it 'will return 200 status code, when ticket creation is initiated' do
        post :index, params: { payload: ticket_param.to_json }
        expect(response.status).to eq(200)
        expect(response.message).to eq('OK')
      end
    end

    context 'Edit help ticket' do
      let!(:edit_ticket_params) {
        {
          type: 'view_submission',
          user: {
            username: 'john_liam',
            name: 'john_liam',
            team_id: config.team_id,
          },
          view: {
            callback_id: 'update_ticket',
            private_metadata: "{\"help_ticket_id\": #{help_ticket.id},\"channel_id\":\"D04AQ62708H\",\"thread_ts\":null,\"global_shortcut\":true,\"is_dm_ticket\":null}",
            state: {
              values: {
                tYyZx: {
                  plain_text_input_action: {
                    type: 'plain_text_input',
                    value: 'Update ticket from genuity app'
                  }
                },
                G56: {
                  static_select_action: {
                    type: 'static_select',
                    selected_option: {
                      text: {
                        type: 'plain_text',
                        text: 'low',
                        emoji: true
                      },
                    value: 'low'
                    }
                  }
                },
              }
            },
          },
          token: config.access_token,
          channel_id: config.channel_id,
          team_id: config.team_id,
        }
      }

      it 'will return 200 status code, when edit ticket form is opened' do
        params[:actions] = [{
          action_id: 'edit_ticket',
          text: {
            type: 'plain_text',
            text: 'Change ticket properties',
          },
          value: "{\"ticket_id\": #{help_ticket.id},\"is_dm_ticket\":false }",
          type: 'button'
        }]

        post :index, params: params
        expect(response.status).to eq(200)
      end

      it 'will return 200 status code, when edit ticket worker is initiated' do
        post :index, params: { payload: edit_ticket_params.to_json }
        expect(response.status).to eq(200)
      end

      it 'will return 200 status code, when edit request for ticket which is not present initated' do
        edit_ticket_params[:view][:private_metadata] = "{\"help_ticket_id\": null,\"channel_id\":\"#{config.channel_id}\",\"thread_ts\":\"1668428586.598109\",\"global_shortcut\":null,\"is_dm_ticket\":false}"
        post :index, params: edit_ticket_params
        expect(response.status).to eq(200)
      end
    end

    context 'Add a comment' do
      before { allow_any_instance_of(Integrations::Slack::Client).to receive(:get_user_email).and_return('<EMAIL>') }

      let!(:comment_params) {
        {
          type: 'view_submission',
          callback_id: 'create_help_ticket_comment',
          user: {
            id: 'U04AQ4933FT',
            username: 'john_liam',
            name: 'john_liam',
            team_id: config.team_id,
          },
          view: {
            private_metadata: "{\"help_ticket_id\": #{ help_ticket.id },\"channel_id\":\"#{ config.channel_id }\",\"thread_ts\":\"1668428586.598109\",\"global_shortcut\":null,\"is_dm_ticket\":false}",
            callback_id: 'create_help_ticket_comment',
            state: {
              value: {
                jE3zz: {
                  value: 'comment on ticket',
                },
              },
            },
          },
          token: config.access_token,
          channel_id: config.channel_id,
          team_id: config.team_id,
        }
      }

      let!(:new_user) { create(:user, email: '<EMAIL>') }
      let!(:company_user) { create(:company_user, user: new_user, company: company, granted_access_at: 1.day.ago) }

      it 'will return 200 status code, when external user is not authorized to add a comment' do
        params[:actions] = [{
          action_id: 'new_comment',
          text: {
            type: 'plain_text',
            text: 'Add comment',
          },
          value: "{\"ticket_id\": #{help_ticket.id},\"is_dm_ticket\":false }",
        }]

        post :index, params: params
        expect(response.status).to eq(200)
      end

      it 'will return 200 status code, when authorized user open comment form' do
        params[:actions] = [{
          action_id: 'new_comment',
          text: {
            type: 'plain_text',
            text: 'Add comment',
          },
          value: "{\"ticket_id\": #{help_ticket.id},\"is_dm_ticket\":false }",
        }]

        post :index, params: params
        expect(response.status).to eq(200)
      end

      it 'will return 200 status code, when initiate comment creation worker' do
        post :index, params: { payload: comment_params.to_json }
        expect(response.status).to eq(200)
      end

      it 'will return 200 status code, when ticket does not exist for relevant comment' do
        comment_params[:view][:private_metadata] = "{\"help_ticket_id\": null,\"channel_id\":\"#{ config.channel_id }\",\"thread_ts\":\"1668428586.598109\",\"global_shortcut\":null,\"is_dm_ticket\":false}"
        post :index, params: comment_params
        expect(response.status).to eq(200)
      end
    end

    context 'Add private comment' do
      before { allow_any_instance_of(Integrations::Slack::Client).to receive(:get_user_email).and_return('<EMAIL>') }
      let!(:prviate_comment_params) {
        {
          type: 'view_submission',
          user: {
            id: 'U04AQ4933FT',
            username: 'john_liam',
            name: 'john_liam',
            team_id: config.team_id,
          },
          view: {
            private_metadata: "{\"help_ticket_id\": #{ help_ticket.id },\"channel_id\":\"#{ config.channel_id }\",\"thread_ts\":\"1668428586.598109\",\"global_shortcut\":null,\"is_dm_ticket\":false}",
            callback_id: 'add_private_comment',
            state: {
              value: {
                jE3zz: {
                  plain_text_input_action: {
                    type: 'plain_text_input',
                    value: 'private commnt on ticket number 5',
                  }
                },
              },
            },
          },
          token: config.access_token,
          channel_id: config.channel_id,
          team_id: config.team_id,
        }
      }

      let!(:new_user) { create(:user, email: '<EMAIL>') }
      let!(:company_user) { create(:company_user, user: new_user, company: company, granted_access_at: 1.day.ago) }

      it 'will return 200 status code, when an unauthorized user is not allowed to add a comment' do
        params[:actions] = [{
          action_id: 'new_private_comment',
          text: {
            type: 'plain_text',
            text: 'Add private comment',
          },
          value: "{\"ticket_id\": #{help_ticket.id},\"is_dm_ticket\":false }",
        }]

        post :index, params: params
        expect(response.status).to eq(200)
      end

      it 'will return 200 status code, when authorized user open private comment form' do
        params[:actions] = [{
          action_id: 'new_private_comment',
          text: {
            type: 'plain_text',
            text: 'Add private comment',
          },
          value: "{\"ticket_id\": #{help_ticket.id},\"is_dm_ticket\":false }",
        }]

        post :index, params: params
        expect(response.status).to eq(200)
      end

      it 'will return 200 status code, when private comment creation is initiated' do
        post :index, params: { payload: prviate_comment_params.to_json }
        expect(response.status).to eq(200)
      end
    end
  end
end
