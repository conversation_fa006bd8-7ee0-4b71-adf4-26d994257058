require 'rails_helper'
include CompanyUserHelper

describe Webhooks::Integrations::Helpdesk::Slack::ContributorOptionsController, type: :controller do
  create_company_and_user
  before { allow_any_instance_of(Webhooks::Integrations::Helpdesk::Slack::BaseController).to receive(:authorize_request).and_return(true) }

  let!(:config) { create(:slack_config, company: company) }
  let!(:helpdesk_custom_email) { create(:helpdesk_custom_email, email: fourth_company_user.email) }
  let!(:help_ticket) do
    ticket = FactoryBot.create(:help_ticket, company: company, workspace: workspace)
    ticket.custom_form.helpdesk_custom_form.update_columns(helpdesk_custom_email_id: helpdesk_custom_email.id)
    ticket
  end

  let!(:first_user) do
    user = FactoryBot.create(:user, first_name: '<PERSON>', last_name: '<PERSON>avi<PERSON>', email: '<EMAIL>')
    user.skip_validations = true
    user.save!(validate: false)
    user
  end

  let!(:second_user) do
    user = FactoryBot.create(:user, first_name: 'Hendy', last_name: '<PERSON>', email: '<EMAIL>')
    user.skip_validations = true
    user.save!(validate: false)
    user
  end

  let!(:third_user) do
    user = FactoryBot.create(:user, first_name: 'Jacob', last_name: 'Kim', email: '<EMAIL>')
    user.skip_validations = true
    user.save!(validate: false)
    user
  end

  let!(:fourth_user) do
    user = FactoryBot.create(:user, first_name: 'Jack', last_name: 'Cannor', email: '<EMAIL>')
    user.skip_validations = true
    user.save!(validate: false)
    user
  end

  let!(:first_company_user) { company.company_users.create(user: first_user) }
  let!(:second_company_user) { company.company_users.create(user: second_user) }
  let!(:third_company_user) { company.company_users.create(user: third_user) }
  let!(:fourth_company_user) { company.company_users.create(user: fourth_user) }

  let!(:first_group) { company.groups.create(name: 'Group henClue') }

  let!(:params) do
    {
      type: 'block_suggestion',
      user: {
        id: 'U04AQ4933FT',
        username: 'john_liam',
        name: 'john_liam',
        team_id: config.team_id,
      },
      container: {
        type: 'view',
        view_id: 'V04DMN0VB5F'
      },
      team: {
        id: config.team_id,
        domain: 'slackintegrat-dmh5412'
      },
      view: {
        callback_id: 'update_ticket',
        private_metadata: "{\"help_ticket_id\": #{help_ticket.id},\"channel_id\":\"D04AQ62708H\",\"thread_ts\":null,\"global_shortcut\":true,\"is_dm_ticket\":null}",
      },
      api_app_id: 'A033BGDFA73',
      action_id: 'assigned_to_multiselect',
      block_id: 'assignees',
      token: config.access_token,
      channel_id: config.channel_id,
    }
  end

  describe '#index' do
    context 'When fetching existing contributors' do
      it 'will return those contributors' do
        params['value'] = 'Hen'
        post :index, params: params

        contributors = JSON.parse(response.body)['options']
        expect(contributors.find { |c| c['value'] == first_company_user.contributor_id.to_s }).to be_present
        expect(contributors.find { |c| c['value'] == second_company_user.contributor_id.to_s }).to be_present
        expect(contributors.find { |c| c['value'] == first_group.contributor_id.to_s }).to be_present
        expect(contributors.count).to eq(3)
        expect(company.contributors.count).to eq(9)
        expect(response.status).to eq(200)
      end
    end

    context 'When fetching non-existing contributors' do
      it 'will return empty array of options' do
        params['value'] = 'aeiou'
        post :index, params: params

        expect(JSON.parse(response.body)['options']).to eq([])
        expect(company.contributors.count).to eq(9)
        expect(response.status).to eq(200)
      end
    end

    context 'When fetching contrinutor whose email is set as custom email in custom form' do
      it 'it will not return that contributor' do
        params['value'] = 'Jack'
        post :index, params: params

        contributors = JSON.parse(response.body)['options']
        expect(contributors.find { |c| c['value'] == fourth_company_user.contributor_id.to_s }).to be_blank
        expect(response.status).to eq(200)
      end
    end
  end
end
