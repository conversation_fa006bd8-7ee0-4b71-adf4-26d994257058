require 'rails_helper'
include CompanyUserHelper

describe Webhooks::Integrations::Helpdesk::Slack::EventsController, type: :controller do
  create_company_and_user
  before { allow_any_instance_of(Webhooks::Integrations::Helpdesk::Slack::BaseController).to receive(:authorize_request).and_return(true) }

  let(:config) { create(:slack_config, company: company) }
  let(:config2) { create(:slack_config, company: company, channel_id: 'C04B160SQ3K', channel_name: '#tech') }

  let(:params) {
    {
      token: config.access_token,
      team_id: config.team_id,
      event: {
        type: 'app_uninstalled',
        user_id: 'U04B17TV672'
      },
      active: true,
      user_id: 'U04B17TV672'
    }
  }

  describe '#index' do
    context 'Uninstall app' do
      it 'will successully uninstall app' do
        config2
        post :index, params: params

        expect(response.status).to eq(200)
        expect(Integrations::Slack::Config.find_by(channel_id: config.channel_id).active).to be_falsy
        expect(Integrations::Slack::Config.find_by(channel_id: config2.channel_id).active).to be_falsy
      end
    end

    context 'Welcome message' do
      it 'will send welcome message to first time visitor only' do
        params[:event][:type] = 'app_home_opened'
        allow_any_instance_of(Integrations::Slack::Client).to receive(:send_message).and_return(true)
        allow_any_instance_of(Slack::Web::Client).to receive(:auth_test).and_return(true)

        expect(config.dm_visitors.length).to eq(0)
        post :index, params: params
        expect(response.status).to eq(200)
        config.reload
        expect(config.dm_visitors.length).to eq(1)
        expect(config.dm_visitors).to eq(['U04B17TV672'])

        post :index, params: params
        expect(response.status).to eq(200)
        config.reload
        expect(config.dm_visitors.length).to eq(1)
        expect(config.dm_visitors).to eq(['U04B17TV672'])
      end
    end
  end
end
