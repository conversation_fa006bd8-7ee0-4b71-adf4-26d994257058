require 'rails_helper'
include CompanyUserHelper

describe Webhooks::Integrations::Helpdesk::Slack::HelpTicketsController, type: :controller do
  create_company_and_user
  before { allow_any_instance_of(Webhooks::Integrations::Helpdesk::Slack::BaseController).to receive(:authorize_request).and_return(true) }

  let(:config) { create(:slack_config, company: company, workspace: workspace) }
  let(:params) {
    {
      user: {
        id: 'U04AQ4933FT',
        username: 'john_liam',
        name: 'john_liam',
        team_id: config.team_id,
      },
      command: '/genuity',
      text: 'help',
      token: config.access_token,
      channel_id: config.channel_id,
      team_id: config.team_id
    }
  }

  describe '#new' do
    context 'slash command for creating help ticket' do
      before {
        Timecop.freeze(DateTime.now)
        request_body = "channel_id=C04B160SQ3S&command=%2Fgenuity&team_id=T04B160RHNC&text=create&token=mock-token&user%5Bid%5D=U04AQ4933FT&user%5Bname%5D=john_liam&user%5Bteam_id%5D=T04B160RHNC&user%5Busername%5D=john_liam"
        timestamp = DateTime.now.to_i.to_s
        request.headers['X-Slack-Request-Timestamp'] = timestamp
        sig_basestring = "v0:#{timestamp}:#{request_body}"
        hash_bytes = OpenSSL::HMAC.hexdigest("SHA256", Rails.application.credentials.slack[:signing_secret], sig_basestring)
        my_signature = "v0=#{hash_bytes}"
        request.headers['X-Slack-Signature'] = my_signature
        allow_any_instance_of(Slack::Web::Client).to receive(:auth_test).and_return(true)
        allow_any_instance_of(Slack::Web::Client).to receive(:chat_postEphemeral).and_return(true)
      }

      it 'will return 200 response, when create ticket command is executed' do
        params[:text] = 'create'

        post :new, params: params
        expect(response.status).to eq(200)
      end
    end

    context 'slash command for help message' do
      before {
        allow_any_instance_of(Slack::Web::Client).to receive(:auth_test).and_return(true)
        allow_any_instance_of(Slack::Web::Client).to receive(:chat_postEphemeral).and_return(true)
      }

      it 'will return 200 response, when help message command is executed' do
        post :new, params: params
        expect(response.status).to eq(200)
      end
    end

    context 'rescue blocks' do
      it 'will return 422 response, when an unexpected error occurs' do
        allow_any_instance_of(Slack::Web::Client).to receive(:chat_postEphemeral).and_raise(NoMethodError, 'method_not_found')

        post :new, params: params
        expect(response.status).to eq(422)
      end

      it 'will return 200 response, when an private channel is not found' do
        allow_any_instance_of(Slack::Web::Client).to receive(:auth_test).and_return(true)
        allow_any_instance_of(Slack::Web::Client).to receive(:chat_postEphemeral).and_raise(Slack::Web::Api::Errors::SlackError, 'channel_not_found')

        post :new, params: params
        expect(response.status).to eq(200)
      end
    end
  end
end
