require 'rails_helper'
include Company<PERSON>serHelper

describe CompanyUserArchivalsController, type: :controller do
  create_company_and_user

  let!(:company_user2) { create(:company_user, granted_access_at: 1.day.ago, company_id: company.id) }

  before do
    company_user2.activities.destroy_all
  end

  describe "#update" do
    it "will update archive company user" do
      put :update, params: { id: company_user2.id }
      company_user2.reload

      activity = company_user2.activities.find_by(activity_type: 'archived')
      expect(activity).to be_present
      expect(activity.data['current_value']).to eq("archived")
      expect(company_user2.archived_at).to be_present
      expect(JSON.parse(response.body)['message']).to eq("User archived successfully.")
    end

    it "will update unarchive company user" do
      company_user2.update_columns(archived_at: Time.now)
      company_user2.activities.destroy_all
      delete :destroy, params: { id: company_user2.id }
      company_user2.reload

      activity = company_user2.activities.find_by(activity_type: 'archived')
      expect(activity).to be_present
      expect(activity.data['current_value']).to eq("unarchived")
      expect(company_user2.archived_at).to be_nil
      expect(JSON.parse(response.body)['message']).to eq("User restored successfully.")
    end
  end
end
