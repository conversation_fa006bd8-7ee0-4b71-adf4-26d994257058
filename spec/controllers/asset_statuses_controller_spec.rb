require 'rails_helper'
include CompanyUserHelper

RSpec.describe AssetStatusesController, type: :controller do
  create_company_and_user

  let(:default_asset_statuses) do
    [
      {"name"=>"In Use", "icon"=>"genuicon-checkmark"},
      {"name"=>"Ready to Use", "icon"=>"nulodgicon-arrow-right-b"},
      {"name"=>"Needs Attention", "icon"=>"genuicon-exclamation"},
      {"name"=>"Unusable", "icon"=>"&times;"}
    ]
  end

  before do
    controller.send(:set_privilege)
  end

  describe 'GET #index' do
    it 'return all default asset statuses' do
      get :index
      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)
      default_asset_statuses.each do |status|
        expect(result.find { |res| res['name'] == status['name'] && res['icon'] == status['icon'] }).to be_present
      end
    end
  end
end
