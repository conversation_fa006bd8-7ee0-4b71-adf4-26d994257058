require 'rails_helper'

include CompanyUserHelper

RSpec.describe CategoriesController, type: :controller do
  create_company_and_user
  let(:category_for_contract) { FactoryBot.create(:category, company: company, name: 'Category for Contract') }
  let(:create_contract) { FactoryBot.create(:contract, name: 'contract1', company: company, category_id: category_for_contract.id) }

  describe "#index" do
    it "returns a list of all company categories" do
      get :index

      parsed_response = JSON.parse(response.body)

      expect(parsed_response.size).to eq(19)
      expect(response).to have_http_status(:ok)
    end

    it "returns a list of all company categories with some new categories" do
      category_1 = Category.create(name: "Category 1", company_id: company.id)
      category_2 = Category.create(name: "Category 2", company_id: company.id)
      get :index

      parsed_response = JSON.parse(response.body)
      expect(parsed_response.size).to eq(21)
      expect(response).to have_http_status(:ok)
    end
  end

  describe "#create" do
    it "creates customs categories for company" do
      post :create, params: { category: { name: 'Custom Category 1'} }

      parsed_response = JSON.parse(response.body)
      expect(parsed_response['categories'].size).to eq(20)
      expect(response).to have_http_status(:ok)
    end

    it "does not create duplicate categories" do
      post :create, params: { category: { name: 'HR'} }

      parsed_response = JSON.parse(response.body)
      expect(parsed_response['message']).to eq("Name has already been taken")
      expect(response).to have_http_status(:unprocessable_entity)
    end
  end

  describe "#destroy" do
    it "destroy categories for company" do
      category_1 = Category.create(name: "Category 1", company_id: company.id)
      delete :destroy, params: { id: category_1.id }

      parsed_response = JSON.parse(response.body)
      expect(parsed_response['categories'].size).to eq(19)
      expect(response).to have_http_status(:ok)
    end

    it "does not destroy categories with associated records for company" do
      create_contract
      delete :destroy, params: { id: category_for_contract.id }

      parsed_response = JSON.parse(response.body)
      expect(parsed_response['associations'].present?).to eq(true)
      expect(company.categories.size).to eq(20)
      expect(response).to have_http_status(:ok)
    end
  end
end
