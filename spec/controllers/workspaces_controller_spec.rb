require 'rails_helper'
include CompanyUserHelper

RSpec.describe WorkspacesController, type: :controller do
  create_company_and_user

  let(:workspace) { FactoryBot.create(:workspace, company: company, name: 'Workspace') }
  let(:workspace2) { FactoryBot.create(:workspace, company: company, name: 'Workspace 2') }

  describe '#set_default_workspace' do
    context 'should set the default workspace' do
      it 'to current company user' do
        params = { workspace: "{\"id\":#{workspace.id},\"name\":\"#{workspace.name}\"}", user_id: user.id, company_id: company.id, format: :json }
        put :set_default_workspace, params: params
        default_workspace_id = CompanyUser.find_by(user_id: user.id).default_workspace_id
        expect(response.status).to eq(200)
        expect(default_workspace_id).to eq(workspace.id)

        params = { workspace: "{\"id\":#{workspace2.id},\"name\":\"#{workspace2.name}\"}", user_id: user.id, company_id: company.id, format: :json }
        put :set_default_workspace, params: params
        default_workspace_id = CompanyUser.find_by(user_id: user.id).default_workspace_id
        expect(response.status).to eq(200)
        expect(default_workspace_id).to eq(workspace2.id)
      end
    end
  end

  describe '#unset_default_workspace' do
    context 'should unset the default workspace' do
      it 'to current company user' do
        params = { workspace: "{\"id\":#{workspace.id},\"name\":\"#{workspace.name}\"}", user_id: user.id, company_id: company.id, format: :json }
        put :set_default_workspace, params: params
        default_workspace_id = CompanyUser.find_by(user_id: user.id).default_workspace_id
        expect(response.status).to eq(200)
        expect(default_workspace_id).to eq(workspace.id)

        put :unset_default_workspace
        default_workspace_id = CompanyUser.find_by(user_id: user.id).default_workspace_id
        expect(response.status).to eq(200)
        expect(default_workspace_id).to eq(nil)
      end
    end
  end
end
