require 'rails_helper'
include CompanyUserHelper
include LoginHelper

RSpec.describe CompaniesController, type: :controller do
  create_company_and_user

  let(:everyone) { company.groups.find_by(name: 'Everyone') }
  let!(:company_user1) { create(:company_user, company: company) }
  let!(:company_user2) { create(:company_user, company: company) }
  let!(:company_user3) { create(:company_user, company: company) }

  describe "#ensure_free_trial_or_subscribed" do
    let(:company_params) do
      {
        name: "Child company 1",
        subdomain: "child-company-1",
        is_reseller_company: false,
        reseller_company_id: company.id,
        default_logo_url: "default_logo_url"
      }
    end

    context "to company has no subscription or free trial" do
      xit "will redirects to billing page to admins and users" do
        company.update_column("free_trial_days", -1)
        get :dashboard

        expect(response).to have_http_status(:found)
        expect(response.redirect_url).to eq(company_billing_payment_methods_url)
      end
    end

    context "to company has no expiring subscription" do
      xit "will redirects to billing page to admins and users" do
        company.update_column("free_trial_days", -1)
        Subscription.create(company_id: company.id, end_date: Date.today - 2.days, status: 'canceled')
        get :dashboard

        expect(response).to have_http_status(:found)
        expect(response.redirect_url).to eq(company_billing_payment_methods_url)
      end
    end

    context "to company has no subscription or free trial" do
      it "will not redirects to billing page to super admins" do
        company.update_column("free_trial_days", 0)
        user.update_column("super_admin", true)
        get :dashboard

        expect(response).to have_http_status(:ok)
        expect(response.redirect_url).not_to eq(company_billing_url)
      end
    end

    context "with a user with just one module" do
      before { login_user(simple_user) }

      let(:simple_company_user) { create(:company_user, company: company) }
      let(:simple_user) {
        ExpandedPrivileges::Populate.new(simple_company_user.contributor).call
        simple_company_user.user
      }

      it "will redirect to help desk" do
        company.update_column("free_trial_days", 30)
        get :dashboard

        expect(response).to have_http_status(:found)
        expect(response.redirect_url).to eq('http://test-company.localhost/help_tickets/end_user_dashboard')
      end
    end

    context "with a user with an admin" do
      it "to admins and users" do
        company.update_column("free_trial_days", 30)
        get :dashboard

        expect(response).to have_http_status(:ok)
        expect(response.redirect_url).to be_blank
      end
    end

    context "when the parent has no subscription" do
      it "passes the remaining parent free trial days to the child company" do
        parent_free_trial_days = 20
        company.update_column("free_trial_days", parent_free_trial_days)
        post :create, params: { company: company_params }

        expect(response.status).to eq(200)
        res = JSON.parse(response.body)
        child_company = Company.find(res["company"]["id"])
        expect(child_company.free_trial_days).to eq(parent_free_trial_days)
      end
    end

    context "when the parent has a subscription" do
      it "sets the child company's free trial days to default" do
        Subscription.create!(company_id: company.id, subscription_plan_id: 2, status: 'active') 
        post :create, params: { company: company_params }

        expect(response.status).to eq(200)
        res = JSON.parse(response.body)
        child_company = Company.find(res["company"]["id"])
        expect(child_company.free_trial_days).to eq(30)
      end
    end
  end

  describe "#copy_admins" do
    before do
      new_group = company.groups.create(name: Faker::Name.name)
      new_group.group_members.find_or_create_by(contributor_id: company_user2.contributor_id)
      new_group.group_members.find_or_create_by(contributor_id: company_user3.contributor_id)

      admin_group = company.groups.find_by(name: "Admins")
      admin_group.group_members.find_or_create_by(contributor_id: company_user1.contributor_id)
      admin_group.group_members.find_or_create_by(contributor_id: new_group.contributor_id)
    end

    context "copy all admin users of parent company to child company" do
      let(:company_params) do
        {
          name: "Child company 1",
          subdomain: "child-company-1",
          is_reseller_company: false,
          reseller_company_id: company.id,
          default_logo_url: "default_logo_url"
        }
      end
      it "will copy all admins group members to child company & add it into child's admin group" do
        post :create, params: { company: company_params }
        expect(response.status).to eq(200)
        res = JSON.parse(response.body)
        child_company = Company.find(res["company"]["id"])
        child_admin_group = child_company.groups.find_by(name: "Admins")
        child_admins = child_admin_group.group_members.map{|grp| grp.contributor.contributor_type}
        expect(child_company.company_users.count).to eq(4)
        expect(child_admin_group.group_members.count).to eq(3)
        expect(child_admins).to include("Group")
      end
    end
  end

  describe '#get_company_template' do
    context 'user without module permissions' do
      before do
        login_user(user_one.user)
        get :get_company_template, format: :json
      end

      let(:user_one) { create(:company_user, company: company) }

      it 'will not allow user to access the page' do
        expect(response.status).to eq(401)
        expect(JSON.parse(response.body)['message']).to eq("Sorry, you're not authorized to perform this action.")
      end
    end

    context 'user with module permissions' do
      before do
        login_user(company_user.user)
        get :get_company_template, format: :html
      end

      it 'will allow user to access the page' do
        expect(response.status).to eq(200)
      end
    end
  end
end
