require 'rails_helper'
include <PERSON><PERSON><PERSON><PERSON>el<PERSON>

describe EventLogsController, type: :controller do
  create_company_and_user

  let!(:custom_form) {
    FactoryBot.create(
      :custom_form,
      company_id: company.id,
      company_module: 'helpdesk',
      is_active: false,
      default: false
    )
  }

  let!(:custom_form2) {
    FactoryBot.create(
      :custom_form,
      form_name: 'Second Form',
      company_id: company.id,
      company_module: 'helpdesk',
      is_active: false,
      default: false
    )
  }

  let(:automated_task) { AutomatedTasks::AutomatedTask.create(workspace: workspace) }

  let!(:event_log) {
    EventLog.create(
      activity_type: 'created',
      company_id: company.id,
      workspace_id: workspace.id,
      entity_id: custom_form.id,
      entity_type: 'CustomForm',
      module_name: 'helpdesk',
      owner_id: company_user.id,
      data:{ form_id: custom_form.id }
    )
  }

  let!(:event_log2) {
    EventLog.create(
      activity_type: 'deleted',
      company_id: company.id,
      workspace_id: workspace.id,
      entity_id: custom_form2.id,
      entity_type: 'CustomForm',
      module_name: 'helpdesk',
      owner_id: company_user.id,
      data:{ form_id: custom_form2.id }
    )
  }

  let!(:automated_task_log) {
    EventLog.create(
      activity_type: 'created',
      company_id: company.id,
      workspace_id: workspace.id,
      entity_id: automated_task.id,
      entity_type: 'AutomatedTask',
      module_name: 'helpdesk',
      owner_id: company_user.id,
      data:{ form_id: automated_task.id }
    )
  }

  let!(:event_log_user1) {
    EventLog.create(
      activity_type: 'deleted',
      company_id: company.id,
      entity_id: custom_form.id,
      entity_type: 'CustomForm',
      module_name: 'company_user',
      owner_id: company_user.id,
      data:{ form_id: custom_form.id }
    )
  }

  let!(:event_log_user2) {
    EventLog.create(
      activity_type: 'deleted',
      company_id: company.id,
      entity_id: custom_form2.id,
      entity_type: 'CustomForm',
      module_name: 'company_user',
      owner_id: company_user.id,
      data:{ form_id: custom_form2.id }
    )
  }

  describe "#index" do
    it "should return helpdesk event logs" do
      params = {
        'filter' => 'helpdesk',
        'activity_type' => 'CustomForm',
        'changed_by' => company_user.id
      }
      get :index, format: 'json', params: params
      res = JSON.parse(response.body)
      expect(res['custom_form_activities'].count).to eq(2)
    end

    it "should return automated task event logs" do
      params = {
        'filter' => 'helpdesk',
        'activity_type' => 'AutomatedTask',
        'changed_by' => company_user.id
      }
      get :index, format: 'json', params: params
      res = JSON.parse(response.body)
      expect(res['custom_form_activities'].count).to eq(1)
    end

    it "should return company_user event logs" do
      params = {
        'filter' => 'company_user',
        'activity_type' => 'CustomForm',
        'changed_by' => 'all'
      }
      get :index, format: 'json', params: params
      res = JSON.parse(response.body)
      expect(res['custom_form_activities'].count).to eq(2)
      expect(res['custom_form_activities'].find { |rec| rec['id'] == event_log_user1.id }).to be_present
      expect(res['custom_form_activities'].find { |rec| rec['id'] == event_log_user2.id }).to be_present
    end
  end
end
