require 'rails_helper'
include <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

describe <PERSON><PERSON><PERSON><PERSON><PERSON>Controller, type: :controller do
  create_company_and_user

  let(:telecom_provider) { FactoryBot.create(:telecom_provider, company: company)}
  let!(:telecom_service) { FactoryBot.create(:telecom_service, service_type: "data_service", telecom_provider: telecom_provider, company: company) }
  let!(:telecom_service2) { FactoryBot.create(:telecom_service, service_type: "data_service", telecom_provider: telecom_provider, company: company) }
  let(:ip_address_attributes) { attributes_for(:ip_address).merge(telecom_provider: telecom_provider.as_json, telecom_service_id: telecom_service.id, company_id: company.id) }

  describe "#create" do
    before(:each) do
      params = { ip_address: ip_address_attributes, format: :json }
      post :create, params: params
    end

    it "will create a new IP address record" do
      json_response = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)

      expect(json_response["ip"]["ip"]).to eq(ip_address_attributes[:ip])
      expect(json_response["ip"]["friendly_name"]).to eq(ip_address_attributes[:friendly_name])
      expect(json_response["ip"]["telecom_service_id"]).to eq(telecom_service.id)
      expect(json_response["ip"]["company_id"]).to eq(company.id)
    end

    it "will not create a new IP address record if the same IP exists" do
      params = { ip_address: ip_address_attributes, format: :json }
      post :create, params: params

      json_response = JSON.parse(response.body)
      expect(response).to have_http_status(:bad_request)
      expect(company.ip_addresses.count).to eq(1)
      expect(json_response["message"]).to eq("IP has already been taken")
    end

    it "will not create a new IP address record if the IP is invalid" do
      params = { ip_address: { ip: "**********" }, format: :json }
      post :create, params: params

      json_response = JSON.parse(response.body)
      expect(response).to have_http_status(:bad_request)
      expect(company.ip_addresses.count).to eq(1)
      expect(json_response["message"]).to eq("IP is invalid")
    end
  end

  describe  "#index" do
    let!(:ip_address1) { FactoryBot.create(:ip_address, ip: "************", telecom_provider: telecom_provider, telecom_service: telecom_service, company: company) }
    let!(:ip_address2) { FactoryBot.create(:ip_address, ip: "************", telecom_provider: telecom_provider, telecom_service: telecom_service, company: company) }
    let!(:ip_address3) { FactoryBot.create(:ip_address, ip: "************", telecom_provider: telecom_provider, telecom_service: telecom_service2, company: company) }
    let!(:ip_address4) { FactoryBot.create(:ip_address, ip: "************", telecom_provider: telecom_provider, telecom_service: telecom_service2, company: company, location_id: company.locations.first.id) }

    context "when no sevice param is present" do
      it "will fetch all IP address records for this company" do
        get :index, format: :json

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        res = json_response["ips"]
        expect(res.length).to eq(4)
        expect(res.first["id"]).to eq(ip_address1.id)
        provider = ip_address1.telecom_service.telecom_provider
        expect(res.first["telecom_provider"]).to eq({
          "id" => provider.id,
          "name" => provider.name,
        })
        expect(res.second["id"]).to eq(ip_address2.id)
        expect(res.third["id"]).to eq(ip_address3.id)
        expect(res.fourth["id"]).to eq(ip_address4.id)
      end
    end

    context "when sevice param is present" do
      it "will fetch only phone number records for this company in order by number" do
        get :index, params: { service_id: telecom_service.id, format: :json }

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        res = json_response["ips"]

        expect(res.length).to eq(2)
        expect(res.first["id"]).to eq(ip_address1.id)
        expect(res.second["id"]).to eq(ip_address2.id)
      end
    end

    context "when location param is present" do
      it "will fetch only phone number records for this company in order by number" do
        get :index, params: { location_id: company.locations.first.id, format: :json }

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response["ips"].length).to eq(1)
        expect(json_response["ips"].first["id"]).to eq(ip_address4.id)
      end
    end
  end

  describe  "#update" do
    let!(:ip_address1) { FactoryBot.create(:ip_address, ip: "************", telecom_service: telecom_service, company: company) }
    let!(:ip_address2) { FactoryBot.create(:ip_address, ip: "************", telecom_service: telecom_service, company: company) }

    context "with proper params" do
      it "updates the IP address when IP is not a duplicate" do
        params = { id: ip_address1.id, ip_address: { ip: "42.52.34.290", telecom_provider: telecom_provider.as_json }, format: :json }
        put :update, params: params

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response["ip"]["ip"]).to eq("42.52.34.290")
      end

      it "does not update the IP address when IP is a duplicate" do
        params = { id: ip_address1.id, ip_address: { ip: ip_address2.ip, telecom_provider: telecom_provider.as_json }, format: :json }
        put :update, params: params

        expect(response).to have_http_status(:bad_request)
        json_response = JSON.parse(response.body)
        expect(json_response["message"]).to eq("IP has already been taken")
      end
    end
  end
end