require 'rails_helper'
include CompanyUserHelper
include LoginHelper

RSpec.describe AppliedBuildDataSetsController, type: :controller do
  create_company_and_user
  let!(:comp_build) { create(:applied_build_data_set, company_build: create(:company_build, company: company), build_by: create(:company_user, company: company)) }

  before do
    sign_in user
  end
  
  describe '#index' do
    it 'returns a list of resources' do
      get :index, format: :json
      expect(response).to have_http_status(:ok)
      json_response = JSON.parse(response.body)
      expect(json_response.count).to eq(1)
      expect(json_response.last["id"]).to eq(comp_build.id)
    end
  end

  describe '#show' do
    context 'when comp_build_data_set exists' do
      it 'returns the attributes of the comp_build_data_set' do
        get :show, params: { id: comp_build.id }, format: :json
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to include(comp_build.attributes.except('groups', 'people', 'created_at', 'updated_at', 'company_build_id'))
      end
    end

    context 'when comp_build_data_set does not exist' do
      it 'returns a 404 status' do
        get :show, params: { id: 'nonexistent_id' }, format: :json
        expect(response).to have_http_status(:not_found)
        expect(JSON.parse(response.body)).to be_empty
      end
    end
  end
end
