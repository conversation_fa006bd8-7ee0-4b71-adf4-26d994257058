require 'rails_helper'
require 'support/aws_stub'

include CompanyUserHelper

describe CustomFormLocationsController, type: :controller do
  create_company_and_user

  before do
    Aws.config[:stub_responses] = true
  end

  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:company) { company_user.company }
  let(:default_form) { company.custom_forms.find_by(default: true, company_module: 'location') }
  let(:avatar_field) { default_form.custom_form_fields.find_by(name: 'location_avatar') }
  let(:attached_file) { File.open(Rails.root.join("README.md")) }
  let(:attachment) { create(:custom_form_attachment, attachment: { io: attached_file, filename: 'README/md' }, company: company) }
  let(:name_field) { default_form.custom_form_fields.find_by(name: 'name') }

  context "with an attachment" do
    it "will create location custom form value with a custom avatar successfully" do
      post :create, params: {"json"=>"{\"values\":[{\"customFormFieldId\":#{name_field.id},\"valueStr\":\"Testing Location\"},{\"customFormFieldId\":#{avatar_field.id},\"attachmentId\":#{attachment.id}}],\"customFormId\":#{default_form.id}}", "attachment_ids"=>[], "custom_form_id"=>"#{default_form.id}", "format"=>"json"}
      location = Location.find_by(id: JSON.parse(response.body)["entity"]["id"])
      avatar_value = location.custom_form_values.find_by(custom_form_field_id: avatar_field.id)
      expect(avatar_value).to be_present
      expect(avatar_value.attachment).to be_present
    end
  end
end
