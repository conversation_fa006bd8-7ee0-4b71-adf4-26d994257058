require 'rails_helper'
include CompanyUserHelper

RSpec.describe DiscoveredVoiceServicesController, type: :controller do
  create_company_and_user

  let!(:discovered_voice_service) { create(:discovered_voice_service, company_id: company.id) }

  describe "#index" do
    it "should return the discovered_voice_service when number is blank" do
      get :index, format: :json

      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)["service"]).to be_present
      expect(JSON.parse(response.body)["status"]).to eq(true)
    end
  end

  describe "#create" do
    it "should create a new telecom service and provider" do
      post :create, params: { telecom_provider: { name: "AT&T" }}, format: :json

      expect(response).to have_http_status(:ok)
      telcos = company.telecom_services
      expect(telcos.count).to eq(1)
      expect(telcos.first.name).to eq("AT&T Service")
      expect(telcos.first.service_type).to eq("voice_service")
      expect(telcos.first.telecom_provider).to be_present
      expect(telcos.first.telecom_provider.name).to eq("AT&T")
    end
  end
end