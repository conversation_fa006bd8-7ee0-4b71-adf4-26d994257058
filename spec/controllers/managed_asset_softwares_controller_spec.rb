require 'rails_helper'
include CompanyUserHelper

RSpec.describe ManagedAssetSoftwaresController, type: :controller do
  create_company_and_user

  let!(:laptop_asset) { FactoryBot.create(:managed_asset, :laptop, company: company ) }
  let!(:phone_asset) { FactoryBot.create(:managed_asset, :phone, company: company ) }
  let!(:router_asset) { FactoryBot.create(:managed_asset, :router, company: company ) }

  describe 'GET #index' do
    it 'return all asset softwares of assets in a company' do
      get :index, params: { limit: 30, offset: 0 }

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)
      expect(result['count']).to eq(3)
      expected_names = ["MacOS", "PhoneOS", "Cisco OS"]
      os_names = result['softwares'].pluck('name')
      expect(os_names).to include(*expected_names)
    end
  end
end
