require 'rails_helper'
require 'support/aws_stub'

include CompanyUserHelper

describe AttachmentUploadsController, type: :controller do
  create_company_and_user

  before do
    Aws.config[:stub_responses] = true
  end

  let(:ticket) { create(:help_ticket, company: company, workspace: workspace) }

  describe "#create" do
    context "with attached file" do
      let(:attachment) { Rack::Test::UploadedFile.new('spec/data/laptop.jpg', 'image/jpeg') }
      subject { post :create, params: { attachable_type: "HelpTicket",  attachable_id: ticket.id,  attachment: attachment }, format: :json }

      it "will return a success status" do
        subject
        expect(response).to have_http_status(:ok)
      end

      it "will have successfully attach the file" do
        subject
        attachment = ticket.attachment_uploads.find_by(attachable_id: ticket.id).attachment.filename.to_s
        expect(attachment).to be_present
      end
    end
  end

  describe "#destroy" do
    context "with attached file" do
      let(:attachment) { Rack::Test::UploadedFile.new('spec/data/laptop.jpg', 'image/jpeg') }
      let(:ticket_attachment) { ticket.attachment_uploads.create(attachment: attachment, company_id: company.id) }

      subject { delete :destroy, params: { id: ticket_attachment.id }, format: :json }

      it "will return a success status" do
        subject
        expect(response).to have_http_status(:ok)
      end

      it "will have successfully attach the file" do
        subject
        expect(ticket.reload.attachment_uploads).to be_blank
      end
    end
  end
end