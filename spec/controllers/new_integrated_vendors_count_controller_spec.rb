require 'rails_helper'
include CompanyUserHelper

describe NewIntegratedVendorsCountController, type: :controller do
  create_company_and_user
  let!(:config) { FactoryBot.create(:quickbooks_config, company_id: company.id) }

  let!(:integrated_vendor1) {
    FactoryBot.create(
      :integrated_vendor,
      integrable_id: config.id,
      integrable_type: config.class.name,
      sync_status: false,
      is_new: true,
      company_id: company.id
    )
  }
  let!(:integrated_vendor2) {
    FactoryBot.create(
      :integrated_vendor,
      integrable_id: config.id,
      integrable_type: config.class.name,
      sync_status: false,
      is_new: true,
      company_id: company.id
    )
  }
  let!(:integrated_vendor3) {
    FactoryBot.create(
      :integrated_vendor,
      integrable_id: config.id,
      integrable_type: config.class.name,
      sync_status: true,
      is_new: false,
      company_id: company.id
    )
  }
  let!(:integrated_vendor4) {
    FactoryBot.create(
      :integrated_vendor,
      integrable_id: config.id,
      integrable_type: config.class.name,
      sync_status: true,
      is_new: false,
      company_id: company.id
    )
  }
  describe "#index" do
    it "should return new integrated vendors count" do
      get :index, format: 'json'
      res = JSON.parse(response.body)

      expect(response).to have_http_status(:ok)
      expect(res['new_vendor_counts'].first['quickbooks']).to eq(2)
    end
  end

  describe "#update" do
    it "should update new ordered integrated vendors count to zero" do
      params = { 'slug' => 'Quickbooks' }
      post :update, format: 'json', params: params

      expect(response).to have_http_status(:ok)
      expect(config.integrated_vendors.pluck(:is_new).uniq).to eq([false])
    end
  end
end
