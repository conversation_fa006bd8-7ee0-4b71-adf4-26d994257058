require 'rails_helper'
include CompanyUserHelper

RSpec.describe HelpTickets::TaskChecklistsController, type: :controller do
  create_company_and_user

  let!(:task_1) { FactoryBot.create(:task, description: "task_1", company: company, workspace: workspace, created_at: DateTime.now) }
  let!(:task_2) { FactoryBot.create(:task, description: "task_2", company: company, workspace: workspace, created_at: DateTime.now - 1.day) }

  describe "#create" do
    it "creates task" do
      post :create, params: { "task_checklist"=>{ "name" => "Checklist1", "task_ids" => [task_1.id, task_2.id] }}
      expect(workspace.task_checklists.count).to eq(3)
    end
  end

  describe "#index" do
    before(:each) do
      @checklist_1 = FactoryBot.create(:task_checklist, name: "checklist_1", task_ids: [task_1.id], company: company, workspace: workspace)
      @checklist_2 = FactoryBot.create(:task_checklist, name: "checklist_2", task_ids: [task_2.id, task_1.id], company: company, workspace: workspace)
    end

    it "lists task checklists tasks in sorted order" do
      get :index
      expect(response.status).to eq(200)
      result = JSON.parse(response.body)
      checklists = result["task_checklists"]
      second_checklist_tasks = checklists.find { |c| c['name'] == 'checklist_2' }['tasks']
      expect(second_checklist_tasks.first['id']).to eq(task_2.id)
      expect(second_checklist_tasks.second['id']).to eq(task_1.id)
      expect(checklists).to be_present
      expect(checklists.count).to eq(4)
    end
  end

  describe "#destroy" do
    before(:each) do
      @checklist = FactoryBot.create(:task_checklist, name: "checklist_1", task_ids: [task_1.id, task_2.id], company: company, workspace: workspace)
    end

    it "destroys task checklist" do
      delete :destroy, params: {"id" => @checklist.id}
      expect(workspace.task_checklists.find_by(id: @checklist.id)).to eq(nil)
      expect(response.status).to eq(200)
    end
  end

  describe "#update" do
    before(:each) do
      @checklist = FactoryBot.create(:task_checklist, name: "checklist_1", task_ids: [task_1.id, task_2.id], company: company, workspace: workspace)
    end

    it "updates task checklist" do
      task_checklist_params = {
        "task_checklist" => {
          "name" => "Updated Name",
          "task_ids" => [task_1.id],
          "company_id" => company.id,
          "workspace_id" => workspace.id
        },
        "id"=> @checklist.id
      }
      put :update, params: task_checklist_params
      expect(@checklist.reload.name).to eq("Updated Name")
    end
  end

  describe "#show" do
    before(:each) do
      @checklist = FactoryBot.create(:task_checklist, name: "checklist_1", task_ids: [task_1.id, task_2.id], company: company, workspace: workspace)
    end

    it "shows task checklist" do
      get :show, params: {"id" => @checklist.id}
      expect(response.status).to eq(200)
      result = JSON.parse(response.body)
      expect(result["id"]).to eq(@checklist.id)
      expect(result["name"]).to eq("checklist_1")
    end
  end

  describe "#update_order" do
    before(:each) do
      @task_checklist_1 = FactoryBot.create(:task_checklist, name: "checklist_1", task_ids: [task_1.id], company: company, workspace: workspace)
      @task_checklist_2 = FactoryBot.create(:task_checklist, name: "checklist_2", task_ids: [task_2.id], company: company, workspace: workspace)
    end

    it "updates the order of checklists" do
      put :order_checklists, params: { "checklists"=>[{"id"=>@task_checklist_2.id, "order"=>0}, {"id"=>@task_checklist_1.id, "order"=>1}] }
      expect(response.status).to eq(200)
      expect( @task_checklist_2.reload.order).to eq(0)
      expect( @task_checklist_1.reload.order).to eq(1)
    end
  end

  describe "#create_project_task_in_ticket" do
    let(:help_ticket) { FactoryBot.create(:help_ticket, company: company) }
    let(:ticket_activity) { HelpTicketActivity.find_by(help_ticket_id: help_ticket.id) }
    let(:task_checklist_1) { FactoryBot.create(:task_checklist, name: "checklist_1", task_ids: [task_1.id], company: company, workspace: workspace) }
    let(:task_checklist_2) { FactoryBot.create(:task_checklist, name: "checklist_2", task_ids: [task_2.id], company: company, workspace: workspace) }

    it "handles default case when is_ticket_show_page is false" do
      post :create_project_task_in_ticket, params: {
        ticket_id: help_ticket.id,
        is_ticket_show_page: false,
        checklists_ids: [task_checklist_2.id]
      }
      expect(ticket_activity.activity_type).to eq("create_checklist_task")
      expect(help_ticket.task_checklist_ids).to eq([task_checklist_2.id])
      expect(response.status).to eq(204)
    end

    it "handles add/remove cases when is_ticket_show_page is true" do
      post :create_project_task_in_ticket, params: {
        ticket_id: help_ticket.id,
        is_ticket_show_page: true,
        checklists_ids: [task_checklist_1.id]
      }
      expect(ticket_activity.activity_type).to eq("create_checklist_task")
      expect(help_ticket.task_checklist_ids).to eq([task_checklist_1.id])
      expect(response.status).to eq(204)
    end
  end
end
