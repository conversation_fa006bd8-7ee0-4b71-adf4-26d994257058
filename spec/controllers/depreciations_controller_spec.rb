require 'rails_helper'
include CompanyUserHelper

RSpec.describe DepreciationsController, type: :controller do
  create_company_and_user

  describe "#index" do
    it "gets the current company's depreciation options" do
      get :index, params: { format: :json }

      res = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(res.count).to eq(3)
    end
  end

  describe "#create" do
    let(:params) {
      {
        name: "New depreciation",
        useful_life_in_years: 10,
        depreciation_type: "sum_of_years_digits",
        description: "New"
      }
    }

    it "creates a new depreciation option" do
      post :create, params: { depreciation: params, format: :json }

      res = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(res["depreciation"]["name"]).to eq("New depreciation")
      expect(company.depreciations.count).to eq(4)
    end
  end

  describe "#destroy" do
    it "deletes the selected depreciation option" do
      delete :destroy, params: { id: company.depreciations.first.id, format: :json }

      expect(response).to have_http_status(:ok)
      expect(company.depreciations.count).to eq(2)
    end
  end
end