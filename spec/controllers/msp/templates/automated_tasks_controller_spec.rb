require 'rails_helper'
include <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

describe Msp::Templates::AutomatedTasksController, type: :controller do
  create_company_and_user

  before do
    Rails.application.load_seed
  end

  let(:automated_task) { company.workspaces.first.automated_tasks.last }
  let(:automated_task1) { company.workspaces.first.automated_tasks.first }

  describe "#create" do
    it "should create new automated task" do
      post :create, params: {"task"=>{"events"=>[{"node_type"=>{"id"=>AutomatedTasks::EventType.find_by(name:"{a ticket} is created").id, "name"=>"{a ticket} is created", "model"=>"HelpTicket", "module"=>"help_tickets", "event_class"=>"TicketAdded", "icon"=>"nulodgicon-ios-copy-outline", "created_at"=>"2021-07-19T16:54:03.122+05:00", "updated_at"=>"2021-07-19T16:54:03.122+05:00", "type"=>"Event"}, "nodes"=>[{"node_type"=>{"id"=>AutomatedTasks::ObjectSubjectType.find_by(name:"any ticket").id, "icon"=>"nulodgicon-ios-copy-outline", "key"=>"ticket", "name"=>"any ticket", "created_at"=>"2021-07-19T16:54:03.160+05:00", "updated_at"=>"2021-07-19T16:54:03.160+05:00", "subject_class"=>"AnyTicket", "parent_type"=>"AutomatedTasks::EventType", "parent_id"=>AutomatedTasks::EventType.find_by(name:"{a ticket} is created").id, "type"=>"Object"}, "parent"=>nil, "nodes"=>[]}], "parent"=>nil}],"actions"=>[{"node_type"=>{"id"=>AutomatedTasks::ActionType.find_by(name:"send an [email]").id, "name"=>"send an [email]", "module"=>"help_tickets", "model"=>"HelpTicket", "action_class"=>"SendEmail", "icon"=>"genuicon-envelope-o", "created_at"=>"2021-07-19T16:54:05.000+05:00", "updated_at"=>"2021-07-19T16:54:05.000+05:00", "type"=>"Action", "class"=>"Action"}}], "automated_task"=>{}, "name"=>"Automated Task"}}, as: :json
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)["message"]).to be_present
      expect(JSON.parse(response.body)["message"]).to eq("Automated Task was successfully created")
      expect(company.msp_automated_tasks.count).to eq(1)
      expect(company.msp_automated_tasks.first.name).to eq("Automated Task")
    end

    it "should create new automated task for a project task is added to a ticket" do
      post :create, params: {"task"=> {"events"=>[{"node_type"=>{"id"=>AutomatedTasks::EventType.find_by(name:"{a project task} is added to a ticket").id, "name"=>"{a project task} is added to a ticket", "model"=>"ProjectTask", "module"=>"help_tickets", "event_class"=>"TaskAdded", "icon"=>"genuicon-clipboard", "created_at"=>"2021-07-19T16:54:04.218+05:00", "updated_at"=>"2021-07-19T16:54:04.218+05:00", "type"=>"Event"}, "nodes"=>[{"node_type"=>{"id"=>AutomatedTasks::ObjectSubjectType.find_by(name:"/a project task/ that has a {description}").id, "icon"=>"genuicon-envelope-o", "key"=>"project task", "name"=>"/a project task/ that has a {description}", "created_at"=>"2021-07-19T16:54:04.337+05:00", "updated_at"=>"2021-07-19T16:54:04.337+05:00", "subject_class"=>"TaskDescription", "parent_type"=>"AutomatedTasks::EventType", "parent_id"=>AutomatedTasks::EventType.find_by(name:"{a project task} is added to a ticket").id, "type"=>"Object"}, "parent"=>nil, "nodes"=>[{"node_type"=>{"id"=>AutomatedTasks::ObjectSubjectType.find_by(name:"any description").id, "icon"=>nil, "key"=>"description", "name"=>"any description", "created_at"=>"2021-07-19T16:54:04.366+05:00", "updated_at"=>"2021-07-19T16:54:04.397+05:00", "subject_class"=>"AnyString", "parent_type"=>"AutomatedTasks::EventSubjectType", "parent_id"=>AutomatedTasks::ObjectSubjectType.find_by(name:"any description").id, "type"=>"Object"}, "parent"=>nil, "nodes"=>[]}]}], "parent"=>nil}],"actions"=>[{"node_type"=>{"id"=>AutomatedTasks::ActionType.find_by(name:"send a [sms]").id, "name"=>"send a [sms]", "module"=>"help_tickets", "model"=>"ProjectTask", "action_class"=>"SendSms", "icon"=>"genuicon-mobile", "created_at"=>"2021-07-19T16:54:05.815+05:00", "updated_at"=>"2021-07-19T16:54:05.815+05:00", "type"=>"Action", "class"=>"Action"}, "value"=>{"target"=>"agents", "message"=>"test"}}]}, "automated_task"=>{}}, as: :json
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)["message"]).to be_present
      expect(JSON.parse(response.body)["message"]).to eq("Automated Task was successfully created")
      expect(company.msp_automated_tasks.count).to eq(1)
    end

    it "should create new automated task for a ticket is created with multiple form fieldst" do
      post :create, params: {"task"=>{"events"=>[{"node_type"=>{"id"=>AutomatedTasks::EventType.find_by(name:"{a ticket} is created").id, "name"=>"{a ticket} is created", "model"=>"HelpTicket", "module"=>"help_tickets", "event_class"=>"TicketAdded", "icon"=>nil}, "nodes"=>[{"node_type"=>{"id"=>AutomatedTasks::ObjectSubjectType.find_by(name: "/a ticket/ with {a form field}").id, "icon"=>nil, "key"=>"ticket", "name"=>"/a ticket/ with {a form field}", "subject_class"=>"TicketFormField", "parent_type"=>"AutomatedTasks::EventType", "parent_id"=>AutomatedTasks::EventType.find_by(name:"{a ticket} is created").id, "type"=>"Object"}, "value"=>[{"custom_forms"=>[{"id"=>"all", "is_active"=>true, "form_name"=>"Any Form"}], "form_field"=>{"name"=>"priority", "field_attribute_type"=>"priority", "options"=>[{"low"=>"#ffd700"}, {"medium"=>"#ffa500"}, {"high"=>"#ff0000"}, {"urgent"=>"#876bc7"}, {"1 week"=>"#876bc7"}], "label"=>"priority"}, "negate"=>false, "any"=>false, "partial_match"=>false, "priority"=>"low"}, {"custom_forms"=>[{"id"=>"all", "is_active"=>true, "form_name"=>"Any Form"}], "form_field"=>{"name"=>"status", "field_attribute_type"=>"status", "options"=>["Open", "In Progress", "Closed", "1 status", "Waiting For Information", "Need Net Ops"], "label"=>"status"}, "negate"=>false, "any"=>false, "partial_match"=>false, "status"=>"In Progress"}]}]}, {"node_type"=>{"id"=>AutomatedTasks::EventType.find_by(name:"{a project task} is added to a ticket").id, "name"=>"{a project task} is added to a ticket", "model"=>"ProjectTask", "module"=>"help_tickets", "event_class"=>"TaskAdded", "icon"=>"genuicon-clipboard", "created_at"=>"2021-07-19T16:54:04.218+05:00", "updated_at"=>"2021-07-19T16:54:04.218+05:00", "type"=>"Event"}, "nodes"=>[{"node_type"=>{"id"=>AutomatedTasks::ObjectSubjectType.find_by(name:"/a project task/ that has a {description}").id, "icon"=>"genuicon-envelope-o", "key"=>"project task", "name"=>"/a project task/ that has a {description}", "created_at"=>"2021-07-19T16:54:04.337+05:00", "updated_at"=>"2021-07-19T16:54:04.337+05:00", "subject_class"=>"TaskDescription", "parent_type"=>"AutomatedTasks::EventType", "parent_id"=>AutomatedTasks::EventType.find_by(name:"{a project task} is added to a ticket").id, "type"=>"Object"}, "parent"=>nil, "nodes"=>[{"node_type"=>{"id"=>AutomatedTasks::ObjectSubjectType.find_by(name:"any description").id, "icon"=>nil, "key"=>"description", "name"=>"any description", "created_at"=>"2021-07-19T16:54:04.366+05:00", "updated_at"=>"2021-07-19T16:54:04.397+05:00", "subject_class"=>"AnyString", "parent_type"=>"AutomatedTasks::EventSubjectType", "parent_id"=>AutomatedTasks::ObjectSubjectType.find_by(name:"any description").id, "type"=>"Object"}, "parent"=>nil, "nodes"=>[]}]}], "parent"=>nil}], "actions"=>[{"node_type"=>{"id"=>AutomatedTasks::ActionType.find_by(name:"add a [comment]").id, "name"=>"add a comment", "action_class"=>"AddComment", "model"=>"HelpTicket"}, "value"=>{"comment"=>"<div><!--block-->abc</div>"}}]}}, as: :json
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)["message"]).to be_present
      expect(JSON.parse(response.body)["message"]).to eq("Automated Task was successfully created")
      expect(company.msp_automated_tasks.count).to eq(1)
    end
  end

  describe "#index" do
    it "should list all msp automated tasks " do
      post :create, params: {"task"=>{"events"=>[{"node_type"=>{"id"=>AutomatedTasks::EventType.find_by(name:"{a ticket} is created").id, "name"=>"{a ticket} is created", "model"=>"HelpTicket", "module"=>"help_tickets", "event_class"=>"TicketAdded", "icon"=>"nulodgicon-ios-copy-outline", "created_at"=>"2021-07-19T16:54:03.122+05:00", "updated_at"=>"2021-07-19T16:54:03.122+05:00", "type"=>"Event"}, "nodes"=>[{"node_type"=>{"id"=>AutomatedTasks::ObjectSubjectType.find_by(name:"any ticket").id, "icon"=>"nulodgicon-ios-copy-outline", "key"=>"ticket", "name"=>"any ticket", "created_at"=>"2021-07-19T16:54:03.160+05:00", "updated_at"=>"2021-07-19T16:54:03.160+05:00", "subject_class"=>"AnyTicket", "parent_type"=>"AutomatedTasks::EventType", "parent_id"=>AutomatedTasks::EventType.find_by(name:"{a ticket} is created").id, "type"=>"Object"}, "parent"=>nil, "nodes"=>[]}], "parent"=>nil}],"actions"=>[{"node_type"=>{"id"=>AutomatedTasks::ActionType.find_by(name:"send an [email]").id, "name"=>"send an [email]", "module"=>"help_tickets", "model"=>"HelpTicket", "action_class"=>"SendEmail", "icon"=>"genuicon-envelope-o", "created_at"=>"2021-07-19T16:54:05.000+05:00", "updated_at"=>"2021-07-19T16:54:05.000+05:00", "type"=>"Action", "class"=>"Action"}}], "automated_task"=>{}}}, as: :json
      get :index
      res = JSON.parse(response.body)
      expect(res.count).to eq(1)
    end
  end
end
