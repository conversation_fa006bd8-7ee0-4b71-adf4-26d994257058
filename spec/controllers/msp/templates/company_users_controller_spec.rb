require 'rails_helper'
include CompanyUserHelper

RSpec.describe Msp::Templates::CompanyUsersController, type: :controller do
  create_company_and_user

  let(:user_1_attributes) do
    {
      email: "<EMAIL>",
      password: "password2",
      first_name: "Test",
      last_name: "User",
      terms_of_services: true
    }
  end

  let(:user_2_attributes) do
    {
      email: "<EMAIL>",
      password: "password2",
      first_name: "<PERSON><PERSON>",
      last_name: "user",
      terms_of_services: true
    }
  end

  let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }

  let!(:user_1) { create(:user, user_1_attributes) }
  let!(:user_2) { create(:user, user_2_attributes) }

  let!(:company_user_0) { CompanyUser.find_by(company_id: company.id, user_id: user.id, custom_form_id: custom_form.id) }
  let!(:company_user_1) { CompanyUser.create(company_id: company.id, user_id: user_1.id, custom_form_id: custom_form.id) }
  let!(:company_user_2) { CompanyUser.create(company_id: company.id, user_id: user_2.id, custom_form_id: custom_form.id) }

  describe "#index" do
    it "should list all company users" do
      get :index
      res = JSON.parse(response.body)
      expect(res["company_users"]).to be_present
      expect(res["company_users"].count).to eq(3)
    end
  end
end
