require 'rails_helper'
include CompanyUserHelper

RSpec.describe Msp::Templates::GroupsController, type: :controller do
  create_company_and_user

  let!(:group1) { Group.create(name: "Group 1", company_id: company.id) }
  let!(:group2) { Group.create(name: "Group 2", company_id: company.id) }

  describe "#index" do
    it "should list all msp groups" do
      get :index
      res = JSON.parse(response.body)
      expect(res["msp_groups"]).to be_present
      expect(res["msp_groups"].count).to eq(4)
    end
  end
end
