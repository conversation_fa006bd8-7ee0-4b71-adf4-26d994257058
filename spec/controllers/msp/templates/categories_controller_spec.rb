require 'rails_helper'
include CompanyUserHelper

RSpec.describe Msp::Templates::CategoriesController, type: :controller do
  create_company_and_user

  let!(:category1) { Msp::Templates::Category.create(name: "Category 1", company_id: company.id) }
  let!(:category2) { Msp::Templates::Category.create(name: "Category 2", company_id: company.id) }

  describe "#create" do
    it "should create msp category" do
      post :create, params: {"category" => "Category"}
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)["message"]).to be_present
      expect(JSON.parse(response.body)["message"]).to eq("Category was successfully created")
      expect(company.msp_categories.count).to eq(3)
    end
  end

  describe "#update" do
    it "should update msp categories" do
      put :update, params: {"id"=> category1.id, "category"=>"Updated Category"}
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)["message"]).to be_present
      expect(JSON.parse(response.body)["message"]).to eq("Category was successfully updated")
      expect(category1.reload.name).to eq("Updated Category")
    end
  end

  describe "#index" do
    it "should list all msp categories" do
      get :index
      res = JSON.parse(response.body)
      expect(res["msp_categories"]).to be_present
      expect(res["msp_categories"].count).to eq(2)
    end
  end
end
