require 'rails_helper'
include CompanyUserHelper

RSpec.describe Msp::Templates::LibraryDocumentsController, type: :controller do
  create_company_and_user

  let!(:attachment) do
    file = URI.open('https://nulodgic-static-assets.s3.amazonaws.com/images/file.png')
    Rack::Test::UploadedFile.new(file, "image/png", original_filename: "file.png")
  end
  let!(:document) { Msp::Templates::Document.create(name: "Test Document", attached_file: { io: attachment, filename: 'file.png' }, company_id: company.id) }

  describe "#create" do
    let(:attached_file) do
      file = URI.open('https://nulodgic-static-assets.s3.amazonaws.com/images/logo.png')
      Rack::Test::UploadedFile.new(file, "image/png", original_filename: "file.png")
    end
    subject { post :create, :params => { library_document: { name: "Test File", attached_file: { io: attached_file, filename: 'file.png' } }}, format: :json }

    it "will have successfully created the document" do
      subject
      expect(response).to have_http_status(:ok)
      res = JSON.parse(response.body)
      expect(res['message']).to be_present
      expect(res['message']).to eq("Document was successfully created")
      expect(company.msp_templates_documents.count).to eq(2)
    end
  end

  describe "#index" do
    it "should list all msp documents" do
      get :index
      res = JSON.parse(response.body)
      expect(res["msp_templates_documents"]).to be_present
      expect(res["msp_templates_documents"].count).to eq(1)
    end
  end

  describe "#update" do
    subject { put :update, :params => { id: document.id, library_document: { name: 'Test File1'}}, format: :json }

    it "will have successfully created the document" do
      subject
      expect(response).to have_http_status(:ok)
      res = JSON.parse(response.body)
      expect(res['message']).to be_present
      expect(res['message']).to eq("Document was successfully updated")
    end
  end
end
