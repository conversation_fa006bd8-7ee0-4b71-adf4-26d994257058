require 'rails_helper'
include CompanyUserHelper

describe Msp::Templates::CustomFormsController, type: :controller do
  create_company_and_user

  describe "#index" do
    it "should list all msp custom forms " do
      post :create, params: { custom_form: {id: nil, company_module: "helpdesk", show_in_open_portal: true, form_name: "Custom Form", form_fields: [{field_attribute_type: "text", order_position: 0, label: "Subject", name: "subject", options: nil, required: true, note: nil, field_position: {position: "title"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "title"}}, {field_attribute_type: "priority", order_position: 1, label: "Priority", name: "priority", options: CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: "low", msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "status", order_position: 2, label: "Status", name: "status", options: CustomFormFieldTemplate::DEFAULT_STATUS_OPTIONS, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: "Open", msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "people_list", order_position: 3, label: "Created By", name: "created_by", options: nil, required: true, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "people_list", order_position: 4, label: "Assigned To", name: "assigned_to", required_to_close: false, audience: "guests", sort_list: "[\"Descending\",\"First Name, Last Name\"]", options: nil, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "right"}}], msp_templates_custom_form_fields_attributes: [{field_attribute_type: "text", order_position: 0, label: "Subject", name: "subject", options: nil, required: true, note: nil, field_position: {position: "title"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "title"}}, {field_attribute_type: "priority", order_position: 1, label: "Priority", name: "priority", options: CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: "low", msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "status", order_position: 2, label: "Status", name: "status", options: CustomFormFieldTemplate::DEFAULT_STATUS_OPTIONS, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: "Open", msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "people_list", order_position: 3, label: "Created By", name: "created_by", options: nil, required: true, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "people_list", order_position: 4, label: "Assigned To", name: "assigned_to", options: nil, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "right"}}]}  }
      get :index
      res = JSON.parse(response.body)
      expect(res["msp_custom_forms"]).to be_present
      expect(res["msp_custom_forms"].count).to eq(1)
    end
  end

  describe "#create" do
    it "should create new custom form" do
      post :create, params: { custom_form: {id: nil, company_module: "helpdesk", show_in_open_portal: true, form_name: "Test Form", form_fields: [{field_attribute_type: "text", order_position: 0, label: "Subject", name: "subject", options: nil, required: true, note: nil, field_position: {position: "title"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "title"}}, {field_attribute_type: "priority", order_position: 1, label: "Priority", name: "priority", options: CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: "low", msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "status", order_position: 2, label: "Status", name: "status", options: CustomFormFieldTemplate::DEFAULT_STATUS_OPTIONS, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: "Open", msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "people_list", order_position: 3, label: "Created By", name: "created_by", options: nil, required: true, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "people_list", order_position: 4, label: "Assigned To", name: "assigned_to", required_to_close: true, audience: "staff", sort_list: "[\"Descending\",\"Last Name, First Name\"]",options: nil, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "right"}}], msp_templates_custom_form_fields_attributes: [{field_attribute_type: "text", order_position: 0, label: "Subject", name: "subject", options: nil, required: true, note: nil, field_position: {position: "title"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "title"}}, {field_attribute_type: "priority", order_position: 1, label: "Priority", name: "priority", options: CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: "low", msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "status", order_position: 2, label: "Status", name: "status", options: CustomFormFieldTemplate::DEFAULT_STATUS_OPTIONS, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: "Open", msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "people_list", order_position: 3, label: "Created By", name: "created_by", options: nil, required: true, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "people_list", order_position: 4, label: "Assigned To", name: "assigned_to", options: nil, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "right"}}]}  }
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)["message"]).to be_present
      expect(JSON.parse(response.body)["message"]).to eq("Custom Form was successfully created")
      expect(company.msp_templates_custom_forms.count).to eq(1)
      form = company.msp_templates_custom_forms.find_by(form_name: "Test Form")
      expect(form.msp_templates_custom_form_fields).to be_present
    end
  end

  describe "#update" do
    it "should throw error in case of updating custom form with missing default values in status and options" do
      priority_options = "[{\"name\":\"low\",\"color\":\"#ffd700\"},{\"name\":\"high\",\"color\":\"#ff0000\"}]"
      status_options = "[{\"name\":\"Open\",\"color\":\"#2ECC71\"},{\"name\":\"Closed\",\"color\":\"#626567\"}]"
      post :create, params: { custom_form: {id: nil, company_module: "helpdesk", show_in_open_portal: true, form_name: "Test Form", form_fields: [{field_attribute_type: "text", order_position: 0, label: "Subject", name: "subject", options: nil, required: true, note: nil, field_position: {position: "title"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "title"}}, {field_attribute_type: "priority", order_position: 1, label: "Priority", name: "priority", options: CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: "low", msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "status", order_position: 2, label: "Status", name: "status", options: CustomFormFieldTemplate::DEFAULT_STATUS_OPTIONS, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: "Open", msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "people_list", order_position: 3, label: "Created By", name: "created_by", options: nil, required: true, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "people_list", order_position: 4, label: "Assigned To", name: "assigned_to", required_to_close: true, audience: "staff", sort_list: "[\"Descending\",\"Last Name, First Name\"]",options: nil, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "right"}}], msp_templates_custom_form_fields_attributes: [{field_attribute_type: "text", order_position: 0, label: "Subject", name: "subject", options: nil, required: true, note: nil, field_position: {position: "title"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "title"}}, {field_attribute_type: "priority", order_position: 1, label: "Priority", name: "priority", options: CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: "low", msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "status", order_position: 2, label: "Status", name: "status", options: CustomFormFieldTemplate::DEFAULT_STATUS_OPTIONS, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: "Open", msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "people_list", order_position: 3, label: "Created By", name: "created_by", options: nil, required: true, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "people_list", order_position: 4, label: "Assigned To", name: "assigned_to", options: nil, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "right"}}]}  }
      form = company.msp_templates_custom_forms.find_by(form_name: "Test Form").id
      put :update, params: { id: form ,custom_form: {id: form, company_module: "helpdesk", show_in_open_portal: true, form_name: "Test Forming  ", form_fields: [{field_attribute_type: "text", order_position: 0, label: "Subject", name: "subject", options: nil, required: true, note: nil, field_position: {position: "title"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "title"}}, {field_attribute_type: "priority", order_position: 1, label: "Priority", name: "priority", options: priority_options, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: "low", msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "status", order_position: 2, label: "Status", name: "status", options: status_options, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: "Open", msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "people_list", order_position: 3, label: "Created By", name: "created_by", options: nil, required: true, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "people_list", order_position: 4, label: "Assigned To", name: "assigned_to", required_to_close: true, audience: "staff", sort_list: "[\"Descending\",\"Last Name, First Name\"]",options: nil, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "right"}}], msp_templates_custom_form_fields_attributes: [{field_attribute_type: "text", order_position: 0, label: "Subject", name: "subject", options: nil, required: true, note: nil, field_position: {position: "title"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "title"}}, {field_attribute_type: "priority", order_position: 1, label: "Priority", name: "priority", options: priority_options, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: "low", msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "status", order_position: 2, label: "Status", name: "status", options: status_options, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: "Open", msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "people_list", order_position: 3, label: "Created By", name: "created_by", options: nil, required: true, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "right"}}, {field_attribute_type: "people_list", order_position: 4, label: "Assigned To", name: "assigned_to", options: nil, required: nil, note: nil, field_position: {position: "right"}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, msp_templates_field_position_attributes: {position: "right"}}]}  }
      expect(response).to have_http_status(:unprocessable_entity)
      expect(JSON.parse(response.body)["message"]).to be_present
      expect(JSON.parse(response.body)["message"]).to eq("Default value(s) medium for priority field are missing.")
      expect(company.msp_templates_custom_forms.count).to eq(1)
    end
  end
end
