require 'rails_helper'
include CompanyUserHelper

RSpec.describe Msp::Templates::SnippetsController, type: :controller do
  create_company_and_user

  let!(:snippet) { Msp::Templates::Snippet.create(title: "Test Snippet", description: "<div>This is a test snippet</div>", company_id: company.id) }
  let!(:snippet1) { Msp::Templates::Snippet.create(title: "Test Snippet 1", description: "<div>This is a 2nd test snippet</div>", company_id: company.id) }

  describe "#create" do
    it "should create msp snippet" do
      post :create, params: {title: "Snippet 1", description: "<div>This is a new snippet</div>"}
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)["message"]).to be_present
      expect(JSON.parse(response.body)["message"]).to eq("Snippet was successfully created")
      expect(company.msp_snippets.count).to eq(3)
    end
  end

  describe "#update" do
    it "should update msp snippet" do
      put :update, params: {id: snippet.id, title: "Snippet Updated", description: "<div>This is updated snippet</div>" }
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)["message"]).to be_present
      expect(JSON.parse(response.body)["message"]).to eq("Snippet was successfully updated")
      expect(snippet.reload.title).to eq("Snippet Updated")
      expect(snippet.reload.description).to eq("<div>This is updated snippet</div>")
    end
  end

  describe "#index" do
    it "should list all msp snippets" do
      get :index
      res = JSON.parse(response.body)
      expect(res["msp_snippets"]).to be_present
      expect(res["msp_snippets"].count).to eq(2)
    end
  end
end
