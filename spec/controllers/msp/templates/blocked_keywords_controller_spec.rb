require 'rails_helper'
include CompanyUserHelper

RSpec.describe Msp::Templates::BlockedKeywordsController, type: :controller do
  create_company_and_user

  let!(:keyword1) { Msp::Templates::BlockedKeyword.create(keyword: "Keyword One", company_id: company.id) }
  let!(:keyword2) { Msp::Templates::BlockedKeyword.create(keyword: "Keyword Two", company_id: company.id) }

  describe "#create" do
    it "should create msp blocked keyword" do
      post :create, params: {"blocked_keyword" => "keywordOne"}
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)["message"]).to be_present
      expect(JSON.parse(response.body)["message"]).to eq("Blocked keyword was successfully created")
      expect(company.msp_blocked_keywords.count).to eq(3)
    end
  end

  describe "#update" do
    it "should update msp blocked keyword" do
      put :update, params: {"id"=> keyword1.id, "blocked_keyword"=>"Updated Keyword"}
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)["message"]).to be_present
      expect(JSON.parse(response.body)["message"]).to eq("Blocked keyword was successfully updated")
      expect(keyword1.reload.keyword).to eq("Updated Keyword")
    end
  end

  describe "#index" do
    it "should list all msp blocked keywords" do
      get :index
      res = JSON.parse(response.body)
      expect(res["msp_blocked_keywords"]).to be_present
      expect(res["msp_blocked_keywords"].count).to eq(2)
    end
  end
end
