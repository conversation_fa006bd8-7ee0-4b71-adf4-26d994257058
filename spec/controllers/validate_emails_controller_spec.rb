require 'rails_helper'
include CompanyUserHelper

RSpec.describe ValidateEmailsController, type: :controller do
  create_company_and_user

  describe "GET #index" do
    let!(:domain_validator) {
      allow_any_instance_of(described_class).to receive(:validate).and_return(false)
    }
    it "returns http success and json response for availability of email" do
      get :index, :params => { :email => user.email }
      expect(response).to have_http_status(:ok)
      expect(response.body).to eq("taken")
    end

    it "returns http success and json response for availability of email" do
      get :index, :params => { :email => "<EMAIL>" }
      expect(response).to have_http_status(:ok)
      expect(response.body).to eq("ok")
    end
  end
end
