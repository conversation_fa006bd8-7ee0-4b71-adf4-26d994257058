require 'rails_helper'
include CompanyUserHelper
include LoginHelper

RSpec.describe DepartmentsController, type: :controller do
  create_company_and_user
  before do
    login_user
  end

  describe 'POST #create' do
    it "will create department" do
      params = { department: { name: 'Customer Support' } }
      departments_count = company.departments.count
      post :create, params: params

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)
      expect(result['company_departments'].find { |res| res['name'] == 'Customer Support' }).to be_present
      expect(result['company_departments'].length).to eq(departments_count + 1)
    end
  end

  describe 'GET #index' do
    let!(:dept_1) { Department.create!(name: 'Customer Support', company_id: company.id) }
    let!(:custom_form) { company.custom_forms.find_by(default: true, company_module: 'company_user') }
    let!(:company_user) { create(:company_user, custom_form: custom_form, company: company) }
  
    let!(:department_field) { custom_form.custom_form_fields.find_by(name: "department") }
  
    let!(:department_value) { create(:custom_form_value,
                                     custom_form_field: department_field,
                                     value_str: 'Construction',
                                     custom_form: custom_form,
                                     module: company_user,
                                     company: company)}

    it 'return company departments and discovered departments' do
      get :index

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)
      expect(result['company_departments'].find { |res| res['name'] == 'Customer Support' }).to be_present
      expect(result['discovered']).to eq(['Construction'])
    end
  end

  describe 'DESTROY #destroy' do
    let!(:dept_1) { Department.create!(name: 'Customer Support', company_id: company.id) }
    it "will destroy the department" do
      params = { id: dept_1.id }
      delete :destroy, params: params

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)['company_departments']
      expect(result.find { |res| res['name'] == 'Customer Support' }).to be_nil
    end
  end
end
