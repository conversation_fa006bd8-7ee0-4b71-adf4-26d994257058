require 'rails_helper'
include CompanyUserHelper

RSpec.describe AssetSoftwaresController, type: :controller do
  render_views
  create_company_and_user

  let!(:managed_asset) { FactoryBot.create(:managed_asset, company: company) }

  describe "POST #create" do
    subject { post :create, :params => params }
    let(:params) {
      {
        managed_asset_id: managed_asset.id,
        asset_software: {
          name: "My Asset",
          software_type: "Utility",
          install_date: 1.year.ago.strftime("%M/%d/%Y"),
        }
      }
    }

    it "is a successful http request" do
      subject
      expect(response).to have_http_status(:ok)
    end

    it "creates the asset software" do
      subject
      software = AssetSoftware.find_by(managed_asset_id: managed_asset.id, name: "My Asset")
      expect(software).to be_present
    end
  end

  describe "PUT #update" do
    let!(:asset_software) { managed_asset.asset_softwares.create(name: 'BINGO', software_type: "Application") }
    subject { put :update, :params => params }
    let(:params) {
      {
        managed_asset_id: managed_asset.id,
        id: asset_software.id,
        asset_software: {
          id: asset_software.id,
          name: "My Asset",
          software_type: "Utility",
          install_date: 1.year.ago.strftime("%M/%d/%Y"),
        }
      }
    }

    it "is a successful http request" do
      subject
      expect(response).to have_http_status(:ok)
    end

    it "creates the asset software" do
      subject
      software = AssetSoftware.find_by(managed_asset_id: managed_asset.id, name: "My Asset")
      expect(software).to be_present
      expect(software.software_type).to eq("Utility")
    end
  end

  describe "DELETE #destroy" do
    let!(:asset_software) { managed_asset.asset_softwares.create(name: 'BINGO', software_type: "Application") }
    subject { delete :destroy, :params => { managed_asset_id: managed_asset.id, id: asset_software.id } }

    it "is a successful http request" do
      subject
      expect(response).to have_http_status(:ok)
    end

    it "removes the software" do
      subject
      software = managed_asset.asset_softwares.find_by(id: asset_software.id)
      expect(software).to be_blank
    end
  end
end