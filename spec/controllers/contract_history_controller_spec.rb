require 'rails_helper'
include Company<PERSON>ser<PERSON>elper

describe ContractHistoryController, type: :controller do
  create_company_and_user

  let!(:internet) { FactoryBot.create(:contract, company: company) }

  describe "#index" do
    context "without any updates to this contact" do
      it "should not return any audits" do
        get :index, params: { id: internet.id, format: :json }

        expect(response).to have_http_status(:ok)
        audit_response = JSON.parse(response.body)['audit_history_items']
        expect(audit_response.length).to eq(0)
      end
    end

    context "with updates to this contract" do
      before do
        @old_name = internet.name
        @new_name = "Wifi"
        @old_date = internet.start_date
        @new_date = @old_date + 1.day
        @old_notes = internet.notes
        @new_notes = "New Notes"
        internet.update(name: @new_name, start_date: @new_date, notes: @new_notes)
      end

      it "should return audits" do
        get :index, params: { id: internet.id, format: :json }

        expect(response).to have_http_status(:ok)
        audit_response = JSON.parse(response.body)['audit_history_items']
        expect(audit_response[0]["audited_changes"].length).to eq(3)
        expect(audit_response[0]["auditable_id"]).to eq(internet.id)
        expect(audit_response[0]["audited_changes"]["name"]).to eq([@old_name, @new_name])
        expect(audit_response[0]["audited_changes"]["notes"]).to eq([@old_notes, @new_notes])
        expect(audit_response[0]["audited_changes"]["start_date"][0].to_date).to eq(@old_date.to_date)
        expect(audit_response[0]["audited_changes"]["start_date"][1].to_date).to eq(@new_date.to_date)
        expect(audit_response[0]['auditable_type']).to eq("Contract")
      end
    end
  end
end
