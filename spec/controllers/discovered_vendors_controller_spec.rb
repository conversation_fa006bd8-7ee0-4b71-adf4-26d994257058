require 'rails_helper'
include CompanyUserHelper

describe DiscoveredVendorsController, type: :controller do
  create_company_and_user

  let(:ready_for_import_vendor_ids) { ready_for_import_vendors.map(&:id) }
  let!(:ready_for_import_vendors) do
    2.times.map do |index|
      FactoryBot.create(:discovered_vendor, company: company, name: "ReadyForImportVendor#{index + 1}", status: 'ready_for_import')
    end
  end

  let(:ignored_vendor_ids) { ignored_vendors.map(&:id) }
  let!(:ignored_vendors) do
    2.times.map do |index|
      FactoryBot.create(:discovered_vendor, company: company, name: "IgnoredVendor#{index + 1}", status: 'ignored')
    end
  end

  
  describe '#index' do
    context 'when there are discovered vendors' do
      it 'returns ready for import vendors when status is ready_for_import' do
        get :index, format: :json, params: { status: 'ready_for_import', per_page: DiscoveredVendorsController::PER_PAGE_SIZE }
        parsed_response = JSON.parse(response.body)

        expect(response).to have_http_status(:ok)
        expect(parsed_response).to include("discovered_vendors", "total", "page_count")
        expect(parsed_response["discovered_vendors"].length).to eq(2)
      end
    end
  end

  describe '#general_transactions' do
    let!(:discovered_vendor) { FactoryBot.create(:discovered_vendor, company: company, name: "Vendor1") }

    context 'when the discovered vendor has no transactions' do
      it 'responds successfully and sets the discovered_vendor' do
        get :general_transactions, params: { id: discovered_vendor.id, format: :json }
        expect(assigns(:discovered_vendor)).to eq(discovered_vendor)
        expect(response).to have_http_status(:ok)
        parsed_response = JSON.parse(response.body)
        expect(parsed_response["transactions"]).to be_empty
      end
    end

    context 'when the discovered vendor has transactions' do
      let!(:transaction1) { FactoryBot.create(:general_transaction, discovered_vendor: discovered_vendor, amount: 100.0) }
      let!(:transaction2) { FactoryBot.create(:general_transaction, discovered_vendor: discovered_vendor, amount: 200.0) }

      it 'responds successfully and returns the transactions' do
        get :general_transactions, params: { id: discovered_vendor.id, format: :json }
        expect(response).to have_http_status(:ok)
        parsed_response = JSON.parse(response.body)

        expect(parsed_response).to include("transactions")
        expect(parsed_response["transactions"].length).to eq(2)
      end
    end
  end

  describe '#bulk_archive' do
    context 'when there are vendors ready for import' do
      it 'calls the bulk_archive action to archive vendors and updates their status to ignored' do
        post :bulk_archive, params: { discovered_vendor_ids: ready_for_import_vendor_ids }

        expect(response).to have_http_status(:ok)

        ready_for_import_vendors.each do |vendor|
          vendor.reload
          expect(vendor.status).to eq('ignored')
        end
      end
    end

    it 'returns a bad request status if no vendor ids are provided' do
      post :bulk_archive, params: { discovered_vendor_ids: [] }
      expect(response).to have_http_status(:bad_request)
    end
  end

  describe '#bulk_unarchive' do
    context 'when there are vendors with status ignored' do
      it 'calls the bulk_unarchive action to unarchive vendors and updates their status to ready_for_import' do
        post :bulk_unarchive, params: { discovered_vendor_ids: ignored_vendor_ids }

        expect(response).to have_http_status(:ok)

        ignored_vendors.each do |vendor|
          vendor.reload
          expect(vendor.status).to eq('ready_for_import')
        end
      end
    end

    it 'returns a bad request status if no vendor ids are provided' do
      post :bulk_unarchive, params: { discovered_vendor_ids: [] }
      expect(response).to have_http_status(:bad_request)
    end
  end

  describe '#bulk_delete' do
    context 'when there are vendors with status ignored' do
      it 'calls the bulk_delete action to delete vendors and removes them from the database' do
        post :bulk_delete, params: { discovered_vendor_ids: ignored_vendor_ids }

        expect(response).to have_http_status(:ok)

        ignored_vendor_ids.each do |id|
          expect(DiscoveredVendor.find_by(id: id)).to be_nil
        end
      end
    end

    it 'returns a bad request status if no vendor ids are provided' do
      post :bulk_delete, params: { discovered_vendor_ids: [] }
      expect(response).to have_http_status(:bad_request)
    end
  end

  describe '#import_bulk' do
    context 'when there are vendors ready for import' do
      it 'calls the import_bulk action to import vendors and updates their status to imported' do
        post :import_bulk, params: { discovered_vendor_ids: ready_for_import_vendor_ids }

        expect(response).to have_http_status(:ok)

        ready_for_import_vendors.each do |vendor|
          vendor.reload
          expect(vendor.status).to eq('imported')
        end
      end
    end

    it 'returns a bad request status if no vendor ids are provided' do
      post :import_bulk, params: { discovered_vendor_ids: [] }
      expect(response).to have_http_status(:bad_request)
    end
  end

  describe '#summary' do
    it 'responds successfully with an HTTP 200 status' do
      get :summary, format: :json
      expect(response).to have_http_status(:ok)
    end

    it 'returns the correct summary of vendor statuses' do
      get :summary, format: :json
      parsed_response = JSON.parse(response.body)

      expect(parsed_response).to include(
        "total_ready_for_import" => 2,
        "total_imported" => 0,
        "total_ignored" => 2
      )
    end
  end
end
