require 'rails_helper'
include CompanyUserHelper

RSpec.describe BulkDiscoveredUsersController, type: :controller do
  create_company_and_user

  let(:discovered_user1) { create(:discovered_user, company_id: company.id) }

  describe "Bulk functionality of discovered users" do
    context "#ignore" do
      it "should throw an error if params are empty" do
        post :delete, params: {discovered_user_ids: []}

        expect(response.status).to eq(400)
        expect(response.server_error?).to eq(false)
        expect(JSON.parse(response.body)["message"]).to eq("Please include users to ignore.")
      end

      it "should update status as `ignore`." do
        post :delete, params: {discovered_user_ids: [discovered_user1.id]}

        expect(response.status).to eq(200)
        expect(discovered_user1.reload.status).to eq("ignored")
      end
    end

    context "#unignore" do
      it "should throw an error if params are empty" do
        post :update, params: {discovered_user_ids: []}

        expect(response.status).to eq(400)
        #The error? predicate is deprecated and will be removed in Rails 6.0. so we use server_error?
        expect(response.server_error?).to eq(false)
        expect(JSON.parse(response.body)["message"]).to eq("Please include users to unignore.")
      end

      it "should update status as `ready_for_import`." do
        post :update, params: {discovered_user_ids: [discovered_user1.id]}

        expect(response.status).to eq(200)
        expect(discovered_user1.reload.status).to eq("ready_for_import")
      end
    end
  end
end
