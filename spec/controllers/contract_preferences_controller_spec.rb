require 'rails_helper'
include CompanyUserHelper

describe ContractPreferencesController, type: :controller do
  create_company_and_user

  let(:request_data) do
    {
      selected_columns: [
        { name: 'name', friendly_name: 'Name', type: 'string', id: 1, active: false },
        { name: 'contract_type', friendly_name: 'Contract Type', type: 'string', id: 2, active: false },
        { name: 'vendor_name', friendly_name: 'Vendor Name', type: 'string', id: 3, active: false },
        { name: 'category', friendly_name: 'Category', type: 'string', id: 4, active: false },
        { name: 'contract_value_amount', friendly_name: 'Total Cost', type: 'string', id: 5, active: false },
        { name: 'alert_dates', friendly_name: 'Next Notification Date', type: 'date', id: 11, active: false }
      ]
    }
  end

  describe "#index" do
    it "will get the customize asset columns" do
      get :index, params: { format: :json }
      parsed = JSON.parse(response.body)
      expect(parsed["contract_preference"]).to be_present
    end
  end

  describe "#update" do
    it "will successfully update contract preference columns" do
      preference = company.contract_preference.preference
      expect(preference.pluck('name')).not_to include('alert_dates')

      put :update, params: { selected_columns: request_data[:selected_columns], format: :json }

      expect(response.code.to_i).to eq(200)
      expect(JSON.parse(response.body)['message']).to eq('Contract preferences have been saved successfully')

      company.reload
      preference = company.contract_preference.preference
      expect(preference.pluck('name')).to include('alert_dates')
    end
  end
end
