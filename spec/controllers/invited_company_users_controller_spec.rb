require 'rails_helper'
include CompanyUserHelper

describe InvitedCompanyUsersController, type: :controller do
  create_company_and_user
  let(:company_user) { user.company_users.first }

  before do
    set_default_host
    company_user.invite_token = "ASFDASDFASDFADSFADSFADSF"
    company_user.granted_access_at = nil
    company_user.save!
  end

  describe "#show" do
    context "with an existing company user" do
      before(:each) do
        headers = { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
        params = { id: company_user.invite_token, format: :json }
        get :show, params: params
      end

      it "returns the company user" do
        expect(response).to have_http_status(:ok)
        actual_company_user = JSON.parse(response.body)["company_user"]
        expect(actual_company_user["id"]).to eq(company_user.id)
      end
    end

    context "without an existing company user" do
      before(:each) do
        headers = { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
        params = { id: 'AABBCC', format: :json }
        get :show, params: params
      end

      it "returns an error response" do
        expect(response).to have_http_status(:not_found)
      end
    end
  end
end
