require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

describe CustomFormsController, type: :controller do
  create_company_and_user

  let!(:subject) {{ field_attribute_type: 'text', label: 'Subject', options:nil, required:true, note:nil, default_value: 'Ticket', private:false, permit_view_default:true, permit_edit_default:true, name: 'subject', field_position_attributes: {position: 'title'}} }
  let!(:priority) {{ field_attribute_type: 'priority', order_position:2, label: 'Priority', name: 'priority', options: '{\'low\':\'#ffd700\',\'medium\':\'#ffa500\',\'high\':\'#ff0000\'}', required:nil, note:nil, field_position:{position: 'right'}, permit_view_default:true, permit_edit_default:true, private:false, default_value: 'low', field_position_attributes:{position: 'right'}} }
  let!(:status) {{ field_attribute_type: 'status', label: 'Status', options:['Open', 'In Progress', 'Closed'], required: nil, note: nil, default_value: 'Open', private: false, permit_view_default:true, permit_edit_default: true, name: 'status', field_position: {position: 'right'}, field_position_attributes: {position: 'right'}} }
  let!(:assigned_to) {{ field_attribute_type: 'people_list', order_position: 5, label: 'Assigned To', name: 'assigned_to', sort_list: "[\"Ascending\", \"First Name, Last Name\"]", options: nil, required: nil, note: nil, field_position: {position: 'right'}, permit_view_default: true, permit_edit_default: true, private: false, default_value: nil, field_position_attributes: {position: 'right'}} }
  let!(:created_by) {{ field_attribute_type: 'external_user', label: 'Created By', options:nil, required:true, note:nil, default_value: nil, private:false, permit_view_default:true, permit_edit_default:true, name: 'created_by', field_position: {position: 'right'}, field_position_attributes: {position: 'right'}} }
  let!(:asset_list) {{ field_attribute_type: 'asset_list', label: 'Impacted Devices', options:nil, required:nil, note:nil, default_value: nil, private:true, permit_view_default:true, permit_edit_default:true, name: 'impacted_devices', field_position: {position: 'right'}, field_position_attributes: {position: 'right'}} }
  let!(:location_list) {{ field_attribute_type: 'location_list', label: 'Location', options:nil, required:nil, note:nil, default_value: nil, private:true, permit_view_default:true, permit_edit_default:true, name: 'location', field_position: {position: 'right'}, field_position_attributes: {position: 'right'}} }
  let!(:description) {{ field_attribute_type: 'rich_text', label: 'Description', options:nil, required:nil, note:nil, default_value: 'Ticket Description', private:false, permit_view_default:true, permit_edit_default:true, name: 'description' , field_position: {position: 'right'}, field_position_attributes: {position: 'right'}} }
  let!(:attachment) {{ field_attribute_type: 'status', label: 'Status', options:[{"name":"Open","color":"#2ECC71"},{"name":"In Progress","color":"#5DADE2"},{"name":"Closed","color":"#626567"}], required: nil, note: nil, default_value: 'Open', private: false, permit_view_default:true, permit_edit_default: true, name: 'status', field_position: {position: 'right'}, field_position_attributes: {position: 'right'}} }
  let!(:impacted_devices) {{ field_attribute_type: 'asset_list', label: 'Impacted Devices', options:nil, required:nil, note:nil, default_value: nil, private:true, permit_view_default:true, permit_edit_default:true, name: 'impacted_devices', field_position: {position: 'right'}, field_position_attributes: {position: 'right'}} }
  let!(:followers) {{ field_attribute_type: 'people_list', label: 'Followers', options:nil, required:nil, note:nil, default_value: nil, private:true, permit_view_default:true, permit_edit_default:true, name: 'followers', field_position: {position: 'right'}, field_position_attributes: {position: 'right'}} }
  let(:form_name) { "IT General Request" }
  let(:group) { company.groups.find_by(name: "Help Desk Agents") }
  let(:form_params) {
    {
      custom_form: {
        id: nil, 
        company_module: "helpdesk", 
        is_active: true,
        helpdesk_custom_form_attributes: {
          show_in_open_portal: true,
          helpdesk_custom_email_attributes: {
            email: "helpdesk@#{company.subdomain}.localhost",
            name: 'Helpdesk',
          }
        },
        form_name: "Sample Form", 
        custom_form_fields_attributes: custom_form_fields_attributes
      },
      privilege_name: 'HelpTicket'
    }
  }

  let(:custom_form_fields_attributes) {
    [
      {
        field_attribute_type: "text", 
        order_position: 0, 
        label:"Subject",
        name:"subject",
        options: nil,
        required: true,
        note:nil,
        field_position: {
          position:"title"
        },
        permit_view_default: true,
        permit_edit_default: true,
        private: false,
        default_value: nil,
        field_position_attributes: {
          position:"title"
        }
      },
      {
        field_attribute_type: "static_text",
        required: false,
        label: "Static text field",
        field_position: {
          position: "left"
        },
        order_position: 1,
        name: "static_text_field",
        field_position_attributes: {
          position: "left"
        }
      },
      {
        field_attribute_type: "priority",
        order_position: 2,
        label: "Priority",
        name: "priority",
        options: CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS,
        required: nil,
        note: nil,
        field_position: {
          position: "right"
        },
        permit_view_default: true,
        permit_edit_default: true,
        private: false,
        default_value: "low",
        field_position_attributes:{
          position:"right"
        }
      },
      {
        field_attribute_type: "status",
        order_position: 3,
        label: "Status",
        name: "status",
        options: CustomFormFieldTemplate::DEFAULT_STATUS_OPTIONS, 
        required: nil,
        note: nil,
        field_position: {
          position: "right"
        },
        permit_view_default: true,
        permit_edit_default: true,
        private: false,
        default_value: "Open",
        field_position_attributes: {
          position: "right"
        }
      },
      {
        field_attribute_type: "external_user",
        order_position: 4,
        label: "Created By",
        name: "created_by",
        options: nil,
        required: true,
        note: nil,
        field_position: {
          position: "right"
        },
        permit_view_default: true,
        permit_edit_default: true,
        private: false,
        default_value: nil,
        field_position_attributes: {
          position: "right"
        }
      },
      {
        field_attribute_type: "people_list",
        order_position: 5,
        label: "Assigned To",
        name: "assigned_to",
        options: "[{\"id\":#{group.id},\"name\":\"#{group.name}\",\"contributorId\":#{group.contributor_id}}]", 
        required: nil,
        note: nil,
        field_position: {
          position: "right"
        },
        permit_view_default: true,
        permit_edit_default: true,
        private: false,
        default_value: nil, 
        field_position_attributes: {
          position: "right"
        }
      },
      {
        field_attribute_type: "tag", 
        required: false, 
        note: "", 
        label: "Tag", 
        options: "[\"SaaS\",\"Business Service\",\"Business Advertising\",\"Infrastructure\"]", 
        field_position: {
          position: "right"
        }, 
        order_position: 1, 
        name: "tag", 
        field_position_attributes: {
          position: "right"
        }
      }, 
      {
        field_attribute_type: "category", 
        required: false, 
        note: "", 
        label: "Category", 
        options: "[\"Advertising\",\"Analytics\",\"Cloud\",\"Customer Support\",\"Developer Tools\",\"DevOps\",\"Facilities\",\"Finance and Accounting\",\"General\",\"HR\",\"Infrastructure\",\"IT and Security\",\"Marketing\",\"Other\",\"Product and Design\",\"Productivity\",\"Sales and Business Development\",\"Telecom\",\"Uncategorized\"]", 
        field_position: {
          position: "right"
        }, 
        order_position: 2, 
        name: "category", 
        field_position_attributes: {
          position: "right"
        }
      },
      {
        field_attribute_type: "department", 
        required: false, 
        note: "", 
        label: "Department", 
        options: "[\"Development\",\"Facilities\",\"Finance\",\"General\",\"HR\",\"IT\",\"Marketing\",\"Sales\",\"General\"]", 
        field_position: {
          position: "right"
        }, 
        order_position: 3, 
        name: "department", 
        field_position_attributes: {
          position: "right"
        }
      },
      {
        default_value: "Second",
        field_attribute_type: "radio_button", 
        required: true, 
        note: nil, 
        label: "Check Radio Button", 
        options: "[\"First\",\"Second\",\"Third\"]", 
        order_position: 1, 
        name: "check_radio_button", 
        field_position: {
          position: "right"
        }, 
        field_position_attributes: {
          position: "right"
        }, 
      },
      {
        field_attribute_type: "checkbox", 
        required: false, 
        label: "Checkbox", 
        options: "[\"Desktop\",\"Laptop\",\"Router\"]", 
        order_position: 1, 
        name: "checkbox", 
        field_position: {
          position: "right"
        }, 
        field_position_attributes: {
          position: "right"
        }, 
      },
      {
        field_attribute_type: "email", 
        required: false, 
        label: "Email Address", 
        options: nil, 
        order_position: 2, 
        name: "email_address", 
        field_position: {
          position: "right"
        }, 
        field_position_attributes: {
          position: "right"
        }, 
      }
    ]
  }

  let!(:field_values) { [ priority, subject, status, created_by, description, attachment ] }

  let!(:help_ticket_params) {
    {
      'Subject' => 'Base Ticket',
      'Created By' => company_user.contributor_id,
      'Assigned To' => company_user.contributor_id,
      'Status' => 'Open',
      'Priority' => 'low',
      'Description' => 'This is a test ticket.',
      'Location' => nil,
      'Attachment' => '1.png',
      'Followers' => nil,
      'Impacted Devices' => nil,
      workspace: company.workspaces.first
    }
  }

  describe "#create" do
    before { post :create, params: form_params }
    let(:form) { 
      res = JSON.parse(response.body)
      CustomForm.find_by(id: res['form']['id'])
    }

    it "create a form" do
      expect(form).to be_present
    end

    it "records data to the event log" do
      event_log = EventLog.find_by(entity_id: form.id)
      expect(event_log).to be_present
      expect(event_log.activity_type).to eq('created')
      expect(event_log.data['form_name']).to eq('Sample Form')
    end

    it "create form with people_list field have particulars users" do
      assigned_to_field = form.custom_form_fields.find_by_name("assigned_to")
      expect(assigned_to_field).to be_present
      expect(JSON.parse(assigned_to_field.options)[0]['name']).to eq(group.name)
    end

    it "Form with tag and category field" do
      tag_field = form.custom_form_fields.find_by_name("tag")
      category_field = form.custom_form_fields.find_by_name("category")
      expect(tag_field).to be_present
      expect(category_field).to be_present
      expect(JSON.parse(category_field.options).count).to be_present
      expect(JSON.parse(tag_field.options).count).to be_present
    end

    it "Form with department field" do
      department_field = form.custom_form_fields.find_by_name("department")
      expect(department_field).to be_present
      expect(JSON.parse(department_field.options).count).to be_present
    end

    it "Form with radio button field" do
      radio_button_field = form.custom_form_fields.find_by_name("check_radio_button")
      expect(radio_button_field).to be_present
      expect(JSON.parse(radio_button_field.options).count).to be_present
      expect(JSON.parse(radio_button_field.options).count).to eq(3)
      # For some reason, this default value is getting cross by the specs to the category field
      # so I'm goign to comment this out for now
      # expect(radio_button_field.default_value).to eq("Second")
    end

    it "Form with checkbox field" do
      checkbox_field = form.custom_form_fields.find_by_name("checkbox")
      expect(checkbox_field).to be_present
      expect(JSON.parse(checkbox_field.options).count).to be_present
      expect(JSON.parse(checkbox_field.options).count).to eq(3)
    end

    it "Form with email address field" do
      email_address_field = form.custom_form_fields.find_by_name("email_address")
      expect(email_address_field).to be_present
      expect(email_address_field.field_attribute_type).to eq("email")
    end

    it "should not allow creation of same name custom forms" do
      EventLog.destroy_all
      form = company.custom_forms.find_by(form_name: form_name)
      expect(form.form_name).to eq(form_name)
      my_params = form_params.merge({ form_name: form_name })
      post :create, params: my_params
      event_log = EventLog.last
      expect(event_log).to_not be_present
      expect(response).to have_http_status(:unprocessable_entity)
      expect(JSON.parse(response.body)["message"]).to eq("Form name has already been taken")
    end
  end

  describe '#update' do
    let(:form_name) { "Update Form" }

    let(:form) { 
      ActiveRecord::Base.transaction do
        template ||= CustomFormTemplate.find_by(form_name: "IT General Request")
        if template.present?
          attrs = template.attributes.except('id', 'created_at', 'updated_at', 'image_url', 'description')
          attrs['company'] = company
          attrs['workspace'] = company.default_workspace 
          attrs['form_name'] = form_name
          custom_form = CustomForm.find_or_initialize_by(attrs)
          if !custom_form.id? 
            custom_form.helpdesk_custom_form = HelpdeskCustomForm.new
            helpdesk_custom_email = HelpdeskCustomEmail.new(email: "updateform@#{company.subdomain}.#{Rails.application.credentials.root_domain}",
                                                            verified: true,
                                                            company: company)
            custom_form.helpdesk_custom_form.helpdesk_custom_email = helpdesk_custom_email
            custom_form.save!
            everyone = self.company.groups.find_by(name: 'Everyone')
            fields = template.custom_form_field_templates.order('order_position ASC')
            fields.each_with_index do |field, idx|
              attrs = field.attributes.except('id', 'created_at', 'updated_at', 'custom_form_template_id')
              attrs[:custom_form_id] = custom_form.id
              attrs[:order_position] = idx
              attrs[:private] = field.required ? false : ["people_list", "asset_list", "location_list", "contract_list", "vendor_list", "telecom_list"].include?( attrs["field_attribute_type"] )
              new_field = custom_form.custom_form_fields.create(attrs)
              field_position = field.field_position_template
              if field_position
                FieldPosition.create(custom_form_field_id: new_field.id, position: field_position.position)
              end
            end
          end
          custom_form
        end
      end
    }
    
    let(:form_params) {
      {
        id: form.id, 
        company_module: 'helpdesk', 
        default: false, 
        custom_form: { 
          id: form.id, 
          company_id: company.id, 
          is_active: true, 
          helpdesk_custom_form_attributes: {
            show_in_open_portal: true, 
            helpdesk_custom_email_attributes: {
              email: 'helpdesk@localhost',
              name: 'Helpdesk',
            }
          },
          form_name: 'Some Form', 
          form_fields: field_values, 
          custom_form_fields_attributes: field_values
        }
      }
    }

    it 'should update custom form name successfully' do
      my_params = form_params.merge({
        custom_form: {
          form_name: 'Updating Name', 
        }
      })
      put :update, params: my_params
      form.reload
      event_log = EventLog.find_by(entity_id: form.id)
      expect(event_log.activity_type).to eq('updated')
      expect(event_log.data['current_value']['form_name']).to eq(form.form_name)
      expect(event_log.data['previous_value']['form_name']).to eq(form_name)
      expect(response).to have_http_status(:ok)
      expect(form.form_name).to eq('Updating Name')
    end

    it 'should archive form' do
      my_params = form_params.merge({
        custom_form: {
          is_active: false, 
        }
      })
      put :update, params: my_params
      form.reload
      event_log = EventLog.find_by(entity_id: form.id)
      expect(event_log).to be_present
      expect(response).to have_http_status(:ok)
      expect(form.is_active).to eq(false)
    end

    it 'should not allow to remove default status if only one form is left' do
      form.destroy!
      my_form = CustomForm.helpdesk.last
      my_params = form_params.merge({
        id: my_form.id,
        default: false,
        custom_form: { 
          id: my_form.id, 
        }
      })
      put :update, params: my_params
      expect(response).to have_http_status(:conflict)
      expect(JSON.parse(response.body)['message']).to eq("You can't remove last custom form from workspace.")
    end

    describe '#update_custom_form_fields' do
      it 'should successfully update custom form on deleting a field' do
        EventLog.destroy_all
        expect(form.custom_form_fields.count).to eq(11)

        field_attrs = [ 
          priority, 
          status, 
          subject, 
          assigned_to, 
          created_by, 
          asset_list, 
          location_list, 
          description, 
          attachment, 
          impacted_devices,
          followers 
        ]

        id_map = {}
        form.custom_form_fields.find_each do |f|
          id_map[f.name] = f.id
        end
        field_attrs.each { |f| f[:id] = id_map[f[:name]] }

        put :update, params: { 
          id: form.id, 
          company_module: 'helpdesk',
          custom_form: {
            id: form.id,
            default: true,
            is_active: true,
            show_in_open_portal: true,
            form_name: 'Testing Update',
            custom_form_fields_attributes: field_attrs
          }
        }
        form.reload
        event_log = EventLog.second
        expect(event_log.activity_type).to eq('updated')
        expect(event_log.data['current_value']['form_name']).to eq(form.form_name)
        expect(event_log.data['previous_value']['form_name']).to eq(form_name)
        expect(response).to have_http_status(:ok)
        expect(form.custom_form_fields.count).to eq(9)
      end
    end

    describe '#update_custom_form_fields' do
      let!(:form_fields_params) {
        [
          priority,
          subject,
          created_by,
          assigned_to,
          status,
          description
        ]
      }

      it 'should update all the old tickets associated with the custom form on adding fields' do
        id_map = {}
        form.custom_form_fields.find_each do |f|
          id_map[f.name] = f.id
        end
        form_fields_params.each { |f| f[:id] = id_map[f[:name]] }

        ticket_params = help_ticket_params.clone
        ticket_params[:custom_form] = form
        create_ticket(ticket_params)
        ticket = CustomFormValue.find_by(value_str: "Base Ticket").help_ticket
        expect(ticket.custom_form_fields.count).to eq(6)
        expect(company.help_tickets).to be_present

        put :update, params: {
          id: form.id,
          company_module: 'helpdesk',
          custom_form: {
            id: form.id,
            default:true,
            is_active: true,
            show_in_open_portal: true,
            form_name: form_name,
            custom_form_fields_attributes: form_fields_params
          }
        }
        form.reload
        event_log = EventLog.find_by(entity_id: form.id)
        expect(ticket.custom_form_fields.count).to eq(6)
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe '#destroy' do
    let!(:new_form) { 
      FactoryBot.create(:custom_form, 
                        company_id: company.id, 
                        company_module: 'helpdesk', 
                        workspace_id: company.default_workspace.id,
                        is_active: false, 
                        default: false) 
    }

    it 'should delete custom form successfully' do
      EventLog.destroy_all
      delete :destroy, params: { 
        id: new_form.id, 
        company_module: 'helpdesk', 
        company_id: company.id, 
      }
      event_log = EventLog.last
      expect(event_log.activity_type).to eq('deleted')
      expect(event_log.data['form_id']).to eq(new_form.id)
      expect(response).to have_http_status(:ok)
    end
  end

  describe "#show" do
    context "Show Help Ticket" do
      let!(:new_form) {
        FactoryBot.create(:custom_form,
                          company_id: company.id,
                          company_module: 'helpdesk',
                          workspace_id: company.default_workspace.id,
                          is_active: false,
                          default: false)
      }
      let!(:help_ticket1) {
        ticket_params = {
          'Subject' => "Boom boom",
          'Created By' => "<EMAIL>",
          'Assigned To' => [company_user.contributor_id],
          'Followers' => [company_user.contributor_id],
          'Status' => 'Open',
          'Priority' => "medium",
          'Description' => "The red robin sits in the tree.",
          company: company
        }
        create_ticket(ticket_params)
      }

      it "will return success response" do
        get :show, params: {
          id: new_form.id,
          company_module: 'helpdesk',
          company_id: company.id,
        }
        expect(response).to have_http_status(:ok)
      end
    end
  end
end
