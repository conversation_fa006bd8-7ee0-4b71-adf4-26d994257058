require 'rails_helper'
include CompanyUserHelper

RSpec.describe ProbeConfigsController, type: :controller do
  create_company_and_user

  before do
    probe_location = ProbeLocation.create(computer_name: "Desktop-Wert2345", company_id: company.id)
    valid_params =  {
      ip_range:
      [
        {
          name: "Wi-Fi",
          is_enabled: true,
          display_name: "Wi-Fi",
          end_ip_address: "**************",
          start_ip_address: "************"
        },
        {
          name: "Lan",
          is_enabled: false,
          display_name: "My Conn",
          end_ip_address: "************",
          start_ip_address: "***********"
        }
      ],
      schedule_setting:
      {
        scheduled_days: [{scheduled_day: 1, scheduled_times: ["6:00 AM"]}],
        schedule_enabled: true
      },
      wmi_setting: [{password: "5X0uWFcm4rRO0Mz87+OcyQ==", username: "wmiConfig"}],
      ssh_setting: [{password: "5X0uWFcm4rRO0Mz87+OcyQ==", username: "sshConfig"}],
      snmp_setting: [{name: "snmp1", community: "Public"}, {name: "snmp2", community: "Public2"}],
      probe_name: "Desktop-Wert2345",
      company_id: company.id,
      probe_location_id: probe_location.id
    }
    @probe_config = ProbeConfig.create(valid_params)
    controller.send(:set_privilege)
  end

  describe "Update #update" do
    it "Update probe name" do
      patch :update, params: {probe_config: {probe_name: "Desktop-Rhn27w"}, id: @probe_config.id, probe_name: "Desktop-Rhn27w", format: "json"}
      expect(ProbeConfig.first.probe_name).to eq("Desktop-Rhn27w")
    end

    it "Update ip range" do
      ip_range_params =  {
        ip_range:
        [
          {
            name: "Wi-Fi",
            is_enabled: true,
            display_name: "Wi-Fi",
            end_ip_address: "**************",
            start_ip_address: "*************"
          }
        ]
      }

      patch :update, params: {probe_config: ip_range_params, id: @probe_config.id, ip_range: ip_range_params[:ip_range], format: "json"}
      expect(ProbeConfig.first.ip_range.count).to eq(1)
      expect(ProbeConfig.first.ip_range[0]["start_ip_address"]).to eq("*************")
    end

    it "Update wmi settings" do
      wmi_setting_params = {wmi_setting: [{password: "5X0uWFcm4rRO0Mz87+OcyQ==", username: "wmiConfig1"}]}

      patch :update, params: {probe_config: wmi_setting_params, id: @probe_config.id, wmi_setting: wmi_setting_params[:wmi_setting], format: "json"}
      expect(ProbeConfig.first.wmi_setting.count).to eq(1)
      expect(ProbeConfig.first.wmi_setting[0]["username"]).to eq("wmiConfig1")
    end

    it "Update ssh settings" do
      ssh_setting_params = {ssh_setting: [{password: "5X0uWFcm4rRO0Mz87+OcyQ==", username: "sshConfig1"}]}

      patch :update, params: {probe_config: ssh_setting_params, id: @probe_config.id, ssh_setting: ssh_setting_params[:ssh_setting], format: "json"}
      expect(ProbeConfig.first.ssh_setting.count).to eq(1)
      expect(ProbeConfig.first.ssh_setting[0]["username"]).to eq("sshConfig1")
    end

    it "Update snmp settings" do
      snmp_setting_params = {snmp_setting: [{name: "snmp1", community: "Public1"}, {name: "snmp2", community: "Public2"}]}

      patch :update, params: {probe_config: snmp_setting_params, id: @probe_config.id, snmp_setting: snmp_setting_params[:snmp_setting], format: "json"}
      expect(ProbeConfig.first.snmp_setting.count).to eq(2)
      expect(ProbeConfig.first.snmp_setting[0]["community"]).to eq("Public1")
    end

    it "Update snmpv_three settings" do
      snmpv_three_setting_params = {snmpv_three_setting: [
                              {security_name: "sec1", context_name: "context1", context_engine_id: "engin1", privacy: "DES", privacy_secret: "3oE+Ugc6W38TeOiMzb1kFA==", authentication: "MD5", authentication_secret: "lUp4RM0+PqAT7QqfrtjXYA=="},
                              {security_name: "sec2", context_name: "context2", context_engine_id: "engin2", privacy: "None", privacy_secret: "", authentication: "None", authentication_secret: ""}
                            ]}

      patch :update, params: {probe_config: snmpv_three_setting_params, id: @probe_config.id, snmp_setting: snmpv_three_setting_params[:snmpv_three_setting], format: "json"}
      expect(ProbeConfig.first.snmpv_three_setting.count).to eq(2)
      expect(ProbeConfig.first.snmpv_three_setting[1]["context_name"]).to eq("context2")
    end

    it "Update schedule settings" do
      schedule_setting_params = {
        schedule_setting: {
          scheduled_days: [{scheduled_day: 2, scheduled_times: ["6:00 AM"]}, {scheduled_day: 4, scheduled_times: ["12:00 AM"]}],
          schedule_enabled: true
        }
      }

      patch :update, params: {probe_config: schedule_setting_params, id: @probe_config.id, schedule_setting: schedule_setting_params[:schedule_setting], format: "json"}
      expect(ProbeConfig.first.schedule_setting.count).to eq(2)
      expect(ProbeConfig.first.schedule_setting["scheduled_days"][0]["scheduled_day"]).to eq("2")
    end
  end
end
