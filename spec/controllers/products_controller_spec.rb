require 'rails_helper'
include CompanyUserHelper

RSpec.describe ProductsController, type: :controller do
   create_company_and_user

  before do
    company_user.granted_access_at = DateTime.now - 1.hour
    company_user.save!

    @vendor = Vendor.create(
      name: "Vendor",
      department: nil,
      phone_number: nil,
      company_id: company.id,
      address1: nil,
      city: "xyz",
      state: "qwerty",
      url: "www.com",
      email: "<EMAIL>",
      zip: nil,
      account_number: 123456,
      description: "Description",
      imported: nil,
      total_spend: 1231,
      category_id: 0,
      contract_id: nil,
      location_id: nil,
      primary_internal_contact_id: nil,
      secondary_internal_contact_id: nil,
      default_tags: [],
      phone_number_country_code: "US",
      phone_number_country_code_number: "1",
      archived: false)
    @category = Category.create(name: "category", category_type: 0, company_id: company.id)
    @product = Product.create(
      name: "Product",
      logo_url: "www.xyz.com",
      url: "www.logourl.com",
      category_id: @category.id,
      company_id: company.id,
      vendor_id: @vendor.id)
  end

  describe "Post #create" do
    it "create product" do
      post :create, params: {"product"=>{"name" =>" product 1", "url" => "www.abc.com", "category_id" => @category.id, "vendor_id" => @vendor.id }, "vendor_id"=>@vendor.id, "format"=>"json"}
      expect(response.status).to eq(200)
      expect(Product.count).to eq(2)
    end
  end

  describe "Update #update" do
    it "Update product" do
      patch :update, params: {"product"=>{"id"=> @product.id, "name"=>"Product abc", "created_at"=>"2019-09-02T12:48:27.249Z", "updated_at"=>"2019-09-02T12:48:27.249Z", "tag"=>nil, "logo_url"=>nil, "url"=>"dev-test.com", "category_id"=>@category.id, "company_id"=>company.id, "vendor_id"=>@vendor.id}, "vendor_id"=>@vendor.id, "id"=>@product.id, "format"=>"json"}
      expect(response.status).to eq(204)
      expect(Product.first.name).to eq("Product abc")
    end
  end

  describe "Get #index" do
    it "List product" do
      get :index, params: {"vendor_id"=>@vendor.id, "format"=>"json"}
      expect(response.status).to eq(200)
      expect(@vendor.products.count).to eq(1)
    end
  end

  describe "Get #show" do
    it "Show product" do
      get :show, params: {"id"=>@product.id, "vendor_id" => @vendor.id, "format"=>"json" }
      expect(response.status).to eq(200)
    end
  end

  describe "Get #usage_transactions" do
    before do
      allow_any_instance_of(Integrations::Aws::Config).to receive(:verify_keys).and_return(true)
    end

    let!(:aws_account) { create(:aws_config, company: company) }
    let!(:integration) { Integration.find_by(name: 'aws') }
    let!(:company_integration) { create(:company_integration_true,
                                        company: company,
                                        integration: integration,
                                        integrable_type: "Integrations::Aws::Config",
                                        integrable_id: aws_account.id) }

    it "Checks for cloud_usage_transactions count for a specific company" do
      create_list(:cloud_usage_transaction, 3)
      create_list(:cloud_usage_transaction, 2, company: company, company_integration: company_integration)

      get :usage_transactions, params: {"id"=>"#{nil}", "format"=>"json" }
      expect(response.status).to eq(200)
      transactions_count = JSON.parse(response.body)['usage_data'].count
      expect(transactions_count).to eq(2)
      expect(CloudUsageTransaction.all.count).to eq(5)
    end

    it "Checks for cloud_usage_transactions count for a specific product" do
      create_list(:cloud_usage_transaction, 2)
      create_list(:cloud_usage_transaction,
                  3,
                  company: company,
                  company_integration: company_integration,
                  product: @product)

      get :usage_transactions, params: {"id"=>@product.id, "format"=>"json" }
      expect(response.status).to eq(200)
      transactions_count = JSON.parse(response.body)['usage_data'].count
      expect(transactions_count).to eq(3)
      expect(CloudUsageTransaction.all.count).to eq(5)
    end
  end
end
