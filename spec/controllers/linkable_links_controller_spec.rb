require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

RSpec.describe LinkableLinksController, type: :controller do
  create_company_and_user

  before(:each) do
    @asset = FactoryBot.create(:managed_asset, :laptop, company: company)

    ticket_params = {
      'Subject' => "First one",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      company: company
    }
    @help_ticket = create_ticket(ticket_params)
  end

  describe "Create #create" do
    it "Create related item for help ticket" do
      post :create, params:  { linkable_link: { source_id: @help_ticket.linkable.id, target_id: @asset.linkable.id }, linkable_id: @help_ticket.linkable.id}
      expect(@help_ticket.linkable.source_linkable_links.count).to eq(1)
      expect(response.status).to eq 200
    end
  end

  describe "Delete #destroy" do
   let!(:related_item) { LinkableLink.create(source_id: @help_ticket.linkable.id, target_id: @asset.linkable.id) }

    it "Delete related item" do
      delete :destroy, params:  {source_id: @help_ticket.linkable.id, target_id: @asset.linkable.id, category: "ManagedAsset", linkable_id: @help_ticket.linkable.id, id: related_item.id}
      expect(@help_ticket.linkable.source_linkable_links.count).to eq(0)
      expect(response.status).to eq 200
    end
  end
end
