require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

RSpec.describe EntityReassignmentsController, type: :controller do
  create_company_and_user

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
        'Status' => 'In Progress',
        'Priority' => "low",
        'Description' => "What a great day!",
        workspace: company.default_workspace,
      },
      {
        'Subject' => "Boom boom",
        'Created By' => company_user.contributor_id,
        'Assigned To' => [company_user.contributor_id],
        'Status' => 'Open',
        'Priority' => "medium",
        'Description' => "The red robin sits in the tree.",
        workspace: company.default_workspace,
      }
    ]
  }

  describe '#update_entity' do
    let(:managed_asset) { FactoryBot.create(:managed_asset, company: company) }
    let(:help_tickets) { help_ticket_params.map{ |p| create_ticket(p) } }
    let(:contract) { FactoryBot.create(:contract, name: 'contract1', company: company) }
    let!(:contract_contact) { contract.contract_contacts.create(company_user: company_user) }

    let(:user1) { create(:user) }
    let(:company_user1) { create(:company_user, user: user1, company: company) }

    context "contract contact of a contract" do
      it "will return successfully after update" do
        params = {
          entity_reassignments: [
            {
              entity_type: 'contracts',
              params: {
                item_id: contract.id,
                old_user_id: company_user.id,
                company_user_cont_id: company_user1.contributor_id,
              },
            }
          ]
        }
        post :update_entity, params: params
        expect(response).to have_http_status(:ok)
        expect(contract.contract_contacts.first.company_user_id).to eq(company_user1.id)
      end
    end

    context "assignment information of an asset" do
      it "will successfully update used by of the asset" do
        params = {
          entity_reassignments: [
            {
              entity_type: 'managed_assets',
              params: {
                item_id: managed_asset.id,
                used_by_contributor_id: company_user.contributor_id,
              },
            }
          ]
        }
        post :update_entity, params: params
        expect(response).to have_http_status(:ok)
        expect(managed_asset.reload.assignment_information.used_by_contributor_id).to eq(company_user.contributor_id)
      end

      it "will successfully update managed by of the asset" do
        params = {
          entity_reassignments: [
            {
              entity_type: 'managed_assets',
              params: {
                item_id: managed_asset.id,
                managed_by_contributor_id: company_user1.contributor_id,
              },
            }
          ]
        }
        post :update_entity, params: params
        expect(response).to have_http_status(:ok)
        expect(managed_asset.reload.assignment_information.managed_by_contributor_id).to eq(company_user1.contributor_id)
      end
    end

    context "creator or assignee of a ticket" do
      it "will successfully update created by of a ticket" do
        params = {
          entity_reassignments: [
            {
              entity_type: 'help_tickets',
              params: {
                item_id: help_tickets.first.id,
                old_user_id: company_user.id,
                created_by: company_user1.contributor_id,
              },
            }
          ]
        }
        post :update_entity, params: params
        expect(response).to have_http_status(:ok)
        expect(help_tickets.first.creators[0].id).to eq(company_user1.id)
      end

      it "will successfully update assigned to of a ticket" do
        params = {
          entity_reassignments: [
            {
              entity_type: 'help_tickets',
              params: {
                item_id: help_tickets.last.id,
                old_user_id: company_user.id,
                assigned_to: company_user1.contributor_id,
              },
            }
          ]
        }
        post :update_entity, params: params
        expect(response).to have_http_status(:ok)
        expect(help_tickets.last.assigned_users.first).to eq(company_user1.contributor_id)

      end

      it "will successfully update tickets in bulk" do
        params = {
          entity_reassignments: [
            {
              entity_type: 'help_tickets',
              params: {
                bulk_entities: help_tickets.map{ |ticket| {id: ticket.id } },
                old_user_id: company_user.id,
                created_by: company_user1.contributor_id,
              },
            }
          ]
        }
        post :update_entity, params: params
        expect(response).to have_http_status(:ok)
        expect(help_tickets.first.creators[0].id).to eq(company_user1.id)
        expect(help_tickets.last.creators[0].id).to eq(company_user1.id)
      end
    end
  end
end
