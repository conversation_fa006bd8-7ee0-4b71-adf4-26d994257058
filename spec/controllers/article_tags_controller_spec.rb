require 'rails_helper'
include CompanyUserHelper

RSpec.describe ArticleTagsController, type: :controller do
  create_company_and_user

  let!(:article) { create(:article, company: company, workspace_id: workspace.id, company_user_id: company_user.id) }
  let!(:article2) { create(:article, title: 'Test Article 2', slug: 'test-article-2' ,company: company, workspace_id: workspace.id, company_user_id: company_user.id) }
  let!(:article_tag) { FactoryBot.create(:article_tag, company_id: company.id, article_id: article.id) }

  describe "#index" do
    it "lists article tags" do
      params = {
        'id' => article_tag.id
      }
      get :index, params: params
      res = JSON.parse(response.body)
      expect(res.count).to eq(1)
      expect(res.first['tag']).to eq(article_tag.tag)
    end
  end
end
