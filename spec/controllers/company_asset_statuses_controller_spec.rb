require 'rails_helper'
include CompanyUserHelper

RSpec.describe CompanyAssetStatusesController, type: :controller do
  create_company_and_user

  let(:company_asset_statuses) do
    [
      {"name"=>"In Use", "icon"=>"genuicon-checkmark"},
      {"name"=>"Ready to Use", "icon"=>"nulodgicon-arrow-right-b"},
      {"name"=>"Needs Attention", "icon"=>"genuicon-exclamation"},
      {"name"=>"Unusable", "icon"=>"&times;"}
    ]
  end

  before do
    controller.send(:set_privilege)
  end

  describe 'GET #index' do
    it 'return all asset statuses of a company' do
      get :index

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)
      company_asset_statuses.each do |status|
        expect(result['asset_statuses'].find { |res| res['name'] == status['name'] && res['icon'] == status['icon'] }).to be_present
      end
    end
  end

  describe 'POST #create' do
    it 'will create asset status and return all asset statuses of company' do
      params = { company_asset_status: { name: 'For Sale', icon: 'genuicon-exclamation' } }
      statuses_count = company.asset_statuses.count
      post :create, params: params

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)['statuses']
      expect(result.find { |res| res['name'] == 'For Sale' }).to be_present
      expect(result.count).to eq(statuses_count + 1)
    end
  end

  describe 'UPDATE #update' do
    let!(:status_1) { CompanyAssetStatus.create!(name: 'Disposed', icon: 'genuicon-exclamation', company: company) }

    it "will update the company asset status and return all company's statuses" do
      statuses_count = company.asset_statuses.count
      params = { id: status_1.id, company_asset_status: { id: status_1.id, name: 'Escrowed' } }
      put :update, params: params

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)['statuses']
      expect(result.find { |res| res['name'] == 'Escrowed' }).to be_present
      expect(result.count).to eq(statuses_count)
    end
  end

  describe 'DESTROY #destroy' do
    let!(:status_1) { CompanyAssetStatus.create!(name: 'Disposed', icon: 'genuicon-exclamation', company: company) }

    it "will destroy the company asset status and return all company's statuses" do
      params = { id: status_1.id }
      delete :destroy, params: params

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)['statuses']
      expect(result.find { |res| res['name'] == 'Disposed' }).to be_nil
    end
  end
end
