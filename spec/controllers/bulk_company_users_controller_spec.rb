require 'rails_helper'
include CompanyUserHelper

RSpec.describe BulkCompanyUsersController, type: :controller do
  create_company_and_user

  let(:custom_form) { company.custom_forms.company_user.first }
  let!(:company_user1) { create(:company_user, company: company, custom_form: custom_form) }
  let!(:company_user2) { create(:company_user, company: company, custom_form: custom_form) }
  let!(:company_user3) { create(:company_user, company: company, custom_form: custom_form) }

  let(:group1) { create(:group, name:'Group1', company: company) }
  let(:location) { company.locations.first }

  before do
    CompanyUserActivity.destroy_all
  end

  describe "Bulk assignment of group to company users" do
    context "When passing good parameters to bulk_assign_group method" do
      it "will bulk assign group to selected members and return 200 status" do
        ids = [company_user1.id, company_user2.id, company_user3.id]
        post :bulk_assign_group, params: { company_users_ids: ids,  group_id: group1.id }
        company_user1.reload
        expect(response.status).to eq(200)
        expect(JSON.parse(response.body)['message']).to eq "Group has been assigned to company users successfully."

        activity = company_user1.activities.find_by(activity_type: 'added')
        expect(activity).to be_present
        expect(activity.data['activity_label']).to eq("Group")
        expect(activity.data['current_value'].to_i).to eq(group1.id)
      end
    end

    context "When passing bad parameters to bulk_assign_group method" do
      it "will not bulk assign group to selected members and return 422 status if company_users_ids is empty" do
        post :bulk_assign_group, params: { company_users_ids: nil, group_id: group1.id }
        expect(response.status).to eq(422)
        expect(JSON.parse(response.body)['message']).to eq "An unknown error has occured while assigning group to company users."
      end

      it "will not bulk assign group to selected members and return 404 status if group doesn't match" do
        ids = [company_user1.id, company_user2.id, company_user3.id]
        post :bulk_assign_group, params: { company_users_ids: ids, group_id: 123 }
        expect(response.status).to eq(404)
        expect(JSON.parse(response.body)['message']).to eq "Group was not found."
      end
    end
  end

  describe "Bulk assignment of location to company users" do
    context "When passing good parameters to bulk_assign_location method" do
      it "will bulk assign location to selected members and return 200 status" do
        ids = [company_user1.id, company_user2.id, company_user3.id]
        post :bulk_assign_location, params: { company_users_ids: ids,  location_id: location.id }

        company.company_users.where(id: ids).each do |cu|
          expect(cu.custom_form.custom_form_fields.find_by(name: 'location').custom_form_values.last.value_int).to eq(location.id)

          activity = cu.activities.find_by(activity_type: 'added')
          expect(activity).to be_present
          expect(activity.data['activity_label']).to eq("Location")
          expect(activity.data['current_value'].to_i).to eq(location.id)
        end
      end
    end
  end

  describe "Bulk archive company users" do
    context "When passing good parameters to bulk_archive method" do
      it "will bulk archive company users" do
        ids = [company_user1.id, company_user2.id, company_user3.id]
        post :bulk_archive, params: { company_users_ids: ids }

        company.company_users.where(id: ids).each do |cu|
          expect(cu.archived_at).to be_present

          activity = cu.activities.find_by(activity_type: 'archived')
          expect(activity).to be_present
          expect(activity.data['current_value']).to eq("archived")
          expect(activity.owner_id).to eq(company_user.id)
        end
      end
    end

    context "When passing good parameters to bulk_unarchive method" do
      it "will bulk unarchive company users" do
        company_user1.update_columns(archived_at: Time.now)
        company_user2.update_columns(archived_at: Time.now)
        company_user3.update_columns(archived_at: Time.now)
        CompanyUserActivity.destroy_all
        ids = [company_user1.id, company_user2.id, company_user3.id]
        post :bulk_unarchive, params: { company_users_ids: ids }

        company.company_users.where(id: ids).each do |cu|
          expect(cu.archived_at).to be_nil

          activity = cu.activities.find_by(activity_type: 'archived')
          expect(activity).to be_present
          expect(activity.data['current_value']).to eq("unarchived")
          expect(activity.owner_id).to eq(company_user.id)
        end
      end
    end
  end
end
