require 'rails_helper'
include Company<PERSON>serHelper
include <PERSON><PERSON><PERSON><PERSON><PERSON>

describe ContractsController, type: :controller do
  create_company_and_user

  let(:asset) { FactoryBot.create(:managed_asset, company: company) }
  let(:vendor) { FactoryBot.create(:vendor, company_id: company.id, category_id: company.categories.first.id, name: "Microsoft", url: "www.microsoft.com") }

  let(:contract_attributes) do
    {
      name: "Internet",
      alert_dates: [AlertDate.new(date: Date.today+6.months)],
      start_date: Date.today,
      end_date: Date.today+1.year,
      company_id: company.id,
    }
  end

  let(:contract1_attributes) do
    {
      name: "Cable",
      alert_dates: [AlertDate.new(date: Date.today+6.months)],
      start_date: Date.today,
      end_date: Date.today+1.year,
      company_id: company.id
    }
  end

  let!(:location1) { create(:location, custom_form: company.custom_forms.where(company_module: "location").first, company: company) }
  let!(:location2) { create(:location, custom_form: company.custom_forms.where(company_module: "location").first, company: company) }
  let!(:location3) { create(:location, custom_form: company.custom_forms.where(company_module: "location").first, company: company) }
  let!(:location4) { create(:location, custom_form: company.custom_forms.where(company_module: "location").first, company: company) }
  let!(:location5) { create(:location, custom_form: company.custom_forms.where(company_module: "location").first, company: company) }

  let!(:contract) { Contract.create(contract_attributes) }
  let!(:contract1) { Contract.create(contract1_attributes) }

  describe "#all_contracts to" do
    before { login_user(user1) }
    let(:user1) { create(:user) }

    context "unauthorized user to json request" do
      it "will return un-authorized message if user is unauthorized" do
        get :index, format: :json

        expect(response).to have_http_status(:unauthorized)
        expect(JSON.parse(response.body)['message']).to eq("Sorry, you're not authorized to perform this action.")
      end
    end

    context "unauthorized user to html request" do
      it "will redirect to access-denied page if user is unauthorized" do
        get :index, format: :html

        expect(response).to have_http_status(:found)
        expect(response.redirect_url).to start_with("http://secure.localhost/no_access")
      end
    end
  end

  describe "#all_contracts" do
    before { login_user }
    context "with format json" do
      it "grabs all contracts if no params present" do
        get :index, params: { format: :json }

        res = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(res['contracts'].count).to eq(2)
      end
    end
  end

  describe "#create" do
    before { login_user }

    context "with format json" do
      it "creates a new contract" do
        name = "New Contract"
        post :create, params: { contract: { name: name,
                                            start_date: Date.today - 1.year,
                                            end_date: Date.today + 2.year,
                                            company_user_ids: "",
                                            alert_dates_attributes: { "0" => { "date" => Date.today + 3.months }},
                                            location_ids: "#{location1.id}, #{location2.id}, #{location3.id}",
                                            tags_array: ["tag1, tag2"],
                                         }, format: :json}

        expect(response).to have_http_status(:ok)
        contract = Contract.find_by_name(name)
        expect(contract).to be_present
        expect(contract.contract_tags.count).to eq(2)
      end
    end

    context "with product link enabled" do
      it "creates a product linked to the new contract" do
        name = "Product Contract"
        expect(controller).to receive(:create_product_and_transactions)
        post :create, params: { contract: { name: name,
                                            start_date: Date.today - 1.year,
                                            end_date: Date.today + 2.year,
                                            company_user_ids: "",
                                            alert_dates_attributes: { "0" => { "date" => Date.today + 3.months }},
                                            location_ids: "#{location1.id}, #{location2.id}, #{location3.id}",
                                            tags_array: ["tag1, tag2"],
                                            track_spend: "true",
                                        }, format: :json}
      end
    end

    context "with product link disabled" do
      it "creates a contract with no linked product" do
        name = "No Product Contract"
        expect(controller).not_to receive(:create_product_and_transactions)
        post :create, params: { contract: { name: name,
                                            start_date: Date.today - 1.year,
                                            end_date: Date.today + 2.year,
                                            company_user_ids: "",
                                            alert_dates_attributes: { "0" => { "date" => Date.today + 3.months }},
                                            location_ids: "#{location1.id}, #{location2.id}, #{location3.id}",
                                            tags_array: ["tag1, tag2"],
                                            track_spend: "false",
                                        }, format: :json}
      end
    end
  end

  describe "#update" do
    before { login_user }
    context "with format json" do
      it "updates an existing contract" do
        old_name = contract1.name
        new_name = "Updated Contract"
        alert_dates_id = contract1.alert_dates.last.id

        put :update, params: { contract: {  name: new_name,
                                            start_date: Date.today - 1.year,
                                            end_date: Date.today + 2.year,
                                            company_user_ids: "",
                                            alert_dates_attributes: { "0" => { "date" => Date.today + 6.months, "id" => alert_dates_id }},
                                            location_ids: "#{location2.id}, #{location1.id}, #{location4.id}, #{location5.id}",
                                            tags_array: ["tag3"],
                                         }, id: contract1.id, format: :json }
        contract = Contract.find(contract1.id)

        expect(response).to have_http_status(:ok)
        expect(contract.name).to eq(new_name)
        expect(contract.start_date.to_date).to eq(Date.today - 1.year) ### Month is +1 due to Rails vs JS date parsing.
        expect(contract.alert_dates.length).to eq(1)
        expect(contract.contract_tags.length).to eq(1)
        expect(contract.contract_tags.first.tag).to eq("tag3")
        expect(contract.alert_dates[0].date.to_date).to eq( Date.today + 6.months )
      end
    end
  end

  describe '#new' do
    context 'user with permissions' do
      before do
        login_user
        get :new, format: :html
      end

      it 'will allow user to add new contract' do
        expect(response.status).to eq(200)
      end
    end

    context 'user without permissions' do
      before do
        company_user = create(:company_user, company: company)
        login_user(company_user.user)
        get :new, format: :json
      end

      it 'will not allow user to access the page' do
        expect(response.status).to eq(401)
        expect(JSON.parse(response.body)['message']).to eq("Sorry, you're not authorized to perform this action.")
      end
    end
  end
end
