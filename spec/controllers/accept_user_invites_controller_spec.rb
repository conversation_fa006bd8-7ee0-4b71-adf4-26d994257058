require 'rails_helper'
include CompanyUserHelper

RSpec.describe AcceptUserInvitesController, type: :controller do
  create_company_and_user

  before do
    company_user.invite_user
  end

  describe "GET #show" do
    let!(:invite) { get :show, :params => { id: company_user.invite_token } }

    it "is a successful http request" do
      #success? predicate is deprecated and will be removed in Rails 6.0.
      expect(response).to have_http_status(:ok)
    end
  end
end
