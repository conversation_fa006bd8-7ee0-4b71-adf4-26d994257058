require 'rails_helper'
include CompanyUserHelper

RSpec.describe AnalyticsReportTemplatesController, type: :controller do
  create_company_and_user

  let(:workspace) { company.workspaces.first }

  let(:template_1) do
    {
      'name': 'New Report',
      'description': 'This is the first report of v2.',
      'analytics_metrics': {
        'charts': [
          {
            'name': 'priority',
            'type': 'bar',
            'order': 1,
            'filters': {
              'values': [{'name': 'status', 'value': 'Open'}],
              'type': 'priority',
              'customDate': {
                'filterType': {
                  'endDate': 'older_than_one_week',
                  'type': 'status_period',
                  'filter': 'custom_date'
                }
              }
            }
          }
        ]
      },
      'workspace_id': workspace.id,
      'scheduled_reports_attributes': [{
        'email_delivery_frequency': 'once_a_week',
        'file_type': 'PDF',
        'scheduled_at': '2018-03-29',
        'include_table_in_report': true,
        'end_date': '2018-03-29'
      }]
    }
  end

  let(:template_2) do
    {
      'name': 'New Report',
      'description': 'This is the first report of v2.',
      'analytics_metrics': {
        'charts': [
          {
            'name': 'priority',
            'type': 'bar',
            'order': 1,
            'filters': {
              'values': [{'name': 'status', 'value': 'Open'}],
              'type': 'priority'
            }
          }
        ]
      },
      'workspace_id': workspace.id
    }
  end

  describe 'GET #index' do
    it 'return all analytics reports template' do
      get :index

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)
      expect(result.keys.count).to eq(2)
      ['saved_reports', 'default_analytics_reports'].each do |key|
        expect(result.keys.include?(key)).to eq(true)
      end
    end
  end

  # describe 'POST #create' do
  #   it 'will create analytics report template and its scheduled report' do
  #     post :create, params: { analytics_report_template: template_1 }

  #     expect(response).to have_http_status(:created)
  #     result = JSON.parse(response.body)
  #     expect(result['message']).to eq('Report template has been created successfully.')
  #   end
  # end

  describe 'UPDATE #update' do
    let(:report_template) { AnalyticsReportTemplate.create!(template_2) }

    it "will update the analytics report template" do
      put :update, params: { id: report_template.id, analytics_report_template: { id: report_template.id, name: 'Statuses report' } }

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)
      expect(result['message']).to eq('Report template has been updated successfully.')
    end
  end

  describe 'DESTROY #destroy' do
    let(:report_template) { AnalyticsReportTemplate.create!(template_2) }

    it 'will destroy the analytics report template' do
      params = { id: report_template.id }
      delete :destroy, params: params

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)
      expect(result['message']).to eq('Report deleted successfully.')
    end

    it 'will give error if report not found' do
      params = { id: 0 }
      delete :destroy, params: params

      expect(response).to have_http_status(:not_found)
      result = JSON.parse(response.body)
      expect(result['message']).to eq('Report template was not found. Please refresh the page and try again.')
    end
  end
end
