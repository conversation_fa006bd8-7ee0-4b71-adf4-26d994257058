require 'rails_helper'
include CompanyUserHelper

RSpec.describe ReportsController, type: :controller do
  create_company_and_user

  describe "Reports testing" do
    let(:request_data) do
      {
        module_name: "contract",
        options: [
          { name: "contract_value_amount", description: "Total Value", id: 1, },
          { name: "start_date", description: "Starting Date", id: 2, },
          { name: "end_date", description: "Expiry Date", id: 3, },
          { name: "notice_period", description: "Notice Period", id: 4, },
          { name: "notes", description: "Notes", id: 5, },
          { name: "created_at", description: "Created On", id: 6, },
          { name: "updated_at", description: "Last Updated", id: 7, }
        ]
      }
    end
    let(:report_time_frame) do
      {
        timeframeSelection:"all_time",
        startDate:nil,
        endDate:nil
      }
    end
    # it "file extension should be csv" do
    #   post :create_report, { params: { :request_data => request_data , :report_time_frame => report_time_frame, :report_format => "csv" }}
    #   expect(response.content_type).to eq("text/csv")
    # end

    # it "file extension should be pdf" do
    #   post :create_report, { params: { :request_data => request_data , :report_time_frame => report_time_frame, :report_format => "pdf" }}
    #   expect(response.content_type).to eq("application/pdf")
    # end

    # it "file extension should be json" do
    #   post :create_report, { params: { :request_data => request_data , :report_time_frame => report_time_frame, :report_format => "json" }}
    #   expect(response.content_type).to eq("application/json")
    # end

    # it "file extension should be html" do
    #   post :create_report, { params: { :request_data => request_data , :report_time_frame => report_time_frame, :report_format => "html" }}
    #   expect(response.content_type).to eq("text/plain")
    # end
  end
end
