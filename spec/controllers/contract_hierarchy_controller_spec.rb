require 'rails_helper'
include Company<PERSON>ser<PERSON>elper

describe ContractHierarchyController, type: :controller do
  create_company_and_user

  let(:contract_attributes) do
    {
      name: "Internet",
      alert_dates: [AlertDate.new(date: Date.today+6.months)],
      start_date: Date.today,
      end_date: Date.today+1.year,
      company_id: company.id,
    }
  end

  let(:contract1_attributes) do
    {
      name: "Cable",
      alert_dates: [AlertDate.new(date: Date.today+6.months)],
      start_date: Date.today,
      end_date: Date.today+1.year,
      company_id: company.id
    }
  end
  let(:contract2_attributes) do
    {
      name: "Food",
      alert_dates: [AlertDate.new(date: Date.today+6.months)],
      start_date: Date.today,
      end_date: Date.today+1.year,
      company_id: company.id
    }
  end
  let!(:contract) { Contract.create(contract_attributes) }
  let!(:contract2) { Contract.create(contract1_attributes) }
  let!(:contract3) { Contract.create(contract2_attributes) }

  describe 'Contract show page' do
    it 'will return the contract hierarchy level' do
      contract2.parent_id = contract.id
      contract2.save!
      get :index, params: { format: :json, :id => contract2.id }
      json_response = JSON.parse(response.body)
      expect(json_response['hierarchy_level']).to eq(1)
      expect(json_response['linked_contracts'][0]['hierarchy_level']).to eq(2)
      expect(json_response['linked_contracts'][0]['parent_id']).to eq(contract.id)
    end
  end

  describe '#assign_parent_contract' do
    it 'will assign the parent contract id to clone' do
      post :assign_parent_contract, params: { format: :json, :id => contract2.id, :parent_id => contract.id }
      contract2.reload
      expect(response.status).to eq(200)
      expect(contract2.parent_id).to eq(contract.id)
    end
  end

  describe '#unlink_contract' do
    it 'will unlink the contract' do
      contract2.parent_id = contract.id
      contract2.save!
      post :unlink_contract, params: { format: :json, :id => contract2.id }
      contract2.reload
      expect(response.status).to eq(200)
      expect(contract2.parent_id).to be_nil
    end
  end

  describe '#bulk_link_contracts' do
    it 'will link the multiple contract' do
      post :bulk_link_contracts, params: { format: :json, :id => contract.id, :contracts_to_link => [
        { id: contract2.id, name: contract2.name },
        { id: contract3.id, name: contract3.name }
      ]}
      contract.reload
      expect(response.status).to eq(200)
      expect(contract.linked_contracts.length).to eq(2)
      expect(contract.linked_contract_ids).to include(contract2.id)
      expect(contract.linked_contract_ids).to include(contract3.id)
    end
  end

  describe '#descendant_contracts' do
    it 'will return descendant contracts' do
      contract2.parent_id = contract.id
      contract3.parent_id = contract.id
      contract2.save! && contract3.save!
      get :descendant_contracts, params: { format: :json, :id => contract.id }
      json_response  =  JSON.parse(response.body)
      expect(response.status).to eq(200)
      expect(json_response).to include(contract2.id)
      expect(json_response).to include(contract3.id)
      linked_contract_ids = contract.linked_contracts.pluck(:id)
      expect(linked_contract_ids).to include(contract2.id)
      expect(linked_contract_ids).to include(contract3.id)
    end
  end

  describe '#ancestor_contracts' do
    it 'will return ancestor contracts' do
      contract2.parent_contract = contract
      contract3.parent_contract = contract2
      contract2.save! && contract3.save!
      get :ancestor_contracts, params: { format: :json, :id => contract3.id }
      json_response  =  JSON.parse(response.body)
      expect(response.status).to eq(200)
      expect(json_response[0]).to eq(contract.id)
      expect(json_response[1]).to eq(contract2.id)
      expect(contract3.parent_contract.id).to eq(contract2.id)
      expect(contract3.parent_contract.parent_contract.id).to eq(contract.id)
    end
  end
end
