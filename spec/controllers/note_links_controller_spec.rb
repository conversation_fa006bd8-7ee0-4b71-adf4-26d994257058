require 'rails_helper'
include CompanyUserHelper

describe NoteLinksController, type: :controller do
  create_company_and_user

  let!(:contract) { create(:contract, company: company) }
  let!(:asset) { create(:managed_asset, company: company) }
  let!(:help_ticket) { create(:help_ticket, company: company, workspace: workspace) }

  describe "#index" do
    it "should pull the names and IDs of the contracts, assets, and help tickets" do
       get :index, params: { format: :json }

       res = JSON.parse(response.body)

       expect(res.keys).to eq(["contracts", "assets", "help_tickets"])
       expect(res["contracts"].length).to eq(1)
       expect(res["assets"].length).to eq(1)
       expect(res["help_tickets"].length).to eq(1)
       expect(res["contracts"].flatten).to eq([company.contracts.first.id, company.contracts.first.name])
       expect(res["assets"].flatten).to eq([company.managed_assets.first.id, company.managed_assets.first.name])
       expect(res["help_tickets"].flatten).to eq([company.help_tickets.first.id, company.help_tickets.first.subject])
    end
  end
end