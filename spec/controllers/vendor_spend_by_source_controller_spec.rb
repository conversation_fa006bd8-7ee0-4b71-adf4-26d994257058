require 'rails_helper'
include CompanyUserHelper

describe VendorSpendBySourceController, type: :controller do
  create_company_and_user

  let!(:vendor1) { create(:vendor, company_id: company.id, category_id: company.categories.first.id, name: "Microsoft", url: "www.microsoft.com") }
  let!(:vendor2) { create(:vendor, company_id: company.id, category_id: company.categories.second.id, name: "<PERSON><PERSON>", url: "www.gusto.com") }
  let!(:plaid_account) { create(:plaid_account, company: company) }
  let!(:okta_account) {
    int = Integrations::Okta::Config.new(name: "Ok<PERSON>", company: company)
    int.save(validate: false)
    int
  }

  let!(:integration1) { Integration.find_by(name: 'okta') }
  let!(:integration2) { Integration.find_by(name: 'plaid') }
  let!(:company_integration1) { CompanyIntegration.create(company: company, integration: integration1, integrable_type: "Integrations::Okta::Config", status: true, integrable_id: okta_account.id) }
  let!(:company_integration2) { CompanyIntegration.create(company: company, integration: integration2, integrable_type: "PlaidAccount", status: true, integrable_id: plaid_account.id) }

  let!(:general_transaction1) { GeneralTransaction.create(company: company, vendor: vendor1, is_active: true, amount: 110, transaction_date: Date.today - 1.day, status: 0, is_manual: true) }
  let!(:general_transaction2) { GeneralTransaction.create(company: company, vendor: vendor1, is_active: true, amount: 20, transaction_date: Date.today - 5.days, status: 0, company_integration: company_integration1) }
  let!(:general_transaction3) { GeneralTransaction.create(company: company, vendor: vendor2, is_active: true, amount: 100, transaction_date: Date.today - 20.days, status: 0, company_integration: company_integration2, plaid_account_id: plaid_account.id) }
  let!(:general_transaction4) { GeneralTransaction.create(company: company, vendor: vendor2, is_active: false, amount: 90, transaction_date: Date.today - 14.days, status: 0, company_integration: company_integration2, plaid_account_id: plaid_account.id) }

  describe "#index" do
    it "will get the first 4 transactions" do
      get :index

      expect(response).to have_http_status(:ok)
      parsed = JSON.parse(response.body)
      expect(parsed.length).to eq(3)
      totals = parsed.map { |x| x["transactions_total"] }
      expect(totals[0]).to eq(general_transaction1.amount)
      expect(totals[1]).to eq(general_transaction3.amount)
      expect(totals[2]).to eq(general_transaction2.amount)
      expect(totals.include?(general_transaction4.amount)).to be_falsey
      expect(totals.sum).to eq(230.0)
    end
  end

  context 'with custom date range' do
    let!(:transaction1) { GeneralTransaction.create(company: company, vendor: vendor1, is_active: true, amount: 120, transaction_date: Date.today - 7.days, status: 0, is_manual: true) }
    let!(:transaction2) { GeneralTransaction.create(company: company, vendor: vendor1, is_active: true, amount: 50, transaction_date: Date.today - 8.days, status: 0, company_integration: company_integration1) }
    let!(:transaction3) { GeneralTransaction.create(company: company, vendor: vendor2, is_active: true, amount: 170, transaction_date: Date.today - 11.days, status: 0, company_integration: company_integration2, plaid_account_id: plaid_account.id) }
    let!(:transaction4) { GeneralTransaction.create(company: company, vendor: vendor2, is_active: true, amount: 80, transaction_date: Date.today - 15.days, status: 0, company_integration: company_integration2, plaid_account_id: plaid_account.id) }

    it 'will return the first transaction' do
      start_date = Date.today - 8.days
      end_date = Date.today - 6.days

      get :index, params: { date_filter: '3', filter_type: 'custom_date', startDate: start_date, endDate: end_date }

      expect(response).to have_http_status(:ok)
      response_transactions = JSON.parse(response.body)
      expect(response_transactions.length).to eq(1)
      expect(response_transactions[0]["transactions_total"]).to eq(120.0)
    end

    it 'will return the first 3 transactions' do
      start_date = Date.today - 12.days
      end_date = Date.today - 6.days

      get :index, params: { date_filter: '3', filter_type: 'custom_date', startDate: start_date, endDate: end_date }

      expect(response).to have_http_status(:ok)
      parsed = JSON.parse(response.body)
      response_transactions = JSON.parse(response.body)
      expect(response_transactions.length).to eq(3)
      totals = parsed.map { |x| x["transactions_total"] }
      expect(totals[0]).to eq(transaction3.amount)
      expect(totals[1]).to eq(transaction1.amount)
      expect(totals[2]).to eq(transaction2.amount)
      expect(totals.include?(transaction4.amount)).to be_falsey
      expect(totals.sum).to eq(340.0)
    end

    it 'will return the first 4 transactions' do
      start_date = Date.today - 16.days
      end_date = Date.today - 6.days

      get :index, params: { date_filter: '3', filter_type: 'custom_date', startDate: start_date, endDate: end_date }

      expect(response).to have_http_status(:ok)
      parsed = JSON.parse(response.body)
      response_transactions = JSON.parse(response.body)
      expect(response_transactions.length).to eq(3)
      totals = parsed.map { |x| x["transactions_total"] }
      expect(totals[0]).to eq(transaction3.amount + transaction4.amount)
      expect(totals[1]).to eq(transaction1.amount)
      expect(totals[2]).to eq(transaction2.amount)
      expect(totals.sum).to eq(420.0)
    end

    it 'does not return any transaction' do
      start_date = Date.today - 30.days
      end_date = Date.today - 28.days

      get :index, params: { date_filter: '3', filter_type: 'custom_date', startDate: start_date, endDate: end_date }

      expect(response).to have_http_status(:ok)
      response_transactions = JSON.parse(response.body)
      expect(response_transactions.length).to eq(1)
      expect(response_transactions[0]["transactions_total"]).to eq(nil)
    end
  end
end
