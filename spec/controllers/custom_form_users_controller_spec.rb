require 'rails_helper'
include <PERSON><PERSON><PERSON><PERSON><PERSON>per

describe CustomFormUsersController, type: :controller do
  create_company_and_user

  let(:default_form) { company.custom_forms.find_by(default: true, company_module: 'company_user') }
  let(:department_field) { default_form.custom_form_fields.find_by(name: 'department') }
  let(:title_field) { default_form.custom_form_fields.find_by(name: 'title') }

  let!(:new_user) { create(:user, first_name: "scarlet", last_name: "coper") }
  let!(:new_company_user) { create(:company_user, user: new_user, company: company, granted_access_at: nil) }

  let!(:title_value) { create(:custom_form_value,
                              custom_form_field: title_field,
                              value_str: 'Tick title',
                              custom_form: default_form,
                              module: new_company_user,
                              company: company) }

  let!(:dept_value) { create(:custom_form_value,
                             custom_form_field: department_field,
                             value_str: 'Tick dept',
                             custom_form: default_form,
                             module: new_company_user,
                             company: company) }

  before do
    company_user.activities.destroy_all
    @request.env['HTTP_REFERER'] = "#{company.subdomain}.#{Rails.application.credentials.root_domain}/help_center"
  end

  context "with a few fields" do
    it "will create company user with all the fields and values successfully" do
      post :create, params: {"json"=>"{\"values\":[{\"customFormFieldId\":#{title_field.id},\"valueStr\":\"Testing Title\"},{\"customFormFieldId\":#{department_field.id},\"valueStr\":\"Testing Department\"}],\"customFormId\":#{default_form.id},\"user_data\":{\"first_name\":\"John\",\"last_name\":\"Doe\",\"email\":\"<EMAIL>\"}}", "custom_form_id"=>"#{default_form.id}", "format"=>"json"}
      cu = CompanyUser.find_by(id: JSON.parse(response.body)["entity"]["id"])
      title_value = cu.custom_form_values.find_by(custom_form_field_id: title_field.id).value_str
      department_value = cu.custom_form_values.find_by(custom_form_field_id: department_field.id).value_str
      expect(title_value).to eq("Testing Title")
      expect(department_value).to eq("Testing Department")

      activity = cu.activities.find_by(activity_type: 'created')
      expect(activity).to be_present
      expect(activity.data).to eq({})
      expect(activity.company_user_id).to eq(cu.id)
    end

    it "will update company user with all the fields and values successfully" do
      allow_any_instance_of(Aws::CognitoIdentityProvider::Client).to receive(:admin_get_user).and_return(new_user.email)
      post :update, params: {"params"=>{"json"=>"{\"values\":[{\"customFormFieldId\":#{title_field.id},\"valueStr\":\"Testing Title Updated\",\"customFormValueId\":#{title_value.id}},{\"customFormFieldId\":#{department_field.id},\"valueStr\":\"Testing Department Updated\",\"customFormValueId\":#{dept_value.id}}],\"customFormId\":#{default_form.id},\"user_data\":{\"first_name\":\"#{new_user.first_name}\",\"last_name\":\"#{new_user.last_name}\",\"email\":\"#{new_user.email}\"}, \"mfaSettings\":\"{}\"}", "attachment_ids"=>[], "company_module"=>"company_user"}, "controller"=>"custom_form_users", "action"=>"update", "custom_form_id"=>"#{default_form.id}", "id"=>"#{new_company_user.id}", "format"=>"json"}
      cu = CompanyUser.find_by(id: JSON.parse(response.body)["entity"]["id"])
      title_value = cu.custom_form_values.find_by(custom_form_field_id: title_field.id).value_str
      department_value = cu.custom_form_values.find_by(custom_form_field_id: department_field.id).value_str
      expect(title_value).to eq("Testing Title Updated")
      expect(department_value).to eq("Testing Department Updated")

      activity = cu.activities.find_by(activity_type: 'updated')
      expect(activity).to be_present
      expect(activity.data['previous_value']).to eq("Tick title")
      expect(activity.data['current_value']).to eq("Testing Title Updated")
      expect(activity.company_user_id).to eq(cu.id)
    end

    it "will update first and last name and email" do
      allow_any_instance_of(Aws::CognitoIdentityProvider::Client).to receive(:admin_get_user).and_return(new_user.email)
      user_params = {"params"=>{"json"=>"{\"values\":[],\"customFormId\":#{default_form.id},\"user_data\":{\"first_name\":\"#{new_user.first_name}\",\"last_name\":\"#{new_user.last_name}\", \"email\":\"<EMAIL>\"}, \"mfaSettings\":\"{}\"}", "attachment_ids"=>[], "company_module"=>"company_user"}, "controller"=>"custom_form_users", "action"=>"update", "custom_form_id"=>"#{default_form.id}", "id"=>"#{company_user.id}", "format"=>"json"}
      post :update, params: user_params
      cu = CompanyUser.find_by(id: JSON.parse(response.body)["entity"]["id"])
      expect(cu.user.first_name).to eq(new_user.first_name)
      expect(cu.user.last_name).to eq(new_user.last_name)
      expect(cu.user.temp_email).to eq("<EMAIL>")

      activity = company_user.activities.find_by("data ->> 'activity_label' = 'first_name'")
      expect(activity).to be_present
      expect(activity.activity_type).to eq("updated")
      expect(activity.data['current_value']).to eq(new_user.first_name)

      activity2 = company_user.activities.find_by("data ->> 'activity_label' = 'last_name'")
      expect(activity2).to be_present
      expect(activity2.activity_type).to eq("updated")
      expect(activity2.data['current_value']).to eq(new_user.last_name)

      activity3 = cu.activities.find_by(activity_type: 'email_requested')
      expect(activity3).to be_present
      expect(activity3.data['activity_label']).to eq("temp_email")
      expect(activity3.data['current_value']).to eq("<EMAIL>")
      expect(activity3.data['previous_value']).to eq(cu.user.email)
      expect(activity3.owner_id).to eq(cu.id)
    end

    it "will update email" do
      allow_any_instance_of(Aws::CognitoIdentityProvider::Client).to receive(:admin_get_user).and_return(new_user.email)
      user.update_columns(temp_email: "<EMAIL>")
      post :update, params: {"params"=>{"json"=>"{\"values\":[],\"customFormId\":#{default_form.id},\"user_data\":{\"email\":\"<EMAIL>\"}, \"mfaSettings\":\"{}\"}", "attachment_ids"=>[], "company_module"=>"company_user"}, "controller"=>"custom_form_users", "action"=>"update", "custom_form_id"=>"#{default_form.id}", "id"=>"#{new_company_user.id}", "format"=>"json"}

      cu = CompanyUser.find_by(id: JSON.parse(response.body)["entity"]["id"])
      expect(cu.user.email).to eq("<EMAIL>")
      expect(cu.user.confirmation_token).to be_present

      activity = cu.activities.find_by("data ->> 'activity_label' = 'email'")
      expect(activity).to be_present
      expect(activity.activity_type).to eq("updated")
      expect(activity.data['current_value']).to eq("<EMAIL>")
      expect(activity.owner_id).to eq(company_user.id)
    end
  end
end
