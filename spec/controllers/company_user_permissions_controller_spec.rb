require 'rails_helper'
include CompanyUserHelper

RSpec.describe CompanyUserPermissionsController, type: :controller do
  create_company_and_user

  let(:force) { false }
  let(:workspace) { company.default_workspace }
  let(:before_post) { }
  let(:permission_type) { "write" }

  describe "#create" do
    context "with a plain, non-workspace privilege" do
      let(:save_permission) {
        before_post
        params = {
          company_user_id: company_user.id,
          permission: {
            name: permission_name,
            root: [ permission_type ],
          },
          force: force,
        }
        post :create, params: params, as: :json
      }
      let(:permission_name) { "TelecomService" }

      context "with a missing privilege name" do
        let(:permission_name) { nil }
        before { save_permission }

        it "should return a success status" do
          expect(response).to have_http_status(:ok)
        end

        it "should update the privilege" do
          privileges = Privilege.where(contributor: company_user.contributor_id)
          expect(privileges).to be_blank
        end
      end

      context "with no user privileges and not any affecting groups" do
        before { save_permission }

        it "should return a success status" do
          expect(response).to have_http_status(:ok)
        end

        it "should update the privilege" do
          privileges = Privilege.where(contributor_id: company_user.contributor_id, name: permission_name)
          expect(privileges.length).to eq(1)
          privilege = privileges.first.reload
          expect(privilege.name).to eq(permission_name)
          expect(privilege.permission_type).to eq("write")
          expect(privilege.workspace_id).to be_nil

          activity = company_user.activities.find_by(activity_type: 'permission')
          expect(activity).to be_present
          expect(activity.data['current_value']).to eq("write all")
          expect(activity.data['activity_label']).to eq(permission_name)
          expect(activity.owner_id).to eq(company_user.id)
        end
      end

      context "with a existing privilege" do
        before { save_permission }

        let(:before_post) {
          Privilege.create!(contributor_id: company_user.contributor_id,
                            name: permission_name,
                            permission_type: 'read')
        }

        it "should return a success status" do
          expect(response).to have_http_status(:ok)
        end

        it "should update the privilege" do
          privileges = Privilege.where(contributor_id: company_user.contributor_id, name: permission_name)
          expect(privileges.length).to eq(1)
          privilege = privileges.first.reload
          expect(privilege.name).to eq(permission_name)
          expect(privilege.permission_type).to eq("write")
          expect(privilege.workspace_id).to be_nil

          activity = company_user.activities.find_by("data ->> 'current_value' = 'write all'")
          expect(activity).to be_present
          expect(activity.activity_type).to eq("permission")
          expect(activity.data['activity_label']).to eq(permission_name)
          expect(activity.owner_id).to eq(company_user.id)
        end
      end

      context "when the company user is tied to a group" do
        let(:before_post) {
          Privilege.create!(contributor_id: company_user.contributor_id,
                            name: permission_name,
                            permission_type: 'write')
        }

        context "without a force" do
          let(:admins) { company.groups.find_by(name: "Admins") }
          let(:help_desk_agents) { company.groups.find_by(name: "Help Desk Agents") }
          let!(:admins_member) { GroupMember.find_or_create_by(group: admins, contributor_id: company_user.contributor_id) }
          let!(:hesk_desk_agents_member) { GroupMember.find_or_create_by(group: help_desk_agents, contributor_id: company_user.contributor_id) }
          let(:group1) { company.groups.create(name: 'Group 1') }
          let!(:group1_privilege) { Privilege.create(contributor: group1.contributor, name: permission_name, permission_type: 'write', workspace: workspace) }
          let!(:group1_member) { GroupMember.create(group: group1, contributor: company_user.contributor) }
          let(:permission_type) { "read" }

          before { save_permission }

          it "should return a success status" do
            expect(response).to have_http_status(:ok)
          end

          it "should return the group" do
            json = JSON.parse(response.body)
            expect(json['groups']).to be_present
            expect(json['groups'].length).to eq(2)
            names = json['groups'].map { |g| g['name'] }.sort
            expect(names).to eq(['Admins', 'Group 1'])
          end
        end

        context "force create" do
          let(:before_post) {
            Privilege.create!(contributor_id: company_user.contributor_id,
                              name: permission_name,
                              permission_type: 'write')
          }
          let(:force) { true }
          let(:group1) {
            g = company.groups.create(name: 'Group 1')
            g.contributor.privileges.create(name: permission_name,
                                            permission_type: 'write',
                                            workspace: workspace)
            g
          }
          let(:group2) {
            g = company.groups.create(name: 'Group 2')
            g.contributor.privileges.create(name: permission_name,
                                            permission_type: 'write',
                                            workspace: workspace)
            g
          }
          let!(:group1_member) { GroupMember.create(group: group1, contributor: company_user.contributor) }
          let!(:group2_member) { GroupMember.create(group: group2, contributor: company_user.contributor) }
          let(:permission_type) { "read" }

          context "with multiple groups" do
            before { save_permission }

            it "should return a success status" do
              expect(response).to have_http_status(:ok)
            end

            it "should update the privilege" do
              company_user.reload
              expect(company_user.privileges).to be_present
              privilege = company_user.privileges.first
              expect(privilege.permission_type).to eq("read")
              expect(privilege.workspace_id).to be_nil

              activity = company_user.activities.find_by("data ->> 'current_value' = 'read all'")
              expect(activity).to be_present
              expect(activity.activity_type).to eq("permission")
              expect(activity.data['activity_label']).to eq(permission_name)
              expect(activity.owner_id).to eq(company_user.id)
            end

            it "remove the user from the group" do
              company_user.reload
              expect(company_user.contributor.group_members).to be_blank
            end
          end
        end
      end
    end

    context "with a workspace-related privilege" do
      let(:permission_name) { "HelpTicket" }
      let(:save_permission) {
        before_post
        params = {
          company_user_id: company_user.id,
          permission: {
            name: permission_name,
            workspace.id.to_s => [ permission_type ],
          },
          force: force,
        }
        post :create, params: params
      }

      context "with just a user privileges and not any affecting groups" do
        context "with a existing privilege" do
          let(:before_post) {
            Privilege.create!(contributor_id: company_user.contributor_id,
                              workspace_id: workspace.id,
                              name: permission_name,
                              permission_type: 'read')
          }
          before { save_permission }

          it "should return a success status" do
            expect(response).to have_http_status(:ok)
          end

          it "should update the privilege" do
            company_user.reload
            expect(company_user.contributor.privileges).to be_present
            privilege = company_user.contributor.privileges.first
            expect(privilege.name).to eq(permission_name)
            expect(privilege.permission_type).to eq("write")
            expect(privilege.workspace_id).to eq(workspace.id)

            activity = company_user.activities.find_by("data ->> 'current_value' = 'write all'")
            expect(activity).to be_present
            expect(activity.activity_type).to eq("permission")
            expect(activity.data['activity_label']).to eq("#{permission_name} (#{workspace.name} workspace )")
            expect(activity.owner_id).to eq(company_user.id)
          end
        end
      end

      context "when the company user is tied to Everyone" do
        context "without a force" do
          let!(:admins) { company.groups.find_by(name: "Admins") }
          let!(:help_desk_agents) { company.groups.find_by(name: "Help Desk Agents") }
          let!(:admins_member) { GroupMember.find_or_create_by(group: admins, contributor_id: company_user.contributor_id) }
          let!(:hesk_desk_agents_member) { GroupMember.find_or_create_by(group: help_desk_agents, contributor_id: company_user.contributor_id) }
          let(:group1) { company.groups.create(name: 'Group 1') }
          let!(:group1_privilege) { Privilege.create(contributor: group1.contributor, name: permission_name, permission_type: 'write', workspace: workspace) }
          let!(:group1_member) {
            GroupMember.create(group: group1, contributor: company_user.contributor)
          }
          let(:permission_type) { "read" }

          before { save_permission }

          it "should return a success status" do
            expect(response).to have_http_status(:bad_request)
          end

          it "should return the group" do
            json = JSON.parse(response.body)
            expect(json['message']).to be_present
          end
        end

        context "force create" do
          let(:permission_type) { "read" }
          let(:force) { true }
          let(:group1) {
            g = company.groups.create(name: 'Group 1')
            g.contributor.privileges.create(name: permission_name,
                                            permission_type: 'write',
                                            workspace: company.default_workspace)
            g
          }
          let(:group2) {
            g = company.groups.create(name: 'Group 2')
            g.contributor.privileges.create(name: permission_name,
                                            permission_type: 'write',
                                            workspace: company.default_workspace)
            g
          }
          let!(:group1_member) { GroupMember.create(group: group1, contributor: company_user.contributor) }
          let!(:group2_member) { GroupMember.create(group: group2, contributor: company_user.contributor) }

          context "with multiple groups" do
            before { save_permission }

            it "should return a success status" do
              expect(response).to have_http_status(:bad_request)
            end

            it "should update the privilege" do
              obj = JSON.parse(response.body)
              expect(obj).to be_present
            end
          end
        end
      end
    end
  end
end