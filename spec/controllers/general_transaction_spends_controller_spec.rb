require 'rails_helper'
include Company<PERSON>serHelper

describe GeneralTransactionSpendsController, type: :controller do
  create_company_and_user

  before { Timecop.travel(2020, 9, 30, 10, 0) }
  after { Timecop.return }

  before do
    company_user.granted_access_at = Time.now
    company_user.save!
  end

  let!(:vendor1) { FactoryBot.create(:vendor, company_id: company.id, category_id: company.categories.first.id, name: "Microsoft", url: "www.microsoft.com") }
  let!(:vendor2) { FactoryBot.create(:vendor, company_id: company.id, category_id: company.categories.second.id, name: "<PERSON><PERSON>", url: "www.gusto.com") }
  let!(:general_transaction1) { GeneralTransaction.create(company: company, vendor: vendor1, is_active: true, amount: 110, transaction_date: Date.today, status: 0) }
  let!(:general_transaction2) { GeneralTransaction.create(company: company, vendor: vendor1, is_active: true, amount: 35, transaction_date: Date.today - 1.month, status: 0, location_id: company.locations.first.id) }
  let!(:general_transaction3) { GeneralTransaction.create(company: company, vendor: vendor1, is_active: true, amount: 20, transaction_date: Date.today - 2.months, status: 0, location_id: company.locations.first.id) }
  let!(:general_transaction4) { GeneralTransaction.create(company: company, vendor: vendor2, is_active: true, amount: 100, transaction_date: Date.today - 2.months, status: 0) }
  let!(:general_transaction5) { GeneralTransaction.create(company: company, vendor: vendor2, is_active: false, amount: 90, transaction_date: Date.today - 3.months, status: 0) }

  let!(:general_transaction_tag) { GeneralTransactionTag.create(company: company, general_transaction_id: general_transaction3.id, tag: "SaaS") }

  describe "#index" do
    context "get last 6 months of transactions" do
      it "with no location or tags parameters" do
        get :index, params: { date_filter: 6, filter_type: "months" }
        expect(response).to have_http_status(:ok)
        parsed = JSON.parse(response.body)
        parsed = parsed["historical_spend"]
        expect(parsed.length).to eq(8)
        expect(parsed[0]).to eq(0)
        expect(parsed[1]).to eq(0)
        expect(parsed[2]).to eq(0)
        expect(parsed[3]).to eq(0)
        expect(parsed[4]).to eq(0)
        expect(parsed[5]).to eq(general_transaction3.amount + general_transaction4.amount)
        expect(parsed[6]).to eq(general_transaction2.amount)
      end

      it "with location params" do
        get :index, params: { date_filter: 6, filter_type: "months", location_id: company.locations.first.id }

        expect(response).to have_http_status(:ok)
        parsed = JSON.parse(response.body)
        parsed = parsed["historical_spend"]
        expect(parsed.length).to eq(8)
        expect(parsed[0]).to eq(0)
        expect(parsed[1]).to eq(0)
        expect(parsed[2]).to eq(0)
        expect(parsed[3]).to eq(0)
        expect(parsed[4]).to eq(0)
        expect(parsed[5]).to eq(general_transaction3.amount)
        expect(parsed[6]).to eq(general_transaction2.amount)
      end

      it "with tags params" do
        get :index, params: { date_filter: 6, filter_type: "months", tag: general_transaction_tag.tag }

        expect(response).to have_http_status(:ok)
        parsed = JSON.parse(response.body)
        parsed = parsed["historical_spend"]
        expect(parsed.length).to eq(8)
        expect(parsed[0]).to eq(0)
        expect(parsed[1]).to eq(0)
        expect(parsed[2]).to eq(0)
        expect(parsed[3]).to eq(0)
        expect(parsed[4]).to eq(0)
        expect(parsed[5]).to eq(general_transaction3.amount)
        expect(parsed[6]).to eq(0)
      end
    end
  end
end