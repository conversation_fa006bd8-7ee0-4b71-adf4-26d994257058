require 'rails_helper'
include CompanyUserHelper

describe GeneralTransactionsController, type: :controller do
  create_company_and_user

  let!(:vendor) { FactoryBot.create(:vendor, company_id: company.id) }
  let!(:vendor2) { FactoryBot.create(:vendor, company_id: company.id, name: "Vendor 2") }

  describe "#create" do
    context "as a non-recurring transaction" do
      it "will create a new transaction" do
        params = { general_transaction: { company_id: company.id, transaction_date: Date.today - 2.months, recurring: false, vendor_id: vendor.id, product_name: "Product 1", amount: 100 }, vendor_id: vendor.id }
        post :create, params: params

        expect(response).to have_http_status(:ok)
        expect(vendor.general_transactions.count).to eq(1)
        transaction = company.general_transactions.find_by(product_name: "Product 1")
        expect(transaction).to be_present
        expect(transaction.vendor_id).to eq(vendor.id)
        expect(transaction.general_transaction_tags.collect(&:tag)).to eq(vendor.default_tags)
      end
    end

    context "as a recurring transaction" do
      it "will create transactions that have recurred since the initial transaction date" do
        today = Date.today
        params = { general_transaction: { company_id: company.id, transaction_date: today - 2.months, recurring: true, recurrance_end_date: Date.today + 3.months, vendor_id: vendor2.id,  product_name: "Product 2", amount: 100 }, vendor_id: vendor2.id }
        post :create, params: params

        expect(response).to have_http_status(:ok)

        expect(vendor2.general_transactions.count).to eq(3)
        transaction = company.general_transactions.find_by(product_name: "Product 2")
        expect(transaction).to be_present

        recurrences = transaction.recurrences
        expect(recurrences).to be_present
        expect(recurrences.count).to eq(2)
        expect(recurrences.first.transaction_date).to eq(transaction.transaction_date + 1.month)
        expect(recurrences.first.transaction_recurred_from_id).to eq(transaction.id)
        expect(recurrences.first.general_transaction_tags.collect(&:tag)).to eq(vendor2.default_tags)

        expect(recurrences.second.transaction_date).to eq(transaction.transaction_date + 2.months)
        expect(recurrences.second.transaction_recurred_from_id).to eq(transaction.id)
        expect(recurrences.second.general_transaction_tags.collect(&:tag)).to eq(vendor2.default_tags)
      end
    end
  end

  describe "#update" do
    context "as a non-recurring transaction" do
      let!(:general_transaction) { create(:general_transaction, transaction_date: Date.today - 2.days, company_id: company.id, vendor_id: vendor.id, product_name: "Non-Recurring Transaction")}

      it "will update the single transaction" do
        params = { general_transaction: { id: general_transaction.id, product_name: "Newly updated name", amount: 100, vendor_id: vendor.id }, vendor_id: vendor.id, id: general_transaction.id }

        put :update, params: params

        expect(response).to have_http_status(:ok)

        transaction = company.general_transactions.find_by(id: general_transaction.id)
        expect(transaction).to be_present
        expect(transaction.product_name).to eq("Newly updated name")
        expect(transaction.amount).to eq(100)

        expect(transaction.recurrences.count).to eq(0)
      end
    end

    context "as a recurring transaction" do
      let!(:general_transaction2) { create(:general_transaction, transaction_date: Date.today - 2.months, company_id: company.id, vendor_id: vendor2.id, product_name: "Recurring Transaction")}

      it "will update the transaction as well as create the recurring transactions" do
        params = { general_transaction: { id: general_transaction2.id, recurring: true, recurrance_end_date: Date.today + 3.months, product_name: "Product 2", amount: 40, vendor_id: vendor2.id }, vendor_id: vendor2.id, id: general_transaction2.id  }
        put :update, params: params

        expect(response).to have_http_status(:ok)

        transaction = company.general_transactions.find_by(id: general_transaction2.id)
        expect(transaction).to be_present
        expect(transaction.product_name).to eq("Product 2")
        expect(transaction.amount).to eq(40)

        recurrences = transaction.recurrences
        expect(recurrences.count).to eq(2)
        expect(recurrences.first.amount).to eq(40)
        expect(recurrences.second.amount).to eq(40)
        expect(recurrences.first.transaction_recurred_from_id).to eq(general_transaction2.id)
        expect(recurrences.second.transaction_recurred_from_id).to eq(general_transaction2.id)
      end
    end
  end
end