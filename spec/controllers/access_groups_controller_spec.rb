require 'rails_helper'
include CompanyUserHelper
include LoginHelper

RSpec.describe AccessGroupsController, type: :controller do
  create_company_and_user
  let!(:access_group) { Msp::Templates::Group.create!(name: "Test Group 3", company: company) }

  before do
    sign_in user
  end

  describe '#index' do
    it 'returns a list of access groups' do
      get :index, format: :json
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)["msp_groups_count"]).to eq(1)
    end
  end

  describe '#create' do
    context 'with valid parameters' do
      let!(:group_params) {
        {
          group: {
            name: "New Access Group",
            members: [{ root_id: company_user.id, name: company_user.full_name }],
            permissions: [{ name: "CompanyUser", permission_types: ["write"] }]
          }
        }
      }

      it 'creates a new access group and returns the group details' do
        expect {
          post :create, params: group_params, format: :json
        }.to change(Msp::Templates::Group, :count).by(1)
        
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)["name"]).to eq("New Access Group")
      end
    end
  end

  describe "#show" do
    it "returns a specific access group" do
      get :show, params: { id: access_group.id }, format: :json
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)["id"]).to eq(access_group.id)
    end

    it "returns a 404 if the access group does not exist" do
      get :show, params: { id: 'nonexistent_id' }, format: :json
      expect(response).to have_http_status(:not_found)
      expect(JSON.parse(response.body)["message"]).to eq("Access group was not found.")
    end
  end 

  describe '#update' do
    context 'with valid parameters' do
      let(:valid_params) {
        {
          group: {
            id: access_group.id,
            name: "Updated Access Group",
            members: [{ root_id: company_user.id, name: company_user.full_name }],
            permissions: [{ name: "CompanyUser", permission_types: ["write"] }]
          }
        }
      }

      it 'updates the access group and returns the updated group' do
        put :update, params: { id: access_group.id }.merge(valid_params), format: :json
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)["name"]).to eq("Updated Access Group")
        access_group.reload
        expect(access_group.name).to eq("Updated Access Group")
      end
    end
  end

  describe '#destroy' do
    it 'deletes the access group' do
      delete :destroy, params: { id: access_group.id }, format: :json
      expect(response).to have_http_status(:ok)
    end

    it 'returns a 404 if the access group does not exist' do
      delete :destroy, params: { id: 'non-existent-id' }, format: :json
      expect(response).to have_http_status(:not_found)
    end
  end
end
