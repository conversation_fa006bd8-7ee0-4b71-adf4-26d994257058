require 'rails_helper'
include CompanyUserHelper
include <PERSON><PERSON><PERSON><PERSON><PERSON>

describe CompanyUsersController, type: :controller do
  create_company_and_user

  let(:user_1_attributes) do
    {
      email: "<EMAIL>",
      password: "password2",
      first_name: "<PERSON>",
      last_name: "<PERSON>",
      terms_of_services: true
    }
  end

  let(:user_2_attributes) do
    {
      email: "<EMAIL>",
      password: "password2",
      first_name: "<PERSON>",
      last_name: "<PERSON>",
      terms_of_services: true
    }
  end

  let(:user_3_attributes) do 
    {
      email: "<EMAIL>",
      password: "password2",
      first_name: "<PERSON>",
      last_name: "<PERSON>",
    }
  end

  let(:user_4_attributes) do 
    {
      email: "harry@#{company.subdomain}.com",
      password: "password2",
      first_name: "<PERSON>",
      last_name: "<PERSON>",
    }
  end

  before { allow_any_instance_of(CognitoService).to receive(:find_user_by_email).and_return(true) }

  let!(:asset) { FactoryBot.create(:managed_asset, company: company) }
  let!(:user_1) { create(:user, user_1_attributes) }
  let!(:user_2) { create(:user, user_2_attributes) }
  let!(:user_3) { create(:user, user_3_attributes) }
  let!(:user_4) { create(:user, user_4_attributes) }

  let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
  let!(:company_user_0) { user.company_users.find_by(company_id: company.id, custom_form_id: custom_form.id) }
  let!(:company_user_1) { CompanyUser.create(company_id: company.id, user_id: user_1.id, custom_form_id: custom_form.id) }
  let!(:company_user_2) { CompanyUser.create(company_id: company.id, user_id: user_2.id, custom_form_id: custom_form.id) }
  let!(:company_user_3) { CompanyUser.create(company_id: company.id, user_id: user_3.id, granted_access_at: nil, invitation_due_at: nil, custom_form_id: custom_form.id) }
  let!(:company_user_4) { CompanyUser.create(company_id: company.id, user_id: user_4.id, granted_access_at: DateTime.now, invitation_due_at: DateTime.now, custom_form_id: custom_form.id) }

  let(:user_2_update_params) do
    {
      company_user: {
        id: company_user_2.id,
        user_id: user_1.id,
        company_id: company.id,
        user: {
          email: user_2.email,
          first_name: "John",
          last_name: "Ball"
        },
        universal_links: [[{ target: { id: asset.linkable.id, name: asset.name, linkable_type: "ManagedAsset" }}]],
      },
      id: company_user_2.id,
      format: :json
    }
  end

  let(:workspace) { FactoryBot.create(:workspace, company: company, name: 'Workspace') }
  let(:workspace2) { FactoryBot.create(:workspace, company: company, name: 'Workspace 2') }

  describe "#all_company_users to" do
    context "unauthorized user to json request" do
      before { login_user(user_1) }

      it "will return un-authorized message if user is unauthorized" do
        request.env["HTTP_REFERER"] = "/company/users"
        get :index, format: :json

        expect(response).to have_http_status(:unauthorized)
        expect(JSON.parse(response.body)['message']).to eq("Sorry, you're not authorized to perform this action.")
      end
    end

    context "unauthorized user to html request" do
      before { login_user(user_2) }

      it "will redirect to access-denied page if user is unauthorized" do
        request.env["HTTP_REFERER"] = "/company/users"
        get :index, format: :html

        expect(response).to have_http_status(:found)
      end
    end
  end

  describe "#all_company_users to" do
    before { login_user }

    context "authorized user to json request" do
      it "will return all company_users if user is authorized and request has no params" do
        request.env["HTTP_REFERER"] = "/company/users"
        get :index, format: :json
        res = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(res['company_user_count']).to eq(5)
      end
    end
  end

  describe "#destroy" do
    before { login_user }

    context "with a bad id" do
      it "will return a 404" do
        request.env["HTTP_REFERER"] = "/company/users"
        delete :destroy, params: {id: 'THISISNOTANID'}
        expect(response).to have_http_status(:not_found)
      end
    end

    context "with your own company user id" do
      let!(:subject) { 
        request.env["HTTP_REFERER"] = "/company/users"
        delete :destroy, params: { id: company_user.id } 
      }
      let(:company_user) { user.company_users.first }

      it "will not remove the company user" do
        expect(CompanyUser.where(id: company_user.id)).to be_present
      end

      it "will return a 406" do
        expect(response).to have_http_status(:not_acceptable)
      end
    end

    context "with a good id" do
      let!(:subject) {
        request.env["HTTP_REFERER"] = "/company/users"
        delete :destroy, params: { id: company_user.id }
      }
      let(:company_user) { company_user_2 }

      it "will remove the company user" do
        expect(CompanyUser.where(id: company_user.id)).to be_blank
      end

      it "will return a 200" do
        expect(response).to have_http_status(:ok)
      end
    end

    context "with super_admin" do
      let!(:subject) {
        request.env["HTTP_REFERER"] = "/company/users"
      }
      let(:company_user) { company_user_2 }

      before do
        user.super_admin = true
        user.save
        user.company_users[0].update_columns(contributor_id: nil)
      end

      it "will remove the company user with super admin" do
        delete :destroy, params: { id: company_user.id }
        expect(CompanyUser.where(id: company_user.id)).to be_blank
      end
    end

    context 'destroying user' do
      let(:company_3) { create(:company) }
      let!(:company_user_3) { CompanyUser.create(company_id: company_3.id, user_id: user_1.id, custom_form_id: custom_form.id) }
      
      it 'successfully destroy the user along with company user' do
        delete :destroy, params: { id: company_user_2.id }
        expect(User.where(id: company_user_2.user_id)).not_to be_present
      end

      it 'will not destroy the user having multiple company users' do
        delete :destroy, params: { id: company_user_3.id }
        expect(User.where(id: company_user_3.user.id)).to be_present
      end
    end
  end

  describe "#update" do
    before { login_user }

    context "with params and user not present on cognito" do
      it "will update user's attributes locally when email not changed" do
        user_2_update_params[:company_user][:user][:first_name] = "Test"
        user_2_update_params[:company_user][:user][:last_name] = "User"
        request.env["HTTP_REFERER"] = "/company/users"
        allow_any_instance_of(CognitoService).to receive(:find_user_by_email).and_return(false)
        put :update, params: user_2_update_params

        company_user_2.reload
        expect(response).to have_http_status(:ok)
        expect(company_user_2.user.first_name).to eq("Test")
        expect(company_user_2.user.last_name).to eq("User")
      end

      it "will update user's attributes and user except email locally with email changed" do
        user_2_update_params[:company_user][:user][:first_name] = "Test"
        user_2_update_params[:company_user][:user][:last_name] = "User"
        user_2_update_params[:company_user][:user][:email] = "<EMAIL>"
        request.env["HTTP_REFERER"] = "/company/users"
        allow_any_instance_of(CognitoService).to receive(:find_user_by_email).and_return(false)
        put :update, params: user_2_update_params

        company_user_2.reload
        expect(response).to have_http_status(:ok)
        expect(company_user_2.user.first_name).to eq("Test")
        expect(company_user_2.user.last_name).to eq("User")
        expect(company_user_2.user.email).to eq("<EMAIL>")
      end
    end

    context "with params and user present on cognito" do
      it "will update user's attributes locally and on cognito except email if email changed and present on cognito" do
        user_2_update_params[:company_user][:user][:first_name] = "Test"
        user_2_update_params[:company_user][:user][:email] = "<EMAIL>"
        request.env["HTTP_REFERER"] = "/company/users"
        put :update, params: user_2_update_params

        company_user_2.reload
        expect(response).to have_http_status(:ok)
        expect(company_user_2.user.first_name).to eq("Test")
        expect(company_user_2.user.email).not_to eq("<EMAIL>")
      end

      it "will update user's attributes and email locally and on cognito if email not changed" do
        user_2_update_params[:company_user][:user][:first_name] = "Test"
        user_2_update_params[:company_user][:user][:last_name] = "User"
        request.env["HTTP_REFERER"] = "/company/users"
        put :update, params: user_2_update_params

        company_user_2.reload
        expect(response).to have_http_status(:ok)
        expect(company_user_2.user.first_name).to eq("Test")
        expect(company_user_2.user.last_name).to eq("User")
      end
    end

    context "with params and email present locally" do
      it "will not update user's attributes because email already exists" do
        user_2_update_params[:company_user][:user][:email] = user_1.email
        request.env["HTTP_REFERER"] = "/company/users"
        put :update, params: user_2_update_params

        company_user_2.reload
        expect(response).to have_http_status(:conflict)
        expect(company_user_2.user.email).not_to eq("<EMAIL>")
      end
    end

    context "with newly updated email" do
      it "will update user's temp email" do
        user_2_update_params[:company_user][:user][:email] = "<EMAIL>"
        request.env["HTTP_REFERER"] = "/company/users"
        put :update, params: user_2_update_params
        company_user_2.reload
        expect(response).to have_http_status(:ok)
        expect(company_user_2.user.temp_email).to eq("<EMAIL>")
      end
    end
  end

  describe "#update_user_office_status" do
    context "out of office status" do
      it "will update user's out of office status" do
        put :update_user_office_status, params: {id: company_user_2.id, format: :json}
        company_user_2.reload

        activity = company_user_2.activities.find_by(activity_type: 'updated')
        expect(activity).to be_present
        expect(activity.data['activity_label']).to eq('out_of_office')
        expect(activity.data['current_value']).to eq(true)
        expect(company_user_2.out_of_office).to eq(true)
      end
    end
  end

  describe "#show" do
    before do
      @group = FactoryBot.create(:group, company: company)
      GroupMember.create(group_id: @group.id, contributor_id: user.company_users.first.contributor_id)
      login_user
    end

    context "user profile" do
      it "will not update user's attributes and user locally with email changed" do
        request.env["HTTP_REFERER"] = "/company/users"
        get :show, params: { id: user.company_users.first.id, format: :json }
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response["groups"].count).to eq(2)
        groups = json_response["groups"].map{ |g| g["name"] }.sort
        expect(groups).to eq(['Admins', 'Grand Poobahs'])
      end
    end
  end

  describe "#filtering params" do
    context "when filtering by company domain" do
      it "returns company users with matching domain" do
        get :index, params: { domain: "company_domain", format: :json }
        expect(response).to have_http_status(:ok)        
        res = JSON.parse(response.body)
        expect(res["company_users"].count).to eq(1)
      end
    end

    context "when filtering by external domain" do
      it "returns company users with non-matching domain" do
        get :index, params: { domain: "external_domain", format: :json }
        expect(response).to have_http_status(:ok)        
        res = JSON.parse(response.body)
        expect(res["company_users"].count).to eq(4)
      end
    end

    context "when filtering by invited state" do
      it "returns invited company users" do
        get :index, params: { state: "invited", format: :json }
        expect(response).to have_http_status(:ok)
        res=JSON.parse(response.body)
        expect(res["company_users"].count).to eq(2)
      end
    end

    context "when filtering by uninvited state" do
      it "returns uninvited company users" do
        get :index, params: { state: "uninvited", format: :json }
        expect(response).to have_http_status(:ok)
        res=JSON.parse(response.body)
        expect(res["company_users"].count).to eq(3)
      end
    end

    context "when filtering by first name ascending sort" do
      it "returns company users in order of ascending first name" do
        get :index, params: { sort_by: 'first_name asc', format: :json }
        expect(response).to have_http_status(:ok)
        res = JSON.parse(response.body)
        first_names = res["company_users"].map { |user| user["user"]["first_name"] }
        expect(first_names).to eq(first_names.sort)
      end
    end

    context "when filtering by first name descending sort" do
      it "returns company users in order of descending first name" do
        get :index, params: { sort_by: 'first_name desc', format: :json }
        expect(response).to have_http_status(:ok)
        res = JSON.parse(response.body)
        first_names = res["company_users"].map { |user| user["user"]["first_name"] }
        expect(first_names).to eq(first_names.sort.reverse)
      end
    end

    context "when filtering by last name ascending sort" do
      it "returns company users in order of ascending last name" do
        get :index, params: { sort_by: 'last_name asc', format: :json }
        expect(response).to have_http_status(:ok)
        res = JSON.parse(response.body)
        last_names = res["company_users"].map { |user| user["user"]["last_name"] }
        expect(last_names).to eq(last_names.sort)
      end
    end

    context "when filtering by last name descending sort" do
      it "returns company users in order of descending last name" do
        get :index, params: { sort_by: 'last_name desc', format: :json }
        expect(response).to have_http_status(:ok)
        res = JSON.parse(response.body)
        last_names = res["company_users"].map { |user| user["user"]["last_name"] }
        expect(last_names).to eq(last_names.sort.reverse)
      end
    end

    context "when filtering by email ascending sort" do
      it "returns company users in order of ascending email" do
        get :index, params: { sort_by: 'email asc', format: :json }
        expect(response).to have_http_status(:ok)
        res = JSON.parse(response.body)
        emails = res["company_users"].map { |user| user["user"]["email"] }
        expect(emails).to eq(emails.sort)
      end
    end

    context "when filtering by email descending sort" do
      it "returns company users in order of descending email" do
        get :index, params: { sort_by: 'email desc', format: :json }
        expect(response).to have_http_status(:ok)
        res = JSON.parse(response.body)
        emails = res["company_users"].map { |user| user["user"]["email"] }
        expect(emails).to eq(emails.sort.reverse)
      end
    end
  end
end
