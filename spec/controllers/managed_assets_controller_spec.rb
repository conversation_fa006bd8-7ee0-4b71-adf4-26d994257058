require 'rails_helper'
include CompanyUserHelper
include <PERSON><PERSON><PERSON><PERSON><PERSON>

describe ManagedAssetsController, type: :controller do
  create_company_and_user

  let!(:asset_type) { CompanyAssetType.create(name: 'Laptop') }
  let!(:mobile_asset_type) { CompanyAssetType.create(name: 'Mobile') }
  let!(:location) { Location.create(custom_form: company.custom_forms.last) }
  let!(:laptop_asset) { create(:managed_asset, :laptop, company_id: company.id, location_id: company.locations.first.id) }
  let!(:parent_asset) { create(:managed_asset, :clone_asset, company_id: company.id, location_id: company.locations.first.id) }
  let!(:parent_integration_asset) { create(:managed_asset, :clone_integration_asset, company_id: company.id, location_id: company.locations.first.id) }
  let!(:parent_cloud_asset) { create(:managed_asset, :clone_cloud_asset, company_id: company.id, location_id: company.locations.first.id) }
  let!(:mobile_asset) { create(:managed_asset, :mobile, company_id: company.id, archived: true , location_id: company.locations.first.id) }
  let(:contract) { FactoryBot.create(:contract, name: 'contract1', company: company) }
  let(:vendor) { FactoryBot.create(:vendor, company_id: company.id, category_id: company.categories.first.id, name: "Microsoft", url: "www.microsoft.com") }
  let(:managed_asset_attributes) do
    {
      name: "Laptop",
      company_id: company.id,
      operating_system: "Windows 10",
      company_asset_type_id: asset_type.id,
      location_id: location.id,
      archived: false,
      links_array: {
        "0" => { "id" => contract.linkable.id, "name" => contract.name, "type" => "Vendor" },
        "1" => { "id" => vendor.linkable.id, "name" => vendor.name, "type" => "Contract" }
      },
    }
  end

  let(:computer_details_managed_asset_attributes) do
    {
      name: "Stevan Laptop",
      company_id: company.id,
      operating_system: "Windows 10",
      company_asset_type_id: asset_type.id,
      location_id: location.id,
      system_uuid: "1275635-123WE-789RG329",
      system_up_time: "144 Hours",
      os_version: "10.0.19043",
      hardware_detail_attributes: {
        processor: "Intel(R) Core(TM) i5-10210U CPU @ 1.60GHz 4 Core",
        hard_drive: "12 GB",
        memory: "1170 GB",
        device_id: "FE63C5D-56A0-4C67-85BE-44E9323",
        processor_logical_cores: "8 Cores",
        processor_cores: "4 Cores",
        hardware_version: "S71 Ver. 01.13.01"
      },
      hardware_detail_type: "ComputerDetail"
    }
  end

  let!(:asset_with_computer_detail1) { ManagedAsset.find_by_name('AssetMobile').hardware_detail.update_columns(disk_encryption: "Off") }
  let!(:asset_with_computer_detail2) { ManagedAsset.find_by_name('AssetLaptop').hardware_detail.update_columns(disk_encryption: "On") }

  let(:phone_detail_attributes) { attributes_for(:phone_detail) }

  describe "#all assets to" do
    context "with unauthorized user" do
      let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
      let(:user) { create(:user) }
      let!(:company_user) { create(:company_user, company: company, user: user, custom_form_id: custom_form.id) }

      before do
        allow(request.env['warden']).to receive(:authenticate!).and_return(user)
        allow(controller).to receive(:current_user).and_return(user)
      end

      context "to json request" do
        it "will return un-authorized message if user is unauthorized" do
          get :index, format: :json

          expect(response).to have_http_status(:unauthorized)
          expect(JSON.parse(response.body)['message']).to eq("Sorry, you're not authorized to perform this action.")
        end
      end

      context "to html request" do
        it "will redirect to access-denied page if user is unauthorized" do
          get :index, format: :html

          expect(response).to have_http_status(:ok)
          expect(response).to render_template("layouts/access_denied")
        end
      end
    end

    context "authorized user with json request" do
      it "will return assets with type" do
        id = CompanyAssetType.find_by(name: "Laptop")&.id
        get :index, :params => { company_asset_type_id: id, format: :json }

        json_response = JSON.parse(response.body)
        assets_count = ManagedAsset.joins(:asset_type).where("company_asset_types.name = (?) and managed_assets.location_id = ?", "Laptop", company.locations.first.id).count
        expect(response).to have_http_status(:ok)
        expect(json_response['assets_info']['assets'].count).to eq(assets_count)
      end

      it "will return assets with search" do
        get :index, :params => { search_term: "all", search: "Asset", format: :json }

        json_response = JSON.parse(response.body)
        assets_count = ManagedAsset.where("managed_assets.name ilike ?", "%Asset%").count
        expect(response).to have_http_status(:ok)
        expect(json_response['assets_info']['assets'].count).to eq(assets_count)
      end
    end
  end

  describe "#all assets" do
    it "grabs all the active assets with manually added source" do
      managed_asset = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({ name:"Laptop", source: "uploaded" }))
      managed_asset1 = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({ name:"Desktop", source: "manually_added" }))
      managed_asset2 = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({ name:"Printer", source: "manually_added" }))
      assets_count = ManagedAsset.manually_added.count

      get :index, params: { source: "manually_added", format: :json }

      expect(response.status).to eq(200)
      expect(JSON.parse(response.body)['assets_info']['assets'].length).to eq(assets_count)
    end

    it "grabs all the active assets with status ready to use" do
      in_use_status = CompanyAssetStatus.find_by(name: 'In Use')
      ready_to_use_status = CompanyAssetStatus.find_by(name: 'Ready to Use')
      managed_asset = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({ name:"Laptop", custom_status: in_use_status }))
      managed_asset1 = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({ name:"Desktop", custom_status: ready_to_use_status }))
      managed_asset2 = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({ name:"Printer", custom_status: ready_to_use_status }))

      get :index, params: { status: [ready_to_use_status.id], format: :json }

      expect(response.status).to eq(200)
      expect(JSON.parse(response.body)['assets_info']['assets'].length).to eq(2)
    end

    it "grabs all the active assets with location" do
      managed_asset = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({ name:"Laptop" }))
      managed_asset1 = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({ name:"Desktop" }))

      get :index, params: { location_id: location.id, format: :json }

      expect(response.status).to eq(200)
      expect(JSON.parse(response.body)['assets_info']['assets'].length).to eq(2)
    end

    it "grabs all the active assets with no warranty" do
      managed_asset = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({ name:"Laptop", warranty_expiration: nil }))
      assets_count = ManagedAsset.no_warranty.count

      get :index, params: { warranty_expiration: "no_warranty", format: :json }

      expect(response.status).to eq(200)
      expect(JSON.parse(response.body)['assets_info']['assets'].length).to eq(assets_count)
    end

    it "grabs all the active assets for analytics having specific asset type" do
      managed_asset1 = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({ name:"Asset 1" }))
      managed_asset2 = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({ name:"Asset 2" }))
      managed_asset3 = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({ name:"Asset 2", company_asset_type_id: mobile_asset_type.id }))
      asset_count = ManagedAsset.where(:company_asset_type_id => asset_type.id).count

      get :index, params: {
        format: :json,
        analytics_filters: { analytics_asset_types: [asset_type.id] }.to_json
      }

      expect(response.status).to eq(200)
      result = JSON.parse(response.body)
      expect(result['assets_info']['assets'].length).to eq(asset_count)
      expected_names = [managed_asset1.name, managed_asset2.name]
      resulted_names = result['assets_info']['assets'].map { |asset| asset['name'] }
      expect(resulted_names).to match_array(expected_names)
    end

    it "grabs all the active assets for analytics having specific manufacturer" do
      managed_asset1 = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({ name:"Asset with manufacturer 1", manufacturer: "Dell" }))
      managed_asset2 = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({ name:"Asset with manufacturer 2", manufacturer: "Lenovo" }))
      get :index, params: {
        format: :json,
        analytics_filters: { analytics_asset_manufacturers: [managed_asset1.manufacturer] }.to_json
      }

      expect(response.status).to eq(200)
      result = JSON.parse(response.body)
      expect(result['assets_info']['assets'].length).to eq(1)
      expected_assets = [managed_asset1.name]
      resulted_assets = result['assets_info']['assets'].map { |asset| asset['name'] }
      expect(expected_assets).to match_array(resulted_assets)
    end

    it "grabs all the active assets for analytics having OS type softwares installed in them" do
      get :index, params: {
        format: :json,
        analytics_filters: { analytics_operating_system: ['MacOS'] }.to_json
      }
      expect(response.status).to eq(200)
      result = JSON.parse(response.body)
      expect(result['assets_info']['assets'].length).to eq(1)
      resulted_asset_name = result['assets_info']['assets'].map { |asset| asset['name'] }
      expect(resulted_asset_name.include?('AssetLaptop')).to be_truthy
    end

    it "grabs all the active assets for analytics having specific softwares installed in them" do
      get :index, params: {
        format: :json,
        analytics_filters: { analytics_installed_software: ['MacOS'] }.to_json
      }

      expect(response.status).to eq(200)
      result = JSON.parse(response.body)
      expect(result['assets_info']['assets'].length).to eq(1)
      resulted_asset_name = result['assets_info']['assets'].map { |asset| asset['name'] }
      expect(resulted_asset_name.include?('AssetLaptop')).to be_truthy
    end

    it "grabs all the active assets for analytics with status needs attention" do
      in_use_status = CompanyAssetStatus.find_by(name: 'In Use')
      needs_attention_status = CompanyAssetStatus.find_by(name: 'Needs Attention')
      managed_asset = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({ name:"Laptop", custom_status: in_use_status }))
      managed_asset1 = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({ name:"Desktop", custom_status: needs_attention_status }))

      get :index, params: {
        format: :json,
        analytics_filters: { analytics_availability_status: [needs_attention_status.id] }.to_json
      }

      expect(response.status).to eq(200)
      expect(JSON.parse(response.body)['assets_info']['assets'].length).to eq(1)
    end

    it "grabs all the active assets for analytics having specific location" do
      managed_asset = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({ name:"Laptop" }))
      managed_asset1 = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({ name:"Desktop" }))

      get :index, params: {
        format: :json,
        analytics_filters: { analytics_location_filter: [location.id] }.to_json
      }

      expect(response.status).to eq(200)
      expect(JSON.parse(response.body)['assets_info']['assets'].length).to eq(2)
    end

    it "grabs all the active assets for analytics having specific warranty" do
      managed_asset = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({ name:"Laptop" }))
      managed_asset1 = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({ name:"Desktop", warranty_expiration: nil }))
      assets_count = ManagedAsset.no_warranty.count

      get :index, params: {
        format: :json,
        analytics_filters: { analytics_asset_warranty: ['no_warranty'] }.to_json
      }

      expect(response.status).to eq(200)
      expect(JSON.parse(response.body)['assets_info']['assets'].length).to eq(assets_count)
    end

    it "grabs all the active assets for analytics having device encryption turned on" do
      assets_names_having_computer_details = ['AssetLaptop', 'AssetMobile']
      assets_with_computer_details = ManagedAsset.where(name: assets_names_having_computer_details)

      get :index, params: {
        format: :json,
        analytics_filters: { analytics_device_encryption: ['On'] }.to_json
      }

      expect(response.status).to eq(200)
      result = JSON.parse(response.body)
      expect(result['assets_info']['assets'].length).to eq(1)
      resulted_asset_name = result['assets_info']['assets'].map { |asset| asset['name'] }
      expect(resulted_asset_name.include?('AssetLaptop')).to be_truthy
    end
  end

  describe "#create" do
    it "will create asset with polymorphic hardware_detail" do
      managed_asset = managed_asset_attributes.merge(hardware_detail_attributes: phone_detail_attributes,
                                                     hardware_detail_type: "PhoneDetail")
      post :create, params: { managed_asset: managed_asset, format: :json }

      phone_detail = PhoneDetail.last
      json_response = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(json_response["message"]).to eq("Asset was successfully created")
      expect(json_response["asset"]["hardware_detail"]["id"]).to eq(phone_detail.id)
    end

    it "will create asset with hardware details of computer_detail" do
      post :create, params: { managed_asset: computer_details_managed_asset_attributes, format: :json }

      computer_detail = ComputerDetail.last
      json_response = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(json_response["message"]).to eq("Asset was successfully created")
      expect(json_response["asset"]["system_uuid"]).to eq("1275635-123WE-789RG329")
      expect(json_response["asset"]["hardware_detail"]["id"]).to eq(computer_detail.id)
      expect(json_response["asset"]["hardware_detail"]["device_id"]).to eq("FE63C5D-56A0-4C67-85BE-44E9323")
      expect(json_response["asset"]["hardware_detail"]["processor_logical_cores"]).to eq("8 Cores")
    end

    it "will clone softwares, attachments and custom detail of asset" do
      managed_asset = managed_asset_attributes.merge(
        company_asset_type_id: parent_asset.company_asset_type_id,
        parent_asset_id: parent_asset.id,               
        asset_softwares: "true",                       
        custom_detail: "true",                                                       
        managed_asset_attachments: parent_asset.managed_asset_attachments.pluck(:id).join(",")
      )

      post :create, params: { managed_asset: managed_asset, format: :json }

      new_asset = ManagedAsset.last
      json_response = JSON.parse(response.body)

      expect(response).to have_http_status(:ok)
      expect(json_response["message"]).to eq("Asset was successfully created")

      expect(new_asset.asset_softwares.count).to eq(parent_asset.asset_softwares.count)
      expect(new_asset.managed_asset_attachments.count).to eq(parent_asset.managed_asset_attachments.count)
      expect(new_asset.details).to eq(parent_asset.details)
      expect(new_asset.image.filename.to_s).to eq(parent_asset.image.filename.to_s)
    end

    it "will clone system detail and sources of asset" do
      managed_asset = managed_asset_attributes.merge(
        company_asset_type_id: parent_integration_asset.company_asset_type_id,
        parent_asset_id: parent_integration_asset.id,
        system_detail: "true"
      )

      post :create, params: { managed_asset: managed_asset, format: :json }

      new_asset = ManagedAsset.last
      json_response = JSON.parse(response.body)

      expect(response).to have_http_status(:ok)
      expect(json_response["message"]).to eq("Asset was successfully created")

      expect(new_asset.system_details.count).to eq(parent_integration_asset.system_details.count)
      expect(new_asset.system_details.pluck(:detail_category, :detail_data)).to match_array(parent_integration_asset.system_details.pluck(:detail_category, :detail_data))
      expect(new_asset.asset_sources.map(&:asset_data)).to include(*parent_integration_asset.asset_sources.map(&:asset_data))
    end

    it "will clone cloud information of asset" do
      managed_asset = managed_asset_attributes.merge(
        company_asset_type_id: parent_cloud_asset.company_asset_type_id,
        parent_asset_id: parent_cloud_asset.id,
        cloud_detail: "true"
      )

      post :create, params: { managed_asset: managed_asset, format: :json }

      new_asset = ManagedAsset.last
      json_response = JSON.parse(response.body)

      expect(response).to have_http_status(:ok)
      expect(json_response["message"]).to eq("Asset was successfully created")
      expect(new_asset.cloud_asset_attributes.count).to eq(parent_cloud_asset.cloud_asset_attributes.count)
    end
  end

  describe "#all_assets" do
    context "with format json to authorized user" do
      before do
        ManagedAsset.destroy_all
      end
      it "grabs all assets" do
        managed_asset = ManagedAsset.create(managed_asset_attributes.except(:links_array))
        managed_asset1 = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({name:"Desktop"}))

        get :index, format: :json

        expect(response).to have_http_status(:ok)
        response_assets = JSON.parse(response.body)['assets_info']['assets']
        expect(response_assets.length).to eq(2)
        expected = response_assets.select {|a| a["id"] == managed_asset.id}
        expected1 = response_assets.select {|a| a["id"] == managed_asset1.id}
        expect(expected).to be_present
        expect(expected1).to be_present
      end

      it "grabs only unarchived assets if archived params equal false" do
        managed_asset = ManagedAsset.create(managed_asset_attributes.except(:links_array))
        managed_asset1 = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({name:"Desktop"}))
        managed_asset2 = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({name:"Printer", archived: true}))

        get :index, params: { archived: "false", format: :json }

        expect(response).to have_http_status(:ok)
        response_assets = JSON.parse(response.body)['assets_info']['assets']
        expect(response_assets.length).to eq(2)
        expected = response_assets.select {|a| a["id"] == managed_asset.id}
        expected1 = response_assets.select {|a| a["id"] == managed_asset1.id}
        expect(expected).to be_present
        expect(expected1).to be_present
      end

      it "grabs only archived assets if archived params equal true" do
        managed_asset = ManagedAsset.create(managed_asset_attributes.except(:links_array))
        managed_asset1 = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({name:"Desktop"}))
        managed_asset2 = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({name:"Printer", archived: true}))

        get :index, params: { archived: "true", format: :json }

        expect(response).to have_http_status(:ok)
        response_assets = JSON.parse(response.body)['assets_info']['assets']
        expect(response_assets.length).to eq(1)
        expect(response_assets.first["id"]).to eq(managed_asset2.id)
      end

      it "grabs all assets if archived params equal all" do
        managed_asset = ManagedAsset.create(managed_asset_attributes.except(:links_array))
        managed_asset1 = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({name:"Desktop"}))
        managed_asset2 = ManagedAsset.create(managed_asset_attributes.except(:links_array).merge({name:"Printer", archived: true}))

        get :index, params: { archived: "all", format: :json }

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['assets_info']['assets'].length).to eq(3)
      end
    end
  end

  describe "#archive" do
    context "with format json" do
      it "archives an asset" do
        managed_asset = ManagedAsset.create(managed_asset_attributes.except(:links_array))

        post :archive, params: { id: managed_asset.id, format: :json }

        expect(response).to have_http_status(:ok)
        managed_asset = ManagedAsset.find_by_id(managed_asset.id)
        expect(managed_asset.archived).to eq(true)
        audits = managed_asset.audits
        expect(audits.length).to eq(1)
        expect(audits.first.action).to eq('update')
        expect(audits.first.auditable_id).to eq(managed_asset.id)
        expect(audits.first.audited_changes.length).to eq(1)
        expect(audits.first.audited_changes).to eq("archived"=>[false, true])
      end
    end
  end

  describe "#unarchive" do
    it "unarchives an asset" do
      managed_asset_attributes[:archived] = true
      managed_asset = ManagedAsset.create(managed_asset_attributes.except(:links_array))

      post :unarchive, params: { id: managed_asset.id, format: :json }

      expect(response).to have_http_status(:ok)
      managed_asset = ManagedAsset.find_by_id(managed_asset.id)
      expect(managed_asset.archived).to eq(false)
      audits = managed_asset.audits
      expect(audits.length).to eq(1)
      expect(audits.first.action).to eq('update')
      expect(audits.first.auditable_id).to eq(managed_asset.id)
      expect(audits.first.audited_changes.length).to eq(1)
      expect(audits.first.audited_changes).to eq("archived"=>[true, false])
    end
  end

  describe 'meraki devices usage history and latency history' do
    let(:config) do
      {
        name: "Stratum-Genuity",
        token: "0db14cc1596716b65d20171da4ac045c5ca675ee",
        company_id: company.id,
        sync_networks: "L_695243192475322705,N_695243192475336835,N_695243192475350845",
        sync_by: "allNetworks"
      }
    end

    let(:device_details) do
      {
        name: "GE-AP1",
        company_id: company.id,
        company_asset_type_id: asset_type.id,
        location_id: location.id,
        asset_type: CompanyAssetType.create(name: 'WAP'),
        os_version: "10.0.19043",
        hardware_detail_attributes: {
          public_ip: "*************",
          dns: "[\"*******\", \"*******\"]",
          gateway: "[\"************\"]",
          wan_ips: nil,
          ssids: "[\"Genuity\", \"Genuity Guest\"]"
        },
        hardware_detail_type: "NetworkingDeviceDetail"
      }
    end

    before do
     Integrations::Meraki::Config.create(config)
    end

    it 'get device usage history' do
      managed_asset = ManagedAsset.create(device_details)
      res = OpenStruct.new({
        filtered_response: {
          :graph_labels => ['24 Sep', '25 Sep', '26 Sep', '27 Sep', '28 Sep', '29 Sep', '30 Sep'],
          :graph_data => [1.476, 0.005, 0.001, 1.483, 1.492, 1.222, 1.131]
        },
        status: :ok
      })

      allow_any_instance_of(Integrations::Meraki::FetchData).to receive(:ap_device_usage_history).and_return(res)
      get :usage_history, params: { id: managed_asset.id, format: :json }
      expect(response).to have_http_status(:ok)
    end

    it 'get device usage history error' do
      managed_asset = ManagedAsset.create(device_details)
      res = OpenStruct.new({
        parsed_response: {
          'errors' => [
            "No device with serial 'Q2KD-56T2-NYPS'"
          ]
        },
        status: :bad_request
      })

      allow_any_instance_of(Integrations::Meraki::FetchData).to receive(:ap_device_usage_history).and_return(res)
      get :usage_history, params: { id: managed_asset.id, format: :json }
      expect(response).to have_http_status(:bad_request)
    end

    it 'get device latency history' do
      managed_asset = ManagedAsset.create(device_details)
      res = OpenStruct.new({
        filtered_response: {
          :latency_data => [0.2062083731660491, 99.13961333954855, 0.6541782872853972],
          :latency_labels => ['latency more than 64 ms', 'latency less than 64 ms', 'latency equals to 64 ms'],
        },
        status: :ok
      })

      allow_any_instance_of(Integrations::Meraki::FetchData).to receive(:device_latency_history).and_return(res)
      get :latency_history, params: { id: managed_asset.id, format: :json }
      expect(response).to have_http_status(:ok)
    end

    it 'get device usage history error' do
      managed_asset = ManagedAsset.create(device_details)
      res = OpenStruct.new({
        parsed_response: {
          'errors' => [
            "No device with serial 'Q2KD-56T2-NYPS'"
          ]
        },
        status: :bad_request,
        success?: false,
      })

      allow_any_instance_of(Integrations::Meraki::FetchData).to receive(:device_latency_history).and_return(res)
      get :latency_history, params: { id: managed_asset.id, format: :json }
      expect(response).to have_http_status(:bad_request)
    end
  end

  describe '#new' do
    context 'user without module permissions' do
      before do
        login_user(user_one.user)
        get :new, format: :json
      end

      let(:user_one) { create(:company_user, company: company) }

      it 'will not allow user to access the page' do
        expect(response.status).to eq(401)
        expect(JSON.parse(response.body)['message']).to eq("Sorry, you're not authorized to perform this action.")
      end
    end

    context 'user with module permissions' do
      before do
        login_user(company_user.user)
        get :new, format: :html
      end

      it 'will allow user to access the page' do
        expect(response.status).to eq(200)
      end
    end
  end
end
