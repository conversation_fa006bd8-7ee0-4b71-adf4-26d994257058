require 'rails_helper'
include CompanyUserHelper

describe InvoiceDownloadsController, type: :controller do
  create_company_and_user

  let(:subscription_plan) { SubscriptionPlan.create(name: "Basic Plan", price: 2999, interval: "month", status: "active") }
  let(:subscription) { Subscription.create(company_id: company.id, subscription_plan_id: subscription_plan.id) }
  let(:transaction) { StripeTransaction.create(subscription_id: subscription.id, amount: 29.99, description: "Invoice 3423-234234", company_id: company.id ) }
  let(:charge_results) { OpenStruct.new(invoice: "in_12345") }
  let(:invoice_results) { OpenStruct.new({ "lines" => { "data" => [{ "period" => { "start" => 1582577972, "end" => 1585083572 }}] }}) }

  describe "#index" do
    before(:each) do
      #Using `stub` from rspec-mocks' old `:should` syntax without explicitly enabling the syntax is deprecated.
      #Use the new `:expect` syntax or explicitly enable `:should` instead.
      allow(Stripe::Charge).to receive(:retrieve).and_return(charge_results)
      allow(Stripe::Invoice).to receive(:retrieve).and_return(invoice_results)
      get :index, params: { transaction_id: transaction.id, format: :pdf }
    end

    it "will return a 200 response" do
      expect(response).to have_http_status(:ok)
    end
  end
end
