require 'rails_helper'
include Company<PERSON>serHelper

describe UserActivitiesController, type: :controller do
  create_company_and_user

  let(:company_user) { company.company_users.first }
  let(:scoped_company_user) { create(:company_user, company: company) }
  let(:contributor) { create(:contributor, company: company, company_user: scoped_company_user) }
  let(:privilege) { create(:expanded_privilege, contributor: contributor, company: company, name: "CompanyUser", permission_type: "read") }

  let!(:user_activity) {
    CompanyUserActivity.create(
      company_user: scoped_company_user,
      owner_id: scoped_company_user.id,
      activity_type: "updated",
      activity_action: nil,
      data: {
        "current_value"=>"1111",
        "activity_label"=>"Extension",
        "previous_value"=>nil
      }
    )
  }
  let!(:user_activity2) {
    CompanyUserActivity.create(
      company_user: scoped_company_user,
      owner_id: scoped_company_user.id,
      activity_type: "updated",
      activity_action: nil,
      data: {
        "current_value"=>"1111",
        "activity_label"=>"Extension",
        "previous_value"=>nil
      }
    )
  }

  describe "#index" do
    it "returns activities" do
      get :index
      expect(response).to have_http_status(:ok)
    end

    it "returns company activities" do
      request.env["HTTP_REFERER"] = "http://sample.localhost:3000/company/user_activities"
      get :index, params: {"search_terms"=>"", "per_page"=>"30", "page"=>"1", "controller"=>"help_ticket_activities", "action"=>"index", "format"=>"json"}

      activities = JSON.parse(response.body)
      expect(activities.length).to eq(2)
    end

    it 'filters the activities by user name' do
      request.env["HTTP_REFERER"] = "http://sample.localhost:3000/company/user_activities"
      get :index, params: { search_terms: scoped_company_user.name }

      expect(JSON.parse(response.body)["activities"][0]["user_name"]).to eq(scoped_company_user.name)
    end
  end
end
