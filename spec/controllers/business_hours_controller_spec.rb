require 'rails_helper'
include CompanyUserHelper

describe BusinessHoursController, type: :controller do
  create_company_and_user

  let(:default_schedule) {
    [
      {'day' => 'Monday', 'start_time' => '09:00', 'end_time' => '17:00', 'active' => true},
      {'day' => 'Tuesday', 'start_time' => '09:00', 'end_time' => '17:00', 'active' => true},
      {'day' => 'Wednesday', 'start_time' => '09:00', 'end_time' => '17:00', 'active' => true},
      {'day' => 'Thursday', 'start_time' => '09:00', 'end_time' => '17:00', 'active' => true},
      {'day' => 'Friday', 'start_time' => '09:00', 'end_time' => '17:00', 'active' => true},
      {'day' => 'Saturday', 'start_time' => '09:00', 'end_time' => '17:00', 'active' => false},
      {'day' => 'Sunday', 'start_time' => '09:00', 'end_time' => '17:00', 'active' => false}
    ]
  }

  let(:new_schedule) {
    [
      {'day' => 'Monday', 'start_time' => '09:00', 'end_time' => '17:00', 'active' => 'true'},
      {'day' => 'Tuesday', 'start_time' => '09:30', 'end_time' => '17:00', 'active' => 'true'},
      {'day' => 'Wednesday', 'start_time' => '09:00', 'end_time' => '17:00', 'active' => 'true'},
      {'day' => 'Thursday', 'start_time' => '09:00', 'end_time' => '15:00', 'active' => 'false'},
      {'day' => 'Friday', 'start_time' => '09:00', 'end_time' => '17:00', 'active' => 'false'},
      {'day' => 'Saturday', 'start_time' => '09:00', 'end_time' => '17:00', 'active' => 'false'},
      {'day' => 'Sunday', 'start_time' => '09:00', 'end_time' => '17:00', 'active' => 'false'}
    ]
  }

  describe "#index" do
    it "will get the default business hours settings" do
      get :index, params: { format: :json }
      parsed = JSON.parse(response.body)
      expect(parsed["business_hour"]).to be_present
      expect(parsed["business_hour"]["schedule"]).to eq(default_schedule)
      expect(parsed["business_hour"]["timezone"]).to eq(company.timezone)

    end
  end

  describe "#update_business_hours" do
    it "will update business hours settings" do
      post :update_business_hours, params: { schedule: new_schedule, timezone: 'US/Central' }
      expect(response).to have_http_status(:ok)
      company.business_hours.reload
      expect(company.business_hours.find_by(workspace_id: nil)['schedule']).to eq(new_schedule)
    end

    it "will fail validation due to absence of timezone" do
      post :update_business_hours, params: { schedule: new_schedule, }
      parsed = JSON.parse(response.body)
      expect(parsed["message"]).to eq("Timezone can't be blank")
    end
  end
end
