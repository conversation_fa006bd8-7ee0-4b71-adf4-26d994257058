require 'rails_helper'
include Company<PERSON>ser<PERSON>elper

describe StoreAccessesController, type: :controller do
  create_company_and_user

  describe "#index" do
    context "User without cognito refresh_id and access_id set" do
      it "will sign-out and redirect to new session path" do
        get :index, params: { redirect_to: "store"}

        expect(response).to have_http_status(:found)
        expect(response.redirect_url).to eq(destroy_user_session_url)
      end
    end

    context "User with cognito refresh_id and access_id set" do
      it "will redirect to store url" do
        allow_any_instance_of(CognitoService).to receive(:refresh_token).and_return(true)
        user.update!(refresh_id: SecureRandom.uuid, access_id: SecureRandom.uuid)
        get :index, params: { redirect_to: "store"}

        url = "#{Rails.application.credentials[:store_url]}/cognito_authentication?id=#{user.access_id}&guid=#{user.company_users.last.guid}&su_guid="
        expect(response).to have_http_status(:found)
        expect(response.redirect_url).to eq(url)
      end
    end
  end

  describe "#show" do
    context "request with company_user's guid" do
      it "will return user's information as json" do
        company_user = user.company_users.last
        get :show, params: { format: :json, id: company_user.guid }

        response_body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(response_body['company']['subdomain']).to eq(company.subdomain)
        expect(response_body['user_id']).to eq(company_user.user_id)
      end
    end

    context "request without company_user's guid" do
      it "will redirect to store url" do
        allow_any_instance_of(CognitoService).to receive(:refresh_token).and_return(true)
        user.update!(refresh_id: SecureRandom.uuid, access_id: SecureRandom.uuid)
        get :show, params: { format: :json, id: "" }

        response_body = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(response_body['error']).to eq("Access not allowed.  Please visit #{Rails.application.credentials.root_domain} to log in.")
      end
    end
  end
end