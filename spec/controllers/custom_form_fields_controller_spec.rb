require 'rails_helper'
include CompanyUserHelper

RSpec.describe CustomForm<PERSON>ieldsController, type: :controller do
  create_company_and_user
  let!(:custom_form) { company.custom_forms.helpdesk.first }

  let!(:status) { create(:custom_form_field,
                         custom_form: custom_form,
                         field_attribute_type: CustomFormField.field_attribute_types[:status],
                         options: CustomFormFieldTemplate::DEFAULT_STATUS_OPTIONS,
                         label: 'Status',
                         name: 'status_1') }

  let!(:priority) { create(:custom_form_field,
                           custom_form: custom_form,
                           field_attribute_type: CustomFormField.field_attribute_types[:priority],
                           options: CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS,
                           label: 'Priority',
                           name: 'priority_1') }

  let!(:checkbox) { create(:custom_form_field,
                           custom_form: custom_form,
                           field_attribute_type: CustomFormField.field_attribute_types[:checkbox],
                           options: CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS,
                           label: 'Checkbox',
                           name: 'checkbox') }
                           
  describe '#show' do
    it "shows custom form status field with valid data" do
      get :show, params: {"options"=>[{"name":"Open","color":"#2ECC71"}, {"name":"Closed","color":"#626567"}, {"name":"Custom","color":"#876bc7"}, {"name":"In Progress","color":"#5DADE2"}], "controller"=>"custom_form_fields", "action"=>"show", "id"=>status.id, "format"=>"json"}
      expect(response.status).to eq(200)
    end

    it "show error for status field with invalid data" do
      get :show, params: {"options"=>[{"name":"Open","color":"#2ECC71"}, {"name":"Done","color":"#454545"}, {"name":"Unread","color":"#876bc7"}, {"name":"In Progress","color":"#5DADE2"}], "controller"=>"custom_form_fields", "action"=>"show", "id"=>status.id, "format"=>"json"}
      expect(response.status).to eq(422)
      expect( JSON.parse(response.body)["errors"].count).to eq(1)
    end

    it "shows custom form priority field with valid data" do
      get :show, params: {"options"=>[{"name":"low","color":"#0d6efd"}, {"name":"medium","color":"#fd7e14"}, {"name":"high","color":"#e14144"}, {"name":"custom","color":"#876bc7"}], "controller"=>"custom_form_fields", "action"=>"show", "id"=>priority.id, "format"=>"json"}
      expect(response.status).to eq(200)
    end

    it "show error for priority field with invalid data" do
      get :show, params:  {"options"=>[{"name":"low","color":"#ffd700"}, {"name":"medium","color":"#ffa500"}, {"name":"high","color":"#ff0000"}, {"name":"low","color":"#876bc7"}], "controller"=>"custom_form_fields", "action"=>"show", "id"=>priority.id, "format"=>"json"}
      expect(response.status).to eq(422)
      expect( JSON.parse(response.body)["errors"].count).to eq(1)
    end

    it "shows error for custom form status field with removing default value" do
      get :show, params: {"options"=>[{"name":"Open","color":"#2ECC71"}, {"name":"Done","color":"#454545"}, {"name":"Custom","color":"#876bc7"}, {"name":"In Progress","color":"#5DADE2"}], "controller"=>"custom_form_fields", "action"=>"show", "id"=>status.id, "format"=>"json"}
      expect( JSON.parse(response.body)["errors"]).to eq(["Sorry, you cannot remove the default options"])
      expect(response.status).to eq(422)
    end

    it "shows custom form checkbox field with mixed JSON and non-JSON options" do
      get :show, params: {"options"=>["1", "Option 2"], "controller"=>"custom_form_fields", "action"=>"show", "id"=>checkbox.id, "format"=>"json"}
      expect(response.status).to eq(200)
    end
    
  end
end
