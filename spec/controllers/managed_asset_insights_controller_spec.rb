require 'rails_helper'
include CompanyUserHelper

describe ManagedAssetInsightsController, type: :controller do
  create_company_and_user

  let!(:mobile_asset) { create(:managed_asset, :laptop, company_id: company.id, manufacturer: "Dell") }
  let!(:laptop_asset) { create(:managed_asset, :router, company_id: company.id, manufacturer: "Lenovo") }

  context "#index" do
    before do
      asset_status = CompanyAssetStatus.create(name: 'Disposed', icon: 'genuicon-exclamation', company: company)
      ManagedAsset.destroy_all
      create(:managed_asset, :router, company_id: company.id, company_asset_status_id: asset_status.id)
      create(:managed_asset, :laptop, company_id: company.id)
      create(:managed_asset, :mobile_with_multiple_software, company_id: company.id)
      create(:managed_asset, :mobile, company_id: company.id, archived: true, location_id: company.locations.first.id)
    end

    it "return insights for assets by chart data" do
      get :index, params: { summary_filter: "operatingSystem", asset_by_change: 'true' },  format: :json
      expect(response).to have_http_status(:ok)
      json_response = JSON.parse(response.body)
      expect(json_response["assets_by_chart_data"]["data"].count).to eq(3)
      expect(json_response["assets_by_chart_data"]["labels"].count).to eq(3)
      expect(json_response["assets_by_chart_data"]["softwares_chart_count"]).to eq(3)
    end

    it "return insights for asset counts along with their asset types" do
      get :index, params: { asset_type_counts: 'true' },  format: :json
      expect(response).to have_http_status(:ok)
      json_response = JSON.parse(response.body)
      expect(json_response["assets_count"]["data"]).to eq([1, 1, 1])
      expect(json_response["assets_count"]["labels"]).to eq(["Laptop", "Mobile", "Router"])
    end

    it "return insights for chart data" do
      get :index, params: { summary_filter: "applications", asset_by_change: "true" },  format: :json
      expect(response).to have_http_status(:ok)
      json_response = JSON.parse(response.body)
      expect(json_response["assets_by_chart_data"]["data"]).to eq([1, 1])
      expect(json_response["assets_by_chart_data"]["labels"]).to match_array(["Google Chrome Installer", "Slack"])
      expect(json_response["assets_by_chart_data"]["softwares_chart_count"]).to eq(2)
    end

    it "return insights of assets by adding filters" do
      20.times do |i|
        create(:managed_asset, :laptop, company_id: company.id)
      end

      get :index, params: {
        filter_type: "operating_system",
        page_index: "0",
        page_size: "15",
        filter_name: "" },  format: :json

      expect(response).to have_http_status(:ok)
      json_response = JSON.parse(response.body)
      expect(json_response["list_data"]["assets_by_filter"].count).to eq(15)
      expect(json_response["list_data"]["filter_assets_count"]).to eq(23)
      expect(json_response["list_data"]["page_index"]).to eq(0)
      expect(json_response["list_data"]["page_count"]).to eq(2)
    end
  end

  context "search_filter_options_for_manufacturer_dropdown" do
    it "returns searched options from filter dropdown" do
      get :search_field_filter_options, params: {
        search_query: "dell",
        limit: "30",
        offset: "0",
        filter_type: "manufacturer"}, format: :json

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)
      expect(result["options"].count).to eq(1)
      expect(result["options"][0]["name"] == mobile_asset.manufacturer).to be_truthy
    end
  end
end
