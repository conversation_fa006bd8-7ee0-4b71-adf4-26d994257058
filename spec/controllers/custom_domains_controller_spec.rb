require 'rails_helper'
include CompanyUserHelper

RSpec.describe CustomDomainsController, type: :controller do
  create_company_and_user

  let(:domain_params) {
    {
      name: 'testing.custom_domain.com',
      verified: true,
      active_domain: true,
      certificate_type: 'custom_certificate',
      primary_key: 'mock_private_key',
      primary_certificate: 'mock_primary_certificate',
      intermediary_certificate: 'mock_intermediary_certificate',
      company_id: company.id
    }
  }

  describe "POST #create" do
    it "creates a custom domain" do
      post :create, params: { custom_domain: domain_params }
      
      expect(response).to have_http_status(:ok)
      custom_domain = CustomDomain.find_by(name: "testing.custom_domain.com")
      expect(custom_domain).to be_present
    end
  end

  describe "PUT #update" do
    let!(:domain1) { CustomDomain.create(domain_params) }
    let!(:update_params) {
      {
        id: domain1.id,
        name: 'custom_domain.net',
        verified: false,
        active_domain: false,
        certificate_type: 'custom_certificate',
        primary_key: 'mock_private_key',
        primary_certificate: 'mock_primary_certificate',
        intermediary_certificate: 'mock_intermediary_certificate',
        company_id: company.id
      }
    }

    it "update the custom domain" do
      put :update, params: { id: domain1.id, custom_domain: update_params }

      updated_domain = JSON.parse(response.body)['domain']
      expect(updated_domain['name']).to eq("custom_domain.net")
      expect(updated_domain['verified']).to eq(false)
      expect(updated_domain['active_domain']).to eq(false)
    end
  end
end
