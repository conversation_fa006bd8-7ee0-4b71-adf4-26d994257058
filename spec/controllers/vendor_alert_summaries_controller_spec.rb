require 'rails_helper'
include CompanyUserHelper

RSpec.describe VendorAlertSummariesController, type: :controller do
  create_company_and_user

  let!(:vendor) { create(:vendor, company: company) }
  let!(:vendor2) { create(:vendor, company: company, name: "Vendor2") }
  let!(:general_transaction1) { create(:general_transaction, transaction_date: Date.today, company_id: company.id, vendor_id: vendor.id, amount:  57.0, status: "recognized")}
  let!(:general_transaction2) { create(:general_transaction, transaction_date: Date.today - 1.month, company_id: company.id, vendor_id: vendor.id, amount: 50.0, status: "recognized")}
  let!(:module_alert) { create(:module_alert, company: company, monitorable_id: vendor.id, type: "VendorSpendAlert", monitorable_type: "Vendor") }
  let!(:module_alert2) { create(:module_alert, company: company, monitorable_id: vendor.id, type: "VendorSpendAlert", monitorable_type: "Vendor", limit: 25) }

  describe "GET #index" do
    it "returns http success vendor alerts" do
      get :index

      expect(response).to have_http_status(:ok)
      res = JSON.parse(response.body)["vendors"]["results"]
      expect(res.length).to eq(1)
      expect(res[0]["id"]).to eq(vendor.id)
      expect(res[0]["name"]).to eq(vendor.name)
      expect(res[0]["last_month_spending"].to_f).to eq(50.0)
      expect(res[0]["this_month_spending"].to_f).to eq(57.0)
      expect(res[0]["configured_alerts"].length).to eq(2)
      expect(res[0]["notification_count"]).to eq(0)
    end
  end
end
