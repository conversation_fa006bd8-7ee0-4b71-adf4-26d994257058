require 'rails_helper'
require 'support/aws_stub'

include CompanyUserHelper

RSpec.describe LibraryDocumentsController, type: :controller do
  create_company_and_user

  before do
    Aws.config[:stub_responses] = true
  end

  describe "#index" do
    context "with a name" do
      let!(:library_document) { create(:library_document, name: "Test File", company: company, workspace: workspace) }
      subject { get :index, format: :json }

      it "will have 200 status" do
        subject
        expect(response).to have_http_status(:ok)
      end

      it "will return document count" do
        subject
        data = JSON.parse(response.body)
        expect(data['documentCount']).to eq(1)
      end

      it "will return library documents" do
        subject
        data = JSON.parse(response.body)
        expect(data['documents'][0]['id']).to eq(LibraryDocument.first.id)
        expect(data['documents'][0]['name']).to eq(LibraryDocument.first.name)
        expect(data['documents'][0]['attached_file_file_name']).to eq(LibraryDocument.first.attached_file_file_name)
      end
    end

    context "without a name" do
      let!(:library_document) { create(:library_document, company: company, workspace: workspace)}
      subject { get :index, format: :json }

      it "will have 200 status" do
        subject
        expect(response).to have_http_status(:ok)
      end

      it "will return document count" do
        subject
        data = JSON.parse(response.body)
        expect(data['documentCount']).to eq(1)
      end

      it "will return library documents" do
        subject
        data = JSON.parse(response.body)
        expect(data['documents'][0]['id']).to eq(LibraryDocument.first.id)
        expect(data['documents'][0]['name']).to be_nil
        expect(data['documents'][0]['attached_file_file_name']).to eq(LibraryDocument.first.attached_file_file_name)
      end
    end
  end

  describe "#destroy" do
    let(:library_document) { create(:library_document, company: company, workspace: workspace) }
    subject { delete :destroy, :params => { id: library_document.id }, format: :json }

    it "will have successfully removed the document" do
      subject
      expect(response).to have_http_status(:ok)
      data = JSON.parse(response.body)
      expect(data['id']).to eq(library_document.id)
    end
  end

  describe "#create" do
    let(:library_document) { create(:library_document, company: company, workspace: workspace) }
    let(:attached_file) { Rack::Test::UploadedFile.new(Rails.root.join("Gemfile"), "mime/type") }
    subject { post :create, :params => { library_document: { attached_file: attached_file }}, format: :json }

    it "will have successfully created the document" do
      subject
      expect(response).to have_http_status(:ok)
      data = JSON.parse(response.body)
      expect(data['id']).to eq(LibraryDocument.last.id)
    end
  end

  describe "#update" do
    let(:library_document) { create(:library_document, company: company, workspace: workspace) }
    let(:attached_file) do
      file = URI.open("https://nulodgic-static-assets.s3.amazonaws.com/images/logo.png")
      Rack::Test::UploadedFile.new(file, "image/png", original_filename: "file.png")
    end
    subject { put :update, :params => { id: library_document.id, library_document: { name: 'My Logo', attached_file: attached_file }}, format: :json }

    it "will have successfully created the document" do
      subject
      expect(response).to have_http_status(:ok)
      data = JSON.parse(response.body)
      library_document = LibraryDocument.find_by(id: data['id'])
      expect(library_document).to be_present
      expect(data['name']).to eq(library_document.name)
      expect(data['attached_file_file_name']).to eq(library_document.attached_file_file_name)
    end
  end
end
