require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

RSpec.describe CompanyDissociatorsController, type: :controller do
  create_company_and_user

  let!(:parent_company) { FactoryBot.create(:company, subdomain: 'parent-company', is_reseller_company: true) }
  let!(:first_user) {
    user = FactoryBot.create(:user, first_name: 'User', last_name: 'First', email: '<EMAIL>')
    user.skip_validations = true
    user.save!(validate: false)
    user
  }

  let!(:company_user1) { FactoryBot.create(:company_user, user: first_user, granted_access_at: 2.day.ago, company: parent_company) }
  let!(:company_user2) { FactoryBot.create(:company_user, user: first_user, granted_access_at: 1.day.ago, company: company) }

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user2.contributor_id,
        'Status' => 'In Progress',
        'Priority' => "low",
        'Description' => "What a great day!",
        workspace: company.default_workspace,
      },
      {
        'Subject' => "Boom boom",
        'Created By' => company_user2.contributor_id,
        'Assigned To' => [company_user2.contributor_id],
        'Status' => 'Open',
        'Priority' => "medium",
        'Description' => "The red robin sits in the tree.",
        workspace: company.default_workspace,
      }
    ]
  }

  let!(:managed_asset1) { FactoryBot.create(:managed_asset, company: company) }
  let!(:managed_asset2) { FactoryBot.create(:managed_asset, company: company) }

  let(:managed_asset_params) {
    [managed_asset1, managed_asset2]
  }

  let(:contract1) { FactoryBot.create(:contract, name: 'contract1', company: company) }
  let(:contract2) { FactoryBot.create(:contract, name: 'contract2', company: company) }

  let(:contract_params) {
    [contract1, contract2]
  }

  let!(:contract_contact1) { contract1.contract_contacts.create(company_user: company_user2) }
  let!(:contract_contact2) { contract2.contract_contacts.create(company_user: company_user2) }

  describe 'Company Dissociator' do
    let(:help_tickets) { help_ticket_params.map{ |p| create_ticket(p) } }

    it 'will dissociate the companies through child company' do
      company.update(reseller_company: parent_company)
      allow_any_instance_of(CompanyDissociatorsController).to receive(:send_slack_alert).and_return(true)
      params = { id: company.id, entity_reassignments: [] }
      put :update, params: params, as: :json
      company.reload
      expect(company.reseller_company_id).to eq(nil)
      expect(CompanyAssociationEventLog.find_by(child_company_id: company.id).performed_by).to eq("child company")
    end

    it 'will dissociate the companies through parent company' do
      company.update(reseller_company: parent_company)
      allow_any_instance_of(CompanyDissociatorsController).to receive(:send_slack_alert).and_return(true)
      params = { id: parent_company.id, entity_reassignments: [], child_company_subdomain: company.subdomain, is_parent_unlinking: true }
      put :update, params: params, as: :json
      company.reload
      expect(company.reseller_company_id).to eq(nil)
      expect(CompanyAssociationEventLog.find_by(parent_company_id: parent_company.id).performed_by).to eq("parent company")
    end

    it 'will reassign new user to related help tickets' do
      company.update(reseller_company: parent_company)
      company.groups.find_by(name: "Admins").group_members.create(contributor_id: company_user2.contributor_id)
      parent_company.groups.find_by(name: "Admins").group_members.create(contributor_id: company_user1.contributor_id)
      allow_any_instance_of(CompanyDissociatorsController).to receive(:send_slack_alert).and_return(true)
      params = {
        id: company.id,
        entity_reassignments: [
          {
            entity_type: 'help_tickets',
            params: {
              bulk_entities: help_tickets.map{ |ticket| {id: ticket.id } },
              created_by: company_user.contributor_id,
            },
          }
        ]
      }
      put :update, params: params, as: :json
      expect(response).to have_http_status(:ok)
      expect(help_tickets.first.creators[0].id).to eq(company_user.id)
      expect(help_tickets.last.creators[0].id).to eq(company_user.id)
    end

    it "will successfully update used by of the asset" do
      company.update(reseller_company: parent_company)
      company.groups.find_by(name: "Admins").group_members.create(contributor_id: company_user2.contributor_id)
      parent_company.groups.find_by(name: "Admins").group_members.create(contributor_id: company_user1.contributor_id)
      allow_any_instance_of(CompanyDissociatorsController).to receive(:send_slack_alert).and_return(true)
      params = {
        id: company.id,
        entity_reassignments: [
          {
            entity_type: 'managed_assets',
            params: {
              bulk_entities: managed_asset_params.map{ |asset| {id: asset.id} },
              used_by_contributor_id: company_user.contributor_id,
            },
          }
        ]
      }
      put :update, params: params, as: :json
      expect(response).to have_http_status(:ok)
      expect(managed_asset1.reload.assignment_information.used_by_contributor_id).to eq(company_user.contributor_id)
      expect(managed_asset2.reload.assignment_information.used_by_contributor_id).to eq(company_user.contributor_id)
    end

    it "will successfully update managed by of the asset" do
      company.update(reseller_company: parent_company)
      company.groups.find_by(name: "Admins").group_members.create(contributor_id: company_user2.contributor_id)
      parent_company.groups.find_by(name: "Admins").group_members.create(contributor_id: company_user1.contributor_id)
      allow_any_instance_of(CompanyDissociatorsController).to receive(:send_slack_alert).and_return(true)
      params = {
        id: company.id,
        entity_reassignments: [
          {
            entity_type: 'managed_assets',
            params: {
              bulk_entities: managed_asset_params.map{ |asset| {id: asset.id} },
              managed_by_contributor_id: company_user.contributor_id,
            },
          }
        ]
      }
      put :update, params: params, as: :json
      expect(response).to have_http_status(:ok)
      expect(managed_asset1.reload.assignment_information.managed_by_contributor_id).to eq(company_user.contributor_id)
      expect(managed_asset2.reload.assignment_information.managed_by_contributor_id).to eq(company_user.contributor_id)
    end

    it "will successfully update contract contact" do
      company.update(reseller_company: parent_company)
      company.groups.find_by(name: "Admins").group_members.create(contributor_id: company_user2.contributor_id)
      parent_company.groups.find_by(name: "Admins").group_members.create(contributor_id: company_user1.contributor_id)
      allow_any_instance_of(CompanyDissociatorsController).to receive(:send_slack_alert).and_return(true)
      params = {
        id: company.id,
        entity_reassignments: [
          {
            entity_type: 'contracts',
            params: {
              bulk_entities: contract_params.map{ |contract| {id: contract.id} },
              company_user_cont_id: company_user.contributor_id,
            },
          }
        ]
      }
      put :update, params: params, as: :json
      expect(response).to have_http_status(:ok)
      expect(contract1.contract_contacts.first.company_user_id).to eq(company_user.id)
      expect(contract2.contract_contacts.first.company_user_id).to eq(company_user.id)
    end

    it "will send email to the parent admin users about unlinking" do
      company.update(reseller_company: parent_company)
      company.groups.find_by(name: "Admins").group_members.create(contributor_id: company_user2.contributor_id)
      parent_company.groups.find_by(name: "Admins").group_members.create(contributor_id: company_user1.contributor_id)
      allow_any_instance_of(CompanyDissociatorsController).to receive(:send_slack_alert).and_return(true)
      params = { id: company.id, entity_reassignments: [] }
      put :update, params: params, as: :json
      expect(response).to have_http_status(:ok)
    end
  end
end
