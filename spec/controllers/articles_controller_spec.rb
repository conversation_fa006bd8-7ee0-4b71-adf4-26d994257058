require 'rails_helper'
include CompanyUserHelper

RSpec.describe ArticlesController, type: :controller do
  create_company_and_user

  let(:workspace2) { FactoryBot.create(:workspace) }
  let!(:article) { FactoryBot.create(:article, company: company, workspace_id: workspace.id, company_user_id: company_user.id) }

  before do
    FactoryBot.create(:article, title: "Article Title Updated", slug: "article-title-updated", company: company, workspace_id: workspace2.id, company_user_id: company_user.id)
  end

  describe "GET index" do
    it "returns articles" do
      get :index

      articles = JSON.parse(response.body)['article']
      expect(article).to be_present
      expect(article['id']).to eq(article.id)
    end
  end

  describe "POST #create" do
    context "with valid params" do
      let(:admins_group) { company.groups.find_by(name: "Admins") }
      let(:everyone_group) { company.groups.find_by(name: "Everyone") }
      let(:article_attributes) {
        {
          title: "New Article Title",
          body: "New Article body",
          tags: ["New Article Tag 1", "New Article Tag 2"],
          category: "Advertising",
          slug: "new-article-title",
          review_date: Date.today + 1.week,
          public: true,
          article_privileges: [
            {
              "contributor"=>
              {
                "id"=> admins_group.contributor_id,
                "name"=>"Admins"
              },
              "permission"=>"write"
            },
            {
              "contributor"=>
              {
                "id"=> everyone_group.contributor_id,
                "name"=>"Everyone",
              },
              "permission"=>"read"
            }
          ]
        }
      }

      let(:article_attributes_empty_field) {
        {
          title: "New Article Title",
          tags: ["New Article Tag 1", "New Article Tag 2"],
          category: "Advertising",
          slug: "new-article-title",
          article_privileges: [
            {
              "contributor"=>
              {
                "id"=> admins_group.contributor_id,
                "name"=>"Admins"
              },
              "permission"=>"write"
            },
            {
              "contributor"=>
              {
                "id"=> everyone_group.contributor_id,
                "name"=>"Everyone",
              },
              "permission"=>"read"
            }
          ]
        }
      }

      before do
        headers = { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
      end

      it "create new article" do
        params = { article: article_attributes, format: :json }
        post :create, params: params

        article = Article.find_by(slug: "new-article-title", company: company, workspace_id: workspace.id, company_user_id: company_user.id)
        first_article_privilege = article.article_privileges.first
        second_article_privilege = article.article_privileges.second

        expect(article).to be_present
        expect(article.title).to eq("New Article Title")
        expect(article.body).to eq("New Article body")
        expect(article.tags).to eq(["New Article Tag 1", "New Article Tag 2"])
        expect(article.category).to eq("Advertising")
        expect(article.slug).to eq("new-article-title")
        expect(article.review_date).to eq(Date.today + 1.week)
        expect(article.public).to eq(true)
        expect(first_article_privilege.contributor_id).to eq( admins_group.contributor_id )
        expect(first_article_privilege.contributor.name).to eq( admins_group.contributor.name )
        expect(second_article_privilege.contributor_id).to eq( everyone_group.contributor_id )
        expect(second_article_privilege.contributor.name).to eq( everyone_group.contributor.name )
      end

      it "creates new article with missing body" do
        params = { article: article_attributes_empty_field, format: :json }
        expect(response.body).to eq("")
      end

      it "returns a valid http status" do
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe "GET #show" do
    context "with valid params" do
      before do
        headers = { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
      end

      it "returns article" do
        params = { slug: article.slug, id: article.id, format: :json }
        get :show, params: params
        expect(JSON.parse(response.body)['id']).to eq(article.id)
      end

      it "returns a valid http status" do
        expect(response).to have_http_status(:ok)
      end
    end

    context "with invalid params" do
      it "returns article not found error" do
        params = { slug: "wrong-slug", id: 1111, format: :json }
        response = get :show, params: params
        expect(JSON.parse(response.body)).to eq({"message"=>"Article not found"})
      end
    end
  end

  describe "PUT #update" do
    context "with valid params" do
      let(:admins_group) { company.groups.find_by(name: "Admins") }
      let(:everyone_group) { company.groups.find_by(name: "Everyone") }
      let(:article_attributes) {
        {
          title: "Article Title Updated",
          body: "Article body Updated",
          tags: ["Article Tag 1 Updated", "Article Tag 2 Updated"],
          category: "Advertising",
          slug: "article-title-updated",
          article_privileges: [
            {
              "contributor"=>
              {
                "id"=> admins_group.contributor_id,
                "name"=>"Admins"
              },
              "permission"=>"write"
            },
            {
              "contributor"=>
              {
                "id"=> everyone_group.contributor_id,
                "name"=>"Everyone",
              },
              "permission"=>"read"
            }
          ]
        }
      }

      let(:article_attributes_empty_field) {
        {
          title: "Article Title Updated",
          tags: ["Article Tag 1 Updated", "Article Tag 2 Updated"],
          category: "Advertising",
          slug: "article-title-updated",
          article_privileges: [
            {
              "contributor"=>
              {
                "id"=> admins_group.contributor_id,
                "name"=>"Admins"
              },
              "permission"=>"write"
            },
            {
              "contributor"=>
              {
                "id"=> everyone_group.contributor_id,
                "name"=>"Everyone",
              },
              "permission"=>"read"
            }
          ]
        }
      }

      before do
        headers = { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
      end

      it "update article" do
        params = { slug: article.slug, id: article.id, article: article_attributes, format: :json }
        put :update, params: params

        article1 = Article.find_by(slug: "article-title-updated", company: company, workspace_id: workspace.id, company_user_id: company_user.id)
        first_article1_privilege = article.article_privileges.first
        second_article1_privilege = article.article_privileges.second

        expect(article1).to be_present
        expect(article1.title).to eq("Article Title Updated")
        expect(article1.body).to eq("Article body Updated")
        expect(article1.tags).to eq(["Article Tag 1 Updated", "Article Tag 2 Updated"])
        expect(article1.category).to eq("Advertising")
        expect(article1.slug).to eq("article-title-updated")
        expect(first_article1_privilege.contributor_id).to eq( admins_group.contributor_id )
        expect(first_article1_privilege.contributor.name).to eq( admins_group.contributor.name )
        expect(second_article1_privilege.contributor_id).to eq( everyone_group.contributor_id )
        expect(second_article1_privilege.contributor.name).to eq( everyone_group.contributor.name )
      end

      it "updates article with empty body" do
        params = { article: article_attributes_empty_field, format: :json }
        expect(response.body).to be_blank
      end

      it "returns a valid http status" do
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe "DELETE #destroy" do
    context "with valid params" do
      let!(:article) { FactoryBot.create(:article, company: company, workspace_id: workspace.id, company_user_id: company_user.id) }

      before do
        headers = { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
      end

      it "destroys the article" do
        params = { slug: article.slug, id: article.id, format: :json }
        delete :destroy, params: params
        expect(JSON.parse(response.body)).to be_blank
      end

      it "cannot destroy the article" do
        params = { slug: "Wrong slug", id: 1111, format: :json }
        response = delete :destroy, params: params
        expect(JSON.parse(response.body)).to eq({"errors"=>"Article not present"})
      end

      it "returns a valid http status" do
        expect(response).to have_http_status(:ok)
      end
    end
  end
end
