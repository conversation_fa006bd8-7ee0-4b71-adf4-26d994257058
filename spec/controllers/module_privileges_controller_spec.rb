require 'rails_helper'
include Company<PERSON>ser<PERSON>elper

describe ModulePrivilegesController, type: :controller do
  let!(:domain_validator) {
    allow_any_instance_of(DomainValidator).to receive(:validate).and_return(true)
  }
  create_company_and_user

  describe "get #index" do
    let(:user_2_attributes) do
      {
        email: "<EMAIL>",
        password: "password2",
        first_name: "<PERSON>",
        last_name: "<PERSON>",
        terms_of_services: true
      }
    end

    let(:user_3_attributes) do
      {
        email: "<EMAIL>",
        password: "password2",
        first_name: "<PERSON>",
        last_name: "<PERSON>",
        terms_of_services: true
      }
    end

    let!(:user2) { create(:user, user_2_attributes) }
    let!(:user3) { 
      user = create(:user, user_3_attributes) 
      user.company_users << CompanyUser.new(company: company, granted_access_at: 1.day.ago)
      user
    }

    context "with an different company user" do
      before { login_user(user2) }

      it "should return module specific permissions" do
        Company.current_id = company.id

        get :index, params: { mod: "Contract"}

        expect(response).to  have_http_status(:found)
      end
    end

    context "with an unauthorized user" do
      before { login_user(user3) }

      it "should return module specific permissions" do
        Company.current_id = company.id

        get :index, params: { mod: "Contract"}

        expect(response).to  have_http_status(:ok)
        expect(JSON.parse(response.body)['permissions']).to be_blank
      end
    end

    context "with an authorized user" do
      before { login_user }

      it "should return module specific permissions for authorized" do
        Company.current_id = company.id

        get :index, params: { mod: "Contract"}

        expect(response).to  have_http_status(:ok)
        expect(JSON.parse(response.body)['permissions']['root']).to eq(["write", ["Admins"]])
      end
    end
  end
end
