require 'rails_helper'
include CompanyUserHelper

describe Sla::PoliciesController , type: :controller do
  create_company_and_user

  let(:custom_form_1) { FactoryBot.create(:custom_form, company: company, company_module: "helpdesk") }
  let(:custom_form_2) { FactoryBot.create(:custom_form, company: company, company_module: "helpdesk", form_name: 'Custom Form 2') }
  let(:custom_form_3) { FactoryBot.create(:custom_form, company: company, company_module: "helpdesk", form_name: 'Custom Form 3') }
  let!(:sla_policy) { FactoryBot.create(:sla_policy, company: company, custom_form: custom_form_1, name: 'SLA Policy1') }
  let!(:sla_policy1) { FactoryBot.create(:sla_policy, company: company, custom_form: custom_form_2, name: 'SLA Policy2') }
  let!(:sla_detail) { FactoryBot.create(:sla_detail, sla_policy_id: sla_policy.id) }
  let!(:sla_condition) { FactoryBot.create(:sla_condition, sla_policy_id: sla_policy.id) }
  let(:valid_params) do
    {
      policy: {
        name: "Create SLA Policy",
        description: "New Description",
        custom_form: {
          id: custom_form_3.id
        }
      },
      details: [
        {
          "priority"=> {"id"=>"low", "name"=>"Low", "color"=>"#ffd700", "label"=>"Priority"},
          "first_response_time"=>"0-0-15",
          "resolution_time"=>"0-0-15",
          "operational_hours"=> "Business Hours",
          "escalation"=>true,
        },
        {
          "priority"=> {"id"=>"medium", "name"=>"Medium", "color"=>"#ffa500", "label"=>"Priority"},
          "first_response_time"=>"0-0-15",
          "resolution_time"=>"0-0-15",
          "operational_hours"=> "Business Hours",
          "escalation"=>false,
        },
        {
          "priority"=> {"id"=>"high", "name"=>"High", "color"=>"#ff0000", "label"=>"Priority"},
          "first_response_time"=>"0-0-15",
          "resolution_time"=>"0-0-15",
          "operational_hours"=> "Business Hours",
          "escalation"=>false,
        }
      ],
      conditions: new_condition_attribute
    }
  end

  let(:invalid_params) do
    {
      policy: {
        description: "New Description",
        custom_form: {
          id: custom_form_3.id
        }
      },
      details: [],
      conditions: []
    }
  end

  let(:invalid_params1) do
    {
      policy: {
        name: "Create SLA Policy",
        description: "New Description",
        custom_form: {
          id: custom_form_3.id
        }
      },
      details: [],
      conditions: []
    }
  end

  let(:detail_attribute) do
    [
      {
        "id"=>sla_policy.details.find_by(priority: 'high').id,
        "priority"=> {"id"=>"high", "name"=>"High", "color"=>"#ff0000", "label"=>"Priority"},
        "first_response_time"=>"0-0-15",
        "resolution_time"=>"0-0-15",
        "operational_hours"=> "Business Hours",
        "escalation"=>false,
      }
    ]
  end

  let(:new_detail_attribute) do
    [
      {
        "priority"=> {"id"=>"new", "name"=>"New", "color"=>"#000000", "label"=>"Priority"},
        "first_response_time"=>"0-0-15",
        "resolution_time"=>"0-0-15",
        "operational_hours"=> "Business Hours",
        "escalation"=>false,
      }
    ]
  end

  let(:new_condition_attribute) do
    [
      {
        "condition_type"=> "form_field",
        "field_type"=>"tag",
        "value"=>{"form_value"=>["Business Service"]}
      }
    ]
  end

  let(:invalid_detail_attribute) do
    [
      {
        "priority"=> nil,
        "first_response_time"=>"0-0-15",
        "resolution_time"=>"0-0-15",
        "operational_hours"=> "Business Hours",
        "escalation"=>false,
      }
    ]
  end

  let(:invalid_condition_attribute) do
    [
      {
        "condition_type"=> nil,
        "field_type"=>"text",
        "value"=>{"form_value"=> "newone"}
      }
    ]
  end

  describe  "#index" do
    context "when policies for the company exist" do
      it "will fetch all the policies" do
        get :index, format: :json
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        sla_policies = json_response['sla_policies']
        base_policies = json_response['base_policies']
        expect(sla_policies[custom_form_1.id.to_s].count).to eq(1)
        expect(base_policies[custom_form_2.id.to_s].count).to eq(1)
        expect(sla_policies[custom_form_1.id.to_s][0]['name']).to eq('SLA Policy1')
      end
    end
  end

  describe  "#create" do
    context "when new policy valid params are present" do
      it "will create a new policy" do
        post :create, params: { params: valid_params }
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['name']).to eq('Create SLA Policy')
        expect(json_response['custom_form_id']).to eq(custom_form_3.id)
      end
    end

    context "when new policy with invalid params are present" do
      it "will send a validation error as name is missing" do
        post :create, params: { params: invalid_params }
        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response['message']).to eq("Validation failed: Name is required")
      end

      it "will send an error since details are missing" do
        post :create, params: { params: invalid_params1 }
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe  "#update" do
    context "when a existing policy is updated" do
      it "will throw a validation error due to duplicate name" do
        params = { policy: { name: 'SLA Policy2', custom_form: { id: custom_form_1.id } }, details: detail_attribute, conditions: new_condition_attribute }
        put :update, params: { id: sla_policy.id, params: params }
        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response['message']).to eq("Validation failed: Name already exists")
      end

      it "will update the name" do
        params = { policy: { name: 'New SLA Policy', custom_form: { id: custom_form_1.id } }, details: detail_attribute, conditions: new_condition_attribute }
        put :update, params: { id: sla_policy.id, params: params }
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['name']).to eq('New SLA Policy')
      end
    end
  end

  describe  "#sla_targets" do
    context "adding and deleting sla targets" do
      it "will not create sla targets if priority isn't provided" do
        params = { 
          policy: { 
                    name: 'Updated Policy', 
                    custom_form: { id: custom_form_1.id } 
                  }, 
          details: invalid_detail_attribute,
          conditions: new_condition_attribute
        }
        put :update, params: { id: sla_policy.id, params: params }
        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response['message']).to eq("Validation failed: Priority is required")
      end

      it "will add new sla target" do
        params =  { 
          policy: { 
                    name: 'Updated Policy', 
                    custom_form: { id: custom_form_1.id } 
                  }, 
          details: new_detail_attribute,
          conditions: new_condition_attribute
        }
        expect { put :update, params: { id: sla_policy.id, params: params } }.to change{ sla_policy.details.count }.by(1)
        expect(response).to have_http_status(:ok)
        sla_policy.reload
        new_detail = sla_policy.details.find_by(priority: new_detail_attribute[0]["priority"]["id"])
        expect(new_detail).to be_present
      end

      it "will remove sla target" do
        sla_policy.details.create!(priority: 'new',
                                   first_response_time: "0-0-15",
                                   resolution_time: "0-0-15",
                                   escalation: false,
                                   operational_hours: "business_hours"
                                   )

        params =  { 
          policy: { 
                    name: 'Updated Policy', 
                    custom_form: { id: custom_form_1.id } 
                  }, 
          details: detail_attribute, 
          removed_target_ids: [sla_policy.details.last.id],
          conditions: new_condition_attribute
        }
        expect { put :update, params: { id: sla_policy.id, params: params } }.to change{ sla_policy.details.count }.by(-1)
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe  "#sla_conditions" do
    context "adding sla conditions" do
      it "will not create sla conditions if condition type isn't provided" do
        params = { 
          policy: { 
                    name: 'Updated Policy Conditions', 
                    custom_form: { id: custom_form_1.id } 
                  }, 
          conditions: invalid_condition_attribute
        }
        put :update, params: { id: sla_policy.id, params: params }
        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response['message']).to eq("Validation failed: Condition type is required")
      end

      it "will add new sla condition" do
        params =  { 
          policy: { 
                    name: 'Updated Policy Conditions', 
                    custom_form: { id: custom_form_1.id } 
                  }, 
          conditions: new_condition_attribute,
          details: new_detail_attribute
        }
        put :update, params: { id: sla_policy.id, params: params }
        expect(response).to have_http_status(:ok)
        sla_policy.reload
        new_condition = sla_policy.conditions.find_by(condition_type: new_condition_attribute[0]["condition_type"])
        expect(new_condition).to be_present
      end

      it "will remove sla condition" do
        sla_condition = sla_policy.conditions.create!("condition_type"=> "form_field",
                                                      "field_type"=>"category",
                                                      "value"=>{"form_value"=>"Facilities"}
                                                      )
        params =  { 
          policy: { 
                    name: 'Updated Policy', 
                    custom_form: { id: custom_form_1.id } 
                  }, 
          details: detail_attribute,
          removed_condition_ids: [sla_condition.id],
          conditions: new_condition_attribute
        }
        put :update, params: { id: sla_policy.id, params: params }
        expect(response).to have_http_status(:ok)
        removed_condition = sla_policy.conditions.find_by(id: sla_condition.id)
        expect(removed_condition).to be_nil
      end
    end

    context 'when there is another policy with no conditions for same custom form' do
      let!(:sla_policy_2) { FactoryBot.create(:sla_policy, company: company, custom_form: custom_form_1, name: 'SLA Policy3') }
  
      it 'should return error to the policy' do
        params =  { 
          policy: { 
                    name: 'Updated Policy Conditions', 
                    custom_form: { id: custom_form_1.id } 
                  },
          details: new_detail_attribute
        }
        put :update, params: { id: sla_policy.id, params: params }
        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response['message']).to eq("policy having no conditions with the same custom form already exists")
      end
    end
  end
end
