require 'rails_helper'
include CompanyUserHelper

RSpec.describe DiscoveredAssetsSummaryController, type: :controller do
  create_company_and_user

  describe "Index" do
    let(:ready_to_import_params) do
      {
        display_name: "asset_1",
        mac_addresses: ["abc", "def"],
        ip_address: "***********",
        status: 0
      }
    end
    let(:incomplete_params) do
      {
        display_name: "asset_1",
        mac_addresses: ["abc", "def"],
        ip_address: "***********",
        status: 1
      }
    end
    let(:imported_params) do
      {
        display_name: "asset_1",
        mac_addresses: ["abc", "def"],
        ip_address: "***********",
        status: 2
      }
    end

    it "test summary of discovered assets" do
      discovered_asset1 = company.discovered_assets.create(incomplete_params)
      discovered_asset2 = company.discovered_assets.create(imported_params)
      discovered_asset3 = company.discovered_assets.create(ready_to_import_params)
      response = get :index, format: :json
      result = JSON.parse(response.body)
      expect(result["totalReadyForImport"]).to eq(1)
      expect(result["totalIncomplete"]).to eq(1)
      expect(result["totalImported"]).to eq(1)
    end
  end
end
