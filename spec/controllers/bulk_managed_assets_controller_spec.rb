require 'rails_helper'
include CompanyUserHelper

RSpec.describe BulkManagedAssetsController, type: :controller do
  create_company_and_user

  let!(:company_asset_type) { CompanyAssetType.create!(name: "Default Asset Type", company_id: company.id) }
  let!(:company_asset_status1) { CompanyAssetStatus.create!(name: "Alive", company: company, icon: "genuicon-checkmark") }

  let!(:assets) do
    [
      ManagedAsset.create!(
        name: "MacBook Pro 43",
        company_id: company.id,
        company_asset_type_id: company_asset_type.id,
        machine_serial_number: "",
        ip_address: "",
        product_number: nil,
        asset_tag: nil,
        department: nil,
        acquisition_date: nil,
        warranty_expiration: nil,
        install_date: nil,
        model: nil,
        description: nil,
        company_asset_status_id: company_asset_status1.id
      ),
      ManagedAsset.create!(
        name: "MacBook Pro 44",
        company_id: company.id,
        company_asset_type_id: company_asset_type.id,
        machine_serial_number: "",
        ip_address: "",
        product_number: nil,
        asset_tag: nil,
        department: nil,
        acquisition_date: nil,
        warranty_expiration: nil,
        install_date: nil,
        model: nil,
        description: nil,
        company_asset_status_id: company_asset_status1.id
      )
    ]
  end
  
  let!(:assignment_informations) do
    assets.each do |asset|
      AssignmentInformation.create!(managed_asset_id: asset.id)
    end
  end
  let!(:company_asset_status2) { CompanyAssetStatus.create!(name: "Dead", company: company, icon: "genuicon-checkmark") }

  describe "#bulk_update" do
    context 'with valid asset parameters' do
      let(:asset_params) do
        assets.map do |asset|
          {
            id: asset.id.to_i,
            name: asset.name,
            machine_serial_number: asset.machine_serial_number,
            ip_address: asset.ip_address,
            product_number: "",
            asset_tag: nil,
            department: nil,
            acquisition_date: nil,
            warranty_expiration: nil,
            install_date: nil,
            model: nil,
            description: nil,
            custom_status_id: company_asset_status2.id.to_s
          }
        end
      end
      it 'updates multiple assets successfully and returns a success message' do
        post :bulk_update, params: { assets: asset_params }, as: :json

        assets.each do |asset|
          asset.reload
          expect(asset.company_asset_status_id).to eq(company_asset_status2.id)
        end

        expect(response.status).to eq(200) 
        expect(response.body).to include('{}')
      end
    end
  end
end
