require 'rails_helper'
include CompanyUserHelper

describe ManagedAssetTagsController, type: :controller do
  create_company_and_user

  let!(:managed_asset) { FactoryBot.create(:managed_asset, company: company) }
  let!(:managed_asset2) { FactoryBot.create(:managed_asset, company: company) }
  let!(:managed_asset3) { FactoryBot.create(:managed_asset, name: 'desktop', company: company) }
  let!(:tag_1) { CompanyAssetTag.create!(name: 'tag 1', company_id: company.id) }
  let!(:tag_2) { CompanyAssetTag.create!(name: 'tag 2', company_id: company.id) }
  let!(:asset_tag) { FactoryBot.create(:managed_asset_tag, company_asset_tag_id: tag_2.id, managed_asset_id: managed_asset3.id) }

  describe '#add_tags' do
    context 'with tags params' do
      it 'will add tags' do
        post :add_tags, params: { format: :json, asset_ids: [managed_asset.id], tags: [tag_1.name] }
        managed_asset.reload
        expect(response).to have_http_status(:ok)
        expect(managed_asset.managed_asset_tags.count).to eq(1)
      end
    end

    context 'with removed tags params' do
      it 'will remove tags' do
        post :add_tags, params: { format: :json, asset_ids: [managed_asset3.id], removed_tags: [tag_2.name] }
        managed_asset3.reload
        expect(response).to have_http_status(:ok)
        expect(managed_asset3.managed_asset_tags).not_to be_present
      end
    end

    context 'without any tag or removed tag' do
      it 'will return 404' do
        params = {
          format: :json,
          asset_ids: [managed_asset.id, managed_asset2.id],
        }
        post :add_tags, params: params
        expect(response).to have_http_status(:not_found)
      end
    end
  end

end
