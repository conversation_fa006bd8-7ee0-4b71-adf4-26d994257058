require 'rails_helper'
include CompanyUserHelper

describe Options::TelecomProviderOptionsController, type: :controller do
  create_company_and_user

  describe "#index" do
    let!(:provider) { FactoryBot.create(:telecom_provider, name: 'AT&T', company: company) }
    let!(:provider1) { FactoryBot.create(:telecom_provider, name: 'Verizon', archived: true, company: company) }

    context "when fetching all company telecom providers" do
      before { get :index, params: { format: :json } }

      it "should return a 200 status" do
        expect(response).to have_http_status(:ok)
      end

      it "should fetch all company telecom providers in alphabetical order" do
        provider_options = JSON.parse(response.body)
        providers = provider_options.select {|v| v['type'] == 'TelecomProvider'}
        expect(providers.length).to eq(1)
        expect(providers.first['name']).to eq('AT&T')
      end
    end
  end
end
