require 'rails_helper'
include CompanyUserHelper

RSpec.describe Options::SourceOptionsController, type: :controller do
  create_company_and_user

  let(:source_options) do
    HelpTicket.sources.map do |source, id|
      name = case source
             when "ms_teams" then "microsoft_teams"
             when "splitted" then "split"
             else source
             end

      {
        'id' => id,
        'name' => name.titleize
      }
    end
  end

  describe '#index' do
    it 'will get the status of tickets' do
      get :index, params: { 'format'=>'json' }

      results = JSON.parse(response.body)
      expect(results).to eq(source_options)
    end
  end
end
