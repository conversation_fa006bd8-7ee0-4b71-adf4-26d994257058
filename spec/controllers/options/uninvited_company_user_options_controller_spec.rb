require 'rails_helper'
include CompanyUserHelper

describe Options::UninvitedCompanyUserOptionsController, type: :controller do
  create_company_and_user

  let(:user_1_attributes) do
    {
      email: "<EMAIL>",
      password: "password2",
      first_name: "<PERSON>",
      last_name: "<PERSON>",
      terms_of_services: true
    }
  end

  let(:user_2_attributes) do
    {
      email: "<EMAIL>",
      password: "password2",
      first_name: "<PERSON>",
      last_name: "<PERSON>",
      terms_of_services: true
    }
  end

  let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
  let!(:user_1) { create(:user, user_1_attributes) }
  let!(:user_2) { create(:user, user_2_attributes) }

  let!(:company_user_0) { user.company_users.find_by(company_id: company.id, custom_form_id: custom_form.id) }
  let!(:company_user_1) { CompanyUser.create(company_id: company.id, user_id: user_1.id, custom_form_id: custom_form.id) }
  let!(:company_user_2) { CompanyUser.create(company_id: company.id, user_id: user_2.id, custom_form_id: custom_form.id) }

  describe "#uninvited_users" do
    it "collects only uninvited users alphabetically by first name" do
      get :index, format: :json

      expect(response).to have_http_status(:ok)
      json_response = JSON.parse(response.body)
      expect(json_response.first['id']).to eq(company_user_2.id)
      expect(json_response.last['id']).to eq(company_user_1.id)
      expect(json_response).to_not include(company_user_0)
    end
  end
end
