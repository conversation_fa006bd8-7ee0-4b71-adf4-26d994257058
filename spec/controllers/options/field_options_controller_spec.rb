require 'rails_helper'
include Company<PERSON>ser<PERSON>elper

describe Options::FieldOptionsController, type: :controller do
  create_company_and_user

  let!(:custom_form1) {
    company.custom_forms.create(form_name: 'Form 1', company_module: 'helpdesk', is_active: true)
  }
  let!(:text1) {
    custom_form1.custom_form_fields.create(name: 'text1', label: 'Text 1', field_attribute_type: 'text')
  }
  let!(:list1) {
    custom_form1.custom_form_fields.create(name: 'list1', label: 'List 1', field_attribute_type: 'list', options: ['square', 'circle'])
  }
  let!(:custom_form2) {
    company.custom_forms.create(form_name: 'Form 2', company_module: 'helpdesk', is_active: true)
  }
  let!(:text2) {
    custom_form2.custom_form_fields.create(name: 'text1',
                                           label: 'Text 1',
                                           field_attribute_type: 'text')
  }
  let!(:list2) {
    custom_form2.custom_form_fields.create(name: 'list1',
                                           label: 'List 1',
                                           field_attribute_type: 'list',
                                           options: ['blue', 'red'].to_json)
  }
  let!(:custom_form3) {
    company.custom_forms.create(form_name: 'Form 3', company_module: 'helpdesk', is_active: true)
  }
  let!(:text3) {
    custom_form3.custom_form_fields.create(name: 'text1',
                                           label: 'Text 1',
                                           field_attribute_type: 'text')
  }
  let!(:status3) {
    custom_form3.custom_form_fields.create(name: 'priority',
                                           label: 'Priority',
                                           field_attribute_type: 'priority',
                                           options: CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS
                                          )
  }
  let!(:custom_form4) {
    company.custom_forms.create(form_name: 'Form 4', company_module: 'helpdesk', is_active: true)
  }
  let!(:text4) {
    custom_form4.custom_form_fields.create(name: 'text1',
                                           label: 'Text 1',
                                           field_attribute_type: 'text')
  }
  let!(:status4) {
    custom_form4.custom_form_fields.create(name: 'priority',
                                           label: 'Priority',
                                           field_attribute_type: 'priority',
                                           options: JSON.parse(CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS).push({'name'=>'hold', 'color'=> '#44444'}).to_json
                                          )
  }

  before do
    Privilege.create(company_user: company_user, name: 'CompanyUser', permission_type: 'write')
    Privilege.create(company_user: company_user, name: 'HelpTicket', permission_type: 'write')
  end

  context "with no params" do
    it "returns nothing" do
      get :index, format: 'json'
      expect(response.body).to eq("")
    end

    it "fails with a 400 status" do
      get :index, format: 'json'
      expect(response.status).to eq(400)
    end
  end

  context "with options as an array for list type" do
    context "with good params" do
      it "returns good option list" do
        get :index, params: { name: "list1", module: 'helpdesk', attr_type: "list", format: 'json' }
        json = JSON.parse(response.body)
        expect(json).to eq([])
      end

      it "has a status 200" do
        get :index, params: { name: "list1", attr_type: "list", format: 'json' }
        expect(response.status).to eq(200)
      end
    end

    context "with limiting forms" do
      it "returns good option list" do
        get :index, params: { custom_form_id: [custom_form1.id, custom_form3.id].join(","), module: 'helpdesk', name: "list1", attr_type: "list", format: 'json' }
        json = JSON.parse(response.body)
        expect(json).to eq(['square', 'circle'])
      end

      it "has a status 200" do
        get :index, params: { name: "list1", module: 'helpdesk', attr_type: "list", format: 'json' }
        expect(response.status).to eq(200)
      end
    end
  end

  context "with options as an array of hashes for priority type" do
    context "with good params" do
      it "returns good option list" do
        get :index, params: { name: "priority", module: 'helpdesk', attr_type: "priority", format: 'json' }
        json = JSON.parse(response.body)
        expect(json).to eq(JSON.parse(CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS))
      end

      it "has a status 200" do
        get :index, params: { name: "priority", attr_type: "priority", format: 'json' }
        expect(response.status).to eq(200)
      end
    end

    context "with limiting forms" do
      it "returns good option list" do
        get :index, params: { custom_form_id: [custom_form1.id, custom_form3.id].join(","), module: 'helpdesk', name: "priority", attr_type: "priority", format: 'json' }
        json = JSON.parse(response.body)
        expect(json).to eq(JSON.parse(CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS))
      end

      it "has a status 200" do
        get :index, params: { name: "priority", attr_type: "priority", format: 'json' }
        expect(response.status).to eq(200)
      end
    end
  end
end