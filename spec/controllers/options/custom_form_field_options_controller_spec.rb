require 'rails_helper'
include CompanyUserHelper

describe Options::CustomFormFieldOptionsController, type: :controller do
  create_company_and_user

  let!(:custom_form1) {
    company.custom_forms.create(form_name: 'Form 1', company_module: 'helpdesk', is_active: true)
  }
  let!(:text1) {
    custom_form1.custom_form_fields.create(name: 'text1', label: 'Text 1', field_attribute_type: 'text')
  }
  let!(:list1) {
    custom_form1.custom_form_fields.create(name: 'list1', label: 'List 1', field_attribute_type: 'list', options: ['square', 'circle'])
  }
  let!(:custom_form2) {
    company.custom_forms.create(form_name: 'Form 2', company_module: 'helpdesk', is_active: true)
  }
  let!(:text2) {
    custom_form2.custom_form_fields.create(name: 'text1',
                                           label: 'Text 1',
                                           field_attribute_type: 'text_area')
  }
  let!(:list2) {
    custom_form2.custom_form_fields.create(name: 'list1',
                                           label: 'List 1',
                                           field_attribute_type: 'list',
                                           options: ['blue', 'red'].to_json)
  }
  let!(:custom_form3) {
    company.custom_forms.create(form_name: 'Form 3', company_module: 'helpdesk', is_active: true)
  }
  let!(:text3) {
    custom_form3.custom_form_fields.create(name: 'text1',
                                           label: 'Text 1',
                                           field_attribute_type: 'rich_text')
  }
  let!(:list3) {
    custom_form3.custom_form_fields.create(name: 'list1',
                                           label: 'List 1',
                                           field_attribute_type: 'list',
                                           options: ['blue', 'red'].to_json)
  }

  before do
    Privilege.create(company_user: company_user, name: 'CompanyUser', permission_type: 'write')
    Privilege.create(company_user: company_user, name: 'HelpTicket', permission_type: 'write')
  end

  context "with no params" do
    it "returns nothing" do
      get :index, format: 'json'
      json = JSON.parse(response.body)
      expect(json.size).to eq(15)
      names = json.select { |s| s['name'] == 'text1' }.map { |j| j['name'] }
      expect(names.length).to eq(3)
      types = json.select { |s| s['name'] == 'text1' }.map { |j| j['field_attribute_type'] }.sort
      expect(types).to eq(["rich_text", "text", "text_area"])
    end

    it "has a status 200" do
      get :index, params: { custom_form_id: "#{custom_form1.id}", format: 'json' }
      expect(response.status).to eq(200)
    end
  end

  context "with a single custom form id" do
    it "returns good option list" do
      get :index, params: { custom_form_id: "#{custom_form1.id}", format: 'json' }
      json = JSON.parse(response.body)
      expect(json.size).to eq(2)
      names = json.select { |s| s['name'] == 'text1' }.map { |j| j['name'] }
      expect(names.length).to eq(1)
      types = json.select { |s| s['name'] == 'text1' }.map { |j| j['field_attribute_type'] }.sort
      expect(types).to eq(["text"])
    end

    it "has a status 200" do
      get :index, params: { custom_form_id: "#{custom_form1.id}", format: 'json' }
      expect(response.status).to eq(200)
    end
  end

  context "with multiple custom form ids" do
    it "returns good option list" do
      get :index, params: { custom_form_id: "#{custom_form3.id},#{custom_form2.id}", format: 'json' }
      json = JSON.parse(response.body)
      expect(json.size).to eq(3)
      names = json.select { |s| s['name'] == 'text1' }.map { |j| j['name'] }
      expect(names.length).to eq(2)
      types = json.select { |s| s['name'] == 'text1' }.map { |j| j['field_attribute_type'] }.sort
      expect(types).to eq(["rich_text", "text_area"])
    end

    it "has a status 200" do
      get :index, params: { custom_form_id: "#{custom_form3.id},#{custom_form2.id}", format: 'json' }
      expect(response.status).to eq(200)
    end
  end
end