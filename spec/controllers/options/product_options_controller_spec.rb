require 'rails_helper'
include CompanyUserHelper

describe Options::ProductOptionsController, type: :controller do
  create_company_and_user

  describe "#index" do
    context "with a simple vendor/product" do
      let(:vendor) { FactoryBot.create(:vendor, company: company) }
      let!(:product) { FactoryBot.create(:product, vendor: vendor, company: company) }

      it "returns a valid request" do
        put :index, params: { vendor_id: vendor.id }
        expect(response).to have_http_status(:ok)
      end

      it "returns a product" do
        put :index, params: { vendor_id: vendor.id }

        product_options = JSON.parse(response.body)['products']
        expect(product_options).to be_present
        expect(product_options.length).to eq(1)
        expect(product_options[0]["name"]).to eq("MyProduct")
        expect(product_options[0]["id"]).to eq(product.id)
        expect(product_options[0]["type"]).to eq("Product")
      end
    end

    context "with a default vendor/product" do
      let(:vendor) { FactoryBot.create(:vendor, name: 'Microsoft', company: company) }
      let(:default_vendor) { DefaultVendor.find_by(name: 'Microsoft') }
      let(:default_product) { default_vendor.default_products.min_by { |product| product['name'].downcase } }
      let!(:product) { FactoryBot.create(:product,
                                         vendor: vendor,
                                         company: company,
                                         name: default_product.name,
                                         default_product: default_product) }

      it "returns a valid request" do
        put :index, params: { vendor_id: vendor.id }
        expect(response).to have_http_status(:ok)
      end

      it "returns product" do
        put :index, params: { vendor_id: vendor.id }
        product_options = JSON.parse(response.body)['products']
        expect(product_options).to be_present
        expect(product_options.length).to eq(360)
        expect(product_options[0]["name"]).to eq("Advanced Communications")
        expect(product_options[0]["type"]).to eq("Product")
      end
    end
  end

  context "with a default vendor/product" do
    let(:vendor) { FactoryBot.create(:vendor, name: 'Microsoft', company: company) }

    it "returns a valid request" do
      put :index, params: { vendor_id: vendor.id }
      expect(response).to have_http_status(:ok)
    end

    it "returns product" do
      put :index, params: { vendor_id: vendor.id }
      product_options = JSON.parse(response.body)['products']
      expect(product_options).to be_present
      expect(product_options.length).to eq(360)
      expect(product_options[0]["name"]).to eq("Advanced Communications")
      expect(product_options[0]["type"]).to eq("DefaultProduct")
    end
  end
end
