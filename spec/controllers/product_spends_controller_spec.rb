require 'rails_helper'
include CompanyUserHelper

RSpec.describe ProductSpendsController, type: :controller do
  create_company_and_user

  let(:vendor) { FactoryBot.create(:vendor, company: company, is_cloud_platform: true) }
  let!(:product) { FactoryBot.create(:product, vendor: vendor, company: company) }
  let!(:transaction1) { CloudUsageTransaction.create(company: company, vendor: vendor, product: product, transaction_date: Time.now, amount: 100, name: "RDS", is_manual: false) }
  let!(:transaction2) { CloudUsageTransaction.create(company: company, vendor: vendor, product: product, transaction_date: Time.now - 1.month, amount: 200, name: "RDS", is_manual: false) }
  let!(:transaction3) { CloudUsageTransaction.create(company: company, vendor: vendor, product: product, transaction_date: Time.now - 1.month, amount: 150, name: "Elastic Beanstalk", is_manual: false) }
  let!(:transaction4) { CloudUsageTransaction.create(company: company, vendor: vendor, product: product, transaction_date: Time.now - 2.months, amount: 570, name: "Elastic Beanstalk", is_manual: false) }

  describe "#index" do
    context "with a simple vendor/product" do
      it "returns a valid request" do
        get :index, params: { format: :json }
        expect(response).to have_http_status(:ok)

        current_year = Date.today.beginning_of_year
        cyt_amount = transaction1.amount
        cyt_amount += transaction2.amount if transaction2.transaction_date.beginning_of_year == current_year
        cyt_amount += transaction3.amount if transaction3.transaction_date.beginning_of_year == current_year
        cyt_amount += transaction4.amount if transaction4.transaction_date.beginning_of_year == current_year

        transaction_data = JSON.parse(response.body)
        expect(transaction_data["transactions_count"]).to eq(product.transactions.length)
        # expect(transaction_data["cost_per_license"]).to eq(0)
        expect(transaction_data["total_spend"]).to eq(transaction1.amount + transaction2.amount + transaction3.amount + transaction4.amount)
        expect(transaction_data["spend_ytd"]).to eq(cyt_amount)
        expect(transaction_data["spend_four_months_ago"]).to eq(0)
        expect(transaction_data["spend_three_months_ago"]).to eq(0)
        expect(transaction_data["spend_two_months_ago"]).to eq(transaction4.amount)
        expect(transaction_data["spend_last_month"]).to eq(transaction2.amount + transaction3.amount)
        expect(transaction_data["spend_this_month"]).to eq(transaction1.amount)
        expect(transaction_data["projected_month_spend"]).to eq(transaction1.amount + transaction2.amount + transaction3.amount)
        expect(transaction_data["cloud_spends_breakdown"]).to eq([{"name"=>"RDS", "total_amount"=>200.0}, {"name"=>"Elastic Beanstalk", "total_amount"=>150.0}, {"name"=>"Other", "total_amount"=>0.0}])
      end
    end
  end
end