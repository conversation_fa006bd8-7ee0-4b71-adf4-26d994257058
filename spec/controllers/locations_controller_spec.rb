require 'rails_helper'
include CompanyUserHelper

describe LocationsController, type: :controller do
  create_company_and_user

  let(:asset) { FactoryBot.create(:managed_asset, company: company) }
  let(:contract) { FactoryBot.create(:contract, name: 'contract1', company: company) }
  let(:vendor) { FactoryBot.create(:vendor, company_id: company.id, category_id: company.categories.first.id, name: "Microsoft", url: "www.microsoft.com") }

  describe "#index" do
    context "with mulitple locations" do
      before do
        15.times do |i|
          create(:location, company: company)
        end
      end

      it "shows additional locations" do
        get :index, params: { per_page: 10, page: 1 },  format: :json

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['count']).to eq(16)
        expect(json_response['locations']).to be_present
      end
    end
  end

  describe "#create" do
    context "with format json" do
      before :each do
        create(:location, company: company)
      end

      it "does create a location" do
        expect(company.locations.last.name).to eq("HQ")
      end
    end
  end
end
