require 'rails_helper'
include CompanyUserHelper

RSpec.describe VendorContactsController, type: :controller do
  create_company_and_user

  let!(:vendor) { create(:vendor, company: company) }
  let!(:vendor_contact1) { VendorContact.create(vendor_id: vendor.id, first_name: "<PERSON>", last_name: "Contact", email: "<EMAIL>") }
  let!(:vendor_contact2) { VendorContact.create(vendor_id: vendor.id, first_name: "<PERSON>", last_name: "Contact", email: "<EMAIL>") }

  describe "#index" do
    it "should get all the vendor contacts for the company" do
      get :index, params: { format: :json, vendor_id: vendor.id }

      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body).count).to eq(2)
    end
  end

  describe "#show" do
    it "should get a specific vendor contact" do
      get :show, params: { format: :json, vendor_id: vendor.id, id: vendor_contact2.id }

      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)["id"]).to eq(vendor_contact2.id)
    end
  end

  describe "#create" do
    it "should create a specific vendor contact" do
      post :create, params: { vendor_id: vendor.id, vendor_contact: { vendor_id: vendor.id, email: "<EMAIL>" }, format: :json }

      expect(response).to have_http_status(:ok)
      expect(vendor.vendor_contacts.count).to eq(3)
      expect(vendor.vendor_contacts.last.email).to eq("<EMAIL>")
    end
  end
end