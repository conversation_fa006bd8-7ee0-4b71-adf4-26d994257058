require 'rails_helper'
include CompanyUserHelper

RSpec.describe ContractAlertDatesController, type: :controller do
  create_company_and_user

  before do
    @alert_date = AlertDate.new(date: Date.today + 6.months)
  end

  let(:contract_attributes) do
    {
      name: "Internet",
      alert_dates: [ @alert_date ],
      start_date: Date.today,
      end_date: Date.today + 1.year,
      company_id: company.id
    }
  end

  let!(:contract) { Contract.create(contract_attributes) }

  describe "#update" do
    context "with alert dates created" do
      it "will not update alert dates if they are after contract expiration keeping the original one" do
        put :update, params: { contract: { alert_dates_attributes:  [{ date: "2021-06-03T00:00:00.000-05:00" }, { date: "2020-01-30T06:00:00.000Z" }, { date: "2020-03-18T05:00:00.000Z" }]}, id: contract.id, format: :json }

        expect(response).to have_http_status(:unprocessable_entity)
        options = JSON.parse(response.body)
        expect(contract.alert_dates.count).to eq(1)
      end

      it "will update alert dates if they are before contract expiration" do
        put :update, params: {
          contract:
          { alert_dates_attributes: [
              { id: @alert_date.id, date: Date.today + 2.days },
              { date: Date.today + 3.days }
            ]
          },
          id: contract.id,
          format: :json
        }
        expect(response).to have_http_status(:ok)
        options = JSON.parse(response.body)
        contract.reload
        expect(contract.alert_dates.count).to eq(2)
      end
    end
  end
end
