require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

RSpec.describe CustomSurveysResponsesController, type: :controller do
  create_company_and_user

  let!(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let!(:company_user2) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let!(:help_ticket_params) {
    {
      'Subject' => 'Base Ticket',
      'Created By' => company_user.contributor_id.to_s,
      'Assigned To' => company_user.contributor_id.to_s,
      'Status' => 'Open',
      'Priority' => 'low',
      'Description' => 'This is a test ticket.',
      company: company,
      workspace: workspace
    }
  }
  let!(:help_ticket) { create_ticket(help_ticket_params) }

  let!(:custom_survey) { 
    CustomSurvey.create!(
      title: "Custom Survey Response Spec Test Cont",
      company: company,
      workspace: workspace
    )
  }

  let!(:survey_response) { 
    CustomSurvey::Response.create!(
      name: "Response Spec Test",
      status: "opened",
      contributor: Contributor.last,
      workspace: workspace,
      company: company,
      custom_survey: custom_survey,
      help_ticket: help_ticket
    ) 
  }

  describe "GET index" do
    it "returns custom survey responses" do
      get :index, params: {
          per_page: 20,
          page: 0,
          search_filter: '', 
          sort_column: 'name',
          sort_direction: 'asc'
      }, format: :json
      
      responses = JSON.parse(response.body)['survey_responses']
      expect(response).to have_http_status(:ok)
      expect(responses).to be_present
      expect(responses.last['id']).to eq(survey_response.id)
    end
  end

  describe "PATCH update" do
    context "with valid parameters" do
      it "updates the existing response" do

        patch :update, params: { 
          id: survey_response.id, 
          params: {
            name: "Response Spec Test Updated",
            status: "Response Status Spec Test Updated",
          }
        }, format: :json
        survey_response.reload

        expect(response).to have_http_status(:ok)
        expect(survey_response.name).to eq("Response Spec Test Updated")
        expect(survey_response.status).to eq("Response Status Spec Test Updated")
      end
    end
  
    context "when the custom survey response does not exist" do
      it "returns a status of (204) no content" do
        expect {
          patch :update, params: { 
            id: 99999, 
            params: {
              name: "Response Spec Test Updated",
              status: "Response Status Spec Test Updated",
            }
          }, format: :json
        }.to raise_error(NoMethodError, /undefined method `update' for nil:NilClass/)
      end
    end
  end
  
  describe "PERFORM custom methods" do
    context "when valid parameters are passed for unopened response" do
      it "returns the status ok" do
        get :get_unopened_response, params: { 
          workspaceId: survey_response.workspace.id,
          customSurveyId: custom_survey.id,
          helpTicketId: help_ticket.id
        }
        expect(response).to have_http_status(:ok)
      end
    end

    context "when valid parameters are passed for resend_email" do
      it "returns the status ok" do
        CustomSurvey::Trigger.create!(
            custom_survey_id: custom_survey.id,
            button_text: "Button Test" 
        )
        get :resend_email, params: { 
          id: survey_response.id
        }
        expect(response).to have_http_status(:ok)
      end
    end
  end
end
