require 'rails_helper'
include CompanyUserHelper

describe SubdomainChecksController, type: :controller do
  create_company_and_user

  let!(:company2) { create(:company) }

  context "with valid subdomain" do
    it "returns success message" do
      get :show, params: {"id"=>"efefef"}, format: :json
      expect(response).to have_http_status(:ok)
      expect(response.body).to eq("valid")
    end
  end

  context "with invalid/duplicate subdomain" do
    it "returns failure message" do
      get :show, params: {"id"=> company2.subdomain}, format: :json
      expect(response).to have_http_status(:ok)
      expect(response.body).to eq("taken")
    end
  end
end