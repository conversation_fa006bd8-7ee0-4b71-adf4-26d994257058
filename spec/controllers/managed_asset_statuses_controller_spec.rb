require 'rails_helper'
include Company<PERSON>serHelper

describe ManagedAssetStatusesController, type: :controller do
  create_company_and_user

  before do
    controller.send(:set_privilege)
  end

  let!(:laptop) { FactoryBot.create(:managed_asset, :laptop, company: company) }

  let(:company_asset_statuses) do
    [
      {"name"=>"In Use", "icon"=>"genuicon-checkmark"},
      {"name"=>"Ready to Use", "icon"=>"nulodgicon-arrow-right-b"},
      {"name"=>"Needs Attention", "icon"=>"genuicon-exclamation"},
      {"name"=>"Unusable", "icon"=>"&times;"}
    ]
  end

  describe "#index" do
    it "should return managed asset status options" do
      get :index, params: { format: :json }

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)
      company_asset_statuses.each do |status|
        expect(result.find { |res| res['name'] == status['name'] && res['icon'] == status['icon'] }).to be_present
      end
    end
  end

  describe "#update" do
    it "should update the managed asset status" do
      new_status = CompanyAssetStatus.find_by_name('Ready to Use')
      put :update, params: { id: laptop.id, status_id: new_status.id, format: :json }
      laptop.reload
      expect(response).to have_http_status(:ok)
      expect(laptop.custom_status).to eq(new_status)
    end
  end
end
