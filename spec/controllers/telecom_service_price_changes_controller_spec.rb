require 'rails_helper'
include Company<PERSON>serHelper

describe TelecomServicePriceChangesController, type: :controller do
  create_company_and_user

  let(:category) { company.categories.find_by(name: 'Telecom') }

  let!(:telecom_provider) { FactoryBot.create(:telecom_provider,
                                              name: 'AT&T',
                                              company: company) }

  let!(:voice_service) { FactoryBot.create(:telecom_service,
                                           name: 'Main Voice',
                                           company: company,
                                           telecom_provider: telecom_provider,
                                           monthly_cost: 100,
                                           service_type: "voice_service",
                                           created_at: Time.now - 2.months) }
    let!(:data_service) { FactoryBot.create(:telecom_service,
                                            name: 'Main Data',
                                            company: company,
                                            monthly_cost: 150,
                                            telecom_provider: telecom_provider,
                                            service_type: "data_service",
                                            created_at: Time.now - 1.month) }

  describe "#show" do
    context "when fetching monthly costs for a provider" do
      it "should fetch all costs based on the price changes over each month" do
        original_voice_service_price_change = voice_service.price_changes.first
        original_voice_service_price_change.update(created_at: voice_service.created_at)
        original_data_service_price_change = data_service.price_changes.first
        original_data_service_price_change.update(created_at: data_service.created_at)

        voice_service.update(monthly_cost: 150) # create another price change record to test
        new_voice_service_price_change = voice_service.price_changes.last
        new_voice_service_price_change.update(created_at: 1.month.ago) # updating created_at since technically it would create this record when the update occurs

        get :show, params: { id: telecom_provider.id, format: :json }

        res = JSON.parse(response.body)["history"]
        date = voice_service.created_at.strftime("%b '%y")
        expect(res[0].keys).to eq([ date ])
        expect(res[0][date]['total']).to eq(100)
        voice_product = res[0][date]['products'].select { |p| p['id'] == voice_service.id }.first
        expect(voice_product).to eq(
          {
            "id" => voice_service.id,
            "amount"=>100.0,
            "telecom_service_id" => voice_service.id
          }
        )
        data_product = res[0][date]['products'].select { |p| p['id'] == data_service.id }.first
        expect(data_product).to eq(
          {
            "id" => data_service.id,
            "amount" => 0,
            "telecom_service_id" => data_service.id
          }
        )

        date = data_service.created_at.strftime("%b '%y")
        expect(res[1].keys).to eq([ date ])
        expect(res[1][date]['total']).to eq(300)
        voice_product = res[1][date]['products'].select { |p| p['id'] == voice_service.id }.first
        expect(voice_product).to eq(
          {
            "id" => voice_service.id,
            "amount"=>150.0,
            "telecom_service_id" => voice_service.id
          }
        )
        data_product = res[1][date]['products'].select { |p| p['id'] == data_service.id }.first
        expect(data_product).to eq(
          {
            "id" => data_service.id,
            "amount" => 150.0,
            "telecom_service_id" => data_service.id
          }
        )

        date = Time.zone.now.strftime("%b '%y")
        expect(res[2].keys).to eq([ date ])
        expect(res[2][date]['total']).to eq(300)
        voice_product = res[2][date]['products'].select { |p| p['id'] == voice_service.id }.first
        expect(voice_product).to eq(
          {
            "id" => voice_service.id,
            "amount"=>150.0,
            "telecom_service_id" => voice_service.id
          }
        )
        data_product = res[2][date]['products'].select { |p| p['id'] == data_service.id }.first
        expect(data_product).to eq(
          {
            "id" => data_service.id,
            "amount" => 150.0,
            "telecom_service_id" => data_service.id
          }
        )
      end
    end
  end
end