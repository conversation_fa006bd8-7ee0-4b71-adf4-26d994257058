require 'rails_helper'
require 'support/company_user_helper'
require 'support/login_helper'

describe ServiceSummariesController, type: :controller do
  extend CompanyUserHelper

  create_company_and_user

  before do
    time = Timecop.freeze(Date.strptime('2017-04-01').to_date)
    allow(Time).to receive(:current).and_return(time.utc)
    allow_any_instance_of(AccessCheck).to receive(:admin?).and_return(true)
    allow_any_instance_of(ServiceSummariesController).to receive(:current_company).and_return(Company.last)
    allow_any_instance_of(ServiceSummariesController).to receive(:current_user).and_return(Company.last.company_users.first.user)
  end

  after do
    Timecop.return
  end

  describe "#index" do
    context "a service that is too early" do
      before do
        company = Company.last
        @telecom_service = create(:telecom_service,
                                  company: company,
                                  monthly_cost: 200,
                                  service_type: 'voice_service')
        @telecom_service.save!
        @telecom_service.update_attribute('created_at', 5.months.ago)
        @telecom_service.update_attribute('archived_at', 5.months.ago)
        price_change = @telecom_service.price_changes.first
        price_change.update_attribute('created_at', 5.months.ago)
      end

      it "returns nothing if only previous archived services exist" do
        get :index
        json = JSON.parse(response.body)
        expect(json).to eq({})
      end
    end

    context "a service that is archived before the start date" do
      before do
        company = Company.last
        start_time = 4.months.ago + 4.days
        @telecom_service = create(:telecom_service,
                                  company: company,
                                  monthly_cost: 200,
                                  service_type: 'voice_service')
        @telecom_service.save!
        @telecom_service.update_attribute('created_at', start_time)
        @telecom_service.update_attribute('archived_at', start_time)
        price_change = @telecom_service.price_changes.first
        price_change.update_attribute('created_at', start_time)
      end

      it "returns a data from single month and no data afterwards" do
        get :index
        json = JSON.parse(response.body)
        expectations = {"12.01.2016"=>200.0, "01.01.2017"=>0, "02.01.2017"=>0, "03.01.2017"=>0}
        expect(json).to eq(expectations)
      end
    end

    context "a service that is in the first month but is archived 2 months later" do
      before do
        company = Company.last
        start_time = 5.months.ago + 4.days
        deleted_time = start_time + 2.months
        @telecom_service = create(:telecom_service,
                                  company: company,
                                  monthly_cost: 200,
                                  service_type: 'voice_service')
        @telecom_service.update_attribute('created_at', start_time)
        @telecom_service.update_attribute('archived_at', deleted_time)
        price_change = @telecom_service.price_changes.first
        price_change.update_attribute('created_at', start_time)
      end

      it "returns only the data from the months in which it existed and no data afterwards" do
        get :index
        json = JSON.parse(response.body)
        expectations = {"12.01.2016"=>200.0, "01.01.2017"=>200.0, "02.01.2017"=>0, "03.01.2017"=>0}
        expect(json).to eq(expectations)
      end
    end

    context "a service that is in the second month and not deleted" do
      before do
        company = Company.last
        start_time = 3.months.ago + 4.days
        @telecom_service = create(:telecom_service,
                                  company: company,
                                  monthly_cost: 200,
                                  service_type: 'voice_service')
        @telecom_service.update_attribute('created_at', start_time)
        price_change = @telecom_service.price_changes.first
        price_change.update_attribute('created_at', start_time)
      end

      it "returns only data from the month created and later and no data for previous months" do
        get :index
        json = JSON.parse(response.body)
        expectations = {"12.01.2016"=>0, "01.01.2017"=>200.0,"02.01.2017"=>0, "03.01.2017"=>0}
        expect(json).to eq(expectations)
      end
    end

    context "a service that is older than our span and not archived" do
      before do
        company = Company.last
        start_time = 10.months.ago + 4.days
        @telecom_service = create(:telecom_service,
                                  company: company,
                                  monthly_cost: 200,
                                  service_type: 'voice_service')
        @telecom_service.update_attribute('created_at', start_time)
        price_change = @telecom_service.price_changes.first
        price_change.update_attribute('created_at', start_time)
      end

      it "returns only data from the month created and later" do
        get :index
        json = JSON.parse(response.body)
        expectations = {"12.01.2016"=>200.0, "01.01.2017"=>200.0, "02.01.2017"=>200.0, "03.01.2017"=>200.0}
        expect(json).to eq(expectations)
      end
    end
  end
end
