require 'rails_helper'
include Company<PERSON>ser<PERSON>elper
include <PERSON><PERSON><PERSON><PERSON><PERSON>

describe TelecomServicesController, type: :controller do
  create_company_and_user

  before { set_default_host }

  let(:telecom_provider) { create(:telecom_provider, company: company) }
  let(:telecom_service_attributes) do
    {
      name: "Test",
      telecom_provider_id: telecom_provider.id,
      account_number: "MyString",
      monthly_cost: 1200,
      term_type: "months",
      company_id: company.id,
      connection: "connection",
      circuit_id: "7417",
      priority: 0,
      service_type: 0,
      equipment: "equipment",
      network_speed: "10MBps"
    }
  end

  let(:telecom_service_attributes_2) do
    {
      name: "Another",
      telecom_provider_id: telecom_provider.id,
      account_number: "*********",
      monthly_cost: 1200,
      term_type: "months",
      company_id: company.id,
      connection: "connection",
      circuit_id: "7417",
      priority: 0,
      service_type: 1,
      equipment: "equipment",
      network_speed: "10MBps"
    }
  end

  let(:location_form) { company.custom_forms.location.first }
  let(:first_location) { create(:location, company: company, custom_form: location_form) }
  let(:second_location) { create(:location, company: company, custom_form: location_form) }
  let!(:service) { TelecomService.create(telecom_service_attributes) }
  let!(:service2) {  TelecomService.create(telecom_service_attributes_2) }
  before do
    service.telecom_service_locations.create(location_id: first_location.id)
    service2.telecom_service_locations.create(location_id: second_location.id)
  end

  describe "with unauthorized user" do
    before { login_user(user1) }
    let(:user1) { create(:user) }

    context "to json request" do
      it "will return un-authorized message if user is unauthorized" do
        get :index, format: :json

        expect(response).to have_http_status(:unauthorized)
        expect(JSON.parse(response.body)['message']).to eq("Sorry, you're not authorized to perform this action.")
      end
    end

    context "to html request" do
      it "will redirect to access-denied page if user is unauthorized" do
        get :index, format: :html

        expect(response).to have_http_status(:found)
        expect(response.redirect_url).to start_with("http://secure.localhost/no_access")
      end
    end
  end

  describe "#index" do
    before { login_user }

    it "displays the telecom service" do
      allow(controller).to receive(:page_size).and_return(1)
      get :index, format: :json

      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)['services'].length).to eq(1)
      expect(JSON.parse(response.body)['page_count']).to eq(2)
    end

    it "will return telecom services with type" do
      params = { service_type: "voice_service", format: :json }
      get :index, params: params

      json_response = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)['services'].length).to eq(1)
      expect(JSON.parse(response.body)['services'].first['id']).to eq(service.id)
    end

    it "will return services with case-insensitive search by service name" do
      params = { search: "another", format: :json }
      get :index, params: params

      json_response = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)['services'].length).to eq(1)
      expect(JSON.parse(response.body)['services'].first['id']).to eq(service2.id)
    end

    it "will return services with case-insensitive search by provider name" do
      params = { search: telecom_provider.name, format: :json }
      get :index, params: params

      json_response = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)['services'].length).to eq(2)
    end

    it "will return services with case-insensitive search by account number" do
      params = { search: "*********", format: :json }
      get :index, params: params

      json_response = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)['services'].length).to eq(1)
      expect(JSON.parse(response.body)['services'].first['id']).to eq(service2.id)
    end

    it "will return services with location" do
      params = { location_id: first_location.id, format: :json }
      get :index, params: params

      json_response = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)['services'].length).to eq(1)
      expect(JSON.parse(response.body)['services'].first['id']).to eq(service.id)
    end
  end

  describe "#create" do
    let(:category) { company.categories.find_by(name: "Telecom") }
    let(:locations) { [company.locations.first] }
    let(:contract) { create(:contract, company: company) }
    let(:product) { create(:product, telecom_provider: telecom_provider, company: company) }

    before do
      login_user
      params = {
        name: "Telecom service",
        telecom_provider_id: telecom_provider.id,
        service_type: "data_service",
        category_id: category.id,
        account_number: "ABCDEFG",
        contract_id: contract,
        monthly_cost: 100,
        auto_monthly_cost: true,
        description: "This is a description.",
        all_locations: false,
        connection: "big connection",
        circuit_id: "BFG",
        priority: "Primary",
        network_speed: "fast",
        equipment: "AB100",
        location_ids: locations.map(&:id),
      }
      post :create, params: { telecom_service: params }, format: :json
    end

    it "returns a 200 status" do
      expect(response).to have_http_status(:ok)
    end

    it "creates a telecom service" do
      telecom = TelecomService.find_by(name: 'Telecom service', company_id: company.id)
      expect(telecom).to be_present
      expect(telecom.contract).to eq(contract)
      expect(telecom.telecom_provider).to eq(telecom_provider)
      expect(telecom.name).to eq("Telecom service")
      expect(telecom.service_type).to eq("data_service")
      expect(telecom.category).to eq(category)
      expect(telecom.account_number).to eq("ABCDEFG")
      expect(telecom.monthly_cost).to eq(100)
      expect(telecom.auto_monthly_cost).to eq(true)
      expect(telecom.description).to eq("This is a description.")
      expect(telecom.all_locations).to eq(false)
      expect(telecom.connection).to eq("big connection")
      expect(telecom.circuit_id).to eq("BFG")
      expect(telecom.priority).to eq("Primary")
      expect(telecom.network_speed).to eq("fast")
      expect(telecom.equipment).to eq("AB100")
      expect(telecom.locations.to_a).to eq(locations)
    end
  end

  describe "#update" do
    let(:telecom) { create(:telecom_service, company: company) }
    let(:category) { company.categories.find_by(name: "Telecom") }
    let(:locations) { [company.locations.first] }
    let(:contract) { create(:contract, company: company) }

    before do
      login_user
      params = {
        name: "Telecom service",
        telecom_provider_id: telecom_provider.id,
        service_type: "data_service",
        category_id: category.id,
        account_number: "ABCDEFG",
        contract_id: contract,
        monthly_cost: 100,
        auto_monthly_cost: true,
        description: "This is a description.",
        all_locations: false,
        connection: "big connection",
        circuit_id: "BFG",
        priority: "Primary",
        network_speed: "fast",
        equipment: "AB100",
        location_ids: locations.map(&:id),
      }
      put :update, params: { id: telecom.id, telecom_service: params }, format: :json
    end

    it "returns a 200 status" do
      expect(response).to have_http_status(:ok)
    end

    it "creates a telecom service" do
      telecom = TelecomService.find_by(name: 'Telecom service' , company_id: company.id)
      expect(telecom).to be_present
      expect(telecom.contract).to eq(contract)
      expect(telecom.telecom_provider).to eq(telecom_provider)
      expect(telecom.name).to eq("Telecom service")
      expect(telecom.service_type).to eq("data_service")
      expect(telecom.category).to eq(category)
      expect(telecom.account_number).to eq("ABCDEFG")
      expect(telecom.monthly_cost).to eq(100)
      expect(telecom.auto_monthly_cost).to eq(true)
      expect(telecom.description).to eq("This is a description.")
      expect(telecom.all_locations).to eq(false)
      expect(telecom.connection).to eq("big connection")
      expect(telecom.circuit_id).to eq("BFG")
      expect(telecom.priority).to eq("Primary")
      expect(telecom.network_speed).to eq("fast")
      expect(telecom.equipment).to eq("AB100")
      expect(telecom.locations.to_a).to eq(locations)
    end
  end

  describe "#destroy" do
    before { login_user }
    context "with remove associations" do
      let(:telecom) { create(:telecom_service,
                             :with_ip_addresses,
                             :with_phone_numbers,
                             company: company) }

      before do
        company.telecom_services.destroy_all
        delete :destroy, params: { removeAssociations: "true", id: telecom.id }, format: :json
      end

      it "returns a 200 status" do
        expect(response).to have_http_status(:ok)
      end

      it "deletes the telecom service" do
        my_company = Company.find_by(id: company.id)
        expect(my_company.telecom_services).to be_blank
      end

      it "deletes the ip addresses" do
        ip_addresses = IpAddress.where(company_id: company.id)
        expect(ip_addresses).to be_blank
      end

      it "deletes the phone numbers" do
        phone_numbers = PhoneNumber.where(company_id: company.id)
        expect(phone_numbers).to be_blank
      end
    end

    context "without remove associations" do
      let(:telecom) { create(:telecom_service,
                             :with_ip_addresses,
                             :with_phone_numbers,
                             company: company) }

      before do
        login_user
        company.telecom_services.destroy_all
        delete :destroy, params: { removeAssociations: "false", id: telecom.id }, format: :json
      end

      it "returns a 200 status" do
        expect(response).to have_http_status(:ok)
      end

      it "deletes the telecom service" do
        my_company = Company.find_by(id: company.id)
        expect(my_company.telecom_services).to be_blank
      end

      it "deassociates the ip addresses" do
        ip_addresses = IpAddress.where(company_id: company.id)
        expect(ip_addresses&.size).to eq(1)
        expect(ip_addresses.first.telecom_service_id).to be_blank
      end

      it "deassociates the phone numbers" do
        phone_numbers = PhoneNumber.where(company_id: company.id)
        expect(phone_numbers&.size).to eq(1)
        expect(phone_numbers.first.telecom_service_id).to be_blank
      end
    end
  end
end
