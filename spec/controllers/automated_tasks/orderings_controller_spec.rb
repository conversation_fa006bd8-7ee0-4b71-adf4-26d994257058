require 'rails_helper'
include CompanyUserHelper

RSpec.describe AutomatedTasks::OrderingsController, type: :controller do
  create_company_and_user

  describe "PUT #update" do
    subject { put :update, params: task_params }

    let!(:task1) { company.automated_tasks.create(name: "Task 1", order: 3) }
    let!(:task2) { company.automated_tasks.create(name: "Task 2", order: 2) }
    let!(:task3) { company.automated_tasks.create(name: "Task 3", order: 1) }
    let(:expected) { [ task1.id, task2.id, task3.id ] }
    let(:task_params) do
      idx = 0;
      tasks = []
      expected.each do |id|
        tasks << { id: id, order: idx}
        idx += 1
      end
      {
        tasks: tasks
      }
    end

    before { subject }

    it "is a successful http request" do
      subject
      expect(response).to have_http_status(:ok)
    end

    it "creates the asset software" do
      ids = company.reload.automated_tasks.order(order: :asc).pluck(:id)
      expect(ids).to eq(expected)
    end
  end
end