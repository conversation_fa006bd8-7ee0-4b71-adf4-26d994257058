require 'rails_helper'
include CompanyUserHelper

RSpec.describe AutomatedTasks::StringInterpolationKeysController, type: :controller do
  create_company_and_user

  let(:custom_form) { company.default_workspace.custom_forms.first }
  let(:subject_field) { custom_form.custom_form_fields.find_by(name: "subject") }
  let(:name) { "Cool Ticket" }
  let(:help_ticket) {
    ticket = create(:help_ticket, company: company)
    ticket.custom_form_values << CustomFormValue.new(custom_form_field: subject_field, value_str: name)
    ticket
  }

  describe "#index" do
    context "for help ticket" do
      subject { get :index, :params => params }
      let(:params) do
        { type: "HelpTicket" }
      end

      it "is a successful http request" do
        subject
        expect(response).to have_http_status(:ok)
      end

      it "creates the asset software" do
        fields = CustomFormField.includes(:custom_form).where(custom_forms: { company_id: company.id, company_module: "helpdesk" })
        fields = fields.uniq { |f| [f.field_attribute_type, f.label] }
        keys = []
        fields.each do |f|
          keys << "ticket #{f.label.downcase}"
        end
        keys = keys + ["ticket button", "ticket number", "ticket created at", "url", "ticket comments", "ticket created by first name", "company name"]

        subject
        json = JSON.parse(response.body)
        expect(json).to eql(keys)
      end
    end

    context "for project task" do
      subject { get :index, :params => params }
      let(:params) do
        { type: "ProjectTask" }
      end

      it "is a successful http request" do
        subject
        expect(response).to have_http_status(:ok)
      end

      it "creates the asset software" do
        fields = CustomFormField.includes(:custom_form).where(custom_forms: { company_id: company.id, company_module: "helpdesk" })
        fields = fields.uniq { |f| [f.field_attribute_type, f.label] }
        keys = []
        keys = keys + ["task description", "task assignee", "task creator"]
        fields.each do |f|
          keys << "ticket #{f.label.downcase}"
        end
        keys = keys + ["ticket button", "ticket number", "ticket created at", "url", "ticket comments", "ticket created by first name", "company name"]

        subject
        json = JSON.parse(response.body)
        expect(json).to eql(keys)
      end
    end
  end

  describe "#key_interpolation" do
    context "For help ticket with valid text replacement" do
      subject { post :key_interpolation, :params => params }
      let(:params) do
        { entity_id: help_ticket.id,
          entity_class: "HelpTicket",
          key: "<div><!--block-->This ticket {ticket_subject} is cool.</div>",
        }
      end

      it "will return string with text replacement query replaced with value" do
        subject
        expect(response).to have_http_status(:ok)
        expect(response.body).to eq("<div><!--block-->This ticket #{name} is cool.</div>")
      end
    end
    
    context "For help ticket with invalid text replacement" do
      subject { post :key_interpolation, :params => params }
      let(:params) do
        { entity_id: help_ticket.id,
          entity_class: "HelpTicket",
          key: "<div><!--block-->This ticket {ticket_subjectt} is cool.</div>",
        }
      end

      it "will return the same string as key" do
        subject
        expect(response).to have_http_status(:ok)
        expect(response.body).to eq("<div><!--block-->This ticket {ticket_subjectt} is cool.</div>")
      end
    end

    context "For missing entity id" do
      subject { post :key_interpolation, :params => params }
      let(:params) do
        { entity_id: nil,
          entity_class: "HelpTicket",
          key: "<div><!--block-->This ticket {ticket_subject} is cool.</div>",
        }
      end

      it "will return http status 404" do
        subject
        expect(response).to have_http_status(:not_found)
        expect(JSON.parse(response.body)).to eq({})
      end
    end

    context "For missing entity class" do
      subject { post :key_interpolation, :params => params }
      let(:params) do
        { entity_id: help_ticket.id,
          entity_class: nil,
          key: "<div><!--block-->This ticket {ticket_subject} is cool.</div>",
        }
      end

      it "will return http status 404" do
        subject
        expect(response).to have_http_status(:not_found)
        expect(JSON.parse(response.body)).to eq({})
      end
    end
  end
end
