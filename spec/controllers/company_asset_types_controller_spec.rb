require 'rails_helper'
include CompanyUserHelper

RSpec.describe CompanyAssetTypesController, type: :controller do
  create_company_and_user

  let(:company_asset_types) do
    [ {"name"=>"Apple Device"}, {"name"=>"Desktop"}, {"name"=>"Dongle"}, {"name"=>"Firewall"}, {"name"=>"iPad"},
      {"name"=>"iPhone"}, {"name"=>"Laptop"}, {"name"=>"Mobile"}, {"name"=>"Other"}, {"name"=>"Phone"},
      {"name"=>"Phone System"}, {"name"=>"Printer"}, {"name"=>"Router"}, {"name"=>"Server"}, {"name"=>"Switch"},
      {"name"=>"Tablet"}, {"name"=>"Thin Client"}, {"name"=>"Unrecognized"}, {"name"=>"Tv"}, {"name"=>"Virtual Machine"}, {"name"=>"WAP"},
      {"name"=>"Windows"} ]
  end

  describe 'GET #index' do
    it 'return all asset types of a company' do
      get :index

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)
      company_asset_types.each do |type|
        expect(result['asset_types'].find { |res| res['name'] == type['name'] }).to be_present
      end
    end
  end
end
