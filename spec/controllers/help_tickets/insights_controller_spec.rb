require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

RSpec.describe HelpTickets::InsightsController, type: :controller do
  create_company_and_user

  let(:workspace_id) { company.default_workspace.id }
  let!(:company_user2) { create(:company_user, company: company_user.company) }

  let(:params) do
    {
      params: {
        "values" => [{ "name" => "status", "value" => "In Progress", "with_survey" => false }],
        "companyId" => company.id.to_s,
        "workspaceId" => workspace_id,
        "widgetId" => 1,
        "filters" => [],
        "type" => "priority"
      }.to_json
    }
  end

  let(:mock_insight) { instance_double(HelpDesk::Insight) }

  before do
    allow(HelpDesk::Insight).to receive(:new).and_return(mock_insight)

    type = JSON.parse(params[:params])['type'].underscore
    allow(mock_insight).to receive("fetch_#{type}_data".to_sym).and_return(expected_data)
  end

  describe 'GET #index' do
    context 'when payload has type "priority"' do
      let(:expected_data) do
        {
          "priority" => [
            { "name" => "high", "value" => 20 },
            { "name" => "low", "value" => 10 },
            { "name" => "medium", "value" => 5 }
          ],
          "widgetId" => 1
        }
      end

      it 'returns the correct insights data for priority' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "status"' do
      let(:params) do
        {
          params: {
            "values" => [{
              "name" => "custom_date", 
              "value" => {
                "durationType" => "createdAt", 
                "startDate" => "2024-09-01", 
                "endDate" => "2024-09-30", 
                "filter" => "custom_date"
              }
            }],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 2,
            "filters" => [],
            "type" => "status"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "status" => [
            { "name" => "Closed", "value" => 20 },
            { "name" => "In Progress", "value" => 5 },
            { "name" => "Open", "value" => 17 }
          ],
          "widgetId" => 2
        }
      end

      it 'returns the correct insights data for status' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "source"' do
      let(:params) do
        {
          params: {
            "values" => [
              { "name" => "status", "value" => "In Progress", "with_survey" => false },
              { "name" => "agent", "value" => 79049 }
            ],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 3,
            "filters" => [],
            "type" => "source"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "source" => [
            { "name" => 0, "value" => 10 },
            { "name" => 1, "value" => 5 },
            { "name" => 2, "value" => 14 }
          ],
          "widgetId" => 3
        }
      end

      it 'returns the correct insights data for source' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "category"' do
      let(:params) do
        {
          params: {
            "values" => [],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 4,
            "filters" => [],
            "type" => "category"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "category" => [
            { "name" => "Facilities", "value" => 10 },
            { "name" => "Telecom", "value" => 15 }
          ],
          "widgetId" => 4
        }
      end

      it 'returns the correct insights data for category' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "ticketsFirstResponse"' do
      let(:params) do
        {
          params: {
            "values" => [
              { "name" => "group", "value" => 78594 },
              { "name" => "source", "value" => 0 }
            ],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 5,
            "filters" => [],
            "type" => "ticketsFirstResponse"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "ticketsFirstResponse" => [
            { "name" => "Responded", "value" => 2 },
            { "name" => "Unresponded", "value" => 2 }
          ],
          "widgetId" => 5
        }
      end

      it 'returns the correct insights data for ticketsFirstResponse' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "firstResponse"' do
      let(:params) do
        {
          params: {
            "values" => [
              { "name" => "category", "value" => "Facilities" }
            ],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 6,
            "filters" => [],
            "type" => "firstResponse"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "first_response" => [
            { "name" => "Achieved SLA", "value" => 10 },
            { "name" => "Missed SLA", "value" => 18 },
            { "name" => "First Response Due", "value" => 0 }
          ],
          "widgetId" => 6
        }
      end

      it 'returns the correct insights data for firstResponse' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "timeOpen"' do
      let(:params) do
        {
          params: {
            "values" => [
              { "name" => "priority", "value" => "medium" }
            ],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 7,
            "filters" => [],
            "type" => "timeOpen"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "timeOpen" => [
            { "name" => "Opened for 3 days", "value" => 2, "ticket_ids" => [] },
            { "name" => "Opened for 7 days", "value" => 0, "ticket_ids" => [] },
            { "name" => "Opened for 15 days", "value" => 1, "ticket_ids" => [] },
            { "name" => "Opened for 30 days", "value" => 3, "ticket_ids" => [] },
            { "name" => "Opened for 60 days", "value" => 6, "ticket_ids" => [] },
            { "name" => "Opened for 90 days or more", "value" => 0, "ticket_ids" => [] }
          ],
          "widgetId" => 7
        }
      end

      it 'returns the correct insights data for timeOpen' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "agent"' do
      let(:params) do
        {
          params: {
            "values" => [
              {
                "name" => "custom_date",
                "value" => {
                  "durationType" => "createdAt",
                  "startDate" => "2024-08-01",
                  "endDate" => "2024-08-10",
                  "filter" => "custom_date"
                }
              },
              { "name" => "source", "value" => 0 }
            ],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 8,
            "filters" => [],
            "type" => "agent"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "agent" => [
            { "value" => 2, "name" => "Admins", "id" => 78594 },
            { "value" => 1, "name" => "Help Desk Agents", "id" => 78596 },
            { "value" => 1, "name" => "Agent 1", "id" => 79046 },
            { "value" => 7, "name" => "Agent 2", "id" => 79049 }
          ],
          "widgetId" => 8
        }
      end

      it 'returns the correct insights data for agent' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "requester"' do
      let(:params) do
        {
          params: {
            "values" => [],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 9,
            "filters" => [],
            "type" => "requester"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "requester" => [
            { "value" => 1, "name" => "Admins", "id" => 78594 },
            { "value" => 1, "name" => "Help Desk Agents", "id" => 78596 },
            { "value" => 52, "name" => "Agent 1", "id" => 78597 },
            { "value" => 1, "name" => "Agent 2", "id" => 79047 },
            { "value" => 1, "name" => "Agent 3", "id" => 79048 },
            { "value" => 2, "name" => "Agent 4", "id" => 79049 },
            { "value" => 1, "name" => "Agent 5", "id" => 79060 }
          ],
          "widgetId" => 9
        }
      end

      it 'returns the correct insights data for requester' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "resolution"' do
      let(:params) do
        {
          params: {
            "values" => [
              { "name" => "status", "value" => "Open", "with_survey" => false },
              { "name" => "priority", "value" => "medium" },
              { "name" => "agent", "value" => 79060 }
            ],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 10,
            "filters" => [],
            "type" => "resolution"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "resolution" => [
            { "name" => "Achieved SLA", "value" => 14, "ticket_ids" => [] },
            { "name" => "Missed SLA", "value" => 12, "ticket_ids" => [] },
            { "name" => "Resolution Due", "value" => 1, "ticket_ids" => [] }
          ],
          "widgetId" => 10
        }
      end

      it 'returns the correct insights data for resolution' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "survey"' do
      let(:params) do
        {
          params: {
            "values" => [],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 11,
            "filters" => [],
            "type" => "survey"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "survey" => [
            { "name" => "Positive", "value" => 10, "ticket_ids" => nil },
            { "name" => "Negative", "value" => 13, "ticket_ids" => nil }
          ],
          "widgetId" => 11
        }
      end

      it 'returns the correct insights data for survey' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "ticketsResponseTime"' do
      let(:params) do
        {
          params: {
            "values" => [
              {
                "name" => "custom_date",
                "value" => {
                  "startDate" => "2024-08-01T00:00:00+05:00",
                  "endDate" => "2024-09-30T00:00:00+05:00",
                  "filter" => "custom_date",
                  "durationType" => "createdAt"
                }
              }
            ],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 12,
            "filters" => [],
            "type" => "ticketsResponseTime"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "ticketsResponseTime" => [
            { "name" => "Less than 15 minutes", "value" => 3, "ticket_ids" => [] },
            { "name" => "Between 15-60 minutes", "value" => 0, "ticket_ids" => [] },
            { "name" => "Between 1-4 hours", "value" => 2, "ticket_ids" => [] },
            { "name" => "Between 4-8 hours", "value" => 0, "ticket_ids" => [] },
            { "name" => "Between 8-24 hours", "value" => 2, "ticket_ids" => [] },
            { "name" => "Greater than 24 hours", "value" => 1, "ticket_ids" => [] }
          ],
          "widgetId" => 12
        }
      end

      it 'returns the correct insights data for ticketsResponseTime' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "responseTime"' do
      let(:params) do
        {
          params: {
            "values" => [],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 13,
            "filters" => [],
            "type" => "responseTime"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "responseTime" => [
            { "name" => "Less than 15 minutes", "value" => 3, "ticket_ids" => [] },
            { "name" => "Between 15-60 minutes", "value" => 0, "ticket_ids" => [] },
            { "name" => "Between 1-4 hours", "value" => 0, "ticket_ids" => [] },
            { "name" => "Between 4-8 hours", "value" => 3, "ticket_ids" => [] },
            { "name" => "Between 8-24 hours", "value" => 0, "ticket_ids" => [] },
            { "name" => "Greater than 24 hours", "value" => 8, "ticket_ids" => [] }
          ],
          "widgetId" => 13
        }
      end

      it 'returns the correct insights data for responseTime' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "ticketsAvgResponse"' do
      let(:params) do
        {
          params: {
            "values" => [
              { "name" => "priority", "value" => "low" },
              { "name" => "status", "value" => "Closed" }
            ],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 14,
            "filters" => [],
            "type" => "ticketsAvgResponse"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "ticketsAvgResponse" => [
            { "name" => "Less than 15 minutes", "value" => 0, "ticket_ids" => [], "count" => 1, "total_time" => 1000 },
            { "name" => "Between 15-60 minutes", "value" => 0, "ticket_ids" => [], "count" => 1, "total_time" => 1000 },
            { "name" => "Between 1-4 hours", "value" => 0, "ticket_ids" => [], "count" => 1, "total_time" => 1000 },
            { "name" => "Between 4-8 hours", "value" => 0, "ticket_ids" => [], "count" => 1, "total_time" => 1000 },
            { "name" => "Between 8-24 hours", "value" => 1000, "ticket_ids" => [], "count" => 1, "total_time" => 1000 },
            { "name" => "Greater than 24 hours", "value" => 0, "ticket_ids" => [], "count" => 1, "total_time" => 1000 }
          ],
          "widgetId" => 14
        }
      end

      it 'returns the correct insights data for ticketsAvgResponse' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "avgResponse" with no values provided' do
      let(:params) do
        {
          params: {
            "values" => [],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 15,
            "filters" => [],
            "type" => "avgResponse"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "avgResponse" => [
            { "name" => "Less than 15 minutes", "value" => 2, "ticket_ids" => [], "count" => 4, "total_time" => 100 },
            { "name" => "Between 15-60 minutes", "value" => 0, "ticket_ids" => [], "count" => 4, "total_time" => 100 },
            { "name" => "Between 1-4 hours", "value" => 95, "ticket_ids" => [], "count" => 4, "total_time" => 100 },
            { "name" => "Between 4-8 hours", "value" => 0, "ticket_ids" => [], "count" => 4, "total_time" => 100 },
            { "name" => "Between 8-24 hours", "value" => 0, "ticket_ids" => [], "count" => 4, "total_time" => 100 },
            { "name" => "Greater than 24 hours", "value" => 0, "ticket_ids" => [], "count" => 4, "total_time" => 100 }
          ],
          "widgetId" => 15
        }
      end

      it 'returns the correct insights data for avgResponse with no values in payload' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "timeSpent"' do
      let(:params) do
        {
          params: {
            "values" => [],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 16,
            "filters" => [],
            "type" => "timeSpent"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "time_spent" => [
            { "id" => 134392, "name" => "Agent 2", "hours" => 7, "minutes" => 330, "value" => 750, "total_users" => 2 },
            { "id" => 134468, "name" => "Agent 4", "hours" => 1, "minutes" => 0, "value" => 60 }
          ],
          "widgetId" => 16
        }
      end

      it 'returns the correct insights data for timeSpent' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "resolvedWithinSla"' do
      let(:params) do
        {
          params: {
            "values" => [{"name" => "agent", "value" => 79049}],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 17,
            "filters" => [],
            "type" => "resolvedWithinSla"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "resolvedWithinSla" => [
            { "name" => "Resolved Within SLA", "value" => 4, "ticket_ids" => [] },
            { "name" => "Not Resolved Within SLA", "value" => 8, "ticket_ids" => [] }
          ],
          "widgetId" => 17
        }
      end

      it 'returns the correct insights data for resolvedWithinSla' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "firstResponseDue"' do
      let(:params) do
        {
          params: {
            "values" => [],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 18,
            "filters" => [],
            "type" => "firstResponseDue"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "first_response_due" => [
            { "name" => "In 1 Hour", "value" => 3, "ticket_ids" => [] },
            { "name" => "In 4 Hours", "value" => 7, "ticket_ids" => [] }
          ],
          "widgetId" => 18
        }
      end

      it 'returns the correct insights data for firstResponseDue' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "ticketAge"' do
      let(:params) do
        {
          params: {
            "values" => [{"name" => "group", "value" => 78596}],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 19,
            "filters" => [],
            "type" => "ticketAge"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "ticketAge" => [
            { "name" => "Less than an hour", "value" => 3, "ticket_ids" => [] },
            { "name" => "Less than a day", "value" => 1, "ticket_ids" => [] },
            { "name" => "Less than a week", "value" => 14, "ticket_ids" => [] }
          ],
          "widgetId" => 19
        }
      end

      it 'returns the correct insights data for ticketAge' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "ticketsReopened" with no values provided' do
      let(:params) do
        {
          params: {
            "values" => [],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 23,
            "filters" => [],
            "type" => "ticketsReopened"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "ticketsReopened" => [
            { "name" => "In Last 5 days", "value" => 7, "ticket_ids" => [] },
            { "name" => "In Last 10 days", "value" => 0, "ticket_ids" => [] },
            { "name" => "In Last 30 days", "value" => 5, "ticket_ids" => [] }
          ],
          "widgetId" => 23
        }
      end

      it 'returns the correct insights data for ticketsReopened with no values in payload' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "timesReopened"' do
      let(:params) do
        {
          params: {
            "values" => [{"name" => "status", "value" => "Open", "with_survey" => false}],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 20,
            "filters" => [],
            "type" => "timesReopened"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "timesReopened" => [
            { "name" => "More than 3 times", "value" => 3, "ticket_ids" => [] },
            { "name" => "More than 5 times", "value" => 1, "ticket_ids" => [] },
            { "name" => "More than 10 times", "value" => 1, "ticket_ids" => [] }
          ],
          "widgetId" => 20
        }
      end

      it 'returns the correct insights data for timesReopened' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "ticketsCreated"' do
      let(:params) do
        {
          params: {
            "values" => [{"name" => "agent", "value" => 79060}],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 21,
            "filters" => [],
            "type" => "ticketsCreated"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "ticketsCreated" => [
            { "name" => "Yesterday", "value" => 5, "ticket_ids" => [] },
            { "name" => "Last 7 days", "value" => 21, "ticket_ids" => [] },
            { "name" => "Last 30 days", "value" => 1, "ticket_ids" => [] }
          ],
          "widgetId" => 21
        }
      end

      it 'returns the correct insights data for ticketsCreated' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "ticketsClosed"' do
      let(:params) do
        {
          params: {
            "values" => [
              {
                "name" => "custom_date",
                "value" => {
                  "durationType" => "createdAt",
                  "startDate" => "2024-06-01",
                  "endDate" => "2024-08-31",
                  "filter" => "custom_date"
                }
              }
            ],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 22,
            "filters" => [],
            "type" => "ticketsClosed"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "ticketsClosed" => [
            { "name" => "Yesterday", "value" => 3, "ticket_ids" => [] },
            { "name" => "Last 7 days", "value" => 2, "ticket_ids" => [] },
            { "name" => "Last 30 days", "value" => 4, "ticket_ids" => [] }
          ],
          "widgetId" => 22
        }
      end

      it 'returns the correct insights data for ticketsClosed' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "lastUpdated"' do
      let(:params) do
        {
          params: {
            "values" => [
              {
                "name" => "tag",
                "value" => "Infrastructure"
              }
            ],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 24,
            "filters" => [],
            "type" => "lastUpdated"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "lastUpdated" => [
            { "name" => "Today", "value" => 4, "ticket_ids" => [] },
            { "name" => "Yesterday", "value" => 0, "ticket_ids" => [] },
            { "name" => "Last 7 days", "value" => 12, "ticket_ids" => [] },
            { "name" => "Last 30 days", "value" => 1, "ticket_ids" => [] }
          ],
          "widgetId" => 24
        }
      end

      it 'returns the correct insights data for lastUpdated' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    context 'when payload has type "firstAssignTime"' do
      let(:params) do
        {
          params: {
            "values" => [
              {
                "name" => "custom_date",
                "value" => {
                  "startDate" => "2024-07-01T00:00:00+05:00",
                  "endDate" => "2024-09-25T00:00:00+05:00",
                  "filter" => "custom_date",
                  "durationType" => "createdAt"
                }
              },
              { "name" => "group", "value" => 78596 },
              { "name" => "status", "value" => "Open" },
              { "name" => "priority", "value" => "low" },
              { "name" => "agent", "value" => 79049 },
              { "name" => "source", "value" => 0 }
            ],
            "companyId" => company.id.to_s,
            "workspaceId" => workspace_id,
            "widgetId" => 25,
            "filters" => [],
            "type" => "firstAssignTime"
          }.to_json
        }
      end

      let(:expected_data) do
        {
          "firstAssignTime" => [
            { "name" => "Within 1 Hour", "value" => 9, "ticket_ids" => [] },
            { "name" => "Within 5 Hours", "value" => 0, "ticket_ids" => [] },
            { "name" => "Within 12 Hours", "value" => 10, "ticket_ids" => [] },
            { "name" => "Within 24 Hours", "value" => 0, "ticket_ids" => [] }
          ],
          "widgetId" => 25
        }
      end

      it 'returns the correct insights data for firstAssignTime' do
        get :index, params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(expected_data)
      end
    end

    
  end
end
