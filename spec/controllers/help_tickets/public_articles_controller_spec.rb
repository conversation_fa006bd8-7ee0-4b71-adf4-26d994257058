require 'rails_helper'
include CompanyUserHelper

RSpec.describe HelpTickets::PublicArticlesController, type: :controller do
  create_company_and_user

  before do
    default_setting = DefaultHelpdeskSetting.find_by(setting_type: 'allow_public_articles_to_logged_out_users')
    workspace2.helpdesk_settings.create!(default_helpdesk_setting: default_setting, enabled: true)
    workspace.helpdesk_settings.create!(default_helpdesk_setting: default_setting, enabled: true)
  end

  let(:workspace2) { FactoryBot.create(:workspace, company: company) }
  let!(:public_article1) { FactoryBot.create(:article, company: company, workspace_id: workspace.id, company_user_id: company_user.id, public: true, title: 'Public Article 1', category: 'DevOps') }
  let!(:public_article2) { FactoryBot.create(:article, company: company, workspace_id: workspace.id, company_user_id: company_user.id, public: true, title: 'Public Article 2', category: 'DevOps') }
  let!(:public_article3) { FactoryBot.create(:article, company: company, workspace_id: workspace.id, company_user_id: company_user.id, public: true, title: 'Public Article 3', category: 'DevOps') }
  let!(:public_article4) { FactoryBot.create(:article, company: company, workspace_id: workspace.id, company_user_id: company_user.id, public: true, title: 'Public Article 4', category: 'DevOps') }
  let!(:public_article5) { FactoryBot.create(:article, company: company, workspace_id: workspace.id, company_user_id: company_user.id, public: true, title: 'Public Article 5', category: 'DevOps') }
  let!(:public_article6) { FactoryBot.create(:article, company: company, workspace_id: workspace.id, company_user_id: company_user.id, public: true, title: 'Another Article', category: 'Different Category') }
  let!(:random_article1) { FactoryBot.create(:article, company: company, workspace_id: workspace.id, company_user_id: company_user.id, public: true, title: 'Search Test 1', category: 'DevOps') }
  let!(:random_article2) { FactoryBot.create(:article, company: company, workspace_id: workspace2.id, company_user_id: company_user.id, public: true, title: 'Search Test 2', category: 'DevOps') }
  let!(:private_article1) { FactoryBot.create(:article, company: company, workspace_id: workspace.id, company_user_id: company_user.id, public: false, title: 'Private Article 1', category: 'DevOps') }
  let!(:public_article_diff_workspace) {
    FactoryBot.create(:article, company: company, workspace_id: workspace2.id, company_user_id: company_user.id, public: true, title: 'Public Article 1 - Different Workspace')}

  describe "GET index" do
    it "returns articles which are public only" do
      get :index, params: { format: :json }
      articles = JSON.parse(response.body)['articles']
      expect(articles).to be_present
      expect(articles.count).to eq(7)
      articles.each do |article|
        expect(article['public']).to eq(true)
      end
    end

    it "returns recent 4 articles which are public only" do
      get :index, params: { format: :json, recent_articles: true }
      articles = JSON.parse(response.body)['articles']
      expect(articles.count).to be <= 4
      articles.each do |article|
        expect(article['public']).to eq(true)
      end
    end
  end

  describe "GET #show" do
    context "with valid params" do
      it "returns article which is public and of the same workspace" do
        params = { slug: public_article1.slug, workspace_id: workspace.id, format: :json }
        get :show, params: params
        expect(JSON.parse(response.body)['id']).to eq(public_article1.id)
      end

      it "returns a valid http status" do
        expect(response).to have_http_status(:ok)
      end
    end

    context "with invalid params" do
      it "returns article not found error as article is private" do
        params = { slug: private_article1.slug, workspace_id: workspace.id, format: :json }
        get :show, params: params
        expect(JSON.parse(response.body)).to eq({"message"=>"Article not found."})
      end

      it "returns article not found error as article is public but of different workspace" do
        params = { slug: public_article_diff_workspace.slug, workspace_id: workspace.id, format: :json }
        get :show, params: params
        expect(JSON.parse(response.body)).to eq({"message"=>"Article not found."})
      end
    end
  end

  describe "GET #index seach" do
    context "with search terms" do
      it "returns articles matching the search terms" do
        params = { search_terms: 'Public Article 1', format: :json }
        get :index, params: params
        articles = JSON.parse(response.body)['articles']
        expect(articles.count).to be <= 5
        expect(articles.first['title']).to eq('Public Article 1')
      end
    end

    context "with category filter" do
      it "returns articles in the specified category" do
        category = 'DevOps'
        params = { categories: [category], search_terms: 'Public Article 1', format: :json  }
        get :index, params: params
        articles = JSON.parse(response.body)['articles']
        expect(articles.count).to be <= 5
        expect(articles.first['title']).to eq('Public Article 1')
      end
    end

    context "with company wide search" do
      it "returns articles from whole company with relavant keywords" do
        params = { company_wide_search: true, search_terms: 'Search', format: :json  }
        get :index, params: params
        articles = JSON.parse(response.body)['articles']
        expect(articles.count).to be <= 5
        expect(articles.first['title']).to eq('Search Test 1')
        expect(articles.second['title']).to eq('Search Test 2')
      end
    end
  end
end
