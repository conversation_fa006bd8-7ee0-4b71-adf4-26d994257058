require 'rails_helper'
include CompanyUserHelper

RSpec.describe HelpTickets::InProgressTicketSummariesController, type: :controller do
  create_company_and_user

  let!(:user_2) { create(:user, first_name: "<PERSON>", last_name: "<PERSON><PERSON>", email: "<EMAIL>") }
  let!(:company_user_2) { create(:company_user, user: user_2, company: company) }

  let!(:custom_form) { create(:custom_form,
                              company_module: 'helpdesk',
                              company: company) }

  let!(:assigned_ticket) { create(:help_ticket,
                                  custom_form: custom_form,
                                  company: company) }
  let!(:unassigned_ticket) { create(:help_ticket,
                                    custom_form: custom_form,
                                    company: company) }

  let!(:subject) { create(:custom_form_field,
                          custom_form: custom_form,
                          field_attribute_type: CustomFormField.field_attribute_types[:text],
                          label: 'Subject',
                          name: 'subject') }

  let!(:status) { create(:custom_form_field,
                         custom_form: custom_form,
                         options: ['Open', 'In progress', 'Closed'],
                         field_attribute_type: CustomFormField.field_attribute_types[:status],
                         label: 'Status',
                         name: 'status') }

  let!(:staff_list_field) { create(:custom_form_field,
                                   custom_form: custom_form,
                                   field_attribute_type: CustomFormField.field_attribute_types[:people_list],
                                   label: 'Assigned To',
                                   name: 'assigned_to') }

  # Unassigned ticket values
  let!(:unassigned_subject_value) { create(:custom_form_value,
                                           custom_form_field: subject,
                                           value_str: 'Unassigned Ticket',
                                           custom_form: custom_form,
                                           module: unassigned_ticket,
                                           company: company) }

  let!(:unassigned_status_value) { create(:custom_form_value,
                                          custom_form_field: status,
                                          value_str: 'In Progress',
                                          custom_form: custom_form,
                                          module: unassigned_ticket,
                                          company: company) }

  # Assigned ticket values
  let!(:assigned_subject_value) { create(:custom_form_value,
                                         custom_form_field: subject,
                                         value_str: 'Assigned Ticket',
                                         custom_form: custom_form,
                                         module: assigned_ticket,
                                         company: company) }

  let!(:assigned_status_value) { create(:custom_form_value,
                                        custom_form_field: status,
                                        value_str: 'In Progress',
                                        custom_form: custom_form,
                                        module: assigned_ticket,
                                        company: company) }

  let!(:assigned_staff_value) { create(:custom_form_value,
                                       custom_form_field: staff_list_field,
                                       value_int: company_user.contributor_id,
                                       custom_form: custom_form,
                                       module: assigned_ticket,
                                       company: company) }

  let!(:parent_ticket) { create(:help_ticket,
                                custom_form: custom_form,
                                company: company) }

  let!(:child_ticket) { create(:help_ticket,
                               custom_form: custom_form,
                               company: company,
                               active_ticket_id: parent_ticket.id) }

  let!(:parent_status_value) { create(:custom_form_value,
                                      custom_form_field: status,
                                      value_str: 'In Progress',
                                      custom_form: custom_form,
                                      module: parent_ticket,
                                      company: company) }

  let!(:child_status_value) { create(:custom_form_value,
                                     custom_form_field: status,
                                     value_str: 'In Progress',
                                     custom_form: custom_form,
                                     module: child_ticket,
                                     company: company) }

  let!(:parent_staff_value) { create(:custom_form_value,
                                     custom_form_field: staff_list_field,
                                     value_int: company_user_2.contributor_id,
                                     custom_form: custom_form,
                                     module: parent_ticket,
                                     company: company) }

  let!(:child_staff_value) { create(:custom_form_value,
                                    custom_form_field: staff_list_field,
                                    value_int: company_user_2.contributor_id,
                                    custom_form: custom_form,
                                    module: child_ticket,
                                    company: company) }

  describe "Index" do
    it "test summary of open tickets" do
      response = get :index, format: :json
      results = JSON.parse(response.body)
      {"summaries"=>[{"count"=>1, "id"=>nil, "first_name"=>"Unassigned", "last_name"=>""},{"count"=>1, "id"=>1, "first_name"=>"Lucille", "last_name"=>"Bluth"}], "total"=>2}
      expect(results["summaries"].count).to eq(3)
      expect(results["total"]).to eq(3)
      expect(results["summaries"].first["count"]).to eq(1)
      expect(results["summaries"].second["count"]).to eq(1)
      expect(results["summaries"].find { |u| u["first_name"] == user.first_name }).to be_truthy
      expect(results["summaries"].find { |u| u["last_name"] == user.last_name }).to be_truthy
      expect(results["summaries"].find { |u| u["first_name"] == "Unassigned" }).to be_truthy
      expect(results["summaries"].find { |u| u["last_name"] == "" }).to be_truthy
    end

    it "test count of in progress tickets when merged tickets exist" do
      response = get :index, format: :json
      results = JSON.parse(response.body)
      user_tickets = results["summaries"].select { |res| res["first_name"] == "John" }
      expect(user_tickets.count).to eq(1)
    end
  end
end
