require 'rails_helper'
include CompanyUserHelper

RSpec.describe HelpTickets::ScheduledTasksController, type: :controller do
  create_company_and_user
  before do
    login_user
  end

  let(:updated_start_date) { (DateTime.current + 1.day).strftime('%Y-%m-%d') }
  let(:updated_end_date) { (DateTime.current + 2.days).strftime('%Y-%m-%d') }

  let(:workspace) { create(:workspace, company: company) }
  describe 'GET #index' do
  let!(:scheduled_tasks) do
    [
      create(:scheduled_task, name: "Task 1", workspace: workspace, assignee_id: company_user.id, recurring: false,
             task_started_at: { 'date' => updated_start_date, 'start_time' => '01:02', 'time_zone' => 'America/Chicago' },
             task_ended_at: { 'date' => updated_end_date, 'end_time' => '01:03', 'time_zone' => 'America/Chicago' }),
      create(:scheduled_task, name: "Task 2", workspace: workspace, assignee_id: company_user.id, 
             task_started_at: { 'date' => updated_start_date, 'start_time' => '01:02', 'time_zone' => 'America/Chicago' },
             task_ended_at: { 'date' => updated_end_date, 'end_time' => '01:03', 'time_zone' => 'America/Chicago' })
    ]
  end

  it 'returns a list of scheduled tasks with expected attributes' do
    get :index, params: { workspace_id: workspace.id, recurrence: { 'recurrencePattern' => { 'daily' => { 'recurrenceType' => "revision", 'revisionCount' => 1}}},
    notifications: [
     { 'notificationType' => "email", 'enabled' => false, 'value' => { 'recipients' => [], 'recipientEmails' => [] }},
     { 'notificationType' => "text",'enabled' => false,'value' => { 'recipients' => [] }}
     ]}, format: :json
    
    expect(response).to have_http_status(200)
    json_response = JSON.parse(response.body)
    expect(json_response['scheduled_tasks'].size).to eq(2)
    expect(json_response['scheduled_tasks'][0]['name']).to eq('Task 1')
    expect(json_response['scheduled_tasks'][1]['name']).to eq('Task 2')
  end
  end

  describe 'POST #create' do
    context 'with valid parameters' do
      let(:scheduled_task_params) do
        {
          scheduled_task: {
            id: 10,
            name: "Task 2",
            description: "Task 3 Description",
            assignee_id: company_user.id,
            task_started_at: {
              date: updated_start_date,
              start_time: "00:02",
              time_zone: "Asia/Karachi"
            },
            task_ended_at: {
              date: updated_end_date,
              end_time: "01:02",
              time_zone: "Asia/Karachi"
            },
            notifications: [
              {
                "notification_type": "email",
                "enabled": true,
                "value": {
                  "recipients": [
                    {
                      "id": company_user.id,
                      "name": "Fahad Saleem",
                      "email": "<EMAIL>",
                      "rootId": 6,
                      "type": "CompanyUser"
                    }
                  ]
                }
              }
            ]
          }
        }
      end
      
      it 'creates a new scheduled task' do
        post :create, params: scheduled_task_params.as_json, format: :json
        expect(response).to have_http_status(200)
        json_response = JSON.parse(response.body)
        expect(json_response['statuses'][0]['name']).to eq('Task 2')
      end
    end

    context 'with invalid parameters' do
      let(:scheduled_task_params) do
        {
          scheduled_task: {
            id: 10,
            description: "Task 2 Description",
            assignee_id: company_user.id,
            task_started_at: {
              date: updated_start_date,
              start_time: "00:02",
              time_zone: "Asia/Karachi"
            },
            task_ended_at: {
              date: updated_end_date,
              end_time: "01:02",
              time_zone: "Asia/Karachi"
            },
            notifications: [
              {
                notification_type: "email",
                enabled: false,
                value: {
                  recipients: [],
                  recipient_emails: [
                    {
                      "id": company_user.id,
                      "name": "Fahad Saleem",
                      "email": "<EMAIL>",
                      "rootId": 6,
                      "type": "CompanyUser"
                    }
                  ]
                }
              }
            ]
          }
        }
      end
      
      it 'does not create a new scheduled task' do
        post :create, params: scheduled_task_params.as_json, format: :json

        expect(response).to have_http_status(400)
        json_response = JSON.parse(response.body)
        expect(json_response['message']).to eq("Name can't be blank")
      end
    end
  end

  describe 'PATCH #update' do
  let!(:scheduled_task) do
    create(:scheduled_task, name: "Task 1", workspace: workspace, assignee_id: company_user.id, recurring: false,
             task_started_at: { 'date' => updated_start_date, 'start_time' => '01:02', 'time_zone' => 'America/Chicago' },
             task_ended_at: { 'date' => updated_end_date, 'end_time' => '01:03', 'time_zone' => 'America/Chicago' }
             )
  end
    context 'with valid parameters' do
      let(:valid_params) do
        {
          id: scheduled_task.id,
          scheduled_task: {
            name: "Updated Task",
            description: "Updated Description",
            task_started_at: { 'date' => updated_start_date, 'start_time' => "10:00", 'time_zone' => "America/Chicago" },
            task_ended_at: { 'date' => updated_end_date, 'end_time' => "18:00", 'time_zone' => "America/Chicago" },
            notifications: [
              {
                "notification_type": "email",
                "enabled": true,
                "value": {
                  "recipients": [
                    {
                      "id": company_user.id,
                      "name": "Fahad Saleem",
                      "email": "<EMAIL>",
                      "rootId": 6,
                      "type": "CompanyUser"
                    }
                  ]
                }
              }
            ]
          }
        }
      end

      it 'updates the scheduled task and returns updated attributes' do
        patch :update, params: valid_params.as_json, format: :json

        expect(response).to have_http_status(200)
        json_response = JSON.parse(response.body)

        expect(json_response['statuses'][0]['name']).to eq('Updated Task')
        expect(json_response['statuses'][0]['description']).to eq('Updated Description')
        expect(json_response['statuses'][0]['task_started_at']['date']).to eq(updated_start_date)
        expect(json_response['statuses'][0]['task_ended_at']['date']).to eq(updated_end_date)
      end
    end
  end

  describe 'DELETE #destroy' do
  let!(:scheduled_task) do
    create(:scheduled_task, name: "Task to Delete", workspace: workspace, assignee_id: company_user.id, 
             task_started_at: { 'date' => (DateTime.current + 1.day).strftime('%Y-%m-%d'), 'start_time' => '01:02', 'time_zone' => 'America/Chicago' },
             task_ended_at: { 'date' => (DateTime.current + 5.days).strftime('%Y-%m-%d'), 'end_time' => '01:03', 'time_zone' => 'America/Chicago' })
  end

  context 'when the scheduled task exists and is successfully destroyed' do
    it 'deletes the scheduled task and returns an empty response' do
      expect {
        delete :destroy, params: { id: scheduled_task.id, workspace_id: workspace.id }, format: :json
      }.to change(ScheduledTask, :count).by(-1)

      expect(response).to have_http_status(200)
      expect(response.body).to eq('{}')
    end
  end

  context 'when the scheduled task does not exist' do
      it 'returns a 400 status with an error message' do
      delete :destroy, params: { id: 9999, workspace_id: workspace.id }, format: :json
      json_response = JSON.parse(response.body)
      expect(json_response['message']).to eq("Scheduled task was not found.")
      end
  end
  end

end
