require 'rails_helper'
include CompanyUserHelper

describe HelpTickets::FaqsController, type: :controller do
  create_company_and_user

  describe "index" do
    let!(:faq1) { FactoryBot.create(:helpdesk_faq, company: company, workspace: workspace, order: 2) }
    let!(:faq2) { FactoryBot.create(:helpdesk_faq, company: company, workspace: workspace, order: 3) }
    let!(:faq3) { FactoryBot.create(:helpdesk_faq, company: company, workspace: workspace, order: 1) }

    before do
      params = { privilege_name: 'HelpTicket' }
      get :index, params: params, format: :json
    end

    it "orders faq's" do
      expect(response).to have_http_status(:ok)
    end

    it "returns faqs" do
      faqs = JSON.parse(response.body)['questions']
      expect(faqs).to be_present
      expect(faqs.length).to eq(3)
      expect(faqs[0]['id']).to eq(faq3.id)
      expect(faqs[0]['workspace_id']).to eq(faq3.workspace_id)
      expect(faqs[1]['id']).to eq(faq1.id)
      expect(faqs[1]['workspace_id']).to eq(faq1.workspace_id)
      expect(faqs[2]['id']).to eq(faq2.id)
      expect(faqs[2]['workspace_id']).to eq(faq2.workspace_id)
    end
  end

  describe "#create" do
    context "with valid params" do
      let(:helpdesk_faq_attributes) {
        {
          category_id: company.categories.find_by_name('Uncategorized').id,
          question_body: "How are you?",
          answer_body: "Good, thanks!",
        }
      }

      before do
        headers = { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
        params = { helpdesk_faq: helpdesk_faq_attributes, privilege_name: 'HelpTicket', format: :json }
        post :create, params: params
      end

      it "creates a new faq" do
        faq = HelpdeskFaq.where(workspace: workspace).first
        expect(faq).to be_present
        expect(faq.question_body).to eq("How are you?")
        expect(faq.answer_body).to eq("Good, thanks!")
      end

      it "returns a valid http status" do
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe "#update" do
    context "with valid params" do
      let(:helpdesk_faq) { FactoryBot.create(:helpdesk_faq, company: company, workspace: workspace) }
      let(:helpdesk_faq_attributes) {
        {
          id: helpdesk_faq.id,
          question_body: "How are you?",
          answer_body: "Good, thanks!",
        }
      }

      before do
        headers = { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
        params = { id: helpdesk_faq.id, helpdesk_faq: helpdesk_faq_attributes, privilege_name: 'HelpTicket', format: :json }
        put :update, params: params
      end

      it "updates the faq" do
        faq = HelpdeskFaq.find(helpdesk_faq.id)
        expect(faq).to be_present
        expect(faq.question_body).to eq("How are you?")
        expect(faq.answer_body).to eq("Good, thanks!")
      end

      it "returns a valid http status" do
        expect(response).to have_http_status(:ok)
      end

      it "returns faqs" do
        faqs = JSON.parse(response.body)['questions']
        expect(faqs).to be_present
        expect(faqs.length).to eq(1)
        expect(faqs[0]['id']).to eq(helpdesk_faq.id)
        expect(faqs[0]['question_body']).to eq("How are you?")
        expect(faqs[0]['answer_body']).to eq("Good, thanks!")
      end
    end

    context "updating only category ID" do
      let(:new_category) { FactoryBot.create(:category, name: 'Category 1', company: company) }
      let(:helpdesk_faq) { FactoryBot.create(:helpdesk_faq, company: company, workspace: workspace) }
      let(:helpdesk_faq_attributes_category_update) {
        {
          id: helpdesk_faq.id,
          category_id: new_category.id,
          only_category_change: true
        }
      }

      before do
        headers = { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
        params = { id: helpdesk_faq.id, helpdesk_faq: helpdesk_faq_attributes_category_update, privilege_name: 'HelpTicket', format: :json }
        put :update, params: params
      end

      it "updates only the category ID" do
        expect(response).to have_http_status(:ok)
        faq = JSON.parse(response.body)['questions'][0]
        expect(faq).to be_present
        expect(faq['category_id']).to eq(new_category.id)
        expect(faq['answer_body']).to eq(helpdesk_faq.answer_body)
        expect(faq['question_body']).to eq(helpdesk_faq.question_body)
      end
    end
  end

  describe "#destroy" do
    context "with valid params" do
      let(:helpdesk_faq) { FactoryBot.create(:helpdesk_faq, company: company, workspace: workspace) }

      before do
        headers = { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
        params = { id: helpdesk_faq.id, privilege_name: 'HelpTicket', format: :json }
        delete :destroy, params: params
      end

      it "destroys the faq" do
        faq = HelpdeskFaq.find_by(id: helpdesk_faq.id)
        expect(faq).to be_blank
      end

      it "returns a valid http status" do
        expect(response).to have_http_status(:ok)
      end
    end
  end
end