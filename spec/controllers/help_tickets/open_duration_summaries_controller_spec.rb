require 'rails_helper'
include CompanyUserHelper

RSpec.describe HelpTickets::OpenDurationSummariesController, type: :controller do
  create_company_and_user

  let!(:custom_form) { create(:custom_form,
                              company_module: 'helpdesk',
                              company: company) }

  let!(:ticket) { create(:help_ticket,
                         custom_form: custom_form,
                         company: company) }

  let!(:subject) { create(:custom_form_field,
                          custom_form: custom_form,
                          field_attribute_type: CustomFormField.field_attribute_types[:text],
                          label: 'Subject',
                          name: 'subject') }

  let!(:status) { create(:custom_form_field,
                         custom_form: custom_form,
                         options: ['Open', 'In progress', 'Closed'],
                         field_attribute_type: CustomFormField.field_attribute_types[:status],
                         label: 'Status',
                         name: 'status') }

  let!(:subject_value) { create(:custom_form_value,
                                custom_form_field: subject,
                                value_str: 'Ticket subject',
                                custom_form: custom_form,
                                module: ticket,
                                company: company) }

  let!(:status_value) { create(:custom_form_value,
                               custom_form_field: status,
                               value_str: 'Open',
                               custom_form: custom_form,
                               module: ticket,
                               company: company) }

  describe "Index" do
    it "test summary of tickets closed duration" do
      response = get :index, format: :json
      results = JSON.parse(response.body)
      expect(results.count).to eq(1)
      expect(results.first['subject']).to eq("Ticket subject")
      expect(results.first['duration']).to be > 0
      expect(results.first['duration']).to be < 1
    end
  end
end
