require 'rails_helper'
include CompanyUserHelper

describe HelpTickets::PublicFaqsController, type: :controller do
  create_company_and_user

  describe "index with setting enabled" do
    before do
      default_setting = DefaultHelpdeskSetting.find_by(setting_type: 'allow_faq_page_to_logged_out_users')
      workspace.helpdesk_settings.create!(default_helpdesk_setting: default_setting, enabled: true)
    end

    let!(:public_faq1) { FactoryBot.create(:helpdesk_faq, company: company, workspace: workspace, public: true) }
    let!(:public_faq2) { FactoryBot.create(:helpdesk_faq, company: company, workspace: workspace, public: true) }
    let!(:private_faq1) { FactoryBot.create(:helpdesk_faq, company: company, workspace: workspace, public: false) }

    it "returns public faqs only" do
      get :index, format: :json

      faqs = JSON.parse(response.body)['questions']
      expect(faqs).to be_present
      expect(faqs.length).to eq(2)
      expect(faqs[0]['id']).to eq(public_faq1.id)
      expect(faqs[0]['workspace_id']).to eq(public_faq1.workspace_id)
      expect(faqs[1]['id']).to eq(public_faq2.id)
      expect(faqs[1]['workspace_id']).to eq(public_faq2.workspace_id)
    end
  end

  describe "index with setting disabled" do
    before do
      default_setting = DefaultHelpdeskSetting.find_by(setting_type: 'allow_faq_page_to_logged_out_users')
      workspace.helpdesk_settings.create!(default_helpdesk_setting: default_setting, enabled: false)
    end

    let!(:public_faq1) { FactoryBot.create(:helpdesk_faq, company: company, workspace: workspace, public: true) }
    let!(:public_faq2) { FactoryBot.create(:helpdesk_faq, company: company, workspace: workspace, public: true) }
    let!(:private_faq1) { FactoryBot.create(:helpdesk_faq, company: company, workspace: workspace, public: false) }

    it "returns no faqs" do
      get :index, format: :json

      faqs = JSON.parse(response.body)['questions']
      expect(faqs).to be_empty
    end
  end
end
