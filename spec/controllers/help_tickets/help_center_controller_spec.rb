require 'rails_helper'
include CompanyUserHelper

describe HelpTickets::HelpCenterController, type: :controller do
  context "with an expired company" do
    create_company_and_user
    let!(:date_company) { company.update_column(:created_at, 50.days.ago) }

    describe "#index" do
      xit "returns url json" do
        req_email = "helpdesk@#{@request.host}"
        response = get :index, format: :json
        response_hash = JSON.parse(response.body)

        expect(response.status).to eq(200)
        expect(response_hash['request_email']).to eq(req_email)

        @request.host = "#{company.subdomain}.#{Rails.application.credentials.root_domain}"
        response = get :index, format: :html
        expect(response).to render_template("help_tickets/help_center/index")
      end
    end
  end

  context "with a valid company" do
    create_company_and_user

    describe "#index" do
      it "returns url json" do
        req_email = "helpdesk@#{@request.host}"
        response = get :index, format: :json
        response_hash = JSON.parse(response.body)

        expect(response.status).to eq(200)
        expect(response_hash['request_email']).to eq(req_email)

        response = get :index, format: :html
        expect(response).to render_template("index")
      end
    end
  end
end
