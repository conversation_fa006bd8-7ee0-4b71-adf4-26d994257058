require 'rails_helper'
include CompanyUserHelper

describe HelpTickets::OrderedFaqsController, type: :controller do
  create_company_and_user

  describe "#create" do
    let(:faq1) { FactoryBot.create(:helpdesk_faq, company: company) }
    let(:faq2) { FactoryBot.create(:helpdesk_faq, company: company) }
    let(:faq3) { FactoryBot.create(:helpdesk_faq, company: company) }

    context "with valid params" do
      before(:each) do
        headers = { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
        params = { faqs: [faq3.id, faq1.id, faq2.id], format: :json }
        post :create, params: params
      end

      it "orders faq's" do
        expect(response).to have_http_status(:ok)
      end

      it "orders faq's" do
        faqs = HelpdeskFaq.where(company: company).order(:order)
        expect(faqs).to be_present
        expect(faqs.length).to eq(3)
        expect(faqs[0].id).to eq(faq3.id)
        expect(faqs[1].id).to eq(faq1.id)
        expect(faqs[2].id).to eq(faq2.id)
      end

      it "returns faqs" do
        faqs = JSON.parse(response.body)['questions']
        expect(faqs).to be_present
        expect(faqs.length).to eq(3)
        expect(faqs[0]["id"]).to eq(faq3.id)
        expect(faqs[1]["id"]).to eq(faq1.id)
        expect(faqs[2]["id"]).to eq(faq2.id)
      end
    end
  end
end
