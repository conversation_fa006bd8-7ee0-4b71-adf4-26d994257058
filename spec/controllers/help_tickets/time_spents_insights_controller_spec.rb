require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

describe HelpTickets::TimeSpentsInsightsController, type: :controller do
  create_company_and_user

  let(:help_ticket) {
    ticket_params = {
      'Subject' => "First one",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      company: company
    }
    create_ticket(ticket_params)
  }
  let(:help_ticket2) {
    ticket_params = {
      'Subject' => "Second one",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Closed',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      company: company
    }
    create_ticket(ticket_params)
  }

  let!(:company_user_2) { FactoryBot.create(:company_user) }
  let!(:privilege) { Privilege.create(company_user: company_user_2, name: 'HelpTicket', permission_type: 'write') }
  let!(:privilege) { Privilege.create(company_user: company_user, name: 'HelpTicket', permission_type: 'write') }
  let!(:time_spent) { FactoryBot.create(:time_spent, company_user: company_user, help_ticket: help_ticket) }
  let!(:time_spent2) { FactoryBot.create(:time_spent, company_user: company_user, help_ticket: help_ticket) }
  let!(:time_spent3) { FactoryBot.create(:time_spent, company_user: company_user_2, help_ticket: help_ticket2) }

  describe "#insights_data" do
    context "within the context of help tickets" do
      before do
        help_ticket2.update_column(:archived, true)
        get :insights_data
      end

      it "should return a 200 http status" do
        expect(response).to have_http_status(:ok)
      end

      it "should return the unarchived time spents" do
        data = JSON.parse(response.body)['time_spents']

        expect(data).to be_present
        expect(data.length).to eq(2)
        expect(data.pluck("id")).to include(time_spent.id)
        expect(data.pluck("id")).to include(time_spent2.id)
      end
    end
  end
end
