require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

describe HelpTickets::TimeSpentsController, type: :controller do
  create_company_and_user

  let(:help_ticket) {
    ticket_params = {
      'Subject' => "First one",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      company: company
    }
    create_ticket(ticket_params)
  }

  let(:time_spent_attributes) do
    {
      hours_spent: "10",
      minutes_spent: "30",
      started_at: 2.days.ago,
    }
  end

  describe "#index" do
    let!(:privilege) { Privilege.create(company_user: company_user, name: 'HelpTicket', permission_type: 'write')}
    let!(:time_spent) { FactoryBot.create(:time_spent, company_user: company_user, help_ticket: help_ticket) }
    let!(:time_spent2) { FactoryBot.create(:time_spent, company_user: company_user, help_ticket: help_ticket) }

    let!(:company_user_2) { FactoryBot.create(:company_user) }
    let!(:privilege) { Privilege.create(company_user: company_user_2, name: 'HelpTicket', permission_type: 'write')}
    let!(:time_spent3) { FactoryBot.create(:time_spent, company_user: company_user_2, help_ticket: help_ticket) }

    context "within the context of a help ticket" do
      before do
        company_user_2.destroy
        get :index, params: { ticket_id: help_ticket.id, format: :json }
      end

      it "should return a 200 http status" do
        expect(response).to have_http_status(:ok)
      end

      it "should return the time spent" do
        data = JSON.parse(response.body)
        expect(data).to be_present
        expect(data.length).to eq(2)
        expect(data.pluck("id")).to include(time_spent.id)
        expect(data.pluck("id")).to include(time_spent2.id)
        expect(data.pluck('hours_spent')).to include(time_spent.hours_spent)
        expect(data.pluck('hours_spent')).to include(time_spent2.hours_spent)
        expect(data.pluck('minutes_spent')).to include(time_spent.minutes_spent)
        expect(data.pluck('minutes_spent')).to include(time_spent2.minutes_spent)
        expect(data.pluck('user_name')).to include(company_user.user.full_name)
        started_at = data.pluck('started_at').map{ |time| time.to_date}
        expect(started_at).to include(time_spent.started_at.to_date)
        expect(started_at).to include(time_spent2.started_at.to_date)
      end
    end
  end

  describe "#create" do
    context "with valid params" do
      before(:each) do
        headers = { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
        time_spent_attributes[:company_user_id] = company_user.id
        params = { ticket_id: help_ticket.id, time_spent: time_spent_attributes, format: :json }
        post :create, params: params
      end

      let(:time_spent) {
        ticket = HelpTicket.find_by(id: help_ticket.id)
        time_spents = ticket.time_spents
        expect(time_spents).to be_present
        expect(time_spents.length).to eq(1)
        time_spents.first
      }

      it "should return a 200 http status" do
        expect(response).to have_http_status(:ok)
      end

      it "creates a new time spent" do
        expect(time_spent.hours_spent).to eq(10)
        expect(time_spent.minutes_spent).to eq(30)
        expect(time_spent.started_at.to_date).to eq(time_spent_attributes[:started_at].to_date)
      end

      it "implicitly sets it to be the current user" do
        expect(time_spent.company_user_id).to eq(company_user.id)
      end
    end
  end

  describe "#update" do
    let(:time_spent) { FactoryBot.create(:time_spent, company_user: company_user, help_ticket: help_ticket) }

    context "with valid params" do
      before(:each) do
        headers = { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
        params = {
                   ticket_id: help_ticket.id,
                   id: time_spent.id,
                   time_spent: time_spent_attributes,
                   format: :json
                 }
        put :update, params: params
      end

      it "returns a 200 http response" do
        expect(response).to have_http_status(:ok)
      end

      it "updates the existing ticket" do
        spent = TimeSpent.find_by(id: time_spent.id)
        expect(spent).to be_present
        expect(spent.hours_spent).to be_present
      end
    end
  end

  describe "#destroy" do
    let(:time_spent) { FactoryBot.create(:time_spent, company_user: company_user, help_ticket: help_ticket) }

    context "with valid params" do
      before(:each) do
        headers = { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
        params = {
                   ticket_id: help_ticket.id,
                   id: time_spent.id,
                   format: :json
                 }
        delete :destroy, params: params
      end

      it "returns a 200 http response" do
        expect(response).to have_http_status(:ok)
      end

      it "destroys the existing time entry" do
        spent = TimeSpent.find_by(id: time_spent.id)
        expect(spent).to be_blank
      end
    end
  end
end
