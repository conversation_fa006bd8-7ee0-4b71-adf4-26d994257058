require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

RSpec.describe HelpTickets::MergedTicketsController, type: :controller do
  create_company_and_user

  let(:workspace_id) { company.default_workspace.id }
  let!(:company_user2) { create(:company_user, company: company_user.company) }
  let!(:help_ticket_1) {
    ticket_params = {
      'Subject' => "First",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      workspace_id: workspace_id
    }
    create_ticket(ticket_params)
  }
  let!(:help_ticket_2) {
    ticket_params = {
      'Subject' => "Second",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      workspace_id: workspace_id
    }
    ticket = create_ticket(ticket_params)
  }
  let!(:help_ticket_3) {
    ticket_params = {
      'Subject' => "Third",
      'Created By' => company_user2.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id, company_user2.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      workspace_id: workspace_id
    }
    ticket = create_ticket(ticket_params)
  }

  describe "#merge_tickets" do
    context "with valid params" do
      before(:each) do
        headers = { CONTENT_TYPE: 'application/json', ACCEPT: 'application/json' }
        post :create, params:  { parent_ticket: help_ticket_1.id, add_comment_to_parent: true, add_description_to_parent: true, child_tickets: [help_ticket_2.id], company_id: company.id }
      end

      it "create merge ticket" do
        expect(response).to have_http_status(:ok)
        help_ticket_2.reload
        expect(help_ticket_1.merged_help_tickets.count).to eq(1)
        expect(help_ticket_2.active_ticket_id).to eq(help_ticket_1.id)
        expect(help_ticket_1.help_ticket_activities.find_by_activity_type('merge_ticket')).to be_present
        expect(help_ticket_2.help_ticket_comments.count).to eq(1)
        expect(help_ticket_1.status).to eq("Open")
        expect(help_ticket_2.status).to eq("Closed")
      end
    end
  end

  describe "#create" do
    context "with valid params" do
      before(:each) do
        headers = { CONTENT_TYPE: 'application/json', ACCEPT: 'application/json' }
        post :create, params:  { parent_ticket: help_ticket_1.id, add_comment_to_parent: true, add_description_to_parent: true, child_tickets: [ help_ticket_2.id, help_ticket_3.id ], company_id: company.id }
      end

      it "will add child ticket creator" do
        expect(response).to have_http_status(:ok)
        help_ticket_2.reload
        help_ticket_3.reload

        expect(help_ticket_1.merged_help_tickets.count).to eq(2)
        expect(help_ticket_2.active_ticket_id).to eq(help_ticket_1.id)
        expect(help_ticket_3.active_ticket_id).to eq(help_ticket_1.id)
        expect(help_ticket_1.help_ticket_activities.find_by_activity_type('merge_ticket')).to be_present
      end
    end
  end
end
