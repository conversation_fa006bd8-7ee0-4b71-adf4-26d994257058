require 'rails_helper'
include CompanyUserHelper
include LoginHelper
include HelpTicketHelper

describe HelpTickets::TicketsController, type: :controller do
  create_company_and_user

  let(:company_user) { company.company_users.first }
  let!(:company_user2) { create(:company_user, company: company_user.company) }
  let(:asset) { FactoryBot.create(:managed_asset, company: company) }
  let(:contract) { FactoryBot.create(:contract, name: 'contract1', company: company) }
  let(:vendor) { FactoryBot.create(:vendor, company_id: company.id, category_id: company.categories.first.id, name: "Microsoft", url: "www.microsoft.com") }

  let(:author) { user.company_users.first }

  let(:help_ticket_attributes) do
    {
      creator_id: author.id,
      subject: "Test Ticket",
      email: user.email,
      help_paragraph: "Help Me",
      company_id: company.id,
      priority: "low",
      links_array: {
        "0" => { "id" => asset.id, "linkable_id" => asset.linkable_id, "name" => asset.name, "type" => "ManagedAsset" },
        "1" => { "id" => contract.id, "linkable_id" => contract.linkable_id, "name" => contract.name, "type" => "Vendor" },
        "2" => { "id" => vendor.id, "linkable_id" => vendor.linkable_id, "name" => vendor.name, "type" => "Contract" }
      }
    }
  end

  let(:help_ticket_attributes_with_agent) do
    {
      creator_id: author.id,
      subject: "Test Ticket 2",
      email: user.email,
      help_paragraph: "Help Me",
      company_id: company.id,
      priority: "low"
    }
  end

  describe "#show" do
    context "Show Help Ticket" do
      let!(:help_ticket) {
        ticket_params = {
          'Subject' => "Boom boom",
          'Created By' => company_user.contributor_id,
          'Assigned To' => [company_user.contributor_id],
          'Followers' => [company_user.contributor_id],
          'Impacted Devices' => [asset.id],
          'Status' => 'Open',
          'Priority' => "medium",
          'Description' => "The red robin sits in the tree.",
          company: company
        }
        create_ticket(ticket_params)
      }

      it "will return success response" do
        get :show, params: {
          "id" => help_ticket.id,
          "format"=>"json",
        }
        expect(JSON.parse(response.body)["assigned_user_ids"]).to include(company_user.contributor_id)
        expect(response).to have_http_status(:ok)
      end

      it "checks if the first response is received when the ticket is closed" do
        help_ticket.create_scheduled_slas

        status_field = help_ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: "status" })
        status_field.update(value_str: "Closed")

        help_ticket.update(status: "Closed")
    
        get :show, params: { id: help_ticket.id, format: "json" }
    
        response_body = JSON.parse(response.body)
    
        expect(response_body["first_response_received"]).to eq(true)
        expect(response).to have_http_status(:ok)
      end

      it "Form value delete with deleting asset" do
        cfv_count = help_ticket.custom_form_values.count
        id = asset.id
        asset.destroy
        Sidekiq::Testing.inline! do
          UpdateCustomFormValuesWorker.perform_async(id, ManagedAsset.new.field_type)
        end
        expect(response.status).to eq(200)
      end

      it "Form value delete with deleting contributor" do
        id = company_user.contributor_id
        company_user.contributor.destroy
        Sidekiq::Testing.inline! do
          UpdateCustomFormValuesWorker.perform_async(id, Contributor.new.field_type)
        end
        expect(response.status).to eq(200)
      end
    end
  end

  describe "#show" do
    context "Show Help Ticket" do
      let!(:help_ticket) {
        ticket_params = {
          'Subject' => "Boom boom",
          'Created By' => "<EMAIL>",
          'Assigned To' => [company_user.contributor_id],
          'Followers' => [company_user.contributor_id],
          'Impacted Devices' => [asset.id],
          'Status' => 'Open',
          'Priority' => "medium",
          'Description' => "The red robin sits in the tree.",
          company: company
        }
        create_ticket(ticket_params)
      }

      it "will return success response" do
        get :show, params: {
          "id" => help_ticket.id,
          "format"=>"json",
        }
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe "#show" do
    context "Show Help Ticket with Radio Button" do
      let(:custom_form) { company.custom_forms.helpdesk.first }
      let!(:radio_button) {
        radio_button = CustomFormField.new(label: "Radio Button", 
                                           field_attribute_type: "radio_button", 
                                           order_position: 10, 
                                           options: "[\"First\",\"Second\",\"Third\"]", 
                                           required: false, 
                                           note: nil, 
                                           default_value: "First", 
                                           private: false, 
                                           permit_view_default: true, 
                                           permit_edit_default: true, 
                                           name: "radio_button", 
                                           custom_form_id: custom_form.id
                                          )
        radio_button.field_position = FieldPosition.new(custom_form_field_id: radio_button.id, position: "right")
        custom_form.custom_form_fields << radio_button
        radio_button
      }
      let!(:checkbox) {
        checkbox = CustomFormField.new(label: "Checkbox", 
                                       field_attribute_type: "checkbox", 
                                       order_position: 11, 
                                       options: "[\"Desktop\",\"Laptop\",\"Router\"]", 
                                       required: false, 
                                       private: false, 
                                       permit_view_default: true, 
                                       permit_edit_default: true, 
                                       name: "checkbox", 
                                       custom_form_id: custom_form.id
                                      )
        checkbox.field_position = FieldPosition.new(custom_form_field_id: checkbox.id, position: "right")
        custom_form.custom_form_fields << checkbox
        checkbox
      }
      let!(:email_address) {
        email_address = CustomFormField.new(label: "Email Address", 
                                            field_attribute_type: "email", 
                                            order_position: 12, 
                                            options: nil, 
                                            required: false, 
                                            private: false, 
                                            permit_view_default: true, 
                                            permit_edit_default: true, 
                                            name: "email_address", 
                                            custom_form_id: custom_form.id
                                          )
        email_address.field_position = FieldPosition.new(custom_form_field_id: email_address.id, position: "right")
        custom_form.custom_form_fields << email_address
        email_address
      }
      let!(:help_ticket) {
        ticket_params = {
          'Subject' => "Boom boom",
          'Created By' => "<EMAIL>",
          'Assigned To' => [company_user.contributor_id],
          'Followers' => [company_user.contributor_id],
          'Impacted Devices' => [asset.id],
          'Status' => 'Open',
          'Priority' => "medium",
          'Description' => "The red robin sits in the tree.",
          'Radio Button' => "First",
          'Checkbox' => "Desktop",
          'Email Address' => "<EMAIL>"
        }
        create_ticket(ticket_params)
      }

      it "will return success response" do
        get :show, params: {
          "id" => help_ticket.id,
          "format"=>"json",
        }
        expect(response).to have_http_status(:ok)
      end

      it "will check radio button field" do
        value_str = help_ticket.custom_form_values.find_by(value_str: "First").value_str
        expect(value_str).to eq("First")
        expect(JSON.parse(help_ticket.custom_form_values.find_by(value_str: "First").custom_form_field.options).count).to eq(3)
        expect(help_ticket.custom_form_values.find_by(value_str: "First").custom_form_field.default_value).to eq("First")
      end

      it "will check checkbox field" do
        value_str = help_ticket.custom_form_values.find_by(custom_form_field_id: checkbox.id).value_str
        expect(value_str).to eq("Desktop")
      end

      it "will check email address field" do
        value_str = help_ticket.custom_form_values.find_by(custom_form_field_id: email_address.id).value_str
        expect(value_str).to eq("<EMAIL>")
      end
    end
  end
  describe "#notification_status" do
    context "#update ticket" do
      let!(:help_ticket) {
        ticket_params = {
          'Subject' => "Boom boom",
          'Created By' => company_user.contributor_id,
          'Assigned To' => [company_user.contributor_id],
          'Followers' => [company_user.contributor_id],
          'Impacted Devices' => [asset.id],
          'Status' => 'Open',
          'Priority' => "medium",
          'Description' => "The red robin sits in the tree.",
          mute_notification: false,
          company: company
        }
        create_ticket(ticket_params)
      }

      it "will check if status is updated" do
        help_ticket.mute_notification = !help_ticket.mute_notification
        put :update_notification_status, params: {id: help_ticket.id, ticket: help_ticket.as_json}, format: :json
        ht = HelpTicket.find(help_ticket.id)
        expect(ht.mute_notification).to eq(true)
      end

      it "will check if ticket is_seen has truthy value then negate it" do
        put :update_ticket_seen_status, params: { id: help_ticket.id, ticket: help_ticket.as_json } , format: :json
        help_ticket.reload
        expect(help_ticket.is_seen).to eq(true)
      end
    end

    context "#create ticket" do 
      let(:ticket_params) {
        {
          'Subject' => "Mute notification 01",
          'Created By' => company_user.contributor_id,
          'Assigned To' => [company_user.contributor_id],
          'Followers' => [company_user.contributor_id],
          'Status' => 'Open',
          'Priority' => "low",
          'Description' => "When mute notification setting is on",
          mute_notification: true,
          company: company
        }
      }

      let(:ticket_params_02) {
        {
          'Subject' => "Mute notification 02",
          'Created By' => company_user.contributor_id,
          'Assigned To' => [company_user.contributor_id],
          'Followers' => [company_user.contributor_id],
          'Status' => 'Open',
          'Priority' => "low",
          'Description' => "When mute notification setting is off",
          mute_notification: false,
          company: company
        }
      }

      it "will check if status is true for ticket with mute notification setting is on" do
        ticket = create_ticket(ticket_params)
        expect(ticket.mute_notification).to eq(true)
      end

      it "will check if status is false for ticket with mute notification setting is off" do
        ticket = create_ticket(ticket_params_02)
        expect(ticket.mute_notification).to eq(false)
      end
    end
  end

  describe '#new' do
    context 'user without module permissions' do
      before do
        login_user(user_one.user)
        get :new, format: :json
      end

      let(:user_one) { create(:company_user, company: company) }

      it 'will not allow user to access the page' do
        expect(response.status).to eq(401)
        expect(JSON.parse(response.body)['message']).to eq("Sorry, you're not authorized to perform this action.")
      end
    end

    context 'user with module permissions' do
      before do
        login_user(company_user.user)
        get :new, format: :html
      end

      it 'will allow user to access the page' do
        expect(response.status).to eq(200)
      end
    end
  end

  describe "#create schedule sla" do
    let!(:sla_policy) { FactoryBot.create(:sla_policy, company: company, custom_form: company.custom_forms.helpdesk.first, name: 'SLA Policy1') }
    let!(:sla_detail) { FactoryBot.create(:sla_detail, sla_policy_id: sla_policy.id, priority: "medium") }
    let!(:sla_condition) { FactoryBot.create(:sla_condition, sla_policy_id: sla_policy.id, field_type: "assigned_to", value: {"form_value"=>[company_user.contributor_id]}.to_json) }

    before do
      sla_policy.emails.create!(
        email_type: "first_response_target_reminder",
        subject: "sla_policy",
        target: ["assigned"],
        email_body: "<div><!--block-->sla_policy</div>",
        approaches_in: "00:05",
        name: 'Sla reminder'
      )
    end

    context "with having policy conditions" do
      let!(:ticket_params) {
        {
          'Subject' => "Boom boom",
          'Created By' => "<EMAIL>",
          'Assigned To' => [company_user.contributor_id],
          'Followers' => [company_user.contributor_id],
          'Impacted Devices' => [asset.id],
          'Status' => 'Open',
          'Priority' => "medium",
          'Description' => "The red robin sits in the tree.",
          company: company,
          custom_form: company.custom_forms.helpdesk.first,
        }
      }
      it "will retune sla_response and create scheduled_slas" do
        help_ticket = create_ticket(ticket_params)
        help_ticket.create_scheduled_slas
        scheduled_sla = help_ticket.scheduled_slas.find_by(sla_detail_id: sla_detail.id)
        expect(help_ticket.sla_response).to be_present
        expect(scheduled_sla).to be_present
      end
    end
  end
end
