require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

describe HelpTickets::ProjectTasksController, type: :controller do
  create_company_and_user

  let!(:help_ticket) {
    ticket_params = {
      'Subject' => "Subject",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      company: company
    }
    create_ticket(ticket_params)
  }

  describe "#destroy" do
    context "with a bad id" do
      it "will return a 404" do
        delete :destroy, params: {id: 'THISISNOTANID', ticket_id: help_ticket.id}
        expect(response).to have_http_status(:not_found)
      end
    end

    context "with a good id" do
      let(:project_task) { create(:project_task, company: company, help_ticket_id: help_ticket.id) }
      let!(:subject) { delete :destroy, params: { id: project_task.id, ticket_id: help_ticket.id } }

      it "will remove the project task" do
        expect(ProjectTask.where(id: project_task.id)).to be_blank
      end

      it "will confirm that delete task activity is created" do
        deleted_task_activity = HelpTicketActivity.find_by(help_ticket_id: project_task.help_ticket_id)
        expect(deleted_task_activity.help_ticket_id).to eq(project_task.help_ticket_id)
      end

      it "will return a 200" do
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe "#update" do
    let(:help_ticket) {
      ticket_params = {
        'Subject' => "Subject",
        'Created By' => company_user.contributor_id,
        'Assigned To' => [company_user.contributor_id],
        'Followers' => [company_user.contributor_id],
        'Impacted Devices' => [],
        'Status' => 'Open',
        'Priority' => "medium",
        'Description' => "The red robin sits in the tree.",
        company: company
      }
      create_ticket(ticket_params)
    }
    let(:project_task) { create(:project_task, company: company, help_ticket_id: help_ticket.id, task_assignees: [TaskAssignee.new(contributor: company_user.contributor)]) }
    let(:project_task_params) {
      {
        id: project_task.id,
        description: 'New Name',
        assignees: [],
      }
    }
    let!(:subject) { put :update, params: { id: project_task.id, project_task: project_task_params, ticket_id: help_ticket.id } }

    context "with good params" do
      it "will update project_task's attributes" do
        updated_task = ProjectTask.find(project_task.id)
        expect(updated_task.description).to eq("New Name")
        expect(updated_task.help_ticket.help_ticket_activities.find_by_activity_type('task_description')).to be_present
      end
      it "will create a activity" do
        updated_task = ProjectTask.find(project_task.id)
        expect(updated_task.help_ticket.help_ticket_activities.find_by_activity_type('task_description')).to be_present
      end
    end
  end

  describe "#create" do
    let!(:subject) { post :create, params: { project_task: project_task_params, ticket_id: help_ticket.id } }
    let!(:company_user1) { create(:company_user, company: company) }
    let!(:company_user2) { create(:company_user, company: company) }

    context "with assignees" do
      let(:new_project_task) {  ProjectTask.find_by(description: 'New Name') }
      let(:project_task_params) do
        {
          description: 'New Name',
          assignees: [
            {
              name: company_user1.full_name,
              id: company_user1.contributor_id,
            },
            {
              name: company_user2.full_name,
              id: company_user2.contributor_id,
            }
          ]
        }
      end

      it "will create a new project task" do
        expect(response).to have_http_status(:ok)
        expect(new_project_task).to be_present
      end

      it "will create company users for project task" do
        expect(new_project_task.task_assignees.count).to eq(2)
      end

      it "will create a activity" do
        expect(new_project_task.help_ticket.help_ticket_activities.find_by_activity_type('create_task')).to be_present
      end
    end

    context "without assignees" do
      let(:new_project_task) {  ProjectTask.find_by(description: 'New Name') }
      let(:project_task_params) do
        {
          description: 'New Name',
          assignees: [],
        }
      end

      it "will create a new project task" do
        expect(response).to have_http_status(:ok)
        expect(new_project_task).to be_present
      end

      it "will create company users for project task" do
        expect(new_project_task.task_assignees.count).to eq(0)
      end

      it "will create a activity" do
        expect(new_project_task.help_ticket.help_ticket_activities.find_by_activity_type('create_task')).to be_present
      end
    end

    context "with bad assignees" do
      let(:new_project_task) {  ProjectTask.find_by(description: 'New Name') }
      let(:project_task_params) do
        {
          description: 'New Name',
          assignees: [
            {
              name: nil,
              id: nil,
            }
          ]
        }
      end

      it "will create a new project task" do
        expect(response).to have_http_status(:ok)
        expect(new_project_task).to be_present
      end

      it "will create company users for project task" do
        expect(new_project_task.task_assignees.count).to eq(0)
      end

      it "will create a activity" do
        expect(new_project_task.help_ticket.help_ticket_activities.find_by_activity_type('create_task')).to be_present
      end
    end
  end
end
