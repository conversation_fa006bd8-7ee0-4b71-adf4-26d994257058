require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper
include ActiveSupport::Testing::TimeHelpers

RSpec.describe HelpTickets::Dashboard::ResponseTimesController, type: :controller do
  travel_to(Time.zone.parse("2022-06-14")) do
    create_company_and_user
  end

  let!(:update_user) {
    company_user.update_column(:granted_access_at, test_time - 1.month)
  }

  let(:test_time) { Time.zone.parse("2022-07-22 1:01pm") }
  let!(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
        'Status' => 'In Progress',
        'Priority' => "low",
        'Description' => "What a great day!",
        workspace: company.default_workspace,
      },
      {
        'Subject' => "Boom boom",
        'Created By' => company_user.contributor_id,
        'Status' => 'Open',
        'Priority' => "medium",
        'Description' => "The red robin sits in the tree.",
        workspace: company.default_workspace,
      },
      {
        'Subject' => "Second Ticket",
        'Created By' => company_user.contributor_id,
        'Status' => 'In Progress',
        'Priority' => "low",
        'Description' => "What a great day!",
        workspace: company.default_workspace,
      },
      {
        'Subject' => "Test Ticket",
        'Created By' => company_user.contributor_id,
        'Status' => 'Open',
        'Priority' => "low",
        'Description' => "This is a test ticket.",
        workspace: company.default_workspace,
      }
    ]
  }

  let!(:help_tickets) { 
    travel_to(test_time - 3.days) do 
      help_ticket_params.map do |p|
        create_ticket(p)
      end
    end
    HelpTicket.all
  }
  let(:ticket1) { help_tickets.first }
  let(:ticket2) { help_tickets.second }
  let!(:help_ticket_comment1) {
    comment = nil
    travel_to(test_time - 1.day) do
      comment = create(:help_ticket_comment, help_ticket: ticket1)
      comment.update_column(:created_at, 1.day.ago)
    end
    comment
  }
  let!(:help_ticket_comment2) {
    comment = nil
    travel_to(test_time) do
      comment = create(:help_ticket_comment, help_ticket: ticket2)
    end
    comment
  }

  describe '#index' do
    it 'will get time taken to respond to tickets created within last 1 week' do
      travel_to(test_time) do
        get :index, params: {
          'response_time': true,
          'time_frame': '1 week',
          'format': 'json',
        }

        results = JSON.parse(response.body)
        expect(results['dates'].count).to eq(2)
        expect(results['dates'].first.to_date).to eq (Date.today)
        expect(results['dates'].second.to_date).to eq (Date.today - 2.days)
        expect(results['response_avg_duration'].count).to eq(2)
        expect(results['response_avg_duration'].first.to_i).to eq(3)
        expect(results['response_avg_duration'].second.to_i).to eq(1)
        expect(results['overall_response_average'].to_i).to eq(2)
      end
    end
  end
end
