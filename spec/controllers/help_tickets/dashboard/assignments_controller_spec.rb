require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

RSpec.describe HelpTickets::Dashboard::AssignmentsController, type: :controller do
  create_company_and_user

  let!(:ticket1) {
    ticket_params = {
      'Subject' => "Ticket 1",
      'Created By' => company_user.contributor_id,
      'Assigned To' => company_user.contributor_id,
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      workspace: company.default_workspace
    }

    create_ticket(ticket_params)
  }

  let!(:ticket2) { 
    ticket_params = {
      'Subject' => "Ticket 2",
      'Created By' => company_user.contributor_id,
      'Status' => 'In Progress',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      workspace: company.default_workspace
    }

    create_ticket(ticket_params)
  }

  let!(:ticket3) { 
    ticket_params = {
      'Subject' => "Ticket 3",
      'Created By' => company_user.contributor_id,
      'Status' => 'Closed',
      'Priority' => "medium",
      workspace: company.default_workspace,
    }

    create_ticket(ticket_params)
  }

  describe '#index' do
    it 'will get overview of assigned and unassigned tickets with status Open and In Progress' do
      get :index, params: { 'show_closed' => 'false' }

      results = JSON.parse(response.body).first
      expect(results['assigned']).to eq(1)
      expect(results['unassigned']).to eq(1)
    end

    it 'will get overview of assigned and unassigned tickets including closed tickets' do
      get :index, params: { 'show_closed' => 'true' }

      results = JSON.parse(response.body).first
      expect(results['assigned']).to eq(1)
      expect(results['unassigned']).to eq(2)
    end
  end
end
