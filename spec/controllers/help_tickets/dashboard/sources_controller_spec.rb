require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

RSpec.describe HelpTickets::Dashboard::SourcesController, type: :controller do
  create_company_and_user

  let!(:help_ticket_params) {
    [
      {
        'Subject' => 'Laptop is not working.',
        'Created By' => company_user.contributor_id,
        'Status' => 'Closed',
        'source' => 'slack',
      },
      {
        'Subject' => 'QA Testing require',
        'Created By' => company_user.contributor_id,
        'Status' => 'In Progress',
        'source' => 'slack'
      },
      {
        'Subject' => 'Second Ticket',
        'Created By' => company_user.contributor_id,
        'Status' => 'In Progress',
        'source' => 'auto_generated',
      },
      {
        'Subject' => "Splitted tickets testing",
        'Created By' => company_user.contributor_id,
        'Status' => 'Closed',
        'source' => 'splitted',
      },
      {
        'Subject' => 'Wire issues',
        'Created By' => company_user.contributor_id,
        'Status' => 'Closed',
        'source' => 'splitted',
      },
      {
        'Subject' => 'Maximum stack issue',
        'Created By' => company_user.contributor_id,
        'Status' => 'Open',
        'source' => 'splitted',
      }
    ]
  }

  let!(:help_tickets) { help_ticket_params.map{ |data| create_ticket(data) } }

  describe '#index' do
    it 'will get the sources and sources count of tickets including closed tickets' do
      get :index, params: { 'format' => 'json', 'show_closed' => 'true' }

      parsed_response = JSON.parse(response.body)
      sources_count = parsed_response['sources_count']
      sources = parsed_response['sources']

      expect(sources_count['slack']).to eq(2)
      expect(sources_count['auto_generated']).to eq(1)
      expect(sources_count['split']).to eq(3)
      expect(sources_count['microsoft_teams']).to eq(nil)
    end

    it 'will get the sources and sources count of tickets not including closed tickets' do
      get :index, params: { 'format' => 'json', 'show_closed' => 'false' }

      parsed_response = JSON.parse(response.body)
      sources_count = parsed_response['sources_count']
      sources = parsed_response['sources']

      expect(sources_count['slack']).to eq(1)
      expect(sources_count['auto_generated']).to eq(1)
      expect(sources_count['split']).to eq(1)
      expect(sources_count['microsoft_teams']).to eq(nil)
    end
  end
end
