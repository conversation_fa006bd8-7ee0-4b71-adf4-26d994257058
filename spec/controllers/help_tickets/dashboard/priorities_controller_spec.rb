require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper
include ActiveSupport::Testing::TimeHelpers

RSpec.describe HelpTickets::Dashboard::PrioritiesController, type: :controller do
  Timecop.freeze(Time.zone.parse("2022-06-17")) do
    create_company_and_user
  end

  before do
    priority_field.update(options: options)
  end

  let!(:granted_access_at) {
    company_user.update_column(:granted_access_at, Time.zone.parse("2022-06-17 12:01am"))
  }
  let(:test_time) { Time.zone.parse("2022-07-22 1:01pm") }
  let(:form_name) { custom_form.form_name }
  let(:custom_form) { company.custom_forms.helpdesk.first }
  let(:priority_field) { custom_form.custom_form_fields.find_by(field_attribute_type: "priority") }
  let(:options) { "[{\"name\":\"low\",\"color\":\"#ffd700\"},{\"name\":\"medium\",\"color\":\"#ffa500\"},{\"name\":\"high\",\"color\":\"#ff0000\"},{\"name\":\"within (30 days)\",\"color\":\"#876bc7\"},{\"name\":\"won't do\",\"color\":\"#e85eb6\"}]" }
  let!(:ticket) { 
    ticket_params = {
      'Subject' => "Boom boom",
      'Created By' => company_user.contributor_id,
      'Status' => 'Open',
      'Priority' => "medium",
      workspace: company.default_workspace,
    }

    t = nil
    travel_to(test_time - 4.days) do 
      t = create_ticket(ticket_params)
    end
    t
  }

  let!(:ticket2) { 
    ticket_params = {
      'Subject' => "Doggie!!",
      'Created By' => company_user.contributor_id,
      'Status' => 'Open',
      'Priority' => "high",
      workspace: company.default_workspace,
    }

    t = nil
    travel_to(test_time - 2.days) do 
      t = create_ticket(ticket_params)
    end
    t
  }

  let!(:ticket3) { 
    ticket_params = {
      'Subject' => "Coffee maker is broken!",
      'Created By' => company_user.contributor_id,
      'Status' => 'Open',
      'Priority' => "low",
      workspace: company.default_workspace,
    }

    t = nil
    travel_to(test_time - 2.weeks) do 
      t = create_ticket(ticket_params)
    end
    t
  }

  let!(:ticket4) { 
    ticket_params = {
      'Subject' => "I can't get in the building",
      'Created By' => company_user.contributor_id,
      'Status' => 'Open',
      'Priority' => "high",
      workspace: company.default_workspace,
      created_at: DateTime.now - 3.days
    }
    t = nil
    travel_to(test_time - 3.weeks) do 
      t = create_ticket(ticket_params)
    end
    t
  }

  let!(:ticket5) { 
    ticket_params = {
      'Subject' => "The door handle is broken",
      'Created By' => company_user.contributor_id,
      'Status' => 'Closed',
      'Priority' => "high",
      workspace: company.default_workspace,
    }

    t = nil
    travel_to(test_time - 2.days) do 
      t = create_ticket(ticket_params)
    end
    t
  }

  let!(:ticket6) { 
    ticket_params = {
      'Subject' => "We have a new problem",
      'Created By' => company_user.contributor_id,
      'Status' => 'Open',
      'Priority' => "won't do",
      workspace: company.default_workspace,
    }

    t = nil
    travel_to(test_time - 10.days) do 
      t = create_ticket(ticket_params)
    end
    t
  }

  let!(:ticket7) { 
    ticket_params = {
      'Subject' => "Again! We have a problem",
      'Created By' => company_user.contributor_id,
      'Status' => 'Open',
      'Priority' => "within (30 days)",
      workspace: company.default_workspace,
    }

    t = nil
    travel_to(test_time - 10.days) do 
      t = create_ticket(ticket_params)
    end
    t
  }

  describe '#index' do
    it 'will get priorities of tickets created in last 3 months' do
      travel_to(test_time) do 
        get :index, params: {
          'priority': true,
          'custom_form_id': [custom_form.id],
          'time_frame': '1 quarter',
          'format': 'json',
        }
      end

      results = JSON.parse(response.body)
      expect(results['dates'].count).to eq(5)
      travel_to(test_time) do 
        expect(results['dates'].first.to_date).to eq(3.weeks.ago.to_date)
      end
      expect(results['priorities'].first[form_name.downcase].count).to eq(5)
      priority_data = results['priorities'].first[form_name.downcase]
      expect(JSON.parse(priority_data.first['high'])['amount']).to eq(1)
      expect(JSON.parse(priority_data.second['low'])['amount']).to eq(1)
      expect(JSON.parse(priority_data.third["won''t_do"])['amount']).to eq(1)
      expect(JSON.parse(priority_data.third["_within_(30_days)"])['amount']).to eq(1)
    end

    it 'will get priorities of tickets created in last 1 week' do
      travel_to(test_time) do 
        get :index, params: {
          'priority': true,
          'custom_form_id': [custom_form.id],
          'time_frame': '1 week',
          'format': 'json',
        }
      end

      results = JSON.parse(response.body)
      expect(results['dates'].count).to eq(2)

      travel_to(test_time) do 
        expect(results['dates'].first.to_date).to eq(4.days.ago.to_date)
        expect(results['dates'].second.to_date).to eq(2.days.ago.to_date)
      end
      priority_data = results['priorities'].first[form_name.downcase]
      expect(priority_data.count).to eq(2)

      expect(JSON.parse(priority_data.first['medium'])['amount']).to eq(1)
      expect(JSON.parse(priority_data.second['high'])['amount']).to eq(2)
    end

    it 'will get priorities of tickets created in last 1 week not including closed tickets' do
      travel_to(test_time) do 
        get :index, params: {
          'priority': true,
          'custom_form_id': [custom_form.id],
          'time_frame': '1 week',
          'format': 'json',
          'show_closed': 'false'
        }
      end

      results = JSON.parse(response.body)
      expect(results['dates'].count).to eq(2)

      travel_to(test_time) do 
        expect(results['dates'].first.to_date).to eq(4.days.ago.to_date)
        expect(results['dates'].second.to_date).to eq(2.days.ago.to_date)
      end
      priority_data = results['priorities'].first[form_name.downcase]
      expect(priority_data.count).to eq(2)

      expect(JSON.parse(priority_data.first['medium'])['amount']).to eq(1)
      expect(JSON.parse(priority_data.second['high'])['amount']).to eq(1)
    end
  end
end
