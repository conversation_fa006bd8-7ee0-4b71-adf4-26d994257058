require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

RSpec.describe HelpTickets::Dashboard::SatisfactionController, type: :controller do
  create_company_and_user

  let!(:closing_survey1) { create(:closing_survey, help_ticket: ticket1, positive: true) }
  let!(:closing_survey2) { create(:closing_survey, help_ticket: ticket2, positive: false) }
  let!(:closing_survey3) { create(:closing_survey, help_ticket: ticket3, positive: true) }

  let(:ticket1) { create(:help_ticket, company: company, workspace: workspace) }
  let(:ticket2) { create(:help_ticket, company: company, workspace: workspace) }
  let(:ticket3) { create(:help_ticket, company: company, workspace: workspace) }

  describe '#index' do
    it 'will get overview of satisfaction of users from the closing surveys of tickets' do
      get :index

      results = JSON.parse(response.body).first
      expect(results["great"]).to eq(2)
      expect(results["not_so_good"]).to eq(1)
    end
  end
end
