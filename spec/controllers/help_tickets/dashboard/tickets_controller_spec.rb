require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

RSpec.describe HelpTickets::Dashboard::TicketsController, type: :controller do
  create_company_and_user
  let(:company_user2) { create(:company_user, company: company) }

  ##
  # Testing to make sure to include assigned to tickets
  let!(:ticket) { 
    ticket_params = {
      'Subject' => "Boom boom",
      'Created By' => company_user2.contributor_id,
      'Assigned To' => [ company_user.contributor_id ],
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      'message_id' => "ptg2c67bk4luop98ogdk8greaiqud1s1cmhj03g2",
      workspace: company.default_workspace
    }

    create_ticket(ticket_params)
  }

  ##
  # Testing to make sure to include created by tickets
  let!(:ticket2) { 
    ticket_params = {
      'Subject' => "Hi",
      'Created By' => company_user.contributor_id,
      'Status' => 'In Progress',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      'message_id' => "ptg2c67bk4luop98ogdk8greaiqud1s1cmhj03g2",
      workspace: company.default_workspace
    }

    create_ticket(ticket_params)
  }

  ##
  # Testing to make sure to exclude others tickets
  let!(:ticket3) { 
    ticket_params = {
      'Subject' => "Boom boom",
      'Created By' => company_user2.contributor_id,
      'Status' => 'Open',
      "Assigned To" => [ company_user2.contributor_id ],
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      'message_id' => "ptg2c67bk4luop98ogdk8greaiqud1s1cmhj03g2",
      workspace: company.default_workspace
    }

    create_ticket(ticket_params)
  }

  ##
  # Testing to make sure to only return Active tickets
  let!(:ticket4) { 
    ticket_params = {
      'Subject' => "Hi",
      'Created By' => company_user.contributor_id,
      'Status' => 'Closed',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      'message_id' => "ptg2c67bk4luop98ogdk8greaiqud1s1cmhj03g2",
      workspace: company.default_workspace
    }

    create_ticket(ticket_params)
  }

  describe '#index' do
    it 'will return my_tickets' do
      get :index

      results = JSON.parse(response.body)
      expect(results.count).to eq(1)
      actual_ids = results.map {|t| t["id"] }.sort
      expected_ids = [ ticket.id ].sort
      expect(actual_ids).to eq(expected_ids)
    end
  end
end
