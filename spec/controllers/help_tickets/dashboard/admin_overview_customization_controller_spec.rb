require 'rails_helper'
include CompanyUserHelper

RSpec.describe HelpTickets::Dashboard::AdminOverviewCustomizationController, type: :controller do
  create_company_and_user

  let!(:user1) { create(:user) }
  let!(:company_user1) { create(:company_user, user: user1, company: company) }
  let!(:agent_group) { company.groups.find_by(name: "Help Desk Agents") }
  let!(:agent) { agent_group.group_members.create!(contributor: company_user.contributor) }
  let!(:agent1) { agent_group.group_members.create!(contributor: company_user1.contributor) }
  let!(:overview_customization) do
    AdminOverviewCustomization.create!(
      workspace_id: company.default_workspace.id,
      company_id: company.id,
      agent_ids: [company_user1.contributor_id]
    )
  end

  describe '#show' do
    it 'will get customized agents & all agents' do
      get :show, params: { 'format' => 'json' }
      results = JSON.parse(response.body)

      expect(results['customized_agents_ids'].length).to eq(1)
      expect(results['all_agents_ids'].length).to eq(2)
      expect(results['customized_agents'].pluck('id')).to eq([company_user1.contributor_id])
    end

    it 'will update the admin overview setting' do
      put :update, params: { agent_ids: [company_user1.contributor_id, company_user.contributor_id] }

      expect(response).to have_http_status(:no_content)
    end
  end
end
