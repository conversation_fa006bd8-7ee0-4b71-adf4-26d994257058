require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

RSpec.describe HelpTickets::Dashboard::ClosedTimesController, type: :controller do
  Timecop.freeze do
    create_company_and_user

    let!(:ticket) {
      ticket_params = {
        'Subject' => "Boom boom",
        'Created By' => company_user.contributor_id,
        'Status' => 'Open',
        'Priority' => "medium",
        'Description' => "The red robin sits in the tree.",
        'ticket_number' => 1,
        'message_id' => "ptg2c67bk4luop98ogdk8greaiqud1s1cmhj03g2",
        workspace: company.default_workspace
      }

      create_ticket(ticket_params)
    }

    let(:activity_type) { HelpTicketActivity.activity_types[:status] }

    let!(:ticket2) {
      # This is totally cheating, but if we set the status from the onset, then
      # we'll have the right data set up for testing
      ticket_params = {
        'Subject' => "Waaaa!!",
        'Created By' => company_user.contributor_id,
        'Status' => 'Closed',
        'Priority' => "High",
        'Description' => "Grumble, grumble, grumble..",
        'ticket_number' => 2,
        'message_id' => "ptg2c67bk4luop98ogdk8greaiqud1s1cmhj03g2",
        workspace: company.default_workspace
      }

      t = create_ticket(ticket_params)
      t.update_column(:created_at, 5.days.ago)
      a = HelpTicketActivity.create(help_ticket: t,
                                    owner_id: company_user.id,
                                    activity_type: activity_type,
                                    data: 
                                        {
                                          previous_value: "Open",
                                          current_value: "Closed",
                                          activity_label: "Status",
                                          ticket_subject: "Waaa",
                                          ticket_number: t.ticket_number
                                        }
                                  )
      a.update_column(:created_at, 4.days.ago)
      a = HelpTicketActivity.create(help_ticket: t,
                                    owner_id: company_user.id,
                                    activity_type: activity_type,
                                    data: 
                                        {
                                          previous_value: "Closed",
                                          current_value: "Open",
                                          activity_label: "Status",
                                          ticket_subject: "Waaa",
                                          ticket_number: t.ticket_number
                                        }
                                  )
      a.update_column(:created_at, 3.days.ago)
      a = HelpTicketActivity.create(help_ticket: t,
                                    owner_id: company_user.id,
                                    activity_type: activity_type,
                                    data: 
                                        {
                                          previous_value: "Open",
                                          current_value: "Closed",
                                          activity_label: "Status",
                                          ticket_subject: "Waaa",
                                          ticket_number: t.ticket_number
                                        }
                                  )
      a.update_column(:created_at, 2.days.ago)
      t
    }

    let!(:ticket3) { 
      ticket_params = {
        'Subject' => "The coffee pot is empty",
        'Created By' => company_user.contributor_id,
        'Status' => 'Closed',
        'Priority' => "medium",
        'Description' => "And we're out of coffee.",
        'ticket_number' => 3,
        'message_id' => "ptg2c67bk4luop98ogdk8greaiqud1s1cmhj03g2",
        workspace: company.default_workspace
      }

      t = create_ticket(ticket_params)
      t.update_column(:created_at, 2.days.ago)
      a = HelpTicketActivity.create(help_ticket: t,
                                    owner_id: company_user.id,
                                    activity_type: activity_type,
                                    data: 
                                        {
                                          previous_value: "Open",
                                          current_value: "Closed",
                                          activity_label: "Status",
                                          ticket_subject: "Waaa",
                                          ticket_number: t.ticket_number
                                        }
                                  )
      a.update_column(:created_at, 1.day.ago)
      t
    }

    describe '#index' do
      it 'will get time taken to close tickets created in last 1 week' do
        get :index, params: {
          'closed_time': true,
          'time_frame': '1 week',
          'format': 'json',
        }

        results = JSON.parse(response.body)
        expect(results['dates'].count).to eq(2)
        expect(results['dates'].first.to_date).to eq (Date.today - 1.days)
        expect(results['dates'].second.to_date).to eq (Date.today - 2.days)
        expect(results['closed_avg_duration'].count).to eq(2)
        expect(results['closed_avg_duration'].first.to_i).to eq(1)
        expect(results['closed_avg_duration'].second.to_i).to eq(3)
        expect(results['overall_closed_average'].to_i).to eq(2)
      end
    end
  end
end
