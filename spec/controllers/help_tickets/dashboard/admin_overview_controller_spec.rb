require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

RSpec.describe HelpTickets::Dashboard::AdminOverviewController, type: :controller do
  create_company_and_user

  let!(:agents) {
    company.groups.find_by(name: "Help Desk Agents")
  }
  let!(:assign_agents) {
    agents.group_members.create!(contributor: company_user.contributor)
  }

  let!(:ticket1) {
    ticket_params = {
      'Subject' => "Ticket 1",
      'Created By' => company_user.contributor_id,
      'Assigned To' => company_user.contributor_id,
      'Status' => 'Closed',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      workspace: company.default_workspace
    }

    create_ticket(ticket_params)
  }

  let!(:ticket2) {
    ticket_params = {
      'Subject' => "Ticket 2",
      'Created By' => company_user.contributor_id,
      'Assigned To' => company_user.contributor_id,
      'Status' => 'In Progress',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      workspace: company.default_workspace
    }

    create_ticket(ticket_params)
  }

  let!(:ticket3) {
    ticket_params = {
      'Subject' => "Ticket 3",
      'Created By' => company_user.contributor_id,
      'Assigned To' => company_user.contributor_id,
      'Status' => 'Open',
      'Priority' => "medium",
      workspace: company.default_workspace
    }

    create_ticket(ticket_params)
  }

  let!(:help_ticket_activity) {
    create(:help_ticket_activity,
           help_ticket: ticket1,
           activity_type: HelpTicketActivity.activity_types[:status],
           data: {'current_value'=>'Closed', 'activity_label'=>'Status', 'previous_value'=>'Open'}) 
  }

  describe '#index' do
    it 'will get overview for admin including closed tickets' do
      TimeSpent.create(company_user: company_user,
                       help_ticket: ticket1,
                       hours_spent: '10',
                       minutes_spent: '30',
                       started_at: 2.days.ago)

      get :index, params: {
        'admin_overview' => true,
        'format' => 'json',
        'show_closed' => 'true'
      }

      results = JSON.parse(response.body)["admin_overview"].first
      expect(results['employee']).to be_present
      expect(results['open']).to eq(1)
      expect(results['closed']).to eq(1)
      expect(results['in_progress']).to eq(1)
      expect(results['total_time']).to eq('10 hrs 30 mins')
    end

    it 'will get overview for admin not including closed tickets' do
      get :index, params: {
        'admin_overview' => true,
        'format' => 'json',
        'show_closed' => 'false'
      }

      results = JSON.parse(response.body)["admin_overview"].first
      expect(results['employee']).to be_present
      expect(results['open']).to eq(1)
      expect(results['closed']).to eq(0)
      expect(results['in_progress']).to eq(1)
    end
  end
end
