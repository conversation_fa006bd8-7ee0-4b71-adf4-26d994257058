require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

RSpec.describe HelpTickets::Dashboard::StatusesController, type: :controller do
  create_company_and_user

  before do
    custom_form = workspace.custom_forms.find_by(company_module: "helpdesk")
  custom_form.custom_form_fields.find_by(name: "status").update(options: "[{\"name\":\"Open\",\"color\":\"#2ECC71\"},{\"name\":\"In Progress\",\"color\":\"#5DADE2\"},{\"name\":\"Closed\",\"color\":\"#626567\"}, {\"name\":\"Custom Status\",\"color\":\"#FFD700\"}, {\"name\":\"1 Status\",\"color\":\"#FFA500\"}, {\"name\":\"Ticket's status\",\"color\":\"#FFA400\"}]")
  end

  let!(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
        'Status' => 'In Progress',
        'Priority' => "low",
      },
      {
        'Subject' => "Boom boom",
        'Created By' => company_user.contributor_id,
        'Status' => 'Open',
        'Priority' => "medium",
      },
      {
        'Subject' => "Second Ticket",
        'Created By' => company_user.contributor_id,
        'Status' => 'In Progress',
        'Priority' => "low",
      },
      {
        'Subject' => "Test Ticket",
        'Created By' => company_user.contributor_id,
        'Status' => 'Closed',
        'Priority' => "low",
      },
      {
        'Subject' => "Last Ticket...  almost",
        'Created By' => company_user.contributor_id,
        'Status' => 'Closed',
        'Priority' => "low",
      },
      {
        'Subject' => "Last Ticket",
        'Created By' => company_user.contributor_id,
        'Status' => 'Closed',
        'Priority' => "low",
      },
      {
        'Subject' => "Custom Status Ticket 1",
        'Created By' => company_user.contributor_id,
        'Status' => 'Custom Status',
        'Priority' => "medium",
      },
      {
        'Subject' => "Custom Status Ticket 2",
        'Created By' => company_user.contributor_id,
        'Status' => '1 Status',
        'Priority' => "medium",
      },
      {
        'Subject' => "Custom Status Ticket 3",
        'Created By' => company_user.contributor_id,
        'Status' => "Ticket's status",
        'Priority' => "medium",
      }
    ]
  }

  let!(:help_tickets) { help_ticket_params.map{ |p| create_ticket(p) } }

  describe '#index' do
    it 'will get the status of tickets including closed tickets' do
      get :index, params: {
        'status' => true,
        'format'=>'json',
        'show_closed' => 'true'
      }

      results = JSON.parse(response.body)["counts"].first
      expect(results['open']).to eq(1)
      expect(results['in_progress']).to eq(2)
      expect(results['closed']).to eq(3)
      expect(results['custom_status']).to eq(1)
      expect(results['1_status']).to eq(1)
      expect(results["ticket''s_status"]).to eq(1)
    end

    it 'will get the status of tickets not including closed tickets' do
      get :index, params: {
        'status' => true,
        'format'=>'json',
        'show_closed' => 'false'
      }

      results = JSON.parse(response.body)["counts"].first
      expect(results['open']).to eq(1)
      expect(results['in_progress']).to eq(2)
      expect(results['closed']).not_to be_present
    end
  end
end
