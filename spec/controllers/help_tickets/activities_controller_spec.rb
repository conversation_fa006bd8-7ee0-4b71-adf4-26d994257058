require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

describe HelpTickets::ActivitiesController, type: :controller do
  create_company_and_user

  let(:company_user) { company.company_users.first }

  let(:author) { user.company_users.first }
  let(:help_ticket) {
    ticket_params = {
      'Subject' => "Subject",
      'Created By' => author.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      company: company
    }
    create_ticket(ticket_params)
  }

  describe "#index" do
    let(:help_ticket) {
      ticket_params = {
        'Subject' => "Subject",
        'Created By' => company_user.contributor_id,
        'Assigned To' => [company_user.contributor_id],
        'Followers' => [company_user.contributor_id],
        'Impacted Devices' => [],
        'Status' => 'Open',
        'Priority' => "medium",
        'Description' => "The red robin sits in the tree.",
        company: company
      }
      create_ticket(ticket_params)
    }

    let(:help_ticket1) {
      ticket_params = {
        'Subject' => "New Ticket",
        'Created By' => company_user.contributor_id,
        'Assigned To' => [company_user.contributor_id],
        'Followers' => [company_user.contributor_id],
        'Impacted Devices' => [],
        'Status' => 'Open',
        'Priority' => "medium",
        'Description' => "Ticket for sorting",
        company: company
      }
      create_ticket(ticket_params)
    }
    let!(:help_ticket_comment) { FactoryBot.create(:help_ticket_comment, help_ticket: help_ticket, private_flag: true) }
    let!(:help_ticket_activity) { FactoryBot.create(:help_ticket_activity, help_ticket: help_ticket, owner: company_user, data: {"help_ticket_comment_id"=>help_ticket_comment.id, "ticket_number"=> help_ticket.ticket_number}) }
    let!(:help_ticket_activity1) { FactoryBot.create(:help_ticket_activity, help_ticket: help_ticket1, activity_type: "text", owner: company_user, data: {"ticket_number"=>help_ticket1.ticket_number}) }

    context "with some data" do
      it "returns an http status" do
        get :index, params: { help_ticket_id: help_ticket.id, format: :json }
        expect(response).to have_http_status(:ok)
      end

      it "returns company activities" do
        get :index, params: {"search_terms"=>"", "per_page"=>"30", "page"=>"1", "controller"=>"help_ticket_activities", "action"=>"index", "format"=>"json"}
        expect(response).to have_http_status(:ok)
        activities = JSON.parse(response.body)
        expect(activities.length).to eq(3)
      end

      it "returns company sorted ticket number activities " do
        get :index, params: {"search_terms"=>"", "per_page"=>"30", "page"=>"1", "sort_field"=>"ticket_number", "sort_direction"=>"asc", "controller"=>"help_ticket_activities", "action"=>"index", "format"=>"json"}
        expect(response).to have_http_status(:ok)
        activities = JSON.parse(response.body)['help_ticket_activities']
        expect(activities[0]['data']['ticket_number'].to_i < activities[1]['data']['ticket_number'].to_i).to be_truthy
      end

      it "returns activities" do
        get :index, params: { help_ticket_id: help_ticket.id, format: :json }
        activities = JSON.parse(response.body)
        expect(activities).to be_present
        expect(activities['total']).to eq(2)
      end

      it "returns activity with private flag" do
        get :index, params: { help_ticket_id: help_ticket.id, format: :json }
        activities = JSON.parse(response.body)
        expect(activities).to be_present
        ticket_activity = activities['help_ticket_activities'].find { |activity| activity['activity_type'] == 'note' }
        expect(ticket_activity['private_flag']).to eq(true)
      end
    end
  end
end
