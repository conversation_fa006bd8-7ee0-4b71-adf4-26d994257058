require 'rails_helper'
include CompanyUserHelper

RSpec.describe HelpTickets::SnippetsController, type: :controller do
  create_company_and_user

  describe "#create" do
    it "create snippet" do
      post :create, params: { "snippet"=>{ "title" => "T1", "description" => "div>d1</div>" }}
      expect(workspace.snippets.count).to eq(1)
    end
  end

  describe "#update" do
    before(:each) do
      @snippet = FactoryBot.create(:snippet, company: company, workspace: workspace)
    end

    it "update snippet" do
      snippet_params = {
        "snippet" => {
          "title" => "T2",
          "description" => "div>This is sample text. 123</div>",
          "company_id" => company.id,
          "workspace_id" => workspace.id
        },
        "id"=> @snippet.id
      }
      put :update, params: snippet_params
      expect(@snippet.reload.title).to eq("T2")
    end
  end

  describe "#index" do
    before(:each) do
      @snippet_1 = FactoryBot.create(:snippet, title: "snippet_1", company: company, workspace: workspace)
      @snippet_2 = FactoryBot.create(:snippet, title: "snippet_2", company: company, workspace: workspace)
    end

    it "list snippet" do
      get :index, params: {"page" => "1", "per_page" => "25"}
      expect(response.status).to eq(200)
      result = JSON.parse(response.body)
      snippets = result["snippets"]
      expect(snippets).to be_present
      expect(snippets.count).to eq(2)
    end
  end

  describe "#show" do
    before(:each) do
      @snippet = FactoryBot.create(:snippet, company: company, workspace: workspace)
    end

    it "show snippet" do
      get :show, params: {"id" => @snippet.id}
      expect(response.status).to eq(200)
      result = JSON.parse(response.body)
      expect(result["id"]).to eq(@snippet.id)
      expect(result["workspace_id"]).to eq(@snippet.workspace_id)
    end
  end

  describe "#destroy" do
    before(:each) do
      @snippet = FactoryBot.create(:snippet, company: company, workspace: workspace)
    end

    it "destroy snippet" do
      delete :destroy, params: {"id" => @snippet.id}
      expect(workspace.snippets.find_by(id: @snippet.id)).to eq(nil)
      expect(response.status).to eq(200)
    end
  end
end
