require 'rails_helper'
include CompanyUserHelper

RSpec.describe HelpTickets::ParentTasksController, type: :controller do
  create_company_and_user

  let(:help_ticket) { FactoryBot.create(:help_ticket, company: company, workspace: company.workspaces.first ) }
  let(:parent) { FactoryBot.create(:project_task, help_ticket: help_ticket) }
  let(:child) { FactoryBot.create(:project_task, help_ticket: help_ticket) }

  describe "#create" do
    it "create super task" do
      sub_task_ids =  [ child.id ]
      post :create, params: { "parent_task_id" => parent.id, "sub_task_array" => sub_task_ids, "ticket_id" => help_ticket.id }
      expect(help_ticket.project_tasks.where(parent_project_task_id: parent.id).count).to eq(1)
    end
  end

  describe "#destroy" do
    it "unembed task" do
      child.update_columns(parent_project_task_id: parent.id )
      delete :destroy, params: { "ticket_id" => help_ticket.id, "id" => child.id}

      expect(ProjectTask.where(parent_project_task_id: parent.id)).to be_blank
    end
  end
end
