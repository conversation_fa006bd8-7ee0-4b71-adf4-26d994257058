require 'rails_helper'
include CompanyUserHelper

describe HelpTickets::TicketEmailsController, type: :controller do
  create_company_and_user

  before do
    TicketEmail.destroy_all
    ticket_email_params.each do |params|
      FactoryBot.create(:ticket_email, params)
    end
  end

  let!(:workspace) { company.default_workspace }

  let(:ticket_email_params) do
    [
      {
        company: company,
        workspace: workspace,
        message_id: '1234567890',
        subject: 'Bug - Laptop not working'
      },
      {
        company: company,
        workspace: workspace,
        message_id: '0987654321',
        subject: 'Utility - Need to provide tissue boxes'
      },
      {
        company: company,
        workspace: workspace,
        message_id: '7654321890',
        subject: 'Desk in meeting room is broken'
      }
    ]
  end

  describe "index" do
    it 'will successfully return all ticket emails' do
      get :index, params: { workspace_id: workspace.id }, format: :json
      res = JSON.parse(response.body)
      expect(res['emails'].count).to eq(3)
    end

    it 'will successfully return ticket for the specific term' do
      params = { workspace_id: workspace.id, search_terms: 'bug' }
      get :index, params: params, format: :json
      res = JSON.parse(response.body)
      expect(res['emails'].count).to eq(1)
      expect(res['emails'][0]['subject']).to eq('Bug - Laptop not working')
    end
  end
end
