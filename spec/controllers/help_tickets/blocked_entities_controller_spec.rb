require 'rails_helper'
include CompanyUserHelper

describe HelpTickets::BlockedEntitiesController, type: :controller do
  create_company_and_user

  let!(:blocked_entity1) { BlockedEntity.create(company_id: company.id, workspace_id: company.default_workspace.id, entity: "keyword3", entity_type: "keyword") }
  let!(:blocked_entity2) { BlockedEntity.create(company_id: company.id, workspace_id: company.default_workspace.id, entity: "keyword4", entity_type: "keyword") }

  describe 'GET #index' do
    it 'returns a list of blocked keywords' do
      get :index, params: { keywords: 'keywords' }
      res = JSON.parse(response.body)
      expect(res["keywords"]).to be_present
      expect(res["keywords"].count).to eq(2)
    end
  end

  describe 'POST #create' do
    it 'creates blocked entities' do
      entities_params = { entities: ['<EMAIL>'], blocked_entity: { entity_type: "email" } }

      expect {
        post :create, params: entities_params
      }.to change(BlockedEntity, :count).by(1)
    end
  end

  describe 'DELETE #destroy' do
    it 'destroys a blocked entity' do
      delete :destroy, params: { id: blocked_entity1.id }

      expect(JSON.parse(response.body)['entity']["id"]).to eq(blocked_entity1.id)
    end
  end
end
