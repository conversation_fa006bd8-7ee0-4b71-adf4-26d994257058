require 'rails_helper'
include CompanyUserHelper

RSpec.describe HelpTickets::TicketsFromEmailsController, type: :controller do
  create_company_and_user(email: "<EMAIL>")

  let!(:custom_form) { create(:custom_form, company_id: company.id) }
  let!(:subjdct) { create(:custom_form_field,
                          custom_form: custom_form,
                          field_attribute_type: CustomFormField.field_attribute_types[:text],
                          label: 'Subject',
                          name: 'subject') }  

  let!(:priority) { create(:custom_form_field,
                           custom_form: custom_form,
                           field_attribute_type: CustomFormField.field_attribute_types[:priority],
                           options: CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS,
                           label: 'Priority',
                           name: 'priority') }

  let!(:status) { create(:custom_form_field,
                         custom_form: custom_form,
                         field_attribute_type: CustomFormField.field_attribute_types[:status],
                         options: CustomFormFieldTemplate::DEFAULT_STATUS_OPTIONS,
                         label: 'Status',
                         name: 'status') }

  let!(:creator) { create(:custom_form_field,
                          custom_form: custom_form,
                          field_attribute_type: CustomFormField.field_attribute_types[:people_list],
                          label: 'Created By',
                          name: 'created_by') }

  let!(:description) { create(:custom_form_field,
                              custom_form: custom_form,
                              field_attribute_type: CustomFormField.field_attribute_types[:rich_text],
                              label: 'Description',
                              name: 'description') }
  let!(:assigned_to) { create(:custom_form_field,
                              custom_form: custom_form,
                              field_attribute_type: CustomFormField.field_attribute_types[:people_list],
                              label: 'Assigned To',
                              name: 'assigned_to') }

  before do
    HelpdeskCustomEmail.create(
      "email"=> "<EMAIL>",
      "company_id"=> company.id,
      "verified"=> false,
      "name"=> "Dev Has"
    )

    ticket_email = TicketEmail.create(
      "subject" => "S6",
      "body_text" => "D6\n",
      "to" => "<EMAIL>",
      "from" => "<EMAIL>",
      "company_id" => company.id,
      "help_ticket_id" => nil,
      "body_html" => "<div dir=\"ltr\">D6</div>",
      "message_id" => "<EMAIL>",
      "s3_message_id" => "CASAS12wqsa22wqasdQWWAS",
      "recipients" => [],
      workspace: company.default_workspace
    )

    attachment = Rack::Test::UploadedFile.new('spec/data/laptop.jpg', 'image/jpg')

    TicketEmailAttachment.create(
      :ticket_email_id => ticket_email.id,
      :attached_file => { io: attachment, filename: 'laptop.jpg' },
      :name => 'laptop.jpg'
    )
  end

  describe "#create" do
    context "create ticket from incomming tab" do
      it "should craete a ticket" do
        msg_id = "<EMAIL>"
        email = TicketEmail.find_by(message_id: msg_id)
        params = {
          ticket_email_id:  email.id,
          custom_form_id: custom_form.id
        }
        post :create, params: params
        subject_value = CustomFormValue.find_by(value_str: 'S6')
        description_value = CustomFormValue.find_by(value_str: "<div dir=\"ltr\">D6</div>")
        form_name = HelpTicket.last.custom_form.form_name
        expect(response.status).to eq(200)
        expect(form_name).to eq('MyString')
        expect(subject_value).to be_present
        expect(description_value).to be_present
        expect(company.help_tickets.count).to eq(1)
      end
      it "should not delete ticket email" do
        msg_id = "<EMAIL>"
        email = TicketEmail.find_by(message_id: msg_id)
        expect(email.ticket_created).to eq(false)
        params = {
          ticket_email_id:  email.id,
          custom_form_id: custom_form.id
        }
        post :create, params: params
        subject_value = CustomFormValue.find_by(value_str: 'S6')
        description_value = CustomFormValue.find_by(value_str: "<div dir=\"ltr\">D6</div>")
        form_name = HelpTicket.last.custom_form.form_name
        updated_email = TicketEmail.find_by(message_id: msg_id)
        expect(updated_email).to be_present
        expect(updated_email.ticket_created).to eq(true)
      end
    end
  end
end
