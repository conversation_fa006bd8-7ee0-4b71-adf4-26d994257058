require 'rails_helper'
include <PERSON><PERSON><PERSON><PERSON>el<PERSON>

describe RequestEmailsController, type: :controller do
  create_company_and_user

  before do
    CustomForm.where(company_module: 'helpdesk').where.not(form_name: 'Helpdesk email').destroy_all
  end

  let!(:custom_form) { create(:custom_form, company: company, company_module: 'helpdesk', form_name: 'Helpdesk email', default: true, custom_form_fields_attributes: custom_form_fields_attributes) }
  let!(:helpdesk_custom_email) { create(:helpdesk_custom_email) }
  let(:custom_form_fields_attributes) {
    [
      {
        field_attribute_type: "priority",
        label: "Priority",
        name: "priority",
        options: CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS
      },
      {
        field_attribute_type: "status",
        label: "Status",
        name: "status",
        options: CustomFormFieldTemplate::DEFAULT_STATUS_OPTIONS
      },
      {
        field_attribute_type: "people_list",
        label: "Assigned To",
        name: "assigned_to",
        options: "[{\"id\":#{company_user.id},\"name\":\"#{company_user.name}\",\"contributorId\":#{company_user.contributor_id}}]"
      }
    ]
  }

  describe "#index" do
    context "With verified custom email" do
      it 'will show custom email as helpdesk email' do
        helpdesk_custom_form = custom_form.helpdesk_custom_form
        helpdesk_custom_form.helpdesk_custom_email_id = helpdesk_custom_email.id
        helpdesk_custom_form.save!
        get :index
        res = JSON.parse(response.body)
        expect(res['request_email']).to eq(helpdesk_custom_email.email)
      end
    end

    context 'With un-verified custom email' do
      it 'will show genuity email as helpdesk email' do
        helpdesk_custom_form = custom_form.helpdesk_custom_form
        helpdesk_custom_form.helpdesk_custom_email_id = helpdesk_custom_email.id
        helpdesk_custom_form.helpdesk_custom_email.verified = false
        helpdesk_custom_form.email = "testemail@#{company.subdomain}.localhost"
        helpdesk_custom_form.save!
        get :index
        res = JSON.parse(response.body)
        expect(res['request_email']).to eq(custom_form.helpdesk_custom_form.email)
      end
    end
  end
end
