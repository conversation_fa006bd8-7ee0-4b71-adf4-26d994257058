require 'rails_helper'
include Company<PERSON>ser<PERSON>elper

describe ManagedAssetHistoryController, type: :controller do
  create_company_and_user

  let!(:laptop) { FactoryBot.create(:managed_asset, :laptop, company: company) }
  let!(:phone) { FactoryBot.create(:managed_asset, :phone, company: company) }
  let!(:mobile) { FactoryBot.create(:managed_asset, :mobile, company: company) }
  let!(:printer) { FactoryBot.create(:managed_asset, :printer, company: company) }
  let!(:router) { FactoryBot.create(:managed_asset, :router, company: company) }

  describe "#index" do
    context "without any updates to this asset" do
      it "should not return any audits" do
        get :index, params: { id: laptop.id, per_page: 25, page: 1, format: :json }

        expect(response).to have_http_status(:ok)
        audit_response = JSON.parse(response.body)['audit_history_items']
        expect(audit_response.length).to eq(0)
      end
    end

    context "with updates to this asset" do
      before do
        @old_name = laptop.name
        @new_name = "new name"
        @old_acquisition = laptop.acquisition_date
        @new_acquisition = Date.today
        @old_location_id = phone.location_id
        @new_location_id = company.locations.first.id
        laptop.update(name: @new_name, acquisition_date: @new_acquisition, location_id: @new_location_id)
      end

      it "should return audits" do
        get :index, params: { id: laptop.id, per_page: 25, page: 1, format: :json }

        expect(response).to have_http_status(:ok)
        audit_response = JSON.parse(response.body)['audit_history_items']
        expect(audit_response.length).to eq(1)
        expect(audit_response[0]["audited_changes"].length).to eq(4)
        expect(audit_response[0]["auditable_id"]).to eq(laptop.id)
        expect(audit_response[0]["audited_changes"]["name"]).to eq([@old_name, @new_name])
        expect(audit_response[0]["audited_changes"]["acquisition_date"]).to eq([@old_acquisition, @new_acquisition.to_s])
        expect(audit_response[0]["audited_changes"]["location_id"]).to eq([@old_location_id, @new_location_id])
      end
    end

    context "with updates to this asset's assignment attributes" do
      before do
        @old_used_by = phone.assignment_information.used_by_contributor_id
        @new_used_by = user.company_users.first.id
        @old_managed_by = phone.assignment_information.managed_by_contributor_id
        @new_managed_by = user.company_users.first.id
        phone.assignment_information.update(used_by_contributor_id: @new_used_by, managed_by_contributor_id: @new_managed_by)
      end

      it "should return audits" do
        get :index, params: { id: phone.id, per_page: 25, page: 1, format: :json }

        expect(response).to have_http_status(:ok)
        audit_response = JSON.parse(response.body)['audit_history_items']
        expect(audit_response.length).to eq(1)
        expect(audit_response[0]["audited_changes"].length).to eq(3)
        expect(audit_response[0]["associated_id"]).to eq(phone.id)
        expect(audit_response[0]["audited_changes"]["used_by_contributor_id"]).to eq([@old_used_by, @new_used_by])
        expect(audit_response[0]["audited_changes"]["managed_by_contributor_id"]).to eq([@old_managed_by, @new_managed_by])
      end
    end

    context "with updates to this asset's hardware attributes" do
      before do
        @old_serial = mobile.machine_serial_number
        if @old_serial == ""
          @old_serial = nil
        end
        @old_mac_addresses = mobile.mac_addresses
        @new_mac_addresses = "234.234.23.324\n324.4.3.2323"
        mobile.update(mac_addresses_text: @new_mac_addresses)
      end

      it "should return audits" do
        get :index, params: { id: mobile.id, per_page: 25, page: 1, format: :json }

        expect(response).to have_http_status(:ok)
        audit_response = JSON.parse(response.body)['audit_history_items']
        expect(audit_response.length).to eq(1)
        expect(audit_response[0]["audited_changes"].length).to eq(2)
        expect(audit_response[0]["audited_changes"]["serial_number"]).to eq(@old_serial)
        expect(audit_response[0]["audited_changes"]["mac_addresses"]).to eq([@old_mac_addresses, @new_mac_addresses.split("\n")])
      end
    end

    context "with additions and removals of asset tags" do
      let!(:tag_1) { CompanyAssetTag.create!(name: 'tag 1', company_id: company.id) }
      let!(:tag_2) { CompanyAssetTag.create!(name: 'tag 2', company_id: company.id) }

      before do
        printer.managed_asset_tags.create(company_asset_tag_id: tag_1.id)
        router.managed_asset_tags.create(company_asset_tag_id: tag_2.id)
        router.managed_asset_tags.first.destroy
      end

      it "should return audits when adding a new tag" do
        get :index, params: { id: printer.id, per_page: 25, page: 1, format: :json }
        expect(response).to have_http_status(:ok)
        audit_response = JSON.parse(response.body)['audit_history_items']
        expect(audit_response.length).to eq(1)
        expect(audit_response[0]["audited_changes"].length).to eq(3)
        expect(audit_response[0]["associated_id"]).to eq(printer.id)
        expect(audit_response[0]["audited_changes"]["tag"]).to eq("tag 1")
      end

      it "should return 2 audits when adding then removing a tag" do
        get :index, params: { id: router.id, per_page: 25, page: 1, format: :json }

        expect(response).to have_http_status(:ok)
        audit_response = JSON.parse(response.body)['audit_history_items']
        expect(audit_response.length).to eq(2)
        expect(audit_response[0]["audited_changes"].length).to eq(3)
        expect(audit_response[1]["audited_changes"].length).to eq(3)
        expect(audit_response[0]["audited_changes"]["tag"]).to eq("tag 2")
        expect(audit_response[1]["audited_changes"]["tag"]).to eq("tag 2")
        expect(audit_response[0]["associated_id"]).to eq(router.id)
        expect(audit_response[1]["associated_id"]).to eq(router.id)
        expect(audit_response.pluck('action')).to include("destroy")
        expect(audit_response.pluck('action')).to include("create")
      end
    end

    context "with updates to the asset status" do
      let!(:old_status) { CompanyAssetStatus.find_by(id: printer.custom_status.id) }
      let!(:new_status) { CompanyAssetStatus.create!(name: 'In Factory', company_id: company.id, icon: "genuicon-exclamation") }

      before do
        printer.update(company_asset_status_id: new_status.id)
      end

      it "should return audits" do
        get :index, params: { id: printer.id, per_page: 25, page: 1, format: :json }
        expect(response).to have_http_status(:ok)
        audit_response = JSON.parse(response.body)['audit_history_items']
        expect(audit_response[0]["audited_changes"].length).to eq(2)
        expect(audit_response[0]["auditable_id"]).to eq(printer.id)
        expect(audit_response[0]["audited_changes"]["company_asset_status_id"]).to eq([old_status.name, new_status.name])
      end
    end
  end
end
