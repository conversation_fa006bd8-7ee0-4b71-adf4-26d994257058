require 'rails_helper'
include CompanyUserHelper

RSpec.describe QuickViewFiltersController, type: :controller do
  create_company_and_user

  let(:workspace) { FactoryBot.create(:workspace, company: company, name: 'Workspace') }

  describe 'index' do
    context 'in sorted order' do
      it "will return workspace's default quick view filters" do
        get :index, format: :json

        expect(response).to have_http_status(:ok)
        parsed_response = JSON.parse(response.body)
        filters_data = parsed_response['filters_data']
        expect(filters_data.length).to eq(5)
        expect(filters_data).to eq(filters_data.sort_by { |filter| filter['order'] })
        expect(filters_data.map{ |obj| obj['title'] }).to eq(['My Active Tickets', 'All Active Tickets', 'Unassigned', 'Drafts', 'Closed'])
      end

      it "will return company user's quick views" do
        create_company_user_quick_view
        get :index, format: :json

        expect(response).to have_http_status(:ok)
        parsed_response = JSON.parse(response.body)
        filters_data = parsed_response['filters_data']
        expect(filters_data.length).to eq(5)
        expect(filters_data).to eq(filters_data.sort_by { |filter| filter['order'] })
        expect(filters_data.map{ |obj| obj['title'] }).to eq(['My Active Tickets', 'All Active Tickets', 'Unassigned', 'Drafts', 'Closed'])
      end
    end
  end

  describe 'update' do
    let(:quick_view_filter) do
      {
        title: 'Assets filter',
        name: 'Assets filter',
        active: false,
        id: 5,
        order: 5,
      }
    end

    it 'will create the quick view filters' do
      put :update, params: { quick_view_filter: quick_view_filter, format: :json }

      expect(response).to have_http_status(:ok)
      filters_data = company_user.quick_view_filters.find_by_workspace_id(workspace.id).filters_data
      expect(filters_data.length).to eq(6)
      expect(filters_data.find{ |obj| obj['title'] == quick_view_filter[:title] }).to be_present
    end

    let(:quick_view_filter) do
      {
        title: 'Contract filter',
        name: 'Contract filter',
        active: false,
        id: 5,
        order: 5,
      }
    end

    it 'will update the quick view filters' do
      create_company_user_quick_view
      put :update, params: { quick_view_filter: quick_view_filter, format: :json }

      expect(response).to have_http_status(:ok)
      filters_data = company_user.quick_view_filters.find_by_workspace_id(workspace.id).filters_data
      expect(filters_data.length).to eq(6)
      expect(filters_data.find{ |obj| obj['title'] == quick_view_filter[:title] }).to be_present
    end
  end

  describe 'destroy' do
    before { create_company_user_quick_view }

    it 'one quick view filter' do
      params = {
        title: 'My Active Tickets',
        name: 'My Active Tickets',
        active: false,
        id: 1,
        order: 1,
        is_default: true
      }
      delete :destroy, params: params

      expect(response).to have_http_status(:ok)
      filters_data = company_user.quick_view_filters.find_by_workspace_id(workspace.id).filters_data
      expect(filters_data.length).to eq(4)
      expect(filters_data.find { |obj| obj['title'] === 'My Active Tickets' }).to be_nil
    end
  end

  describe 'reorder' do
    it 'will set the order of filters and return the quick view' do
      params = {
        quick_filters: [
          {
            title: "My Active Tickets",
            name: "My Active Tickets",
            active: false,
            id: 1,
            order: 4,
          },
          {
            title: "All Active Tickets",
            name: "All Active Tickets",
            active: false,
            id: 2,
            order: 1,
          },
          {
            title: "Unassigned",
            name: "Unassigned",
            active: false,
            id: 3,
            order: 3,
          },
          {
            title: "Closed",
            name: "Closed",
            active: false,
            id: 4,
            order: 2,
          }
        ]
      }
      put :reorder, params: params

      expect(response).to have_http_status(:ok)
      parsed_response = JSON.parse(response.body)
      expect(parsed_response['filters_data'].map{ |obj| obj['order'] }).to eq(['1', '2', '3', '4'])
    end
  end

  def create_company_user_quick_view
    default_quick_view = QuickViewFilter.find_by(workspace_id: workspace.id, company_user_id: nil);
    quick_view = company_user.quick_view_filters.new(workspace_id: workspace.id)
    quick_view.filters_data = default_quick_view.filters_data
    quick_view.save
  end
end
