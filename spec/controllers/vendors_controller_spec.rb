require 'rails_helper'
include CompanyUserHelper
include <PERSON><PERSON><PERSON>elper

describe VendorsController, type: :controller do
  create_company_and_user

  before do
    login_user
  end

  let(:asset) { FactoryBot.create(:managed_asset, company: company) }
  let(:contract) { FactoryBot.create(:contract, name: 'contract1', company: company) }
  let(:vendor) { FactoryBot.create(:vendor, company_id: company.id, category_id: company.categories.first.id, name: "Microsoft2", url: "www.microsoft2.com") }

  let(:vendor_attributes) do
    {
      company: company
    }
  end
  let!(:vendors_names) { ["Microsoft", "Apple", "AT&T"] }

  let!(:vendor1) { FactoryBot.create(
    :vendor,
    vendor_attributes.merge(
      category_id: company.categories.first.id,
      name: vendors_names[0],
      url: "www.microsoft.com"
    )
    )
  }

  let!(:vendor2) { FactoryBot.create(
    :vendor,
    vendor_attributes.merge(
      category_id: company.categories.second.id,
      name: vendors_names[1],
      url: "www.apple.com"
    )
    )
  }

  let!(:vendor3) { FactoryBot.create(
    :vendor,
    vendor_attributes.merge(
      category_id: company.categories.third.id,
      name: vendors_names[2],
      url: "www.att.com"
    )
    )
  }

  let!(:general_transaction2) { GeneralTransaction.create(company: company, vendor: vendor2, is_active: true, amount: 110, transaction_date: Date.today-1.month, status: 0)}
  let!(:general_transaction3) { GeneralTransaction.create(company: company, vendor: vendor2, is_active: true, amount: 20, transaction_date: Date.today-2.months, status: 0)}
  let!(:general_transaction4) { GeneralTransaction.create(company: company, vendor: nil, is_active: true, amount: 55, transaction_date: Date.today-10.days, status: 2)}
  let!(:general_transaction5) { GeneralTransaction.create(company: company, vendor: vendor3, is_active: true, amount: 100, transaction_date: Date.today-7.months, status: 0)}

  describe "#index" do
    context "when fetching all company vendors" do
      it "should fetch all company vendors in alphabetical order" do
        get :index, params: { format: :json }

        json_response = JSON.parse(response.body)["vendors"]["results"]
        expect(response).to have_http_status(:ok)
        expect(json_response.length).to eq(3)
        expect(json_response.first["id"]).to eq(vendor2.id)
        expect(json_response.second["id"]).to eq(vendor3.id)
        expect(json_response.third["id"]).to eq(vendor1.id)
      end

      it "should fetch the current company user guid" do
        get :index, params: { format: :json }

        json_response = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(json_response["channel_key"]).to eq(user.company_users.first.guid)
      end

      it "should fetch the last unrecognized transaction created date" do
        get :index, params: { format: :json }

        json_response = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)

        tz = company.try(:timezone) || "US/Central"
        timezone = Time.find_zone(tz)
        transaction_date = general_transaction4.created_at.in_time_zone(timezone).to_date
        expect(json_response["last_unrecognized_created_date"].to_date).to eq(transaction_date)
      end

      it "should fetch the last unconfirmed transaction created date" do
        get :index, params: { format: :json }

        json_response = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(json_response["last_unconfirmed_created_date"]).to eq(nil)
      end
    end
  end

  describe "#create" do
    context "with proper params" do
      let!(:new_vendor_attributes) { vendor_attributes.merge(
        category_id: company.categories.last.id,
        name: "gusto",
        url: "www.gusto.com",
        universal_links: [
          { target: { "id" => asset.linkable.id, "name" => asset.name, "linkable_type" => "ManagedAsset" }},
          { target: { "id" => contract.linkable.id, "name" => contract.name, "linkable_type" => "Vendor" }},
          { target: { "id" => vendor.linkable.id, "name" => vendor.name, "linkable_type" => "Contract" }}
        ]
      )}

      it "should create a vendor with a description when description is left blank" do
        params = { vendor: new_vendor_attributes, format: :json }
        post :create, params: params

        json_response = JSON.parse(response.body)["vendor"]
        expect(response).to have_http_status(:ok)
        expect(json_response["name"]).to eq("gusto")
        # expect(json_response["description"]).to be_present # commenting out as google knowledge graph has stopped providing this
        expect(json_response["company_id"]).to eq(company.id)
        expect(json_response["contracts"].length).to eq(0)
        expect(json_response["telecom_services"].length).to eq(0)
        expect(json_response["internal_contacts"].length).to eq(0)
      end
    end

    context "with improper params" do
      let!(:new_vendor_attributes) { vendor_attributes.merge(
        category_id: company.categories.last.id,
        name: "",
        url: "www.gusto.com"
      )}

      it "should not create a vendor when the name is missing" do
        params = { vendor: new_vendor_attributes, format: :json }
        post :create, params: params

        json_response = JSON.parse(response.body)
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response["message"]).to eq("Name can't be blank")
      end
    end
  end

  describe "#update" do
    context "with proper params" do
      it "updates the vendor" do
        params = { id: vendor1.id, vendor: { name: "Microsoft 365", notes: 'Vendor notes', universal_links: [{ target: { "id": asset.linkable.id, "name": asset.name, "linkable_type": "ManagedAsset" }}] }, format: :json }
        post :update, params: params

        json_response = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        expect(json_response["vendor"]["name"]).to eq("Microsoft 365")
        expect(json_response["vendor"]["notes"]).to eq("Vendor notes")
        expect(json_response["message"]).to eq("This vendor was successfully updated")
      end
    end

    context "with improper params" do
      it "does not update the vendor when the URL is invalid" do
        params = { id: vendor1.id, vendor: { url: "wwwmicrosoftcom", universal_links: [{ target: { "id": asset.linkable.id, "name": asset.name, "linkable_type": "ManagedAsset" }}] }, format: :json }
        post :update, params: params

        json_response = JSON.parse(response.body)
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response["message"]).to eq("URL is invalid")
      end
    end
  end

  describe "#destroy" do
    context "destroying the vendor" do
      it "destroys the vendor and archives its' transactions" do
        params = { id: vendor2.id, format: :json }
        delete :destroy, params: params

        json_response = JSON.parse(response.body)
        expect(response).to have_http_status(:ok)
        vendor = company.vendors.find_by(id: vendor2.id)
        expect(vendor).to_not be_present
        general_transaction2.reload
        general_transaction3.reload
        expect(general_transaction2.status).to eq('unrecognized')
        expect(general_transaction2.vendor_id).to eq(nil)
        expect(general_transaction3.status).to eq('unrecognized')
        expect(general_transaction3.vendor_id).to eq(nil)
      end
    end
  end

  describe '#new' do
    context 'user without module permissions' do
      before do
        login_user(user_one.user)
        get :new, format: :json
      end

      let(:user_one) { create(:company_user, company: company) }

      it 'will not allow user to access the page' do
        expect(response.status).to eq(401)
        expect(JSON.parse(response.body)['message']).to eq("Sorry, you're not authorized to perform this action.")
      end
    end

    context 'user with module permissions' do
      before do
        login_user(company_user.user)
        get :new, format: :html
      end

      it 'will allow user to access the page' do
        expect(response.status).to eq(200)
      end
    end
  end
end
