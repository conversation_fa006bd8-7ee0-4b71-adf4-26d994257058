require 'rails_helper'
include CompanyUserHelper
include <PERSON><PERSON><PERSON><PERSON><PERSON>

describe PhoneNumbersController, type: :controller do
  create_company_and_user
  let(:telecom_provider) { FactoryBot.create(:telecom_provider, name: "My Test Telecom", company: company) }
  let!(:telecom_service) { TelecomService.create(company_id: company.id, name: 'Main Phone', telecom_provider: telecom_provider, service_type: "voice_service") }
  let!(:telecom_service2) { TelecomService.create(company_id: company.id, name: 'Secondary Phone', telecom_provider: telecom_provider, service_type: "voice_service") }
  let(:phone_number_attributes) { attributes_for(:phone_number).merge(telecom_provider: telecom_provider.as_json.merge(type: 'TelecomProvider'), telecom_service_id: telecom_service.id) }

  before do
    login_user
  end

  describe "#create" do
    before(:each) do
      params = { company_id: company.id, phone_number: phone_number_attributes, format: :json }
      post :create, params: params
    end

    it ".merge(type: 'TelecomProvider')will return a success status" do
      expect(response).to have_http_status(:ok)
    end

    it "will create a new phone number record" do
      json_response = JSON.parse(response.body)
      saved_number = PhoneNumber.find(json_response["phone_number"]["id"])
      expect(saved_number).to be_present
      expect(saved_number.telecom_provider).to be_present
      expect(saved_number.telecom_provider).to eq(telecom_provider)
      expect(json_response["phone_number"]["number"]).to eq(phone_number_attributes[:number])
      expect(json_response["phone_number"]["friendly_name"]).to eq(phone_number_attributes[:friendly_name])
      expect(json_response["phone_number"]["telecom_service_id"]).to eq(telecom_service.id)
      expect(json_response["phone_number"]["company_id"]).to eq(company.id)
    end

    it "will not create a new phone number record if the same number exists" do
      params = { company_id: company.id, phone_number: phone_number_attributes, format: :json }
      post :create, params: params

      json_response = JSON.parse(response.body)
      expect(response).to have_http_status(:bad_request)
      expect(company.phone_numbers.count).to eq(1)
      expect(json_response["message"]).to eq("Number has already been taken")
    end
  end

  describe  "#index" do
    let!(:phone_number1) { FactoryBot.create(:phone_number, number: "**********", company: company, telecom_provider: telecom_provider, telecom_service: telecom_service) }
    let!(:phone_number2) { FactoryBot.create(:phone_number, number: "**********", company: company, telecom_provider: telecom_provider) }
    let!(:phone_number3) { FactoryBot.create(:phone_number, number: "**********", company: company) }
    let!(:phone_number4) { FactoryBot.create(:phone_number, number: "**********", company: company, location_id: company.locations.first.id) }

    context "when no sevice param is present" do
      it "will fetch all phone number records for this company in order by number" do
        get :index, format: :json

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        res = json_response["phone_numbers"]
        expect(res.length).to eq(4)

        expect(res.first["id"]).to eq(phone_number1.id)
        expect(res.first["telecom_provider"]).to eq({
          'id' => phone_number1.telecom_provider.id,
          'name' => phone_number1.telecom_provider.name,
        })

        expect(res.second["id"]).to eq(phone_number2.id)
        expect(res.second["telecom_service"]).to be_blank
        expect(res.second["telecom_provider"]).to eq({
          'id' => phone_number2.telecom_provider.id,
          'name' => phone_number2.telecom_provider.name,
        })

        expect(res.third["id"]).to eq(phone_number3.id)
        expect(res.third["telecom_provider"]).to be_blank
        expect(res.third["telecom_service"]).to be_blank

        expect(res.fourth["id"]).to eq(phone_number4.id)
        expect(res.fourth["location_name"]).to eq(company.locations.first.name)
      end
    end

    context "when sevice param is present" do
      it "will fetch only phone number records for this company in order by number" do
        get :index, params: { service_id: telecom_service.id, format: :json }

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response["phone_numbers"].length).to eq(1)
        expect(json_response["phone_numbers"].first["id"]).to eq(phone_number1.id)
      end
    end

    context "when location param is present" do
      it "will fetch only phone number records for this company in order by number" do
        get :index, params: { location_id: company.locations.first.id, format: :json }

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response["phone_numbers"].length).to eq(1)
        expect(json_response["phone_numbers"].first["id"]).to eq(phone_number4.id)
      end
    end
  end

  describe "#update" do
    let(:new_provider) { FactoryBot.create(:telecom_provider, name: 'Verizon', company: company) }
    let(:new_service) { FactoryBot.create(:telecom_service, name: 'New Service', telecom_provider: telecom_provider, company: company) }
    let!(:phone_number1) { FactoryBot.create(:phone_number, number: "**********", company: company, telecom_service: telecom_service) }
    let!(:phone_number2) { FactoryBot.create(:phone_number, number: "**********", company: company, telecom_service: telecom_service) }

    context "with full params" do
      it "updates the phone number when number is not a duplicate" do
        params = { id: phone_number1.id,
                    phone_number: {
                      number: "**********",
                      telecom_provider: new_provider.as_json.merge(type: 'TelecomProvider'),
                      telecom_service_id: new_service.id
                    },
                    format: :json }
        put :update, params: params

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        res = json_response["phone_number"]

        expect(res["number"]).to eq("**********")
        expect(res["telecom_provider"]).to be_present
        saved_number = PhoneNumber.find(res["id"])
        expect(saved_number).to be_present
        expect(saved_number.telecom_provider).to be_present
        expect(res["telecom_provider"]["id"]).to be_present
        expect(res["telecom_provider"]["name"]).to eq("Verizon")
        expect(res["telecom_service"]["name"]).to eq("New Service")
      end

      it "does not update the phone number when number is a duplicate" do
        params = { id: phone_number1.id, phone_number: { number: phone_number2.number }, format: :json }
        put :update, params: params

        expect(response).to have_http_status(:bad_request)
        json_response = JSON.parse(response.body)
        expect(json_response["message"]).to eq("Number has already been taken")
      end
    end

    context "with new provider" do
      it "updates the phone number when number is not a duplicate" do
        params = { id: phone_number1.id,
                    phone_number: {
                      number: "**********",
                      telecom_provider: { name: 'Boom Telecom' },
                      telecom_service_id: nil,
                    },
                    format: :json }
        put :update, params: params

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        res = json_response["phone_number"]
        expect(res["number"]).to eq("**********")
        saved_number = PhoneNumber.find(res["id"])
        expect(saved_number).to be_present
        expect(saved_number.telecom_provider).to be_present
        expect(res["telecom_provider"]).to be_present
        expect(res["telecom_provider"]["id"]).to be_present
        expect(res["telecom_provider"]["name"]).to eq("Boom Telecom")
        expect(res["telecom_service"]).to be_blank
      end
    end
  end

  describe '#new' do
    context 'user without module permissions' do
      before do
        login_user(user_one.user)
        get :new, format: :json
      end

      let(:user_one) { create(:company_user, company: company) }

      it 'will not allow user to access the page' do
        expect(response.status).to eq(401)
        expect(JSON.parse(response.body)['message']).to eq("Sorry, you're not authorized to perform this action.")
      end
    end

    context 'user with module permissions' do
      before do
        login_user(company_user.user)
        get :new, format: :json
      end

      it 'will allow user to access the page' do
        expect(response.status).to eq(200)
      end
    end
  end
end
