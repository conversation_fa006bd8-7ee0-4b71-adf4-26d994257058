require 'rails_helper'
include CompanyUserHelper

describe NoAccessController, type: :controller do
  create_company_and_user

  describe "get #index" do
    context "while logged in to your company" do
      it "should route you to your company dashboard if you have access to the subdomain" do
        get :index, params: { sub: company.subdomain }

        expect(response).to have_http_status(:found)
        expect(response).to redirect_to(user_access_url(id: UserAccess.last.auth_token,
                                                        domain: Rails.application.credentials.root_domain,
                                                        subdomain: company.subdomain))
      end

      it "should show you the no access page if you do not have access to the subdomain" do
        get :index, params: { sub: "nope" }

        expect(response).to have_http_status(:ok)
        expect(response).to render_template('no_access/index')
      end
    end
  end
end