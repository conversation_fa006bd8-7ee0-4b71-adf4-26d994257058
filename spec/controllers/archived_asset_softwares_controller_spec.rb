require 'rails_helper'
include CompanyUserHelper

RSpec.describe ArchivedAssetSoftwaresController, type: :controller do
  render_views
  create_company_and_user

  let!(:managed_asset) { FactoryBot.create(:managed_asset, company: company) }

  describe "PUT #update" do
    let!(:asset_software) { managed_asset.asset_softwares.create(name: 'BINGO1', software_type: "Application") }
    subject { put :update, :params => {id: asset_software.id, managed_asset_id: managed_asset.id} }

    it "is a successful http request" do
      subject
      expect(response).to have_http_status(:ok)
    end

    it "archives the software" do
      subject
      software = managed_asset.asset_softwares.find_by(id: asset_software.id)
      expect(software).to be_present
      expect(software.archived_at).to be_present
    end
  end

  describe "DELETE #destroy" do
    let!(:asset_software) { managed_asset.asset_softwares.create(name: 'BINGO2', software_type: "Application") }
    subject { delete :destroy, :params => { managed_asset_id: managed_asset.id, id: asset_software.id } }

    it "is a successful http request" do
      subject
      expect(response).to have_http_status(:ok)
    end

    it "unarchives the software" do
      subject
      software = managed_asset.asset_softwares.find_by(id: asset_software.id)
      expect(software).to be_present
      expect(software.archived_at).to be_blank
    end
  end
end