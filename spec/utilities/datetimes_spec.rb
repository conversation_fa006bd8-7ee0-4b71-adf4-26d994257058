require 'rails_helper'
include Utilities::Datetimes

describe Utilities::Datetimes  do
  describe "#to_date" do
    it 'will convert YYYY/MM/DD string to date' do
      string_date = "2018/02/01"
      date = to_date(string_date)
      expect(date).to eq(Date.strptime(string_date, "%Y/%m/%d"))
    end

    it 'will convert YYYY-MM-DD string to date' do
      string_date = "2018-02-01"
      date = to_date(string_date)
      expect(date).to eq(Date.strptime(string_date, "%Y-%m-%d"))
    end

    it 'will convert MM/DD/YYYY string to date' do
      string_date = "02/01/2018"
      date = to_date(string_date)
      expect(date).to eq(Date.strptime(string_date, "%m/%d/%Y"))
    end

    it 'will convert MM-DD-YYYY string to date' do
      string_date = "02-01-2018"
      date = to_date(string_date)
      expect(date).to eq(Date.strptime(string_date, "%m-%d-%Y"))
    end

    it "will return blank string while converting MM.DD.YYYY string to date" do
      string_date = "02.01.2018"
      date = to_date(string_date)
      expect(date).to eq('')
    end
  end

  describe "#is_valid_date" do
    it 'will return false on converting MM.DD.YYYY string to date' do
      string_date = "02.01.2018"
      date = is_valid_date(string_date)
      expect(date).to eq(false)
    end

    it 'will return false on converting simple string to date' do
      string_date = "hello"
      date = is_valid_date(string_date)
      expect(date).to eq(false)
    end

    it "will return Date on converting correct date string to date" do
      string_date = "02-01-2018"
      date = is_valid_date(string_date)
      expect(date).to eq((Date.strptime(string_date, "%m-%d-%Y")))
    end
  end
end
