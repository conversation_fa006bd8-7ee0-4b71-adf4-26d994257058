Return-Path: <<EMAIL>>
Received: from NAM10-MW2-obe.outbound.protection.outlook.com (mail-mw2nam10on2127.outbound.protection.outlook.com [*************])
 by inbound-smtp.us-west-2.amazonaws.com with SMTP id dpgc04lmq0o08skh9enhri3d5n69vcakfk097b81
 for <EMAIL>;
 Mon, 13 Jan 2020 23:12:38 +0000 (UTC)
X-SES-Spam-Verdict: PASS
X-SES-Virus-Verdict: PASS
Received-SPF: pass (spfCheck: domain of gogenuity.com designates ************* as permitted sender) client-ip=*************; envelope-from=<EMAIL>; helo=mail-mw2nam10on2127.outbound.protection.outlook.com;
Authentication-Results: amazonses.com;
 spf=pass (spfCheck: domain of gogenuity.com designates ************* as permitted sender) client-ip=*************; envelope-from=<EMAIL>; helo=mail-mw2nam10on2127.outbound.protection.outlook.com;
 dkim=pass header.i=@nulodgic.onmicrosoft.com;
 dkim=pass header.i=@Stratum47.onmicrosoft.com;
 dmarc=none header.from=stratumnetworks.com;
X-SES-RECEIPT: AEFBQUFBQUFBQUFFVmVRcEpCcmxKWlI1Qms5cHlsY21LRll5aXdSRWUwQlNtYW5WMVM0NEZTV0F4QU51anhaaWdjMGdkL0FaWjNTaVhJa1FIMXNlcVlWbUlPbHhNVERsTzZEQ29LUVBaVDFMcjNTZlIvdzh5M2FwUmV5WFBMcUhXbUdROXhzUStySEduUkpzeFJOUG4rU2pjdzNSQTZWdjVaNml5NWpqc1Blb2lPaVRsakh3YzJuSHVMLy9RT2N2RFMrb0wzUi8vZ3RKN1JFUEJudnh2TWdtdjhqYzV2K1lRRDdhZTF4cjNFMDYxdTVkMGhkb28vM2hnZkllODNJcWFLNFhpU3pIMHhBZEV6aUp0NUZKNFlKRlpycHBJNXQ1SnphUDQyand0Zi9rQ1JsdXEwWU5ObVE9PQ==
X-SES-DKIM-SIGNATURE: a=rsa-sha256; q=dns/txt; b=DPGeNDSqsUIzINfieXVQN9j7G5gQgRpHP9yn/m1loinAzYS1hbpjhAYnjcM7xcO8nT8VFhPpjJsEBBI0hzH3HuYxYxuePxsmtN5wF33kwHpovgAFhXbjwrHcV9LOU0zKIgtjGGK8l62xMAHdSnNRKfe1pFS77bmpOU/9izw0DwI=; c=relaxed/simple; s=gdwg2y3kokkkj5a55z2ilkup5wp5hhxx; d=amazonses.com; t=1578957159; v=1; bh=MHSMcoquQA+EQqsKMbxFNBtDi0x28LdchhKTzcsmc2I=; h=From:To:Cc:Bcc:Subject:Date:Message-ID:MIME-Version:Content-Type:X-SES-RECEIPT;
ARC-Seal: i=3; a=rsa-sha256; s=arcselector9901; d=microsoft.com; cv=pass;
 b=bk+VunvnuAj8FUnqkxRfTrHYMXqcNx7PzqkxWJOwVZ5iat3TznExQ69AdTltL/PXXXSZKmVBrVcBq0ZjSpXEYFnhpoEnilaCTON4lwLLFiPxkGhfERhC2HtxC0HevUcfVtak+b9V6v+axgN1HAvX0aljxVzKsQtdHFkra/S4BuySTzGmiyLuUTQlnzAXLmqrftrz4TnfkRERl2OEIeWbxvzPAch69AXfsZEvVWMEUCnK3IDXLRcOAB8LBssbmznE6TSyV5nWEHxSSatEEHiRHTEzO/GmDAl/ZaWs0IW0rN2ofyzbMfOKGtgpEvrZZ7ggiNxCWCgm8cNiNoyIz7PkIQ==
ARC-Message-Signature: i=3; a=rsa-sha256; c=relaxed/relaxed; d=microsoft.com;
 s=arcselector9901;
 h=From:Date:Subject:Message-ID:Content-Type:MIME-Version:X-MS-Exchange-SenderADCheck;
 bh=J5bFGWYPifs/Lg6b/wiWei02Kwr9TCVOLrWd1vBrmlA=;
 b=hqvD9vmvi74MobeNT4JqCrsoPg2cAFb2cHv0w6rjwURr1rk/vsu4xA1VjwnQdA3loDJM4ZIX14D0OXhEkUngqk++3mTnu2a7Xs0jtsR40S8xhkaSLXfO2kcUGsAXpfBCRJsoxth5Fd+vEIZV0SjJpwPAa5YpkjlpJqAWi1f0zIwqYJApRl5GoLjbjPoADSjw+baVbL+2mJUcSRtRjeP3qpsAfExE8Lk3kE2awGG+pcIg7+06I5qtWQceyMslRPgx9ySSiIk+LOoMFuD9hIEVAemdn+2/lN+KraJxjxajEll8sbN8kZJOrh+QcR8jRdVM9hinrUCJj8zR0ZM7r37SZA==
ARC-Authentication-Results: i=3; mx.microsoft.com 1; spf=pass (sender ip is
 **************) smtp.rcpttodomain=gogenuity.com
 smtp.mailfrom=stratumnetworks.com; dmarc=bestguesspass action=none
 header.from=stratumnetworks.com; dkim=pass (signature was verified)
 header.d=stratum47.onmicrosoft.com; arc=pass (0 oda=1 ltdi=1
 spf=[1,1,smtp.mailfrom=stratumnetworks.com]
 dkim=[1,1,header.d=stratumnetworks.com]
 dmarc=[1,1,header.from=stratumnetworks.com])
DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;
 d=nulodgic.onmicrosoft.com; s=selector2-nulodgic-onmicrosoft-com;
 h=From:Date:Subject:Message-ID:Content-Type:MIME-Version:X-MS-Exchange-SenderADCheck;
 bh=J5bFGWYPifs/Lg6b/wiWei02Kwr9TCVOLrWd1vBrmlA=;
 b=SkY+pW9xbjRbflWjTig3bTegOEeppPULDTC34rVx97wMu67Yl4qkR3tPUMkv1akIeJWj0sEOngWzkMAKoFmsDL9dkUO/eG0HvYrNLgHOlVz7Sh7GzFugO4L68kvPqcIWelJctDVfpg1c77r4TrpBvBrLUfYzX1JrW7zGGxpAsM8=
Resent-From: <<EMAIL>>
ARC-Seal: i=2; a=rsa-sha256; s=arcselector9901; d=microsoft.com; cv=pass;
 b=eHNu7BAyMXVNA34ySrEnLPnKeFV+LurMxzO06YGsB4L4iglmxmab2DYRszHHclZanmJ2/hkBX7+YrvPdt6/uwwHorYMSPfTQ/SMg85t3gTV0tPQrmqDH3I6LjE039GA0YrcaJh67mdDC34TcED3ZcLmU8KCC/Bl/FtMmyX8SXhU2VcHlPJ1xqZ/8OO9YZFq4QPaJzrMfVajcYwSDUKkP3NK0arWA9uZQaIoHAvNGurQ8l/rX5FyPvAHHQqV+BUPWu+n8jyze3kDEKy/Xmh1fxmwFVtvixT5gKuMj9rVY5XUUQvLdpP7zRmhDwon40nEqmXKWl3Gv1yhDPh7JZ/CYsg==
ARC-Message-Signature: i=2; a=rsa-sha256; c=relaxed/relaxed; d=microsoft.com;
 s=arcselector9901;
 h=From:Date:Subject:Message-ID:Content-Type:MIME-Version:X-MS-Exchange-SenderADCheck;
 bh=J5bFGWYPifs/Lg6b/wiWei02Kwr9TCVOLrWd1vBrmlA=;
 b=M3Ou/VqaMgoDpYO34kIKJi4f2W7/QiMXMWFChgeEJp1QhFpCw+OindsO3R0Y4WL0lcuVvOusQ9NHbfmpdDqynk7u8ikXcOsXOIFkWub6/l773Jk5SWgBNQPj00qi2w5mG9PV0hrOd0cHlUig5Qtzm9taLvW+dXnKRjYEiCd0gxnVJuP8QjxeNN/Rj3DQIOf6wLExzqm38URYXMQogv8P54v+UFuH3GLwkycJMmKCmX21xrKeFzZvwfK0U/tVQ0x8QsMAB2jvVnfwlexpOy7TzpF47oo4+5KXVWfMjR1KyMJRjtLG7N5yTyXMyVKC3gNfuO9Ry8+B4wQhnwHJbFj/KQ==
ARC-Authentication-Results: i=2; mx.microsoft.com 1; spf=pass (sender ip is
 **************) smtp.rcpttodomain=gogenuity.com
 smtp.mailfrom=stratumnetworks.com; dmarc=bestguesspass action=none
 header.from=stratumnetworks.com; dkim=pass (signature was verified)
 header.d=stratum47.onmicrosoft.com; arc=pass (0 oda=1 ltdi=1
 spf=[1,1,smtp.mailfrom=stratumnetworks.com]
 dkim=[1,1,header.d=stratumnetworks.com]
 dmarc=[1,1,header.from=stratumnetworks.com])
Received: from CY4PR12CA0033.namprd12.prod.outlook.com (2603:10b6:903:129::19)
 by BYAPR12MB3110.namprd12.prod.outlook.com (2603:10b6:a03:d7::13) with
 Microsoft SMTP Server (version=TLS1_2,
 cipher=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384) id 15.20.2623.13; Mon, 13 Jan
 2020 23:12:36 +0000
Received: from BN8NAM12FT003.eop-nam12.prod.protection.outlook.com
 (2a01:111:f400:fe5b::200) by CY4PR12CA0033.outlook.office365.com
 (2603:10b6:903:129::19) with Microsoft SMTP Server (version=TLS1_2,
 cipher=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384) id 15.20.2623.9 via Frontend
 Transport; Mon, 13 Jan 2020 23:12:36 +0000
Authentication-Results: spf=pass (sender IP is **************)
 smtp.mailfrom=stratumnetworks.com; gogenuity.com; dkim=pass (signature was
 verified) header.d=Stratum47.onmicrosoft.com;gogenuity.com;
 dmarc=bestguesspass action=none header.from=stratumnetworks.com;compauth=pass
 reason=109
Received-SPF: Pass (protection.outlook.com: domain of stratumnetworks.com
 designates ************** as permitted sender)
 receiver=protection.outlook.com; client-ip=**************;
 helo=NAM12-BN8-obe.outbound.protection.outlook.com;
Received: from NAM12-BN8-obe.outbound.protection.outlook.com (**************)
 by BN8NAM12FT003.mail.protection.outlook.com (************) with Microsoft
 SMTP Server (version=TLS1_2, cipher=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384) id
 15.20.2644.6 via Frontend Transport; Mon, 13 Jan 2020 23:12:36 +0000
ARC-Seal: i=1; a=rsa-sha256; s=arcselector9901; d=microsoft.com; cv=none;
 b=FO+gAT8wgN3y5ExcEUBQSG2iRsBFzo3jr340Jz5vtOrXpb09dh/o1l+fiId75qK3cPZnfEJqSTV5QiPy/VLUFGZ0Or0WoDT7OmkKhtY9xnSa+pG3JIwzu0/dt6t5MWe1j7L55U4O6VohL+ou/rhdLM7hQE0NJuaAog9lILt5CSjp++lFhkjBGPY3gz9U/fP/LGVEhfRL6UX1T7H7STkjiOcQY2AB08yFnAgQYp9a78VHWkZQmJsuP6+dM8zvluWKu2V+NWNRnbo5TI9ZFuTpUGRGLNoLHdwqaw3Hdz1kyf54EgWlqqYZWpMpnB8vQI3cu4hOudH4M8eEdn2DkTQEBA==
ARC-Message-Signature: i=1; a=rsa-sha256; c=relaxed/relaxed; d=microsoft.com;
 s=arcselector9901;
 h=From:Date:Subject:Message-ID:Content-Type:MIME-Version:X-MS-Exchange-SenderADCheck;
 bh=J5bFGWYPifs/Lg6b/wiWei02Kwr9TCVOLrWd1vBrmlA=;
 b=CgDa3smoqGWIZMsFLHSX/zPLbXY3rrqEp0NbwfDCOS7ol001gilNhlLBJEAIBODeIjheaz7pFSfPctbHKaS4m9Opavb6gMChg+e23xqrp7NVkGCgSKEfLvkb5lCnX//67zPuphou2RYnsoB42sZEmXYkT7Xn5s5DJ+P0BwR0NnErS46TrJlPiC2wopMaL17nDCXBi2cr+EAyvReDom90QTKAGHoHJJX7/vKfKYQtfaMiFMEUrbMstFQFW1uIOUyabY5MkEk33wqVfsFyBDCBQTyzX9RcD458JAYwA6Ag+OWsUGoHXYyVobff/i5CB+P5KT2MlYCF3I2WbkKSnZsKag==
ARC-Authentication-Results: i=1; mx.microsoft.com 1; spf=pass
 smtp.mailfrom=stratumnetworks.com; dmarc=pass action=none
 header.from=stratumnetworks.com; dkim=pass header.d=stratumnetworks.com;
 arc=none
DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;
 d=Stratum47.onmicrosoft.com; s=selector2-Stratum47-onmicrosoft-com;
 h=From:Date:Subject:Message-ID:Content-Type:MIME-Version:X-MS-Exchange-SenderADCheck;
 bh=J5bFGWYPifs/Lg6b/wiWei02Kwr9TCVOLrWd1vBrmlA=;
 b=ep/ClLevFMK2yqs2js3QPWckODhziGRXBDYkN6gQU8Qf+x3CuYvS+in5lrawf6R5xd3BV8CEmGm5J/5D1nMxtNbHNsWFVsQUbwwEy9SfJkd9+pO0nNjWm9wzupTdfXktkI0l9qoVsKByiGKc6GIDh3loH31fPJtJ3lyMT9Uib1w=
Received: from DM6PR02MB5753.namprd02.prod.outlook.com (************) by
 DM6PR02MB3929.namprd02.prod.outlook.com (*************) with Microsoft SMTP
 Server (version=TLS1_2, cipher=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384) id
 15.20.2623.10; Mon, 13 Jan 2020 23:12:35 +0000
Received: from DM6PR02MB5753.namprd02.prod.outlook.com
 ([fe80::c966:40c1:df0d:a869]) by DM6PR02MB5753.namprd02.prod.outlook.com
 ([fe80::c966:40c1:df0d:a869%5]) with mapi id 15.20.2623.015; Mon, 13 Jan 2020
 23:12:35 +0000
From: Jason Noble <<EMAIL>>
To: "<EMAIL>" <<EMAIL>>
Subject: test @ 512PM
Thread-Topic: test @ 512PM
Thread-Index: AdXKZu4q/8f85HkAQESyLztS3rGACw==
Date: Mon, 13 Jan 2020 23:12:34 +0000
Message-ID:
 <<EMAIL>>
Accept-Language: en-US
Content-Language: en-US
X-MS-Has-Attach: yes
X-MS-TNEF-Correlator:
Authentication-Results-Original: spf=none (sender IP is )
 smtp.mailfrom=<EMAIL>;
x-originating-ip: [************]
x-ms-publictraffictype: Email
X-MS-Office365-Filtering-Correlation-Id: 18d89e82-6230-49df-41a6-08d7987e13d5
X-MS-TrafficTypeDiagnostic: DM6PR02MB3929:|BYAPR12MB3110:
X-Microsoft-Antispam-PRVS:
 <<EMAIL>>
x-ms-oob-tlc-oobclassifiers: OLM:8882;OLM:8882;
x-forefront-prvs: 028166BF91
X-Forefront-Antispam-Report-Untrusted:
 SFV:NSPM;SFS:(10019020)(346002)(376002)(366004)(136003)(396003)(39830400003)(199004)(189003)(6916009)(55016002)(4744005)(9686003)(66556008)(66446008)(66476007)(86362001)(64756008)(66946007)(66576008)(5660300002)(2906002)(52536014)(76116006)(19627405001)(7696005)(8676002)(81156014)(81166006)(71200400001)(861006)(508600001)(26005)(6506007)(33656002)(186003)(316002)(8936002);DIR:OUT;SFP:1102;SCL:1;SRVR:DM6PR02MB3929;H:DM6PR02MB5753.namprd02.prod.outlook.com;FPR:;SPF:None;LANG:en;PTR:InfoNoRecords;A:1;MX:1;
received-spf: None (protection.outlook.com: stratumnetworks.com does not
 designate permitted sender hosts)
x-ms-exchange-senderadcheck: 1
X-Microsoft-Antispam-Untrusted: BCL:0;
X-Microsoft-Antispam-Message-Info-Original:
 GstxuWKvcCPO1jc+JAfOr+sHmkJYgs2VrEgLd77G1svLwdpaqoZJfeFs669JlTYVYl7loy4MfwPV5ug1M6U4QjDfS3UsxgkEbIxFkKUVmiyNqqL0mA206nhgInsq0cELxsX1eOdjJ9qka2iT74VC3yFY9z2Yv2UPiEuJOj/Ijkrsxn6MYTfegc+LbWMX/p8nkm7sUikW5/TxsavEJP6AizH0F3bnkke5EjnsQwVqm+xuYkn2lfPB2KZIfPWTRExoKag+Ctq9ubTL+WOOOs8jCm1cA3YtDxlyBF7i5TAbpoxCFO1HXkqwmQPxi7dUUhV8VkhsXitMq4dB27954kRsLLwXdHUpFKPhhZI63/cDKFf8VUeBk45PMfcB08M7Y4WObQrVjIj0iLwSwS1WFIfWZnBKNhmthctZIzJNzbgVLV1w72m7BN3G/s45W5EShqoRdhJUZ5i++M8ReiA/HxFA4ZdCHZl78qWk3uoSbSMFtEsQMpfsDqzZjyje1xkefr6wcFK8Q2k9l2g9LeBVzc7pdA==
x-ms-exchange-transport-forked: True
Content-Type: multipart/related;
	boundary="_007_DM6PR02MB575303D4FA8371543DD39D88B9350DM6PR02MB5753namp_";
	type="multipart/alternative"
MIME-Version: 1.0
X-MS-Exchange-Transport-CrossTenantHeadersStamped: DM6PR02MB3929
Return-Path: <EMAIL>
X-EOPAttributedMessage: 0
X-EOPTenantAttributedMessage: 51f6420c-47a1-4701-8bf9-e5b71795f17a:0
X-MS-Exchange-Transport-CrossTenantHeadersStripped:
 BN8NAM12FT003.eop-nam12.prod.protection.outlook.com
X-MS-Exchange-Transport-CrossTenantHeadersPromoted:
 BN8NAM12FT003.eop-nam12.prod.protection.outlook.com
X-Forefront-Antispam-Report:
 CIP:**************;IPV:;CTRY:US;EFV:NLI;SFV:NSPM;SFS:(10019020)(396003)(136003)(376002)(39830400003)(346002)(199004)(189003)(8676002)(86362001)(498600001)(6506007)(5660300002)(9686003)(26005)(55016002)(316002)(246002)(19627405001)(7696005)(52536014)(68406010)(66576008)(15974865002)(6862004)(70586007)(336012)(45080400002)(2906002)(36906005)(7636002)(356004)(33656002);DIR:OUT;SFP:1102;SCL:1;SRVR:BYAPR12MB3110;H:NAM12-BN8-obe.outbound.protection.outlook.com;FPR:;SPF:Pass;LANG:en;PTR:mail-bn8nam12on2138.outbound.protection.outlook.com;A:1;MX:1;
X-MS-Office365-Filtering-Correlation-Id-Prvs:
 49ab53ba-3a97-4838-44a2-08d7987e1321
X-LD-Processed: 51f6420c-47a1-4701-8bf9-e5b71795f17a,ExtAddr,ExtFwd
X-Forefront-PRVS: 028166BF91
X-Microsoft-Antispam: BCL:0;
X-Microsoft-Antispam-Message-Info:
 OuaW68/c4bxlr7hUSo/3TPZd8PZoSXisYpwZ70XL72qiywfGsW7dPgZ3qZa8IrmJ7nxFdf+5KxJpDjjUgv2KRivQOwJ8zX5WCkAxPDzIKvxtOELE11f8Qtofn9wsa9Qs2ADianZJIuIcBWTHl2ydRiIvrzd/ZgzseK1jPBrth9phi0KebfCDekTGY3T17Lqz49+ZRoU853B8JhaHlBjL3hD95CX0M/K0WyZSujhSWvaDkVZCraZw0PzFqoIT/b1boxplRExd7Jqi3tVTOu8Ld+0hDmN5/GPoX1Lp5kzV57gYOL4xd6YxXozdypA8PiaAjlPWG33HT6EddcQlI9A+gdswGJnXdRMOsciaFeRvvYDLN2vFYgrByqfcVZxP1r/qFFvBSSeGqTw7NX8VazKN4qF5kq4cLW4PgFur70mLSncfc3PWkimeyAE7ajhguELXt2lGB1yU2tzlQ/qUCb83v2TZh2pUJbexWdYOreclO0EqzTKaxEAAgfpRNsILZVigqg1GGM2srBoLjDVkCER3IK+RMxPNw3uNA5sM94j5yb9qfZycdEnlMC5s+QNC+H871iezsXzvN6TAC4+Lur679Q==
X-ExternalRecipientOutboundConnectors: 51f6420c-47a1-4701-8bf9-e5b71795f17a
X-OriginatorOrg: gogenuity.com
X-MS-Exchange-CrossTenant-OriginalArrivalTime: 13 Jan 2020 23:12:36.1017
 (UTC)
X-MS-Exchange-CrossTenant-Network-Message-Id: 18d89e82-6230-49df-41a6-08d7987e13d5
X-MS-Exchange-CrossTenant-Id: 51f6420c-47a1-4701-8bf9-e5b71795f17a
X-MS-Exchange-CrossTenant-FromEntityHeader: Internet
X-MS-Exchange-Transport-CrossTenantHeadersStamped: BYAPR12MB3110

--_007_DM6PR02MB575303D4FA8371543DD39D88B9350DM6PR02MB5753namp_
Content-Type: multipart/alternative;
	boundary="_000_DM6PR02MB575303D4FA8371543DD39D88B9350DM6PR02MB5753namp_"

--_000_DM6PR02MB575303D4FA8371543DD39D88B9350DM6PR02MB5753namp_
Content-Type: text/plain; charset="us-ascii"
Content-Transfer-Encoding: quoted-printable


Test


[photo]
Jason Noble
STRATUM NETWORKS
Office: ************<tel:Office:%20************> | Mobile: ************<tel=
:Mobile:%20************> | <EMAIL><mailto:jnoble@stratum=
networks.com>
www.stratumnetworks.com<http://www.stratumnetworks.com/>
651 W. Washington Blvd Suite 103 Chicago, IL 60661

[cid:image002.png@01D5CA34.A5688BE0]<http://www.linkedin.com/company/stratu=
m-networks-inc.>
[cid:image003.png@01D5CA34.A5688BE0]<http://www.facebook.com/stratumnetwork=
s/>
[cid:image004.png@01D5CA34.A5688BE0]<http://twitter.com/StratumWorks>

IMPORTANT: The contents of this email and any attachments are confidential.=
 They are intended for the named recipient(s) only. If you have received th=
is email by mistake, please notify the sender immediately and do not disclo=
se the contents to anyone or make copies thereof.



--_000_DM6PR02MB575303D4FA8371543DD39D88B9350DM6PR02MB5753namp_
Content-Type: text/html; charset="us-ascii"
Content-Transfer-Encoding: quoted-printable

<html xmlns:v=3D"urn:schemas-microsoft-com:vml" xmlns:o=3D"urn:schemas-micr=
osoft-com:office:office" xmlns:w=3D"urn:schemas-microsoft-com:office:word" =
xmlns:m=3D"http://schemas.microsoft.com/office/2004/12/omml" xmlns=3D"http:=
//www.w3.org/TR/REC-html40">
<head>
<meta http-equiv=3D"Content-Type" content=3D"text/html; charset=3Dus-ascii"=
>
<meta name=3D"Generator" content=3D"Microsoft Word 15 (filtered medium)">
<!--[if !mso]><style>v\:* {behavior:url(#default#VML);}
o\:* {behavior:url(#default#VML);}
w\:* {behavior:url(#default#VML);}
.shape {behavior:url(#default#VML);}
</style><![endif]--><style><!--
/* Font Definitions */
@font-face
	{font-family:"Cambria Math";
	panose-1:2 4 5 3 5 4 6 3 2 4;}
@font-face
	{font-family:Calibri;
	panose-1:2 15 5 2 2 2 4 3 2 4;}
@font-face
	{font-family:Tahoma;
	panose-1:2 11 6 4 3 5 4 4 2 4;}
/* Style Definitions */
p.MsoNormal, li.MsoNormal, div.MsoNormal
	{margin:0in;
	margin-bottom:.0001pt;
	font-size:11.0pt;
	font-family:"Calibri",sans-serif;}
a:link, span.MsoHyperlink
	{mso-style-priority:99;
	color:#0563C1;
	text-decoration:underline;}
a:visited, span.MsoHyperlinkFollowed
	{mso-style-priority:99;
	color:#954F72;
	text-decoration:underline;}
span.EmailStyle17
	{mso-style-type:personal-compose;
	font-family:"Calibri",sans-serif;
	color:windowtext;}
.MsoChpDefault
	{mso-style-type:export-only;
	font-family:"Calibri",sans-serif;}
@page WordSection1
	{size:8.5in 11.0in;
	margin:1.0in 1.0in 1.0in 1.0in;}
div.WordSection1
	{page:WordSection1;}
--></style><!--[if gte mso 9]><xml>
<o:shapedefaults v:ext=3D"edit" spidmax=3D"1026" />
</xml><![endif]--><!--[if gte mso 9]><xml>
<o:shapelayout v:ext=3D"edit">
<o:idmap v:ext=3D"edit" data=3D"1" />
</o:shapelayout></xml><![endif]-->
</head>
<body lang=3D"EN-US" link=3D"#0563C1" vlink=3D"#954F72">
<div class=3D"WordSection1">
<p class=3D"MsoNormal"><o:p>&nbsp;</o:p></p>
<p class=3D"MsoNormal">Test<o:p></o:p></p>
<p class=3D"MsoNormal"><o:p>&nbsp;</o:p></p>
<p class=3D"MsoNormal"><o:p>&nbsp;</o:p></p>
<table class=3D"MsoNormalTable" border=3D"0" cellspacing=3D"3" cellpadding=
=3D"0">
<tbody>
<tr>
<td style=3D"padding:0in 0in 0in 0in">
<table class=3D"MsoNormalTable" border=3D"0" cellspacing=3D"3" cellpadding=
=3D"0" width=3D"600" style=3D"width:6.25in">
<tbody>
<tr>
<td style=3D"padding:.75pt .75pt .75pt .75pt">
<table class=3D"MsoNormalTable" border=3D"0" cellspacing=3D"0" cellpadding=
=3D"0" width=3D"470" style=3D"width:352.5pt">
<tbody>
<tr>
<td style=3D"padding:0in 0in 7.5pt 0in">
<table class=3D"MsoNormalTable" border=3D"0" cellspacing=3D"0" cellpadding=
=3D"0" width=3D"470" style=3D"width:352.5pt">
<tbody>
<tr>
<td width=3D"10" valign=3D"top" style=3D"width:7.5pt;padding:0in 7.5pt 0in =
0in">
<p class=3D"MsoNormal"><img width=3D"120" height=3D"37" style=3D"width:1.25=
in;height:.3819in" id=3D"Picture_x0020_1" src=3D"cid:image001.png@01D5CA34.=
A5688BE0" alt=3D"photo"><o:p></o:p></p>
</td>
<td valign=3D"top" style=3D"border:none;border-right:solid #45668E 1.0pt;pa=
dding:0in 0in 0in 0in">
</td>
<td valign=3D"top" style=3D"padding:0in 7.5pt 0in 7.5pt">
<p class=3D"MsoNormal"><b><span style=3D"font-size:9.0pt;font-family:&quot;=
Tahoma&quot;,sans-serif;color:#646464">Jason Noble</span></b><span style=3D=
"font-size:9.0pt;font-family:&quot;Tahoma&quot;,sans-serif;color:#646464"><=
br>
STRATUM NETWORKS<o:p></o:p></span></p>
<table class=3D"MsoNormalTable" border=3D"0" cellspacing=3D"0" cellpadding=
=3D"0" width=3D"470" style=3D"width:352.5pt">
<tbody>
<tr>
<td style=3D"padding:0in 0in 0in 0in">
<p class=3D"MsoNormal"><span style=3D"font-size:9.0pt;color:#8D8D8D"><a hre=
f=3D"tel:Office:%20************"><span style=3D"font-family:&quot;Tahoma&qu=
ot;,sans-serif;color:#8D8D8D">Office: ************</span></a>&nbsp;</span><=
span style=3D"font-size:9.0pt;color:#45668E">|</span><span style=3D"font-si=
ze:9.0pt;color:#8D8D8D">&nbsp;<a href=3D"tel:Mobile:%20************"><span =
style=3D"font-family:&quot;Tahoma&quot;,sans-serif;color:#8D8D8D">Mobile:
 ************</span></a>&nbsp;</span><span style=3D"font-size:9.0pt;color:#=
45668E">|</span><span style=3D"font-size:9.0pt;color:#8D8D8D">&nbsp;<a href=
=3D"mailto:<EMAIL>"><span style=3D"font-family:&quot;Tah=
oma&quot;,sans-serif;color:#8D8D8D"><EMAIL></span></a><o=
:p></o:p></span></p>
</td>
</tr>
<tr>
<td style=3D"padding:0in 0in 0in 0in">
<p class=3D"MsoNormal"><span style=3D"font-size:9.0pt;color:#8D8D8D"><a hre=
f=3D"http://www.stratumnetworks.com/" target=3D"_blank"><span style=3D"font=
-family:&quot;Tahoma&quot;,sans-serif;color:#8D8D8D">www.stratumnetworks.co=
m</span></a><o:p></o:p></span></p>
</td>
</tr>
<tr>
<td style=3D"padding:0in 0in 0in 0in">
<p class=3D"MsoNormal"><span style=3D"font-size:9.0pt;font-family:&quot;Tah=
oma&quot;,sans-serif;color:#8D8D8D">651 W. Washington Blvd Suite 103 Chicag=
o, IL 60661<o:p></o:p></span></p>
</td>
</tr>
</tbody>
</table>
<p class=3D"MsoNormal"><span style=3D"font-size:9.0pt;font-family:&quot;Tah=
oma&quot;,sans-serif;color:#646464;display:none"><o:p>&nbsp;</o:p></span></=
p>
<table class=3D"MsoNormalTable" border=3D"0" cellspacing=3D"0" cellpadding=
=3D"0">
<tbody>
<tr>
<td style=3D"padding:0in 3.75pt 0in 0in">
<p class=3D"MsoNormal" align=3D"center" style=3D"text-align:center"><a href=
=3D"http://www.linkedin.com/company/stratum-networks-inc." target=3D"_blank=
"><span style=3D"color:blue;text-decoration:none"><img border=3D"0" width=
=3D"16" height=3D"16" style=3D"width:.1666in;height:.1666in" id=3D"Picture_=
x0020_2" src=3D"cid:image002.png@01D5CA34.A5688BE0"></span></a><o:p></o:p><=
/p>
</td>
<td style=3D"padding:0in 3.75pt 0in 0in">
<p class=3D"MsoNormal" align=3D"center" style=3D"text-align:center"><a href=
=3D"http://www.facebook.com/stratumnetworks/" target=3D"_blank"><span style=
=3D"color:blue;text-decoration:none"><img border=3D"0" width=3D"16" height=
=3D"16" style=3D"width:.1666in;height:.1666in" id=3D"Picture_x0020_3" src=
=3D"cid:image003.png@01D5CA34.A5688BE0"></span></a><o:p></o:p></p>
</td>
<td style=3D"padding:0in 3.75pt 0in 0in">
<p class=3D"MsoNormal" align=3D"center" style=3D"text-align:center"><a href=
=3D"http://twitter.com/StratumWorks" target=3D"_blank"><span style=3D"color=
:blue;text-decoration:none"><img border=3D"0" width=3D"16" height=3D"16" st=
yle=3D"width:.1666in;height:.1666in" id=3D"Picture_x0020_4" src=3D"cid:imag=
e004.png@01D5CA34.A5688BE0"></span></a><o:p></o:p></p>
</td>
</tr>
</tbody>
</table>
<p class=3D"MsoNormal"><o:p></o:p></p>
</td>
</tr>
</tbody>
</table>
</td>
</tr>
</tbody>
</table>
<p class=3D"MsoNormal"><span style=3D"display:none"><o:p>&nbsp;</o:p></span=
></p>
<table class=3D"MsoNormalTable" border=3D"1" cellspacing=3D"3" cellpadding=
=3D"0" width=3D"600" style=3D"width:6.25in;border:none;border-top:solid gra=
y 1.0pt">
<tbody>
<tr>
<td style=3D"border:none;padding:.75pt .75pt .75pt .75pt">
<p class=3D"MsoNormal" style=3D"line-height:11.7pt"><span style=3D"font-siz=
e:10.0pt;font-family:&quot;Tahoma&quot;,sans-serif;color:gray">IMPORTANT: T=
he contents of this email and any attachments are confidential. They are in=
tended for the named recipient(s) only. If you have
 received this email by mistake, please notify the sender immediately and d=
o not disclose the contents to anyone or make copies thereof.<o:p></o:p></s=
pan></p>
</td>
</tr>
</tbody>
</table>
<p class=3D"MsoNormal"><o:p></o:p></p>
</td>
</tr>
</tbody>
</table>
</td>
</tr>
</tbody>
</table>
<p class=3D"MsoNormal"><o:p>&nbsp;</o:p></p>
<p class=3D"MsoNormal"><o:p>&nbsp;</o:p></p>
</div>
</body>
</html>

--_000_DM6PR02MB575303D4FA8371543DD39D88B9350DM6PR02MB5753namp_--

--_007_DM6PR02MB575303D4FA8371543DD39D88B9350DM6PR02MB5753namp_
Content-Type: image/png; name="image001.png"
Content-Description: image001.png
Content-Disposition: inline; filename="image001.png"; size=10867;
	creation-date="Mon, 13 Jan 2020 23:12:34 GMT";
	modification-date="Mon, 13 Jan 2020 23:12:34 GMT"
Content-ID: <image001.png@01D5CA34.A5688BE0>
Content-Transfer-Encoding: base64

iVBORw0KGgoAAAANSUhEUgAAALQAAAA3CAYAAACl6WBEAAAAAXNSR0IArs4c6QAAAAlwSFlzAAAW
JQAAFiUBSVIk8AAAABl0RVh0U29mdHdhcmUATWljcm9zb2Z0IE9mZmljZX/tNXEAACnzSURBVHhe
7Z0JnJV1vfD/z3aWWRiOLLIjqCg6KopggIAsIYrLfa9RlJZv2y2vTRrXV8u3hgulZqVlZN6k5c1P
KYX3ratioKACMiSCIQ6OoiKiAoo6A7Oc7Vnu9/ecM8OZsw8QlZenHs+c8/yX3//3/+2/3//BXLhw
oTp2HcPAhwUD5odlIcfWcQwDgoFjBH2MDj5UGDhG0B+q7Ty2mGMEfYwGPlQYOCyCbpxYV6Ui4arm
5mjr5IbF7R8qzBxbzD8kBnpE0M+e88VIsDo0RunGuZpyRylP9fE6YtU1lnbg+Wlf+UBztZ26ab2g
VHJL7erFuwQjEP1At9KYrGy9gt+dQ8WS67qaqVvK1Z03XWVFTdcdZStH6QooilyuUhqNHN1VHaau
Wmj/enNrZPeEjYsShwLLlil155mWW2u7KqkrPe/czGEq191PiydqGxa3dM6zbmJd/0jYmOYqPaTc
JKAd2iW40OUyjTeV7YBTd4StWGM+eHQjoOtqp3rLaahtWhztnPHVGTdGYnb7LB8WXeXAoisDvDke
K9lcu3Lxi5mQNk68cYheaU+0bVVwT3VlmbabfC0ajTSAa8DrfjWe88V+qk/1+cpWvUFlznNpreuM
YSe3Dx0Y2dhn6aJkOdgqi6AbZ3yxn2uHPg5m5oLJ05Rh1GhKC2ia1jWH5wlOtCSf7RDvm1unX/+4
6wR+rbzoMAjxNr3COt6NFye+YgDrUCPrU248uhJm2qOHwp9htbryivMIvZSnuUqDqB3XSTqe925l
ZcsrWz96/eN6u/MIBPdGOYiSNs9OrBscDpjfMiurLlDt7QUZSbcszbPtmKMSn6PbQ53jh0x1EoDc
YQaCNe4hsVNqJAhZbs3u6Hjc1VQiUNlrjplIesrfg+6XWVVJu/aVKuJs40kXQcfs6BBXN35ohiur
3UQ8p59uBpQy9ITd0bqIh90I2rXapxlW9e2mpfViHXnxYIQqNC/e8axZ1TyP/u9kT2BXhWZYpnWH
FjBqVAG6MCsrWF185Yu7dlwzWal3y9mnkgS9ecq1MwNa8OtaQJ9qmCYU5YA3l5tP+R/LSdE1/9E1
S9P03low1BvBeQbAfEy52iYnEavRDLNSqOqQL53xmYgZQ/wn4Cm3lzCUJzK6xAVMPnwahMDfffnz
NM9x5niV5v9unHb9j1vb9/5+wsalubuaNW4waI9RWnASPMRaZNEHGTqzqeDHCFdWuo594UOj6x6/
rEsy2prrmZWGplUdDi40wQViFyEaRPOABqNS0+y8uJC2nuNCGd0vO6Q0I+mvIz8s0s914BADys7m
En2wY9sDfaIvtKfCXI5zltWmRmYT9IL6eu1/rW35CNp8kJtE8BakC9aYdE4JKauGMQ6foLdOu/YK
y6r4gWEFToAolROHwYWCs66un1hD51M9EBTi6e8p24aU2gC8j2cfhlgSAgJJ/NdmDhuCdJCCRkoz
9OzyNYtumEYwdC4j3l1Z2W/IhvHz7ixG1Bt6jzcr9NAkPRDobXccAA1FLZ0UwWna9JH9rGGqSb0s
EFqaKcAm3WRCuYeBC2FQzV+3J6oazZ4aLx9MbjIkfGeHIt0lqQn9e0pLFIaF9RmQPFSZid0Fo0fr
V7hquFgkQg8i2PJd6d8jpm6M5fmGzDaXrGoeqIfMMwVen6YKXLaQm+sMtKyqvjR5pZxdLiiht0yt
mwIwPzACwROSHW0+MeW/ZGOz+R+5aQWVYzt/1j33Mc8InCNq0slrKZUD5pFt4288wNisy6yoqlaG
e2OlGiCmx/2FZgqfOXYYjDAN5ixNzAwihILkPFkZiYl89Qn6w3DNjMysBHsjDMtCVBUxa8GxCDUE
2TiERQBh0SXNLMs9Xde1U9AcRVGSsgJUBBN/aDZTFOqYl6DFZjY140YjVHWCHS1MzJqOCscKEV73
IBBRtXKxkSlpaidXYxHsRBIE/1430462K6uqV2/XbvtS45wb19Yu/95b+WDVNfMsXdPOEk1VzoUG
wewIGU7Mvahx9FXLapt+AyL/8a9+puqT0PQ+spJi2hGT0FfX4OGcarPfAJr7QQL/8tQ5nq4P9JIl
rDxoSLcCyBBtxOjR9XpT06KS6jgvQbtuaDYSdZoLB3YSabetEO6zAilb2k4eSNGwVW2EAsplwzVs
Kzsee9f11FOmG3Q1w6Px0d1MYTYjGPaVh6wjn+OTQi6oj8fhO2283ZaYzi/3ZUP69lX1IW1v2wV6
MBi228unS5cwgKvcKV6//qdgdmxOWQgl3Zaji6gezhbTrOH4o32JtBTvyX77bXRtuLK80zsJet3E
q3rVmMZYMxDWkh37i47hQUCGQWTL84YvG9kcrG066NQW6piD3ffn1VvenpYpEGyFE88vjUSVoC52
MeF/6E68QblmhW04pxquPYGJZgWqrBrn/egz2Gl/cSvdCTrUjunSDQZRyYXsr86GIv2JOqW+psxe
5UYdq3ikDoY2iGy4zrtOvOMpT9fCmuON00xrAMyXFw9if0L8Id1pP6tx9Gijtqmpm2G4+819Q0NW
aEZe5vZhE/s+JXoyL9FahmEcrzRXGGUznC5Ng7owGgyXcizFjkRSlfAFcKDoLktH39EPJxt1Hg/w
5TAckx5SM811PTlY6cHepeBNCQu0tGFWscLz+PYn+anarD5Z08V+BsW+H5Lfse6ETMxD13WGV8TC
OAOHQNB7WmO9PM0bJk5NoQ0U4sSu3qTH3XtqG+5pSU/+p3Vz6n4eiQWnJPZ3fA6KX0lIrL1xal1S
WcZr2KvNjPc+8Kc2wPNO1Q2jr9vd50gTr0Q0JCLn7HLc+GssKsjXsGeb1ZqnvekHNwphQggfRnAS
3sta1KmLqbBrBdvPNRzte0aw4gyIPGcXfZtaCMzzBkfGzqxQTU2tmY0szxTHZrSbKEA7eYg5tZ+i
MkPKcaOYHXU/MT2rFSJ/DRgqYbgWz9NE5+qo7jN13aguxOCESkWAvOJ58d2IKygbXGhaDcT8FmPI
Z88p8xB76K4xhAVVQyOlR8AGZv2aYyfGvT2xPjS4YVHM1PTTwfeJDgKtFDH7EzAGoaETYlq0mm/N
pSbNkdDRWDSE1RJMoSg/0HZM3E812Qnp3yXs1WA7+ovJttY3Ji9fvI9Oy9cNmPOkGinRGq4kksns
+LRYL6ZV3aKizY5t2gPNQOTHSMVpLhGD7IWJBBItYEc7HjcSbTfiFwBONGxb7RWmGYljP9xAliV/
VsOf1Jd89tzmyPvYXSJtV2yddt3lSLkz3EQep06knt9NC0V3REWVdBE0TFpREzMvNAIhXeztzEsI
DdMm4TjJjeBjiG5YJ3hOphZAuohvobSz7f76hGi0am04vG+eAn9V1ZH9seZm27bMIdjnDxhWcLSd
h9mEsSU85iWjv1cfxO5SoYgbVc1hZVaHLDsZ0oPWt30GjpdBYKWooYznnu7hEAYNJ1E4OtE5TCrq
IsFddfpuc9/w0Lz6He67zR8xwmHT6Sgvseybtcod4NjW8Qx20A4vAGsOQYdVOOlqMbsY14sqRf33
Q4p/CTvp85rmvh+MVO56Ycb8zUz+ZCj63ppxDYv9YDpSWozObl5+48QvBogmu14RwSLRBE3zkrUN
Sz7IhL2eGOYnGhIld0+keJqYVeOcujDKqldBUyUt8pkHDyDUzS4JtdpDSTBMy69JkJyel9A8fQVe
+/nEuU+AuLujWmLSRqDGdeNzyJg9wcPtmQ0aZ9RV+ntWTMqKH+Co6JnPLRGB0XWRha3xM6FHSUK/
PaeuWrW7gzTDyB/pEVPIjwIdtK+FnGH0Ppbjjt6zq7ldq7TGSJuUyXKQAPycQr5QqLTztF5KT55I
h2dL8VwOQTdHdx+oCR2327dvJJZawPgXycNGij1nIiGOR1Idz9/j7FjHl8JVAzZsmXrtz8x3dbz7
g+nWDGBQAmVkRIiELlhQry1cuKiLgK/bXoZXJXaXp6pemjL/3JhKmlqHdjHmzSzxCfIhrRPBIH9v
68xIu2o4CGnYCk5GvQ/NZ3+LhEZ1xjTH2eRq1mBDojtZl2wu7YTkpkGAg2Hwt7s3sUgSJUvbDOSi
N4wfb07YuDEj+Blm/8qLupQihHKeN8fsQZ4RHOjL3HzERwo3/XtXtkuEHzYziR1trLJIjrjeyeI/
ddPKXUmq7kQuMMl+kXk1TccevqB+gbZw0cKiwiyHoCc3/Ca2Zco1K5PRjo+ReAj7qqGABOi0sSVE
5V8pZ0WjH7FX7Wx3QPLERnXV949qyEqImewTFHJaMuDdr3vGcRgwfQQ21xaTNYt2BGFBic7EE5Dj
cxMWHaw7EMnudnhzjGAQ3zePipUEh9L26LbeaGvOqbpI52whIBsvyQddHwUrksFVS8shnr/HNnbS
HGCGzL75hBxSGFp138PZa0OwnYD2JXycInyylbgI+pl4FG+SEKrpopcU0UibJH33skUD2SezG7P4
/aVuQZ0we8XLeMUH49n5cJQ3hpRssx8N9nJ+DxRXe0FsA4l2lKPW0vHoZOt+Sf2GTcO40R7Q993f
7pl375UtS8sqLjkSG+lLRcOoIlJzsiDfDz9STZQvAaSRIDBCIZVsb10di6vHMue3Y+oUIu3jQXYu
WMK84rjq+qtzm2v2/K7/vq1uUkWxd8PZIS3fRDMDlY6bmN1YX7+sdpFv1/+tr5JmWzaApub2x3GN
eK5ooky/3Jei2PnJfdDmy8ow+xLZ6uWCEN8GTvkRZ/CHxaelZWQXffs/ab+NSF8Llj8BTiHog+jp
0qiuN8ru10/8m6JRnbwEPe65Jc2ox/pkrF1Kuq6yqmo0Udeui7YTrisVVGazHT9hUROiqOhfzxob
WaVWH91smUgBxyniuACjGaryadw+0LYmZrfVj2tYsjtzE01lXahMbUB3idKpjKTWISnx60YJ+LuD
6l43lLkDSXW6MFDm5WsyQonQ/0fUM22n8Kxbsc/fgrLZeCuZKoYp+3J1fQSmWy9qvHKEg8T9deKe
juY9T5XcmfgTvVLpfdGMPkEPQ1APSdX/dE6aqknRDO1FcLQZ9/fjvsDujj2/PRJ/UEiFe/OoWwQq
h+kKrQZbbxdE/VUV1tc7ifg8bMhxJBYq4TyfoIkNIvUkO1hA2IArO9YuwI12XVPikH8/6V/fAVFx
JxnfoiUSD5MNfWDcxl/tyMSF1Hq7njPTMEK6JIuyL186OwQHXd13VHaF1DsjEvomNANJhFR8uRtR
gyuEz3BMG4lJHxmCjjBSbhQyC1Q0CeWGEr7MfIDbX23ohACLxIpS7dHyXKmCouZhlkW0pyObpoQw
/WKmKCG9TZ7hztCN0EmZ84FylB1Un0EvqcIpn37WYX2/Ll6V71RmI9uPRXt9rTZ7GI/eLESz8nvB
tFXj6HqjtmFRC21+xub+QQu6Z7hJ7UxU7ClAfhKSegQADgVGq3CBCh5+IKTpjj3CHy8VQjs6V6eJ
lMd5kQQNpaQtXqLjrjPW3PNAPoBsU52L7CABIF52HjEm4zveAaW74S1T548a1poMuEai1UkVJeX0
EaeSksoQ0nvGujn1v5y8fFFJUiyNqBCFWjFKdoEvz5zS35duSlVpzdFuVXOYk2R33KJJKkLN1EOn
KPCS9Xt6Gyo8qHPMHNiE6Bw7qdvmc+Bhm+6Gpgj9duUyBI6sUl9JDoGXZlcLrqbMOCgiIrMkuXMO
WQMJqirMxuH8tv6QCNru3zz+hUHzr3RUcumY1YufZpDVcm+YVx+o3tUcts1YBJx8Fok0n/huVbaa
zZzU1c1Qe/V2Ee1HjaDFHSG2oOWtQJM6C8vqgxqro8b5JUKMf8lGEtnGWdh3ffOZG9I2pUbdGiI9
d+qGGIm+2K8q5G/4cIgDappjqtuazy61MaWJmeh8s4q/b7ltkuPI1QmpEfwKR02d4Zr2hA3j6/8o
xfaifTAELiTzGCq0PmEQkjmcoXD8/LRl4wwGLJ+gu9vPfD0oPNrR7HsJKmwiktEBXVSg3Qsuxc92
OskX+oVCL+2zoyPQeq0IycpcZpEUOo11Wwi66FVQQmu6dxHe/bVeQl1O8uQe22l/MBobuGPCUv+k
h9z7G6fVPYdt1CHpTZU3rdwZUfAS1bNHOWpjKXCOwHNBLnSMBpEMYweO4ZmpMFHGhdRlIykfrZgQ
9DoWsMFXsxH+xsnln6ZQ3hRS77orSaS8lyRjNItNG5yqt6YHdC3hzEKXz/RoNTbnAtoUlTTlYEIy
b+9d8JU3yMSlMp155hbfh/Lffo4ZvLWquvWcxhk37ibjeA7yci7czv8L1GRIVMiNJ3TXTSUzYG6i
Df18iZvpD/Koa5eVlsahuclz7TdJ8Z9CTXz+paQ1CubQk4OXL2rdN/WaNk/TWxBCUsiUtV8S6VAm
046S8tWFTU0FC0nyV9vNqRuoOrTpfoGPaQ3xdPcWXa/8VFWw9bGtU69tZEEfsIqR7OeVYKR/PhtT
IBL1ISE0PZl882h59oJc4CY6ZL/KkbDV7MptfDezzSKf+CB0wwxe6AXjX8JG/P7CRel4d01oIs7P
aD88lc/cyEB3DrMUoURxpqyKXgbJl6kw0RKYqKyi9WLETUF4E1x0gCX2cgowk6SZJeIDHr7h74uS
E2AUbBVhPgmVsfa9KpnwfR8IexA+U38/JpAd8ZJ9RushRt6XttHYvpcr9f7bOIZxSqFchkhnJxlD
IrtrUvxithGbaslX7O/HooFH17zhcyMzMZWaCqYZ80voNnu6Eaiolc3yU5w4gtRvnC4OT6qEleyY
QT6WSfwqtgKb7tfDJmJwuL6pHIlzJNoIvqX22le1pv2w61r/ZIarJ+U6MqmaZeqhQxTBfeWSp/Y9
Q9c14vx87OmW6ajjiDi9xa9CIYICeZKUncnG6Ge7QWccYy8/3DXbnv6M4alt7M+EYqWtPuNlEmIR
Ru3MBCIXV8+d+fSeJkk0OWqoFrCwY3Ojr76tnJQzkp5P0HJQYsuUazegkS6B0QJunvNmRiBAut59
HsmTzpyGOU9nSwy7IEoIhw50KTKjwWuFGuUQdGNdfdhtbJ4t596cqPgtqTRlKrGQqmSDiwI+dxc5
5+nXOYjR72kPatVHO8Lhy+mA2t3vVdX/wH3Aeh6wmCKVsy8n1qE4Vzc0pKkbODi6ldMUQa9Cn2Ag
5W2nsP0n5bO+FMtz+XHvfEVXEsJCAFDD0jcZc6aQBX107oVtHD08dLIes3bxzq1T6x72rOS5HKqw
qMArPFgJbdPZUdaGBN9j6OZvmhY1udjeZqX3wTC0tUrmG99PJnmEgoz3uia39PWYQO8g1IYS3ex+
pTODmC9r2tr2+en85lDUicQ5sJWPnn2H0te8fQKGkkhH+QRtb20eg/ifJCOkvNLMGaSQv7RfJxtt
VvaC3/ZvjNsd945bvqR0Jcuh72luz5TXbyybq9y5qwLLlRn/DEQ7ieRJTluxCUWyQQyzOmIdV4Ut
411PM0amvPPCEhgV2yKBbkJPBxEk+lgcUdeNgINAvmpFPxUsh1wN4/xLHt4zVLWGEir31F6PsBFP
ql/qidgUo6J6toTFipkSpQaWk0ayxyRI7vn4pMimJkIBpkmEI1A1LOV5ZuMklcmjRDQGxrrqblpD
6vmaDudFxNrQ7DoNKbbisPMBHOunD55kiSQ9dz+JGalOyY0S+bUfuh7msPyQYmvoJqE31MOJa/df
jhoeIXaXr0rS3nkpRPjPAUQyP3qwguVFN2u2+2/j1i75m8WfZ65SwdqG772NU7sUyThBnLx8ZoRv
S2sUXuv6fPKKvI5B9XIK1LD4BxscuxmT5v+6XnAjYdouckzathtWlhRdXa+HQp8S3yLH6ZJon1+t
6J0TjoSIdsQaPCfUg/RG7k5IIVjj1Gu+6cS0iB6qOE8cMd8BLVMi+1tHTNig1FXOxVIisISXTixu
WpQ6IUIlRQ0ho0E+kJ0hwgwwUrFjTdR5l2NN5WXH1hnXN+C7iC9mHdSOhOB8c8PmdRf6S53DXLpP
JdZUenspFU0L0aw4vuyHpoc8w5YipYJXN4LesX27drrW70USIhuA8Gy4VYqqU5siKcx8xO2ngLnT
JoaDTkp2tP/Ri0a/O6bhni35ZibGS3JI8XYDJBUEkp2SlrH8VGrMy2/jU6iT6peGLXMSYSqeOYmE
OTKSKoCy4/tXGkbvTVZV9XiO/ueCJBE11mcGK07g4QlCDH65L1Vl2ZdZUaGSrQdeq0gmHjmp4e68
QX7OY64gvXuFWVEZLBS2ClRXh+IHDszUzeqXeOeBK+vVnTyiWspHZT3xWC4wGcDVrrln85YZdZ83
EjFx/P7ZDIXDqdP5qTuHuP1gUCpmLoJL/CGiJW9BgD/V96p7KCprOTg84UsOxkp7H+9Zdi4Hh5Xb
3kroMt5dBdrOOttz26xwdUSlTRWfVtBQbtTdYO6LdOGvhXCiNrWuRex3AxmRLQh8mjAtMxmNj+aM
YrDQgeZuBHPl0qXJ386b98CoXf1WVlZa4yHOj4KNseztSRJThA+l0J4C807bUV4FIVXcehTbbT9n
xJ7FdV1mJFuX1zb8potbc4jCjsLQNe2ceGmDSXKlkx+y5Y0TnsGxYnovOjjC9h3btaqaAXGiK60p
/spVgdIXvHU0Nzdrg+k6puFXr2Bn3m9H46eSrdOJNuSRiFLUFJeUl5+c9c2NvPWtBJx1/bk9KtrS
LRXWfZEv45U3IfZPApZ0lDizQeoVGtikYzB3eFeH1kK7/LhIpYoB2yxZVke+YBsvs/lyJGj8FyHk
j0N54zTdOg54RTDJKya6gEjDFce8iHFw4U3NMlc7ieQDn5yKmZGWzJ2Nofe+7EYYRm9NVdp1X6zg
m5/2IDp8p7Crnxl73nXDz7G+8X7FOSasX/AfjR0gerIuJ9Gmqw+IfftMkb2vEqXSZAmefnx19bCC
tdE5ElCImvGklvlhPP5H5q6PRRw7eiI+1ghdJU/0PU1NC4iJjZTluJf3nmwex5y2tUX3bS/n/RbN
kci+6jb3NjfZOkDkQjbBuzYOWSIBZRmv+dIg4x3Wn2sdZT8Qar6POor1eFhSP5BDnG4UBWk7+1rt
Z7o8JN2M3e8mjFep+Kow9QJvcEpDQr1X3st1DS3R+oHBGC+umjmybXJGmWlmh9ZkZFtEb/sayqp/
vvXxtiCVIB/CZxKv/SXwWp9s6+jD8aa88VU7Ki9+UttWzJ7tUD6aH7j0r7ySTerPlyHFHqoODzqR
N00RflQna4Y9EMs46FGhbxDPRCi1Iox2wlYvx43Qi49MDO/hXeFekx9E635V6Gprh+P+C7RPojgX
38n9DsctnHf72QO7FeDXrl7ywZYZ879uJxPD8Sb9veLQj2G6eksf28xZSFxXq4OJ+Gdl9nzzJNod
+hLrrooWFpbFsJOOy4qhL3fJ4uqimM54KPYVX9eW035M1gvZpRBoTJPaQl+5y75Arn+apuwOJRqO
kbxpgWtywyKJkz7Vg7kKeu2ZYxSbM3uutGCRmpGy6kbGFXnx/UmrF4tpULSGotBax6y+U0K2ZYVt
xx3GPJ3z/2MfQe4BxRxr+j8DAzkE/dBDD1WHQiFz1qxZzf8zUNCzVS5YwKkJVHPPeh1rfbQwkEPQ
ELP4UZ974okn/nP69OmSPct7/fa3vw3269dvCg8HW5b19LRp014tF+jHHntsMHa3nN7gJaIH4zTy
Qk2+u8TBX2DuraXGg/kGAO9UupGN6h5n4zfD5tU+/Lzp4osvLvkaKdZ7Fm3Po99bbW1t6y677LKc
oDVtxk6aNKk/RL0im6iF0KdOnXqGjCHquaOjY32+MYqtad26dX3j8fj5tJH3WPTlbWcdeLeC1z8j
YJpK4aPQc+A+GVyMk+J22iSAUU4mv3Hdddc1cRWsiyg0HngfWlVVNZFx4sFgcNXkyZO7XlYiArGi
okLoYhDP98disZXgoaDNmz0HsI7mt1msfQT+2X7GWLthw4YnwXdZcOYzOWxK9T7KQJMgvM+AyLz2
3bBhw8Ig/wbaTmDirwBE2QQNwZ7O+D8GwUEhYvp2JieI2lC6Ztt38ltJgg4EArxSSv8x41WBAFmw
ZH1EenJYxpChO5jnZr6XJOhEIjESxvw2bV3G/TSfq7KRDVyXQWBjZ86cKf7EwawYX8aOHXuczMUY
lzHWr1pbW5/sCQGC6ytofy1rGcNnxD/5nspERhl3J8+lzPVu9qMreVHu+MAtTH87t1/czGecPWv+
yU9+8hjz3IXwKImfzLkgWIHx+4xxIJlMPs/fPkH/+te/rhw4cOA3wP0X2A+JrNwWiXBKv4xLBALC
Yi59vwV8o8GjITKKOfbAMD+H2e/gsyRj5CNoiXLEGHAiwP4YjqljwTuyYYpGSZLpehUIqQb4nr7q
S+aVt+8IAYrT0g7gEmcVIuSNtx4hoNIXbQ/QdhsbVsmnlB2OBh6hgpeBSaRQlGfdTkoXGrWysvJZ
GPRFJM4F9L2IzXn66quv7gqVgdABIHsy85zN2k9jnG5OLUwgm/zP9I3R5skrr7yyrI0cTfXY4sWL
r5eN5O5N/530/y/Gep07wj2RNY2H0Bfy7GQI+waIukdFTaLBGEMYTmBrFEnN5xmMeS3MN4gxv8SY
ZeFJ8Aec1ER5x6Vx6QsjNLY1ePDg6xn7Jp7xrhHvFqTzTxm3ZLhR+qPdxgDfdxl6BOv8C/0f4Wd5
n+CVfN4Ezrfw+f9LUUU+gpYaYgnHCeAXM3iSBf8rgHU7niQD00bUl/xZOh+eAQlIdOTNvAC7lfGv
YeEvid3OZ6ekLqv4vbq6+i9Iwo+xWIf+Y0HmL4BpGGP+H+ZYy29GTU1NWS+AgPvfYp0PwwBTgOsi
zKmfAXLXKweYg5fB6GPBSS9+pw5jwbpOswOVGBTJzJosmOJxnheJgXTfkrvuuuvT9KtnTl514P6c
zzthjtdXrVqVGDVqlAEcEeb9FOuqp92nETL7me+GCRMmlMUw6dnsdNHPeub4cnt7+z6k7OWs9S7G
nMVvM2hX9uFdYBGhk+COM0a0sbFR4Pwac/w7v8mLYb+5b9++78PUZVepiBZh3SP43Ao8n16/fn0T
mjAAPskoqhrusiIlBaMcQqjyxloWfDmDdWAb/Ru2UFmSsxQXZXA2NBx7g3FFmvb4YlMlcuyrYDSJ
OLG8z94RzbG/J3Zb58SsdRUIfQmiPY2/xQ70CVqkT//+/Wcwbi8xA2gzHRPj//HoLXkOsZ/IPop0
FngeLtehBuaRjHcddw0M+HNwITj2cQGDyYdIC5HGP4LZhDHvBrbP7t+/X5jmoZ4gTAQUt2jfDwQ3
rOl3xx9//Gzm/iRwn9qTsTrbQsAi8YO7d+/+KnDdyndQYy+AmO+EmMs+FI1w0DE3wumaD2GCA2mb
WaT7HT2BrSBBpxGwDADPQ/p8Eo7WQepXe6ruCgEjWoBnEXEgGHeXSGzmIiHGGxhse+chzGOJqvOp
gJrRniChsy1Eu23Pnj1PgdjT2Oh/wsx4EMJqQfpIhdel3AKbHBM6F7ipw0gRNL9djHQZwudzfH20
3LlpP5P1nsbnLu4fFmNsiOQ+4LhENAHzX46p8sghOHQu6/OlJmNV8NFfnHKusrRYxrpEk4qUpjZZ
zRcmA6Y21nALWvOH7F2puttuKBLihQbWi/YBH+cyzt0rVqxYwh5szmcZFMNvIYIGPl/7/w6g74eD
lzDRJ5iwg02+kU0Wh6hw4WqJHRXiZVzhvlOZ51diN4NYcQjF9hJzQ+oRflEuYRypdrW1tQ7S5hHW
y3F6bSqS9wzGXsc9QRwVfn8a+N4B/iuA96NIuRUQBkfHFWlm4SP3MTagW7asGGyMJYwTFE9emLhY
W7HJMUNW0/Yy+tTee++9x6X3odzlC+H2QSqf9+ijj7owxuWscQZrEnhljWVfwgSyXvqLhqnj7xi/
3YKZcEe50YjsyWDYP4PLb0IDX0c4XAqtXUybxyH0P/D5YLnOcKnESoCBloLIGhb+fTbys6hFF/Pj
JmzXA0i0spGQ3TDtvElZ6Sv8LRLCTHNRgnn+ZjFw1vc0DuIz4j9AABex1ufF3mTjxDZcJvDy+yw+
L0Qa3cHvZwP3ORDkbu4He4IQ5uCfkvDlwm42s6RUY653xBSkT4QNF4exW6SlBPPY9B9D358BP+8I
NAcyFuWa3g9YR1n2afb4wC/mnWgoYeZi9bYl0SL2NqbH3ZgeLwPjFYx7ITDOZuwLwftECPsb0GJJ
k7cUQfuqG+P8Puw9cRZvh7M/z2TthGNuY/MlOlES2OwGAClHbiRkt1XsRv5+jVvixlR4mh5x4LI9
7h5PXqKDxI5Z63L2ZzpruxTHciuf00GqmBcS2XiDZ8+D9ElIktnSTkJMwP7EM888s4WYd9kgMYbv
/PLZFwlVtJouPWhfYQDmbGXOou+nyAeEOHH8Phwcm6znBca5gXmf6qmJIEQs5h1jNHOv5/tFEPdN
+DR7Gf++shGQ1TDtZD+O07sWgSkx/bms98uAezV/b8f5vF20aLHxSxF0F7WuWbPmPrgnAOA/YJKv
QIjVTMJh0tTZwUO4pFNUiBmEvnEI/f+aXVZINIAJarn/nQ07jo17nM3fJs4O0uIJIWhwwZtR1QCk
5QHaLGNDehTtgSheEUeSuSaTqBjIWDnh0c5FSiSBduczpxD0ywiUHjE98Mper2e+LXxez10D7vf3
JBKRgXDZO4GH13dpN/P5Dsz9edbzHXCzl/3s9gaqcjZK4tAIzkqsgSh4FMbbhEm3Fc3FP76mC7yX
7Nix46cQdMvhEHRXX+EekPrLvXv3ivN1mzgCPBRPVoi+52JaOsHlIpnLWfDRbCNxdzZmNYiUWO0p
IhGZf2Wn585vYst+Fik5UjQU0nkFmcE8dWrFoWb8tYzzOuNJJu9aNvDmQvFrbPuPgfNLaZ+AiB4t
JanyzCxEKA7hvYwxDAK8kjm/l06e9VSg+PstZmM4HN5Nme5Cvo4khj+NMW9Fw70PDjeXu2cS4z//
/PO/TojuZISmRDWekL7CbBD4X0SzcPfmp1ICuHSDTKAEicuWLbuHScVeup2N6MUCDomY00ygi6Rn
QZIU0aR+WeZD3cQPUXKUi8OS7VjbH5Bmn2LT+oPMV9k4CZX5F4T8HMh/jt+HSpyez//saZpbxkHr
vYBj90sI7FaIez4+iUj/e9AEu4V5RGoRHuwF8V3OfavY3GiD38E8PQrZdcItUQnw3YwZdQv7Jsma
KcB/Iyr+unQItCReshqIMKpi7W8A983A9ktwM5YQ5C04ntdgfklyqJxLxplB31r6vo7f8mfGlACE
JO6msm7KXr3taKWS+Ykcihc7Fk6jvtWPgOVIz7Sd8x8sIMRk3xEkp/yB8i82R2o2pMMp/P0dENHC
3yZqV6IcHgiXjNDvyx/RbynMIHCLrZ//9GoPBgS5G4FxE2OJUfyUJF46u0vtQjricIkgmt/9f26h
p5fgks27G2YZCNxfZs6bILBZqNk/gd+dzNOb8SeD50tlP8DTCjb8W4cQt5c6AMkryH6Lo98E/N/m
+8/5/XMkWprQDj8rN3YMXujmp9EDwOTTCGP+mVDbAuC9lz29kJ9uZQ3zy3HkIFQU/97fAM93Weu/
4IQHgW8DY0zn+5XMF2W++8FHzwkaYhKCkIC5eN0FbcJBgwYtRg0KAdVzS0yz7EucQgBt5Q6JQ8Fc
EuAQSS+IkuSI1I/0lKBlTKndiElEomxgCjQEeTGQ+jCwnEOTldnNmOdJ5hFVvZxw1W427pCmFMmO
JLoZif8qePkiaxAz52wxZTJ8k50Q8h+Z4EdIvZ6aB348kTHFLhWTwydApPyDRHMku/plHl8HE0kJ
gq/qy7mAr4O+3bKVs2fPXgbORGvdDOzzuHeytu+Ay6Jx7nS4VDTVcJhatOIXGP8LAgefkqH+IUwv
5QAlrxwJTYThPTjkDoAaAhIKVtulzY8fAWxIbExxWsq161B722Cc+QArNQE+kPKZ5nxJi0uCokcX
cO9EwsthrWo0zCFXpmVOivZZhQQ6EXhzwlqYBa9ABL+j/cOHW06a3vDFSDRxpqaACym6Oh58HGBz
XwEfG2CYQ37TEv3XwSxfY2wp0vczkTBSlOlul2gHc1XRRsJmZZXG0l4iJDcxTFzoJRNnMMov2Aep
RxnCvR8zUoqUSiZupJZEwsFpv2KSRH7ot4sxVvfEycwh6LQ6K4sb2EgbDvwRQFfOnTuXfwKiPDpi
DgnmH9HECWPKsbG8L17sEWdkNIbpdmLP/2Dz5s2SLu42jDhvrP2uXbt2HbGYORsnJ+T9U/LYtQFU
ePJwmUXGwkHLe3IlbQ507QM2fVnhqnSxWt6ITLrkoGQRUb49SfshUlOyVNaPXV92LUjneCW9xlLE
kK6FbSuXmEuN9/f0PO0ovZNNzJ0wsnZhor/KJZvJ/VcZu9CgR4J5jhTAh0LMMvdhE/SRWsCxcY5h
4Ehg4BhBHwksHhvj7wYD/w239k0cxBsWqwAAAABJRU5ErkJggg==

--_007_DM6PR02MB575303D4FA8371543DD39D88B9350DM6PR02MB5753namp_
Content-Type: image/png; name="image002.png"
Content-Description: image002.png
Content-Disposition: inline; filename="image002.png"; size=309;
	creation-date="Mon, 13 Jan 2020 23:12:34 GMT";
	modification-date="Mon, 13 Jan 2020 23:12:34 GMT"
Content-ID: <image002.png@01D5CA34.A5688BE0>
Content-Transfer-Encoding: base64

iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAA
AXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADKSURBVHgBzZK9CsIwFIVPpUMFLcUp4A9ugnVw
cXFwcvANXH0E36D4AO6+goN7RycLTo46CAV1cdDqYkFBm4AQkoKBCPrBIeGQHO7NjUE8/wkNMtDk
DwOWww5mgxZUMUXDXx+xi25QxUwzy04WDWJj1KshCM+okxzzPX8jhUsBLsmz1bZMtKsFFrYIT+g3
i6gk++4k+FwBz3R1wHi+ZRW5iUSUp3CJ76m+9hgN8SvT3qn2UYySY+EaP5h4n0d6g/cFCn+Y97/a
wu8DXrFyPoj9lhXCAAAAAElFTkSuQmCC

--_007_DM6PR02MB575303D4FA8371543DD39D88B9350DM6PR02MB5753namp_
Content-Type: image/png; name="image003.png"
Content-Description: image003.png
Content-Disposition: inline; filename="image003.png"; size=265;
	creation-date="Mon, 13 Jan 2020 23:12:34 GMT";
	modification-date="Mon, 13 Jan 2020 23:12:34 GMT"
Content-ID: <image003.png@01D5CA34.A5688BE0>
Content-Transfer-Encoding: base64

iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAA
AXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAACeSURBVHgB7dMtD4JQFAbgF2c0qRsmkkGTm9Gs
f0CTSf8PWZObBEYmQ4VCIxIoJNgYha+NwselAuPuFgJvuW+4e84ph7u+pRIMWYAxEwcuZwHq5wn9
/wK/WXX+WQ4B99sRcVbgq1hI84JuA37bTkxqwHYC8lIBsvjA6bDDXliT3pdeQPwZCMIErheRTg1o
pgu/BprVm04NjA0zwM23gArYljFWwxC+lQAAAABJRU5ErkJggg==

--_007_DM6PR02MB575303D4FA8371543DD39D88B9350DM6PR02MB5753namp_
Content-Type: image/png; name="image004.png"
Content-Description: image004.png
Content-Disposition: inline; filename="image004.png"; size=309;
	creation-date="Mon, 13 Jan 2020 23:12:34 GMT";
	modification-date="Mon, 13 Jan 2020 23:12:34 GMT"
Content-ID: <image004.png@01D5CA34.A5688BE0>
Content-Transfer-Encoding: base64

iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAA
AXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADKSURBVHgB7ZO9CoJQGIZfTQMlRQiHaCloCaeG
am3qAhy6i7qB9sC5W2hpcWj1CtqabGioQRtain5MErEUkvwJB7fomb7zwfecj/NyiMZ87yEHJHLy
i4KuWIycRxKHzaACpS1ArjHZArnOYNYroylQ4GkSQ6kU9qtsISGg4o3l4QGlw2LRF3Fxogmru3v2
Bp9DHE2EtXFzYVputkAz7WCLOOrWQhqpKUxW5+DGN/rRwVS/pgoSb+CnMG7xr8rD+uRAM+yvwz7E
/y/gCUCGOobckm/NAAAAAElFTkSuQmCC

--_007_DM6PR02MB575303D4FA8371543DD39D88B9350DM6PR02MB5753namp_--
