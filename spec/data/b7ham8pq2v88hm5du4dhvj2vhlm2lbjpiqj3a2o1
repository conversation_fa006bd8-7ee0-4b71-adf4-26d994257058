Return-Path: <<EMAIL>>
Received: from mail-ej1-f47.google.com (mail-ej1-f47.google.com [*************])
 by inbound-smtp.us-west-2.amazonaws.com with SMTP id b7ham8pq2v88hm5du4dhvj2vhlm2lbjpiqj3a2o1
 for <EMAIL>;
 Mon, 14 Jun 2021 15:21:27 +0000 (UTC)
X-SES-Spam-Verdict: PASS
X-SES-Virus-Verdict: PASS
Received-SPF: pass (spfCheck: domain of _spf.google.com designates ************* as permitted sender) client-ip=*************; envelope-from=<EMAIL>; helo=mail-ej1-f47.google.com;
Authentication-Results: amazonses.com;
 spf=pass (spfCheck: domain of _spf.google.com designates ************* as permitted sender) client-ip=*************; envelope-from=<EMAIL>; helo=mail-ej1-f47.google.com;
 dkim=pass header.i=@gmail.com;
 dmarc=pass header.from=gmail.com;
X-SES-RECEIPT: AEFBQUFBQUFBQUFIa01kSENUcmZoQ3Fmd2dvdUlFYmcrTFhqVGs3YU1hdlZYN2VDVUkyZCtQWFhPT3M5aWliZDFoQ3ErRDRVb1RtMURPcWxZUkN4VHdHZnEvMU1LQlloYzIyYjE0UXZPQW5zMm9BVW5rOGF4OWxtbnpiU0plcFNhZzB3SFFFWnladENiNjFyd0R1bGxTc1RYZW9pSDhaM2ZTRjdMdzVFUVlsc3hWeE9XWlNmWk1Eekp2bFdiTWlHWDh3Y2JlTzNSbXRLRHIyMmc4MDdCaGNCT0pPRGpaZGsyMXVadTlIbWVXRWRQeUZ5TW1jNDNuSzBIV3ZPM1NxTUQvVEFIWElSWE1SSnJCRVp4Y1hLMlVET3g1NnByS1BBby9kbTNvR3FFRHI2K1dPMmkyeTcxN2c9PQ==
X-SES-DKIM-SIGNATURE: a=rsa-sha256; q=dns/txt; b=Lqwe8Vo7mZF/DtmrMDT/J7LJJpBXEyqdBT0/WS2dOO1hbowQNCIkWVy/JY3QzFfdcRrrEwHDmkCCjKRjxlA22R51S30P3htsPIb1ObkRMl77S6KraF5pqcanRTUfhbcK80Z4N4DA0YxiQxPdt2bQgSb8Wl1tvPgOgLmYwPvk03M=; c=relaxed/simple; s=7v7vs6w47njt4pimodk5mmttbegzsi6n; d=amazonses.com; t=**********; v=1; bh=9Vnr7icKv3Q8O9TTC/MbrSpPSainsGjDN8ovxLp6oc8=; h=From:To:Cc:Bcc:Subject:Date:Message-ID:MIME-Version:Content-Type:X-SES-RECEIPT;
Received: by mail-ej1-f47.google.com with SMTP id g8so17339255ejx.1
        for <<EMAIL>>; Mon, 14 Jun 2021 08:21:27 -0700 (PDT)
DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;
        d=gmail.com; s=20161025;
        h=mime-version:references:in-reply-to:from:date:message-id:subject:to;
        bh=GzpQa7WH7FRJ7eVbcqFa3xVJVHRzENR9kKUYkzsCLIo=;
        b=UIx33B9q0heyu8rbe+6rKw/828NcxRobjUT7aEdJs3n/fjd9G4O0lOQUeQ8QjihOvq
         tyxdGs45cCrKhJP5+KonG8YSYZd+zGD7nmy+rE+aNtVZpmxiajFu3M3STOCywC/OCqfM
         a2SfjRqpf4LxL7sFFAmTK4U6mZpZFX9XvzJcK/VRUaviCbYlzYK/Lzy3Ihmv28Lw6EYm
         VHRBtJg3tRm0n7/HqSUCiRQgZAuFf/+uqXeaUpfGtAU0pw0lRXTkPo1a9oQqU9H9Rchs
         3RyN6eWTQcBiPzhXLkZOdfIFNBoe/z2Q7y8lJTEX6/ZY7x2hCAMRJY/SLGcdGT1j8oQm
         UvMg==
X-Google-DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;
        d=1e100.net; s=20161025;
        h=x-gm-message-state:mime-version:references:in-reply-to:from:date
         :message-id:subject:to;
        bh=GzpQa7WH7FRJ7eVbcqFa3xVJVHRzENR9kKUYkzsCLIo=;
        b=XSNP+vEp9Z81eECPl4PJLi/LIOUoGPIJwZ+YH0ctG+tbG5M9QeLbZLoWrhUa0WglDl
         HJSLwght8FK7FsT6ptxqsGnRI0iOnPKrjs4HwMqJYq642gy/bEorhfPjZMbSjfBpyOH8
         FHLdvb5bgJgPca9UsoCCQXgcL+nqksS2MHQ3oIh58rQP3YssidIS13fZCX1rWje7U2wA
         NlFDMz6a9Iy5QkkgcoiP14SluhEZ+qfmi99ux81shPM00x1AOJ/OqJ0Vk+BOlDzTPcUI
         4NeJIgsIeqRC27S4tcsKD715XokC1rhQH2hVasy0DWRAb+6mjuccOAmOPBV00tXBU3Qx
         czag==
X-Gm-Message-State: AOAM532klzsM0kVtto+Nr/3loL2junAEIwpyx1nEb6gF+HoZpxxhlpGu
	LNZzKwj0FcRC5OTu67FIdJlRr6yeadJt5iqI1gCRqu06
X-Google-Smtp-Source: ABdhPJzqq0DtL1tjAMxovYZXFIE0OG4Javmt3HuHM5qHgz8FVlFiMxQqEwp+cwbPagmjGxzQr633BHwUnafjMLer8/I=
X-Received: by 2002:a17:906:b55:: with SMTP id v21mr15702720ejg.88.1623684085024;
 Mon, 14 Jun 2021 08:21:25 -0700 (PDT)
MIME-Version: 1.0
References: <CA+MGk8RRQ8Af65scAxT=<EMAIL>>
 <CA+MGk8QzfAcjfpE0JqkR0OTx6B3LZagieX-J0BOr6j=L=<EMAIL>>
In-Reply-To: <CA+MGk8QzfAcjfpE0JqkR0OTx6B3LZagieX-J0BOr6j=L=<EMAIL>>
From: Bob Lasch <<EMAIL>>
Date: Mon, 14 Jun 2021 10:21:12 -0500
Message-ID: <<EMAIL>>
Subject: Re: My Mouse is too small.
To: Help Desk <<EMAIL>>,
	Robert Lasch <<EMAIL>>
Content-Type: multipart/alternative; boundary="0000000000001d8a7a05c4bb69a2"

--0000000000001d8a7a05c4bb69a2
Content-Type: text/plain; charset="UTF-8"

I can't believe we're talking about this.

On Mon, Jun 14, 2021 at 10:20 AM Bob Lasch <<EMAIL>>
wrote:

>
> Really?  Is this really an issue?
>
> On Mon, Jun 14, 2021 at 10:19 AM Bob Lasch <<EMAIL>> wrote:
>
>> It's hard to use.
>>
>

--0000000000001d8a7a05c4bb69a2
Content-Type: text/html; charset="UTF-8"
Content-Transfer-Encoding: quoted-printable

<div dir=3D"ltr"><br><div>I can&#39;t believe we&#39;re talking about this.=
</div></div><br><div class=3D"gmail_quote"><div dir=3D"ltr" class=3D"gmail_=
attr">On Mon, Jun 14, 2021 at 10:20 AM Bob Lasch &lt;<a href=3D"mailto:rlas=
<EMAIL>"><EMAIL></a>&gt; wrote:<br>=
</div><blockquote class=3D"gmail_quote" style=3D"margin:0px 0px 0px 0.8ex;b=
order-left:1px solid rgb(204,204,204);padding-left:1ex"><div dir=3D"ltr"><b=
r><div>Really?=C2=A0 Is this really an issue?</div></div><br><div class=3D"=
gmail_quote"><div dir=3D"ltr" class=3D"gmail_attr">On Mon, Jun 14, 2021 at =
10:19 AM Bob Lasch &lt;<a href=3D"mailto:<EMAIL>" target=3D"_blank=
"><EMAIL></a>&gt; wrote:<br></div><blockquote class=3D"gmail_quote=
" style=3D"margin:0px 0px 0px 0.8ex;border-left:1px solid rgb(204,204,204);=
padding-left:1ex"><div dir=3D"ltr">It&#39;s hard to use.</div>
</blockquote></div>
</blockquote></div>

--0000000000001d8a7a05c4bb69a2--
