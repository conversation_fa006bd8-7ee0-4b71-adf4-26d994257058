Return-Path: <<EMAIL>>
Received: from mail-qk1-f176.google.com (mail-qk1-f176.google.com [**************]) by inbound-smtp.us-west-2.amazonaws.com with SMTP id 8l60nj148tok84jf659cnj0e86gea2jg9jdkgtg1 for <EMAIL>; Wed, 12 May 2021 13:08:14 +0000
Received: by mail-qk1-f176.google.com with SMTP id k127so22031876qkc.6        for <<EMAIL>>; Wed, 12 May 2021 06:08:14 -0700
Date: Wed, 12 May 2021 18:08:00 +0500
From: <EMAIL>
To: <EMAIL>
Message-ID: <<EMAIL>>
In-Reply-To: <<EMAIL>>
References: <<EMAIL>>
Subject: New Comment
Mime-Version: 1.0
Content-Type: multipart/related;
 boundary=000000000000f4cfc705c221b369
Content-Transfer-Encoding: 7bit
X-SES-Spam-Verdict: PASS
X-SES-Virus-Verdict: PASS
Received-SPF: pass (spfCheck: domain of _spf.google.com designates
 ************** as permitted sender) client-ip=**************;
 envelope-from=<EMAIL>; helo=mail-qk1-f176.google.com;
Authentication-Results: amazonses.com; spf=pass (spfCheck: domain of
 _spf.google.com designates ************** as permitted sender)
 client-ip=**************; envelope-from=<EMAIL>;
 helo=mail-qk1-f176.google.com; dkim=pass header.i=@gmail.com; dmarc=pass
 header.from=gmail.com;
X-SES-RECEIPT: AEFBQUFBQUFBQUFHb2RRNEs2WGpyeVZDMXVRS2JtcUpaTXYzZ0tnQTJBNVZzOFl1RGFVeUZEREtibnhTTy8xdnRrQjYrRCtlcXhldldzSVZzL1BubXZmRStBalY2U2JsdUJZczN3a05QalVIakdTdm5INHNjSm4wL3JtVmdDdFdOY2FEeGdJN21sYmFZbHpzMWVLMUtQOTVGSlRIUU1QK1Juc1VDcGo0a0kzak9kZEc2bHJtbXRMcmVBV1BpMXZqWXhGRS93R2dsZzJoMHlEWitpUUNvWndGYWRVVmxjUXpoRDZ0NzRPeHhQRDQ3Nzg1VkVrSVNPMTlqcTVFcTdSZHRGMWkxdnZMU1FndEtKQXNiM1UxTnExQ05ESU90VFoxMTFyZXRYRW8rRjFjekRaQXlndEdPK0E9PQ==
X-SES-DKIM-SIGNATURE: a=rsa-sha256; q=dns/txt;
 b=hZSn5cu9LWm44GyF5g8BdhiZrhOjK0A3S7u8Ul5YV6orVJjsjI5uTxXeLz0h7DAvLc7clvxpnqiLiS37w7quLim6klOICjxzfgM/He1hFW637Xl/Lr0bvutS/EMiJwsq8/uXiFSfQBEmtw8vnaYZwKxsN1xFCCLtxK+VWJU4JKs=;
 c=relaxed/simple; s=7v7vs6w47njt4pimodk5mmttbegzsi6n; d=amazonses.com;
 t=1620824895; v=1; bh=f5GUZCMBidlkJnVDJbABnxYG+BRIxXcUZM7Oxj434no=;
 h=From:To:Cc:Bcc:Subject:Date:Message-ID:MIME-Version:Content-Type:X-SES-RECEIPT;
DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;        d=gmail.com;
 s=20161025;
 h=mime-version:references:in-reply-to:from:date:message-id:subject:to;
 bh=f5GUZCMBidlkJnVDJbABnxYG+BRIxXcUZM7Oxj434no=;
 b=N1wc87CMjW/vcYvJZ9HxD9WmpdtjjoCPLwXwsxavw5j6rGjljVHeoNqcM+yvTlJsPW
 h9FBCPnKAw9H7oHBhUmDoJNw+8oLsLfIt2ef73E0xvRSSeLRl32IRkXi94dqvaCMH2Nf
 7C7ampmOXzeoSckOFst30p4LZCUjrpJ3Fx7NFRd8JcQrXiPYfTPaM9a1fFqfvR+3muxK
 aWLgq9PFlhv1vQJJlghha7yOz/NEjUec0z+qb4HoOCDNMDaUc+zVGTwMVVe79IWWYj5R
 dOs0MpRSt35hzSUF2mrxYz+3stb6K8IXgnKEDq1Cy6CoFP0ccUbGBVnX81L/XnMkPF5e
 2yew==
X-Google-DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;
 d=1e100.net; s=20161025;
 h=x-gm-message-state:mime-version:references:in-reply-to:from:date
 :message-id:subject:to;
 bh=f5GUZCMBidlkJnVDJbABnxYG+BRIxXcUZM7Oxj434no=;
 b=djIfbOXI70FYGB4O8MW0jJmj89ccrOHIuEkFqS5NB+II/n716JO2F+qmWQPc3/CaIH
 jZJ31I4WMxr6K8OQ0toocEB+pzDtIxUzqvwb2YdXiFid8lUOpvGnoziiVhIJg5AqV014
 oCe2hykeVO1U3yy2t02YxrHz3RFgeq5aDRL7k6A53sSvcb15CYaGROQSDDQ0kU8rBNjr
 7+TWrlpS7RwlyN7i7Zv8++9y+3M92RvUIX0HzHwh5wf9j0muDdI/MDhTh+F0LbO0kdWK
 Vwcf1Pzst0ymQhGw+aIvgqYtNCn/nXaufZTeUJQx2lMSa5L+NuuOZjfqUt2pN7YD7IFs
 PynA==
X-Gm-Message-State: AOAM533Nz6xJCyidm9XlBARdvJ9DWBsjyVPNACNndGdEZYNsWMbfIc+2
 p/miJcnnddtKgE/wUHrLFnQgYF01xgkvDRWOD30RWH8=
X-Google-Smtp-Source: ABdhPJzqBZ2WvmhgvTjBbcW/1tQ2IlW5YvNBERw6QbA9qMzs1vZVW+VwzOvC3Gt9WU/eYiBfdpytjQ/77DNUXUP8Tlc=
X-Received: by 2002:a37:2cc3:: with SMTP id
 s186mr14172878qkh.500.1620824892417; Wed, 12 May 2021 06:08:12 -0700 (PDT)


--000000000000f4cfc705c221b369
Content-Type: multipart/alternative;
 boundary=000000000000f4cfc605c221b368
Content-Transfer-Encoding: 7bit


--000000000000f4cfc605c221b368
Content-Type: text/plain;
 charset=UTF-8
Content-Transfer-Encoding: 7bit

[image: Selection_037.png]
[image: Selection_036.png]

>

--000000000000f4cfc605c221b368
Content-Type: text/html;
 charset=UTF-8
Content-Transfer-Encoding: quoted-printable

<div dir=3D"ltr"><div>Inserted image<br>=0D
</div>=0D
=0D
</div></div>=0D

--000000000000f4cfc605c221b368--
