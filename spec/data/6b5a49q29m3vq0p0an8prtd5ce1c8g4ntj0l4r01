Return-Path: <<EMAIL>>
Received: from mail-ej1-f53.google.com (mail-ej1-f53.google.com [*************])
 by inbound-smtp.us-west-2.amazonaws.com with SMTP id 6b5a49q29m3vq0p0an8prtd5ce1c8g4ntj0l4r01
 for <EMAIL>;
 Mon, 14 Jun 2021 15:20:49 +0000 (UTC)
X-SES-Spam-Verdict: PASS
X-SES-Virus-Verdict: PASS
Received-SPF: pass (spfCheck: domain of _spf.google.com designates ************* as permitted sender) client-ip=*************; envelope-from=<EMAIL>; helo=mail-ej1-f53.google.com;
Authentication-Results: amazonses.com;
 spf=pass (spfCheck: domain of _spf.google.com designates ************* as permitted sender) client-ip=*************; envelope-from=<EMAIL>; helo=mail-ej1-f53.google.com;
 dkim=pass header.i=@bengaltechnologies-com.20150623.gappssmtp.com;
 dmarc=none header.from=bengaltechnologies.com;
X-SES-RECEIPT: AEFBQUFBQUFBQUFGZ1RjSVJmYkhNMXdVWVdIV2ZuOUJscDJzSTRhZ0E0WGpZQkdNc1AzYjl6cWI5RGQyWnNIOWZKYStndFZ3RmRtck1CVEdDODlVd0ZTOHBUU2diNUE0eUFGZk9mSmhsK1hNRmtTTWFnc1loTkhQMnZIV2JqNlUrRzZDdTZWVEhpa1U0anNPR0JuTFVrdVdJUXcvbm5Ta3ZOdmNidWR5K0tsZERnT2h3U2t0WEttZDJTdzhGbjc0ME41dVRFclNUM0ZvZ3BJdm4yVnJ3SmdrSUZQMW45Q2FJR1crRkJxTks2SUdYeTZEcmRHZ1BWV0owQVlPTzNPU0dkRHlWMmUxUDZHbWlxcXFoUkw4ZXZjUkMzZXM0QlFtU2srajhwbGxIcnZRZ1Nra1pBRGFHN2c9PQ==
X-SES-DKIM-SIGNATURE: a=rsa-sha256; q=dns/txt; b=fTuCS6tCh444JOO9kyvvrYFKKNOJRNKGb7kJnmcOvrNda2rSgo/Acz6U86e3mlMIgiz8KbzQBQaSfxjqjcT4hhEn7q0Q7074uDVFGnwx28+ShJFw2rnPoGhK6pS4oH+QnPXdzYRIfEv4ZKKSfI0OaWyecYz24OFbiXZfl5+A9oE=; c=relaxed/simple; s=7v7vs6w47njt4pimodk5mmttbegzsi6n; d=amazonses.com; t=1623684050; v=1; bh=sMnAA0GqqCcErNIvjiQgcDa3b5LBoC8Q9GCzLB4RZsw=; h=From:To:Cc:Bcc:Subject:Date:Message-ID:MIME-Version:Content-Type:X-SES-RECEIPT;
Received: by mail-ej1-f53.google.com with SMTP id nd37so9584536ejc.3
        for <<EMAIL>>; Mon, 14 Jun 2021 08:20:49 -0700 (PDT)
DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;
        d=bengaltechnologies-com.20150623.gappssmtp.com; s=20150623;
        h=mime-version:references:in-reply-to:from:date:message-id:subject:to;
        bh=FDt8uz1Zj58gwdDGaFd9bv+GQcmN+h9vX2IKmCfzteM=;
        b=S50AGodpMkXNflpQbO7dHtS/QvFFpvzm9cJ4YiRLGMJ2/O5hFUYXVAEwZoXDKBThoX
         fdY+EHyCitSXCFDbgS4LUFTCufbN2Ssy3FMOrnb4wxBC/wNjOuwI4ZsDyzvMqa5RLSif
         tTuLY5p7G0AbgF5W5JAD8Rtx6Ebz5nNj7s6rNsXe6d+JCbPShXFRTaFasPnkputfq3cO
         0Et5GpVC+QVMRWLZLdkSEHA2wGc3nPJWo1Isr10corH9i2awJ3EKlU9eF17ErsoRXQP6
         BbHFzyaP5z9Qv5p9+UneLNvaWFZCB4Ukekoi+9PPGCa6pCf+rMViAPxaiXbKBVVzyWpg
         4paQ==
X-Google-DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;
        d=1e100.net; s=20161025;
        h=x-gm-message-state:mime-version:references:in-reply-to:from:date
         :message-id:subject:to;
        bh=FDt8uz1Zj58gwdDGaFd9bv+GQcmN+h9vX2IKmCfzteM=;
        b=Sp3O92jpyOZ9JPvWqHyXUPEByrB1oMZVj+CA17nZbVWuos87ofsqW3vjEOMV+Y4Mn0
         c25LxIz4fT90RHthJj3cahsOGJm6nSVfeal3rWZ2ycX9c6vRLokqVRJINioRr0gGuwbh
         SsJy/EDhhuwMe4897RbMRf6MuDX2xFNERJJsGTiL0evcr/+o1SW4TK1GJTMAzOc+d6KQ
         8ewhIzyqRWevuSVh3npW2uyJADbTOLIU3BGL3hFOpX47ra8eqWXppLCTDzgu1BM7BRBe
         4X5Sk/oDt67C0PwSwBBF7Zzb06ParO2BmpE9baGdtIp5mMixJR0IZQti2a5r31bMGg9O
         FzZQ==
X-Gm-Message-State: AOAM533rKOxqcekyyEwc+cJJHXvM0oz/OBTicIeRFRPndjxfa0HvXJrl
	n8y9ZXRQXEp0Zt/knatKxBirdit61t67inP1eGgsD4KO
X-Google-Smtp-Source: ABdhPJypSiEbU4iwDh49W8lyODQ+1ZaiOl1l2GbvF9fGRtM11Er+ekWOW5VFgLvscxNyNDZndAaYhb4ESQjXHXEzQwY=
X-Received: by 2002:a17:906:d0da:: with SMTP id bq26mr16186613ejb.287.1623684047258;
 Mon, 14 Jun 2021 08:20:47 -0700 (PDT)
MIME-Version: 1.0
References: <CA+MGk8RRQ8Af65scAxT=<EMAIL>>
In-Reply-To: <CA+MGk8RRQ8Af65scAxT=<EMAIL>>
From: Bob Lasch <<EMAIL>>
Date: Mon, 14 Jun 2021 10:20:34 -0500
Message-ID: <CA+MGk8QzfAcjfpE0JqkR0OTx6B3LZagieX-J0BOr6j=L=<EMAIL>>
Subject: Re: My Mouse is too small.
To: Robert Lasch <<EMAIL>>,
	Help Desk <<EMAIL>>
Content-Type: multipart/alternative; boundary="000000000000dd491705c4bb6600"

--000000000000dd491705c4bb6600
Content-Type: text/plain; charset="UTF-8"

Really?  Is this really an issue?

On Mon, Jun 14, 2021 at 10:19 AM Bob Lasch <<EMAIL>> wrote:

> It's hard to use.
>

--000000000000dd491705c4bb6600
Content-Type: text/html; charset="UTF-8"
Content-Transfer-Encoding: quoted-printable

<div dir=3D"ltr"><br><div>Really?=C2=A0 Is this really an issue?</div></div=
><br><div class=3D"gmail_quote"><div dir=3D"ltr" class=3D"gmail_attr">On Mo=
n, Jun 14, 2021 at 10:19 AM Bob Lasch &lt;<a href=3D"mailto:<EMAIL>=
m"><EMAIL></a>&gt; wrote:<br></div><blockquote class=3D"gmail_quot=
e" style=3D"margin:0px 0px 0px 0.8ex;border-left:1px solid rgb(204,204,204)=
;padding-left:1ex"><div dir=3D"ltr">It&#39;s hard to use.</div>
</blockquote></div>

--000000000000dd491705c4bb6600--
