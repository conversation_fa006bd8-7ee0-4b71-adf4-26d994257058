Return-Path: <helptest+caf_=helpdesk=<EMAIL>>
Received: from mail-ot1-f44.google.com (mail-ot1-f44.google.com [*************])
 by inbound-smtp.us-west-2.amazonaws.com with SMTP id 7p573dmak5s9b50ba8gajnt0tk65us2bd5rebmo1
 for <EMAIL>;
 Mon, 13 Jan 2020 22:04:53 +0000 (UTC)
X-SES-Spam-Verdict: PASS
X-SES-Virus-Verdict: PASS
Received-SPF: pass (spfCheck: domain of northlakechristian.org designates ************* as permitted sender) client-ip=*************; envelope-from=helptest+caf_=helpdesk=<EMAIL>; helo=mail-ot1-f44.google.com;
Authentication-Results: amazonses.com;
 spf=pass (spfCheck: domain of northlakechristian.org designates ************* as permitted sender) client-ip=*************; envelope-from=helptest+caf_=helpdesk=<EMAIL>; helo=mail-ot1-f44.google.com;
 dkim=pass header.i=@gmail.com;
 dmarc=pass header.from=gmail.com;
X-SES-RECEIPT: AEFBQUFBQUFBQUFIR0JQSXkxNTYxYWV3OHY5OGlLSzZjNjh3cDVNT1cyTjhOVWRhTTAxODhzdU54c3BnRGxnamhqZlpUMUFXR3FTL2RjRGR4eFRnNHVGajdsUU5VT3NBbWZYd3lMZ1VyY1IwVzFNNkduWHRDaS9rcmtRL2VlbFkyNEpTdFdZSjJJWnlVMjhTZnhCYlRGQjE0M25wb3VQSUJTcS9qSUIzaW4wS3I4clFCY2hrcmtqa0ZLY0tvcVBCMkVrQ2R2TWZFa0JxUkw3NVo5WTlZaFJDQ1hOdEN2TWVXK3U2Nm1JckQxaVZiMWlWRXFHREZQTnhXNFFqTUttNjJzVkxVQ2pZd2pqb0V4aFNDWkswTGg1eDVFeFVWbjJuVHZYSWRMNEloOXcwUFFNMzRzQkhBVkJ5UnNaa1VqN3ZSazBqZmhDSGJXaUMyRFA4dy9kTEY4b0JUZEFTWTJVK01rSU43WEhlSUxyWlY2RGtxTkc2aDd3PT0=
X-SES-DKIM-SIGNATURE: a=rsa-sha256; q=dns/txt; b=ZLVo0RCBcsewiz4FHfkEjE51/tg8ZEs1qF52/haUmADFMI6K3ioiy5BH8tomOf4myhBKZJ8Xh5nCSoiyPenYqta/SHcXumL+Lg87dr1tAQ6rZdjHOPITTzp7cG+eQD8kuAGLtgaGIJP0RSlFORtSH68vOUgAEvY0BxxmpVAjvdA=; c=relaxed/simple; s=gdwg2y3kokkkj5a55z2ilkup5wp5hhxx; d=amazonses.com; t=1578953094; v=1; bh=d56b1pZmOFPtdgno4qt1IION2CwQVooP/hdcZXzovMo=; h=From:To:Cc:Bcc:Subject:Date:Message-ID:MIME-Version:Content-Type:X-SES-RECEIPT;
Received: by mail-ot1-f44.google.com with SMTP id m2so5677213otq.3
        for <<EMAIL>>; Mon, 13 Jan 2020 14:04:53 -0800 (PST)
X-Google-DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;
        d=1e100.net; s=20161025;
        h=x-gm-message-state:delivered-to:dkim-signature:mime-version:from
         :date:message-id:subject:to;
        bh=d56b1pZmOFPtdgno4qt1IION2CwQVooP/hdcZXzovMo=;
        b=HJUN9Sm3/rph8vK6Mh2nv9t8AUXiXhtV7tOe9anx/ee6Orr5m9afvoSynhVVi72BAS
         +5OSS8gJMSuhEwTD0s8tpqHGOaDgdhQ0RezX47RZxnFT1abOZjZi7p5926prUQzRdckL
         WuBaDA5hN86wu4qncHxi8jqXNDePteF9JKWspU8OaDDUrezH9H2+c4fbugc2EOwb086G
         gW4TiVC//1d7gSDVr+tXZMI3zeR9OeWI4loCJY46f1Idt7846sdUnyMhzs3zxD3T3qOP
         IYfCgfPR/TtDY7tMZsvqTz5mhHnZbXNnCdfeMCZgHS69RAAyK7lQDGnfj6ZBaBaWfgSt
         90Ig==
X-Gm-Message-State: APjAAAXiSVVrkiDCJ7kCMHYW5Ed96AnTDsJsA1h95oULSNBtNZ5gHplt
	W7T0Gl738CTfTROblzs+ogbLaktyEmfLVadgEBl6BGZ1es0vk6hitQ==
X-Received: by 2002:a9d:1c95:: with SMTP id l21mr14440669ota.271.1578953092531;
        Mon, 13 Jan 2020 14:04:52 -0800 (PST)
X-Forwarded-To: <EMAIL>
X-Forwarded-For: <EMAIL> <EMAIL>
Delivered-To: <EMAIL>
Received: by 2002:a4a:dd82:0:0:0:0:0 with SMTP id h2csp4784956oov;
        Mon, 13 Jan 2020 14:04:51 -0800 (PST)
X-Received: by 2002:a05:6830:15ca:: with SMTP id j10mr12101628otr.190.1578953091132;
        Mon, 13 Jan 2020 14:04:51 -0800 (PST)
ARC-Seal: i=1; a=rsa-sha256; t=1578953091; cv=none;
        d=google.com; s=arc-20160816;
        b=qUS0dA52IVE6VXduLAjLMIFpFvNVXbHAtKKpmIFUWiwCQMl76Y7XaW1HEz21fOXMCG
         I8xFdg8z5zmgrJEKu6FUfyWP5p73mwkPzebLLbKfA327GG1jzsNyAYAiFSj6wCvScCYT
         Mo2KDKmFspUVcY8TZ5cb7XakelidKmFCrxZuLB9qDiT6uW54lGPcgjJO0lQ7hlqNQ6QB
         6pAT9VxI/YbKTw69P5FEDowKkgOL6r/ch1tp7xpsQfZBL2c1kju9XyklmB1iiHioQnGu
         4Xqh2tNfZaag1XghWvPYegl8kJlLFQK7GTziFa8a6NYnDidYygTIAqXAcB7eGJEyVYqj
         yHLA==
ARC-Message-Signature: i=1; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20160816;
        h=to:subject:message-id:date:from:mime-version:dkim-signature;
        bh=d56b1pZmOFPtdgno4qt1IION2CwQVooP/hdcZXzovMo=;
        b=mfF+0OJi1rLnQcVujuvcyCFJhoSAjvvSMy2mCxALQQiD8McbTeO3WSK2qa7oiW/fpD
         yMGfh6nRoXhQ1zHxfdvKsEi08R3bTCSbSqX+pisxLQ4HdS0zqjn8IcWtPVqzys8TPtE9
         2hx9KsrF2L1yfRZQIIOC5JFBBq5Us+v5H1dulnqWEorE1aCGrWdtISHFC12Yoadc5IfB
         Gqp+Ss8YvVjVMJ+I6hd90kxjb6NZmmiOTe81MlGgYiGI9PvEPT+aQVXD69xvelJZvsXm
         FzEMSpvokeU8e60y1oqpmquGSZ0eUn/z0KHO+tIom9PfkQHSu21hLFt4wGOvs0p2Z/Jg
         GCiQ==
ARC-Authentication-Results: i=1; mx.google.com;
       dkim=pass header.i=@gmail.com header.s=20161025 header.b=hOLeu+Fx;
       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;
       dmarc=pass (p=NONE sp=QUARANTINE dis=NONE) header.from=gmail.com
Return-Path: <<EMAIL>>
Received: from mail-sor-f41.google.com (mail-sor-f41.google.com. [*************])
        by mx.google.com with SMTPS id d10sor6661557otc.111.2020.***********.50
        for <<EMAIL>>
        (Google Transport Security);
        Mon, 13 Jan 2020 14:04:51 -0800 (PST)
Received-SPF: pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) client-ip=*************;
Authentication-Results: mx.google.com;
       dkim=pass header.i=@gmail.com header.s=20161025 header.b=hOLeu+Fx;
       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;
       dmarc=pass (p=NONE sp=QUARANTINE dis=NONE) header.from=gmail.com
DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;
        d=gmail.com; s=20161025;
        h=mime-version:from:date:message-id:subject:to;
        bh=d56b1pZmOFPtdgno4qt1IION2CwQVooP/hdcZXzovMo=;
        b=hOLeu+Fxs4uZLCVWvSIjQltdfnm/0cdKFPnqcwhpvj4GFLCNG84bnEx0sMUsG5ymzR
         JoRr4aszFoxd54ig1UDwXOm3pZ60f6oPTov3gDO6dAgob0mBe4ep6+pq6T2mGLzKgsXu
         Hb7Fa+ZA0wcmSLz204SSU26Ea12VHHoGQgbglkdxAuUXUpj67h7X2HXRSh7savRaQlqi
         PfdYfbW0RVKzWcPgxWWM1LfqRuaxLuhkpmsxqEvXp2mipSf6GWJAUkhCnajXrzzfokAL
         ebK2ZWBvR77E3GganABQtStfUdoXm30sPNZOPO1+uKG+UWDHUUevBjtSlm8joqRb/3Rl
         /60w==
X-Google-Smtp-Source: APXvYqydgCCJGOzJc3z4zFzxNNEDXA7yl8VlwiDHiFRgWPlCPlqOn4iysVae9dFAjtaZ0772kqPoRehjh+GYTxugYso=
X-Received: by 2002:a9d:6f85:: with SMTP id h5mr14853427otq.19.1578953090269;
 Mon, 13 Jan 2020 14:04:50 -0800 (PST)
MIME-Version: 1.0
From: Mikey Jeaux <<EMAIL>>
Date: Mon, 13 Jan 2020 16:04:39 -0600
Message-ID: <CALf=jUuGcT9zDgCjM56R0tdjWC9dSXk-MLEpwsDhqh=<EMAIL>>
Subject: Test ticket for wifi
To: <EMAIL>
Content-Type: multipart/alternative; boundary="0000000000001003ad059c0caa14"

--0000000000001003ad059c0caa14
Content-Type: text/plain; charset="UTF-8"

wifi is down

--0000000000001003ad059c0caa14
Content-Type: text/html; charset="UTF-8"

<div dir="ltr">wifi is down</div>

--0000000000001003ad059c0caa14--
