Return-Path: <<EMAIL>>
Received: from NAM11-CO1-obe.outbound.protection.outlook.com (mail-co1nam11on2121.outbound.protection.outlook.com [**************])
 by inbound-smtp.us-west-2.amazonaws.com with SMTP id c5j2obp6i6cv32nl704b3lm5ipri8hmehnqmcm01
 for <EMAIL>;
 Tue, 28 Jan 2020 17:46:50 +0000 (UTC)
X-SES-Spam-Verdict: PASS
X-SES-Virus-Verdict: PASS
Received-SPF: pass (spfCheck: domain of Leftbankart.com designates ************** as permitted sender) client-ip=**************; envelope-from=<EMAIL>; helo=mail-co1nam11on2121.outbound.protection.outlook.com;
Authentication-Results: amazonses.com;
 spf=pass (spfCheck: domain of Leftbankart.com designates ************** as permitted sender) client-ip=**************; envelope-from=<PERSON>@Leftbankart.com; helo=mail-co1nam11on2121.outbound.protection.outlook.com;
 dkim=pass header.i=@Leftbankart.onmicrosoft.com;
 dmarc=none header.from=leftbankart.com;
X-SES-RECEIPT: AEFBQUFBQUFBQUFFNWNMZkkxSVVVcnMxRWwxczA3Q3NzZmVjOUpwcTFHR1BNcEVWOWlBTEZqQkl5ODlMZitQaldMMFkzOWFiS3BKMWJhUkNJRG1iODJFRFBVZThZdytmNmlDYVFmYndpY1pUcEhyb0ExR1BmN2tIa3NTQlF2SXFtRDliZFhsR0JvMWt3MnZDMUdHT1JIL2RLWm12L3ZRa0Y1WWg2eVEvYWJpZWVnRXhCT0MySkNzRmRpYWhoVGdnU0NlY25QU1RMUWc5ZXJNT1JYWXdyRWpBd0Q4ZHc4M2U3dTdqUTVvaFFqMldNM2hqSC9lNUticlZVRTJIbklzTEI1Q0RRamVaMVRYQUZFc09ubTdydHNWeEVSSXhHL28xNWQ5RkYwYU9WU05aL3NpNk56aTFiNUE9PQ==
X-SES-DKIM-SIGNATURE: a=rsa-sha256; q=dns/txt; b=bRGNN9+sQ3pkEJnTD/7I1wS6KLEjiM3Xp+GtBv3nM4tikp0risEoR3Rjn/4f9BIBGpDyLxuu8RZ2pFTvHXxlgPANFFGgxteAYhBQxU9NckSGBxe+oM18AHB2EJ9ZTtZsvzqCJeGfjfMuNC+pheiryt6UcaDs1ydRrlspWm6nTW8=; c=relaxed/simple; s=gdwg2y3kokkkj5a55z2ilkup5wp5hhxx; d=amazonses.com; t=**********; v=1; bh=+VE/epMIcomSAxlo7Xb/gJOg2mkh0FACOz/xqByq2oo=; h=From:To:Cc:Bcc:Subject:Date:Message-ID:MIME-Version:Content-Type:X-SES-RECEIPT;
ARC-Seal: i=1; a=rsa-sha256; s=arcselector9901; d=microsoft.com; cv=none;
 b=Sj/YPlfXD/CaBGnUxzaNRX289xgAoz6XTfsqByQgpdjW1TIOjB6JpcTaMwxEsPjCaMarpXkt9tJcvqZVq4DNrAvKLAL4J8WPr9huaeZf7fLnRLLV7jlOdIU7Pm+fujfJccX4GCvICDYe7yK6tutLYiP3vrnteiW1Jvo56W1jCZUtSJ5lQvGcmqYF7nLr0HHU3xW2sKh/Jx6yvXQHfEJTNuxrbCW5YBWoK+A+iTuwHg3qc83IBXDXX2rTUhFGUCzx1PN5qBKZ5+CVVpbxUCwHjd5UPXvQwkrcVayl1a62wA+CaJGAaitzvSCFGdtzAfVcOM4c9wnFrhae+PsNMlonRg==
ARC-Message-Signature: i=1; a=rsa-sha256; c=relaxed/relaxed; d=microsoft.com;
 s=arcselector9901;
 h=From:Date:Subject:Message-ID:Content-Type:MIME-Version:X-MS-Exchange-SenderADCheck;
 bh=J5cccrsOhIOQtIUo85L1DyWPOjmcJOItVP4Wez5snIQ=;
 b=HOiRVhrT+KE8bfqOnjq14Qe8oOhQqothlAi8UE1Mv0pz1h4IfYLtCZorHIh8K+hZMjX/VBn4POmvXX1SoK/fzbJwnxgJihiyL6i51CVa54AiJGalgqYxbZBhkEbBe1XrtVJ7U40cfTMgsOnU5it3L1mVrq5j61ClVvi/vNJkaOImOqnVUuokWEbk8GCIUXC2B2hIi3okhl/lUupIwnE/DKhxLR2v8rS0awq5Jxv5KYUsEj2mvjo2gN7UiBy00IK06kND0HX2E+PLPLhAVH4FTam622aB8dS2aSmNIb+UU5cqIQKIcRMaYvayHYPUj1qyJRjG/lb53/swom2mbneTyg==
ARC-Authentication-Results: i=1; mx.microsoft.com 1; spf=pass
 smtp.mailfrom=leftbankart.com; dmarc=pass action=none
 header.from=leftbankart.com; dkim=pass header.d=leftbankart.com; arc=none
DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;
 d=Leftbankart.onmicrosoft.com; s=selector1-Leftbankart-onmicrosoft-com;
 h=From:Date:Subject:Message-ID:Content-Type:MIME-Version:X-MS-Exchange-SenderADCheck;
 bh=J5cccrsOhIOQtIUo85L1DyWPOjmcJOItVP4Wez5snIQ=;
 b=nlUsKvWnb8PHnWarQOdePKHRd0JtwfjS61Y7pDL4PdxL9VNa1f3IRc1V7fEmT1uf1w1FIG6PSmHyklPKUxdP/gQbU49yf3kteafJqc2EbN9PB8PHfZqY3JW7aI4QuttleUWyrR/0yKIW1Eitur3qnOXhbgmFu2+tf5jKpQsUGSg=
Received: from BY5PR07MB7201.namprd07.prod.outlook.com (************) by
 BY5PR07MB7250.namprd07.prod.outlook.com (*************) with Microsoft SMTP
 Server (version=TLS1_2, cipher=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384) id
 15.20.2665.22; Tue, 28 Jan 2020 17:46:47 +0000
Received: from BY5PR07MB7201.namprd07.prod.outlook.com
 ([fe80::9115:936b:1d31:d400]) by BY5PR07MB7201.namprd07.prod.outlook.com
 ([fe80::9115:936b:1d31:d400%4]) with mapi id 15.20.2665.026; Tue, 28 Jan 2020
 17:46:47 +0000
From: Jessica Olague <<EMAIL>>
To: Genuity Helpdesk <<EMAIL>>
Subject: RE: New notes added to ticket [#546]: UPS Worldship Error
Thread-Topic: New notes added to ticket [#546]: UPS Worldship Error
Thread-Index: AQHV1f6ySS3coqrak0Goj996HE7MQagAWcXw
Date: Tue, 28 Jan 2020 17:46:47 +0000
Message-ID:
 <<EMAIL>>
References:
 <<EMAIL>>
In-Reply-To:
 <<EMAIL>>
Accept-Language: en-US
Content-Language: en-US
X-MS-Has-Attach: yes
X-MS-TNEF-Correlator:
authentication-results: spf=none (sender IP is )
 smtp.mailfrom=<EMAIL>; 
x-originating-ip: [*************]
x-ms-publictraffictype: Email
x-ms-office365-filtering-correlation-id: e7740466-1d60-48dd-c7af-08d7a41a0c2d
x-ms-traffictypediagnostic: BY5PR07MB7250:
x-ld-processed: 55769ed1-63c9-4c76-b919-b397be2e4372,ExtAddr
x-microsoft-antispam-prvs:
 <<EMAIL>>
x-ms-oob-tlc-oobclassifiers: OLM:561;
x-forefront-prvs: 029651C7A1
x-forefront-antispam-report:
 SFV:NSPM;SFS:(10019020)(39850400004)(396003)(346002)(136003)(366004)(376002)(189003)(199004)(8676002)(********)(********)(********)(********)(8936002)(26005)(********)(316002)(2906002)(7696005)(********)(********)(6506007)(*********)(186003)(**********)(***********)(9686003)(********)(********)(********)(********)(********)(********)(********)(********)(6916009);DIR:OUT;SFP:1102;SCL:1;SRVR:BY5PR07MB7250;H:BY5PR07MB7201.namprd07.prod.outlook.com;FPR:;SPF:None;LANG:en;PTR:InfoNoRecords;MX:1;A:1;
received-spf: None (protection.outlook.com: Leftbankart.com does not designate
 permitted sender hosts)
x-ms-exchange-senderadcheck: 1
x-microsoft-antispam: BCL:0;
x-microsoft-antispam-message-info:
 HrXaLI4QhJgIR0K/YTaQStioVPImANDLyI62z4Um+DLNKrH60dLffRz2gA2FnrwdOtoRAU0g/5G0lg9EuYttrsHwWWw3+TAIrFCrGyuQSmvy61UJXqLBcMWjFZbuyDVNzIipFSd3BUTNXTdmqGY7mRk00ilYcuYjnaQAEUer72l6MfD4MDcN5xDljpPzYKj8Ezm7AauzdTnEa1wAH64bnQ+eOuhrTyE2dJcYm1B2OsNzZXUBLZAzYPTLcS0vEOQRl3+i2nFy+cbb4RAQgD+B3Y3WXASaSuW4OUJOH3l3sqwoWwgR+NxjOkRmeBRgqI70CUc096QQ/i750pATT/5nWQ0HLgi1AIN9qT/7hnA7NCympCD0wst6sJFrv/Cfztr2OuHOAuQwMPFm+W0cX8cPBKaqG7ROuUwkIA4iFnBj06/JBV6uT4NDjq3XQ2mwg3gQZ/7a5on8LsrNIUa6kw66oFzaSD2fidIkQVNqMD8ryFB4YwL9pAS20FdQZOjCmfq+t8lyCH2SnQNX8rCB+BPUeg==
x-ms-exchange-antispam-messagedata:
 jp2oTqsRuGC2Up/+kLfT6DNp77n5d2uE7G2zw0mHieiw9wNtc+RJEbDHXJT4pNkl8r13ks21ScmTSc2gjZMI6Yr9TAFcKwt/q1iRpowwCLIqWJJlP+wDGeC1c5xSzHnBrV8vnsBh8rsKhEVTJswMYg==
x-ms-exchange-transport-forked: True
Content-Type: multipart/related;
	boundary="_004_BY5PR07MB720162388F16D1AA0762BA21AB0A0BY5PR07MB7201namp_";
	type="multipart/alternative"
MIME-Version: 1.0
X-OriginatorOrg: Leftbankart.com
X-MS-Exchange-CrossTenant-Network-Message-Id: e7740466-1d60-48dd-c7af-08d7a41a0c2d
X-MS-Exchange-CrossTenant-originalarrivaltime: 28 Jan 2020 17:46:47.4923
 (UTC)
X-MS-Exchange-CrossTenant-fromentityheader: Hosted
X-MS-Exchange-CrossTenant-id: 55769ed1-63c9-4c76-b919-b397be2e4372
X-MS-Exchange-CrossTenant-mailboxtype: HOSTED
X-MS-Exchange-CrossTenant-userprincipalname: ScJAGQ5qbyOh16IqWyntPEbVUAeoFWc97ZPLo1DJC8V9azVh02bcQA/8KF4h0zz4ZVlhZ1rWVJm1JTyhAZrjuQ==
X-MS-Exchange-Transport-CrossTenantHeadersStamped: BY5PR07MB7250

--_004_BY5PR07MB720162388F16D1AA0762BA21AB0A0BY5PR07MB7201namp_
Content-Type: multipart/alternative;
	boundary="_000_BY5PR07MB720162388F16D1AA0762BA21AB0A0BY5PR07MB7201namp_"

--_000_BY5PR07MB720162388F16D1AA0762BA21AB0A0BY5PR07MB7201namp_
Content-Type: text/plain; charset="utf-8"
Content-Transfer-Encoding: base64

VGhhdCB3b3JrZWQuDQoNClRoYW5rIHlvdSENCg0KSmVzc2ljYSBPbGFndWUNClNoaXBwaW5nIE9m
ZmljZQ0KX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX18NClAgNTYyLTYyMy05MzI4ICBG
IDU2Mi02MjMtOTM2OQ0KX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX18NCiBMZWZ0YmFu
a0FydA0KMTQ4NjAgQWxvbmRyYSBCbHZkLjxodHRwczovL3d3dy5nb29nbGUuY29tL21hcHMvc2Vh
cmNoLzE0ODYwJTBEJTBBK0Fsb25kcmErQmx2ZC4rJTBEJTBBK0xhJTBEJTBBK01pcmFkYSwrQ0Er
OTA2Mzg/ZW50cnk9Z21haWwmc291cmNlPWc+DQpMYSBNaXJhZGEsIENBIDkwNjM4PGh0dHBzOi8v
d3d3Lmdvb2dsZS5jb20vbWFwcy9zZWFyY2gvMTQ4NjAlMEQlMEErQWxvbmRyYStCbHZkLislMEQl
MEErTGElMEQlMEErTWlyYWRhLCtDQSs5MDYzOD9lbnRyeT1nbWFpbCZzb3VyY2U9Zz4NCg0KSmVz
****************************************************************************
LmxlZnRiYW5rYXJ0LmNvbTxodHRwOi8vd3d3LmxlZnRiYW5rYXJ0LmNvbS8+DQpUaGUgaW5mb3Jt
YXRpb24gY29udGFpbmVkIGluIHRoaXMgY29tbXVuaWNhdGlvbiBpcyBjb25maWRlbnRpYWwsIG1h
eSBiZSBwcml2aWxlZ2VkIGFuZCBpcyBpbnRlbmRlZCBmb3IgdGhlIGV4Y2x1c2l2ZSB1c2Ugb2Yg
dGhlIGFib3ZlIG5hbWVkIGFkZHJlc3NlZShzKS4gSWYgeW91IGFyZSBub3QgdGhlIGludGVuZGVk
IHJlY2lwaWVudChzKSwgeW91IGFyZSBleHByZXNzbHkgcHJvaGliaXRlZCBmcm9tIGNvcHlpbmcs
IGRpc3RyaWJ1dGluZywgZGlzc2VtaW5hdGluZywgb3IgaW4gYW55IG90aGVyIHdheSB1c2luZyBh
bnkgaW5mb3JtYXRpb24gY29udGFpbmVkIHdpdGhpbiB0aGlzIGNvbW11bmljYXRpb24uIElmIHlv
dSBoYXZlIHJlY2VpdmVkIHRoaXMgY29tbXVuaWNhdGlvbiBpbiBlcnJvciBwbGVhc2UgY29udGFj
dCB0aGUgc2VuZGVyIGJ5IHRlbGVwaG9uZSBvciBieSByZXNwb25zZSB2aWEgbWFpbC4gTGVmdGJh
bmsgQXJ0IGhhcyB0YWtlbiBwcmVjYXV0aW9ucyB0byBtaW5pbWl6ZSB0aGUgcmlzayBvZiB0cmFu
c21pdHRpbmcgc29mdHdhcmUgdmlydXNlcywgYnV0IHdlIGFkdmlzZSB5b3UgdG8gY2Fycnkgb3V0
IHlvdXIgb3duIHZpcnVzIGNoZWNrcyBvbiBhbnkgYXR0YWNobWVudCB0byB0aGlzIG1lc3NhZ2Uu
IExlZnRiYW5rIEFydCBjYW5ub3QgYWNjZXB0IGxpYWJpbGl0eSBmb3IgYW55IGxvc3Mgb3IgZGFt
YWdlIGNhdXNlZCBieSBzb2Z0d2FyZSB2aXJ1c2VzLg0KDQpGcm9tOiBoZWxwZGVza0BsZWZ0YmFu
****************************************************************************
DQpTZW50OiBUdWVzZGF5LCBKYW51YXJ5IDI4LCAyMDIwIDk6MTYgQU0NClRvOiBKZXNzaWNhIE9s
****************************************************************************
dG8gdGlja2V0IFsjNTQ2XTogVVBTIFdvcmxkc2hpcCBFcnJvcg0KDQpbSW1hZ2UgcmVtb3ZlZCBi
eSBzZW5kZXIuIEdlbnVpdHldDQpVcGRhdGUgdG8gaGVscCBkZXNrIHRpY2tldCAjNTQ2Lg0KVGlj
a2V0IGFzc2lnbmVkIHRvOiBOb25lDQpQcmlvcml0eSBzZXQgdG86IExvdw0KDQpDaGFuIEhlbmcg
anVzdCBhZGRlZCBub3RlIHRvIHRpY2tldCBVUFMgV29ybGRzaGlwIEVycm9yOg0KTmV3IENvbW1l
bnQ6DQpKZXNzaWNhLA0KDQpXaWxsIHlvdSB0cnkgYWdhaW4/DQoNCg0KR28gdG8gdGlja2V0PGh0
dHBzOi8vbGVmdGJhbmstYXJ0LmdvZ2VudWl0eS5jb20vaGVscF90aWNrZXRzP3JlZmVyZW5jZT0x
NDA5OT4NCl9fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fDQpEaWRuJ3QgcmVxdWVzdCB0
aGlzIGVtYWlsPw0KU29ycnkgYWJvdXQgdGhhdCEgSnVzdCBpZ25vcmUgdGhpcyBtZXNzYWdlLg0K
DQo=

--_000_BY5PR07MB720162388F16D1AA0762BA21AB0A0BY5PR07MB7201namp_
Content-Type: text/html; charset="utf-8"
Content-Transfer-Encoding: base64

PGh0bWwgeG1sbnM6dj0idXJuOnNjaGVtYXMtbWljcm9zb2Z0LWNvbTp2bWwiIHhtbG5zOm89InVy
bjpzY2hlbWFzLW1pY3Jvc29mdC1jb206b2ZmaWNlOm9mZmljZSIgeG1sbnM6dz0idXJuOnNjaGVt
YXMtbWljcm9zb2Z0LWNvbTpvZmZpY2U6d29yZCIgeG1sbnM6bT0iaHR0cDovL3NjaGVtYXMubWlj
cm9zb2Z0LmNvbS9vZmZpY2UvMjAwNC8xMi9vbW1sIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcv
VFIvUkVDLWh0bWw0MCI+DQo8aGVhZD4NCjxtZXRhIGh0dHAtZXF1aXY9IkNvbnRlbnQtVHlwZSIg
Y29udGVudD0idGV4dC9odG1sOyBjaGFyc2V0PXV0Zi04Ij4NCjxtZXRhIG5hbWU9IkdlbmVyYXRv
ciIgY29udGVudD0iTWljcm9zb2Z0IFdvcmQgMTUgKGZpbHRlcmVkIG1lZGl1bSkiPg0KPCEtLVtp
ZiAhbXNvXT48c3R5bGU+dlw6KiB7YmVoYXZpb3I6dXJsKCNkZWZhdWx0I1ZNTCk7fQ0Kb1w6KiB7
YmVoYXZpb3I6dXJsKCNkZWZhdWx0I1ZNTCk7fQ0Kd1w6KiB7YmVoYXZpb3I6dXJsKCNkZWZhdWx0
I1ZNTCk7fQ0KLnNoYXBlIHtiZWhhdmlvcjp1cmwoI2RlZmF1bHQjVk1MKTt9DQo8L3N0eWxlPjwh
W2VuZGlmXS0tPjxzdHlsZT48IS0tDQovKiBGb250IERlZmluaXRpb25zICovDQpAZm9udC1mYWNl
DQoJe2ZvbnQtZmFtaWx5OiJDYW1icmlhIE1hdGgiOw0KCXBhbm9zZS0xOjIgNCA1IDMgNSA0IDYg
MyAyIDQ7fQ0KQGZvbnQtZmFjZQ0KCXtmb250LWZhbWlseTpDYWxpYnJpOw0KCXBhbm9zZS0xOjIg
MTUgNSAyIDIgMiA0IDMgMiA0O30NCkBmb250LWZhY2UNCgl7Zm9udC1mYW1pbHk6IkNlbnR1cnkg
R290aGljIjsNCglwYW5vc2UtMToyIDExIDUgMiAyIDIgMiAyIDIgNDt9DQpAZm9udC1mYWNlDQoJ
e2ZvbnQtZmFtaWx5OiJTZWdvZSBVSSI7DQoJcGFub3NlLTE6MiAxMSA1IDIgNCAyIDQgMiAyIDM7
fQ0KLyogU3R5bGUgRGVmaW5pdGlvbnMgKi8NCnAuTXNvTm9ybWFsLCBsaS5Nc29Ob3JtYWwsIGRp
di5Nc29Ob3JtYWwNCgl7bWFyZ2luOjBpbjsNCgltYXJnaW4tYm90dG9tOi4wMDAxcHQ7DQoJZm9u
dC1zaXplOjExLjBwdDsNCglmb250LWZhbWlseToiQ2FsaWJyaSIsc2Fucy1zZXJpZjt9DQpoNg0K
CXttc28tc3R5bGUtcHJpb3JpdHk6OTsNCgltc28tc3R5bGUtbGluazoiSGVhZGluZyA2IENoYXIi
Ow0KCW1zby1tYXJnaW4tdG9wLWFsdDphdXRvOw0KCW1hcmdpbi1yaWdodDowaW47DQoJbXNvLW1h
cmdpbi1ib3R0b20tYWx0OmF1dG87DQoJbWFyZ2luLWxlZnQ6MGluOw0KCWZvbnQtc2l6ZTo3LjVw
dDsNCglmb250LWZhbWlseToiQ2FsaWJyaSIsc2Fucy1zZXJpZjsNCglmb250LXdlaWdodDpib2xk
O30NCmE6bGluaywgc3Bhbi5Nc29IeXBlcmxpbmsNCgl7bXNvLXN0eWxlLXByaW9yaXR5Ojk5Ow0K
CWZvbnQtZmFtaWx5OiJTZWdvZSBVSSIsc2Fucy1zZXJpZjsNCgljb2xvcjpibHVlOw0KCXRleHQt
ZGVjb3JhdGlvbjp1bmRlcmxpbmU7fQ0Kc3Bhbi5IZWFkaW5nNkNoYXINCgl7bXNvLXN0eWxlLW5h
bWU6IkhlYWRpbmcgNiBDaGFyIjsNCgltc28tc3R5bGUtcHJpb3JpdHk6OTsNCgltc28tc3R5bGUt
bGluazoiSGVhZGluZyA2IjsNCglmb250LWZhbWlseToiQ2FsaWJyaSBMaWdodCIsc2Fucy1zZXJp
ZjsNCgljb2xvcjojMUYzNzYzO30NCnNwYW4uRW1haWxTdHlsZTIyDQoJe21zby1zdHlsZS10eXBl
OnBlcnNvbmFsLXJlcGx5Ow0KCWZvbnQtZmFtaWx5OiJDYWxpYnJpIixzYW5zLXNlcmlmOw0KCWNv
bG9yOndpbmRvd3RleHQ7fQ0KLk1zb0NocERlZmF1bHQNCgl7bXNvLXN0eWxlLXR5cGU6ZXhwb3J0
LW9ubHk7DQoJZm9udC1zaXplOjEwLjBwdDt9DQpAcGFnZSBXb3JkU2VjdGlvbjENCgl7c2l6ZTo4
LjVpbiAxMS4waW47DQoJbWFyZ2luOjEuMGluIDEuMGluIDEuMGluIDEuMGluO30NCmRpdi5Xb3Jk
U2VjdGlvbjENCgl7cGFnZTpXb3JkU2VjdGlvbjE7fQ0KLS0+PC9zdHlsZT48IS0tW2lmIGd0ZSBt
c28gOV0+PHhtbD4NCjxvOnNoYXBlZGVmYXVsdHMgdjpleHQ9ImVkaXQiIHNwaWRtYXg9IjEwMjYi
IC8+DQo8L3htbD48IVtlbmRpZl0tLT48IS0tW2lmIGd0ZSBtc28gOV0+PHhtbD4NCjxvOnNoYXBl
bGF5b3V0IHY6ZXh0PSJlZGl0Ij4NCjxvOmlkbWFwIHY6ZXh0PSJlZGl0IiBkYXRhPSIxIiAvPg0K
PC9vOnNoYXBlbGF5b3V0PjwveG1sPjwhW2VuZGlmXS0tPg0KPC9oZWFkPg0KPGJvZHkgYmdjb2xv
cj0iI0ZBRkFGQSIgbGFuZz0iRU4tVVMiIGxpbms9ImJsdWUiIHZsaW5rPSJwdXJwbGUiIHN0eWxl
PSItbXMtdGV4dC1zaXplLWFkanVzdDogMTAwJTstd2Via2l0LXRleHQtc2l6ZS1hZGp1c3Q6IDEw
MCUiPg0KPGRpdiBjbGFzcz0iV29yZFNlY3Rpb24xIj4NCjxwIGNsYXNzPSJNc29Ob3JtYWwiPlRo
YXQgd29ya2VkLjxvOnA+PC9vOnA+PC9wPg0KPHAgY2xhc3M9Ik1zb05vcm1hbCI+PG86cD4mbmJz
cDs8L286cD48L3A+DQo8ZGl2Pg0KPHAgY2xhc3M9Ik1zb05vcm1hbCI+PHNwYW4gc3R5bGU9ImZv
bnQtc2l6ZToxMi4wcHQ7Zm9udC1mYW1pbHk6JnF1b3Q7Q2VudHVyeSBHb3RoaWMmcXVvdDssc2Fu
cy1zZXJpZjtjb2xvcjojMjYyNjI2O2JvcmRlcjpub25lIHdpbmRvd3RleHQgMS4wcHQ7cGFkZGlu
ZzowaW4iPlRoYW5rIHlvdSE8L3NwYW4+PHNwYW4gc3R5bGU9ImNvbG9yOmJsYWNrIj48bzpwPjwv
bzpwPjwvc3Bhbj48L3A+DQo8cCBjbGFzcz0iTXNvTm9ybWFsIj48c3BhbiBzdHlsZT0iY29sb3I6
YmxhY2siPjxvOnA+Jm5ic3A7PC9vOnA+PC9zcGFuPjwvcD4NCjxwIGNsYXNzPSJNc29Ob3JtYWwi
PjxiPjxzcGFuIHN0eWxlPSJmb250LXNpemU6MTQuMHB0O2ZvbnQtZmFtaWx5OiZxdW90O0NlbnR1
cnkgR290aGljJnF1b3Q7LHNhbnMtc2VyaWY7Y29sb3I6IzI2MjYyNjtib3JkZXI6bm9uZSB3aW5k
b3d0ZXh0IDEuMHB0O3BhZGRpbmc6MGluIj5KZXNzaWNhIE9sYWd1ZTwvc3Bhbj48L2I+PHNwYW4g
c3R5bGU9ImNvbG9yOmJsYWNrIj48bzpwPjwvbzpwPjwvc3Bhbj48L3A+DQo8cCBjbGFzcz0iTXNv
Tm9ybWFsIj48Yj48c3BhbiBzdHlsZT0iZm9udC1mYW1pbHk6JnF1b3Q7Q2VudHVyeSBHb3RoaWMm
cXVvdDssc2Fucy1zZXJpZjtjb2xvcjpncmF5O2JvcmRlcjpub25lIHdpbmRvd3RleHQgMS4wcHQ7
cGFkZGluZzowaW4iPlNoaXBwaW5nIE9mZmljZTwvc3Bhbj48L2I+PHNwYW4gc3R5bGU9ImNvbG9y
OmJsYWNrIj48bzpwPjwvbzpwPjwvc3Bhbj48L3A+DQo8ZGl2Pg0KPGRpdiBjbGFzcz0iTXNvTm9y
bWFsIiBzdHlsZT0idmVydGljYWwtYWxpZ246YmFzZWxpbmUiPjxzcGFuIHN0eWxlPSJmb250LXNp
emU6MTAuMHB0O2ZvbnQtZmFtaWx5OiZxdW90O0NlbnR1cnkgR290aGljJnF1b3Q7LHNhbnMtc2Vy
aWY7Y29sb3I6Z3JheTtib3JkZXI6bm9uZSB3aW5kb3d0ZXh0IDEuMHB0O3BhZGRpbmc6MGluIj4N
CjxociBzaXplPSIxIiB3aWR0aD0iMTklIiBhbGlnbj0ibGVmdCI+DQo8L3NwYW4+PC9kaXY+DQo8
L2Rpdj4NCjxwIGNsYXNzPSJNc29Ob3JtYWwiPjxiPjxzcGFuIHN0eWxlPSJmb250LXNpemU6MTIu
MHB0O2ZvbnQtZmFtaWx5OiZxdW90O0NlbnR1cnkgR290aGljJnF1b3Q7LHNhbnMtc2VyaWY7Y29s
b3I6IzI2MjYyNjtib3JkZXI6bm9uZSB3aW5kb3d0ZXh0IDEuMHB0O3BhZGRpbmc6MGluIj5QPC9z
cGFuPjwvYj48Yj48c3BhbiBzdHlsZT0iZm9udC1zaXplOjEyLjBwdDtmb250LWZhbWlseTomcXVv
dDtDZW50dXJ5IEdvdGhpYyZxdW90OyxzYW5zLXNlcmlmO2NvbG9yOmdyYXk7Ym9yZGVyOm5vbmUg
d2luZG93dGV4dCAxLjBwdDtwYWRkaW5nOjBpbiI+Jm5ic3A7NTYyLTYyMy05MzI4DQogJm5ic3A7
PC9zcGFuPjwvYj48Yj48c3BhbiBzdHlsZT0iZm9udC1zaXplOjEyLjBwdDtmb250LWZhbWlseTom
cXVvdDtDZW50dXJ5IEdvdGhpYyZxdW90OyxzYW5zLXNlcmlmO2NvbG9yOiMyNjI2MjY7Ym9yZGVy
Om5vbmUgd2luZG93dGV4dCAxLjBwdDtwYWRkaW5nOjBpbiI+RiZuYnNwOzwvc3Bhbj48L2I+PGI+
PHNwYW4gc3R5bGU9ImZvbnQtc2l6ZToxMi4wcHQ7Zm9udC1mYW1pbHk6JnF1b3Q7Q2VudHVyeSBH
b3RoaWMmcXVvdDssc2Fucy1zZXJpZjtjb2xvcjpncmF5O2JvcmRlcjpub25lIHdpbmRvd3RleHQg
MS4wcHQ7cGFkZGluZzowaW4iPjU2Mi02MjMtOTM2OTwvc3Bhbj48L2I+PHNwYW4gc3R5bGU9ImNv
bG9yOmJsYWNrIj48bzpwPjwvbzpwPjwvc3Bhbj48L3A+DQo8ZGl2Pg0KPGRpdiBjbGFzcz0iTXNv
Tm9ybWFsIiBzdHlsZT0idmVydGljYWwtYWxpZ246YmFzZWxpbmUiPjxzcGFuIHN0eWxlPSJmb250
LWZhbWlseTomcXVvdDtDZW50dXJ5IEdvdGhpYyZxdW90OyxzYW5zLXNlcmlmO2NvbG9yOmdyYXk7
Ym9yZGVyOm5vbmUgd2luZG93dGV4dCAxLjBwdDtwYWRkaW5nOjBpbiI+DQo8aHIgc2l6ZT0iMSIg
d2lkdGg9IjE5JSIgYWxpZ249ImxlZnQiPg0KPC9zcGFuPjwvZGl2Pg0KPC9kaXY+DQo8cCBjbGFz
cz0iTXNvTm9ybWFsIj48Yj48c3BhbiBzdHlsZT0iZm9udC1zaXplOjQuMHB0O2ZvbnQtZmFtaWx5
OiZxdW90O0NlbnR1cnkgR290aGljJnF1b3Q7LHNhbnMtc2VyaWY7Y29sb3I6IzI2MjYyNjtib3Jk
ZXI6bm9uZSB3aW5kb3d0ZXh0IDEuMHB0O3BhZGRpbmc6MGluIj4mbmJzcDs8L3NwYW4+PC9iPjxi
PjxzcGFuIHN0eWxlPSJmb250LXNpemU6MTIuMHB0O2ZvbnQtZmFtaWx5OiZxdW90O0NlbnR1cnkg
R290aGljJnF1b3Q7LHNhbnMtc2VyaWY7Y29sb3I6IzI2MjYyNjtib3JkZXI6bm9uZSB3aW5kb3d0
ZXh0IDEuMHB0O3BhZGRpbmc6MGluIj5MZWZ0YmFua0FydDwvc3Bhbj48L2I+PHNwYW4gc3R5bGU9
ImNvbG9yOmJsYWNrIj48bzpwPjwvbzpwPjwvc3Bhbj48L3A+DQo8cCBjbGFzcz0iTXNvTm9ybWFs
Ij48Yj48c3BhbiBzdHlsZT0iZm9udC1zaXplOjEyLjBwdDtmb250LWZhbWlseTomcXVvdDtDZW50
dXJ5IEdvdGhpYyZxdW90OyxzYW5zLXNlcmlmO2NvbG9yOmdyYXk7Ym9yZGVyOm5vbmUgd2luZG93
dGV4dCAxLjBwdDtwYWRkaW5nOjBpbiI+PGEgaHJlZj0iaHR0cHM6Ly93d3cuZ29vZ2xlLmNvbS9t
YXBzL3NlYXJjaC8xNDg2MCUwRCUwQSYjNDM7QWxvbmRyYSYjNDM7Qmx2ZC4mIzQzOyUwRCUwQSYj
NDM7TGElMEQlMEEmIzQzO01pcmFkYSwmIzQzO0NBJiM0Mzs5MDYzOD9lbnRyeT1nbWFpbCZhbXA7
c291cmNlPWciIHRhcmdldD0iX2JsYW5rIj4xNDg2MA0KIEFsb25kcmEgQmx2ZC48L2E+PC9zcGFu
PjwvYj48c3BhbiBzdHlsZT0iY29sb3I6YmxhY2siPjxvOnA+PC9vOnA+PC9zcGFuPjwvcD4NCjxw
IGNsYXNzPSJNc29Ob3JtYWwiPjxiPjxzcGFuIHN0eWxlPSJmb250LXNpemU6MTIuMHB0O2ZvbnQt
ZmFtaWx5OiZxdW90O0NlbnR1cnkgR290aGljJnF1b3Q7LHNhbnMtc2VyaWY7Y29sb3I6Z3JheTti
b3JkZXI6bm9uZSB3aW5kb3d0ZXh0IDEuMHB0O3BhZGRpbmc6MGluIj48YSBocmVmPSJodHRwczov
L3d3dy5nb29nbGUuY29tL21hcHMvc2VhcmNoLzE0ODYwJTBEJTBBJiM0MztBbG9uZHJhJiM0MztC
bHZkLiYjNDM7JTBEJTBBJiM0MztMYSUwRCUwQSYjNDM7TWlyYWRhLCYjNDM7Q0EmIzQzOzkwNjM4
P2VudHJ5PWdtYWlsJmFtcDtzb3VyY2U9ZyIgdGFyZ2V0PSJfYmxhbmsiPkxhDQogTWlyYWRhLCBD
QSA5MDYzODwvYT48L3NwYW4+PC9iPjxzcGFuIHN0eWxlPSJjb2xvcjpibGFjayI+PG86cD48L286
cD48L3NwYW4+PC9wPg0KPHAgY2xhc3M9Ik1zb05vcm1hbCI+PGI+PHNwYW4gc3R5bGU9ImZvbnQt
c2l6ZToyLjBwdDtmb250LWZhbWlseTomcXVvdDtUaW1lcyBOZXcgUm9tYW4mcXVvdDssc2VyaWY7
Y29sb3I6Z3JheTtib3JkZXI6bm9uZSB3aW5kb3d0ZXh0IDEuMHB0O3BhZGRpbmc6MGluIj4mbmJz
cDsmbmJzcDsmbmJzcDsmbmJzcDsmbmJzcDs8L3NwYW4+PC9iPjxzcGFuIHN0eWxlPSJjb2xvcjpi
bGFjayI+PG86cD48L286cD48L3NwYW4+PC9wPg0KPHAgY2xhc3M9Ik1zb05vcm1hbCI+PHNwYW4g
c3R5bGU9ImZvbnQtc2l6ZToxMC4wcHQ7Zm9udC1mYW1pbHk6JnF1b3Q7Q2VudHVyeSBHb3RoaWMm
cXVvdDssc2Fucy1zZXJpZjtjb2xvcjojMUY0OTdEO2JvcmRlcjpub25lIHdpbmRvd3RleHQgMS4w
****************************************************************************
****************************************************************************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****************************************************************************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--_000_BY5PR07MB720162388F16D1AA0762BA21AB0A0BY5PR07MB7201namp_--

--_004_BY5PR07MB720162388F16D1AA0762BA21AB0A0BY5PR07MB7201namp_
Content-Type: image/jpeg; name="image001.jpg"
Content-Description: image001.jpg
Content-Disposition: inline; filename="image001.jpg"; size=428;
	creation-date="Tue, 28 Jan 2020 17:46:47 GMT";
	modification-date="Tue, 28 Jan 2020 17:46:47 GMT"
Content-ID: <image001.jpg@01D5D5BF.D4463B60>
Content-Transfer-Encoding: base64

/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAoHBwgHBgoICAgLCgoLDhgQDg0NDh0VFhEYIx8lJCIf
IiEmKzcvJik0KSEiMEExNDk7Pj4+JS5ESUM8SDc9Pjv/wAALCAA9AHkBAREA/8QAHwAAAQUBAQEB
AQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1Fh
ByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZ
WmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXG
x8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/9oACAEBAAA/APZqKKKKKKKKKKKKKKKK
KKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK
KKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK//9k=

--_004_BY5PR07MB720162388F16D1AA0762BA21AB0A0BY5PR07MB7201namp_--
