Return-Path: <<EMAIL>>
Received: from mail-lf1-f47.google.com (mail-lf1-f47.google.com [*************])
 by inbound-smtp.us-west-2.amazonaws.com with SMTP id ptg2c67bk4luop98ogdk8greaiqud1s1cmhj03g1
 for <EMAIL>;
 Wed, 29 Jan 2020 00:57:52 +0000 (UTC)
X-SES-Spam-Verdict: PASS
X-SES-Virus-Verdict: PASS
Received-SPF: pass (spfCheck: domain of _spf.google.com designates ************* as permitted sender) client-ip=*************; envelope-from=<EMAIL>; helo=mail-lf1-f47.google.com;
Authentication-Results: amazonses.com;
 spf=pass (spfCheck: domain of _spf.google.com designates ************* as permitted sender) client-ip=*************; envelope-from=<EMAIL>; helo=mail-lf1-f47.google.com;
 dkim=pass header.i=@gmail.com;
 dmarc=pass header.from=gmail.com;
X-SES-RECEIPT: AEFBQUFBQUFBQUFIOUREWWd3ZnNvTGRGNnVSblgvM2lxVGRUU2l3cGVEa0UvTDNMVU45SVNvRURzNSszeU9GdXdmNEpXK05lQzdqOE9GT2hsa2RSMWgyZ0FMUjlnVnAxck9rQmxmaCszRTFjcHIvb1B1ZVM5SmpzdXA1Z1RmcitKR2MyZlNlWnZPSnJiRU0xOVM4d0tuK0VXZFJBWG5CNTAxVGR3OXJJd29EL0hpZmIwQzBiVkk1TE5kM3lMaEhJWU9MK2E5MUFyRGdHRE9BSXNPWHYvZGY0MXZ0ZENUZG8rUDFTMVkxTkhYQi9lZ0JXakh2K3o5ZWRWSUtnK2ZxVlo1M0tzUTlvdGFlYWhpNENhcnJJSUZMOVdXdk5QaU5wTUUweUhBWFpEYVpHQkFCaHlWREF1TkE9PQ==
X-SES-DKIM-SIGNATURE: a=rsa-sha256; q=dns/txt; b=M1wUqgUuBRTH2YyXei+ipqWFl44awdE06aaLG4RkPGE8ZM8QU+jyQQt+YDpZ6PNP+tq1NvOrsc0rljQ+nKUWo+J2Sz/VgnqlZu0ETVAaCcE+BpTfC8cpwngKqkZrdCeVWO57bNdDieTKY98tDJf0VyvkfRWJdABPoTHCocXddfU=; c=relaxed/simple; s=gdwg2y3kokkkj5a55z2ilkup5wp5hhxx; d=amazonses.com; t=**********; v=1; bh=/cmz0D93XVeaIN+bazDZ/t9D5m4rWwhGnBCGfQxfV2M=; h=From:To:Cc:Bcc:Subject:Date:Message-ID:MIME-Version:Content-Type:X-SES-RECEIPT;
Received: by mail-lf1-f47.google.com with SMTP id r14so10593200lfm.5
        for <<EMAIL>>; Tue, 28 Jan 2020 16:57:52 -0800 (PST)
DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;
        d=gmail.com; s=20161025;
        h=mime-version:from:date:message-id:subject:to;
        bh=AolAlxl60P2S+RGuKEccsw82HFX7NhYjQnQ7IVW5Ptk=;
        b=YUcxYufcTsIJaq2SZx6ZFGersTZuCs9Zwm8b0hRjDDY3reg6GSCPRmPFRjrRgBa5zr
         Jo+bYOIZ/cadl+gCkiPDJ912MMWzdOjw683zKRdXVhqYuqY/2N6mzErTs55YjHWFjnla
         j3J0JQNz6e9Hs8G2bZH0K6rpor3YbwG3Pkv8q2ntFMZPzJiL9Y5Br2yBdFf3nwbBVHbn
         eBcTMNJhlT1aCUYoYFv8vUljODswHmMsfbV5uRPi4t6P5ZqPXyb3f60H/pEOWgmNA2Hw
         kGjzEyBTU8cENOTjp+hlq45tHTCditLw0ug7H4Mzz8AIPhOxO+WeUtU53pot+NgEE6lA
         Q2Ig==
X-Google-DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;
        d=1e100.net; s=20161025;
        h=x-gm-message-state:mime-version:from:date:message-id:subject:to;
        bh=AolAlxl60P2S+RGuKEccsw82HFX7NhYjQnQ7IVW5Ptk=;
        b=TvdkkHs5AwzYhf1dZO0kqYyuJmIK2T+ljZzQ1wuLSr3q9db78iQYFP56GIhnUwLh9V
         CJhzg9/bbpcFsOhAvkA+KAcDVcycB/myNXSf5BwqKezqF3IbW9AdCGbRbpeViHkd1bvq
         I7jzhajnPAUkDrD2NblVF0IE5NfDnuDSyhy71fnte3zIbfdoFgYEamGUWb35VYl3kwnN
         xg/dMSHuZWnjF5/F+/SxJxEFQpnwic20rETLK+6hT9T0hjJeA4L7PaYb9LqVbSoVRKrh
         qsrVKMX3f4Gjh9q3vocmEKKMDd9IDGUozR2dXaobrxLfaTbIyi+HdwMWf8f2qTnLD28Y
         kljQ==
X-Gm-Message-State: APjAAAXm7y/iUJgLAkaDviX5yF2nuA/6Q9XbfXo4uShKR9j67mwKb4FI
	RD00T7HtkKXIRJ90cMy/vTfHE0/las0fS1zXvEbVBR/P
X-Google-Smtp-Source: APXvYqwd/7DM5y3Tx5Adxanwu9rSbJkgs+4tvkHKcEfNfKjMjEFsdrxvLxoS008GV3TBVtAU2PJuARJtAxtLFMPwhGM=
X-Received: by 2002:ac2:5444:: with SMTP id d4mr3847994lfn.49.1580259469904;
 Tue, 28 Jan 2020 16:57:49 -0800 (PST)
MIME-Version: 1.0
From: Bob Lasch <<EMAIL>>
Date: Tue, 28 Jan 2020 18:57:38 -0600
Message-ID: <CA+MGk8QNHpOZ9-KwkPUdhpCESucXYkBi_mcQO=<EMAIL>>
Subject: Great job [#4]
To: <EMAIL>
Content-Type: multipart/mixed; boundary="0000000000005b6178059d3cd48e"

--0000000000005b6178059d3cd48e
Content-Type: multipart/alternative; boundary="0000000000005b6175059d3cd48c"

--0000000000005b6175059d3cd48c
Content-Type: text/plain; charset="UTF-8"

We need to have a response.

--0000000000005b6175059d3cd48c
Content-Type: text/html; charset="UTF-8"

<div dir="ltr">We need to have a response.</div>

--0000000000005b6175059d3cd48c--
--0000000000005b6178059d3cd48e
Content-Type: application/octet-stream;
	name="gregs-fishing-barge-Locations_Data_SAVE.xlsx"
Content-Disposition: attachment;
	filename="gregs-fishing-barge-Locations_Data_SAVE.xlsx"
Content-Transfer-Encoding: base64
Content-ID: <f_k5ylobh70>
X-Attachment-Id: f_k5ylobh70

UEsDBBQABgAIAAAAIQBi7p1oXgEAAJAEAAATAAgCW0NvbnRlbnRfVHlwZXNdLnhtbCCiBAIooAAC
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACs
lMtOwzAQRfdI/EPkLUrcskAINe2CxxIqUT7AxJPGqmNbnmlp/56J+xBCoRVqN7ESz9x7MvHNaLJu
bbaCiMa7UgyLgcjAVV4bNy/Fx+wlvxcZknJaWe+gFBtAMRlfX41mmwCYcbfDUjRE4UFKrBpoFRY+
gOOd2sdWEd/GuQyqWqg5yNvB4E5W3hE4yqnTEOPRE9RqaSl7XvPjLUkEiyJ73BZ2XqVQIVhTKWJS
uXL6l0u+cyi4M9VgYwLeMIaQvQ7dzt8Gu743Hk00GrKpivSqWsaQayu/fFx8er8ojov0UPq6NhVo
Xy1bnkCBIYLS2ABQa4u0Fq0ybs99xD8Vo0zL8MIg3fsl4RMcxN8bZLqej5BkThgibSzgpceeRE85
NyqCfqfIybg4wE/tYxx8bqbRB+QERfj/FPYR6brzwEIQycAhJH2H7eDI6Tt77NDlW4Pu8ZbpfzL+
BgAA//8DAFBLAwQUAAYACAAAACEAtVUwI/QAAABMAgAACwAIAl9yZWxzLy5yZWxzIKIEAiigAAIA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKyS
TU/DMAyG70j8h8j31d2QEEJLd0FIuyFUfoBJ3A+1jaMkG92/JxwQVBqDA0d/vX78ytvdPI3qyCH2
4jSsixIUOyO2d62Gl/pxdQcqJnKWRnGs4cQRdtX11faZR0p5KHa9jyqruKihS8nfI0bT8USxEM8u
VxoJE6UchhY9mYFaxk1Z3mL4rgHVQlPtrYawtzeg6pPPm3/XlqbpDT+IOUzs0pkVyHNiZ9mufMhs
IfX5GlVTaDlpsGKecjoieV9kbMDzRJu/E/18LU6cyFIiNBL4Ms9HxyWg9X9atDTxy515xDcJw6vI
8MmCix+o3gEAAP//AwBQSwMEFAAGAAgAAAAhABwJx2uPAgAA+gUAAA8AAAB4bC93b3JrYm9vay54
bWykVG1vmzAQ/j5p/8HydwomLUtRSdVCskXaqmh906RIkwNOsAo2s01DVfW/7wwhbZp96FoEts8H
j5+7e7iT06Ys0D1TmksRYXLgYcREKjMuVhG+vpo4Q4y0oSKjhRQswg9M49PR508na6nuFlLeIQAQ
OsK5MVXoujrNWUn1gayYAM9SqpIaMNXK1ZViNNM5Y6YsXN/zArekXOAOIVRvwZDLJU9ZItO6ZMJ0
IIoV1AB9nfNK92hl+ha4kqq7unJSWVYAseAFNw8tKEZlGk5XQiq6KCDshhyhRsEdwEM8GPz+JHDt
HVXyVEktl+YAoN2O9F78xHMJ2UlBs5+DtyEduordc1vDLSsVvJNVsMUKnsGI92E0AtJqtRJC8t6J
drTl5uPRyZIX7KaTLqJVdUFLW6kCo4JqM864YVmEv4Ap12xnQ9XVec0L8Pok8I+xO9rKeabAgNqf
FYYpQQ2LpTAgtQ31j8qqxY5zCSJGP9mfmisG/w5ICMKBkaYhXegZNTmqVRHhOJxfa4hwvlJsJeeJ
ktVCNvNLo6ipy/nX8cX19OrX/IUU6b7u/0OMNLW5cCH+jmO3fp0LoKrCXnAzoxCsp8l3SPolvYcS
QKGzzR86hRwPfz/6x8ngzEvOHX8wJM5h7B85Z0l87IwHExKPvUkwjCdPEIUKwlTS2uSbslrMCB8O
/uH6QZveQ7yw5tnz+Y/e5nLs/GrofU82UtvAbjhb62cBWBM1t1xkch1hh/gQzcOuuW6dtzwzOSjI
+zKAV7q9b4yvcmBMiD+0cle+ZRbhHUZJx2gCl2OHHUbuC0ptqwRq7YxEK+9L2z4J9GQ72+zCWoX2
DDXNSFu9/rOUFulMITvZF73W2bft0V8AAAD//wMAUEsDBBQABgAIAAAAIQCBPpSX8wAAALoCAAAa
AAgBeGwvX3JlbHMvd29ya2Jvb2sueG1sLnJlbHMgogQBKKAAAQAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAACsUk1LxDAQvQv+hzB3m3YVEdl0LyLsVesPCMm0KdsmITN+9N8bKrpdWNZLLwNvhnnv
zcd29zUO4gMT9cErqIoSBHoTbO87BW/N880DCGLtrR6CRwUTEuzq66vtCw6acxO5PpLILJ4UOOb4
KCUZh6OmIkT0udKGNGrOMHUyanPQHcpNWd7LtOSA+oRT7K2CtLe3IJopZuX/uUPb9gafgnkf0fMZ
CUk8DXkA0ejUISv4wUX2CPK8/GZNec5rwaP6DOUcq0seqjU9fIZ0IIfIRx9/KZJz5aKZu1Xv4XRC
+8opv9vyLMv072bkycfV3wAAAP//AwBQSwMEFAAGAAgAAAAhAAMNzJUXAwAArgoAABgAAAB4bC93
b3Jrc2hlZXRzL3NoZWV0MS54bWyclmtv2jAUhr9P2n+I/L3kBr0goGrLGFSaNO362SQOWE3izDaX
atp/33FSQs5hlVhRG0Ie+7X9cBIzut0XubcV2khVjlnYC5gnykSlslyN2fdvs4tr5hnLy5TnqhRj
9iwMu528fzfaKf1k1kJYDxJKM2Zra6uh75tkLQpueqoSJZBM6YJb+KhXvqm04Gndqcj9KAgu/YLL
kjUJQ31OhsoymYipSjaFKG0TokXOLczfrGVlDmlFck5cwfXTprpIVFFBxFLm0j7XocwrkuFiVSrN
lzmsex/2eeLtNfxF8B8fhqmvn4xUyEQrozLbg2S/mfPp8m/8G58nbdLp+s+KCfu+FlvpvsBjVPS2
KYWDNis6hsVvDLtsw5wuPdzIdMx+By+vC3gP3SE4Hg7sD5uMUgnfsFuVp0U2Znfh8HHA/Mmorp8f
UuxM59yzfPlV5CKxAsYImefKc6nUk2u4gEsBJJq6gUvkiZVb8SDyHIJdhf9qxrh2A/jtCN3zw2iz
uqA/ay8VGd/k9ovazYVcrS0MG/WuBrBSVyrD9HkqTAI1CmP3IpebqBxC4OgV0t1rUGJ8D+9wy+1k
atdjFg3qgGRjrCp+NtfCekpN33piU275ZKTVzoOCgRBTcXf7hUMX9M+xYVDX9g4aw2wMmNhOgpG/
heUlL+y+y0LMHroswmzaZTFmH7qsj9msywaYfeyyS8zmXXaF2aLLrjF77LKblvmgsXUJFX++S2jc
ugypTASpTQSpTgSpTwSpUASpUQSpUgSpUwSpVARfsQq3/vlWoXFrNaJWEaRWEaRWEaRWuzCkVhGk
VlEs6TlHPalVBKlVBF+x2v8fq9D4aJUs4h5BahVBUjdTBMkKPyBIVjjrwpBaRT2Py68fYXPUk1pF
kFpF8BWr8PA+v1ahcWs1prWKILWKIK1VBGmtDupndxzHURT14UUeqt2+J16bvv1+HBHjC9SNekOQ
emu2ymZHqvhKfOJ6JUvj5SKr970r5ulmbwx6cG5V5XZDt0kulYU97vBpDb8LBWxRQQ+KP1PKHj64
7bj9pTn5CwAA//8DAFBLAwQUAAYACAAAACEAyHvWqeAGAACKHQAAEwAAAHhsL3RoZW1lL3RoZW1l
MS54bWzsWUtvGzcQvhfofyD2nliyJcc2IgeWLCVt4sSwlRQ5UrvULmPuckFStnUrkmOBAkXTopcC
vfVQtA2QAL2kv8ZtijYF8hc6JFerpUX5lQR9RQd7H98M580Z7tVrhylD+0RIyrNWUL9cCxDJQh7R
LG4Fd/u9SysBkgpnEWY8I61gTGRwbf39967iNZWQlCCgz+QabgWJUvnawoIM4TGWl3lOMng35CLF
Cm5FvBAJfAB8U7awWKstL6SYZgHKcAps7wyHNCSor1kG6xPmXQa3mZL6QcjErmZNHAqDjfbqGiHH
ssME2sesFcA6ET/ok0MVIIalghetoGZ+wcL61QW8VhAxNYe2Qtczv4KuIIj2Fs2aIh6Ui9Z7jdUr
myV/A2BqFtftdjvdesnPAHAYgqZWlirPRm+l3p7wrIDs5SzvTq1Za7j4Cv+lGZlX2+12c7WQxTI1
IHvZmMGv1JYbG4sO3oAsvjmDb7Q3Op1lB29AFr88g+9dWV1uuHgDShjN9mbQ2qG9XsG9hAw5u+GF
rwB8pVbApyiIhjK69BJDnql5sZbiB1z0AKCBDCuaITXOyRCHEMUdnA4ExXoBvEZw5Y19FMqZR3ot
JENBc9UKPswxZMSU36vn3796/hS9ev7k6OGzo4c/HT16dPTwR8vLIbyBs7hK+PLbz/78+mP0x9Nv
Xj7+wo+XVfyvP3zyy8+f+4GQQVOJXnz55LdnT1589env3z32wDcEHlThfZoSiW6TA7TDU9DNGMaV
nAzE+Sj6CaYOBU6At4d1VyUO8PYYMx+uTVzj3RNQPHzA66MHjqy7iRgp6ln5ZpI6wC3OWZsLrwFu
6rUqFu6Psti/uBhVcTsY7/vW7uDMcW13lEPVnASlY/tOQhwxtxnOFI5JRhTS7/geIR7t7lPq2HWL
hoJLPlToPkVtTL0m6dOBE0hTohs0Bb+MfTqDqx3bbN1Dbc58Wm+SfRcJCYGZR/g+YY4Zr+ORwqmP
ZR+nrGrwW1glPiF3xyKs4rpSgadjwjjqRkRKH80dAfpWnH4TQ73yun2LjVMXKRTd8/G8hTmvIjf5
XifBae6VmWZJFfuB3IMQxWibKx98i7sZou/BDzib6+57lDjuPr0Q3KWxI9I0QPSbkSiqtlN/U5qd
VIwZhWr8rhhPdqcN2Jp8KXHjWAmeh/sXFt5NPMq2CcT67Mbzru6+q7vBf77uzsvls1bbaYGFJnna
F5suOZ3bJA8pY7tqzMgtafpkCZtF1IOHpoE3U1w5NOUJXBbF3cHFAhsaJLj6iKpkN8E59Nh1M/LF
smAdS5RzCbOdeWyGT3KMtxknKbTZZjJs6pnB1gOJ1RaP7OOl6mxYsjGTYmzmz8lCS5rBWRdbuvJ6
i9WtVHPN5qpWN6KZUueoVqoMPpxVDR6W1oQuBEHvAlZehhFdyw6zCWYk0na3c/PELXrpt+SiQmur
SIIjYl3kPK64rm58NwmhSXR5XHc+a1YD5XQhTFhMRssLG3nCYGpknXbHsoll1dxiGTpoBavNxWaA
Qpy3giFMpXCZ5uA0qfs2zGI42gmVsFF7ai6aaJtqvOqPqjocNNhEmokqJ41zIdUmlon1oXlVuIpl
ZoY28i82GzrY3owCNlAvIMXSCoTI3yYF2NF1LRkOSaiqzq48MUcMBlBUQj5SROwm0QEasJHYweB+
sKnWJ6ISDhdMQusbOAnT1jav3Npa1LXq+ZPB2eeY5QkuqqU+SZlknIWbfCtlMHdWWiMe6OaV3Sh3
flV0xr8pVaph/D9TRW8HMO0vRdoDIRzECox0vrYCLlTCoQrlCQ17As6oTO2AaIHTVHgNxofjYPNf
kH393+ac5WHSGoY2tUNjJChsJyoRhGxDWTLRdwqzerH1WJasYGQiqiKuzK3YA7JPWF/XwGVdgwOU
QKibalKUAYM7Hn/ufZFBg1j3KP/UxsUm83l3d7252w7J0p+xlWhUin5lK1j1tzMnNxhTEc6yAcvp
crZizWi82Jy78+hWrdrP5HBmg/Qf2P+oCJn9tqA31D7fgdqK4FOBFR5BVF/SVQ0iSBdIezWAvsc+
tMGkWdkViub0LXZB5bqQpRdpVM9p7LKJcpdzcvHkvuZ8xi4s7Ni6GkceU4Nnj6eobo8mc4hxjPko
Vf1uxAcPwNGbcEI/YvZLkszhzuRBvi1MdA14NC4umbQbro06PcPYJmWHDBGNDifzx7FBo/gwUzY2
gDYjEgRaSbjkGxpcQh2YBandLUvixdOJSwqzMpTsktgck/kYwLesQmQ92pmVdTNntdZXE0ux7HVM
dgbhWeYzmXfOOqvJ7KB4oqMuYDJ1eLLJCkuB8WYDD75GCgzDqf22BJuOLSomZNf/AgAA//8DAFBL
AwQUAAYACAAAACEAAtToz4gCAAAaBgAADQAAAHhsL3N0eWxlcy54bWyklG1vmzAQx99P2new/J4a
aMiSCKiWpkiVtmlSu2lvHTCJVT8g22Rk0777zpAHok7b1L7B58P++X++O6c3nRRox4zlWmU4ugox
YqrUFVebDH95LIIZRtZRVVGhFcvwnll8k799k1q3F+xhy5hDgFA2w1vnmgUhttwySe2VbpiCP7U2
kjqYmg2xjWG0sn6TFCQOwymRlCs8EBay/B+IpOapbYJSy4Y6vuaCu33PwkiWi/uN0oauBUjtogkt
URdNTYw6czyk9z47R/LSaKtrdwVcouual+y53DmZE1qeSUB+GSlKSBhfxN6ZF5ImxLAd9+nDeVpr
5SwqdascJBOE+itYPCn9XRX+l3cOq/LU/kA7KsATYpKniko2zL8yU1FFvZN43mGwsI8LcaLHHgSO
PIU0OGZUARN0sB/3DSRAQcUMmH7dP1ZvDN1HcTLaQPoD83StTQUVeo7r6MpTwWoHQg3fbP3odAPf
tXYOspinFacbrajwoQyQkwHhlEyIB1/F3+oLdlcj1cpCuvsqw9AP/hKOJgRyMAfeMPH8MW1gvxqL
uvqSD8SR7AvRp+ORz2SGP/m2E1ABBwRat1w4rv4gGJhVd76Cvhycb6H+ck6nwE1UrKatcI+nnxk+
2x9ZxVsZn1Z95jvtekSGz/YHn6lo6rPMOvfBQnnBiFrDM/zzbvluvror4mAWLmfB5JolwTxZroJk
crtcrYp5GIe3v0aN/Io27t+dPIUGWVgBzW4OwR5CfDj7MjyaDPL7GgXZY+3zeBq+T6IwKK7DKJhM
6SyYTa+ToEiieDWdLO+SIhlpT17Y7iGJouHh8OKTheOSCa6OuTpmaOyFJMH0L0GQYybI+VHPfwMA
AP//AwBQSwMEFAAGAAgAAAAhAD+Mo+imAQAAGQQAABQAAAB4bC9zaGFyZWRTdHJpbmdzLnhtbHxT
TW/bMAy9D9h/IHRf5c8kLWz3MGBAgfWUBTsGqkzHAizJleSs2a8fvR62Sk19kh5Nvifysbl/0ROc
0XllTcvym4wBGml7ZU4tO/z49mXHwAdhejFZgy27oGf33edPjfcBKNf4lo0hzHecezmiFv7Gzmgo
MlinRaCrO3E/OxS9HxGDnniRZRuuhTIMpF1MaFm1ZbAY9bzg11egrFjXeNU1oTNCY8ND1/D1/oqJ
vnfofQxLFS4xRuJDkj+P9JijWfQTuqTIqsBdju/x/lbzFSlFjP9Pcfz7TKpJjf1Qy5sfr+hzqG1A
kKOS4mTBDoOSSdG8zsoSPDwJFyYMAcQ5JZ5oBIPCqY+1P3yPkV1eF5tye1tv48jBqIA97Nc2JwPZ
ZHVVRRn8zSQP+7hgHgGry+78LCS5j2zk0Z2RdY+kHfZEDUk+rNYi0zpyWxw8obEiBn8+xEhdbcpk
otRpvxJqqkvrAfNEmsBYF8bUdLYXLunrL5WIzfOiKCr63pFQ72LQoaRFCrSrIOgA3i4peQHUJVJ1
pQVaCZ3oGKZ/VJxWu/sDAAD//wMAUEsDBBQABgAIAAAAIQAt365AOgEAAEMCAAARAAgBZG9jUHJv
cHMvY29yZS54bWwgogQBKKAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACMkV9PwyAUxd9N
/A4N7y1ljZuSliVq9uQSE2tmfCNw1xELJYB2+/aybqvzz4MJL+Sc++PcQznf6jb5AOdVZypEshwl
YEQnlWkq9Fwv0muU+MCN5G1noEI78GjOLi9KYanoHDy6zoILCnwSScZTYSu0CcFSjL3YgOY+iw4T
xXXnNA/x6hpsuXjjDeBJnk+xhsAlDxzvgakdieiIlGJE2nfXDgApMLSgwQSPSUbwlzeA0/7PgUE5
c2oVdjbudIx7zpbiII7urVejse/7rC+GGDE/wS/Lh6dh1VSZfVcCENv303IflrHKtQJ5u2ONgybp
oTHgSvxbLqUYAlLhgAeQSXySHgKelFVxd18vEJvk5CbNpykpanJFJ/EUryX+Mc9GoD5m+CdxRvMZ
JeSMeAKwIff3b2efAAAA//8DAFBLAwQUAAYACAAAACEAYUkJEIkBAAARAwAAEAAIAWRvY1Byb3Bz
L2FwcC54bWwgogQBKKAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACckkFv2zAMhe8D+h8M
3Rs53VAMgaxiSFf0sGEBkrZnTaZjobIkiKyR7NePttHU2XrqjeR7ePpESd0cOl/0kNHFUInlohQF
BBtrF/aVeNjdXX4VBZIJtfExQCWOgOJGX3xSmxwTZHKABUcErERLlFZSom2hM7hgObDSxNwZ4jbv
ZWwaZ+E22pcOAsmrsryWcCAINdSX6RQopsRVTx8NraMd+PBxd0wMrNW3lLyzhviW+qezOWJsqPh+
sOCVnIuK6bZgX7Kjoy6VnLdqa42HNQfrxngEJd8G6h7MsLSNcRm16mnVg6WYC3R/eG1XovhtEAac
SvQmOxOIsQbb1Iy1T0hZP8X8jC0AoZJsmIZjOffOa/dFL0cDF+fGIWACYeEccefIA/5qNibTO8TL
OfHIMPFOONuBbzpzzjdemU/6J3sdu2TCkYVT9cOFZ3xIu3hrCF7XeT5U29ZkqPkFTus+DdQ9bzL7
IWTdmrCH+tXzvzA8/uP0w/XyelF+LvldZzMl3/6y/gsAAP//AwBQSwECLQAUAAYACAAAACEAYu6d
aF4BAACQBAAAEwAAAAAAAAAAAAAAAAAAAAAAW0NvbnRlbnRfVHlwZXNdLnhtbFBLAQItABQABgAI
AAAAIQC1VTAj9AAAAEwCAAALAAAAAAAAAAAAAAAAAJcDAABfcmVscy8ucmVsc1BLAQItABQABgAI
AAAAIQAcCcdrjwIAAPoFAAAPAAAAAAAAAAAAAAAAALwGAAB4bC93b3JrYm9vay54bWxQSwECLQAU
AAYACAAAACEAgT6Ul/MAAAC6AgAAGgAAAAAAAAAAAAAAAAB4CQAAeGwvX3JlbHMvd29ya2Jvb2su
eG1sLnJlbHNQSwECLQAUAAYACAAAACEAAw3MlRcDAACuCgAAGAAAAAAAAAAAAAAAAACrCwAAeGwv
d29ya3NoZWV0cy9zaGVldDEueG1sUEsBAi0AFAAGAAgAAAAhAMh71qngBgAAih0AABMAAAAAAAAA
AAAAAAAA+A4AAHhsL3RoZW1lL3RoZW1lMS54bWxQSwECLQAUAAYACAAAACEAAtToz4gCAAAaBgAA
DQAAAAAAAAAAAAAAAAAJFgAAeGwvc3R5bGVzLnhtbFBLAQItABQABgAIAAAAIQA/jKPopgEAABkE
AAAUAAAAAAAAAAAAAAAAALwYAAB4bC9zaGFyZWRTdHJpbmdzLnhtbFBLAQItABQABgAIAAAAIQAt
365AOgEAAEMCAAARAAAAAAAAAAAAAAAAAJQaAABkb2NQcm9wcy9jb3JlLnhtbFBLAQItABQABgAI
AAAAIQBhSQkQiQEAABEDAAAQAAAAAAAAAAAAAAAAAAUdAABkb2NQcm9wcy9hcHAueG1sUEsFBgAA
AAAKAAoAgAIAAMQfAAAAAA==
--0000000000005b6178059d3cd48e--
