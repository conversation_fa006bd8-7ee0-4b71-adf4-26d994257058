require 'rails_helper'
include CompanyUserHelper

RSpec.describe EmailTemplatesController, type: :controller do
  create_company_and_user
  let!(:workspace) { company.workspaces.first }

  describe 'GET #index' do
    it 'returns a list of email templates where template type is end users' do
      get :index, params: { template_type: 'end_users', index_page: true}, format: :json

      expect(response).to have_http_status(:ok)
      json_response = JSON.parse(response.body)
      expect(json_response['email_templates'].count).to eq(3)
      expect(json_response['email_templates'][0]["template_name"]).to eq('ticket created')
      expect(json_response['email_templates'][1]["template_name"]).to eq('comment added')
      expect(json_response['email_templates'][2]["template_name"]).to eq('ticket assigned')
    end
  end

  describe 'POST #create' do
    context 'with valid parameters' do
      let(:email_template) do
        {
          email_template: {
            template_title: 'New Template',
            subject_title: 'Subject',
            email_body: '<div><!--block-->Testing Body</div>',
            template_name: 'ticket created',
            template_type: 'end_users',
          }
        }
      end

      it 'creates a new email template' do
        post :create, params: email_template, format: :json

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['status']).to eq('success')
        expect(json_response['email_template']).to be_present
        expect(json_response['email_template']["template_title"]).to eq('New Template')
        expect(json_response['email_template']["subject_title"]).to eq('Subject')
        expect(json_response['email_template']["email_body"]).to eq('<div><!--block-->Testing Body</div>')
        expect(json_response['email_template']["template_name"]).to eq('ticket created')
        expect(json_response['email_template']["template_type"]).to eq('end_users')
      end
    end

    context 'with invalid parameters' do
      let(:invalid_email_template) do
        {
          email_template: {
            template_title: 'New Template',
            subject_title: 'Subject',
            template_name: 'ticket created',
            template_type: 'end_users',
          }
        }
      end

      it 'returns an error message' do
        post :create, params: invalid_email_template, format: :json

        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response['status']).to eq('error')
        expect(json_response['errors']).to eq(["Email body can't be blank"])
      end
    end
  end

  describe 'PATCH #update' do
    let!(:email_template) do
      EmailTemplate.create(
        template_title: "Initial Title",
        subject_title: "Initial Subject",
        email_body: "<div><!--block-->Initial Body</div>",
        template_name: "ticket created",
        template_type: "end_users"
      )
    end

    context 'with valid parameters' do
      let(:valid_params) do
        {
          id: email_template.id,
          email_template: {
            template_title: 'Updated Title',
            subject_title: 'Updated Subject',
            email_body: '<div><!--block-->Updated Body</div>',
          }
        }
      end

      it 'updates the email template' do
        patch :update, params: valid_params, format: :json

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['status']).to eq('success')
        expect(json_response['email_template']).to be_present
        expect(json_response['email_template']['template_title']).to eq('Updated Title')
        expect(json_response['email_template']['subject_title']).to eq('Updated Subject')
        expect(json_response['email_template']['email_body']).to eq('<div><!--block-->Updated Body</div>')
        expect(json_response['email_template']['template_name']).to eq('ticket created')
        expect(json_response['email_template']['template_type']).to eq('end_users')
      end
    end

    context 'when the email template is not found' do
      it 'returns a not found message' do
        patch :update, params: { id: email_template.id + 1 }, format: :json

        expect(response).to have_http_status(:not_found)
        json_response = JSON.parse(response.body)
        expect(json_response['message']).to eq('Email Template was not found.')
      end
    end
  end

  describe 'DELETE #destroy' do
    let!(:email_template) do
      EmailTemplate.create(
        template_title: 'Test Template',
        subject_title: 'Test Subject',
        email_body: '<div><!--block-->Testing Body</div>',
        template_name: 'test_template',
        template_type: 'test_type',
        company_id: company.id,
        workspace_id: workspace.id
      )
    end

    context 'when the email template exists' do
      it 'deletes the email template' do
        delete :destroy, params: { id: email_template.id }, format: :json

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['status']).to eq('success')
        expect(json_response['message']).to eq('Email template deleted successfully')
      end
    end

    context 'when the email template does not exist' do
      it 'returns a not found message' do
        delete :destroy, params: { id: email_template.id + 1 }, format: :json

        expect(response).to have_http_status(:not_found)
        json_response = JSON.parse(response.body)
        expect(json_response['message']).to eq('Email Template was not found.')
      end
    end
  end
end
