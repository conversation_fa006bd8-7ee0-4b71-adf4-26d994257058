require 'rails_helper'
include Company<PERSON><PERSON><PERSON>elper

describe AgentLocationsController, type: :controller do
  create_company_and_user

  describe "#index" do
    context "with multiple agent locations" do
      before do
        15.times do
          create(:agent_location, company: company)
        end
      end

      it "returns a paginated list of agent locations" do
        get :index, params: { page_size: 10, page: 1 }, format: :json
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['agent_locations'].count).to eq(10)
        expect(json_response['page_count']).to eq(2)
      end
    end
  end

  describe "#update" do
    context "when toggling the is_enabled attribute" do
      let(:agent_location) { create(:agent_location, company: company) }

      it "toggles the is_enabled attribute" do
        patch :update, params: { id: agent_location.id, toggle: true }, format: :json
        agent_location.reload
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(agent_location.is_enabled).to eq(false)
      end
    end

    context "with valid parameters" do
      let(:agent_location) { create(:agent_location, company: company) }

      it "updates the agent location" do
        patch :update, params: { id: agent_location.id, agent: { location_id: company_user.location.id, managed_by_contributor_id: company_user.contributor.id } }, format: :json
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(agent_location.reload.location_id).to eq(company_user.location.id)
        expect(agent_location.reload.managed_by_contributor_id).to eq(company_user.contributor.id)
      end
    end
  end

  describe "#destroy" do
    context "with existing agent location" do
      let(:agent_location) { create(:agent_location, company: company) }

      it "deletes the agent location" do
        delete :destroy, params: { id: agent_location.id }, format: :json
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(AgentLocation.exists?(agent_location.id)).to be_falsey
      end
    end
  end
end
