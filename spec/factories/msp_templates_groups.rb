FactoryBot.define do
  factory :msp_templates_group, class: 'Msp::Templates::Group' do
    name { "Default Group Name" }
    association :company 

    trait :with_members do
      after(:create) do |group|
        create_list(:msp_templates_group_member, 3, msp_templates_group: group)
      end
    end

    trait :with_privileges do
      after(:create) do |group|
        create_list(:msp_templates_group_privilege, 2, msp_templates_group: group)
      end
    end
  end

  factory :msp_templates_group_member, class: 'Msp::Templates::GroupMember' do
    association :company_user 
    association :msp_templates_group 
  end

  factory :msp_templates_group_privilege, class: 'Msp::Templates::GroupPrivilege' do
    name { "Default Privilege Name" }
    permission_type { "read" }
    association :msp_templates_group 
  end
end
