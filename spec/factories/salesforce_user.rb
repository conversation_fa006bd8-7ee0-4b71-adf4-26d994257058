FactoryBot.define do
  factory :salesforce_user, class: "Integrations::Salesforce::User" do
    contact_id { "ContactId" }
    account_id { "AccountId" }
    call_center_id { "CallCenterId" }
    created_date { "CreatedDate" }
    email { "Email" }
    name { "Name" }
    first_name { "FirstName" }
    last_name { "LastName" }
    user_name { "UserName" }
    company_name { "CompanyName" }
    country { "Country" }
    last_login_date { "LastLoginDate" }
    is_active { "IsActive" }
    sales_id { "1234567" }
  end
end
