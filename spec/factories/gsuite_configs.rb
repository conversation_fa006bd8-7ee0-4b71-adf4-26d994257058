FactoryBot.define do
  factory :gsuite_config, class: "Integrations::Gsuite::Config" do
    token { "credentials.access_tokendsgdgsdg0-46ngdkki4uthwn3029" }
    scope {["https://www.googleapis.com/auth/admin.reports.usage.readonly","https://www.googleapis.com/auth/admin.directory.user.readonly","https://www.googleapis.com/auth/admin.directory.user.security"]}
    code { "params[:code]84u349ht4j9" }
    refresh_token { "credentials.refresh_tokenj943nlk6n34k6nk6jn=-5=-533" }
    client_id { Rails.application.credentials.google[:client_id] }
    expiration_time { 8496734896 }
  end
end
