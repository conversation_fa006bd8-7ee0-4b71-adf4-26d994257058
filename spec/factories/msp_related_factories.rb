FactoryBot.define do
  factory :child_company, class: 'Company' do
    name { "Child Company" }
    association :parent, factory: :company
  end
  
  factory :msp_templates_category, class: 'Msp::Templates::Category' do
    sequence(:name) { |n| "Template Category #{n}-#{SecureRandom.hex(2)}" }
    association :company
  end

  factory :msp_category, class: 'Category' do
    association :company
    sequence(:name) { |n| "Category #{n}-#{SecureRandom.hex(2)}" }
    association :msp_templates_category, factory: :msp_templates_category
  end

  factory :msp_templates_asset_type, class: 'Msp::Templates::AssetType' do
    sequence(:name) { |n| "Template Asset Type #{n}-#{SecureRandom.hex(2)}" }
    association :company
  end

  factory :msp_asset_type, class: 'AssetType' do
    association :company
    name { "Asset Type #{SecureRandom.hex(4)}" } # Make name unique to avoid conflicts
  end

  factory :msp_templates_document, class: 'Msp::Templates::Document' do
    sequence(:name) { |n| "Template Document #{n}-#{SecureRandom.hex(2)}" }
    association :company
  end

  factory :msp_document, class: 'Document' do
    association :company
    sequence(:name) { |n| "Document #{n}-#{SecureRandom.hex(2)}" }
    association :msp_templates_document, factory: :msp_templates_document
  end
end
