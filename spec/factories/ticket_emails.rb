FactoryBot.define do
  factory :ticket_email do
    subject { "MyString" }
    body_text { "MyText" }
    body_html { "<div>MyText</div>" }
    to { Faker::Internet.email }
    from { Faker::Internet.email }
    company_id { 1 }
    help_ticket_id { 1 }
    ticket_email_attachments { [] }
    sequence :message_id do |n|
      n.to_s
    end
    sequence :s3_message_id do |n|
      n.to_s
    end
  end
end
