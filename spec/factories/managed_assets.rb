FactoryBot.define do
  factory :managed_asset do
    description { "Apple Product" }
    manufacturer { "Mac" }
    name { "iPad" }
    fullname { "Apple iPad Pro 12.9-inch" }
    mac_addresses { [] }
    asset_type { CompanyAssetType.find_by(name: 'Desktop') }
    custom_status { CompanyAssetStatus.find_by(name: 'In Use') }
    company
    cost
    trait(:router) do
    	name { "AssetRouter" }
      fullname { "Cisco Enterprise Router Model 2900" }
      after(:build) do |managed_asset, evaluator|
        managed_asset.asset_softwares.new(name: 'Cisco OS', software_type: 'Operating System', product_key: 'asdfasdf')
        managed_asset.asset_type = CompanyAssetType.find_by(name: "Router")
      end
    end

    trait(:laptop) do
      name { "AssetLaptop" }
      fullname { "MacBook Pro 13-inch M2 Chip" }
      after(:build) do |managed_asset, evaluator|
        managed_asset.asset_softwares.new(name: 'MacOS', software_type: 'Operating System', product_key: '2342384023')
        managed_asset.asset_type = CompanyAssetType.find_by(name: "Laptop")
      end
    end

    trait(:phone) do
      name { "AssetPhone" }
      fullname { "iPhone 14 Pro Max 256GB" }
      after(:build) do |managed_asset, evaluator|
        managed_asset.asset_softwares.new(name: 'PhoneOS', software_type: 'Operating System', product_key: '8023847')
        managed_asset.asset_type = CompanyAssetType.find_by(name: "Phone")
      end
    end

    trait(:mobile) do
      name { "AssetMobile" }
      fullname { "Samsung Galaxy S23 Ultra 512GB" }
      after(:build) do |managed_asset, evaluator|
        managed_asset.asset_softwares.new(name: 'iOS', software_type: 'Operating System', product_key: '9348034')
        managed_asset.asset_type = CompanyAssetType.find_by(name: "Mobile")
      end
    end

    trait(:mobile_with_multiple_software) do
      name { "AssetMobile" }
      fullname { "Samsung Galaxy S23 Ultra 512GB with Multiple Apps" }
      after(:build) do |managed_asset, evaluator|
        managed_asset.asset_softwares.new([
          {
            name: 'iOS',
            software_type: 'Operating System'
          },
          {
            name: 'Slack',
            software_type: 'Application'
          },
          {
            name: 'Google Chrome Installer',
            software_type: 'Application'
          }
        ])
        managed_asset.asset_type = CompanyAssetType.find_by(name: "Mobile")
      end
    end

    trait(:printer) do
      name { "AssetPrinter" }
      fullname { "HP LaserJet Pro 4001n Monochrome Printer" }
      after(:build) do |managed_asset, evaluator|
        managed_asset.asset_type = CompanyAssetType.find_by(name: "Printer")
      end
    end

    after(:build) do |managed_asset, evaluator|
      department = managed_asset.company.departments.find_by(name: "Development")
      managed_asset.assignment_information = AssignmentInformation.create(department_id: department&.id || 1)
      managed_asset.hardware_detail = ComputerDetail.new
    end
    trait(:clone_asset) do
      name { "AssetLaptop" }
      fullname { "MacBook Pro 13-inch M2 Chip Clone Asset" }

      after(:build) do |managed_asset, evaluator|
        managed_asset.asset_type = CompanyAssetType.find_by(name: "Laptop")
        managed_asset.asset_softwares.new(name: 'MaOS', software_type: 'Operating System', product_key: '2342384023')
        managed_asset.asset_softwares.new(name: 'Chrome', software_type: 'Browser', product_key: '9876543210')
        managed_asset.managed_asset_attachments.new
        managed_asset.managed_asset_attachments.new
        managed_asset.details = { "qwerty" => "yes" }
      end
    end
    
    trait(:clone_integration_asset) do
      name { "AssetLaptop" }
      fullname { "MacBook Pro 13-inch M2 Chip Integration Asset" }

      after(:build) do |managed_asset, evaluator|
        managed_asset.asset_type = CompanyAssetType.find_by(name: "Laptop")
        managed_asset.source = "selfonboarding"
        managed_asset.system_details.new(
          detail_category: "chrome_extensions_info",
          detail_data: [
            { "name" => "Google Hangouts", "version" => "1.3.21", "profileName" => "Zeeshan" },
            { "name" => "Google Docs Offline", "version" => "1.66.0", "profileName" => "Zeeshan" }
          ]
        )
        managed_asset.asset_sources.new(
          source: "selfonboarding",
          asset_data: {
            "os" => "MAC OS",
            "memory" => "8.0",
            "source" => "selfonboarding",
            "os_name" => "Ventura (13.1)",
            "hostname" => "devoss-macbook-pro.local",
            "device_id" => "7FAE77CF-0317-5CEA-A46D-BEA056522835",
            "protocols" => nil,
            "asset_type" => "Laptop",
            "hard_drive" => "120.88 GB",
            "ip_address" => "***************",
            "os_version" => "Version 13.1 (Build 22C65)",
            "domain_name" => "local",
            "display_name" => "MacBook Pro",
            "manufacturer" => "Apple inc.",
            "os_serial_no" => nil,
            "mac_addresses" => ["8C:85:90:6D:C4:DC"],
            "processor_name" => "Intel(R) Core(TM) i5-7360U CPU @ 2.30GHz",
            "disk_encryption" => "Off",
            "disk_free_space" => "8.86 GB",
            "processor_cores" => 2,
            "hardware_version" => "499.********",
            "machine_serial_no" => "FVFVT0GCHV22",
            "remote_ip_address" => "***************",
            "processor_architecture" => "x86_64",
            "processor_logical_cores" => 4,
            "secondary_mac_addresses" => ["82:15:94:E2:C4:01", "8C:85:90:6D:C4:DC", "82:15:94:E2:C4:00"]
          }
        )
      end
    end
    
    trait(:clone_cloud_asset) do
      name { "AssetLaptop" }
      fullname { "AWS EC2 t2.medium Virtual Machine" }

      after(:build) do |managed_asset, evaluator|
        managed_asset.asset_type = CompanyAssetType.find_by(name: "Virtual Machine")
        managed_asset.source = "aws"
        managed_asset.cloud_asset_attributes.new([
          { key: "Display Name", value: "genuity-monitoring-staging" },
          { key: "Source", value: "aws" },
          { key: "Model", value: "t2.medium" },
          { key: "Asset Type", value: "Virtual Machine" },
          { key: "Os", value: "Linux/UNIX" },
          { key: "Os Name", value: "" }
        ])
      end
    end
  end
end
