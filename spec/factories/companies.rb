FactoryBot.define do
  factory :company do
    default_logo_url { "default_logo_url" }
    free_trial_days { 28 }
    timezone { "America/Chicago" }

    sequence :name do |n|
      "Test Company #{n}"
    end
    sequence :subdomain do |n|
      "test-#{n}"
    end
    phone_number { "1234567890" }
    is_reseller_company { false }

    #custom_forms { [self.create_default_custom_base_form, self.create_default_location_custom_base_form] }
    trait(:with_locations) do
      after(:build) do |company, evaluator|
        custom_form = company.create_default_location_custom_base_form
        name_field = custom_form.custom_form_fields.find_by(name: 'name')
        loc = build(:location, custom_form: custom_form)
        loc.custom_form_values << CustomFormValue.new(value_str: "HQ", custom_form_field: name_field)
        company.locations << loc

        loc = build(:location, custom_form: company.create_default_location_custom_base_form)
        loc.custom_form_values << CustomFormValue.new(value_str: "Sample Location", custom_form_field: name_field)
        company.locations << loc
      end

      before(:create) do |company, evaluator|
        company.locations.each { |l| l.save!(validate: false) }
      end
    end

    trait(:with_general_services) do
      transient do
        general_service_count { 1 }
      end

      after(:build) do |company, evaluator|
        company.general_services = create_list(:general_service, evaluator.general_service_count, company: company)
      end
    end

    trait(:with_vendors) do
      transient do
        vendor_count { 1 }
      end

      after(:build) do |company, evaluator|
        company.vendors = create_list(:vendor, evaluator.vendor_count, :with_contacts, :with_category, company: company)
      end
    end

    trait(:with_general_transactions) do
      transient do
        transaction_count { 1 }
      end

      after(:build) do |company, evaluator|
        company.general_transactions = create_list(:general_transaction, evaluator.transaction_count, company: company)
        company.general_transactions.each do |t|
          t.vendor = company.vendors.first
          t.save!
        end
      end
    end

    trait(:with_contracts) do
      transient do
        contract_count { 1 }
      end

      after(:build) do |company, evaluator|
        company.contracts = create_list(:contract, evaluator.contract_count, company: company)
      end
    end

    trait(:with_assets) do
      transient do
        asset_count { 1 }
      end

      after(:build) do |company, evaluator|
        company.managed_assets = create_list(:managed_asset, evaluator.asset_count, company: company)
      end

      before(:create) do |company, evaluator|
        company.managed_assets.each { |a| a.save!(validate: false) }
      end
    end

    trait(:with_categories) do
      transient do
        category_count { 4 }
      end

      after(:build) do |company, evaluator|
        company.categories = create_list(:category, evaluator.category_count, company: company)
      end
    end
  end
end
