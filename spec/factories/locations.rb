FactoryBot.define do
  factory :location do
    transient do
      name_field { company.custom_forms.location.first.custom_form_fields.find_by(name: 'name') }
      address_field { company.custom_forms.location.first.custom_form_fields.find_by(name: 'address') }
      city_field { company.custom_forms.location.first.custom_form_fields.find_by(name: 'city') }
      zip_field { company.custom_forms.location.first.custom_form_fields.find_by(name: 'zip') }
      phone_field { company.custom_forms.location.first.custom_form_fields.find_by(name: 'phone_number') }
    end

    before(:build) do |location, evaluator|
      company = location.company
      custom_form = company.custom_forms.location.first
      location.custom_form = custom_form
      attributes = location.attributes

      name_field = custom_form.custom_form_fields.find_by(name: 'name')
      address_field = custom_form.custom_form_fields.find_by(name: 'address')
      city_field = custom_form.custom_form_fields.find_by(name: 'city')
      zip_field = custom_form.custom_form_fields.find_by(name: 'zip')
      phone_field = custom_form.custom_form_field.find_by(name: 'phone_number')

      location.custom_form_values << build(:custom_form_value, 
                                           custom_form_field: name_field, 
                                           value_str: evaluator.name, 
                                           custom_form: custom_form, 
                                           company: company)
      location.custom_form_values << build(:custom_form_value, 
                                           custom_form_field: address_field, 
                                           value_str: evaluator.address, 
                                           custom_form: custom_form, 
                                           company: company)
      location.custom_form_values << build(:custom_form_value, 
                                           custom_form_field: city_field, 
                                           value_str: evaluator.city, 
                                           custom_form: custom_form, 
                                           company: company)
      location.custom_form_values << build(:custom_form_value, 
                                           custom_form_field: zip_field, 
                                           value_str: evaluator.zip, 
                                           custom_form: custom_form, 
                                           company: company)
      location.custom_form_values << build(:custom_form_value, 
                                           custom_form_field: phone_field, 
                                           value_str: evaluator.phone_number, 
                                           custom_form: custom_form, 
                                           company: company)
    end

    trait(:phone_number) do
      after(:build) do |location, evaluator|
        company = location.company
        custom_form = company.custom_forms.location.first
        phone_field = custom_form.custom_form_fields.find_by(name: 'phone_number') 
        location.custom_form_values << build(:custom_form_value,
                                             custom_form_field: phone_field,
                                             value_str: "07229865293",
                                             custom_form: custom_form,
                                             company: company)
      end
    end
  end
end
