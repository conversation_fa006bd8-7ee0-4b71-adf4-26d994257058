FactoryBot.define do
  factory :library_document do
    attached_file do
      file_url = "https://nulodgic-static-assets.s3.amazonaws.com/images/file.png"
      file = OpenURI.open_uri(file_url)
      Tempfile.create(["file", ".png"]) do |tmp_file|
        tmp_file.binmode
        tmp_file.write(file.read)
        tmp_file.rewind
        Rack::Test::UploadedFile.new(tmp_file.path, "image/png")
      end
    end
  end
end
