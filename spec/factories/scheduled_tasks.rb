FactoryBot.define do
  factory :scheduled_task do
    name { "Default Task Name" }
    description { "Default Task Description" }
    workspace
    assignee_id { create(:user).id }
    task_started_at do
      {
        'date' => (DateTime.current + 1.day).strftime('%Y-%m-%d'),
        'startTime' => '09:00',
        'timeZone' => 'America/Chicago'
      }
    end
    task_ended_at do
      {
        'date' => (DateTime.current + 2.days).strftime('%Y-%m-%d'),
        'endTime' => '17:00',
        'timeZone' => 'America/Chicago'
      }
    end
  end
end
