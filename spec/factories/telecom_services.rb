FactoryBot.define do
  factory :telecom_service do
    name { "Sample Telecom Service" }
    connection { "test connection" }
    circuit_id { "124" }
    network_speed { "100MB/s" }
    equipment { "test equipment" }
    company
    telecom_provider

    trait(:with_contracts) do
      after(:build) do |telecom_service|
        telecom_service.contract = create(:contract, company: telecom_service.company)
      end
    end

    trait(:with_phone_numbers) do
      after(:build) do |telecom_service, evaluator|
        telecom_service.phone_numbers << PhoneNumber.new(friendly_name: 'Main Phone', company: telecom_service.company, number: '**********')
      end
    end

    trait(:with_ip_addresses) do
      after(:build) do |telecom_service, evaluator|
        telecom_service.ip_addresses << IpAddress.new(friendly_name: 'Main IP', company: telecom_service.company, ip: '*******', telecom_provider: telecom_service.telecom_provider)
      end
    end
  end
end
