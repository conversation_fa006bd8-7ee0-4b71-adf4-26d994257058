FactoryBot.define do
  factory :email_format do |email_format|
    header_image { { io: Rack::Test::UploadedFile.new('spec/data/laptop.jpg', 'image/jpeg'), filename: 'header_image.jpeg' } }
    footer_image { { io: Rack::Test::UploadedFile.new('spec/data/laptop.jpg', 'image/jpeg'), filename: 'footer_image.jpeg' } }
    header_text { 'Header text' }
    footer_text { 'Footer text' }
    company
  end
end
