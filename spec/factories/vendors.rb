FactoryBot.define do
  factory :vendor do
    name { 'Test Vendor' }
    company
    default_tags { ["default"] }

    trait(:with_contacts) do
      transient do
        count { 1 }
      end

      after(:create) do |vendor, evaluator|
        vendor.vendor_contacts << create(:vendor_contact)
      end
    end

    trait(:with_category) do
      transient do
        category_count { 1 }
      end

      after(:build) do |vendor, evaluator|
        vendor.category = create(:category)
      end
    end
  end
end
