require 'faker'

FactoryBot.define do
  sequence :email do |n|
    value = nil
    until value.present?
      value = Faker::Internet.safe_email
      value = value.split('@').first + "@" + "gogenuity.com"
      value = nil if value.include?('@example.com') || User.find_by_email(value).present?
    end
    value
  end

  factory :user do
    email { FactoryBot.generate(:email) }
    password { "password" }
    first_name { Faker::Name.first_name }
    last_name { Faker::Name.last_name }
    terms_of_services { true }
    sign_in_count { 5 }
    confirmed_at { Time.now }
    has_confirmed_email { true }
    timezone { "America/Chicago" }
  end
end
