FactoryBot.define do
  factory :salesforce_config, class: "Integrations::Salesforce::Config" do
    token { "credentials.access_tokendsgdgsdg0-46ngdkki4uthwn3029" }
    refresh_token { "dsgkgnksjgd98dtj349n43t3=-4t0349itj34" }
    code { "dsgkgnksjgd98dtj349n43t3=-4t0349itj34" }
    signature { "dsgkgnksjgd98dtj349n43t3=-4t0349itj34" }
    id_token { "dsgkgnksjgd98dtj349n43t3=-4t0349itj34" }
    token_type { "Bearer" }
    instance_url { "www.instance.com" }
    issued_at { "843534895" }
    salesforce_id { "8435834895" }
  end
end
