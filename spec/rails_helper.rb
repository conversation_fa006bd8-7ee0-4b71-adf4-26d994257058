# This file is copied to spec/ when you run 'rails generate rspec:install'
require 'spec_helper'
ENV['RAILS_ENV'] = 'test'
require File.expand_path('../../config/environment', __FILE__)
# Prevent database truncation if the environment is production
abort("The Rails environment is running in production mode!") if Rails.env.production? && Rails.env.development?
require 'rspec/rails'
require 'database_cleaner'
require 'sidekiq/testing'
require "rspec/mocks/standalone"
Sidekiq::Testing.fake!
WebMock.disable_net_connect! allow: ["https://api.stripe.com/v1/customers"]
# Add additional requires below this line. Rails is not loaded until this point!

# Requires supporting ruby files with custom matchers and macros, etc, in
# spec/support/ and its subdirectories. Files matching `spec/**/*_spec.rb` are
# run as spec files by default. This means that files in spec/support that end
# in _spec.rb will both be required and run as specs, causing the specs to be
# run twice. It is recommended that you do not name files matching this glob to
# end with _spec.rb. You can configure this pattern with the --pattern
# option on the command line or in ~/.rspec, .rspec or `.rspec-local`.
#
# The following line is provided for convenience purposes. It has the downside
# of increasing the boot-up time by auto-requiring all files in the support
# directory. Alternatively, in the individual `*_spec.rb` files, manually
# require only the support files necessary.
#
Dir[Rails.root.join('spec/support/*.rb')].each { |f| require f }

# Checks for pending migrations and applies them before tests are run.
# If you are not using ActiveRecord, you can remove this line.
ActiveRecord::Migration.maintain_test_schema!
DatabaseCleaner.strategy = :truncation
DatabaseCleaner.clean

Rails.application.load_seed

RSpec.configure do |config|
  # Remove this line if you're not using ActiveRecord or ActiveRecord fixtures
  config.fixture_path = "#{::Rails.root}/spec/fixtures"

  # If you're not using ActiveRecord, or you'd prefer not to run each of your
  # examples within a transaction, remove the following line or assign false
  # instead of true.
  config.use_transactional_fixtures = true

  config.before(:all) do
    allow_any_instance_of(Monitoring::SyncRemoteCompanyWorker).to receive(:perform).and_return(true)
    allow_any_instance_of(Monitoring::DestroyRemoteCompanyWorker).to receive(:perform).and_return(true)
    allow_any_instance_of(Monitoring::SyncRemoteUserWorker).to receive(:perform).and_return(true)
    allow_any_instance_of(Monitoring::DestroyRemoteUserWorker).to receive(:perform).and_return(true)
    allow_any_instance_of(AppDirect::SyncCompanyWorker).to receive(:perform).and_return(true)
    allow_any_instance_of(AppDirect::UpdateCompanyUserWorker).to receive(:perform).and_return(true)

    # Rails.application.load_seed
  end

  config.before(:suite) { clear_test_cases_directory }
  config.after(:suite) { clear_test_cases_directory }

  Pusher.stub(:trigger)

  config.before(:each) do
    allow_any_instance_of(CognitoService).to receive(:delete_cognito_user).and_return(status: :ok, body: {})
    allow_any_instance_of(CognitoService).to receive(:send_forgot_password_request).and_return(OpenStruct.new(
      {
        code_delivery_details:
        OpenStruct.new({
            attribute_name: "string",
            delivery_medium: "EMAIL",
            destination: "string"
          })
      }
    ))
    allow_any_instance_of(CognitoService).to receive(:sign_up_user).and_return(OpenStruct.new(
      {
        user:
        OpenStruct.new({
            username: Faker::Name.name,
            attributes: [
              OpenStruct.new({name: "sub",value: Faker::Name.name}),
              OpenStruct.new({name: "email_verified",value: "true"}),
              OpenStruct.new({name: "custom: company_name", value: "string"}),
              OpenStruct.new({name: "custom: external_company_id", value: SecureRandom.uuid}),
              OpenStruct.new({name: "given_name", value: ""}),
              OpenStruct.new({name: "family_name", value: Faker::Name.name}),
              OpenStruct.new({name: "email", value: Faker::Internet.email})
            ],
            user_create_date: Time.current,
            user_last_modified_date: Time.current,
            enabled: true,
            user_status: "FORCE_CHANGE_PASSWORD",
            mfa_options: "null"
          })
      }
    ))
    WebMock.stub_request(:post, /#{Rails.application.credentials.eventing_api[:url]}\/company_users/).
      with(headers: {'Accept'=>'*/*', 'User-Agent'=>'Ruby'}).
      to_return(status: :ok, body: "stubbed response", headers: {})

    WebMock.stub_request(:post, "https://api.stripe.com/v1/customers").
      with(headers: {
          'Accept'=>'*/*',
          'Accept-Encoding'=>'gzip;q=1.0,deflate;q=0.6,identity;q=0.3',
          'Authorization'=>'Bearer sk_test_pUqy9UAe1Yq3v1lPrXBF4Sd5',
          'Content-Type'=>'application/x-www-form-urlencoded',
          'User-Agent'=>'Stripe/v1 RubyBindings/3.28.0',
      }).to_return(status: :ok, body: "", headers: {})

    WebMock.stub_request(:post, "#{Rails.application.credentials.eventing_api[:url]}/events").
      with(body: {"event_type"=>"created", "target_type"=>"Target", "user_id"=>"2"},
           headers: {
          'Accept'=>'*/*',
          'Accept-Encoding'=>'gzip, deflate',
          'Content-Length'=>'47',
          'Content-Type'=>'application/x-www-form-urlencoded',
           }).
        to_return(status: :ok, body: {user: {reward_points: 200}}.to_json, headers: {})

    Sidekiq::Job.clear_all
  end

  # RSpec Rails can automatically mix in different behaviours to your tests
  # based on their file location, for example enabling you to call `get` and
  # `post` in specs under `spec/controllers`.
  #
  # You can disable this behaviour by removing the line below, and instead
  # explicitly tag your specs with their type, e.g.:
  #
  #     RSpec.describe UsersController, :type => :controller do
  #       # ...
  #     end
  #
  # The different available types are documented in the features, such as in
  # https://relishapp.com/rspec/rspec-rails/docs
  config.infer_spec_type_from_file_location!

  # Filter lines from Rails gems in backtraces.
  config.filter_rails_from_backtrace!
  # arbitrary gems may also be filtered via:
  # config.filter_gems_from_backtrace("gem name")
  Shoulda::Matchers.configure do |config|
    config.integrate do |with|
      with.test_framework :rspec
      with.library :rails
    end
  end

  config.before(:each) do
    ActiveStorage::Current.url_options = { protocol: 'http', host: Rails.application.credentials.root_domain, port: Rails.application.credentials.port }
  end

  RSpec.configure do |config|
    config.include(Shoulda::Callback::Matchers::ActiveModel)
  end
end

def clear_test_cases_directory
  FileUtils.rm_rf(Dir["#{Rails.root}/tmp/storage/test_cases/*"])
end
