# spec/services/integrations/google_workspace/fetch_data_spec.rb
require 'rails_helper'
require 'ostruct'
require 'httparty'

describe Integrations::GoogleWorkspace::FetchData do
  let(:company) { create(:company) }
  let!(:config) do
    create(:google_workspace_config, company: company, token: 'access-token-123', refresh_token: 'refresh-token-abc')
  end
  let(:service) { described_class.new(company.id) }

  describe '#fetch_all_mobile_devices' do
    it 'returns a list of mobile devices from Google API' do
      mock_client = instance_double(Google::Apis::AdminDirectoryV1::DirectoryService)
      allow(service).to receive(:client).and_return(mock_client)

      device = OpenStruct.new(
        mobiledevices: [
          OpenStruct.new(
                          model: 'Pixel', 
                          os: 'Android 13', 
                          serial_number: '0000-0003-6421-4278-0245-7654-53',
                          manufacturer: 'Google'
                        )
        ]
      )

      allow(mock_client).to receive(:list_mobile_devices).and_return(device)
      expect(service.fetch_all_mobile_devices(nil)).to eq(device)
    end
  end

  describe '#fetch_all_chrome_os_devices' do
    it 'returns a list of chrome os devices from Google API' do
      mock_client = instance_double(Google::Apis::AdminDirectoryV1::DirectoryService)
      allow(service).to receive(:client).and_return(mock_client)

      device = OpenStruct.new(
        chromeosdevices: [
          OpenStruct.new(
                          model: 'Chromebook C425', 
                          os: 'ChromeOS', 
                          serial_number: '0000-0003-6421-4278-0245-7654-53',
                          manufacturer: 'ASUS'
                        )
        ]
      )

      allow(mock_client).to receive(:list_chrome_os_devices).and_return(device)
      expect(service.fetch_all_chrome_os_devices(nil)).to eq(device)
    end
  end

  describe '#get_credentials' do
    it 'returns a valid auth URL containing required params' do
      url = service.get_credentials
      expect(url).to include("https://accounts.google.com/o/oauth2/v2/auth")
      expect(url).to include("client_id=")
      expect(url).to include("redirect_uri=")
    end
  end
end
