require 'rails_helper'
require 'ostruct'
require 'httparty'
include CompanyUserHelper

describe Integrations::SageAccounting::FetchData do
  before(:all) do
    @company =  FactoryBot.create(:company)
    FactoryBot.create(:sage_accounting_config, company_id: @company.id)
    @config ||= Company.find(@company.id).sage_accounting_config
    @sage_accounting_service = described_class.new(@company.id)
  end

  describe '#client' do
    it 'Fetch client responds with 200'  do
      client = Signet::OAuth2::Client.new(
                authorization_uri: 'https://www.sageone.com/oauth2/auth/central?filter=apiv3.1',
                client_id: "7a48a799-b262-4c74-bac1-9fb4effd521c/c20c8cfc-dd4a-4971-a2d9-c3f5982c1338",
                client_secret: "ZT2[x-j{-3wLr7In<nN[",
                response_type: "code",
                response_mode: "query",
                scope: 'full_access',
                redirect_uri: "http://localhost:3000/integrations/sage_accounting/oauth2callback",
                state: @company_id
          )
      allow_any_instance_of(described_class).to receive(:token).and_return(client)
      expect(@sage_accounting_service.client).to be_present
    end
  end

  describe '#token' do
    it 'Fetch token  responds with 200'  do
      token_detail = OpenStruct.new(
                      {parsed_response:
                        { "token_type":"Bearer",
                          "scope":"full_access",
                          "expires_in":300,
                          "access_token":"mock_access_token",
                          "refresh_token":"OAQABAAAAAAAP0whx6nwnkGU78H2kT1N0uPKky6sk9d7J6HQnQiM9bweL7lT4uAEsMDouHIXIEwdWZ5FFiEbogAA",
                          "refresh_token_expires_in":2678400
                        },
                        success?: true,
                        code: 200,
                        message: "OK",
                        header: "OK",
                        body: { "token_type":"Bearer",
                          "scope":"full_access",
                          "expires_in":300,
                          "refresh_token_expires_in":2678400,
                          "access_token":"mock-access-token",
                          "refresh_token":"mock-refresh-token",
                        },
                        request: {},
                      })

      got_token = {parsed_response:
                      { "token_type":"Bearer",
                        "scope":"full_access",
                        "expires_in":300,
                        "access_token":"mock_access_token",
                        "refresh_token":"OAQABAAAAAAAP0whx6nwnkGU78H2kT1N0uPKky6sk9d7J6HQnQiM9bweL7lT4uAEsMDouHIXIEwdWZ5FFiEbogAA",
                        "refresh_token_expires_in":2678400
                      },
                    response_status: true,
                    }
      allow(HTTParty).to receive(:post).and_return(token_detail)
      expect(@sage_accounting_service.token "3456ghj-hjkyu7jk-jhgf4").to eq(got_token)
    end
  end

  describe '#refresh_token' do
    it 'Fetch refresh_token responds with 200'  do
     token_detail = OpenStruct.new(
                      {parsed_response:
                         {"token_type":"Bearer",
                          "scope":"full_access",
                          "expires_in":300,
                          "refresh_token_expires_in":2678400,
                          "access_token":"eyJ0eXAiOiJKV",
                          "refresh_token":"mock_refresh_token",
                          },
                        success?: true,
                        code: 200,
                        message: "OK",
                        header: "OK",
                        body:
                          {"token_type":"Bearer",
                            "scope":"full_access",
                            "expires_in":300,
                            "refresh_token_expires_in":2678400,
                            "access_token":"mock_access_token",
                            "refresh_token":"mock_refresh_token"},
                        request: {},
                      })
      allow(HTTParty).to receive(:post).and_return(token_detail)
      expect(@sage_accounting_service.refresh_token).to eq(token_detail.parsed_response)
    end
  end

  describe '#Transactions' do
    it 'Fetch All Transactions responds with 200'  do
      fetch_transactions = OpenStruct.new({parsed_response:
                            {
                              "$total" => 2,
                              "$page" => 1,
                              "$next" => nil,
                              "$back" => nil,
                              "$itemsPerPage" => 20,
                              "$items" =>
                                [
                                  {
                                      "id" => "240f62a174914e42b4dd7d15e70bf33f",
                                      "displayed_as" => "2020-01-27",
                                      "$path" => "/other_payments/240f62a174914e42b4dd7d15e70bf33f"
                                  },
                                  {
                                      "id" => "453ab4fa6c9740a2b75964ea7187d3e2",
                                      "displayed_as" => "2020-01-27",
                                      "$path" => "/other_payments/453ab4fa6c9740a2b75964ea7187d3e2"
                                  }
                                ]
                             },
                              success?: true,
                              code: 200,
                              message: "OK",
                              header: "OK"
                    })
      allow(HTTParty).to receive(:get).and_return(fetch_transactions)
      expect(@sage_accounting_service.transactions "2020-01-27").to eq(fetch_transactions.parsed_response)
    end
  end

  describe 'Get individual Transaction' do
    it "Fetch Only individual Transaction Against id" do
      transaction = OpenStruct.new({parsed_response:
                      {
                        "id" => "453ab4fa6c9740a2b75964ea7187d3e2",
                        "displayed_as" => "2020-01-27",
                        "$path" => "/other_payments/453ab4fa6c9740a2b75964ea7187d3e2",
                        "created_at" => "2020-01-27T11:59:42Z",
                        "updated_at" => "2020-01-27T11:59:42Z",
                        "transaction" => {
                          "id" => "d6b6c26c1b0a46c0b2b1b5415ed8216c",
                          "displayed_as" => "",
                          "$path" => "/transactions/d6b6c26c1b0a46c0b2b1b5415ed8216c"
                          },
                        "transaction_type" => {
                          "id" => "OTHER_PAYMENT",
                          "displayed_as" => "Money out",
                          "$path" => "/transaction_types/OTHER_PAYMENT"
                          },
                        "payment_method" => {
                          "id" => "CHECK",
                          "displayed_as" => "Check",
                          "$path" => "/payment_methods/CHECK"
                          },
                        "contact" => {
                          "id" => "dcf1dfa05a49410b9f516ab5e78af1ec",
                          "displayed_as" => "Helo (*********)",
                          "$path" => "/contacts/dcf1dfa05a49410b9f516ab5e78af1ec"
                          },
                        "bank_account" => {
                          "id" => "ec8e797562bb47178e47108139681d62",
                          "displayed_as" => "Checking (1010)",
                          "$path" => "/bank_accounts/ec8e797562bb47178e47108139681d62"
                          },
                        "tax_address_region" => nil,
                        "date" => "2020-01-27",
                        "net_amount" => "30.0",
                        "tax_amount" => "0.0",
                        "total_amount" => "30.0",
                        "reference" => "",
                        "payment_lines" =>
                          [
                            {
                              "id" => "0dbcf03d06a84f94a87971fefb16ca6e",
                              "displayed_as" => "",
                              "ledger_account" =>
                                {
                                  "id" => "798f82003c3b11eaae730ee73a5c6c6b",
                                  "displayed_as" => "Prepaid Expenses (1400)",
                                  "$path" =>"/ledger_accounts/798f82003c3b11eaae730ee73a5c6c6b"
                                },
                              "details" => "",
                              "tax_rate" => nil,
                              "net_amount" => "30.0",
                              "tax_amount" => "0.0",
                              "total_amount" => "30.0",
                              "tax_breakdown" => [],
                              "is_purchase_for_resale" => false,
                              "trade_of_asset" => false,
                              "gst_amount" => nil,
                              "pst_amount" => nil
                            }
                        ],
                        "editable" => true,
                        "deletable" => true,
                        "withholding_tax_rate" => nil,
                        "withholding_tax_amount" => nil,
                    },
                        success?: true,
                        code: 200,
                        message: "OK",
                        header: "OK"
            })
        allow(HTTParty).to receive(:get).and_return(transaction)
        expect(@sage_accounting_service.get_transaction "453ab4fa6c9740a2b75964ea7187d3e2").to eq(transaction.parsed_response)
    end
  end
end
