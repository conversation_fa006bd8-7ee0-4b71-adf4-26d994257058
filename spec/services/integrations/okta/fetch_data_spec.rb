require 'rails_helper'
require 'oktakit'

describe Integrations::Okta::FetchData do
    let(:company) { create(:company) }

    let(:auth_params) do 
      { 
        token: 'mock-token',
        name: 'loream param',
        current_company: company
      }
    end

    let(:service) do
      described_class.new(auth_params)
    end

    describe 'Authenticate' do
      it "is authenticate" do
        allow_any_instance_of(Oktakit::Client).to receive(:list_users).and_return(true)
        expect(service.authenticate?).to be_present
        expect(Logs::ApiEvent.where(company_id: company.id,
                                    status: :success,
                                    class_name: described_class.name,
                                    api_type: "authenticate").count).to eq(1)
      end
    end

    describe '#all_users' do
      it 'Fetch All Users responds with 200'  do
        users = [[{
                  id: "00uqaq4pi0BdEwuwP356",
                  status: "ACTIVE",
                  created: "2019-06-17T11:51:03.000Z",
                  activated: nil,
                  statusChanged: "2019-06-17T12:02:45.000Z",
                  lastLogin: "2019-06-19T09:35:52.000Z",
                  lastUpdated: "2019-06-17T12:17:14.000Z",
                  passwordChanged: "2019-06-17T12:02:45.000Z",
                  profile: { 
                    firstName: "dev",
                    lastName: "madi",
                    mobilePhone: nil,
                    secondEmail: nil,
                    login: "<EMAIL>",
                    email: "<EMAIL>"
                  }
                }], 200]
      allow_any_instance_of(Oktakit::Client).to receive(:list_users).and_return(users)
      expect(service.fetch_users("/users").first.count).to eq(1)
      expect(Logs::ApiEvent.where(company_id: company.id, status: :success, class_name: described_class.name, api_type: "fetch users").count).to eq(1)
      end
    end

  describe '#all_apps' do
    it 'Fetch All Apps responds with 200'  do
      apps = [[{
                id: "0oaqaplu09hlTgEmS356",
                name: "10000ft",
                label: "10000ft",
                status: "ACTIVE",
                lastUpdated: "2019-06-17T12:09:28.000Z",
                created: "2019-06-17T12:04:17.000Z",
              }], 200]
      allow_any_instance_of(Oktakit::Client).to receive(:list_applications).and_return(apps)
      expect(service.fetch_apps("/apps").first.count).to eq(1)
      expect(Logs::ApiEvent.where(company_id: company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "fetch apps").count).to eq(1)
    end
  end

  describe '#all_users_app' do
    it 'Fetch All Users Assigned To App responds with 200'  do
      users = [[{
                id: "00uqaq4pi0BdEwuwP356",
                externalId: nil,
                created: "2019-06-17T12:09:50.000Z",
                lastUpdated: "2019-06-17T12:09:50.000Z",
                scope: "USER",
                status: "ACTIVE",
                statusChanged: "2019-06-17T12:09:50.000Z",
                passwordChanged: "2019-06-17T12:09:50.000Z",
                syncState: "DISABLED",
                credentials: {
                  userName: "<EMAIL>", password: {}
                }
              }], 200]
      allow_any_instance_of(Oktakit::Client).to receive(:list_users_assigned_to_application).and_return(users)
      expect(service.fetch_users_assigned_to_app("12038cHF90Yus3156W").first.count).to eq(1)
      expect(Logs::ApiEvent.where(company_id: company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "fetch app users").count).to eq(1)
    end
  end
end
