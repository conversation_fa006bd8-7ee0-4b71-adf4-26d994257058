require 'rails_helper'
require 'ostruct'
require 'sidekiq/testing'
include CompanyUserHelper

describe Integrations::SageIntacct::FetchData do
  create_company_and_user

  let(:company) { create(:company) }

  let(:config) do
    FactoryBot.create(:sage_intacct_config, company_id: company.id)
  end

  let(:client) do
    described_class.new(config)
  end

  let(:bills) do {
      "vendors" => [
        {"NAME" => "Ali", "EntityId" => "accountant"},
        {"NAME" => "Ahmad", "EntityId" => "unkown"}
      ],
      "vendorBills" => [
        {"VENDORID" => nil, "WHENCREATED" => "10/10/2020", "TOTALENTERED" => "500", "VENDORNAME" => "Test VEndor"},
        {"VENDORID" => nil, "WHENCREATED" => "10/20/2020", "TOTALENTERED" => "600", "VENDORNAME" => "Task"},
        {"VENDORID" => nil, "WHENCREATED" => "10/30/2020", "TOTALENTERED" => "900", "VENDORNAME" => "Task"}
      ]
    }
  end

  let(:event) do {
      api_type: "random",
      class_name: described_class,
      company_id:  @company_id,
      status: 1,
      response: @bills
    }
  end

  describe '#all_payments' do
    it 'Fetch Sage Intacct Payments' do
      allow_any_instance_of(described_class).to receive(:get_data_from_s3).and_return(bills)
      expect(client.get_data_from_s3).to eq(bills)
    end
  end

  describe 'Event Log' do
    before do
      allow_any_instance_of(RestClient::Request).to receive(:execute).and_return(double(code: 200))
    end

    it 'Create Event' do
      response = 'http://s3.amazonaws.com/hotglue-logs/test/subdomain-intacct-1638350853.json'
      allow_any_instance_of(Integrations::SageIntacct::FetchData).to receive(:get_data_from_s3).and_return(bills)

      allow_any_instance_of(Integrations::SageIntacct::SyncDataWorker).to receive(:uploaded_file_path).and_return(response)
      Sidekiq::Testing.inline! do
        FactoryBot.create(:sage_intacct_config, company_id: company.id)
      end

      expect(Logs::ApiEvent.where(company_id:company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "perform_etl").count).to eq(2)
    end
  end
end
