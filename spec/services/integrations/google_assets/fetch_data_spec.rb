require 'rails_helper'
describe Integrations::GoogleAssets::FetchData do
  let(:company) { FactoryBot.create(:company) }

  let(:client) do
    described_class.new(company.id)
  end

  describe '#list_instances' do
    it 'List All Instances '  do
      @instances_res = OpenStruct.new({entries: [
                                                  ["zones/us-west1-b",
                                                   {instances: [{:can_ip_forward=>false,
                                                                 :cpu_platform=>"Intel Broadwell",
                                                                 :creation_timestamp=>"2021-06-28T07:06:20.044-07:00",
                                                                 :deletion_protection=>false,
                                                                 :description=>"",
                                                                 :disks=>[{:device_name=>"instance-1"}],
                                                                 :machine_type=>"https://www.googleapis.com/compute/v1/projects/genuity-project/zones/us-west1-b/machineTypes/e2-micro",
                                                                 :name=>"instance-1",
                                                                 :network_interfaces=>[{:access_configs=>[{:kind=>"compute#accessConfig", :name=>"External NAT", :nat_ip=>"************"}], :network_ip=>"**********"}]
                                                               }]}]]})
      allow_any_instance_of(Integrations::GoogleAssets::FetchData).to receive(:authorizer).and_return(true)
      allow_any_instance_of(Google::Apis::ComputeV1::ComputeService).to receive(:fetch_all).and_return(@instances_res)
      expect(client.list_instances("genuity-project").count).to eq(1)
      expect(Logs::ApiEvent.where(company_id: company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "list_instances").count).to eq(1)
    end
  end

  describe '#get_disk' do
    it 'Get Disk Information'  do
      disk_res = OpenStruct.new({creation_timestamp: "2021-06-28T07:06:20.059-07:00",
                                 name: "instance-1",
                                 physical_block_size_bytes: "4096",
                                 size_gb: "10",
                                 status: "READY"})

      allow_any_instance_of(Integrations::GoogleAssets::FetchData).to receive(:authorizer).and_return(true)
      allow_any_instance_of(Google::Apis::ComputeV1::ComputeService).to receive(:get_disk).and_return(disk_res)
      expect(client.get_disk("project", "zone", "name")).to eq("10")
      expect(Logs::ApiEvent.where(company_id: company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "get_disk").count).to eq(1)
    end
  end

  describe '#get_machine_info' do
    it 'Get Virtual Machine Information'  do
      machine_res = OpenStruct.new({ creation_timestamp: "1969-12-31T16:00:00.000-08:00",
                                     description: "Efficient Instance, 2 vCPU (1/8 shared physical core) and 1 GB RAM",
                                     guest_cpus: 2,
                                     id: 334002,
                                     image_space_gb: 0,
                                     is_shared_cpu: true,
                                     memory_mb: 1024,
                                     name: "e2-micro" })
      machine = {cores: 2, memory: "1GB", is_shared_cpu: true}

      allow_any_instance_of(Integrations::GoogleAssets::FetchData).to receive(:authorizer).and_return(true)
      allow_any_instance_of(Google::Apis::ComputeV1::ComputeService).to receive(:get_machine_type).and_return(machine_res)
      expect(client.get_machine_info("project", "zone", "type")).to eq(machine)
      expect(Logs::ApiEvent.where(company_id: company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "get_machine_info").count).to eq(1)
    end
  end
end
