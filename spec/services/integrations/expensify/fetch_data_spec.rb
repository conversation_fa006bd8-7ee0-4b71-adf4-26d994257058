require 'rails_helper'
require 'ostruct'

describe Integrations::Expensify::FetchData do
  let(:company) { create(:company) }

  let(:config) do
    allow_any_instance_of(Integrations::Expensify::FetchData).to receive(:authenticate?).and_return(true)
    FactoryBot.create(:expensify_config, company_id: company.id)
  end

  let(:client) do
    described_class.new(config)
  end

  expense_report_request_response = OpenStruct.new({ "parsed_response": "myExportad62b04e-041c-4965-bb03-33283e183223.csv" })
  get_expense_report_response = OpenStruct.new({ "parsed_response": [["'"], [nil, "Microsoft", "*********", "Benefits", "2020-01-03"], ["            "], [nil, "Hudson News", "124434", "Labor", "2020-01-08"], [nil, "Google", "124400", "Fees", "2020-01-01"], ["            "], [nil, "Skype", "1234567800", "Uncategorized", "2020-01-03"], ["            '"]] })
  file_name = expense_report_request_response.parsed_response
  describe '#all_payments' do
    it 'Fetch expensify Payments' do
      response = [
        [nil, "Microsoft", "*********", "Benefits", "2020-01-03"],
        [nil, "Hudson News", "124434", "Labor", "2020-01-08"],
        [nil, "Google", "124400", "Fees", "2020-01-01"],
        [nil, "Skype", "1234567800", "Uncategorized", "2020-01-03"]
      ]

      allow_any_instance_of(described_class).to receive(:expense_report_request).and_return(expense_report_request_response)
      allow_any_instance_of(described_class).to receive(:get_expense_report).and_return(get_expense_report_response)
      expect(client.download_expensify_payments(true, file_name)).to eq(response)
    end
  end

  describe '#Authenticate' do
    it 'Authenticate credentials and create api event log' do
      allow_any_instance_of(described_class).to receive(:expense_report_request).and_return(expense_report_request_response)
      FactoryBot.create(:expensify_config, company_id: company.id)
      expect(Logs::ApiEvent.where(company_id: company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "authenticate").count).to eq(1)
    end
  end
end
