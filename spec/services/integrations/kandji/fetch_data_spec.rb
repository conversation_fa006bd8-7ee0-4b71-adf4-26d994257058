require 'rails_helper'
require 'ostruct'
require 'httparty'

describe Integrations::Kandji::FetchData do
  let(:company) { FactoryBot.create(:company) }
  let(:kandji_config) { FactoryBot.create(:kandji_config, :without_validations, company: company) }

  let(:service) do
    described_class.new(kandji_config)
  end

  describe '#authenticate?' do
    it 'Authenticates Kanji for valid response' do
      valid_authenticate_response = OpenStruct.new(
                                      { parsed_response:
                                        [
                                          {
                                            "device_id": "f25a87de-61e8-4ab2-9032-02f6678914a1",
                                            "device_name": "Test MacBook Air",
                                            "model": "MacBook Air (M1, 2020)",
                                            "serial_number": "AAAANNAAAA",
                                            "platform": "Mac",
                                            "os_version": "10.15.6",
                                            "mac_address": "00:0c:29:05:43:b6",
                                            "last_check_in": "2021-03-30T17:46:31.864498Z",
                                            "user": {
                                              "email": "<EMAIL>",
                                              "name": "Test User",
                                              "id": 23,
                                              "is_archived": false
                                            },
                                            "asset_tag": "",
                                            "blueprint_id": "2118bda6-8772-4c5b-ae0a-bb506b2b9516",
                                            "mdm_enabled": true,
                                            "agent_installed": true,
                                            "is_missing": false,
                                            "is_removed": false,
                                            "agent_version": "2.7.3 (1253)",
                                            "first_enrollment": "2021-03-29 21:46:13.552931+00:00",
                                            "last_enrollment": "2021-03-29 21:46:13.552931+00:00",
                                            "blueprint_name": "All Staff"
                                          }
                                        ],
                                        code: 200
                                      }
                                    )
      allow(HTTParty).to receive(:get).and_return(valid_authenticate_response)
      expect(service.authenticate?).to eq(true)
      expect(Logs::ApiEvent.count).to eq(1)
      expect(Logs::ApiEvent.first.status).to eq('success')
    end

    it 'Authenticates Kanji for invalid response' do
      invalid_authenticate_response = OpenStruct.new(
                                        { parsed_response: nil,
                                          code: 400
                                        }
                                      )
      allow(HTTParty).to receive(:get).and_return(invalid_authenticate_response)
      expect(service.authenticate?).to eq(false)
      expect(Logs::ApiEvent.count).to eq(1)
      expect(Logs::ApiEvent.first.status).to eq('error')
    end
  end

  describe '#list_devices' do
    it 'Gets all the devices' do
      list_devices = OpenStruct.new(
                      { parsed_response:
                        [
                          {
                            "device_id": "f25a87de-61e8-4ab2-9032-02f6678914a1",
                            "device_name": "Test MacBook Air",
                            "model": "MacBook Air (M1, 2020)",
                            "serial_number": "AAAANNAAAA",
                            "platform": "Mac",
                            "os_version": "10.15.6",
                            "mac_address": "00:0c:29:05:43:b6",
                            "last_check_in": "2021-03-30T17:46:31.864498Z",
                            "user": {
                              "email": "<EMAIL>",
                              "name": "Test User",
                              "id": 23,
                              "is_archived": false
                            },
                            "asset_tag": "",
                            "blueprint_id": "2118bda6-8772-4c5b-ae0a-bb506b2b9516",
                            "mdm_enabled": true,
                            "agent_installed": true,
                            "is_missing": false,
                            "is_removed": false,
                            "agent_version": "2.7.3 (1253)",
                            "first_enrollment": "2021-03-29 21:46:13.552931+00:00",
                            "last_enrollment": "2021-03-29 21:46:13.552931+00:00",
                            "blueprint_name": "All Staff"
                          }
                        ],
                        headers: { 'link' => ['<None>; rel="next"'] },
                        code: 200
                      }
                    )
      allow(HTTParty).to receive(:get).and_return(list_devices)
      devices, next_url = service.list_devices(nil)
      expect(devices).to eq(list_devices.parsed_response)
      expect(next_url).to eq(nil)
      expect(Logs::ApiEvent.count).to eq(1)
      expect(Logs::ApiEvent.first.status).to eq('success')
    end
  end

  describe '#get_device_details' do
    it 'Gets the device details' do
      device_details = OpenStruct.new(
                        { parsed_response:
                          {
                            "general": {
                              "device_id": "f25a87de-61e8-4ab2-9032-02f6678914a1",
                              "device_name": "LAB01",
                              "last_enrollment": "2022-10-03 22:28:50.459432+00:00",
                              "first_enrollment": "2022-10-03 22:28:50.459432+00:00",
                              "model": "Apple TV 4K (2nd generation)",
                              "platform": "AppleTV",
                              "os_version": "16.0",
                              "system_version": "",
                              "boot_volume": "",
                              "time_since_boot": "",
                              "last_user": "",
                              "asset_tag": "KANDJI-TDGQWKQ5WT",
                              "assigned_user": "",
                              "blueprint_name": "ATV - Basic",
                              "blueprint_uuid": "19218946-0760-444f-ad9d-92916a721450"
                            },
                            "mdm": {
                              "mdm_enabled": "True",
                              "install_date": "2022-10-03 22:28:50.113208+00:00",
                              "last_check_in": "2022-11-04 17:56:23.766099+00:00",
                              "mdm_enabled_user": []
                            },
                            "activation_lock": {
                              "bypass_code_failed": false,
                              "user_activation_lock_enabled": nil,
                              "device_activation_lock_enabled": false,
                              "activation_lock_allowed_while_supervised": nil,
                              "activation_lock_supported": false
                            },
                            "filevault": {
                              "filevault_enabled": nil,
                              "filevault_recoverykey_type": "",
                              "filevault_prk_escrowed": false,
                              "filevault_next_rotation": "",
                              "filevault_regen_required": false
                            },
                            "automated_device_enrollment": {},
                            "kandji_agent": {
                              "agent_installed": "False",
                              "install_date": "",
                              "last_check_in": "2022-11-04T17:56:23.766105Z",
                              "agent_version": ""
                            },
                            "hardware_overview": {
                              "uuid": "6c8f9096198b54a03b17aa930204850091e616f8",
                              "serial_number": "TDGQWKQ5WT",
                              "model_identifier": "AppleTV11,1"
                            },
                            "volumes": [],
                            "network": {},
                            "recovery_information": {
                              "recovery_lock_enabled": false,
                              "firmware_password_exist": false,
                              "firmware_password_pending": false,
                              "password_rotation_scheduled": nil
                            },
                            "users": {
                              "regular_users": [],
                              "system_users": []
                            },
                            "installed_profiles": [],
                            "apple_business_manager": {},
                            "security_information": {
                              "remote_desktop_enabled": nil
                            }
                          },
                          code: 200
                        }
                      )
      allow(HTTParty).to receive(:get).and_return(device_details)
      expect(service.get_device_details('f25a87de-61e8-4ab2-9032-02f6678914a1')).to eq(device_details.parsed_response)
      expect(Logs::ApiEvent.count).to eq(1)
      expect(Logs::ApiEvent.first.status).to eq('success')
    end
  end

  describe '#get_device_apps' do
    it 'Gets the device apps' do
      device_apps = OpenStruct.new(
                      { parsed_response: {
                          "device_id": "f25a87de-61e8-4ab2-9032-02f6678914a1",
                          "apps": [
                            {
                              "app_id": "11012",
                              "app_name": "Activity Monitor",
                              "bundle_id": "com.apple.ActivityMonitor",
                              "bundle_size": "4802065",
                              "app_store_vendable": "",
                              "device_based_vpp": "",
                              "source": "Apple",
                              "process": "Activity Monitor",
                              "signature": "",
                              "creation_date": "2021-03-25T18:33:21.720616Z",
                              "modification_date": "2021-03-25T18:33:21.720644Z",
                              "path": "/System/Applications/Utilities/Activity Monitor.app",
                              "version": "10.14"
                            },
                            {
                              "app_id": "11013",
                              "app_name": "AirPort Utility",
                              "bundle_id": "com.apple.airport.airportutility",
                              "bundle_size": "47505998",
                              "app_store_vendable": "",
                              "device_based_vpp": "",
                              "source": "Apple",
                              "process": "AirPort Utility",
                              "signature": "",
                              "creation_date": "2021-03-25T18:33:21.720753Z",
                              "modification_date": "2021-03-25T18:33:21.720761Z",
                              "path": "/System/Applications/Utilities/AirPort Utility.app",
                              "version": "6.3.9"
                            },
                            {
                              "app_id": "11014",
                              "app_name": "App Store",
                              "bundle_id": "com.apple.AppStore",
                              "bundle_size": "18300985",
                              "app_store_vendable": "",
                              "device_based_vpp": "",
                              "source": "Apple",
                              "process": "App Store",
                              "signature": "",
                              "creation_date": "2021-03-25T18:33:21.720815Z",
                              "modification_date": "2021-03-25T18:33:21.720822Z",
                              "path": "/System/Applications/App Store.app",
                              "version": "3.0"
                            },
                            {
                              "app_id": "11015",
                              "app_name": "Audio MIDI Setup",
                              "bundle_id": "com.apple.audio.AudioMIDISetup",
                              "bundle_size": "5159142",
                              "app_store_vendable": "",
                              "device_based_vpp": "",
                              "source": "Apple",
                              "process": "Audio MIDI Setup",
                              "signature": "",
                              "creation_date": "2021-03-25T18:33:21.720883Z",
                              "modification_date": "2021-03-25T18:33:21.720891Z",
                              "path": "/System/Applications/Utilities/Audio MIDI Setup.app",
                              "version": "3.5"
                            },
                            {
                              "app_id": "11016",
                              "app_name": "Automator",
                              "bundle_id": "com.apple.Automator",
                              "bundle_size": "6915571",
                              "app_store_vendable": "",
                              "device_based_vpp": "",
                              "source": "Apple",
                              "process": "Automator",
                              "signature": "",
                              "creation_date": "2021-03-25T18:33:21.720941Z",
                              "modification_date": "2021-03-25T18:33:21.720948Z",
                              "path": "/System/Applications/Automator.app",
                              "version": "2.10"
                            },
                            {
                              "app_id": "11017",
                              "app_name": "BBEdit",
                              "bundle_id": "com.barebones.bbedit",
                              "bundle_size": "55003874",
                              "app_store_vendable": "",
                              "device_based_vpp": "",
                              "source": "Identified Developer",
                              "process": "BBEdit",
                              "signature": "",
                              "creation_date": "2021-03-25T18:33:21.720996Z",
                              "modification_date": "2021-03-25T18:33:21.721002Z",
                              "path": "/Applications/BBEdit.app",
                              "version": "13.5.5"
                            },
                            {
                              "app_id": "11018",
                              "app_name": "Bluetooth File Exchange",
                              "bundle_id": "com.apple.BluetoothFileExchange",
                              "bundle_size": "1162837",
                              "app_store_vendable": "",
                              "device_based_vpp": "",
                              "source": "Apple",
                              "process": "Bluetooth File Exchange",
                              "signature": "",
                              "creation_date": "2021-03-25T18:33:21.721049Z",
                              "modification_date": "2021-03-25T18:33:21.721056Z",
                              "path": "/System/Applications/Utilities/Bluetooth File Exchange.app",
                              "version": "7.0.6"
                            },
                            {
                              "app_id": "11019",
                              "app_name": "Books",
                              "bundle_id": "com.apple.iBooksX",
                              "bundle_size": "54267763",
                              "app_store_vendable": "",
                              "device_based_vpp": "",
                              "source": "Apple",
                              "process": "Books",
                              "signature": "",
                              "creation_date": "2021-03-25T18:33:21.721102Z",
                              "modification_date": "2021-03-25T18:33:21.721108Z",
                              "path": "/System/Applications/Books.app",
                              "version": "2.4"
                            },
                            {
                              "app_id": "11020",
                              "app_name": "Boot Camp Assistant",
                              "bundle_id": "com.apple.bootcampassistant",
                              "bundle_size": "4441210",
                              "app_store_vendable": "",
                              "device_based_vpp": "",
                              "source": "Apple",
                              "process": "Boot Camp Assistant",
                              "signature": "",
                              "creation_date": "2021-03-25T18:33:21.721155Z",
                              "modification_date": "2021-03-25T18:33:21.721162Z",
                              "path": "/System/Applications/Utilities/Boot Camp Assistant.app",
                              "version": "6.1.0"
                            },
                            {
                              "app_id": "11021",
                              "app_name": "Calculator",
                              "bundle_id": "com.apple.calculator",
                              "bundle_size": "5989994",
                              "app_store_vendable": "",
                              "device_based_vpp": "",
                              "source": "Apple",
                              "process": "Calculator",
                              "signature": "",
                              "creation_date": "2021-03-25T18:33:21.721224Z",
                              "modification_date": "2021-03-25T18:33:21.721231Z",
                              "path": "/System/Applications/Calculator.app",
                              "version": "10.15.4"
                            },
                            {
                              "app_id": "11066",
                              "app_name": "VoiceOver Utility",
                              "bundle_id": "com.apple.VoiceOverUtility",
                              "bundle_size": "10448764",
                              "app_store_vendable": "",
                              "device_based_vpp": "",
                              "source": "Apple",
                              "process": "VoiceOver Utility",
                              "signature": "",
                              "creation_date": "2021-03-25T18:33:21.724170Z",
                              "modification_date": "2021-03-25T18:33:21.724177Z",
                              "path": "/System/Applications/Utilities/VoiceOver Utility.app",
                              "version": "10"
                            }
                          ]
                        },
                        code: 200
                      }
                    )
      allow(HTTParty).to receive(:get).and_return(device_apps)
      expect(service.get_device_apps('f25a87de-61e8-4ab2-9032-02f6678914a1')).to eq(device_apps.parsed_response)
      expect(Logs::ApiEvent.count).to eq(1)
      expect(Logs::ApiEvent.first.status).to eq('success')
    end
  end
end
