require 'rails_helper'
require 'ostruct'
require 'httparty'

describe Integrations::AzureAd::FetchData do
  before(:all) do
    @company =  FactoryBot.create(:company)
    FactoryBot.create(:azure_ad_config, company_id: @company.id)
    @config ||= Company.find(@company.id).azure_ad_config
    @azure_ad_service = described_class.new(@company.id)
  end

  describe '#token' do
    it 'Fetch token  responds with 200'  do
      token_detail = OpenStruct.new(
                      { parsed_response:
                        { "token_type": "Bearer",
                          "scope": "offline_access User.Read.All",
                          "expires_in": 3600,
                          "ext_expires_in": 3600,
                          "access_token": "mock_access_token",
                          "refresh_token": "OAQABAAAAAAAP0whx6nwnkGU78H2kT1N0uPKky6sk9d7J6HQnQiM9bweL7lT4uAEsMDouHIXIEwdWZ5FFiEbogAA",
                          "id_token": "mock-id-token"
                        },
                        code: 200,
                        success?: true,
                        message: "OK",
                        header: "OK",
                        body: { "token_type":" Bearer",
                          "scope":"offline_access User.Read.All",
                          "expires_in": 3600,
                          "ext_expires_in": 3600,
                          "access_token": "mock-access-token",
                          "refresh_token": "mock-refresh-token",
                          "id_token": "mock-id-token"
                        },
                        request: {},
                      })
      allow(HTTParty).to receive(:post).and_return(token_detail)
      expect(@azure_ad_service.token("3456ghj-hjkyu7jk-jhgf4")).to eq(token_detail.parsed_response)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: "success", api_type: "token", class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#refresh_token' do
    it 'Fetch refresh_token responds with 200'  do
       token_detail = OpenStruct.new(
                        { parsed_response:
                           { "token_type": "Bearer",
                            "scope": "offline_access User.Read.All",
                            "expires_in": 3600,
                            "ext_expires_in": 3600,
                            "access_token": "eyJ0eXAiOiJKV",
                            "refresh_token": "mock_refresh_token",
                            "id_token": "mock-id-token"},
                          code: 200,
                          success?: true,
                          message: "OK",
                          header: "OK",
                          body: { "token_type": "Bearer",
                            "scope": "offline_access User.Read.All",
                            "expires_in": 3600,
                            "ext_expires_in": 3600,
                            "access_token": "mock_access_token",
                            "refresh_token": "mock_refresh_token",
                            "id_token": "mock-id-token"},
                          request: {},
                        })
      allow(HTTParty).to receive(:post).and_return(token_detail)
      expect(@azure_ad_service.refresh_token).to eq(token_detail.parsed_response)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: "success", api_type: "refresh_token", class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#get_users' do
    it 'Fetch All Users responds with 200'  do
        users = OpenStruct.new({parsed_response:
                  { "value"=>
                    [{
                      "businessPhones": [],
                      "displayName": "Robert Lasch",
                      "givenName": "Robert Lasch",
                      "jobTitle": nil,
                      "mail": nil,
                      "mobilePhone": nil,
                      "officeLocation": nil,
                      "preferredLanguage": "en-GB",
                      "surname": nil,
                      "userPrincipalName": "<EMAIL>",
                      "id": "ee331c9d-eaae-4fff-a0d4-12b2a769d88f"
                    }]
                  },
                  code: 200,
                  success?: true,
                  message: "OK",
                  header: "OK"
                })
      allow(HTTParty).to receive(:get).and_return(users)
      expect(@azure_ad_service.get_users).to eq(users)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: "success", api_type: "get_users", class_name: described_class.name).count).to eq(1)
    end
  end
end
