require 'rails_helper'
require 'ostruct'

describe Integrations::Xero::FetchData do
  let(:company) { create(:company) }

  let(:config) { FactoryBot.create(:xero_config, company_id: company.id) }

  let(:client) do
    described_class.new(config)
  end

  describe 'Refresh Token' do
    it "request for refresh token" do
      resp = {
              token: "mock-token",
              secret: "mock-secret"
            }

      allow_any_instance_of(Xeroizer::OAuth2Application).to receive(:renew_access_token).and_return(resp)
      expect(client.refresh_token).to eq(resp)
      expect(Logs::ApiEvent.where(company_id: company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: 'get refresh token').count).to eq(1)
    end
  end

  describe '#all_paymensts' do
      it 'Fetch All Payments'  do
        payments =  [
                      {
                        payment_id: SecureRandom.uuid,
                        date: Date.today,
                        amount: 31.39,
                        invoice: {
                          contact: {
                            contact_id: SecureRandom.uuid,
                            name: "<PERSON><PERSON>"
                          },
                          currency_code: "USD",
                          type: "ACCPAY",
                          invoice_id: SecureRandom.uuid,
                          invoice_number: "AP"
                        }
                      }
                    ]
      allow_any_instance_of(described_class).to receive(:get_xero_payments).and_return(payments)
      expect(client.fetch_xero_payments true).to eq(payments)
      expect(Logs::ApiEvent.where(company_id: company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "fetch payments").count).to eq(1)
      end
  end
end
