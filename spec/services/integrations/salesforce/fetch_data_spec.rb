require 'rails_helper'
require 'ostruct'
require 'httparty'
include CompanyUserHelper

describe Integrations::Salesforce::FetchData do
  before(:all) do
    @company =  FactoryBot.create(:company)
    FactoryBot.create(:salesforce_config, company_id: @company.id)
    @config ||= Company.find(@company.id).salesforce_config
    @salesforce_service = described_class.new(@company.id)
  end

  describe '#client' do
    it 'Fetch client responds with 200'  do
      client = Signet::OAuth2::Client.new(
        authorization_uri: "https://login.salesforce.com/services/oauth2/authorize",
        client_id: Rails.application.credentials.salesforce[:client_id],
        response_type: "code",
        redirect_uri: "#{Rails.application.credentials[:domain_with_port]}/integrations/salesforce/authentication/callback",
        state: @company.id)
      allow_any_instance_of(described_class).to receive(:token).and_return(client)
      expect(@salesforce_service.client).to be_present
    end
  end

  describe '#token' do
    it 'Fetch token  responds with 200'  do
      token_detail = OpenStruct.new(
                      {parsed_response:
                        {
                          "access_token"=>"00D3i000000tXr3!AQcAQFUMZ4hwoN7rSJxGm6wCSLxng6dYaJOO5BKl0PWno54Y.Zn_GT5SfLwACqZ1Y1SRa0Ty1AYTXCz3y_Gz7vvab2RvZvn.",
                          "refresh_token"=>"mock_refresh_token", "signature"=>"SXLthfilVE6hj/Ur8T0hHmfeNHGxZpuPPti+J77OXqY=",
                          "scope"=>"refresh_token full",
                          "id_token"=>"mock-id-token",
                          "instance_url"=>"https://na112.salesforce.com",
                          "id"=>"https://login.salesforce.com/id/00D3i000000tXr3EAE/0053i000001cfPMAAY",
                          "token_type"=>"Bearer",
                          "issued_at"=>"1567064791103"},
                        code: 200,
                        success?: true,
                        message: "OK",
                        header: "OK",
                        body: { "token_type":"Bearer",
                          "scope":"Directory.AccessAsUser.All Directory.Read.All Directory.ReadWrite.All email openid profile Reports.Read.All User.Read User.ReadBasic.All User.ReadWrite User.ReadWrite.All",
                          "expires_in":3600,
                          "ext_expires_in":3600,
                          "access_token":"mock_access_token",
                          "refresh_token":"mock_refresh_token",
                          "id_token":"mock_id_token"
                        },
                        request: {},
                      })
      allow(HTTParty).to receive(:post).and_return(token_detail)
      expect(@salesforce_service.token "3456ghj-hjkyu7jk-jhgf4").to eq(token_detail.parsed_response)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: "success", api_type: "token", class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#refresh_token' do
    it 'Fetch refresh_token responds with 200'  do
       token_detail = OpenStruct.new(
                        {parsed_response:
                          {
                            "access_token"=>"00D3i000000tXr3!AQcAQIKWnGXuxsT.1k0ki0K1iqljE.e0Hv5xB9dgYmU8bhM4Qu6kuMBZwrZW2baBxaVjshEBunOLIbhWZ_ywsa.D3WXnzHew",
                            "signature"=>"kHry99Y4J/veeJgcvaYrry7VYishizl/z2mZJEiSJjQ=",
                            "scope"=>"refresh_token full",
                            "instance_url"=>"https://na112.salesforce.com", "id"=>"https://login.salesforce.com/id/00D3i000000tXr3EAE/0053i000001cfPMAAY",
                            "token_type"=>"Bearer",
                            "issued_at"=>"1567064487738"},
                          code: 200,
                          success?: true,
                          message: "OK",
                          header: "OK",
                          body: {"token_type":"Bearer",
                            "scope":"Directory.AccessAsUser.All Directory.Read.All Directory.ReadWrite.All email openid profile Reports.Read.All User.Read User.ReadBasic.All User.ReadWrite User.ReadWrite.All",
                            "expires_in":3600,
                            "ext_expires_in":3600,
                            "access_token":"mock_access_token",
                            "refresh_token":"mock_refresh_token",
                            "id_token":"mock_id_token"},
                          request: {},
                        })
      allow(HTTParty).to receive(:post).and_return(token_detail)
      expect(@salesforce_service.refresh_token).to eq(token_detail.parsed_response)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: "success", api_type: "refresh_token", class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#get_users' do
    it 'Fetch All Users responds with 200'  do
      users = OpenStruct.new({parsed_response:
                {"totalSize"=>5,
                  "done"=>true,
                  "records"=>[
                    {"attributes"=>{"type"=>"User", "url"=>"/services/data/v42.0/sobjects/User/0053i0000028seRAAQ"}, "Id"=>"0053i0000028seRAAQ", "Name"=>"Automated Process", "Email"=>"autoproc@00d3i000000txr3eae", "Country"=>"US", "IsActive"=>true, "FirstName"=>"Automated", "LastName"=>"Process", "CompanyName"=>"GoGenuity", "Username"=>"autoproc@00d3i000000txr3eae", "CreatedDate"=>"2019-08-21T07:08:21.000+0000", "UserType"=>"AutomatedProcess", "ContactId"=>nil, "AccountId"=>nil, "CallCenterId"=>nil, "LastLoginDate"=>nil},
                    {"attributes"=>{"type"=>"User", "url"=>"/services/data/v42.0/sobjects/User/0053i000001cfPMAAY"}, "Id"=>"0053i000001cfPMAAY", "Name"=>"User Name", "Email"=>"<EMAIL>", "Country"=>"US", "IsActive"=>true, "FirstName"=>"User", "LastName"=>"Name", "CompanyName"=>"GoGenuity", "Username"=>"<EMAIL>", "CreatedDate"=>"2019-08-21T07:08:21.000+0000", "UserType"=>"Standard", "ContactId"=>nil, "AccountId"=>nil, "CallCenterId"=>nil, "LastLoginDate"=>"2019-08-29T07:46:31.000+0000"},
                    {"attributes"=>{"type"=>"User", "url"=>"/services/data/v42.0/sobjects/User/0053i0000028seQAAQ"}, "Id"=>"0053i0000028seQAAQ", "Name"=>"Integration User", "Email"=>"<EMAIL>", "Country"=>"US", "IsActive"=>true, "FirstName"=>"Integration", "LastName"=>"User", "CompanyName"=>"GoGenuity", "Username"=>"<EMAIL>", "CreatedDate"=>"2019-08-21T07:08:21.000+0000", "UserType"=>"Standard", "ContactId"=>nil, "AccountId"=>nil, "CallCenterId"=>nil, "LastLoginDate"=>nil},
                    {"attributes"=>{"type"=>"User", "url"=>"/services/data/v42.0/sobjects/User/0053i0000028seUAAQ"}, "Id"=>"0053i0000028seUAAQ", "Name"=>"Security User", "Email"=>"<EMAIL>", "Country"=>"US", "IsActive"=>true, "FirstName"=>"Security", "LastName"=>"User", "CompanyName"=>"GoGenuity", "Username"=>"<EMAIL>", "CreatedDate"=>"2019-08-21T07:08:21.000+0000", "UserType"=>"Standard", "ContactId"=>nil, "AccountId"=>nil, "CallCenterId"=>nil, "LastLoginDate"=>nil},
                    {"attributes"=>{"type"=>"User", "url"=>"/services/data/v42.0/sobjects/User/0053i0000028seTAAQ"}, "Id"=>"0053i0000028seTAAQ", "Name"=>"Chatter Expert", "Email"=>"<EMAIL>", "Country"=>"US", "IsActive"=>true, "FirstName"=>nil, "LastName"=>"Chatter Expert", "CompanyName"=>"GoGenuity", "Username"=>"<EMAIL>", "CreatedDate"=>"2019-08-21T07:08:21.000+0000", "UserType"=>"CsnOnly", "ContactId"=>nil, "AccountId"=>nil, "CallCenterId"=>nil, "LastLoginDate"=>nil}
                  ]
                },
                code: 200,
                success?: true,
                message: "OK",
                header: "OK"
              })
      allow(HTTParty).to receive(:get).and_return(users)
      expect(@salesforce_service.get_users).to eq(users)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: "success", api_type: "get_users", class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#get_apps' do
    it 'Fetch All apps responds with 200'  do
      apps = OpenStruct.new({parsed_response:
                  {"apps"=>[
                    {"appId"=>"06m3i0000016tXnAAI", "description"=>"Manage customer service with accounts, contacts, cases, and more", "developerName"=>"Service", "eTag"=>"75a987cdf4750589c9f4369ec792c344", "formFactors"=>["Large"], "headerColor"=>"#0070D2", "iconUrl"=>"https://na112.salesforce.com/logos/Salesforce/ServiceCloud/icon.png", "isNavAutoTempTabsDisabled"=>false, "isNavPersonalizationDisabled"=>false, "label"=>"Service", "logoUrl"=>"https://na112.salesforce.com/logos/Salesforce/ServiceCloud/logo.png", "mobileStartUrl"=>nil, "navItems"=>[], "selected"=>false, "startUrl"=>"https://na112.salesforce.com/home/<USER>", "uiType"=>"Classic", "userNavItems"=>[]},
                    {"appId"=>"06m3i0000016tXoAAI", "description"=>"Best-in-class on-demand marketing automation", "developerName"=>"Marketing", "eTag"=>"2e37eac7e032c89412531a509b922c07", "formFactors"=>["Large"], "headerColor"=>"#0070D2", "iconUrl"=>"https://na112.salesforce.com/logos/Salesforce/MarketingCloud/icon.png", "isNavAutoTempTabsDisabled"=>false, "isNavPersonalizationDisabled"=>false, "label"=>"Marketing", "logoUrl"=>"https://na112.salesforce.com/logos/Salesforce/MarketingCloud/logo.png", "mobileStartUrl"=>nil, "navItems"=>[], "selected"=>false, "startUrl"=>"https://na112.salesforce.com/home/<USER>", "uiType"=>"Classic", "userNavItems"=>[]},
                    {"appId"=>"06m3i0000016tXtAAI", "description"=>"App Launcher tabs", "developerName"=>"AppLauncher", "eTag"=>"223d9e277d0ce4af75f47565326f8086", "formFactors"=>["Large"], "headerColor"=>"#0070D2", "iconUrl"=>"https://na112.salesforce.com/logos/Salesforce/AppLauncher/icon.png", "isNavAutoTempTabsDisabled"=>false, "isNavPersonalizationDisabled"=>false, "label"=>"App Launcher", "logoUrl"=>"https://na112.salesforce.com/logos/Salesforce/AppLauncher/logo.png", "mobileStartUrl"=>nil, "navItems"=>[], "selected"=>false, "startUrl"=>"https://na112.salesforce.com/app/mgmt/applauncher/appLauncher.apexp?tsid=02u3i0000016tXt", "uiType"=>"Classic", "userNavItems"=>[]},
                    {"appId"=>"06m3i0000016tXuAAI", "description"=>"Salesforce CRM Communities", "developerName"=>"Community", "eTag"=>"06a106a0de002074ed4d9aa6a210ac5a", "formFactors"=>["Large"], "headerColor"=>"#0070D2", "iconUrl"=>"https://na112.salesforce.com/logos/Salesforce/Community/icon.png", "isNavAutoTempTabsDisabled"=>false, "isNavPersonalizationDisabled"=>false, "label"=>"Community", "logoUrl"=>"https://na112.salesforce.com/logos/Salesforce/Community/logo.png", "mobileStartUrl"=>nil, "navItems"=>[], "selected"=>false, "startUrl"=>"https://na112.salesforce.com/home/<USER>", "uiType"=>"Classic", "userNavItems"=>[]}
                  ]
                },
                code: 200,
                success?: true,
                message: "OK",
                header: "OK"
              })
      allow(HTTParty).to receive(:get).and_return(apps)
      expect(@salesforce_service.get_apps).to eq(apps)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: "success", api_type: "get_apps", class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#get_licenses' do
    it 'Fetch All licenses responds with 200'  do
      licenses = OpenStruct.new({parsed_response:
                  {"totalSize"=>27,
                    "done"=>true,
                    "records"=>[
                      {"attributes"=>{"type"=>"UserLicense", "url"=>"/services/data/v42.0/sobjects/UserLicense/1003i0000014pF2AAI"}, "Id"=>"1003i0000014pF2AAI", "Name"=>"Salesforce", "TotalLicenses"=>2, "UsedLicenses"=>1, "Status"=>"Active"},
                      {"attributes"=>{"type"=>"UserLicense", "url"=>"/services/data/v42.0/sobjects/UserLicense/1003i0000014pF3AAI"}, "Id"=>"1003i0000014pF3AAI", "Name"=>"Cloud Integration User", "TotalLicenses"=>1, "UsedLicenses"=>0, "Status"=>"Active"},
                      {"attributes"=>{"type"=>"UserLicense", "url"=>"/services/data/v42.0/sobjects/UserLicense/1003i0000014pF4AAI"}, "Id"=>"1003i0000014pF4AAI", "Name"=>"Salesforce Platform", "TotalLicenses"=>3, "UsedLicenses"=>0, "Status"=>"Active"},
                      {"attributes"=>{"type"=>"UserLicense", "url"=>"/services/data/v42.0/sobjects/UserLicense/1003i0000014pF5AAI"}, "Id"=>"1003i0000014pF5AAI", "Name"=>"Customer Community Login", "TotalLicenses"=>5, "UsedLicenses"=>0, "Status"=>"Active"}
                    ]
                  },
                  code: 200,
                  success?: true,
                  message: "OK",
                  header: "OK"
                })
      allow(HTTParty).to receive(:get).and_return(licenses)
      expect(@salesforce_service.get_licenses).to eq(licenses)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: "success", api_type: "get_licenses", class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#get_license_users' do
    it 'Fetch All licenses and their users'  do
      license_user = OpenStruct.new({parsed_response:
                  { "totalSize"=>8,
                    "done"=>true,
                    "records"=>[
                      {"attributes"=>{"type"=>"User", "url"=>"/services/data/v42.0/sobjects/User/0054T0000017WSlQAM"}, "Id"=>"0054T0000017WSlQAM", "Name"=>"Automated Process", "Profile"=>nil},
                      {"attributes"=>{"type"=>"User", "url"=>"/services/data/v42.0/sobjects/User/0054T0000017WSXQA2"}, "Id"=>"0054T0000017WSXQA2", "Name"=>"Gogenuity Support", "Profile"=>{"attributes"=>{"type"=>"Profile", "url"=>"/services/data/v42.0/sobjects/Profile/00e4T0000015PjAQAU"}, "Name"=>"System Administrator", "UserLicense"=>{"attributes"=>{"type"=>"UserLicense", "url"=>"/services/data/v42.0/sobjects/UserLicense/1004T000000oGtfQAE"}, "Name"=>"Salesforce", "Id"=>"1004T000000oGtfQAE"}}},
                      {"attributes"=>{"type"=>"User", "url"=>"/services/data/v42.0/sobjects/User/0054T0000017WSkQAM"}, "Id"=>"0054T0000017WSkQAM", "Name"=>"Integration User", "Profile"=>{"attributes"=>{"type"=>"Profile", "url"=>"/services/data/v42.0/sobjects/Profile/00e4T0000015PjCQAU"}, "Name"=>"Analytics Cloud Integration User", "UserLicense"=>{"attributes"=>{"type"=>"UserLicense", "url"=>"/services/data/v42.0/sobjects/UserLicense/1004T000000oGu5QAE"}, "Name"=>"Analytics Cloud Integration User", "Id"=>"1004T000000oGu5QAE"}}},
                      {"attributes"=>{"type"=>"User", "url"=>"/services/data/v42.0/sobjects/User/0054T0000017WSoQAM"}, "Id"=>"0054T0000017WSoQAM", "Name"=>"Security User", "Profile"=>{"attributes"=>{"type"=>"Profile", "url"=>"/services/data/v42.0/sobjects/Profile/00e4T0000015PjDQAU"}, "Name"=>"Analytics Cloud Security User", "UserLicense"=>{"attributes"=>{"type"=>"UserLicense", "url"=>"/services/data/v42.0/sobjects/UserLicense/1004T000000oGu5QAE"}, "Name"=>"Analytics Cloud Integration User", "Id"=>"1004T000000oGu5QAE"}}}
                    ]
                  },
                  code: 200,
                  success?: true,
                  message: "OK",
                  header: "OK"
                })
      allow(HTTParty).to receive(:get).and_return(license_user)
      expect(@salesforce_service.get_license_users).to eq(license_user)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: "success", api_type: "get_license_users", class_name: described_class.name).count).to eq(1)
    end
  end
end
