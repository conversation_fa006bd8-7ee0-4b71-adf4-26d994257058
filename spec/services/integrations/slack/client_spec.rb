require 'rails_helper'
include CompanyUserHelper

describe Integrations::Slack::Client do
  create_company_and_user

  let(:config) { create(:slack_config, company: company, workspace: workspace) }
  let(:config2) { create(:slack_config, company: company, workspace: workspace) }

  describe '#client' do
    it 'will return client' do
      allow_any_instance_of(Slack::Web::Client).to receive(:auth_test).and_return(true)
      response = described_class.new(config)
      expect(response.client).to be_present
    end

    it 'will not return client' do
      response = described_class.new(config2)
      expect(response.client).to eq(nil)
    end
  end
end
