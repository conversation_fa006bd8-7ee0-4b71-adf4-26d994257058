require 'rails_helper'
require 'ostruct'
include CompanyUserHelper

describe Integrations::Azure::FetchData do
  before(:all) do
    @company =  FactoryBot.create(:company)
    Integrations::Azure::Config.create(
      token: "access_token",
      expires_in: 2321,
      refresh_token: "refresh_token",
      company_id: @company.id)
    @config ||= Company.find(@company.id).azure_config
    @azure_service = described_class.new(@company.id)
  end

  describe '#client' do
    it 'Fetch client responds with 200'  do
      client = Signet::OAuth2::Client.new(
          authorization_uri: "https://login.microsoftonline.com/common/oauth2/authorize?resource=https://management.core.windows.net/",
          client_id: "mock-client-id",
          response_type: "code",
          response_mode: "query",
          scope: "offline_access Directory.AccessAsUser.All Directory.Read.All Directory.ReadWrite.All email openid profile Reports.Read.All User.Read User.ReadBasic.All User.ReadWrite User.ReadWrite.All",
          redirect_uri: "http://localhost:3000/integrations/azure/oauth2callback",
          state: @company.id)
      allow_any_instance_of(described_class).to receive(:token).and_return(client)
      expect(@azure_service.client).to be_present
    end
  end

  describe '#token' do
    it 'Fetch token responds with 200'  do
      token_detail = {"token_type":"Bearer",
        "scope":"Directory.AccessAsUser.All Directory.Read.All Directory.ReadWrite.All email openid profile Reports.Read.All User.Read User.ReadBasic.All User.ReadWrite User.ReadWrite.All",
        "expires_in":3600,
        "ext_expires_in":3600,
        "access_token":"mock-access-token",
        "refresh_token":"OAQABAAAAAAAP0wLlqdLVToOpA4kwzS-hx6nwnkGU78H2kTuAEsMDouHIXIEwdWZ5FFiEbogAA",
        "id_token":"mock-id-token"}
      allow_any_instance_of(described_class).to receive(:token_call).and_return(token_detail)
      expect(@azure_service.token "3456ghj-hjkyu7jk-jhgf4").to eq(token_detail)
      expect(Logs::ApiEvent.where(company_id: @company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "get token").count).to eq(1)
    end
  end

  describe '#refresh_token' do
    it 'Fetch refresh_token responds with 200'  do
      token_detail = {"token_type":"Bearer",
        "scope":"Directory.AccessAsUser.All Directory.Read.All Directory.ReadWrite.All email openid profile Reports.Read.All User.Read User.ReadBasic.All User.ReadWrite User.ReadWrite.All",
        "expires_in":3600,
        "ext_expires_in":3600,
        "access_token":"mock-access-token",
        "refresh_token":"OAQABAAAAAAAP0wLlqdLVToOpAJ6HQnQiM9bweL7lT4uAEsMDouHIXIEwdWZ5FFiEbogAA",
        "id_token":"mock-id-token"}
      allow_any_instance_of(described_class).to receive(:refresh_token_call).and_return(token_detail)
      expect(@azure_service.refresh_token).to eq(token_detail)
      expect(Logs::ApiEvent.where(company_id: @company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "get refresh token").count).to eq(1)
    end
  end
  
  describe '#get_subscriptions' do
    it 'Fetch All Subscriptions responds with 200'  do
      get_subscription = OpenStruct.new({parsed_response:
                            {"value"=>
                              [{"id"=>"h1ukXt3AlUapQ-CtMS6fFjKiGmk9deJNrQHEEUA_Pv0",
                                "SubscriptionName"=>"Pay As You Go"
                              }]
                            }
                          })
      allow_any_instance_of(described_class).to receive(:get_subscriptions_call).and_return(get_subscription)
      expect(@azure_service.get_subscriptions).to eq(get_subscription)
      expect(Logs::ApiEvent.where(company_id: @company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "get subscriptions").count).to eq(1)
    end
  end

  describe '#get_subscription_usage' do
    it 'Fetch All Subscription usage responds with 200'  do
      get_subscription_usage = OpenStruct.new({parsed_response:
              {"value"=>
                [{"id"=>"h1ukXt3AlUapQ-CtMS6fFjKiGmk9deJNrQHEEUA",
                  "properties"=>
                    {
                      "consumedService"=>"power.shell",
                      "usageStart"=>"2017-05-23T14:01:38.7786461Z",
                      "usageEnd"=>"2017-05-23T14:01:38.7786461Z",
                      "usageQuantity"=>"0.012USD"
                    }
                }]
              }
            })
      allow_any_instance_of(described_class).to receive(:get_subscription_usage_call).and_return(get_subscription_usage)
      response, next_url = @azure_service.get_subscription_usage("35385-hn3h-5i654b654-546n54", nil)
      expect(response).to eq(get_subscription_usage.parsed_response['value'])
      expect(Logs::ApiEvent.where(company_id: @company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "get subscription usage").count).to eq(1)
    end
  end
end
