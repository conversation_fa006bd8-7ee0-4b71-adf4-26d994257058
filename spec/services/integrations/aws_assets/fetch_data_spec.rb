require "rails_helper"
require "ostruct"
include CompanyUserHelper

RSpec.describe Integrations::AwsAssets::FetchData do
  before(:all) do
    @company = FactoryBot.create(:company)
    allow_any_instance_of(Integrations::Aws::FetchData).to receive(:authenticate?).and_return([true, "success"])
    @aws_config = FactoryBot.create(:aws_assets_config, :skip_validate, company_id: @company.id)
    @aws_assets_service = described_class.new(@aws_config)
  end

  describe "API Client" do
    it "will fetch response with success status" do
      api_client = Aws::EC2::Client.new
      allow_any_instance_of(Integrations::Aws::FetchData).to receive(:api_client).and_return(api_client)
      expect(@aws_assets_service.api_client(@aws_config.regions[0])).to be_present
    end
  end

  describe "#" do
    it "Fetch devices from specified region success status" do
      instance_data = {
        :instance_id => "i-01b5e082e4b83bfe9",
        :name => "build-service-staging",
        :type => "t2.small", :manufacturer => "Amazon EC2",
        :architecture => "x86_64",
        :core_count => 1, :harddisk => "30GB",
        :hypervisor => "xen",
        :launch_time => "2021-06-15 09:47:38 UTC",
        :mac_address => "02:6e:7c:b4:59:30",
        :mac_addresses => ["02:6E:7C:B4:59:30"],
        :platform_description => nil,
        :platform_details => nil,
        :public_dns_name => "ec2-54-218-250-57.us-west-2.compute.amazonaws.com",
        :public_ip_address => "*************",
        :root_device_name => "/dev/sda1",
        :state => "running",
        :vpc_id => "vpc-dbde42b3",
        :threads_per_core => 1,
        :virtualization_type => "hvm",
        :memory => "2GB",
        :availability_zone => "us-west-2b",
        :region => "US West (Oregon)",
        :security_group => "SSH and HTTP In",
        :tags => { :Name => "build-service-staging" },
      }
      virtual_machines = OpenStruct.new({ parsed_response: { "value" => [instance_data] } })
      allow_any_instance_of(described_class).to receive(:region_specific_devices).and_return(virtual_machines)
      expect(@aws_assets_service.region_specific_devices("us-east-2")).to eq(virtual_machines)
    end
  end
end
