require 'rails_helper'
describe Integrations::GsuiteAd::FetchData do
  let(:company) { FactoryBot.create(:company) }
  let(:client) do
    described_class.new(company.id, "write")
  end
  before(:each) do
    user_1 = OpenStruct.new({
              primary_email: "<EMAIL>",
              name:  OpenStruct.new({fullname: "user1"}),
              creation_time: "Wed, 14 Mar 2018 03:41:50 +0000",
              customer_id: "C014vjuq6",
              id: "10078456892758223370445",
              is_admin: false,
              last_login_time: "Wed, 26 Jun 2019 09:18:15 +0000",
            })
    user_2 = OpenStruct.new({
              primary_email: "<EMAIL>",
              name: OpenStruct.new({fullname: "user2"}),
              creation_time: "Wed, 14 Mar 2018 03:41:50 +0000",
              customer_id: "C014vjuq6",
              id: "100784568927582370445",
              is_admin: false,
              last_login_time: "Wed, 26 Jun 2019 09:18:15 +0000",
            })
    @users = OpenStruct.new(users: [user_1, user_2])

    @credentials = OpenStruct.new({
                    client_id: *********,
                    client_secret: "3453849",
                    access_token: "mock-access-token",
                    scope: "nfekh5834",
                    refresh_token: "fkjfbbfkjsbfsjkbfsjkfb3784y32b2j35b"
                  })
  end
  describe '#get_users' do
    it 'Get All Users '  do
      allow_any_instance_of(Google::Apis::AdminDirectoryV1::DirectoryService).to receive(:list_users).and_return(@users)
      expect(client.get_users.users.count).to eq(2)
      expect(Logs::ApiEvent.where(company_id: company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "get users").count).to eq(1)
    end
  end

  describe '#directory_client' do
    it 'Client object'  do
      dir_client = OpenStruct.new({
        root_url: "https://www.googleapis.com/",
        base_path: "admin/directory/v1/",
        upload_path: "upload/admin/directory/v1/",
        batch_path: "batch/admin/directory_v1",
        client_options: "Genuity",
        application_version: "0.0.0",
        project_id: nil,
        authorization_uri: "https://accounts.google.com/o/oauth2/auth",
        token_credential_uri: "https://oauth2.googleapis.com/token",
        client_id: Rails.application.credentials.gsuite[:client_id],
        client_secret: Rails.application.credentials.gsuite[:client_secret],
        code: nil,
        expires_at: "2019-07-10 20:42:10 +0500",
        issued_at: nil,
        scope: ["https://www.googleapis.com/auth/admin.directory.user.readonly"],
        state: nil,
        username: nil,
        access_type: "offline",
        expiry:60,
        access_token: "mock-access-token",
        })
      allow_any_instance_of(Integrations::GsuiteAd::FetchData).to receive(:directory_client).and_return(dir_client)
      expect(client.directory_client).to be_present
    end
  end
  describe '#get_authorizer' do
    it 'Get Authorizer'  do
      authorizer =OpenStruct.new({
                    id: Rails.application.credentials.gsuite[:client_id],
                    secret: Rails.application.credentials.gsuite[:client_secret],
                    scope: ["https://www.googleapis.com/auth/admin.reports.usage.readonly", "https://www.googleapis.com/auth/admin.directory.user.readonly", "https://www.googleapis.com/auth/admin.directory.user.security"],
                    token_store: "mock_token_store",
                    store: "8943u3498h934h6n934n",
                    filename: "token.yaml",
                    abort: false,
                    u0ltra_safe: false,
                    thread_safe: false,
                    callback_uri: "/oauth2callback",
                    })
      allow_any_instance_of(Google::Auth::UserAuthorizer).to receive(:get_credentials).and_return(authorizer)
      expect(client.get_authorizer(company.id, "write"))
    end
  end
  describe '#get_refresh_token' do
    it 'Get Refresh Token'  do
      ref_token = {
                    access_token: "mock-access-token",
                    expires_in: 3600,
                    scope: "https://www.googleapis.com/auth/admin.directory.user.readonly https://www.googleapis.com/auth/admin.directory.user.security https://www.googleapis.com/auth/apps.licensing https://www.googleapis.com/auth/admin.reports.usage.readonly",
                    token_type: "Bearer"
                  }
      allow_any_instance_of(described_class).to receive(:refresh_token_call).and_return(ref_token)
      expect(client.refresh_token).to eq(ref_token)
      expect(Logs::ApiEvent.where(company_id: company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "refresh token").count).to eq(1)
    end
  end
end
