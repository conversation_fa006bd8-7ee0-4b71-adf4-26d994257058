require 'rails_helper'
require 'ostruct'
include CompanyUserHelper

describe Integrations::Aws::FetchData do
  before(:all) do
    @company =  FactoryBot.create(:company)
    allow_any_instance_of(Integrations::Aws::FetchData).to receive(:authenticate?).and_return([true, "success"])
    @aws_config = FactoryBot.create(:aws_config, company_id: @company.id)
    @aws_service = described_class.new(@aws_config)
  end

  describe '#api_client' do
    it 'Fetch api_client responds with 200'  do
      api_client = Aws::CostExplorer::Client.new()
      allow_any_instance_of(Integrations::Aws::FetchData).to receive(:api_client).and_return(api_client)
      expect(@aws_service.api_client).to be_present
    end
  end

  describe '#get_transactions' do
    it 'Fetch transactions responds with 200'  do
      api_client = Aws::CostExplorer::Client.new()
      metrics = OpenStruct.new(amount:"0.006301", unit:"USD")
      transaction  = OpenStruct.new(keys:["Amazon Elastic Load Balancing"], metrics:{"AmortizedCost"=>metrics})
      metrics1 = OpenStruct.new(amount:"221.232", unit:"USD")
      transaction1  = OpenStruct.new(keys:["Amazon Relational Database Service"], metrics:{"AmortizedCost"=>metrics1})
      groups = [transaction, transaction1]
      time_period = OpenStruct.new(start:"2019-10-21", end:"2019-10-22")
      results_by_time = OpenStruct.new(time_period: time_period, groups: groups)
      service_transactions = OpenStruct.new(results_by_time: [results_by_time])
      allow_any_instance_of(Integrations::Aws::FetchData).to receive(:api_client).and_return(api_client)
      allow_any_instance_of(Aws::CostExplorer::Client).to receive(:get_cost_and_usage).and_return(service_transactions)
      expect(@aws_service.get_transactions "2019-08-24", "2019-10-24", "SERVICE").to eq(service_transactions)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: "success", api_type: "get_transactions", class_name: described_class.name).count).to eq(1)
    end
  end
end
