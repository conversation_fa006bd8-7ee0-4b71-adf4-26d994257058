require 'rails_helper'
require 'ostruct'
require 'httparty'
include CompanyUserHelper

describe Integrations::Kaseya::FetchData do
  before(:all) do
    @company = FactoryBot.create(:company)
    @kaseya_config = FactoryBot.create(:kaseya_config, company: @company)
    @kaseya_service = described_class.new(@company.id)
  end

  describe '#client' do
    it 'Fetch client responds with 200'  do
      client = Signet::OAuth2::Client.new(authorization_uri: 'https://na1vsa13.kaseya.net/vsapres/web20/core/login.aspx',
                                          client_id: '216366875412113',
                                          response_type: 'code',
                                          response_mode: 'query',
                                          redirect_uri: 'http://localhost:3000/integrations/kaseya/oauth2callback',
                                          state: @company.id)
      allow_any_instance_of(described_class).to receive(:client).and_return(client)
      expect(@kaseya_service.client(@kaseya_config.client_id , @kaseya_config.integrator_username, @kaseya_config.client_secret)).to be_present
    end
  end

  describe '#token' do
    it 'Fetch token responds with 200'  do
      token_detail =  OpenStruct.new({ parsed_response:
                                        { 'token_type':'Bearer',
                                          'expires_in':3600,
                                          'access_token':'mock_access_token',
                                          'refresh_token':'OAQABAAAAAAAP0whx6nwnkGU78H2kT1N0uPKky6sk9d7J6HQnQiM9bweL7lT4uAEsMDouHIXIEwdWZ5FFiEbogAA',
                                        },
                                        code: 200,
                                        success?: true,
                                        message: 'OK',
                                        header: 'OK',
                                        request: {},
                                      })
      allow(HTTParty).to receive(:post).and_return(token_detail)
      expect(@kaseya_service.retrieve_kaseya_access_token('3456ghj-hjkyu7jk-jhgf4',  @kaseya_config.client_id, @kaseya_config.client_secret, nil, nil)).to eq(token_detail.parsed_response)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: 'success', api_type: 'token', class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#refresh_token' do
    it 'Fetch refresh_token responds with 200'  do
       token_detail = OpenStruct.new({ parsed_response:
                                        {
                                          'token_type':'Bearer',
                                          'expires_in':3600,
                                          'access_token':'eyJ0eXAiOiJKV',
                                          'refresh_token':'mock_refresh_token',
                                        },
                                        code: 200,
                                        success?: true,
                                        message: 'OK',
                                        header: 'OK',
                                        request: {},
                                      })
      allow(HTTParty).to receive(:post).and_return(token_detail)
      expect(@kaseya_service.refresh_token).to eq(token_detail[:parsed_response])
      expect(Logs::ApiEvent.where(company_id: @company.id, status: 'success', api_type: 'refresh_token', class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#get_kaseya_assets' do
    it 'Fetch All devices and responds with 200'  do
      devices = OpenStruct.new({ parsed_response:
                                  {'Results'=>
                                    [{
                                      'displayName': 'HP78E7D1B3EFB7',
                                      'manufacturer': 'Microsoft',
                                      'ip_address': "***************",
                                      'os_name': 'Microsoft Windows',
                                      'asset_type': "Laptop",
                                      'mac_addresses': "78:E7:D1:B3:EF:B7",
                                      'kaseya_network_id': 390599317006114
                                    }]
                                  },
                                  code: 200,
                                  success?: true,
                                  message: 'OK',
                                  header: 'OK'
                                })
      allow(HTTParty).to receive(:get).and_return(devices)
      expect(@kaseya_service.get_kaseya_assets(10, 0)).to eq(devices)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: 'success', api_type: 'get_kaseya_assets', class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#get_kaseya_asset_types' do
    it 'Fetch All asset types and responds with 200'  do
      asset_types = OpenStruct.new({  parsed_response: {
                                        'Results' => [{
                                          'AssetTypeId': '1002',
                                          'AssetTypeName': 'Computer'
                                        }]
                                      },
                                      code: 200,
                                      success?: true,
                                      message: 'OK',
                                      header: 'OK'
                                    })
      allow(HTTParty).to receive(:get).and_return(asset_types)
      expect(@kaseya_service.get_kaseya_asset_types).to eq(asset_types)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: 'success', api_type: 'get_kaseya_asset_types', class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#get_kaseya_agents' do
    it 'Fetch All kaseya agents and responds with 200'  do
      agents_data = OpenStruct.new({  parsed_response: {
                                        'Results' => [{
                                          "DisplayName": "Hp.techs.workstations",
                                          'MacAddress': "74-46-A0-C0-38-5C",
                                          "IpAddress": "***************",
                                          "ProcessorFamily": "Intel(r) Core(tm) i5 processor",
                                          "CpuCount": '8',
                                          "RamMBytes": '7889',
                                          "FreeSpace": 126887,
                                          'OSType': '10',
                                          'OSInfo': 'Professional x64 Edition  Build 17134'
                                        }]
                                      },
                                      code: 200,
                                      success?: true,
                                      message: 'OK',
                                      header: 'OK'
                                    })
      allow(HTTParty).to receive(:get).and_return(agents_data)
      expect(@kaseya_service.get_kaseya_agents(10, 0)).to eq(agents_data)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: 'success', api_type: 'get_kaseya_agents', class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#get_kaseya_agent' do
    it 'Fetch All kaseya agents and responds with 200'  do
      agent_data = OpenStruct.new({ parsed_response: {
                                      'Results' => [{
                                        "DisplayName": "Hp.techs.workstations",
                                        'MacAddress': "74-46-A0-C0-38-5C",
                                        "IpAddress": "***************",
                                        "ProcessorFamily": "Intel(r) Core(tm) i5 processor",
                                        "CpuCount": '8',
                                        "RamMBytes": '7889',
                                        "FreeSpace": 126887,
                                        'OSType': '10',
                                        'OSInfo': 'Professional x64 Edition  Build 17134',
                                        'BIOS': { 'BiosVersion': '1.2.3' }
                                      }]
                                    },
                                    code: 200,
                                    success?: true,
                                    message: 'OK',
                                    header: 'OK'
                                  })
      allow(HTTParty).to receive(:get).and_return(agent_data)
      expect(@kaseya_service.get_kaseya_agent(1)).to eq(agent_data)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: 'success', api_type: 'get_kaseya_agent', class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#get_installed_applications' do
    it 'Fetch All kaseya agents and responds with 200'  do
      installed_applications = OpenStruct.new({ parsed_response: {
                                                  'Results' => [{
                                                    "ApplicationName": "-fb6pr51.exe",
                                                    "Manufacturer": "Microsoft Corporation",
                                                    "ProductName": "Microsoft SQL Server",
                                                    "DirectoryPath": "C:\\Program Files (x86)\\MICROSOFT SQL SERVER\\120\\Setup Bootstrap\\Update Cache\\KB4505218\\GDR\\1033_enu_lp\\x86\\setup\\sqlsupport_msi\\pfiles\\sqlservr\\120\\setup\\ev9nqowo\\x86",
                                                    "Version": "12.0.6108.1",
                                                  }]
                                                },
                                                code: 200,
                                                success?: true,
                                                message: 'OK',
                                                header: 'OK'
                                              })
      allow(HTTParty).to receive(:get).and_return(installed_applications)
      expect(@kaseya_service.get_installed_applications(1, 1, '')).to eq(installed_applications)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: 'success', api_type: 'get_installed_applications', class_name: described_class.name).count).to eq(1)
    end
  end
end
