require 'rails_helper'
require 'ostruct'
require 'sidekiq/testing'
include CompanyUserHelper

describe Integrations::Netsuite::FetchData do
  create_company_and_user

  let(:company) { create(:company) }

  let(:config) do
    FactoryBot.create(:netsuite_config, company_id: company.id)
  end

  let(:client) do
    described_class.new(config)
  end

  let(:bills) do {
      "vendors" => [
        {"NAME" => "John", "EntityId" => "accountant"},
        {"NAME" => "Billi", "EntityId" => "unkown"}
      ],
      "vendorBills" => [
        {"Id" => "123458", "TranDate" => "2020-01-08", "Total" => "500", "EntityRef" => "{\"name\": \"Test VEndor\"}"},
        {"Id" => "987642", "TranDate" => "2020-01-01", "Total" => "600", "EntityRef" => "{\"name\": \"Task\"}"},
        {"Id" => "985832", "TranDate" => "2020-01-02", "Total" => "900", "EntityRef" => "{\"name\": \"Task\"}"}
      ]
    }
  end

  let(:event) do {
      api_type: "random",
      class_name: described_class,
      company_id:  @company_id,
      status: 1,
      response: @bills
    }
  end

  describe '#all_payments' do
    it 'Fetch Netsuite Intacct Payments' do
      allow_any_instance_of(described_class).to receive(:get_data_from_s3).and_return(bills)
      expect(client.get_data_from_s3).to eq(bills)
    end
  end

  describe 'Event Log' do
    before do
      allow_any_instance_of(RestClient::Request).to receive(:execute).and_return(double(code: 200))
    end

    it 'Create Event' do
      response = 'http://s3.amazonaws.com/hotglue-logs/test/subdomain-netsuite-1638347103.json'
      allow_any_instance_of(Integrations::Netsuite::FetchData).to receive(:get_data_from_s3).and_return(bills)
      
      allow_any_instance_of(Integrations::Netsuite::SyncDataWorker).to receive(:uploaded_file_path).and_return(response)
      Sidekiq::Testing.inline! do
        FactoryBot.create(:netsuite_config, company_id: company.id)
      end
      expect(Logs::ApiEvent.where(company_id:company.id, 
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "perform_etl").count).to eq(2)
    end
  end
end
