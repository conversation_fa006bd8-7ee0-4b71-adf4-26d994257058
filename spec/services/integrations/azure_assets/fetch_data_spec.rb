require 'rails_helper'
require 'ostruct'
include CompanyUserHelper

describe Integrations::AzureAssets::FetchData do
  before(:all) do
    @company =  FactoryBot.create(:company)
    Integrations::AzureAssets::Config.create(token: "access_token",
                                             expires_in: 2321,
                                             refresh_token: "refresh_token",
                                             company_id: @company.id)
    @config ||= Company.find(@company.id).azure_assets_config
    @azure_assets_service = described_class.new(@company.id)
    @machine = {:name=>"Testing",
                 :id=>"/subscriptions/5cbccab6-30c8-44ba-901e-32d216bf72a1/resourceGroups/TESTING_VMS/providers/Microsoft.Compute/virtualMachines/Testing",
                 :type=>"Microsoft.Compute/virtualMachines",
                 :location=>"northcentralus",
                 :identity=>{:type=>"SystemAssigned", :principalId=>"def17fd6-b474-44ad-81ef-dbced744c9a1", :tenantId=>"51f6420c-47a1-4701-8bf9-e5b71795f17a"},
                 :properties=>
                  {:vmId=>"a93f3673-df57-4786-9f9c-78c81f2ea227",
                   :hardwareProfile=>{:vmSize=>"Standard_DS1_v2"},
                   :storageProfile=>
                    {:imageReference=>{:publisher=>"MicrosoftWindowsServer", :offer=>"WindowsServer", :sku=>"2019-Datacenter", :version=>"latest", :exactVersion=>"17763.1999.**********"},
                     :osDisk=>
                      {:osType=>"Windows",
                       :name=>"Testing_OsDisk_1_b522a3e378364ddea2c5c48fb6faae24",
                       :createOption=>"FromImage",
                       :caching=>"ReadWrite",
                       :managedDisk=>
                        {:storageAccountType=>"StandardSSD_LRS",
                         :id=>"/subscriptions/5cbccab6-30c8-44ba-901e-32d216bf72a1/resourceGroups/Testing_VMs/providers/Microsoft.Compute/disks/Testing_OsDisk_1_b522a3e378364ddea2c5c48fb6faae24"},
                       :diskSizeGB=>127},
                     :dataDisks=>[]},
                   :osProfile=>
                    {:computerName=>"Testing",
                     :adminUsername=>"genuityadmin",
                     :windowsConfiguration=>{:provisionVMAgent=>true, :enableAutomaticUpdates=>true, :patchSettings=>{:patchMode=>"AutomaticByOS", :assessmentMode=>"ImageDefault", :enableHotpatching=>false}},
                     :secrets=>[],
                     :allowExtensionOperations=>true,
                     :requireGuestProvisionSignal=>true},
                   :networkProfile=>{:networkInterfaces=>[{:id=>"/subscriptions/5cbccab6-30c8-44ba-901e-32d216bf72a1/resourceGroups/Testing_VMs/providers/Microsoft.Network/networkInterfaces/Test_NIC_1"}]},
                   :diagnosticsProfile=>{:bootDiagnostics=>{:enabled=>true}},
                   :licenseType=>"Windows_Server",
                   :provisioningState=>"Succeeded"}
                }
  end

  describe '#client' do
    it 'Fetch client responds with 200' do
      expect(@azure_assets_service.client).to be_present
    end
  end

  describe '#token' do
    it 'Fetch token responds with 200' do
      token_detail = {"token_type": "Bearer",
                      "expires_in": 3600,
                      "ext_expires_in": 3600,
                      "access_token": "mock-access-token",
                      "refresh_token": "OAQABAAAAAAAP0wLlqdLVToOpA4kwzS-hx6nwnkGU78H2kTuAEsMDouHIXIEwdWZ5FFiEbogAA",
                      "id_token": "mock-id-token"}

      allow_any_instance_of(described_class).to receive(:token_call).and_return(token_detail)
      expect(@azure_assets_service.token "3456ghj-hjkyu7jk-jhgf4").to eq(token_detail)
      expect(Logs::ApiEvent.where(company_id: @company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "get_token").count).to eq(1)
    end
  end

  describe '#refresh_token' do
    it 'Fetch refresh_token responds with 200' do
      token_detail = {"token_type": "Bearer",
                      "expires_in": 3600,
                      "ext_expires_in": 3600,
                      "access_token": "mock-access-token",
                      "refresh_token": "OAQABAAAAAAAP0wLlqdLVToOpAJ6HQnQiM9bweL7lT4uAEsMDouHIXIEwdWZ5FFiEbogAA",
                      "id_token": "mock-id-token"}

      allow_any_instance_of(described_class).to receive(:refresh_token_call).and_return(token_detail)
      expect(@azure_assets_service.refresh_token).to eq(token_detail)
      expect(Logs::ApiEvent.where(company_id: @company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "get_refresh_token").count).to eq(1)
    end
  end
  
  describe '#get_subscriptions' do
    it 'Fetch All Subscriptions responds with 200' do
      get_subscription = OpenStruct.new({parsed_response:
                            {"value"=>
                              [{"id"=>"h1ukXt3AlUapQ-CtMS6fFjKiGmk9deJNrQHEEUA_Pv0",
                                "SubscriptionName"=>"Pay As You Go"
                              }]
                            }
                          })
      allow_any_instance_of(described_class).to receive(:get_subscriptions_call).and_return(get_subscription)
      expect(@azure_assets_service.get_subscriptions).to eq(get_subscription.parsed_response['value'])
      expect(@azure_assets_service.get_subscriptions.count).to eq(1)
      expect(Logs::ApiEvent.where(company_id: @company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "get_subscriptions").count).to eq(2)
    end
  end

  describe '#get_virtual_machines' do
    it 'Fetch All Virtual machines responds with 200' do
      virtual_machines = OpenStruct.new({ parsed_response: { "value"=>[@machine] }})
      allow_any_instance_of(described_class).to receive(:get_virtual_machines_call).and_return(virtual_machines)
      expect(@azure_assets_service.get_virtual_machines "35385-hn3h-5i654b654-546n54").to eq(virtual_machines.parsed_response['value'])
      expect(Logs::ApiEvent.where(company_id: @company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "get_virtual_machines").count).to eq(1)
    end
  end

  describe '#get_network_info' do
    it 'Fetch All network interfaces responds with 200' do
      net_interface = {:properties=>
                        {:ipConfigurations=>
                          [{:properties=>
                             {:privateIPAddress=>"**********",
                              :privateIPAllocationMethod=>"Dynamic",
                              :publicIPAddress=>{:id=>"/subscriptions/5cbccab6-30c8-44ba-901e-32d216bf72a1/resourceGroups/Testing_VMs/providers/Microsoft.Network/publicIPAddresses/Testing-ip"},
                              :privateIPAddressVersion=>"IPv4"}}],
                         :macAddress=>"00-22-48-8D-50-8E",
                         :virtualMachine=>{:id=>"/subscriptions/5cbccab6-30c8-44ba-901e-32d216bf72a1/resourceGroups/Testing_VMs/providers/Microsoft.Compute/virtualMachines/Testing"}
                       }}

      public_ip = {:properties=>{:ipAddress=>"***********", :publicIPAddressVersion=>"IPv4", :publicIPAllocationMethod=>"Dynamic", :idleTimeoutInMinutes=>4}}
      response = [{:properties=>
                    {:ipConfigurations=>
                      [{:properties=>
                        {:privateIPAddress=>"**********",
                          :privateIPAllocationMethod=>"Dynamic",
                          :publicIPAddress=>{
                            :id=>"/subscriptions/5cbccab6-30c8-44ba-901e-32d216bf72a1/resourceGroups/Testing_VMs/providers/Microsoft.Network/publicIPAddresses/Testing-ip"
                          },
                          :privateIPAddressVersion=>"IPv4"
                        },
                        :publicIPAddress=>{
                          :properties=>{
                            :ipAddress=>"***********",
                            :publicIPAddressVersion=>"IPv4",
                            :publicIPAllocationMethod=>"Dynamic",
                            :idleTimeoutInMinutes=>4
                          }
                        }}],
                      :macAddress=>"00-22-48-8D-50-8E",
                      :virtualMachine=>{
                        :id=>"/subscriptions/5cbccab6-30c8-44ba-901e-32d216bf72a1/resourceGroups/Testing_VMs/providers/Microsoft.Compute/virtualMachines/Testing"
                      }
                    }
                  }]

      allow_any_instance_of(described_class).to receive(:get_network_interface).and_return(net_interface)
      allow_any_instance_of(described_class).to receive(:get_public_ip).and_return(public_ip)
      expect(@azure_assets_service.get_network_info @machine).to eq(response)
    end
  end

  describe '#find_vm_size' do
    it 'Fetch All Virtual machine sizes responds with 200 and finds given one' do
      vm_sizes = [{ :name=>"Standard_B1ls",
                    :numberOfCores=>1,
                    :osDiskSizeInMB=>1047552,
                    :resourceDiskSizeInMB=>4096,
                    :memoryInMB=>512,
                    :maxDataDiskCount=>2
                  },
                  { :name=>"Standard_DS1_v2",
                    :numberOfCores=>1,
                    :osDiskSizeInMB=>1047552,
                    :resourceDiskSizeInMB=>7168,
                    :memoryInMB=>3584,
                    :maxDataDiskCount=>4
                  }]

      allow_any_instance_of(described_class).to receive(:get_vm_sizes).and_return(vm_sizes)
      expect(@azure_assets_service.find_vm_size @machine).to eq(vm_sizes[1])
    end
  end

  describe '#find_vm_location' do
    it 'Fetch All locations responds with 200 and finds given one' do
      vm_locations = [{ :id=>"/subscriptions/5cbccab6-30c8-44ba-901e-32d216bf72a1/locations/eastus2",
                        :name=>"eastus2",
                        :displayName=>"East US 2",
                        :regionalDisplayName=>"(US) East US 2",
                      },
                      { :id=>"/subscriptions/5cbccab6-30c8-44ba-901e-32d216bf72a1/locations/northcentralus",
                        :name=>"northcentralus",
                        :displayName=>"North Central US",
                        :regionalDisplayName=>"(US) North Central US"
                      }]

      allow_any_instance_of(described_class).to receive(:get_vm_locations).and_return(vm_locations)
      expect(@azure_assets_service.find_vm_location @machine).to eq(vm_locations[1])
    end
  end

  describe '#get_disk_info' do
    it 'Fetch image disk info responds with 200' do
      disk_info = OpenStruct.new({parsed_response: {"properties": {
                                                    "osType": "Windows",
                                                    "hyperVGeneration": "V1",
                                                    "diskSizeGB": 127,
                                                    "diskIOPSReadWrite": 500,
                                                    "diskMBpsReadWrite": 60,
                                                    "timeCreated": "2021-06-14T17:05:16.5892918+00:00",
                                                    "provisioningState": "Succeeded",
                                                    "diskSizeBytes": 136367308800}}
                                })
      response = {:osType=>"Windows",
                  :hyperVGeneration=>"V1",
                  :diskSizeGB=>127,
                  :diskIOPSReadWrite=>500,
                  :diskMBpsReadWrite=>60,
                  :timeCreated=>"2021-06-14T17:05:16.5892918+00:00",
                  :provisioningState=>"Succeeded",
                  :diskSizeBytes=>136367308800}

      allow_any_instance_of(described_class).to receive(:make_api_call).and_return(disk_info)
      expect(@azure_assets_service.get_disk_info @machine).to eq(response)
      expect(Logs::ApiEvent.where(company_id: @company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "get_disk_info").count).to eq(1)
    end
  end

  describe '#get_instance_view' do
    it 'Fetch instance view responds with 200' do
      inst_view = OpenStruct.new({parsed_response: {"computerName": "Testing",
                                                    "osName": "Windows Server 2019 Datacenter",
                                                    "osVersion": "Microsoft Windows NT 10.0.17763.0",
                                                    "statuses": [
                                                        {
                                                        "code": "ProvisioningState/succeeded",
                                                        "level": "Info",
                                                        "displayStatus": "Provisioning succeeded",
                                                        "time": "2021-06-14T17:07:26.4436837+00:00"
                                                        },
                                                        {
                                                          "code": "PowerState/running",
                                                          "level": "Info",
                                                          "displayStatus": "VM running"
                                                        }
                                                    ]}})
      response = {:computerName=>"Testing",
                   :osName=>"Windows Server 2019 Datacenter",
                   :osVersion=>"Microsoft Windows NT 10.0.17763.0",
                   :statuses=>
                    [{:code=>"ProvisioningState/succeeded", :level=>"Info", :displayStatus=>"Provisioning succeeded", :time=>"2021-06-14T17:07:26.4436837+00:00"},
                     {:code=>"PowerState/running", :level=>"Info", :displayStatus=>"VM running"}]
                  }

      allow_any_instance_of(described_class).to receive(:make_api_call).and_return(inst_view)
      expect(@azure_assets_service.get_instance_view @machine).to eq(response)
      expect(Logs::ApiEvent.where(company_id: @company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "get_instance_view").count).to eq(1)
    end
  end
end
