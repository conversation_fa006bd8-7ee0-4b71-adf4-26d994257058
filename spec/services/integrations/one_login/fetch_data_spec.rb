require 'rails_helper'
require 'ostruct'
include CompanyUserHelper

describe Integrations::OneLogin::FetchData do
  let(:company) { FactoryBot.create(:company) }
  let(:onelogin_config) { FactoryBot.create(:one_login_config, company_id: company.id) }
  let(:service) { described_class.new(onelogin_config) }

  describe 'Authenticate' do
    it "is authenticate" do
      allow_any_instance_of(OneLogin::Api::Client).to receive(:access_token).and_return("token/-sjfbds-=hdbfiewkjbge")
      expect(service.authenticate?).to be_present
      expect(Logs::ApiEvent.where(company_id: company.id, status: "success", api_type: "authenticate?", class_name: described_class.name).count).to eq(2)
    end
    it "not authenticate" do
      comp = FactoryBot.create(:company)
      config = FactoryBot.create(:one_login_config, company_id: comp.id)
      serv = described_class.new(config)
      allow_any_instance_of(OneLogin::Api::Client).to receive(:access_token).and_return(nil)
      serv.authenticate?
      expect(Logs::ApiEvent.where(company_id:comp.id, status: "error", api_type: "authenticate?", class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#users' do
    it 'Fetch All Users responds with 200'  do
      user_1 = OpenStruct.new({
                company_id: 159,
                email: "<EMAIL>",
                firstname: "user",
                lastname: "first",
                openid_name: 'openidNmae',
                state: 45,
                status: 324
            })
      user_2 = OpenStruct.new({
                company_id: 159,
                email: "<EMAIL>",
                firstname: "user2",
                lastname: "second",
                openid_name: 'openidNmae2',
                state: 452,
                status: 3242
            })
      users = [user_1, user_2]
      allow_any_instance_of(OneLogin::Api::Client).to receive(:access_token).and_return("token/-sjfbds-=hdbfiewkjbge")
      allow_any_instance_of(OneLogin::Api::Client).to receive(:get_users).and_return(users)
      expect(service.get_users).to be_present
      expect(Logs::ApiEvent.where(company_id: company.id, status: "success", api_type: "get_users", class_name: described_class.name).count).to eq(1)
    end
  end
  describe '#apps' do
    it 'Fetch All apps responds with 200'  do
     app_1 = OpenStruct.new({
              connectorId: 101,
              extension: true,
              icon: "https://s3.amazonaws.com/onelogin-assets/images/icons/twitter.png",
              id: 941502,
              name: "Twitter",
              provisioning: false,
              visible: true,
          })
      app_2 = OpenStruct.new({
              connectorId: 101,
              extension: true,
              icon: "https://s3.amazonaws.com/onelogin-assets/images/icons/twitter.png",
              id: 941512,
              name: "Twitter",
              provisioning: false,
              visible: true,
          })
      apps = [app_1, app_2]
      allow_any_instance_of(OneLogin::Api::Client).to receive(:access_token).and_return("token/-sjfbds-=hdbfiewkjbge")
      allow_any_instance_of(OneLogin::Api::Client).to receive(:get_apps).and_return(apps)
      expect(service.get_apps).to be_present
      expect(Logs::ApiEvent.where(company_id: company.id, status: "success", api_type: "get_apps", class_name: described_class.name).count).to eq(1)
    end
  end
  describe '#user_apps' do
    it 'Fetch All User apps responds with 200'  do
     app_1 = OpenStruct.new({
            connectorId: 101,
            extension: true,
            icon: "https://s3.amazonaws.com/onelogin-assets/images/icons/twitter.png",
            id: 941502,
            name: "Twitter",
            provisioning: false,
            visible: true,
          })
      app_2 = OpenStruct.new({
            connectorId: 101,
            extension: true,
            icon: "https://s3.amazonaws.com/onelogin-assets/images/icons/twitter.png",
            id: 941512,
            name: "Twitter",
            provisioning: false,
            visible: true,
          })
      apps = [app_1, app_2]
      allow_any_instance_of(OneLogin::Api::Client).to receive(:access_token).and_return("token/-sjfbds-=hdbfiewkjbge")
      allow_any_instance_of(OneLogin::Api::Client).to receive(:get_user_apps).and_return(apps)
      expect(service.get_user_apps(1)).to be_present
      expect(Logs::ApiEvent.where(company_id: company.id, status: "success", api_type: "get_user_apps", class_name: described_class.name).count).to eq(1)
    end
  end
end
