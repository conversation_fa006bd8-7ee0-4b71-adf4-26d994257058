require 'rails_helper'
require 'ostruct'
include CompanyUserHelper

describe Integrations::Quickbooks::FetchData do
  before(:all) do
    @company =  FactoryBot.create(:company)
    @config = FactoryBot.create(:quickbooks_config, company_id: @company.id)
    @quickbook_service = described_class.new(@config)
  end

  describe '#client' do
    it 'Fetch client responds with 200'  do
      client ||= Signet::OAuth2::Client.new(
      authorization_uri: "https://appcenter.intuit.com/connect/oauth2",
      token_credential_uri:  "https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer",
      client_id: Rails.application.credentials.quickbooks[:client_id],
      client_secret: Rails.application.credentials.quickbooks[:client_secret],
      scope: "com.intuit.quickbooks.accounting",
      redirect_uri: "#{Rails.application.credentials.domain_with_port}/integrations/quickbooks/oauth_callback")
      allow_any_instance_of(described_class).to receive(:token).and_return(client)
      expect(@quickbook_service.client).to be_present
    end
  end

  describe '#token' do
    it 'Fetch token  responds with 200'  do
      token_detail = OpenStruct.new(
                      {parsed_response:
                        {
                          "refresh_token"=>"mock-refresh-token",
                          "access_token"=>"eyJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwiYWxnIjoiZGlyIn0..5NeRGFrmydwCvhj2u6zxRA.e_HYMbOMdln8Vs1j4MpWA_94luWJ5J0oLvPBeyqKK3DO3pcxnfdvvkDHAEpaO5SCvQfuPOEzzk_D_zGr-hQ2L8A1ePhHoZ0KIiUuMyADnFVuSfxFbEd_e3PSN1m2PhRatwdYIWwDOxYr7_hNYtGN6vcQ9BnCAaYof9iCAcviZvQvkM0zQPTittyADUrU2Q05dB9RPtAjjClvvLITpirTB1g7l8t2QJlHzmb9bAjYMIi-lI-w5lyx039v5P0D6BA9NG7L9pzY1iOq3TXiJb8PEjB8MKpQpSnejJ7iiLnl6lkU332byNKCw4esyXuCpxXqfqpxc3NPBuud0h7hbJHyMML1dRZEiGbT07mKlhB3NZuSiM2yr8HT5fhh2myxkNrGc6zsj-2ROkvZM7AwDXdiAZ5R2necLvkLhLIuJ8uvalSQib1WCPTZgUr8KcI9r1PuHFqbKKKk_MShlwNxc2tM-6SPflndeL5zmjBYIRRtwyG6rj4SusfNA6z9_xmB4_fVx5RQIXWFAE2hKoRrtyoGHg590tIwHzCr9GqO6P2ZLww88lbA8GXhoigg5LrXigpdoIGSwHdplViDMXWOay7Jn-_LqOV4e0DWYiKZ3joNf1pOAvMQX_-quNkOA1lSouS0djEVFzkpcgMDxuqmsatshCFHJ6jQXxoU1PWNBOVe90ZzFm4H200DKM7SrgUX79X5.0y4MVCzk_DDA-kq_xGGd1Q",
                          "x_refresh_token_expires_in"=>8726400,
                          "token_type"=>"bearer",
                          "expires_in"=>3600
                        },
                        code: 200,
                        success?: true,
                        message: "OK",
                        header: "OK",
                        body: {
                          "refresh_token"=>"mock-refresh-token",
                          "access_token"=>"eyJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwiYWxnIjoiZGlyIn0..5NeRGFrmydwCvhj2u6zxRA.e_HYMbOMdln8Vs1j4MpWA_94luWJ5J0oLvPBeyqKK3DO3pcxnfdvvkDHAEpaO5SCvQfuPOEzzk_D_zGr-hQ2L8A1ePhHoZ0KIiUuMyADnFVuSfxFbEd_e3PSN1m2PhRatwdYIWwDOxYr7_hNYtGN6vcQ9BnCAaYof9iCAcviZvQvkM0zQPTittyADUrU2Q05dB9RPtAjjClvvLITpirTB1g7l8t2QJlHzmb9bAjYMIi-lI-w5lyx039v5P0D6BA9NG7L9pzY1iOq3TXiJb8PEjB8MKpQpSnejJ7iiLnl6lkU332byNKCw4esyXuCpxXqfqpxc3NPBuud0h7hbJHyMML1dRZEiGbT07mKlhB3NZuSiM2yr8HT5fhh2myxkNrGc6zsj-2ROkvZM7AwDXdiAZ5R2necLvkLhLIuJ8uvalSQib1WCPTZgUr8KcI9r1PuHFqbKKKk_MShlwNxc2tM-6SPflndeL5zmjBYIRRtwyG6rj4SusfNA6z9_xmB4_fVx5RQIXWFAE2hKoRrtyoGHg590tIwHzCr9GqO6P2ZLww88lbA8GXhoigg5LrXigpdoIGSwHdplViDMXWOay7Jn-_LqOV4e0DWYiKZ3joNf1pOAvMQX_-quNkOA1lSouS0djEVFzkpcgMDxuqmsatshCFHJ6jQXxoU1PWNBOVe90ZzFm4H200DKM7SrgUX79X5.0y4MVCzk_DDA-kq_xGGd1Q",
                          "x_refresh_token_expires_in"=>8726400,
                          "token_type"=>"bearer",
                          "expires_in"=>3600
                        },
                        request: {},
                      })
      allow(HTTParty).to receive(:post).and_return(token_detail)
      expect(@quickbook_service.token "3456ghj-hjkyu7jk-jhgf4").to eq(token_detail.parsed_response)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: "success", api_type: "token", class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#refresh_token' do
    it 'Fetch refresh_token responds with 200'  do
       token_detail = OpenStruct.new(
                        {parsed_response:
                          {
                            "access_token"=>"00D3i000000tXr3!AQcAQIKWnGXuxsT.1k0ki0K1iqljE.e0Hv5xB9dgYmU8bhM4Qu6kuMBZwrZW2baBxaVjshEBunOLIbhWZ_ywsa.D3WXnzHew",
                            "scope"=>"com.intuit.quickbooks.accounting",
                            "refresh_token"=>"mock_refresh_token",
                            "token_type"=>"Bearer",
                            "x_refresh_token_expires_in"=>8726400,
                            "expires_in"=>3600,
                            "issued_at"=>"*************"},
                          code: 200,
                          success?: true,
                          message: "OK",
                          header: "OK",
                          body: {
                            "access_token"=>"00D3i000000tXr3!AQcAQIKWnGXuxsT.1k0ki0K1iqljE.e0Hv5xB9dgYmU8bhM4Qu6kuMBZwrZW2baBxaVjshEBunOLIbhWZ_ywsa.D3WXnzHew",
                            "scope"=>"com.intuit.quickbooks.accounting",
                            "refresh_token"=>"mock_refresh_token",
                            "token_type"=>"Bearer",
                            "x_refresh_token_expires_in"=>8726400,
                            "expires_in"=>3600,
                            "issued_at"=>"*************"},
                          request: {},
                        })
      allow(HTTParty).to receive(:post).and_return(token_detail)
      expect(@quickbook_service.refresh_token).to eq(token_detail.parsed_response)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: "success", api_type: "refresh_token", class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#purchases' do
    it 'Fetch All purchases responds with 200'  do
      purchases = [
                    {"Id"=>"***********", "TxnDate"=>"2019-07-24 13:15:23 +0500", "TotalAmt"=>"123", "PaymentType"=>"CreditCard", "EntityRef"=>{"name"=>"Vendor Name"}, "MetaData"=> {"CreateTime"=>"2019-07-24 13:15:23 +0500", "LastUpdatedTime"=>"2019-07-24 13:15:23 +0500"}},
                    {"Id"=>"24327787112", "TxnDate"=>"2019-07-24 13:15:23 +0500", "TotalAmt"=>"13", "PaymentType"=>"CreditCard", "EntityRef"=>{"name"=>"Vendor Name"}, "MetaData"=> {"CreateTime"=>"2019-07-24 13:15:23 +0500", "LastUpdatedTime"=>"2019-07-24 13:15:23 +0500"}},
                    {"Id"=>"24327787512", "TxnDate"=>"2019-07-24 13:15:23 +0500", "TotalAmt"=>"12", "PaymentType"=>"CreditCard", "EntityRef"=>{"name"=>"Vendor Name"}, "MetaData"=> {"CreateTime"=>"2019-07-24 13:15:23 +0500", "LastUpdatedTime"=>"2019-07-24 13:15:23 +0500"}}
                  ]
      allow_any_instance_of(QboApi).to receive(:query).and_return(purchases)
      expect(@quickbook_service.purchases "2019-07-24").to eq(purchases)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: "success", api_type: "purchases", class_name: described_class.name).count).to eq(1)
    end
  end
end
