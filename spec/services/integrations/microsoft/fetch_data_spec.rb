require 'rails_helper'
require 'ostruct'
require 'httparty'
include CompanyUserHelper

describe Integrations::Microsoft::FetchData do
  before(:all) do
    @company =  FactoryBot.create(:company)
    FactoryBot.create(:microsoft_config, company_id: @company.id)
    @config ||= Company.find(@company.id).microsoft_config
    @microsoft_service = described_class.new(@company.id)
  end

  describe '#client' do
    it 'Fetch client responds with 200'  do
      client = Signet::OAuth2::Client.new(
          authorization_uri: "https://login.microsoftonline.com/common/oauth2/v2.0/authorize",
          client_id: "mock-client-id",
          response_type: "code",
          response_mode: "query",
          scope: "offline_access Directory.AccessAsUser.All Directory.Read.All Directory.ReadWrite.All email openid profile Reports.Read.All User.Read User.ReadBasic.All User.ReadWrite User.ReadWrite.All",
          redirect_uri: "http://localhost:3000/integrations/microsoft/oauth2callback",
          state: @company.id)
      allow_any_instance_of(described_class).to receive(:token).and_return(client)
      expect(@microsoft_service.client).to be_present
    end
  end

  describe '#token' do
    it 'Fetch token  responds with 200'  do
      token_detail = OpenStruct.new(
                      {parsed_response:
                        { "token_type":"Bearer",
                          "scope":"Directory.AccessAsUser.All Directory.Read.All Directory.ReadWrite.All email openid profile Reports.Read.All User.Read User.ReadBasic.All User.ReadWrite User.ReadWrite.All",
                          "expires_in":3600,
                          "ext_expires_in":3600,
                          "access_token":"mock_access_token",
                          "refresh_token":"OAQABAAAAAAAP0whx6nwnkGU78H2kT1N0uPKky6sk9d7J6HQnQiM9bweL7lT4uAEsMDouHIXIEwdWZ5FFiEbogAA",
                          "id_token":"mock_id_token"
                        },
                        code: 200,
                        success?: true,
                        message: "OK",
                        header: "OK",
                        body: { "token_type":"Bearer",
                          "scope":"Directory.AccessAsUser.All Directory.Read.All Directory.ReadWrite.All email openid profile Reports.Read.All User.Read User.ReadBasic.All User.ReadWrite User.ReadWrite.All",
                          "expires_in":3600,
                          "ext_expires_in":3600,
                          "access_token":"mock-access-token",
                          "refresh_token":"mock-refresh-token",
                          "id_token":"mock-id-token"
                        },
                        request: {},
                      })
      allow(HTTParty).to receive(:post).and_return(token_detail)
      expect(@microsoft_service.token "3456ghj-hjkyu7jk-jhgf4").to eq(token_detail.parsed_response)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: "success", api_type: "token", class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#refresh_token' do
    it 'Fetch refresh_token responds with 200'  do
       token_detail = OpenStruct.new(
                        {parsed_response:
                           {"token_type":"Bearer",
                            "scope":"Directory.AccessAsUser.All Directory.Read.All Directory.ReadWrite.All email openid profile Reports.Read.All User.Read User.ReadBasic.All User.ReadWrite User.ReadWrite.All",
                            "expires_in":3600,
                            "ext_expires_in":3600,
                            "access_token":"eyJ0eXAiOiJKV",
                            "refresh_token":"mock_refresh_token",
                            "id_token":"mock_id_token"},
                          code: 200,
                          success?: true,
                          message: "OK",
                          header: "OK",
                          body: {"token_type":"Bearer",
                            "scope":"Directory.AccessAsUser.All Directory.Read.All Directory.ReadWrite.All email openid profile Reports.Read.All User.Read User.ReadBasic.All User.ReadWrite User.ReadWrite.All",
                            "expires_in":3600,
                            "ext_expires_in":3600,
                            "access_token":"mock_access_token",
                            "refresh_token":"mock_refresh_token",
                            "id_token":"mock_id_token"},
                          request: {},
                        })
      allow(HTTParty).to receive(:post).and_return(token_detail)
      expect(@microsoft_service.refresh_token).to eq(token_detail.parsed_response)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: "success", api_type: "refresh_token", class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#users' do
    it 'Fetch All Users responds with 200'  do
        users = OpenStruct.new({parsed_response:
                  {"value"=>
                    [{
                      "businessPhones"=>[],
                      "displayName"=>"Robert Lasch",
                      "givenName"=>"Robert Lasch",
                      "jobTitle"=>nil,
                      "mail"=>nil,
                      "mobilePhone"=>nil,
                      "officeLocation"=>nil,
                      "preferredLanguage"=>"en-GB",
                      "surname"=>nil,
                      "userPrincipalName"=>"<EMAIL>",
                      "id"=>"ee331c9d-eaae-4fff-a0d4-12b2a769d88f"
                    }]
                  },
                  code: 200,
                  success?: true,
                  message: "OK",
                  header: "OK"
                })
      allow(HTTParty).to receive(:get).and_return(users)
      expect(@microsoft_service.get_users).to eq(users)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: "success", api_type: "get_users", class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#apps' do
    it 'Fetch All apps responds with 200'  do
      apps = OpenStruct.new({parsed_response:
                  {"value"=>
                    [{"id"=>"6e79fc05-c916-4d20-9fff-3bdc6d848f6b",
                      "deletedDateTime"=>nil,
                      "accountEnabled"=>true,
                      "appDisplayName"=>"Slack",
                      "appId"=>"da2ef37b-cf36-4f1b-85f1-be9710083459",
                      "applicationTemplateId"=>nil,
                      "appOwnerOrganizationId"=>"9188040d-6c67-4c5b-b112-36a304b66dad",
                      "appRoleAssignmentRequired"=>false,
                      "displayName"=>"Slack",
                      "errorUrl"=>nil,
                      "homepage"=>nil,
                      "info"=>{"termsOfServiceUrl"=>"https://slack.com/terms-of-service",
                      "supportUrl"=>nil,
                      "privacyStatementUrl"=>"https://slack.com/privacy-policy",
                      "marketingUrl"=>nil,
                      "logoUrl"=>"https://secure.aadcdn.microsoftonline-p.com/dbd5a2dd-otrskziqqxxsssdghj7jjfrifoprytjmtmfdfhco0ss/appbranding/iztviocwtqcwf5rlh9mo47fj8qex4su0vbfyvkhthoe/1033/bannerlogo?ts=636271037506621951"},
                      "logoutUrl"=>nil,
                      "notificationEmailAddresses"=>[],
                      "publishedPermissionScopes"=>[],
                      "preferredSingleSignOnMode"=>nil,
                      "preferredTokenSigningKeyEndDateTime"=>nil,
                      "preferredTokenSigningKeyThumbprint"=>nil,
                      "publisherName"=>"Microsoft Accounts",
                      "replyUrls"=>["https://oauth2.slack.com/services/auth/microsoft"],
                      "samlMetadataUrl"=>nil,
                      "samlSingleSignOnSettings"=>nil,
                      "servicePrincipalNames"=>["da2ef37b-cf36-4f1b-85f1-be9710083459"],
                      "signInAudience"=>"AzureADandPersonalMicrosoftAccount",
                      "tags"=>["WindowsAzureActiveDirectoryIntegratedApp"],
                      "addIns"=>[],
                      "appRoles"=>[],
                      "keyCredentials"=>[],
                      "passwordCredentials"=>[]
                    }]
                  },
                  code: 200,
                  success?: true,
                  message: "OK",
                  header: "OK"
                })

      allow(HTTParty).to receive(:get).and_return(apps)
      expect(@microsoft_service.get_apps).to eq(apps)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: "success", api_type: "get_apps", class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#get_app_users' do
    it 'Fetch All App users responds with 200'  do
      get_app_users = OpenStruct.new({parsed_response:
                  {"value"=>
                    [{"id"=>"h1ukXt3AlUapQ-CtMS6fFjKiGmk9deJNrQHEEUA_Pv0",
                      "creationTimestamp"=>"2017-05-23T14:01:38.7786461Z",
                      "appRoleId"=>"00000000-0000-0000-0000-000000000000",
                      "principalDisplayName"=>"Robert Lasch",
                      "principalId"=>"5ea45b87-c0dd-4695-a943-e0ad312e9f16",
                      "principalType"=>"User",
                      "resourceDisplayName"=>"Slack",
                      "resourceId"=>"6e79fc05-c916-4d20-9fff-3bdc6d848f6b"
                    }]
                  },
                  code: 200,
                  success?: true,
                  message: "OK",
                  header: "OK"
                })
      allow(HTTParty).to receive(:get).and_return(get_app_users)
      expect(@microsoft_service.get_app_users"35385-hn3h-5i654b654-546n54").to eq(get_app_users)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: "success", api_type: "get_app_users", class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#get_app_usage' do
    it 'Fetch All App users responds with 200'  do
      usage = OpenStruct.new({parsed_response:
                  {"value"=>
                    [{
                      "createdDateTime"=>Time.now,
                      "userDisplayName"=>"Robert Lasch",
                      "userPrincipalName"=>"<EMAIL>",
                      "id"=>"ee331c9d-eaae-4fff-a0d4-12b2a769d88f",
                      "userId"=>"ee331c9d-eaae-4fff-a0d4-12b2a769d88f",
                      "appId"=>"570ca2ca-0b19-41f3-aad6-ac8359155082",
                      "appDisplayName"=>"Slack",
                      "ipAddress"=>"***************",
                      "clientAppUsed"=>"Browser",
                      "correlationId"=>"d2086930-b121-4b97-a96b-09c1fb2b9a24",
                      "conditionalAccessStatus"=>nil,
                      "originalRequestId"=>"98584db7-9128-406f-8e55-50b71d643e00",
                      "isInteractive"=>true,
                      "tokenIssuerName"=>""
                    }]
                  },
                  code: 200,
                  success?: true,
                  message: "OK",
                  header: "OK"
                })
      allow(HTTParty).to receive(:get).and_return(usage)
      expect(@microsoft_service.get_app_usage "35385-hn3h-5i654b654-546n54").to eq(usage)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: "success", api_type: "get_app_usage", class_name: described_class.name).count).to eq(1)
    end
  end
end
