require 'rails_helper'
require 'ostruct'
require 'httparty'
include CompanyUserHelper

describe Integrations::AzureAdAssets::FetchData do
  before(:all) do
    @company = FactoryBot.create(:company)
    Integrations::AzureAdAssets::Config.create(
      token: 'access_token',
      expires_in: 2321,
      refresh_token: 'refresh_token',
      company_id: @company.id)

    @config ||= Company.find_by_id(@company.id)&.azure_ad_assets_config
    @azure_ad_assets_service = described_class.new(@company.id)
  end

  describe '#client' do
    it 'Fetch client responds with 200'  do
      client = Signet::OAuth2::Client.new(
        authorization_uri: 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',
        client_id: 'mock-client-id',
        response_type: 'code',
        response_mode: 'query',
        scope: 'offline_access Device.Read.All',
        redirect_uri: 'http://localhost:3000/integrations/azure_ad_assets/oauth2callback',
        state: @company.id)
      allow_any_instance_of(described_class).to receive(:token).and_return(client)
      expect(@azure_ad_assets_service.client).to be_present
    end
  end

  describe '#token' do
    it 'Fetch token responds with 200'  do
      token_detail = OpenStruct.new(
                      {parsed_response:
                        { 'token_type':'Bearer',
                           'scope': 'offline_access Device.Read.All',
                          'expires_in':3600,
                          'ext_expires_in':3600,
                          'access_token':'mock_access_token',
                          'refresh_token':'OAQABAAAAAAAP0whx6nwnkGU78H2kT1N0uPKky6sk9d7J6HQnQiM9bweL7lT4uAEsMDouHIXIEwdWZ5FFiEbogAA',
                          'id_token':'mock_id_token'
                        },
                        code: 200,
                        success?: true,
                        message: 'OK',
                        header: 'OK',
                        request: {},
                      })
      allow(HTTParty).to receive(:get).and_return(token_detail)
      expect(@azure_ad_assets_service.token '3456ghj-hjkyu7jk-jhgf4').to eq(token_detail.parsed_response)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: 'success', api_type: 'token', class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#refresh_token' do
    it 'Fetch refresh_token responds with 200'  do
       token_detail = OpenStruct.new(
                        {parsed_response:
                           {'token_type':'Bearer',
                            'scope': 'offline_access Device.Read.All',
                            'expires_in':3600,
                            'ext_expires_in':3600,
                            'access_token':'eyJ0eXAiOiJKV',
                            'refresh_token':'mock_refresh_token',
                            'id_token':'mock_id_token'},
                          code: 200,
                          success?: true,
                          message: 'OK',
                          header: 'OK',
                          request: {},
                        })
      allow(HTTParty).to receive(:get).and_return(token_detail)
      expect(@azure_ad_assets_service.refresh_token).to eq(token_detail.parsed_response)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: 'success', api_type: 'refresh_token', class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#devices' do
    it 'Fetch All devices and responds with 200'  do
        devices = OpenStruct.new({parsed_response:
                  {'value'=>
                    [{
                      'displayName'=>'User Genuity',
                      'manufacturer'=>'Apple',
                      'model'=>'MacBook Pro',
                      'operatingSystem'=>'MAC',
                      'operatingSystemVersion'=>'10.0.19042.631',
                      'deviceId'=>'b059c627-5885-491a-b855-07147d25e397'
                    }]
                  },
                  code: 200,
                  success?: true,
                  message: 'OK',
                  header: 'OK'
                })
      allow(HTTParty).to receive(:get).and_return(devices)
      expect(@azure_ad_assets_service.get_devices).to eq(devices)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: 'success', api_type: 'get_devices', class_name: described_class.name).count).to eq(1)
    end
  end
end
