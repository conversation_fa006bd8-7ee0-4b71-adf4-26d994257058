require 'rails_helper'
require 'ostruct'
require 'httparty'
include CompanyUserHelper

describe Integrations::JamfPro::FetchData do
  before(:all) do
    @company = FactoryBot.create(:company)
    config = FactoryBot.create(:jamf_pro_config, company: @company)
    @jamf_pro_service = described_class.new(@company.id, config)
  end

  describe '#token' do
    it 'Fetch token responds with 200'  do
      token_detail = OpenStruct.new({ parsed_response:
                      {
                        'token': SecureRandom.hex(32),
                        'expires_in': DateTime.now + 30.minutes,
                      },
                      code: 200,
                      success?: true,
                      message: 'OK',
                      header: 'OK',
                      request: {},
                    })
      jamf_pro_data = {
        'username'=> 'ryan',
        'password'=> '12345678',
        'instance_name'=> 'airland'
      }
      allow(HTTParty).to receive(:post).and_return(token_detail)
      expect(@jamf_pro_service.token(jamf_pro_data)).to eq(token_detail.parsed_response)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: 'success', api_type: 'token', class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#devices' do
    it 'Fetch All devices and responds with 200'  do
      devices = OpenStruct.new({ parsed_response:
                  {'results'=>
                    [{
                      'displayName'=>'User Genuity',
                      'manufacturer'=>'Apple',
                      'model'=>'MacBook Pro',
                      'machine_serial_no' => '0000-0003-6421-4278-0245-7654-53',
                      'os_name': 'Windows'
                    }]
                  },
                  code: 200,
                  success?: true,
                  message: 'OK',
                  header: 'OK'
                })
      allow(HTTParty).to receive(:get).and_return(devices)
      expect(@jamf_pro_service.get_computers_inventory(0)).to eq(devices)
      expect(Logs::ApiEvent.where(company_id: @company.id, status: 'success', api_type: 'get_computers_inventory', class_name: described_class.name).count).to eq(1)
    end
  end

  describe '#devices' do
    it 'Fails to fetch All devices and responds with 401'  do
      devices = OpenStruct.new({ parsed_response:
                  {'error'=>
                    {
                      "code": "InvalidAuthenticationToken",
                      "message": "Access token has expired or is not yet valid.",
                    }
                  },
                  response: OpenStruct.new({ message: 'Access token has expired or is not yet valid.' }),
                  code: 401,
                  success?: false,
                })
      allow(HTTParty).to receive(:get).and_return(devices)
      expect{ @jamf_pro_service.get_computers_inventory(0) }.to raise_error(RuntimeError, 'Access token has expired or is not yet valid.')
      expect(Logs::ApiEvent.where(company_id: @company.id, status: 'error', api_type: 'get_computers_inventory', class_name: described_class.name).count).to eq(1)
    end

    it 'Fails to fetch All devices and responds with 403'  do
      devices = OpenStruct.new({ parsed_response:
                  {'error' =>
                    {
                      'code': 'Unauthorize',
                      'message': 'Forbidden',
                    }
                  },
                  response: OpenStruct.new({ message: 'Forbidden' }),
                  code: 403,
                  success?: false,
                })
      allow(HTTParty).to receive(:get).and_return(devices)
      expect{ @jamf_pro_service.get_computers_inventory(0) }.to raise_error(RuntimeError, 'Forbidden')
      expect(Logs::ApiEvent.where(company_id: @company.id, status: 'error', api_type: 'get_computers_inventory', class_name: described_class.name).count).to eq(1)
    end
  end
end
