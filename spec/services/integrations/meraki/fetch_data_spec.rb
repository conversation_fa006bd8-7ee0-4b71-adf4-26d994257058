require 'rails_helper'
require 'ostruct'
require 'httparty'
describe Integrations::Meraki::FetchData do
  let(:company) { FactoryBot.create(:company) }
  let(:meraki_config) { FactoryBot.create(:meraki_config, company_id: company.id) }

  let(:meraki_params){
    OpenStruct.new(
      {
        token: 'mock-token',
        name: 'Stratum Networks',
        company_id: company.id
      }
    )
  }

  let(:service) do
    described_class.new(meraki_params)
  end

  describe '#authenticate?' do
    it 'Authenticate Meraki' do
      response = OpenStruct.new(
                  {parsed_response:
                    [
                      {'id' => '695243192475320747',
                        'name' => 'Stratum-Genuity',
                        'url' => 'https://n235.meraki.com/o/l0-zbdRd/manage/organization/overview'
                      },
                      {'id' => '622923',
                        'name' => 'Stratum Networks',
                        'url' => 'https://n235.meraki.com/o/KTMBqc/manage/organization/overview'
                      }
                    ],
                    code: 200,
                    success?: true,
                    message: 'OK',
                  }
                )
      allow(HTTParty).to receive(:get).and_return(response)
      expect(service.authenticate?).to eq(true)
    end
  end

  describe '#organizations' do
    it 'describes the organizations' do
      response = OpenStruct.new(
                  {parsed_response:
                    [
                      {'id' => '695243192475320747',
                        'name' => 'Stratum-Genuity',
                        'url' => 'https://n235.meraki.com/o/l0-zbdRd/manage/organization/overview'
                      },
                      {'id' => '622923',
                        'name' => 'Stratum Networks',
                        'url' => 'https://n235.meraki.com/o/KTMBqc/manage/organization/overview'
                      }
                    ],
                    code: 200,
                    success?: true,
                    message: 'OK',
                    header: 'OK'
                  }
                )
      allow(HTTParty).to receive(:get).and_return(response)
      expect(service.organizations).to eq(response.parsed_response)
    end
  end

  describe '#networks' do
    it 'describes the network of specific organization' do
      response = OpenStruct.new(
                  {parsed_response:
                    {'id' => 'N_664280945037197269',
                      'organizationId' => '622923',
                      'name' => 'Stratum 651 Washington',
                      'timeZone' => 'America/Chicago',
                      'tags' => nil,
                      'productTypes' => ['wireless'],
                      'type' => 'wireless',
                      'disableMyMerakiCom' => false,
                      'disableRemoteStatusPage' => true
                    },
                    code: 200,
                    success?: true,
                    message: 'OK',
                    header: 'OK'
                  }
                )
      allow(HTTParty).to receive(:get).and_return(response)
      expect(service.networks('622923')).to eq(response.parsed_response)
    end
  end

  describe '#network_clients' do
    it 'describes the clients of specific network' do
      response = OpenStruct.new(
                  {parsed_response:
                    [
                      {
                        'id' => 'k7ddf7c',
                        'mac' => '0c:d7:46:76:8d:e3',
                        'description' => 'fac3a9ec-267c-4b8c-a1e7-2a5423fe96cc1',
                        'ip' => '**************',
                        'ip6' => nil,
                        'user' => nil,
                        'firstSeen' => '2019-05-07T00:14:41Z',
                        'lastSeen' => '2020-01-07T07:31:14Z',
                        'manufacturer' => 'Apple',
                        'os' => 'Apple iPhone',
                        'recentDeviceSerial' => 'Q2PD-M8WH-BPU9',
                        ' ' => 'Stratum MR33',
                        'recentDeviceMac' => '0c:8d:db:17:3c:03',
                        'ssid' => 'Stratum-Guest',
                        'vlan' => 0,
                        'switchport' => nil,
                        'usage' => {'sent' => 59709, 'recv' => 834166},
                        'status' => 'Offline'
                      },
                      {
                        'id' => 'k8a4b66',
                        'mac' => 'd0:c6:37:f9:b3:1b',
                        'description' => 'DESKTOP-M4U5KRB',
                        'ip' => '*************',
                        'ip6' => nil,
                        'user' => nil,
                        'firstSeen' => '2019-06-21T15:20:36Z',
                        'lastSeen' => '2020-01-07T10:23:05Z',
                        'manufacturer' => 'Intel',
                        'os' => nil,
                        'recentDeviceSerial' => 'Q2PD-M8WH-BPU9',
                        'recentDeviceName' => 'Stratum MR33',
                        'recentDeviceMac' => '0c:8d:db:17:3c:03',
                        'ssid' => 'Stratum', 'vlan' => 0,
                        'switchport' => nil,
                        'usage' => {'sent' => 821, 'recv' => 1620},
                        'status' => 'Online'
                      }
                    ],
                    code: 200,
                    success?: true,
                    message: 'OK',
                    header: 'OK'
                  }
                )
      allow(HTTParty).to receive(:get).and_return(response)
      result = service.network_clients('k7ddf7c')
      expect(result.parsed_response).to eq(response.parsed_response)
    end
  end

  describe '#switch_device_usage' do
    it 'describes clients usage history for switch device' do
      api_response = OpenStruct.new({
        parsed_response: [
          {
            'clientId' => 'k0c52a5',
            'clientIp' => '*************',
            'clientMac' => '00:15:65:f4:e7:d2',
            'usageHistory' => [
              {
                'ts' => '2022-09-29T07:00:00.000000Z',
                'received' => 0,
                'sent' => 0
              },
              {
                'ts' => '2022-09-29T07:20:00.000000Z',
                'received' => 50,
                'sent' => 109
              },
              {
                'ts' => '2022-09-29T07:40:00.000000Z',
                'received' => 100,
                'sent' => 86
              },
              {
                'ts' => '2022-09-29T08:00:00.000000Z',
                'received' => 36,
                'sent' => 63
              },
              {
                'ts' => '2022-09-29T08:20:00.000000Z',
                'received' => 69,
                'sent' => 112
              },
              {
                'ts' => '2022-09-29T08:40:00.000000Z',
                'received' => 95,
                'sent' => 54
              }
            ]
          }
        ],
        code: 200,
        success?: true,
        message: 'OK',
        header: 'OK'
      })

      expected_response = {
        filtered_response: {
          :graph_labels => ['07:00', '08:00'],
          :graph_data => [0.0007666728, 0.00095334096]
        },
        status: :ok
      }

      allow(HTTParty).to receive(:get).and_return(api_response)
      result = service.switch_client_usage_history('N_695243192475336835', ['k0c52a5'], '2022-09-23T04:24:07.137Z', '86400', '%H:%M')
      expect(result).to eq(expected_response)
    end

    it 'describes clients, who does not allow Track by Ip Option' do
      api_response = OpenStruct.new({
        parsed_response: {
          'errors' => [
            'One or more clients were not found'
          ]
        },
        status: :bad_request,
        success?: false,
      })
      allow(HTTParty).to receive(:get).and_return(api_response)
      result = service.switch_client_usage_history('N_695243192475336835', ['k0c52a5','k0c52a3','k0c52a1'], '2022-09-23T04:24:07.137Z', '86400', '%H:%M')
      expect(result).to eq(api_response)
    end
  end

  describe '#AP_device_latency_history' do
    it 'describes AP device usage history' do
      api_response = OpenStruct.new({
        parsed_response: {
            'serial' => 'Q2KD-56T2-NYPX',
            'latencyStats' =>  {
              'backgroundTraffic' =>  {
                'rawDistribution' =>  {
                  '0' => 27716,
                  '1' => 48388,
                  '2' => 28901,
                  '4' => 12435,
                  '8' => 5193,
                  '16' => 2071,
                  '32' => 778,
                  '64' => 828,
                  '128' => 148,
                  '256' => 32,
                  '512' => 49,
                  '1024' => 18,
                  '2048' => 14
                },
                  'avg' => 1.74
              }
            }
          },
        code: 200,
        success?: true,
        message: 'OK',
        header: 'OK'
      })

      expected_response = {
        filtered_response: {
          :latency_data => [0.2062083731660491, 99.13961333954855, 0.6541782872853972],
          :latency_labels => ['latency more than 64 ms', 'latency less than 64 ms', 'latency equals to 64 ms'],
        },
        status: :ok
      }

      allow(HTTParty).to receive(:get).and_return(api_response)
      result = service.device_latency_history('k7ddf7c', nil)
      expect(result).to eq(expected_response)
    end

    it 'describes response when device is not found' do
      api_response = OpenStruct.new({
        parsed_response: {
          'errors' => [
            "No device with serial 'Q2KD-56T2-NYPS'"
          ]
        },
        status: :bad_request,
        success?: false,
      })
      allow(HTTParty).to receive(:get).and_return(api_response)
      result = service.device_latency_history('Q2KD-56T2-NYPS', nil)
      expect(result).to eq(api_response)
    end
  end

  describe '#wireless_ap_usage_history' do
    it 'describes device usage history for AP device' do
      api_response = OpenStruct.new({
        parsed_response: [
          {
            'startTs' => '2022-09-23T00:00:00Z',
            'endTs' => '2022-09-24T00:00:00Z',
            'totalKbps' => 1476, 'sentKbps' => 485,
            'receivedKbps' => 990
          },
          {
            'startTs' => '2022-09-24T00:00:00Z',
            'endTs' => '2022-09-25T00:00:00Z',
            'totalKbps' => 5,
            'sentKbps' => 3,
            'receivedKbps' => 3
          },
          {
            'startTs' => '2022-09-25T00:00:00Z',
            'endTs' => '2022-09-26T00:00:00Z',
            'totalKbps' => 1,
            'sentKbps' => 1,
            'receivedKbps' => 1
          },
          {
            'startTs' => '2022-09-26T00:00:00Z',
            'endTs' => '2022-09-27T00:00:00Z',
            'totalKbps' => 1483,
            'sentKbps' => 341,
            'receivedKbps' => 1142
          },
          {
            'startTs' => '2022-09-27T00:00:00Z',
            'endTs' => '2022-09-28T00:00:00Z',
            'totalKbps' => 1492,
            'sentKbps' => 351,
            'receivedKbps' => 1141
          },
          {
            'startTs' => '2022-09-28T00:00:00Z',
            'endTs' => '2022-09-29T00:00:00Z',
            'totalKbps' => 1222,
            'sentKbps' => 470,
            'receivedKbps' => 752
          },
          {
            'startTs' => '2022-09-29T00:00:00Z',
            'endTs' => '2022-09-30T00:00:00Z',
            'totalKbps' => 1131,
            'sentKbps' => 357,
            'receivedKbps' => 774
          }
        ],
        code: 200,
        success?: true,
        message: 'OK',
        header: 'OK'
      })

      expected_response = {
        filtered_response: {
          :graph_labels => ['24 Sep', '25 Sep', '26 Sep', '27 Sep', '28 Sep', '29 Sep', '30 Sep'],
          :graph_data => [1.476, 0.005, 0.001, 1.483, 1.492, 1.222, 1.131]
        },
        status: :ok
      }

      allow(HTTParty).to receive(:get).and_return(api_response)
      result = service.ap_device_usage_history('N_695243192475336835', 'k7ddf7c', '2022-09-23T04:24:07.137Z', '86400', '%d %b')
      expect(result).to eq(expected_response)
    end

    it 'describes response when device is not found' do
      api_response = OpenStruct.new({
        parsed_response: {
          'errors' => [
            "No device with serial 'Q2KD-56T2-NYPS'"
          ]
        },
        status: :bad_request,
        success?: false,
      })
      allow(HTTParty).to receive(:get).and_return(api_response)
      result = service.ap_device_usage_history('N_695243192475336835', 'Q2KD-56T2-NYPS', '2022-09-23T04:24:07.137Z', '86400', '%d %b')
      expect(result).to eq(api_response)
    end
  end

  describe '#firewall_usage_history' do
    it 'describes firewall usage history' do
      api_response = OpenStruct.new({
        parsed_response: [
          {'startTime' => '2022-09-23T00:00:00Z',
            'endTime' => '2022-09-24T00:00:00Z',
            'byInterface' =>  [
              {
                'interface' => 'cellular',
                'sent' => 0,
                'received' => 0
              },
              {
                'interface' => 'cellular',
                'sent' => 0,
                'received' => 0
              },
              {
                'interface' => 'wan1',
                'sent' => 5165882057,
                'received' => 2977257551
              },
              {
                'interface' => 'wan2',
                'sent' => 3742139536,
                'received' => 55572215538
              }
            ]
          },
          {
            'startTime' => '2022-09-24T00:00:00Z',
            'endTime' => '2022-09-25T00:00:00Z',
            'byInterface' =>  [
              {
                'interface' => 'cellular',
                'sent' => 0,
                'received' => 0
              },
              {
                'interface' => 'cellular',
                'sent' => 0,
                'received' => 0
              },
              {
                'interface' => 'wan1',
                'sent' => 4118469921,
                'received' => 982842448
              },
              {
                'interface' => 'wan2',
                'sent' => 2761862513,
                'received' => 54548872344
              }
            ]
          },
          {
            'startTime' => '2022-09-25T00:00:00Z',
            'endTime' => '2022-09-26T00:00:00Z',
            'byInterface' =>  [
              {
                'interface' => 'cellular',
                'sent' => 0,
                'received' => 0
              },
              {
                'interface' => 'cellular',
                'sent' => 0,
                'received' => 0
              },
              {
                'interface' => 'wan1',
                'sent' => 3848112508,
                'received' => 964624523
              },
              {
                'interface' => 'wan2',
                'sent' => 2804674331,
                'received' => 54514033741
              }
            ]
          },
          {
            'startTime' => '2022-09-26T00:00:00Z',
            'endTime' => '2022-09-27T00:00:00Z',
            'byInterface' =>  [
              {
                'interface' => 'cellular',
                'sent' => 0,
                'received' => 0
              },
              {
                'interface' => 'cellular',
                'sent' => 0,
                'received' => 0
              },
              {
                'interface' => 'wan1',
                'sent' => 5117252128,
                'received' => 2469385671
              },
              {
                'interface' => 'wan2',
                'sent' => 4943149860,
                'received' => 56402558032
              }
            ]
          },
          {
            'startTime' => '2022-09-27T00:00:00Z',
            'endTime' => '2022-09-28T00:00:00Z',
            'byInterface' =>  [
              {
                'interface' => 'cellular',
                'sent' => 0,
                'received' => 0
              },
              {
                'interface' => 'cellular',
                'sent' => 0,
                'received' => 0
              },
              {
                'interface' => 'wan1',
                'sent' => 4317035976,
                'received' => 2849185308
              },
              {
                'interface' => 'wan2',
                'sent' => 4282949028,
                'received' => 55813028928
              }
            ]
          },
          {
            'startTime' => '2022-09-28T00:00:00Z',
            'endTime' => '2022-09-29T00:00:00Z',
            'byInterface' =>  [
              {
                'interface' => 'cellular',
                'sent' => 0,
                'received' => 0
              },
              {
                'interface' => 'cellular',
                'sent' => 0,
                'received' => 0
              },
              {
                'interface' => 'wan1',
                'sent' => 5992847156,
                'received' => 2593014657
              },
              {
                'interface' => 'wan2',
                'sent' => 5323648815,
                'received' => 57152398682
              }
            ]
          },
          {
            'startTime' => '2022-09-29T00:00:00Z',
            'endTime' => '2022-09-30T00:00:00Z',
            'byInterface' =>  [
              {
                'interface' => 'cellular',
                'sent' => 0,
                'received' => 0
              },
              {
                'interface' => 'cellular',
                'sent' => 0,
                'received' => 0
              },
              {
                'interface' => 'wan1',
                'sent' => 4016382451,
                'received' => 2298706773
              },
              {
                'interface' => 'wan2',
                'sent' => 5314619002,
                'received' => 57223210458
              }
            ]
          }
        ],
        code: 200,
        success?: true,
        message: 'OK',
        header: 'OK'
      })

      expected_response = {
        filtered_response: {
          :graph_labels => ['24 Sep', '25 Sep', '26 Sep', '27 Sep', '28 Sep', '29 Sep', '30 Sep'],
          :wan1_graph_data => [18.0956848368976, 11.3361363463918, 10.6948642302882, 16.8590265169378, 15.9247769373048, 19.079502120848602, 14.0333912735728],
          :wan2_graph_data => [131.8083598454428, 127.3559149992254, 127.3736330775984, 136.3224320776024, 133.5452822138232, 138.8342727478334, 138.971564626012]
        },
        status: :ok
      }

      allow(HTTParty).to receive(:get).and_return(api_response)
      result = service.firewall_usage_history('N_695243192475336835', '2022-09-23T04:24:07.137Z', '86400', '%d %b')
      expect(result).to eq(expected_response)
    end
  end
end
