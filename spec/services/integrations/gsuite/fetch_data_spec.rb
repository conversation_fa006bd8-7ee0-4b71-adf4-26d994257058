require 'rails_helper'
describe Integrations::Gsuite::FetchData do
  let(:company) { FactoryBot.create(:company) }
  let(:client) do
    described_class.new(company.id, "write")
  end
  before(:each) do
    config = Integrations::Gsuite::Config.create(
      token: "*********************************************************************************************************************************************************************",
      scope: "[\"https://www.googleapis.com/auth/admin.reports.usage.readonly\", \"https://www.googleapis.com/auth/admin.directory.user.readonly\", \"https://www.googleapis.com/auth/admin.directory.user.security\", \"https://www.googleapis.com/auth/apps.licensing\"]",
      refresh_token: "1//03DfeJXZ60nGJCgYIARAAGAMSNwF-L9IrFEPDZDH7udEz5s8JLbhU6v08Bdsfc11wWWV0Qnjz1VxUSCBRdRoRwvu2ZqFaX-i5J0Q",
      code: "4/0AX4XfWiKYEgEZgkoF3ZMAp6EGGil32yHqxvNWiyGsfZwUBGD8kaMrGGkqHuJkd7xGHmtgw",
      company_id: company.id,
      client_id: "997960466792-ug27lkbf3dv0vtq40l9oe17ehg197k9s.apps.googleusercontent.com",
      expiration_time: "1652896617"
    )
    gsuite_user = Integrations::Gsuite::User.create(
      name: "CodingCops Support",
      email: "<EMAIL>",
      external_customer_id: "C00qumwn8",
      creation_time: 'Tue, 22 Sep 2020 22:40:23 UTC +00:00',
      last_login_time: 'Mon, 09 May 2022 12:21:51 UTC +00:00',
      is_admin: true,
      config_id: config.id,
      company_id: company.id
    )
    user_1 = OpenStruct.new({
              primary_email: "<EMAIL>",
              name:  OpenStruct.new({fullname: "user1"}),
              creation_time: "Wed, 14 Mar 2018 03:41:50 +0000",
              customer_id: "C014vjuq6",
              id: "10078456892758223370445",
              is_admin: false,
              last_login_time: "Wed, 26 Jun 2019 09:18:15 +0000",
            })
    user_2 = OpenStruct.new({
              primary_email: "<EMAIL>",
              name: OpenStruct.new({fullname: "user2"}),
              creation_time: "Wed, 14 Mar 2018 03:41:50 +0000",
              customer_id: "C014vjuq6",
              id: "100784568927582370445",
              is_admin: false,
              last_login_time: "Wed, 26 Jun 2019 09:18:15 +0000",
            })
    @users = OpenStruct.new(users: [user_1, user_2])
    @apps = [
              {"client_id"=>"705229005811-2fdpup66d8aefq4qs2ru1n8qiosuq4fb.apps.googleusercontent.com", "client_name"=>"Honey", "num_users"=>"1"},
              {"client_id"=>"984739005367-g6fhcp7r3ls683n4r3mig0ghcsvt2bp9.apps.googleusercontent.com", "client_name"=>"SoundCloud", "num_users"=>"1"},
              {"client_id"=>"946018238758-aht63nue21hvlj2d6maf07vvdjisckhh.apps.googleusercontent.com", "client_name"=>"OS X", "num_users"=>"1"},
              {"client_id"=>"2***********.apps.googleusercontent.com", "client_name"=>"Mailtrack", "num_users"=>"1"}
            ]
    user_app_1 = OpenStruct.new({
                  anonymous: false,
                  client_id: "***********.apps.googleusercontent.com",
                  display_text: "SoundCloud",
                  etag: "\"zPBZh0mDALCYqI7HMkUiX8qQjpg/qh1H9Snq8T8efIwEfuC8Rj3kYOw\"",
                  kind: "admin#directory#token",
                  native_app: false,
                  scopes: ["https://www.google.com/accounts/OAuthLogin"],
                  user_key: "109601430506775666744"
                  })
    user_app_2 = OpenStruct.new({
                  anonymous: false,
                  client_id: "***********.apps.googleusercontent.com",
                  display_text: "Honey",
                  etag: "\"zPBZh0mDALCYqI7HMkUiX8qQjpg/qh1H9Snq8T8efIwEfuC8Rj3kYOw\"",
                  kind: "admin#directory#token",
                  native_app: false,
                  scopes: ["https://www.google.com/accounts/OAuthLogin"],
                  user_key: "109601430506775666744"
                })
    @user_apps = OpenStruct.new({items: [user_app_1, user_app_2]})
    @credentials = OpenStruct.new({
                    client_id: *********,
                    client_secret: "3453849",
                    access_token: "mock-access-token",
                    scope: "nfekh5834",
                    refresh_token: "fkjfbbfkjsbfsjkbfsjkfb3784y32b2j35b"
                  })
    license_1 = OpenStruct.new({
                  product_id: "Google-Apps",
                  product_name: "G Suite",
                  sku_name: "G Suite Basic",
                  user_id: "<EMAIL>",
                  sku_id: "Google-Apps-For-Business"
                })
    license_2 = OpenStruct.new({
                  product_id: "Google-Apps",
                  product_name: "G Suite",
                  sku_name: "G Suite Basic",
                  user_id: "<EMAIL>",
                  sku_id: "Google-Apps-For-Business"
                })
    @licenses = OpenStruct.new({items: [license_1, license_2]})
  end
  describe '#get_users' do
    it 'Get All Users '  do
      allow_any_instance_of(Google::Apis::AdminDirectoryV1::DirectoryService).to receive(:list_users).and_return(@users)
      expect(client.get_users.users.count).to eq(2)
      expect(Logs::ApiEvent.where(company_id: company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "get users").count).to eq(1)
    end
  end
  describe '#get_apps' do
    it 'Get All Apps '  do
      allow_any_instance_of(Google::Apis::AdminReportsV1::ReportsService).to receive(:get_customer_usage_report).and_return(@apps)
      expect(client.get_apps("Wed, 26 Jun 2019 09:18:15 +0000")).to be_present
      expect(Logs::ApiEvent.where(company_id: company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "get apps").count).to eq(1)
    end
  end
  describe '#get_user_apps' do
    it 'Get User Apps'  do
      allow_any_instance_of(Google::Apis::AdminDirectoryV1::DirectoryService).to receive(:list_tokens).and_return(@user_apps)
      expect(client.get_user_apps("<EMAIL>")).to be_present
      expect(Logs::ApiEvent.where(company_id: company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "get user apps").count).to eq(1)
    end
  end
  describe '#get_gsuite_licenses' do
    it 'Get User License'  do
      allow_any_instance_of(Google::Apis::LicensingV1::LicensingService).to receive(:list_license_assignments_for_product_and_sku).and_return(@licenses)
      expect(client.get_gsuite_licenses).to be_present
      expect(Logs::ApiEvent.where(company_id: company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "get licenses").count).to eq(14)
    end
  end
  describe '#directory_client' do
    it 'Client object'  do
      dir_client = OpenStruct.new({
        root_url: "https://www.googleapis.com/",
        base_path: "admin/directory/v1/",
        upload_path: "upload/admin/directory/v1/",
        batch_path: "batch/admin/directory_v1",
        client_options: "Genuity",
        application_version: "0.0.0",
        project_id: nil,
        authorization_uri: "https://accounts.google.com/o/oauth2/auth",
        token_credential_uri: "https://oauth2.googleapis.com/token",
        client_id: Rails.application.credentials.gsuite[:client_id],
        client_secret: Rails.application.credentials.gsuite[:client_secret],
        code: nil,
        expires_at: "2019-07-10 20:42:10 +0500",
        issued_at: nil,
        scope: ["https://www.googleapis.com/auth/admin.reports.usage.readonly", "https://www.googleapis.com/auth/admin.directory.user.readonly", "https://www.googleapis.com/auth/admin.directory.user.security"],
        state: nil,
        username: nil,
        access_type: "offline",
        expiry:60,
        access_token: "mock-access-token",
        })
      allow_any_instance_of(Integrations::Gsuite::FetchData).to receive(:directory_client).and_return(dir_client)
      expect(client.directory_client).to be_present
    end
  end
  describe '#get_authorizer' do
    it 'Get Authorizer'  do
      authorizer =OpenStruct.new({
                    id: Rails.application.credentials.gsuite[:client_id],
                    secret: Rails.application.credentials.gsuite[:client_secret],
                    scope: ["https://www.googleapis.com/auth/admin.reports.usage.readonly", "https://www.googleapis.com/auth/admin.directory.user.readonly", "https://www.googleapis.com/auth/admin.directory.user.security"],
                    token_store: "mock_token_store",
                    store: "8943u3498h934h6n934n",
                    filename: "token.yaml",
                    abort: false,
                    u0ltra_safe: false,
                    thread_safe: false,
                    callback_uri: "/oauth2callback",
                    })
      allow_any_instance_of(Google::Auth::UserAuthorizer).to receive(:get_credentials).and_return(authorizer)
      expect(client.get_authorizer(company.id, "write"))
    end
  end
  describe '#get_refresh_token' do
    it 'Get Refresh Token'  do
      ref_token = {
                    access_token: "mock-access-token",
                    expires_in: 3600,
                    scope: "https://www.googleapis.com/auth/admin.directory.user.readonly https://www.googleapis.com/auth/admin.directory.user.security https://www.googleapis.com/auth/apps.licensing https://www.googleapis.com/auth/admin.reports.usage.readonly",
                    token_type: "Bearer"
                  }
      allow_any_instance_of(described_class).to receive(:refresh_token_call).and_return(ref_token)
      expect(client.refresh_token).to eq(ref_token)
      expect(Logs::ApiEvent.where(company_id: company.id,
                                  status: :success,
                                  class_name: described_class.name,
                                  api_type: "refresh token").count).to eq(1)
    end
  end
  describe '#revoke_permission?' do
    it 'Revoke permisiion of gsuite user'  do
      allow_any_instance_of(Integrations::Gsuite::FetchData).to receive(:revoke_permission?).and_return(true)
      expect(client.revoke_permission? "8464969hgdf84-=+\]/.,<`_+348y87").to eq(true)
    end
  end
end
