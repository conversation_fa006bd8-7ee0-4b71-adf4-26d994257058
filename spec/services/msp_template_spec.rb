require 'rails_helper'
include CompanyUserHelper

RSpec.describe Msp::Template, type: :service do
  create_company_and_user
  let!(:template_asset_type) { create(:msp_templates_asset_type, id: 3) }
  let!(:template_category) { create(:msp_templates_category, id: 2) }
  let!(:template_document) { create(:msp_templates_document, id: 3) }
  let(:items) do
    {
      "asset_types" => [{ "id" => 1, "name" => "Asset Type A" }],
      "categories" => [{ "id" => 2, "name" => "Category A" }],
      "documents" => [{ "id" => 3, "name" => "Document A" }]
    }
  end

  subject { described_class.new([company.id], items, company_user.id) }

  describe '#create_items' do
    context 'when asset_types, categories, and documents are provided' do
      it 'calls copy_asset_types, copy_categories, and copy_documents with correct arguments' do
        expect(subject).to receive(:copy_asset_types).with(company, items["asset_types"])
        expect(subject).to receive(:copy_categories).with(company, items["categories"])
        expect(subject).to receive(:copy_documents).with(company, items["documents"])
        subject.create_items
      end
    end

    context 'when errors occur during copy' do
      before do
        allow(subject).to receive(:copy_asset_types).and_raise(StandardError, "Copy Error")
      end

      it 'adds error to @errors and raises a StandardError' do
        expect { subject.create_items }.to raise_error(StandardError, /Copy Error/)
      end
    end
  end 

  describe '#delete_items' do
    let!(:asset_type) { create(:msp_templates_asset_type, company: company) }  
    let!(:category) { create(:msp_category, company: company) }  
    let(:items) do
      {
        "asset_types" => [{ "id" => asset_type.id }],  
        "categories" => [{ "id" => category.id }]       
      }
    end

    it 'deletes specified asset_types and categories' do
      expect { subject.delete_items }
        .to change { company.msp_asset_types.count }.by(-1)
    end
  end

  describe '#copy_asset_types' do
    let(:mock_asset_type) { { "id" => 3, "name" => "Asset Type B" } }

    it 'creates a new asset type if it does not exist' do
      expect { subject.copy_asset_types(company, [mock_asset_type]) }
        .to change { company.asset_types.count }.by(1)
    end
  end

  describe '#copy_categories' do
    let(:mock_category) { { "id" => 2, "name" => "Category A" } }

    it 'creates a new category if it does not exist' do
      expect { subject.copy_categories(company, [mock_category]) }
        .to change { company.categories.count }.by(1)
    end

    it 'updates the category if it already exists' do
      existing_category = create(:msp_category, company: company, name: "Category A", msp_templates_category_id: 2)
      expect { subject.copy_categories(company, [mock_category]) }.not_to change(company.categories, :count)
      expect(existing_category.reload.msp_templates_category_id).to eq(mock_category["id"])
    end
  end

  describe '#copy_documents' do
    let(:mock_document) { { "id" => 3, "name" => "Document A" } }

    it 'creates a new document if it does not exist' do
      expect { subject.copy_documents(company, [mock_document]) }
        .to change { company.library_documents.count }.by(1)
    end
  end

  describe '#valid_file_url?' do
    it 'returns true for a valid URL' do
      allow(RestClient).to receive(:get).and_return(double(code: 200))
      expect(subject.send(:valid_file_url?, 'http://example.com/file')).to be true
    end

    it 'returns false for an invalid URL' do
      allow(RestClient).to receive(:get).and_return(double(code: 404))
      expect(subject.send(:valid_file_url?, 'http://invalid-url.com')).to be false
    end
  end

  describe '#custom_form_params' do
    let(:mock_item) { { "form_name" => "Form A", "company_module" => "Module A" }.with_indifferent_access }

    it 'permits specific fields for a custom form' do
      params = subject.send(:custom_form_params, ActionController::Parameters.new(mock_item))
      expect(params.to_h).to include("form_name" => "Form A", "company_module" => "Module A")
    end
  end

  describe '#create_copied_logs' do
    let(:copy_item) { double("copy_item") }
    let(:item) { { "name" => "Example Item" } }

    it 'calls MspEventLogService to create a copy activity log' do
      event_log_service = double("MspEventLogService")
      allow(MspEventLogService).to receive(:new).and_return(event_log_service)
      expect(event_log_service).to receive(:create_copy_activity)
      subject.send(:create_copied_logs, copy_item, item, 'Category')
    end
  end
end
