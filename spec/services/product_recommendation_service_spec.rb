require 'rails_helper'

describe ProductRecommendationService do
  let(:company) { FactoryBot.create(:company) }
  let(:vendor) { FactoryBot.create(:vendor, company: company) }

  subject { described_class.new(company, app) }

  context "matching product" do
    let!(:app) { company.apps.create(name: 'My App') }
    let!(:product) { company.products.create(name: 'My App 1', vendor: vendor) }

    it 'should return the right matching product' do
      actual = subject.matching_product
      expect(actual).to eq(product)
    end
  end

  context "matching default product" do
    let!(:app) { company.apps.create(name: 'Amazon Web Service') }
    let!(:default_product) { DefaultProduct.find_by(name: "Amazon Web Services") }

    it 'should return the right matching product' do
      actual = subject.matching_product
      expect(actual).to eq(default_product)
    end
  end
end
