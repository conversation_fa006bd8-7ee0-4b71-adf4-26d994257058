require 'rails_helper'
require 'faker'

describe ContributorOptionsQuery do
  let!(:company) { FactoryBot.create(:company) }
  let!(:group) { Group.create(name: 'Group CCC', company: company) }
  let!(:guest) { Guest.create(first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', company: company) }
  let!(:guest2) { Guest.create(first_name: '<PERSON>', last_name: '<PERSON>', company: company) }
  let!(:aaa_user) { 
    user = FactoryBot.create(:user, first_name: 'Aaa', last_name: 'User', email: '<EMAIL>')
    user.skip_validations = true
    user.save!(validate: false)
    user
  }
  let!(:aaa_company_user) { company.company_users.create(user: aaa_user) }
  let!(:bbb_user) {
    user = FactoryBot.create(:user, first_name: 'Bbb', last_name: 'Other', email: '<EMAIL>')
    user.skip_validations = true
    user.save!(validate: false)
    user
  }
  let!(:bbb_company_user) { company.company_users.create(user: bbb_user) }
  10.times do |idx|
    let!("user_#{idx}") { 
      user = FactoryBot.create(:user, first_name: Faker::Name.first_name, last_name: Faker::Name.last_name, email: "user#{idx}@gogenuity.com")
      user.skip_validations = true
      user.save(validate: false)
      user
    }
    let!("company_user_#{idx}") { company.company_users.create(user: send("user_#{idx}")) }
  end

  let(:root_contributors) { 
    company.contributors.left_outer_joins(:group, :guest, company_user: :user)
  }

  subject { described_class.new(contributor_params) }

  let(:actual_contributors) { subject.call }
  
  context "#call" do
    context "with simple query params" do
      let(:contributor_params) do
        {
          contributors: root_contributors,
          archived: false,
          offset: 0,
        }
      end

      let(:expected_contributors) { 
        root_contributors.order("groups.name, users.first_name, users.last_name, guests.first_name, guests.last_name") 
      }

      it "will return contributor options" do
        expect(actual_contributors.pluck(:id).sort).to eq(expected_contributors.pluck(:id).sort )
      end

      it "should include groups" do
        expect(actual_contributors).to include(group.contributor)
      end

      it "should not include guests" do
        expect(actual_contributors).not_to include(guest2.contributor)
      end
    end

    context "with include guests params" do
      let(:contributor_params) do
        {
          contributors: root_contributors,
          archived: false,
          offset: 0,
          include_guests: true,
        }
      end

      let(:expected_contributors) { 
        root_contributors.order("groups.name, users.first_name, users.last_name, guests.first_name, guests.last_name") 
      }

      it "will return contributor options" do
        expect(actual_contributors.pluck(:id).sort).to eq(expected_contributors.pluck(:id).sort )
      end

      it "should include groups" do
        expect(actual_contributors).to include(group.contributor)
      end

      it "should include guests" do
        expect(actual_contributors).to include(guest.contributor)
      end
    end

    context "with includes/excludes params" do
      let(:contributor_params) do
        {
          contributors: root_contributors,
          archived: false,
          limit: 10,
          offset: 0,
          includes: [ aaa_company_user.contributor_id ],
          excludes: [ company_user_1.contributor_id ],
        }
      end

      let(:expected_contributors) { root_contributors.where('users.first_name' => 'Aaa') }

      it "will return contributor options" do
        expect(actual_contributors).to eq(expected_contributors)
      end
    end

    context "with simple query params" do
      let(:contributor_params) do
        {
          contributors: root_contributors,
          archived: false,
          limit: 10,
          offset: 0,
          query: "Aaa",
        }
      end

      let(:expected_contributors) { root_contributors.where('users.first_name' => 'Aaa') }

      it "will return contributor options" do
        expect(actual_contributors).to eq(expected_contributors)
      end
    end

    context "with query params" do
      let(:contributor_params) do
        {
          contributors: root_contributors,
          archived: false,
          limit: 10,
          offset: 0,
          query: "Aaa User",
        }
      end

      let(:expected_contributors) { root_contributors.where('users.first_name' => 'Aaa') }

      it "will return contributor options" do
        expect(actual_contributors).to eq(expected_contributors)
      end
    end
  end
end
