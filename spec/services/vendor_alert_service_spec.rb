require 'rails_helper'

describe VendorAlertService do
  subject { described_class.new(vendor: vendor, date: Date.today) }
  let!(:company) { FactoryBot.create(:company) }

  context "with a current transaction set" do
    context "when vendor spend exceeds previous month" do
      let!(:vendor) { FactoryBot.create(:vendor, company_id: company.id) }
      let!(:module_alert) { FactoryBot.create(:module_alert, company_id: company.id, monitorable_id: vendor.id, type: "VendorSpendAlert", monitorable_type: "Vendor") }
      let!(:transaction1) { FactoryBot.create(:general_transaction,
                                              company_id: company.id,
                                              status: :recognized,
                                              transaction_date: 1.month.ago,
                                              vendor: vendor,
                                              amount: 100) }
      let!(:transaction2) { FactoryBot.create(:general_transaction,
                                              company_id: company.id,
                                              status: :recognized,
                                              vendor: vendor,
                                              amount: 200) }

      it "should create a new notication" do
        subject.alert
        expect(vendor.module_alerts.first.module_alert_notifications).to be_present
      end
    end

    context "when vendor spend does not exist for previous month" do
      let(:vendor) { FactoryBot.create(:vendor) }
      let!(:module_alert) { FactoryBot.create(:module_alert, company_id: company.id, monitorable_id: vendor.id, type: "VendorSpendAlert", monitorable_type: "Vendor") }
      let!(:transaction1) { FactoryBot.create(:general_transaction,
                                              status: :recognized,
                                              company_id: company.id,
                                              transaction_date: Date.today,
                                              vendor: vendor, amount: 100) }

      it "should not create a new notication" do
        subject.alert
        expect(vendor.module_alerts.first.module_alert_notifications).to be_blank
      end
    end
  end

  context "when vendor spend does not exceed previous month" do
    let(:vendor) { FactoryBot.create(:vendor) }
    let!(:module_alert) { FactoryBot.create(:module_alert, company_id: company.id, monitorable_id: vendor.id, type: "VendorSpendAlert", monitorable_type: "Vendor") }
    let!(:transaction1) { FactoryBot.create(:general_transaction,
                                            status: :recognized,
                                            company_id: company.id,
                                            transaction_date: 1.month.ago,
                                            vendor: vendor, amount: 100) }
    let!(:transaction2) { FactoryBot.create(:general_transaction,
                                            status: :recognized,
                                            company_id: company.id,
                                            vendor: vendor,
                                            amount: 100) }

    it "should not create a new notication" do
      subject.alert
      expect(vendor.module_alerts.first.module_alert_notifications).to be_blank
    end
  end

  context "with an old transaction set" do
    let(:vendor) { FactoryBot.create(:vendor) }
    let!(:module_alert) { FactoryBot.create(:module_alert, company_id: company.id, monitorable_id: vendor.id, type: "VendorSpendAlert", monitorable_type: "Vendor") }
    let(:base_date) { 100.days.ago }
    let!(:transaction1) { FactoryBot.create(:general_transaction,
                                            status: :recognized,
                                            company_id: company.id,
                                            transaction_date: 1.month.ago,
                                            vendor: vendor, amount: 100) }
    let!(:transaction2) { FactoryBot.create(:general_transaction,
                                            transaction_date: base_date + 1.month,
                                            status: :recognized,
                                            company_id: company.id,
                                            vendor: vendor,
                                            amount: 200) }

    subject { described_class.new(vendor: vendor, date: base_date + 1.month) }

    it "should not create a new notication" do
      subject.alert
      expect(vendor.module_alerts.first.module_alert_notifications).to be_blank
    end
  end
end
