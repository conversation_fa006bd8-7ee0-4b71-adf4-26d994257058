require 'rails_helper'
require "ostruct"

describe Checks::PlaidInstitutionCheck do
  before do
    @company = FactoryBot.create(:company)
  end

  # ID of one of Plaid's test institutions that does not return transaction status information
  let!(:subject1) { Checks::PlaidInstitutionCheck.new("ins_109509") }

  # Chase Bank ID (assuming it is healthy at the time of running this test)
  let!(:subject2) { Checks::PlaidInstitutionCheck.new("ins_3") }

  context "checking health status of the institution" do
    it "should return true when transaction status health is not present" do
      response = subject1.check()
      expect(response).to eq(true)
    end

    it "should return true when institution health is present and is healthy" do
      response = subject2.check()
      expect(response).to eq(true)
    end

    it "should return false when transaction status health is present and degraded" do
      response = { institution: { status: { transactions_updates: { status: "DEGRADED" }}}}.to_json
      response_object = JSON.parse(response, object_class: OpenStruct)
      allow_any_instance_of(Checks::PlaidInstitutionCheck).to receive(:response).and_return(response_object)
      new_response = subject2.check()

      expect(new_response).to eq(false)
    end

    it "should return false when transaction status health is present and down" do
      response = { institution: { status: { transactions_updates: { status: "DOWN" }}}}.to_json
      response_object = JSON.parse(response, object_class: OpenStruct)
      allow_any_instance_of(Checks::PlaidInstitutionCheck).to receive(:response).and_return(response_object)
      new_response = subject2.check()

      expect(new_response).to eq(false)
    end
  end
end

