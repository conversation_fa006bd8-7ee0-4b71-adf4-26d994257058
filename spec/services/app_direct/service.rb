require 'rails_helper'
include CompanyUserHelper

describe AppDirect::Service do
  create_company_and_user
  let(:company_user) { company.company_users.first }
  let(:reseller_company) { create(:company, is_reseller_company: true ) }
  let(:service) {
    allow_any_instance_of(AppDirect::Service).to receive(:token_call).and_return("aQUdrW3NuXv2XeQFhG1TsCJFJtL0Vo7ZF2bLL")
    described_class.new(company)
  }

  describe 'Authenticate' do
    it "is authenticate" do
      expect(service.token_call).to be_present
    end
  end

  describe '#company' do
    it 'Create app direct company'  do
      req_res = {
        "uuid": "39aacedf-34gfdwre-6346c9bb3",
        "companyId": nil,
        "name": "testing",
        "enabled": true,
        "allowLogin": true,
        "address": nil,
        "customAttributes": [],
        "creationDate": 1608291354532,
        "salesAgent": nil,
        "emailAddress": nil,
        "reseller": false,
        "channelAdmin": false,
        "referral": false,
        "externalId": nil,
        "phoneNumber": nil,
        "defaultRole": "USER",
        "countryCode": "US",
        "status": "ACTIVE",
        "firstUser": {
          "uuid": "92e2ab80-f3ef-4acf-81a5-1c1d120ae417",
          "email": "<EMAIL>",
          "firstName": "Super",
          "lastName": "Admin",
          "roles": ["SYS_ADMIN", "BILLING_ADMIN", "USER"]
        },
        "links": []
      }
      allow_any_instance_of(AppDirect::Service).to receive(:create_company).and_return(req_res)
      expect(service.create_company(company, false)).to eql(req_res)
    end
  end

  describe '#update company' do
    it 'Update app direct company'  do
      req_res = {
        "uuid": "a21a1bc6d-a22d-8bf888ee267c",
        "companyId": 8984632,
        "name": "testing",
        "enabled": true,
        "allowLogin": true,
        "address": {
          "street1": nil,
          "street2": nil,
          "city": nil,
          "state": nil,
          "zip": nil,
          "country": nil
        },
        "companySize": nil,
        "customAttributes": [],
        "creationDate": 1608298991000,
        "industry": nil,
        "salesAgent": nil,
        "emailAddress": nil,
        "website": nil,
        "picture": nil,
        "vendor": false,
        "reseller": true,
        "channelAdmin": false,
        "phoneNumber": nil,
        "defaultRole": "USER",
        "countryCode": "US",
        "status": "ACTIVE",
        "links": []
      }
      allow_any_instance_of(AppDirect::Service).to receive(:update_company).and_return(req_res)
      expect(service.update_company(company, true)).to eq(req_res)
    end
  end

  describe '#company user' do
    it 'Create app direct company user'  do
      roles = ["ROLE_USER"]
      allow_any_instance_of(AppDirect::Service).to receive(:create_company_user).and_return(nil)
      expect(service.create_company_user(company_user, roles)).to eq(nil)
    end
  end

  describe '#user role' do
    it 'Update user role to reseller'  do
      req_res =  {
        "user": {
          "uuid": "bf854580-716f5b5defc",
          "email": "<EMAIL>",
          "userName": "<EMAIL>",
          "firstName": "Super",
          "lastName": "Admin",
          "picture": nil,
          "activated": false,
          "allowLogin": true,
          "ldapId": nil,
          "boostUser": nil,
          "creationDate": nil,
          "externalId": nil,
          "roles": ["ROLE_USER"],
          "currency": nil,
          "customAttributes": [],
          "properties": nil,
          "metadata": {"apsUid": nil},
          "links": []
        },
        "company": {
          "uuid": "a21a1bc6d-8bf888ee267c",
          "companyId": nil,
          "name": "testing",
          "enabled": true,
          "allowLogin": true,
          "address": nil,
          "companySize": nil,
          "customAttributes": [],
          "emailAddress": nil,
          "reseller": true,
          "channelAdmin": false,
          "referral": false,
          "externalId": nil,
          "phoneNumber": nil,
          "defaultRole": "RESELLER",
          "countryCode": nil,
          "status": nil,
          "thirtyDaysPurchaseLimit": nil,
          "links": []
        },
        "enabled": false,
        "lastUsed": false,
        "roles": ["ROLE_BILLING_ADMIN", "ROLE_RESELLER", "ROLE_SYS_ADMIN"],
        "credentialsProfile": nil,
        "metadata": {"mosi_username": nil},
        "links": []
      }
      comp_uuid = company.app_direct_uuid
      user_uuid = company.app_direct_admin_uuid
      allow_any_instance_of(AppDirect::Service).to receive(:update_reseller_roles).and_return(req_res)
      expect(service.update_reseller_roles(comp_uuid, user_uuid)).to eq(req_res)
    end
  end

  describe '#link company' do
    it 'Link customer comapany with reseller company'  do
      req_res = {
        "customerCompany": {
          "id": "f4sdbfa9-484e-9476-4b0a75a3511c",
          "name": "testing2",
          "allowLogin": true,
          "enabled": true},
        "resellerCompany": {
          "id": "a21a1bc6-sredr2d-8bf888ee267c",
          "name": "testing1",
          "allowLogin": true,
          "enabled": true
        },
        "id": "3e91efe4-4618-45c7-acf2-609e24d60330",
        "createdOn": 1608301859762
      }
      cust_comp = company.app_direct_uuid
      reseller_comp = reseller_company.app_direct_uuid

      allow_any_instance_of(AppDirect::Service).to receive(:link_customer_comp).and_return(req_res)
      expect(service.link_customer_comp(cust_comp, reseller_comp)).to eq(req_res)
    end
  end
end