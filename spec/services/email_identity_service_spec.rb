# frozen_string_literal: true

require 'rails_helper'

describe EmailIdentityService do
  subject do
    subj = described_class.new
    subj.client = double()
    subj
  end

  let(:domain) { ticket_email.from[ticket_email.from.index("@")..-1] }

  context "#template_name" do
    it "should return correct value" do
      expect(subject.template_name).to eq("EmailVerification#{Rails.env.titleize}")
    end
  end

  context "#verify_email" do
    let(:email) { "<EMAIL>" }
    let!(:helpdesk_custom_email) { create(:helpdesk_custom_email) }

    before :each do
      allow(subject.client).to receive(:send_custom_verification_email).with({
        email_address: email,
        template_name: "EmailVerificationTest",
      }).and_return(OpenStruct.new)

      allow(subject).to receive(:set_indentity_notification_topic).and_return(true)

      allow(subject.client).to receive(:get_identity_verification_attributes).with({
        identities: [helpdesk_custom_email.email]
      }).and_return(OpenStruct.new({
        verification_attributes: {
          helpdesk_custom_email.email => OpenStruct.new(verification_status: 'Success')
        }
      }))
    end

    it 'will return no emails' do
      subject.verify_email(helpdesk_custom_email)
    end
  end

  context "#when_email_is_nil" do
    let(:email) { nil }

    it 'does not throw ArgumentError' do
      expect { described_class.new.verify_email(email) }.not_to raise_error(ArgumentError)
    end
  end

  context "#is_email_verified?" do
    let(:email) { "<EMAIL>" }
    let(:emails) { [ email ] }
    let(:expected_response) {
      OpenStruct.new({
        verification_attributes: {
          email => OpenStruct.new(verification_status: 'Success')
        }
      })
    }

    before :each do
      waiter = double()
      allow(waiter).to receive(:wait).with(identities: emails).and_return(expected_response)
      allow(subject).to receive(:waiter).and_return(waiter)
    end

    it 'will return no emails' do
      response = subject.is_email_verified?(emails)
      expect(response).to be_present
      expect(response[email]).to be_truthy
    end
  end
end
