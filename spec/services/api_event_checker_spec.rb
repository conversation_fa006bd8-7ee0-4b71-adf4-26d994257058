require 'rails_helper'

describe ApiEventChecker do
  let!(:company) { create(:company) }
  subject { ApiEventChecker.new(event).check }
  before do
    allow_any_instance_of(Logs::ApiEvent).to receive(:check_event).and_return(true)
  end

  context "with no previous events" do
    let(:event) { create(:api_event, company: company) }

    it "should send an error notification email" do
      expect(ErrorNotificationMailer).to receive(:send_error).with(event)
      subject
    end

    it "should create an error notification" do
      subject
      expect(ErrorNotification.count).to eq(1)
      notification = ErrorNotification.first
      expect(notification.fallible).to eq(event)
    end
  end

  context "with previous error events" do
    let!(:event2) { create(:api_event, company: company) }
    let(:event) { create(:api_event, company: company) }

    it "should not send an error notification email" do
      expect(ErrorNotificationMailer).not_to receive(:send_error).with(event)
      subject
    end

    it "should not create an error notification" do
      subject
      expect(ErrorNotification.count).to eq(0)
    end
  end

  context "with previous successful events" do
    let!(:event2) { create(:api_event, status: :success, company: company) }
    let(:event) { create(:api_event, company: company) }

    it "should not send an error notification email" do
      expect(ErrorNotificationMailer).to receive(:send_error).with(event)
      subject
    end

    it "should create an error notification" do
      subject
      expect(ErrorNotification.count).to eq(1)
      notification = ErrorNotification.first
      expect(notification.fallible).to eq(event)
    end
  end

  context "with previous error events that are old" do
    let!(:event1) do
      e = create(:api_event, status: :success, company: company)
      e.created_at = 15.days.ago
      e.save!
      e
    end

    let!(:event2) do
      e = create(:api_event, company: company)
      e.created_at = 15.days.ago
      e.save!
      e
    end

    let(:event) { create(:api_event, company: company) }

    it "should send an error notification email" do
      expect(ErrorNotificationMailer).to receive(:send_error).with(event)
      subject
    end

    it "should create an error notification" do
      subject
      expect(ErrorNotification.count).to eq(1)
      notification = ErrorNotification.first
      expect(notification.fallible).to eq(event)
    end
  end
end