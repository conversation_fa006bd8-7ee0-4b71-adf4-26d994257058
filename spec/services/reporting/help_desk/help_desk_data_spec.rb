require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper
include LocationHelper

describe Reporting::HelpDesk::HelpDeskData do
  create_company_and_user

  let(:workspace) { company.workspaces.first }
  let(:data_params)  {
    {
      module_name: "help_tickets",
      options: [
        { "filtered_field_name": "priority_0", "field_name": "priority", "name": "priority", "description": "Priority", "active": false },
        { "filtered_field_name": "followers_1", "field_name": "followers", "name": "people_list", "description": "Followers", "active": false },
        { "filtered_field_name": "impacted_devices_2", "field_name": "impacted_devices", "name": "asset_list", "description": "Impacted Devices", "active": false },
        { "filtered_field_name": "location_3", "field_name": "location", "name": "location_list", "description": "Location", "active": false },
        { "filtered_field_name": "description_4", "field_name": "description", "name": "rich_text", "description": "Description", "active": false },
        { "filtered_field_name": "attachments_5", "field_name": "attachments", "name": "attachment", "description": "Attachments Count", "active": false },
        { "filtered_field_name": "list_6", "field_name": "list", "name": "list", "description": "List", "active": false },
        { "filtered_field_name": "external_user_7", "field_name": "external_user", "name": "external_user", "description": "External User", "active": false },
        { "filtered_field_name": "telecom_8", "field_name": "telecom", "name": "telecom_list", "description": "Telecom", "active": false },
        { "filtered_field_name": "staff_9", "field_name": "staff", "name": "people_list", "description": "Staff", "active": false },
        { "filtered_field_name": "static_text_10", "field_name": "static_text", "name": "static_text", "description": "Static Text", "active": false },
        { "filtered_field_name": "text_11", "field_name": "text", "name": "text", "description": "Text", "active": false },
        { "filtered_field_name": "text_area_12", "field_name": "text_area", "name": "text_area", "description": "Text Area", "active": false },
        { "filtered_field_name": "rich_text_13", "field_name": "rich_text", "name": "rich_text", "description": "Rich Text", "active": false },
        { "filtered_field_name": "number_14", "field_name": "number", "name": "number", "description": "Number", "active": false },
        { "filtered_field_name": "attachment_15", "field_name": "attachment", "name": "attachment", "description": "Attachment Count", "active": false },
        { "filtered_field_name": "date_16", "field_name": "date", "name": "date", "description": "Date", "active": false },
        { "filtered_field_name": "phone_17", "field_name": "phone", "name": "phone", "description": "Phone", "active": false },
        { "filtered_field_name": "category_18", "field_name": "category", "name": "category", "description": "Category", "active": false },
        { "filtered_field_name": "tag_19", "field_name": "tag", "name": "tag", "description": "Tag", "active": false },
        { "filtered_field_name": "asset_20", "field_name": "asset", "name": "asset_list", "description": "Asset", "active": false },
        { "filtered_field_name": "contract_21", "field_name": "contract", "name": "contract_list", "description": "Contract", "active": false },
        { "filtered_field_name": "vendor_22", "field_name": "vendor", "name": "vendor_list", "description": "Vendor", "active": false },
        { "filtered_field_name": "ticket_number_23", "field_name": "ticket_number", "name": "ticket_number", "description": "Help Ticket Number", "active": false },
        { "filtered_field_name": "comment_count_24", "field_name": "comment_count", "name": "comment_count", "description": "Comment Count", "active": false },
        { "filtered_field_name": "created_at_25", "field_name": "created_at", "name": "created_at", "description": "Date Created", "active": false },
        { "filtered_field_name": "date_opened_26", "field_name": "date_opened", "name": "date_opened", "description": "Date Opened", "active": false },
        { "filtered_field_name": "date_closed_27", "field_name": "date_closed", "name": "date_closed", "description": "Date Closed", "active": false },
        { "filtered_field_name": "days_opened_28", "field_name": "days_opened", "name": "days_opened", "description": "Days Opened", "active": false },
        { "filtered_field_name": "total_time_spent_29", "field_name": "total_time_spent", "name": "total_time_spent", "description": "Total Time Spent", "active": false },
        { "filtered_field_name": "survey_result_30", "field_name": "survey_result", "name": "survey_result", "description": "Survey Result", "active": false },
        { "filtered_field_name": "survey_comment_31", "field_name": "survey_comment", "name": "survey_comment", "description": "Survey Comment", "active": false },
        { "filtered_field_name": "related_items_32", "field_name": "related_items", "name": "related_items", "description": "Related Items", "active": false },
        { "filtered_field_name": "days_to_start_33", "field_name": "days_to_start", "name": "days_to_start", "description": "Days to Start", "active": false },
        { "filtered_field_name": "subject_34", "field_name": "subject", "name": "text", "description": "Subject", "active": false },
        { "filtered_field_name": "created_by_35", "field_name": "created_by", "name": "external_user", "description": "Created By", "active": false },
        { "filtered_field_name": "status_36", "field_name": "status", "name": "status", "description": "Status", "active": false },
        { "filtered_field_name": "assigned_to_37", "field_name": "assigned_to", "name": "people_list", "description": "Assigned To", "active": false }
      ],
      custom_form_ids: [company.custom_forms.find_by(company_module: "helpdesk").id],
      report_time_frame: { "timeframe_selection": "all_time", "start_date": "Fri Nov 30 2018 20:18:53 GMT+0500 (Pakistan Standard Time)", "end_date": "Tue Feb 22 2022 12:36:26 GMT+0500 (Pakistan Standard Time)" },
      report_format: "csv"
    }
  }

  let(:options) {
    {
      company_id: company.id,
      company_module: "helpdesk",
      class: "HelpTicket",
      column_options: data_params[:options],
      time_frame: data_params[:report_time_frame],
      custom_form_ids: data_params[:custom_form_ids]
    }
  }

  subject { described_class.new(options) }

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
        'Assigned To' => company_user.contributor_id,
        'Followers' => company_user.contributor_id,
        'Status' => 'In Progress',
        'Priority' => "low",
        'Description' => "What a great day!",
        workspace: workspace
      },
      {
        'Subject' => "Boom boom",
        'Created By' => company_user.contributor_id,
        'Assigned To' => company_user.contributor_id,
        'Followers' => nil,
        'Impacted Devices' => nil,
        'Status' => 'Open',
        'Priority' => "medium",
        'Description' => "The red robin sits in the tree.",
        workspace: workspace
      },
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
        'Assigned To' => company_user.contributor_id,
        'Followers' => company_user.contributor_id,
        'Impacted Devices' => nil,
        'Status' => 'In Progress',
        'Priority' => "low",
        'Description' => "What a great day!",
        workspace: workspace
      },
      {
        'Subject' => "Test Ticket",
        'Created By' => company_user.contributor_id.to_s,
        'Assigned To' => nil,
        'Status' => 'Open',
        'Priority' => "low",
        'Description' => "This is a test ticket.",
        workspace: workspace
      },{
        'Subject' => "First Ticket",
        'Created By' => company_user.contributor_id,
        'Assigned To' => company_user.contributor_id,
        'Followers' => company_user.contributor_id,
        'Impacted Devices' => nil,
        'Status' => 'In Progress',
        'Priority' => "low",
        'Description' => "What a great day!",
        workspace: workspace
      },
      {
        'Subject' => "Second Ticket",
        'Created By' => company_user.contributor_id,
        'Assigned To' => nil,
        'Followers' => nil,
        'Impacted Devices' => nil,
        'Status' => 'Open',
        'Priority' => "medium",
        'Description' => "The red robin sits in the tree.",
        workspace: workspace
      },
      {
        'Subject' => "Third Ticket",
        'Created By' => "<EMAIL>",
        'Assigned To' => company_user.contributor_id,
        'Followers' => company_user.contributor_id,
        'Impacted Devices' => nil,
        'Status' => 'In Progress',
        'Priority' => "low",
        'Description' => "What a great day!",
        workspace: workspace
      },
      {
        'Subject' => "Fourth Ticket",
        'Created By' => company_user.contributor_id,
        'Assigned To' => company_user.contributor_id,
        'Followers' => nil,
        'Impacted Devices' => nil,
        'Status' => 'Open',
        'Priority' => "medium",
        'Description' => "The red robin sits in the tree.",
        workspace: workspace
      },
      {
        'Subject' => "Fifth Ticket",
        'Created By' => "<EMAIL>",
        'Assigned To' => nil,
        'Followers' => nil,
        'Impacted Devices' => nil,
        'Status' => 'Open',
        'Priority' => "medium",
        'Description' => "The red robin sits in the tree.",
        workspace: workspace
      }
    ]
  }

  describe "call help desk data" do
    context "for tickets" do
      let!(:help_tickets) do
        HelpTicket.destroy_all
        help_ticket_params.map{ |p| create_ticket(p) }
      end

      let(:results) { subject.call }
      it 'it will return nine tickets' do
        expect(results).to be_present
        expect(results.size).to eq(9)
      end
    end

    context "for tickets subjects" do
      let!(:help_tickets) do
        HelpTicket.destroy_all
        help_ticket_params.map{ |p| create_ticket(p) }
      end

      let(:results) { subject.call }

      let(:expected_subjects) {
        [
          "Test Ticket",
          "Greatest Moment",
          "Boom boom",
          "First Ticket",
          "Second Ticket",
          "Third Ticket",
          "Fourth Ticket",
          "Fifth Ticket"
        ]
      }
      it 'it will returns subjects' do
        expect(results).to be_present
        results.map{ |c, i| expect(expected_subjects.include? i["subject"])}
      end
    end

    context "for tickets comment" do
      let!(:help_tickets) do
        HelpTicket.destroy_all
      end

      let!(:help_ticket1) { 
        create_ticket(
          'Subject' => "Whoa!!",
          'Priority' => 'low',
          'Status' => 'Open',
          workspace: workspace
        )
      }
      let!(:help_ticket_comment1) { FactoryBot.create(:help_ticket_comment, help_ticket: help_ticket1) }
      let!(:help_ticket_comment2) { FactoryBot.create(:help_ticket_comment, help_ticket: help_ticket1) }

      let(:results) { subject.call }
      it 'return ticket comment count' do
        expect(results).to be_present
        expect(results.first[1]["comment_count"]).to eq(2)
      end
    end

    context "for priority" do
      let(:help_ticket_params) {
        [
          {
            'Subject' => "Test Ticket",
            'Created By' => "<EMAIL>",
            'Assigned To' => company_user.contributor_id,
            'Followers' => company_user.contributor_id,
            'Impacted Devices' => nil,
            'Status' => 'In Progress',
            'Priority' => "low",
            'Description' => "What a great day!",
            workspace: workspace
          }
        ]
      }

      let!(:help_tickets) do
        HelpTicket.destroy_all
        help_ticket_params.map{ |p| create_ticket(p) }
      end

      let(:results) { subject.call }
      it 'it will return ticket priority' do
        expect(results).to be_present
        expect(results.first[1]["priority"]).to eq("low")
      end
    end

    context "for follower" do
      let(:form) { workspace.custom_forms.helpdesk.first }
      let!(:help_tickets) do
        HelpTicket.destroy_all
        help_ticket_params.map{ |p| create_ticket(p) }
      end
      let(:help_ticket_params) {
        [
          {
            'Subject' => "Test Ticket",
            'Created By' => "<EMAIL>",
            'Assigned To' => company_user.contributor_id,
            'Followers' => company_user.contributor_id,
            'Impacted Devices' => nil,
            'Status' => 'In Progress',
            'Priority' => "low",
            'Description' => "What a great day!",
            workspace: workspace
          }
        ]
      }
      let(:results) { subject.call }

      it 'it will return follower' do
        expect(results).to be_present
        followers_info = results.first[1]["followers"].split(' ')
        expect(followers_info[0]).to eq(company_user.first_name)
        expect(followers_info[1]).to eq(company_user.last_name)
        expect(followers_info[2]).to eq(company_user.email)
      end
    end

    context "for impacted device" do
      let(:managed_asset) { create(:managed_asset, company: company) }

      let(:help_ticket_params) {
        [
          {
            'Subject' => "Test Ticket",
            'Created By' => "<EMAIL>",
            'Assigned To' => company_user.contributor_id,
            'Followers' => company_user.contributor_id,
            'Impacted Devices' => managed_asset.id,
            'Status' => 'In Progress',
            'Priority' => "low",
            'Description' => "What a great day!",
            workspace: workspace
          }
        ]
      }

      let!(:help_tickets) do
        HelpTicket.destroy_all
        help_ticket_params.map{ |p| create_ticket(p) }
      end

      let(:results) { subject.call }
      it 'it will return impacted device' do
        expect(results).to be_present
        expect(results.first[1]["impacted_devices"]).to eq(managed_asset.name)
      end
    end

    context "for location" do
      let(:form) { workspace.custom_forms.helpdesk.first }
      let(:location1) { 
        create_location(
          "Name" => "XYZ",
          company: company
        )
      }
      let(:ticket) {
        create_ticket(
          'Subject' =>  "Big ticket",
          'Status' => 'Open',
          'Priority' => 'low',
          "Created By" => company_user.contributor_id,
          workspace: workspace
        )
      }
      let(:location_field) { 
        CustomFormField.create!(name: "location", field_attribute_type: :location_list, label: "Location", custom_form: form)
      }
      let(:location_value) {
        CustomFormValue.create(module: ticket, 
                               custom_form_field: location_field, 
                               value_int: location1.id)
      }
      let(:results) { subject.call }

      it 'it will return location' do
        expect(location_value).to be_present
        expect(results).to be_present
        expect(results.first[1]["location"]).to eq("XYZ")
      end
    end

    context "for description" do
      let(:help_ticket_params) {
        [
          {
            'Subject' => "Test Ticket",
            'Created By' => "<EMAIL>",
            'Assigned To' => company_user.contributor_id,
            'Followers' => company_user.contributor_id,
            'Status' => 'In Progress',
            'Priority' => "low",
            'Description' => "What a great day!",
            workspace: workspace
          }
        ]
      }

      let!(:help_tickets) do
        HelpTicket.destroy_all
        help_ticket_params.map{ |p| create_ticket(p) }
      end

      let(:results) { subject.call }

      it 'it will return description' do
        expect(results).to be_present
        expect(results.first[1]["description"]).to eq("What a great day!")
      end
    end

    context "for external user" do
      let!(:company_user_1) { FactoryBot.create(:company_user) }

      let!(:help_tickets) do
        company.custom_forms.find_by(company_module: 'helpdesk').custom_form_fields.create(
          custom_form_id: company.custom_forms.first.id,
          field_attribute_type: 'external_user',
          label: 'External User',
          name: 'external_user',
          required: false,
          order_position:company.custom_forms.first.custom_form_fields.order('order_position asc').last.order_position + 1
        )
        HelpTicket.destroy_all
        [
          {
            'Subject' => "Test Ticket",
            'Created By' => "<EMAIL>",
            'External User' => company_user_1.contributor_id,
            'Status' => 'In Progress',
            'Priority' => "low",
            'Description' => "What a great day!",
            workspace: workspace
          }
        ].map { |p| create_ticket(p) }
      end

      let(:results) { subject.call }
      it 'it will return external user' do
        expect(results).to be_present
        expect(results.first[1]["external_user"]).to eq(company_user_1.name)
      end
    end

    describe "for telecom" do
      let!(:telecom_provider) { FactoryBot.create(:telecom_provider, company: company) }
      let!(:telecom_1) { FactoryBot.create(:telecom_service, service_type: "data_service", telecom_provider: telecom_provider, company: company)}
      let!(:telecom_2) { FactoryBot.create(:telecom_service, service_type: "data_service", telecom_provider: telecom_provider, company: company)}

      let!(:help_tickets) do
        company.custom_forms.find_by(company_module: 'helpdesk').custom_form_fields.create(
          custom_form_id: company.custom_forms.first.id,
          field_attribute_type: 'telecom_list',
          label: 'Telecom',
          name: 'telecom',
          required: false,
          order_position:company.custom_forms.first.custom_form_fields.order('order_position asc').last.order_position + 1
        )
        HelpTicket.destroy_all
        [
          {
            'Subject' => "Test Ticket Telecom",
            'Created By' => "<EMAIL>",
            'Assigned To' => [company_user.contributor_id],
            'Followers' => [company_user.contributor_id],
            'Telecom' => [telecom_1.id],
            'Status' => 'In Progress',
            'Priority' => "low",
            'Description' => "What a great day!",
            workspace: workspace
          }
        ].map { |p| create_ticket(p) }
      end

      context "telecom"  do
        let!(:results) { subject.call }

        it 'it will return telecom provider' do
          expect(results).to be_present
          expect(results.first[1]["telecom"]).to eq(telecom_1.name)
        end
      end
    end

    describe "for staff" do
      let!(:company_user_1) { FactoryBot.create(:company_user) }

      let!(:help_tickets) do
        company.custom_forms.find_by(company_module: 'helpdesk').custom_form_fields.create(
          custom_form_id: company.custom_forms.first.id,
          field_attribute_type: 'people_list',
          label: 'Staff',
          name: 'staff',
          required: false,
          order_position:company.custom_forms.first.custom_form_fields.order('order_position asc').last.order_position + 1
        )
        HelpTicket.destroy_all
        [
          {
            'Subject' => "Test Ticket",
            'Created By' => "<EMAIL>",
            'Staff' => [company_user_1.contributor_id],
            'Status' => 'In Progress',
            'Priority' => "low",
            'Description' => "What a great day!",
            workspace: workspace
          }
        ].map { |p| create_ticket(p) }
      end

      context "staff"  do
        let!(:results) { subject.call }

        it 'it will return staff' do
          expect(results).to be_present
          staff_info = results.first[1]["staff"].split(' ')
          expect(staff_info[0]).to eq(company_user_1.first_name)
          expect(staff_info[1]).to eq(company_user_1.last_name)
          expect(staff_info[2]).to eq(company_user_1.email)
        end
      end
    end

    describe "for text" do
      let!(:help_tickets) do
        company.custom_forms.find_by(company_module: 'helpdesk').custom_form_fields.create(
          custom_form_id: company.custom_forms.first.id,
          field_attribute_type: 'text',
          label: 'Text',
          name: 'text',
          required: false,
          order_position:company.custom_forms.first.custom_form_fields.order('order_position asc').last.order_position + 1
        )
        HelpTicket.destroy_all
        [
          {
            'Subject' => "Test Ticket",
            'Created By' => "<EMAIL>",
            'Text' => "Hello World!",
            'Status' => 'In Progress',
            'Priority' => "low",
            'Description' => "What a great day!",
            workspace: workspace
          }
        ].map { |p| create_ticket(p) }
      end

      context "text"  do
        let!(:results) { subject.call }

        it 'it will return text' do
          expect(results).to be_present
          expect(results.first[1]["text"]).to eq("Hello World!")
        end
      end
    end

    describe "for phone" do
      let!(:help_tickets) do
        company.custom_forms.find_by(company_module: 'helpdesk').custom_form_fields.create(
          custom_form_id: company.custom_forms.first.id,
          field_attribute_type: 'phone',
          label: 'Phone',
          name: 'phone',
          required: false,
          order_position:company.custom_forms.first.custom_form_fields.order('order_position asc').last.order_position + 1
        )
        HelpTicket.destroy_all
        [
          {
            'Subject' => "Test Ticket",
            'Created By' => "<EMAIL>",
            'Phone' => "+922508994335",
            'Status' => 'In Progress',
            'Priority' => "low",
            'Description' => "What a great day!",
            workspace: workspace,
          }
        ].map { |p| create_ticket(p) }
      end

      context "phone"  do
        let!(:results) { subject.call }

        it 'it will return phone' do
          expect(results).to be_present
          expect(results.first[1]["phone"]).to eq("+922508994335")
        end
      end
    end

    # TODO: FLAKY TEST CASE EXPECTED 3 GOT 29
    # describe "for date opened and days opened" do
    #   context "date closed and days opened"  do
    #     let(:help_ticket_2) do
    #       HelpTicket.destroy_all

    #       create_ticket({
    #         'Subject' => "Test Ticket 1",
    #         'Created By' => "<EMAIL>",
    #         'Status' => 'Open',
    #         'Priority' => "low",
    #         'Description' => "What a great day!",
    #         'created_at' => Time.zone.now.to_date - 3.days,
    #         workspace: workspace,
    #       })
    #     end

    #     let(:results) { subject.call }

    #     it 'it will return date opened and how many days it is opened' do
    #       help_ticket_2
    #       expect(results).to be_present

    #       result = results.values.find { |h| h["id"] == help_ticket_2.id }

    #       expect(result).to be_present
    #       expect(result['days_opened']).to eq(3)
    #       expect(result['date_opened'].to_date).to eq(Time.zone.now.to_date)
    #     end
    #   end
    # end

    describe "for time spents" do
      subject { described_class.new(options) }

      before do
        help_ticket.time_spents.create(started_at: 1.day.ago)
      end

      let(:time_spent_option) {
        {
          "timeframe_selection": "time_spent",
          "start_date": 2.months.ago,
          "end_date": 1.month.from_now
        }
      }

      let(:options) {
        {
          company_id: company.id,
          company_module: "helpdesk",
          class: "HelpTicket",
          column_options: data_params[:options],
          time_frame: time_spent_option,
          custom_form_ids: data_params[:custom_form_ids]
        }
      }

      let(:help_ticket) {
        create_ticket(
          'Subject' => "Whoa!!",
          'Priority' => 'low',
          'Status' => 'Open',
          workspace: workspace
        )
      }

      let(:results) { subject.call }
      it 'it will return 1 ticket' do
        expect(results).to be_present
        expect(results.size).to eq(1)
      end
    end
  end
end
