# frozen_string_literal: true

require 'rails_helper'

describe 'creates a managed asset' do
  let!(:company_user) { FactoryBot.create(:company_user) }
  let!(:company) { company_user.company }
  let!(:assigned_to) { nil }
  let(:display_name) { 'DESKTOP-MLNFTHJ' }
  let(:mac_addresses) { ["60:45:CB:9A:58:1F"] }
  let(:asset_type) { 'null' }

  let(:hardware_detail) do
    ComputerDetail.new(
        'processor' => 'AMD Ryzen 7 1700X Eight-Core Processor  8 Cores',
        'memory' => '32 GB',
        'hard_drive' => '932 GB',
        'serial_number' => 'CDN1234R'
    )
  end

  let(:asset_software) do
    AssetSoftware.new(
      'name' => 'Microsoft Windows 10 Home',
      'software_type' => 'operating_system',
      'product_key' => '00326-10870-80315-AA643',
      'install_date' => '2018/05/27-20:05:22'
    )
  end

  let(:agent_location) do
    AgentLocation.new(
      company_id: company.id,
      computer_name: "#{display_name}",
      mac_addresses: ["60:45:CB:9A:58:1F"],
      ip_address: "************",
      app_version: SecureRandom.uuid,
      last_scanned_at: DateTime.now,
      system_uuid: SecureRandom.uuid,
      last_active: Time.current
    )
  end

  let(:agent_location2) do
    AgentLocation.new(
      company_id: company.id,
      computer_name: "asset-2",
      mac_addresses: ["61:45:CB:9A:60:2F"],
      ip_address: "************",
      app_version: SecureRandom.uuid,
      last_scanned_at: DateTime.now,
      system_uuid: SecureRandom.uuid,
      last_active: Time.current
    )
  end

  let(:json_payload) do
    json_string = <<END
    {
      "managedAsset": {
        "companyUserGuid": "#{company_user.guid}",
        "companyGuid": "#{company.guid}",
        "assignedTo": "#{assigned_to}",
        "macAddresses": ["60:45:CB:9A:58:1F"],
        "company": null,
        "assetType": #{asset_type},
        "processorCores": "8 Cores",
        "processorName": "AMD Ryzen 7 1700X Eight-Core Processor ",
        "memory": "32 GB",
        "osName": "Microsoft Windows 10 Home",
        "osVersion": "10.0.17134",
        "osSerialNo": "00326-10870-80315-AA643",
        "ipAddress": "************",
        "windowProductKey": null,
        "windowsInstallationDate": "2018/05/27-20:05:22",
        "diskSpace": "932 GB",
        "manufacturer": "System manufacturer",
        "model": "System Product Name",
        "displayName": "#{display_name}",
        "pcSerialNo": "CDN1234R",
        "deviceType": "Desktop",
        "applications": [{"displayName": "sample", "installDate": "2018-04-23"}]
      }
    }
END
    JSON.parse(json_string)
  end

  subject { ManagedAssetService.new(json_payload) }

  context 'with an assigned to' do
    let(:assigned_to) { company_user.guid }

    context 'with an existing managed asset' do
      let!(:managed_asset) do
        desktop = CompanyAssetType.find_by(name: 'Desktop')
        asset = company.managed_assets.create(name: display_name, company_asset_type_id: desktop.id)
        asset
      end

      it 'should update the existing asset' do
        company.managed_assets.reload
        company.managed_assets.destroy_all
        subject.update(agent_location)
        managed_asset = company.managed_assets.find_by(name: display_name)
        expect(managed_asset).to be_present
        expect(managed_asset.assignment_information.user&.company_user&.guid).to eq(assigned_to)
        expect(managed_asset.hardware_detail).to be_present
        expect(managed_asset.hardware_detail.processor).to eq('AMD Ryzen 7 1700X Eight-Core Processor  8 Cores')
        expect(managed_asset.hardware_detail.memory).to eq('32 GB')
        expect(managed_asset.hardware_detail.hard_drive).to eq('932 GB')
        expect(managed_asset.asset_softwares.first).to be_present
      end
    end

    context 'without an existing managed asset' do
      it 'should create a new asset' do
        company.managed_assets.reload
        company.managed_assets.destroy_all
        subject.update(agent_location)
        managed_asset = company.managed_assets.find_by(name: display_name)
        expect(managed_asset).to be_present
        expect(managed_asset.assignment_information.user&.company_user&.guid).to eq(assigned_to)
        expect(managed_asset.assignment_information.manager&.company_user&.guid).to be_nil
        expect(managed_asset.hardware_detail).to be_present
        expect(managed_asset.hardware_detail.processor).to eq('AMD Ryzen 7 1700X Eight-Core Processor  8 Cores')
        expect(managed_asset.hardware_detail.memory).to eq('32 GB')
        expect(managed_asset.hardware_detail.hard_drive).to eq('932 GB')
        expect(managed_asset.asset_softwares.first).to be_present
      end
    end
  end

  context 'with an assigned to' do
    context 'with an existing managed asset' do
      let!(:managed_asset) do
        company.managed_assets.reload
        company.managed_assets.destroy_all
        desktop = CompanyAssetType.find_by(name: 'Desktop')
        asset = company.managed_assets.create(name: display_name, company_asset_type_id: desktop.id, mac_addresses: mac_addresses, machine_serial_number: "CDN1234R")
        asset
      end

      it 'should update the existing asset' do
        subject.update(agent_location)
        expected = managed_asset.reload
        expect(company.managed_assets.where(name: display_name).count).to eq(1)
        expect(expected.hardware_detail.processor).to eq('AMD Ryzen 7 1700X Eight-Core Processor  8 Cores')
        expect(expected.hardware_detail.memory).to eq('32 GB')
        expect(expected.hardware_detail.hard_drive).to eq('932 GB')
        expect(expected.asset_softwares.first).to be_present
      end
    end

    context 'without an existing managed asset' do
      it 'should create a new asset' do
        company.managed_assets.reload
        company.managed_assets.destroy_all
        subject.update(agent_location)
        expect(company.managed_assets.where(name: display_name).count).to eq(1)
      end
    end
  end

  context 'with an assigned to' do
    let(:other) { CompanyAssetType.find_by(name: 'Other') }
    let(:display_name) { 'Other Asset' }
    let(:asset_type) { "\"#{other.name}\"" }

    context 'with an existing managed asset' do
      let!(:managed_asset) do
        company.managed_assets.reload
        company.managed_assets.destroy_all
        asset = company.managed_assets.create(name: 'Big Asset', company_asset_type_id: other.id)
        asset
      end

      it 'should create a new asset' do
        subject.update(agent_location)
        company.managed_assets.reload
        expected = company.managed_assets.find_by(name: display_name)
        expect(company.managed_assets.where(name: display_name).count).to eq(1)
        expect(company.managed_assets.count).to eq(2)

        expect(expected.asset_softwares.first).to be_present
      end
    end

    context 'without an existing managed asset' do
      it 'should create a new asset' do
        company.managed_assets.reload
        company.managed_assets.destroy_all
        subject.update(agent_location)
        expect(company.managed_assets.where(name: display_name).count).to eq(1)
      end
    end
  end

  context 'without a company guid' do
    let(:json_payload_without_company_guid) do
      json_string_without_company_guid = <<END
    {
      "managedAsset": {
        "companyUserGuid": "#{company_user.guid}",
        "companyGuid": "",
        "assignedTo": "#{assigned_to}",
        "macAddresses": ["60:45:CB:9A:58:1F"],
        "company": null,
        "assetType": null,
        "processorCores": "8 Cores",
        "processorName": "AMD Ryzen 7 1700X Eight-Core Processor ",
        "memory": "32 GB",
        "osName": "Microsoft Windows 10 Home",
        "osVersion": "10.0.17134",
        "osSerialNo": "00326-10870-80315-AA643",
        "ipAddress": "************",
        "windowProductKey": null,
        "windowsInstallationDate": "2018/05/27-20:05:22",
        "diskSpace": "932 GB",
        "manufacturer": "System manufacturer",
        "model": "System Product Name",
        "displayName": "#{display_name}",
        "pcSerialNo": "CDN1234R",
        "deviceType": "Desktop"
      }
    }
END
      JSON.parse(json_string_without_company_guid)
    end
    subject { ManagedAssetService.new(json_payload_without_company_guid) }

    it 'should not update the managed assets' do
      expect { subject.update(agent_location) }.to raise_error "MissingCompanyError"
    end
  end

  context 'managed assets have serial number' do
    let(:json_payload1) do
      json_string = <<END
      {
        "managedAsset": {
          "companyUserGuid": "#{company_user.guid}",
          "companyGuid": "#{company.guid}",
          "macAddresses": ["60:45:CB:9A:58:1F"],
          "assetType": #{asset_type},
          "processorCores": "8 Cores",
          "memory": "16 GB",
          "displayName": "#{display_name}",
          "pcSerialNo": "NC1D3R2X",
          "deviceType": "Desktop"
        }
      }
END
      JSON.parse(json_string)
    end

    let(:json_payload2) do
      json_string = <<END
      {
        "managedAsset": {
          "companyUserGuid": "#{company_user.guid}",
          "companyGuid": "#{company.guid}",
          "macAddresses": ["60:45:CB:9A:58:1F"],
          "assetType": #{asset_type},
          "processorCores": "8 Cores",
          "memory": "32 GB",
          "displayName": "#{display_name}",
          "pcSerialNo": "NC1D3R2X",
          "deviceType": "Desktop"
        }
      }
END
      JSON.parse(json_string)
    end

    let(:json_payload3) do
      json_string = <<END
      {
        "managedAsset": {
          "companyUserGuid": "#{company_user.guid}",
          "companyGuid": "#{company.guid}",
          "macAddresses": ["61:45:CB:9A:60:2F"],
          "assetType": #{asset_type},
          "processorCores": "4 Cores",
          "memory": "64 GB",
          "displayName": "asset-2",
          "pcSerialNo": "",
          "deviceType": "Desktop"
        }
      }
END
      JSON.parse(json_string)
    end

    let(:json_payload4) do
      json_string = <<END
      {
        "managedAsset": {
          "companyUserGuid": "#{company_user.guid}",
          "companyGuid": "#{company.guid}",
          "macAddresses": ["61:45:CB:9A:60:2F"],
          "assetType": #{asset_type},
          "processorCores": "4 Cores",
          "memory": "64 GB",
          "displayName": "#{display_name}",
          "pcSerialNo": "D33RTR2XW",
          "deviceType": "Desktop"
        }
      }
END
      JSON.parse(json_string)
    end

    let(:json_payload5) do
      json_string = <<END
      {
        "managedAsset": {
          "companyUserGuid": "#{company_user.guid}",
          "companyGuid": "#{company.guid}",
          "macAddresses": ["61:45:CB:9A:60:2F"],
          "assetType": #{asset_type},
          "processorCores": "8 Cores",
          "memory": "64 GB",
          "displayName": "#{display_name}",
          "pcSerialNo": "XWE54RZ",
          "deviceType": "Desktop"
        }
      }
END
      JSON.parse(json_string)
    end
    subject(:sub1)  { ManagedAssetService.new(json_payload1) }
    subject(:sub2)  { ManagedAssetService.new(json_payload2) }
    subject(:sub3)  { ManagedAssetService.new(json_payload3) }
    subject(:sub4)  { ManagedAssetService.new(json_payload4) }
    subject(:sub5)  { ManagedAssetService.new(json_payload5) }

    it 'should create three managed assets' do
      company.managed_assets.reload
      company.managed_assets.destroy_all
      sub1.update(agent_location)
      expect(company.managed_assets.count).to eq(1)
    
      sub2.update(agent_location)
      company.managed_assets.reload
      expect(company.managed_assets.count).to eq(1)
      expect(company.managed_assets.first.hardware_detail.memory).to eq("32 GB")

      sub3.update(agent_location2)
      sub4.update(agent_location)
      company.managed_assets.reload
      expect(company.managed_assets.count).to eq(2)
      expect(company.managed_assets.find_by(machine_serial_number: "D33RTR2XW").machine_serial_number).to eq("D33RTR2XW")

      sub5.update(agent_location)
      company.managed_assets.reload
      expect(company.managed_assets.count).to eq(3)
      expect(company.managed_assets.find_by(machine_serial_number: "XWE54RZ").machine_serial_number).to eq("XWE54RZ")
    end
  end

  context 'managed assets have mac addresses but did not have serial number' do
    let(:json_payload1) do
      json_string = <<END
      {
        "managedAsset": {
          "companyUserGuid": "#{company_user.guid}",
          "companyGuid": "#{company.guid}",
          "macAddresses": ["60:45:CB:9A:58:1F", "62:45:CB:9A:58:2F", "63:45:CB:9A:58:3F"],
          "assetType": #{asset_type},
          "processorCores": "4 Cores",
          "memory": "8 GB",
          "displayName": "#{display_name}",
          "pcSerialNo": "",
          "deviceType": "Desktop"
        }
      }
END
      JSON.parse(json_string)
    end

    let(:json_payload2) do
      json_string = <<END
      {
        "managedAsset": {
          "companyUserGuid": "#{company_user.guid}",
          "companyGuid": "#{company.guid}",
          "macAddresses": ["60:45:CB:9A:58:1F", "62:45:CB:9A:58:2F", "63:45:CB:9A:58:3F"],
          "assetType": #{asset_type},
          "processorCores": "8 Cores",
          "memory": "16 GB",
          "displayName": "#{display_name}",
          "pcSerialNo": null,
          "deviceType": "Desktop"
        }
      }
END
      JSON.parse(json_string)
    end

    let(:json_payload3) do
      json_string = <<END
      {
        "managedAsset": {
          "companyUserGuid": "#{company_user.guid}",
          "companyGuid": "#{company.guid}",
          "macAddresses": ["62:45:CD:6A:58:2F", "63:54:CD:6A:58:2F"],
          "assetType": #{asset_type},
          "processorCores": "4 Cores",
          "memory": "32 GB",
          "displayName": "asset-2",
          "pcSerialNo": "",
          "deviceType": "Desktop"
        }
      }
END
      JSON.parse(json_string)
    end

    let(:json_payload4) do
      json_string = <<END
      {
        "managedAsset": {
          "companyUserGuid": "#{company_user.guid}",
          "companyGuid": "#{company.guid}",
          "macAddresses": ["63:54:CD:6A:58:2F"],
          "assetType": #{asset_type},
          "processorCores": "4 Cores",
          "memory": "64 GB",
          "displayName": "#{display_name}",
          "pcSerialNo": "",
          "deviceType": "Desktop"
        }
      }
END
      JSON.parse(json_string)
    end

    let(:json_payload5) do
      json_string = <<END
      {
        "managedAsset": {
          "companyUserGuid": "#{company_user.guid}",
          "companyGuid": "#{company.guid}",
          "macAddresses": ["61:45:86:9A:60:2F", "63:45:CB:9A:87:2F", "64:45:CB:9A:60:88"],
          "assetType": #{asset_type},
          "processorCores": "8 Cores",
          "memory": "64 GB",
          "displayName": "#{display_name}",
          "pcSerialNo": "XWE765Z",
          "deviceType": "Desktop",
          "manufacturer": "Dell"
        }
      }
END
      JSON.parse(json_string)
    end

    let(:json_payload6) do
      json_string = <<END
      {
        "managedAsset": {
          "companyUserGuid": "#{company_user.guid}",
          "companyGuid": "#{company.guid}",
          "macAddresses": ["61:45:86:9A:60:2F", "63:45:CB:9A:87:2F"],
          "assetType": #{asset_type},
          "processorCores": "8 Cores",
          "memory": "64 GB",
          "displayName": "#{display_name}",
          "pcSerialNo": "",
          "deviceType": "Desktop",
          "manufacturer": "Dell"
        }
      }
END
      JSON.parse(json_string)
    end

    let(:json_payload7) do
      json_string = <<END
      {
        "managedAsset": {
          "companyUserGuid": "#{company_user.guid}",
          "companyGuid": "#{company.guid}",
          "macAddresses": [],
          "assetType": #{asset_type},
          "processorCores": "8 Cores",
          "memory": "64 GB",
          "displayName": "NX-123",
          "pcSerialNo": "",
          "deviceType": "Desktop",
          "manufacturer": "Dell"
        }
      }
END
      JSON.parse(json_string)
    end

    let(:json_payload8) do
      json_string = <<END
      {
        "managedAsset": {
          "companyUserGuid": "#{company_user.guid}",
          "companyGuid": "#{company.guid}",
          "macAddresses": ["65:45:86:9A:60:4F"],
          "assetType": #{asset_type},
          "processorCores": "8 Cores",
          "memory": "64 GB",
          "displayName": "NX-123",
          "pcSerialNo": "",
          "deviceType": "Desktop",
          "manufacturer": "Dell"
        }
      }
END
      JSON.parse(json_string)
    end

    subject(:sub1)  { ManagedAssetService.new(json_payload1) }
    subject(:sub2)  { ManagedAssetService.new(json_payload2) }
    subject(:sub3)  { ManagedAssetService.new(json_payload3) }
    subject(:sub4)  { ManagedAssetService.new(json_payload4) }
    subject(:sub5)  { ManagedAssetService.new(json_payload5) }
    subject(:sub6)  { ManagedAssetService.new(json_payload6) }
    subject(:sub7)  { ManagedAssetService.new(json_payload7) }
    subject(:sub8)  { ManagedAssetService.new(json_payload8) }

    it 'should create three managed assets' do
      company.managed_assets.reload
      company.managed_assets.destroy_all
      sub1.update(agent_location)
      company.managed_assets.reload
      expect(company.managed_assets.count).to eq(1)
      expect(company.managed_assets.first.hardware_detail.memory).to eq("8 GB")

      sub2.update(agent_location)
      company.managed_assets.reload
      expect(company.managed_assets.count).to eq(1)
      expect(company.managed_assets.first.hardware_detail.memory).to eq("16 GB")

      sub3.update(agent_location2)
      company.managed_assets.reload
      expect(company.managed_assets.count).to eq(2)
      expect(company.managed_assets.find_by(mac_addresses: ["62:45:CD:6A:58:2F", "63:54:CD:6A:58:2F"]).hardware_detail.memory).to eq("32 GB")
      
      sub4.update(agent_location)
      company.managed_assets.reload
      expect(company.managed_assets.count).to eq(2)
      expect(company.managed_assets.find_by(mac_addresses: ["63:54:CD:6A:58:2F"]).hardware_detail.memory).to eq("64 GB")

      sub5.update(agent_location2)
      company.managed_assets.reload
      expect(company.managed_assets.count).to eq(3)
      expect(company.managed_assets.find_by(mac_addresses: ["61:45:86:9A:60:2F", "63:45:CB:9A:87:2F", "64:45:CB:9A:60:88"]).mac_addresses.count).to eq(3)

      sub6.update(agent_location2)
      company.managed_assets.reload
      expect(company.managed_assets.count).to eq(3)
      expect(company.managed_assets.find_by(mac_addresses: ["61:45:86:9A:60:2F", "63:45:CB:9A:87:2F"]).mac_addresses.count).to eq(2)
      #find with name
      sub7.update(agent_location2)
      company.managed_assets.reload
      expect(company.managed_assets.count).to eq(4)
      sub7.update(agent_location2)
      company.managed_assets.reload
      expect(company.managed_assets.count).to eq(4)

      sub8.update(agent_location2)
      company.managed_assets.reload
      expect(company.managed_assets.count).to eq(4)
      expect(company.managed_assets.find_by(mac_addresses: ["65:45:86:9A:60:4F"]).mac_addresses.count).to eq(1)
    end
  end
end
