require 'rails_helper'

describe AlertSummaryService do
  let!(:company) { FactoryBot.create(:company) }
  let!(:vendor1) { FactoryBot.create(:vendor, company_id: company.id, category_id: company.categories.first.id, name: "Microsoft", url: "www.microsoft.com") }
  let!(:vendor2) { FactoryBot.create(:vendor, company_id: company.id, category_id: company.categories.second.id, name: "<PERSON><PERSON>", url: "www.gusto.com") }
  let!(:general_transaction1) { GeneralTransaction.create(company: company, vendor: vendor1, is_active: true, amount: 110, transaction_date: Date.today, status: 0) }
  let!(:general_transaction2) { GeneralTransaction.create(company: company, vendor: vendor1, is_active: true, amount: 35, transaction_date: Date.today - 1.month, status: 0) }
  let!(:general_transaction3) { GeneralTransaction.create(company: company, vendor: vendor1, is_active: true, amount: 20, transaction_date: Date.today - 2.months, status: 0) }
  let!(:general_transaction4) { GeneralTransaction.create(company: company, vendor: vendor2, is_active: true, amount: 100, transaction_date: Date.today - 2.months, status: 0) }
  let!(:general_transaction5) { GeneralTransaction.create(company: company, vendor: vendor2, is_active: false, amount: 90, transaction_date: Date.today - 3.months, status: 0) }

  context "get the transaction sums for this month, last month, and 2 months ago for all vendors" do
    it "should get a breakdown for all of the vendors" do
      response = described_class.new(company.id).vendor_map
      expect(response).to eq({vendor2.id=>{"id"=>vendor2.id, "this_month_spending"=>nil, "last_month_spending"=>nil, "two_months_ago_spending"=>100.0}, vendor1.id=>{"id"=>vendor1.id, "this_month_spending"=>110.0, "last_month_spending"=>35.0, "two_months_ago_spending"=>20.0}})
    end
  end

  context "get the transaction sums for this month, last month, and 2 months ago for all vendors" do
    it "should get a breakdown of just the vendor passed in" do
      response = described_class.new(company.id, vendor1.id).vendor_map
      expect(response).to eq({vendor1.id=>{"id"=>vendor1.id, "this_month_spending"=>110.0, "last_month_spending"=>35.0, "two_months_ago_spending"=>20.0}})
    end
  end
end
