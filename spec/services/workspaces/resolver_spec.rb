# frozen_string_literal: true

require 'rails_helper'

describe Workspaces::Resolver do
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: granted_access_at) }
  let(:company) { company_user.company }
  let(:user) { company_user.user }
  let(:granted_access_at) { 1.day.ago }

  subject { described_class.new(company_user2).call.workspaces }

  context "workspaces" do
    context "without a single group member" do
      let(:company_user2) { FactoryBot.create(:company_user, company: company, granted_access_at: granted_access_at) }
      let(:workspace) { company.workspaces.create(name: "Test Workspace") }
      let!(:privilege) { 
        workspace.privileges.create(
          name: 'HelpTicket', 
          permission_type: 'write', 
          contributor: test_group.contributor
        ) 
      }
      let(:test_group) { company.groups.create(name: 'Test Group') }

      it "should pass check" do
        expect(subject).to eq([])
      end
    end

    context "with multiple groups" do
      let(:company_user2) { FactoryBot.create(:company_user, company: company, granted_access_at: granted_access_at) }
      let(:workspace) { company.workspaces.create(name: "Test Workspace") }
      let(:everyone) { company.groups.find_by(name: "Everyone") }
      let!(:privilege) { 
        workspace.privileges.create(
          name: 'HelpTicket', 
          permission_type: 'write', 
          contributor: test_group.contributor
        ) 
      }
      let!(:privilege2) { 
        workspace.privileges.create(
          name: 'HelpTicket', 
          permission_type: 'write', 
          contributor: everyone.contributor
        ) 
      }
      let(:test_group) do 
        g = company.groups.create(name: 'Test Group')
        g.group_members.create(contributor: company_user2.contributor)
        g
      end
      let(:expected_workspaces) { company.workspaces.where(name: ['Test Workspace', 'Everyone']).to_a }

      it "should pass check" do
        expect(subject).to eq(expected_workspaces)
      end
    end

    context "with a single group member" do
      let(:company_user2) { FactoryBot.create(:company_user, company: company, granted_access_at: granted_access_at) }
      let(:workspace) { company.workspaces.create(name: "Test Workspace") }
      let!(:privilege) { 
        workspace.privileges.create(
          name: 'HelpTicket', 
          permission_type: 'write', 
          contributor: test_group.contributor
        ) 
      }
      let(:test_group) do 
        g = company.groups.create(name: 'Test Group')
        g.group_members.create(contributor: company_user2.contributor)
        g
      end
      let(:expected_workspaces) { company.workspaces.where(name: 'Test Workspace').to_a }

      it "should pass check" do
        expect(subject).to eq(expected_workspaces)
      end
    end
  end
end
