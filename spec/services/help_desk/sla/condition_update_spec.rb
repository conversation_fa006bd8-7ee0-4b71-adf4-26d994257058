require 'rails_helper'
include CompanyUserHelper

RSpec.describe Sla::ConditionUpdate do
  create_company_and_user
  let(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "helpdesk", form_name: 'Custom Form 2') }
  let(:policy) { FactoryBot.create(:sla_policy, company: company, custom_form: custom_form, name: 'SLA Policy1') }
  let(:conditions) do
    [
      {
        "condition_type" => "form_field",
        "field_type" => "subject",
        "value" => {"form_value"=> "newone"}
      },
      {
        "condition_type" => "form_field",
        "field_type" => "text_area",
        "value" => {"form_value"=> "newcondition"}
      }
    ]
  end

  describe '#create' do
    it 'creates conditions for the policy' do
      service = described_class.new(policy, conditions, user)

      expect {
        service.create
      }.to change { policy.conditions.count }.by(2)

      condition = policy.conditions.find_by(field_type: "subject")
      expect(condition).to be_present
    end
  end

  describe '#update' do
      let!(:sla_condition) { FactoryBot.create(:sla_condition, sla_policy_id: policy.id, field_type: "tag", value: {"form_value"=>["Business Service"]}) }
      let!(:sla_condition1) { FactoryBot.create(:sla_condition, sla_policy_id: policy.id, field_type: "category", value: {"form_value"=>"Facilities"}) }
      let!(:sla_condition2) { FactoryBot.create(:sla_condition, sla_policy_id: policy.id, field_type: "checkbox", value: {"form_value"=>["new"]}) }

    context 'when custom form changed' do
      it 'destroys all existing conditions and creates new ones' do
        service = described_class.new(policy, conditions, user)

        expect {
          service.update(true)
        }.to change { policy.conditions.count }.from(3).to(2)
      end
    end

    context 'when custom form not changed' do
      it 'destroys specified conditions and updates or creates the remaining ones' do
        removed_ids = [sla_condition.id]
        service = described_class.new(policy, conditions, user)

        expect {
          service.update(false, removed_ids)
        }.to change { policy.conditions.count }.from(3).to(4)

        removed_condition = policy.conditions.find_by(id: removed_ids.first)
        expect(removed_condition).to be_nil
      end
    end
  end
end
