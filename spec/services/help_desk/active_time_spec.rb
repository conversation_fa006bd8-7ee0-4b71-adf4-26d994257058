require 'rails_helper'
include CompanyUserHelper

describe HelpDesk::ActiveTime do
  create_company_and_user

  subject { described_class.new(event_subject) }

  let(:calendar_time) { described_class.new(company, 1000, 'calendar_hours', company.workspaces.first).calculate_time }
  let(:business_time) { described_class.new(company, 1000, 'business_hours', company.workspaces.first).calculate_time }

  describe "#calculate_time" do
    it "outputs time according to calendar hours" do
      expect(calendar_time).to be_present
      expect(calendar_time).to be_within(1.second).of (DateTime.now + 1000.minutes)
    end

    it "outputs time according to business hours" do
      expect(business_time).to be_present
      expect(business_time).to be_within(1.second).of (1000.working.minutes.from_now)
    end
  end
end
