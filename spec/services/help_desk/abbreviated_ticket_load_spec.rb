require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::AbbreviatedTicketLoad do
  create_company_and_user

  let!(:user1) { create(:user, email: "<EMAIL>") }
  let!(:user2) { create(:user, email: "<EMAIL>") }
  let!(:user3) { create(:user, email: "<EMAIL>") }
  let!(:user4) { create(:user, email: "<EMAIL>") }
  let!(:company_user_1) { create(:company_user, user: user1, company: company) }
  let!(:company_user_2) { create(:company_user, user: user2, company: company) }
  let!(:company_user_3) { create(:company_user, user: user3, company: company) }
  let!(:company_user_4) { create(:company_user, user: user4, company: company) }

  let(:custom_form) { company.custom_forms.helpdesk.first }
  let!(:checkbox) {
    checkbox = CustomFormField.new(label: "Checkbox", 
                                   field_attribute_type: "checkbox", 
                                   order_position: 11, 
                                   options: "[\"Desktop\",\"Laptop\",\"Router\"]", 
                                   required: false, 
                                   private: false, 
                                   permit_view_default: true, 
                                   permit_edit_default: true, 
                                   name: "checkbox", 
                                   custom_form_id: custom_form.id
                                  )
    checkbox.field_position = FieldPosition.new(custom_form_field_id: checkbox.id, position: "right")
    custom_form.custom_form_fields << checkbox
    checkbox
  }

  let(:ticket_cols) { 
    [
      {
        "field_name" => "ticket_number",
      },
      {
        "field_name" => "assigned_to",
      },
      {
        "field_name" => "created_by",
      },
      {
        "field_name" => "status",
      },
      {
        "field_name" => "priority",
      },
      {
        "field_name" => "subject",
      },
      {
        "field_name" => "checkbox",
      }
    ]
  }

  let(:default_ticket_columns) {
    [
      {
        "field_name" => "ticket_number",
      },
      {
        "field_name" => "assigned_to",
      },
      {
        "field_name" => "created_by",
      },
      {
        "field_name" => "status",
      },
      {
        "field_name" => "priority",
      },
      {
        "field_name" => "subject",
      }
    ]
  }

  let!(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
        'Assigned To' => company_user_1.contributor_id,
        'Status' => 'In Progress',
        'Priority' => "low",
        'Description' => "What a great day!",
        'Checkbox' => "Desktop"
      },
      {
        'Subject' => "Boom boom",
        'Created By' => company_user.contributor_id,
        'Assigned To' => company_user_2.contributor_id,
        'Status' => 'Open',
        'Priority' => "medium",
        'Description' => "The red robin sits in the tree.",
        'Checkbox' => "Laptop"
      },
      {
        'Subject' => "Second Ticket",
        'Created By' => company_user.contributor_id,
        'Assigned To' => company_user_3.contributor_id,
        'Status' => 'In Progress',
        'Priority' => "low",
        'Description' => "What a great day!",
        'Checkbox' => "Desktop"
      },
      {
        'Subject' => "Test Ticket",
        'Created By' => company_user.contributor_id,
        'Assigned To' => company_user_4.contributor_id,
        'Status' => 'Open',
        'Priority' => "low",
        'Description' => "This is a test ticket.",
        'Checkbox' => "Router"
      }
    ]
  }

  let!(:help_tickets) { help_ticket_params.map{ |p| create_ticket(p) } }

  let(:tickets) { 
    described_class.new(company_user, ticket_cols, default_ticket_columns).call(help_tickets.map{ |t| t.id }, 'ticket_number asc')
  }

  describe "#call" do
    it "will return total tickets count" do
      expect(tickets).to be_present
      expect(tickets.count).to eq(4)
    end

    it "will match all tickets data" do
      tickets.each do |ht|
        help_ticket = help_ticket_params.find { |t| t["Subject"] == ht[:fields][:subject][:value] }
        expect(ht).to be_present
        expect(ht[:fields][:assigned_to].first[:id]).to eq(help_ticket["Assigned To"])
        expect(ht[:fields][:created_by].first[:id]).to eq(help_ticket["Created By"])
        expect(ht[:fields][:status][:value]).to eq(help_ticket["Status"])
        expect(ht[:fields][:priority][:value]).to eq(help_ticket["Priority"])
        expect(ht[:source]).to eq("manually_added")
        expect(ht[:fields][:checkbox][:value]).to eq(help_ticket["Checkbox"])
      end
    end

    it "will exclude faulty tickets" do
      priority_field = CustomFormField.find_by(field_attribute_type: 'priority', label: "Priority", custom_form_id: custom_form.id)
      # Deleting medium option
      priority_field.options = "[{\"name\":\"low\",\"color\":\"#ffd700\"},{\"name\":\"high\",\"color\":\"#ff0000\"}]"
      priority_field.save!

      expect(tickets).to be_present
      expect(tickets.count).to eq(3)
    end

    it "will expect admin user to have all permissions of all custom form fields" do
      tickets.each do |ticket|
        expect(ticket[:can_write]).to be true
        fields = ticket[:fields]
        
        fields.each do |field_name, field_attributes|
          # Skip checkbox case as we don't send perms from service here
          next if field_name == :checkbox  
          if field_attributes.is_a?(Hash)
            expect(field_attributes[:visible]).to be true
            expect(field_attributes[:disabled]).to be false
          # The people_lists have nested perms
          elsif field_attributes.is_a?(Array)
            field_attributes.each do |attribute|
              expect(attribute[:visible]).to be true
              expect(attribute[:disabled]).to be false
            end
          end
        end
      end
    end

    context "With different form field permission" do
      let(:form_fields) do
        custom_form.custom_form_fields
                   .where(name: ticket_cols.map { |col| col["field_name"] })
                   .index_by(&:id)
      end      
      before do
        form_fields.each_with_index do |(field_id, form_field), index|
          case index
          when 0
            #user 1 to have write, user 2 to not see field
            form_field.custom_form_field_permissions.destroy_all
            set_user_permissions(
              form_field_id: form_field.id,
              contributor_id: company_user_1.contributor_id,
              permission_type: "write"
            )
          when 1
            #user 2 to have write, user 1 to not see field
            form_field.custom_form_field_permissions.destroy_all
            set_user_permissions(
              form_field_id: form_field.id,
              contributor_id: company_user_2.contributor_id,
              permission_type: "write"
            )
          when 2
            #user 1 to have write, user 2 to have write
            form_field.custom_form_field_permissions.destroy_all
            set_user_permissions(
              form_field_id: form_field.id,
              contributor_id: company_user_1.contributor_id,
              permission_type: "write"
            )
            set_user_permissions(
              form_field_id: form_field.id,
              contributor_id: company_user_2.contributor_id,
              permission_type: "write"
            )
          when 3
            #user 2 to have read, user 2 to have read
            form_field.custom_form_field_permissions.destroy_all
            set_user_permissions(
              form_field_id: form_field.id,
              contributor_id: company_user_1.contributor_id,
              permission_type: "read"
            )
            set_user_permissions(
              form_field_id: form_field.id,
              contributor_id: company_user_2.contributor_id,
              permission_type: "read"
            )
          when 4
            # Case 4 for everyone group
          end
        end

        [company_user_1, company_user_2].each do |user|
          ExpandedPrivilege.create(
            name: "HelpTicket",
            permission_type: "write",
            workspace_id: workspace.id,
            company_id: company.id,
            contributor_id: user.contributor_id,
            sources: ["Everyone"]
          )
        end
        @user_1_tickets = described_class.new(company_user_1, ticket_cols, default_ticket_columns).call(help_tickets.map{ |t| t.id }, 'ticket_number asc')
        @user_2_tickets = described_class.new(company_user_2, ticket_cols, default_ticket_columns).call(help_tickets.map{ |t| t.id }, 'ticket_number asc')
      end
      it "will expect field 1 user 1 to have write, user 2 to not see field" do
        validate_ticket_permissions(@user_1_tickets, form_fields, 0, is_disabled: false, is_visible: true)
        validate_ticket_permissions(@user_2_tickets, form_fields, 0, is_disabled: true, is_visible: false)
      end

      it "will expect field 2 user 2 to have write, user 1 to not see field" do
        validate_ticket_permissions(@user_2_tickets, form_fields, 1, is_disabled: false, is_visible: true)
        validate_ticket_permissions(@user_1_tickets, form_fields, 1, is_disabled: true, is_visible: false)
      end

      it "will expect field 3 user 2 to have write, user 1 to have write" do
        validate_ticket_permissions(@user_2_tickets, form_fields, 2, is_disabled: false, is_visible: true)
        validate_ticket_permissions(@user_1_tickets, form_fields, 2, is_disabled: false, is_visible: true)
      end

      it "will expect field 4 user 2 to have read, user 1 to have read" do
        validate_ticket_permissions(@user_2_tickets, form_fields, 3, is_disabled: true, is_visible: true)
        validate_ticket_permissions(@user_1_tickets, form_fields, 3, is_disabled: true, is_visible: true)
      end

      it "will expect field 5 user 1 and user 2 to write because of everyone group" do
        validate_ticket_permissions(@user_2_tickets, form_fields, 4, is_disabled: false, is_visible: true)
        validate_ticket_permissions(@user_1_tickets, form_fields, 4, is_disabled: false, is_visible: true)
      end
    end

    context "When workspace permissions are different for one user " do
      let(:workspace2) { FactoryBot.create(:workspace, company: company) } 
      let(:second_workspace_query_params) {
        {
          workspaces: [workspace2], 
          scoped_contributor_id: company_user_3.contributor_id, 
          user: company_user_3, 
          ticket_limit: 2, 
          sort: "ticket_number asc"  
        }
      }
      let(:first_workspace_query_params) {
        {
          workspaces: [workspace], 
          scoped_contributor_id: company_user_3.contributor_id, 
          user: company_user_3, 
          ticket_limit: 4, 
          sort: "ticket_number asc"  
        }
      }
      
      before do
        # This is needed to create tickets and and custom form for second workspace
        create_custom_form(workspace2)
        second_workspace_help_ticket_params = [
          {
            workspace: workspace2,
            custom_form: workspace2.custom_forms.first,
            "Subject" => "Time flies",
            "Created_By" => company_user.contributor_id,
            "Assigned_To" => company_user_1.contributor_id,
            "Status" => "In Progress",
            "Priority" => "low",
            "Description" => "This time will pass",
          },
          {
            workspace: workspace2,
            custom_form: workspace2.custom_forms.first,
            "Subject" => "Greatest Moment",
            "Created_By" => company_user.contributor_id,
            "Assigned_To" => company_user_1.contributor_id,
            "Status" => "In Progress",
            "Priority" => "low",
            "Description" => "What a great day!"
          }
        ]
        second_workspace_help_ticket_params.map{ |p| create_ticket(p) }
        ExpandedPrivilege.create(
          name: "HelpTicket",
          permission_type: "write",
          workspace_id: workspace.id,
          company_id: company.id,
          contributor_id: company_user_3.contributor_id,
          sources: ["Everyone"]
        )
      end

      it "will expect company user 3 to not have access to any tickets of second workspace" do
        ticket_ids = HelpDesk::TicketQuery::TicketQuery.new(second_workspace_query_params, false, company_user_3, company).call.map(&:id)
        user_3_tickets = described_class.new(company_user_3, ticket_cols, default_ticket_columns).call(ticket_ids, 'ticket_number asc')
        expect(user_3_tickets.count).to eq 0
      end

      it "will expect company user 3 to have access tickets of first workspace" do
        ticket_ids = HelpDesk::TicketQuery::TicketQuery.new(first_workspace_query_params, false, company_user_3, company).call.map(&:id)
        user_3_tickets = described_class.new(company_user_3, ticket_cols, default_ticket_columns).call(ticket_ids, 'ticket_number asc')
        expect(user_3_tickets.count).to eq 4
      end
    end

    context "When a user with same email is in multiple companies" do
      let!(:child_com) { create(:company, reseller_company: company) }
      let!(:company_user_5) { create(:company_user, user: user4, company: child_com) }
      let(:child_company_workspace) { FactoryBot.create(:workspace, company: child_com) } 
      let(:child_company_workspace_query_params) {
        {
          workspaces: [child_company_workspace], 
          scoped_contributor_id: company_user_5.contributor_id, 
          user: company_user_5, 
          ticket_limit: 2, 
          sort: "ticket_number asc"  
        }
      }
      before do 
        # This is needed to create tickets and and custom form for child company's workspace
        create_custom_form(child_company_workspace)
        child_company_workspace_help_ticket_params = [
          {
            workspace: child_company_workspace,
            custom_form: child_company_workspace.custom_forms.first,
            "Subject" => "Time flies",
            "Created_By" => company_user_5.contributor_id,
            "Assigned_To" => company_user_5.contributor_id,
            "Status" => "In Progress",
            "Priority" => "low",
            "Description" => "This time will pass",
          },
          {
            workspace: child_company_workspace,
            custom_form: child_company_workspace.custom_forms.first,
            "Subject" => "Greatest Moment",
            "Created_By" => company_user_5.contributor_id,
            "Assigned_To" => company_user_5.contributor_id,
            "Status" => "In Progress",
            "Priority" => "low",
            "Description" => "What a great day!"
          }
        ]
        child_company_workspace_help_ticket_params.map{ |p| create_ticket(p) }
        ExpandedPrivilege.create(
          name: "HelpTicket",
          permission_type: "write",
          workspace_id: workspace.id,
          company_id: company.id,
          contributor_id: company_user_4.contributor_id,
          sources: ["Everyone"]
        )
        ExpandedPrivilege.create(
          name: "HelpTicket",
          permission_type: "read",
          workspace_id: child_company_workspace.id,
          company_id: child_com.id,
          contributor_id: company_user_5.contributor_id,
          sources: ["Everyone"]
        )
      end

      it "will have write permission for tickets of parent workspace" do
        user_5_tickets = described_class.new(company_user_5, ticket_cols, default_ticket_columns).call(help_tickets.map{ |t| t.id }, 'ticket_number asc')
        user_5_tickets.each do |ticket|
          expect(ticket[:can_write]).to be true
        end
      end

      it "will have read permission for tickets of child company" do
        ticket_ids = HelpDesk::TicketQuery::TicketQuery.new(child_company_workspace_query_params, false, company_user_5, child_com).call.map(&:id)
        user_5_tickets = described_class.new(company_user_5, ticket_cols, default_ticket_columns).call(ticket_ids, 'ticket_number asc')
        user_5_tickets.each do |ticket|
          expect(ticket[:can_write]).to be false
        end
      end
    end

    def set_user_permissions(form_field_id:, contributor_id:, permission_type:)
      can_view = permission_type == "read"
      can_edit = permission_type == "write"
    
      CustomFormFieldPermission.create(
        custom_form_field_id: form_field_id,
        contributor_id: contributor_id,
        can_view: can_view,
        can_edit: can_edit
      )
    
      ExpandedFormFieldPermission.create(
        custom_form_field_id: form_field_id,
        contributor_id: contributor_id,
        permission_type: permission_type
      )
    end

    def validate_ticket_permissions(tickets, form_fields, field_number, is_disabled:, is_visible:)
      tickets.each do |ticket|
        expect(ticket[:can_write]).to be true
        # Skip checkbox case as we don't send perms from service here
        field_name = form_fields.values[field_number][:name]
        next if field_name == :checkbox  
        field_attributes = ticket[:fields][field_name.to_sym]
        if field_attributes.is_a?(Hash)
          expect(field_attributes[:visible]).to be is_visible
          expect(field_attributes[:disabled]).to be is_disabled
        elsif field_attributes.is_a?(Array)
          field_attributes.each do |attribute|
            expect(attribute[:visible]).to be is_visible
            expect(attribute[:disabled]).to be is_disabled
          end
        end
      end
    end    
  end
end
