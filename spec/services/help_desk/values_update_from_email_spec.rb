require 'rails_helper'
include CompanyUserHelper

describe HelpDesk::ValuesUpdateFromEmail do
  create_company_and_user(subdomain: "test", email: "<EMAIL>")

  class Aws::S3::Client
    def get_object(hash)
      key = hash[:key]
      if key =~ /\/(.+)$/
        message_id = $1
        if File.exist?(Rails.root.join("spec", "data", message_id))
          OpenStruct.new(body: File.open(Rails.root.join("spec", "data", message_id)))
        else
          raise Aws::S3::Errors::NoSuchKey.new(nil, nil)
        end
      end
    end
  end

  let(:default_form) {
    company.custom_forms.where(company_module: 'helpdesk').first
  }

  describe "#create" do
    context "creating a ticket and calling add values service" do
      before do
        HelpTicket.destroy_all
        TicketEmail.destroy_all
      end
      
      let!(:message_id) { "cg0hrbp0aglhtabhjr6g8kge2jqq9mteoe1h0bg1" }
      let!(:from) { "<EMAIL>" }

      it "creates a new ticket and adds back missing values" do
        helpdesk_setting = DefaultHelpdeskSetting.find_by(setting_type: "allow_incoming_requests")
        company.helpdesk_settings.find_by(default_helpdesk_setting_id: helpdesk_setting.id).update(enabled: true)

        InboundEmailWorker.new.perform(message_id, from)

        expect(company.help_tickets.count).to eq(1)
        ticket = company.help_tickets.last

        priority_field = ticket.custom_form.custom_form_fields.find_by(name: 'priority')
        description_field = ticket.custom_form.custom_form_fields.find_by(name: 'description')
        attachment_field = ticket.custom_form.custom_form_fields.find_by(name: 'attachments')

        expect(priority_field.custom_form_values).to be_present
        expect(description_field.custom_form_values).to be_present
        expect(attachment_field.custom_form_values).to be_present
        
        priority_field.custom_form_values.destroy_all
        description_field.custom_form_values.destroy_all
        attachment_field.custom_form_values.destroy_all

        HelpDesk::ValuesUpdateFromEmail.new().call
        priority_field.reload
        description_field.reload
        attachment_field.reload

        expect(ticket.custom_form_values.find_by(custom_form_field_id: priority_field.id).value_str).to include(priority_field.default_value || 'Open')
        expect(ticket.custom_form_values.find_by(custom_form_field_id: description_field.id).value_str).to include("The information contained in this communication is confidential")
        expect(ticket.custom_form_values.where(custom_form_field_id: attachment_field.id)).to be_present
      end

      it "creates a new ticket and does not change existing values" do
        helpdesk_setting = DefaultHelpdeskSetting.find_by(setting_type: "allow_incoming_requests")
        company.helpdesk_settings.find_by(default_helpdesk_setting_id: helpdesk_setting.id).update(enabled: true)

        InboundEmailWorker.new.perform(message_id, from)

        expect(company.help_tickets.count).to eq(1)
        ticket = company.help_tickets.last

        priority_field = ticket.custom_form.custom_form_fields.find_by(name: 'priority')
        description_field = ticket.custom_form.custom_form_fields.find_by(name: 'description')
        attachment_field = ticket.custom_form.custom_form_fields.find_by(name: 'attachments')

        old_priority_value = ticket.custom_form_values.find_by(custom_form_field_id: priority_field.id)
        old_description_value = ticket.custom_form_values.find_by(custom_form_field_id: description_field.id)
        old_attachment_value = ticket.custom_form_values.find_by(custom_form_field_id: attachment_field.id)

        HelpDesk::ValuesUpdateFromEmail.new().call
        priority_field.reload
        description_field.reload
        attachment_field.reload

        expect(ticket.custom_form_values.find_by(custom_form_field_id: priority_field.id)).to eq(old_priority_value)
        expect(ticket.custom_form_values.find_by(custom_form_field_id: description_field.id)).to eq(old_description_value)
        expect(ticket.custom_form_values.where(custom_form_field_id: attachment_field.id)).to include(old_attachment_value)
      end
    end
  end
end
