require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::CategoryClause do
  create_company_and_user

  subject { described_class.new(query_params) }
  let(:company_user2) { create(:company_user, company: company)  }
  let(:custom_form) { company.custom_forms.helpdesk.first }

  let(:base) { HelpTicket.all.select("help_tickets.id") }
  let(:results) { subject.call(base) }
  let(:category) { CustomFormField.field_attribute_types[:category] }
  let!(:category_field) { CustomFormField.create(name: 'category', label: 'category', field_attribute_type: category, custom_form: custom_form) }
  let(:boom) { CustomFormValue.find_by(value_str: 'Boom boom').module }
  let(:greatest) { CustomFormValue.find_by(value_str: 'Greatest Moment').module }
  let(:working) { CustomFormValue.find_by(value_str: 'Working Hard').module }

  let(:query_params) { {} }

  let!(:help_tickets) do
    HelpTicket.destroy_all
    help_ticket_params.map{ |p| create_ticket(p) }
  end

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
        'category' => 'Cloud',
      },
      {
        'Subject' => "Boom boom",
        'Created By' => '<EMAIL>',
        'category' => 'DevOps',
      },
      {
        'Subject' => "Working Hard",
        'Created By' => '<EMAIL>',
      }
    ]
  }

  context 'with a category of cloud' do
    let(:query_params) {
      {
        category: 'Cloud',
      }
    }

    it 'should return only one ticket' do
      expect(results.length).to eq(1)
    end

    it 'should include the ticket with category cloud' do
      expect(results.first).to eq(greatest)
    end
  end

  context 'with a category of devops' do
    let(:query_params) {
      {
        category: 'DevOps',
      }
    }
    it 'should return only one ticket' do
      expect(results.length).to eq(1)
    end

    it 'should include the ticket with category devops' do
      expect(results.first).to eq(boom)
    end
  end

  context 'with a category of devops and cloud' do
    let(:query_params) {
      {
        category: ['DevOps', 'Cloud'],
      }
    }
    it 'should return only two ticket' do
      expect(results.length).to eq(2)
    end

    it 'should include the ticket with category devops and cloud' do
      expect(results.pluck(:id).sort).to eq([boom.id, greatest.id].sort)
    end
  end

  context 'without any category' do
    it 'should return all tickets' do
      expect(results.length).to eq(3)
    end
  end
end
