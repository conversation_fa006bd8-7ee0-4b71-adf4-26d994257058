require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::DueSoonClause do
  create_company_and_user

  subject { described_class.new(query_params) }
  let(:company_user2) { create(:company_user, company: company)  }

  let(:base) { HelpTicket.all }
  let(:results) { subject.call(base) }
  let(:boom) { CustomFormValue.find_by(value_str: 'Boom boom').module }
  let(:greatest) { CustomFormValue.find_by(value_str: 'Greatest Moment').module }

  let!(:help_tickets) do
    HelpTicket.destroy_all
    help_ticket_params.map{ |p| create_ticket(p) }
  end

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
      },
      {
        'Subject' => "Boom boom",
        'Created By' => '<EMAIL>',
      }
    ]
  }

  context 'with due soon filter' do
    let!(:create_tasks) {
      greatest.project_tasks.create(due_at: 1.day.from_now)
    }
    let(:query_params) {
      {
        due_soon_filter: true,
        workspace_ids: company.workspaces.pluck(:id),
      }
    }

    it 'returns 1 ticket' do
      expect(results.length).to eq(1)
    end

    it 'returns the first ticket' do
      expect(results.first).to eq(greatest)
    end
  end

  context 'with no due soon filter' do
    let(:query_params) {
      {
        due_soon_filter: false,
        workspace_ids: company.workspaces.pluck(:id),
      }
    }

    it 'returns 2 tickets' do
      expect(results.length).to eq(2)
    end

    it 'returns the first ticket' do
      expect(results.order(:id)).to eq(HelpTicket.order(:id))
    end
  end
end
