require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::PrioritySortClause do
  create_company_and_user

  subject { described_class.new(query_params) }
  let(:company_user2) { create(:company_user, company: company)  }

  let(:base) { HelpTicket.all.select("help_tickets.id") }
  let(:results) { subject.call(base) }
  let(:boom) { CustomFormValue.find_by(value_str: 'Boom boom').module }
  let(:greatest) { CustomFormValue.find_by(value_str: 'Greatest Moment').module }
  let!(:custom_form) { company.custom_forms.last }

  let!(:help_tickets) do
    HelpTicket.destroy_all
    custom_form.workspace = company.default_workspace
    custom_form.save!
    help_ticket_params.map{ |p| create_ticket(p) }
  end

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
        'Priority' => 'high',
      },
      {
        'Subject' => "Boom boom",
        'Created By' => '<EMAIL>',
        'Priority' => 'low',
      }
    ]
  }

  context 'with priority desc' do
    let(:query_params) {
      {
        sort: 'priority desc',
        workspaces: company.workspaces,
      }
    }

    it 'returns ticket in descending order' do
      expect(results.to_a).to eq([greatest, boom])
    end
  end

  context 'with priority asc' do
    let(:query_params) {
      {
        sort: 'priority asc',
        workspaces: company.workspaces,
      }
    }

    it 'returns ticket in descending order' do
      expect(results.to_a).to eq([boom, greatest])
    end
  end
end
