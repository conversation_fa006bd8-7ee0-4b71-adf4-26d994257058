require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::CommentCountSortClause do
  create_company_and_user

  let(:company_user2) { create(:company_user, company: company) }

  subject { described_class.new(query_params) }

  let(:base) { HelpTicket.all.select("help_tickets.id") }

  let!(:help_tickets) do
    HelpTicket.destroy_all
    help_ticket_params.map{ |p| create_ticket(p) }
  end

  let!(:comments) { 
    t = HelpTicket.joins(:custom_form_values).find_by(custom_form_values: { value_str: 'Greatest Moment' })
    2.times { HelpTicketComment.create(help_ticket: t) }
    t = HelpTicket.joins(:custom_form_values).find_by(custom_form_values: { value_str: 'Working Hard' })
    HelpTicketComment.create(help_ticket: t)
  }

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
      },
      {
        'Subject' => "Boom boom",
      },
      {
        'Subject' => "Working Hard",
      }
    ]
  }

  describe '#call' do
    context "in desc order" do
      let(:query_params) {
        {
          sort: 'comment_count desc',
        }
      }
      let(:expected_ids) {
        ids = []
        ids << CustomFormValue.joins(:custom_form_field).find_by(custom_form_fields: { name: 'subject' }, value_str: "Greatest Moment").module_id
        ids << CustomFormValue.joins(:custom_form_field).find_by(custom_form_fields: { name: 'subject' }, value_str: "Working Hard").module_id
        ids << CustomFormValue.joins(:custom_form_field).find_by(custom_form_fields: { name: 'subject' }, value_str: "Boom boom").module_id
        ids
      }

      let(:results) { subject.call(base) }
      
      it 'returns tickets in ascending assignee count order' do
        expect(results.pluck(:id)).to eq(expected_ids)
      end
    end

    context "in asc order" do
      let(:query_params) {
        {
          sort: 'comment_count asc',
        }
      }
      let(:expected_ids) {
        ids = []
        ids << CustomFormValue.joins(:custom_form_field).find_by(custom_form_fields: { name: 'subject' }, value_str: "Boom boom").module_id
        ids << CustomFormValue.joins(:custom_form_field).find_by(custom_form_fields: { name: 'subject' }, value_str: "Working Hard").module_id
        ids << CustomFormValue.joins(:custom_form_field).find_by(custom_form_fields: { name: 'subject' }, value_str: "Greatest Moment").module_id
        ids
      }

      let(:results) { subject.call(base) }

      it 'returns tickets in descending assignee count order' do
        expect(results.pluck(:id)).to eq(expected_ids)
      end
    end
  end
end
