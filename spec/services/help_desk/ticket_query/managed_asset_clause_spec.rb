require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::ManagedAssetClause do
  create_company_and_user

  subject { described_class.new(query_params) }
  let(:company_user2) { create(:company_user, company: company)  }

  let(:base) { HelpTicket.all }
  let(:results) { subject.call(base) }
  let(:managed_asset) { create(:managed_asset, company: company) }
  let(:boom) { CustomFormValue.find_by(value_str: 'Boom boom').module }
  let(:greatest) { CustomFormValue.find_by(value_str: 'Greatest Moment').module }

  let!(:help_tickets) do
    HelpTicket.destroy_all
    help_ticket_params.map{ |p| create_ticket(p) }
  end

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
        'Impacted Devices' => [ managed_asset.id ],
      },
      {
        'Subject' => "Boom boom",
        'Created By' => '<EMAIL>',
      }
    ]
  }

  context 'with a managed asset' do
    let(:query_params) {
      {
        managed_asset_id: managed_asset.id,
      }
    }

    it 'returns 1 ticket' do
      expect(results.length).to eq(1)
    end

    it 'returns the first ticket' do
      expect(results.first).to eq(greatest)
    end
  end

  context 'with no managed asset' do
    let(:query_params) {
      {
        due_soon_filter: false,
        workspace_ids: company.workspaces.pluck(:id),
      }
    }

    it 'returns 2 tickets' do
      expect(results.length).to eq(2)
    end
  end
end
