require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::TicketNumberSortClause do
  create_company_and_user

  subject { described_class.new(query_params) }
  let(:company_user2) { create(:company_user, company: company)  }

  let(:base) { HelpTicket.all.select("help_tickets.id") }
  let(:results) { subject.call(base) }
  let(:boom) { CustomFormValue.find_by(value_str: 'Boom boom').module }
  let(:boom_boom) { CustomFormValue.find_by(value_str: 'Boom boom boom').module }
  let(:greatest) { CustomFormValue.find_by(value_str: 'Greatest Moment').module }
  let(:second_greatest) { CustomFormValue.find_by(value_str: 'Second Greatest Moment').module }
  let!(:custom_form) { company.custom_forms.last }

  let!(:help_tickets) do
    HelpTicket.destroy_all
    custom_form.workspace = company.default_workspace
    custom_form.save!
    help_ticket_params.map{ |p| create_ticket(p) }
    ticket_number_setting = DefaultHelpdeskSetting.find_by(setting_type: 'customize_ticket_number_for_help_desk')
    workspace.helpdesk_settings
             .find_by(default_helpdesk_setting_id: ticket_number_setting.id)
             .update_columns(enabled: true)
    help_ticket_params2.map{ |p| create_ticket(p) }
  end

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
        'ticket_number' => '1',
        skip_save_ticket_number: true,
      },
      {
        'Subject' => "Boom boom",
        'Created By' => '<EMAIL>',
        'ticket_number' => '207',
        skip_save_ticket_number: true,
      }
    ]
  }
  let(:help_ticket_params2) {
    [
      {
        'ticket_number' => "GEN-52",
        'Subject' => "Second Greatest Moment",
        skip_save_ticket_number: true,
      },
      {
        'ticket_number' => "GEN-57",
        'Subject' => "Boom boom boom",
        skip_save_ticket_number: true,
      }
    ]
  }

  context 'with ticket_number desc' do
    let(:query_params) {
      {
        sort: 'ticket_number desc',
        workspace_ids: company.workspaces.pluck(:id),
      }
    }

    it 'returns ticket in descending order' do
      expected_output = ['207', 'GEN-57', 'GEN-52', '1']
      expect(results.pluck(:ticket_number)).to eq(expected_output)
    end
  end

  context 'with ticket_number asc' do
    let(:query_params) {
      {
        sort: 'ticket_number asc',
        workspace_ids: company.workspaces.pluck(:id),
      }
    }

    it 'returns ticket in ascending order' do
      expected_output = ['1', 'GEN-52', 'GEN-57', '207']
      expect(results.pluck(:ticket_number)).to eq(expected_output)
    end
  end
end
