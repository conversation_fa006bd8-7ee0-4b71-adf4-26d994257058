require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::AssignmentClause do
  create_company_and_user

  let(:company_user2) {
    cu = create(:company_user, company: company)
    cu.user.first_name = 'Aaalf'
    cu.user.last_name = 'Aaandrews'
    cu.user.save!
    cu
  }
  let(:company_user1) {
    company_user.user.first_name = '<PERSON>'
    company_user.user.last_name = 'Jones'
    company_user.user.save!
    company_user
  }

  subject { described_class.new(query_params) }

  let(:base) { HelpTicket.all.select("help_tickets.id") }

  let!(:help_tickets) do
    HelpTicket.destroy_all
    help_ticket_params.map{ |p| create_ticket(p) }
  end

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Assigned To' => company_user1.contributor_id,
        'Status' => 'Open'
      },
      {
        'Subject' => "Boom boom",
        'Status' => 'Open'
      },
      {
        'Subject' => "Working Hard",
        'Assigned To' => company_user2.contributor_id,
        'Status' => 'Open'
      },
      {
        'Subject' => "New Ticket",
        'Assigned To' => company_user2.contributor_id,
        'Status' => 'Closed'
      }
    ]
  }

  describe '#call' do
    context "assigned help tickets" do
      let(:query_params) {
        {
          assignment: ['Assigned'],
          custom_status_options: ['Open', 'In Progress'],
        }
      }
      let(:expected_ids) {
        ids = []
        ids << CustomFormValue.joins(:custom_form_field).find_by(custom_form_fields: { name: 'subject' }, value_str: "Greatest Moment").module_id
        ids << CustomFormValue.joins(:custom_form_field).find_by(custom_form_fields: { name: 'subject' }, value_str: "Working Hard").module_id
        ids
      }

      let(:results) { subject.call(base) }

      it 'returns tickets which has assigned users with status Open' do
        expect(results.map(&:id).sort).to eq(expected_ids)
      end
    end

    context "unassigned help tickets" do
      let(:query_params) {
        {
          assignment: ['Unassigned'],
          custom_status_options: ['Open', 'In Progress'],
        }
      }
      let(:expected_ids) {
        ids = []
        ids << CustomFormValue.joins(:custom_form_field).find_by(custom_form_fields: { name: 'subject' }, value_str: "Boom boom").module_id
        ids
      }

      let(:results) { subject.call(base) }

      it 'returns tickets which has no assigned user with status Open' do
        expect(results.map(&:id)).to eq(expected_ids)
      end
    end
  end
end
