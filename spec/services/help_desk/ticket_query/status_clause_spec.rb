require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::StatusClause do
  create_company_and_user

  subject { described_class.new(query_params) }
  let(:company_user2) { create(:company_user, company: company)  }

  let(:base) { HelpTicket.all }
  let(:results) { subject.call(base) }
  let(:boom) { CustomFormValue.find_by(value_str: 'Boom boom').module }
  let(:greatest) { CustomFormValue.find_by(value_str: 'Greatest Moment').module }
  let(:working) { CustomFormValue.find_by(value_str: 'Working Hard').module }
  let(:query_params) { {} }
  let(:helpdesk_agents) { company.groups.find_by(name: 'Help Desk Agents') }

  before do
    helpdesk_agents.group_members << GroupMember.new(contributor: company_user.contributor)
  end

  let!(:help_tickets) do
    HelpTicket.destroy_all
    help_ticket_params.map{ |p| create_ticket(p) }
  end

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
        'Status' => 'Closed',
      },
      {
        'Subject' => "Boom boom",
        'Created By' => '<EMAIL>',
        'Status' => 'In Progress',
      },
      {
        'Subject' => "Working Hard",
        'Created By' => '<EMAIL>',
        'Status' => 'Open',
      }
    ]
  }

  context 'with archived' do
    let!(:archived)  { 
      working.archived = true
      working.save!
    }
    let(:query_params) { 
      {
        statuses: ['Archived'],
      } 
    }
    
    it 'should return only one ticket' do
      expect(results.length).to eq(1)
    end

    it 'should include the archived ticket' do
      expect(results.first).to eq(working)
    end
  end

  context 'with archived' do
    let!(:is_new)  { 
      working.is_new = true
      working.save!
    }
    let(:query_params) { 
      {
        statuses: ['New'],
        scoped_company_user: company_user,
        current_workspace: workspace
      } 
    }
    
    it 'should return only one ticket' do
      expect(results.length).to eq(1)
    end

    it 'should include the new ticket' do
      expect(results.first).to eq(working)
    end
  end

  context 'with an active status' do
    let(:query_params) { 
      {
        statuses: ['Active'],
      } 
    }
    it 'should return only active tickets' do
      expect(results.length).to eq(2)
    end

    it 'should include the correct prioritized ticket' do
      expect(results.pluck(:id).sort).to eq([boom.id, working.id].sort)
    end
  end

  context 'with a status' do
    let(:query_params) { 
      {
        statuses: ['In Progress'],
      } 
    }
    it 'should return only one ticket' do
      expect(results.length).to eq(1)
    end

    it 'should include the correct status ticket' do
      expect(results.first).to eq(boom)
    end
  end

  context 'without a status' do
    it 'should return all tickets' do
      expect(results.length).to eq(2)
    end
  end
end
