require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::DateRangeClause do
  create_company_and_user

  subject { described_class.new(query_params) }
  let(:company_user2) { create(:company_user, company: company)  }

  let(:base) { HelpTicket.all }
  let(:results) { subject.call(base) }
  let(:boom) { CustomFormValue.find_by(value_str: 'Boom boom').module }
  let(:greatest) { CustomFormValue.find_by(value_str: 'Greatest Moment').module }
  let(:working) { CustomFormValue.find_by(value_str: 'Working hard').module }

  let!(:help_tickets) do
    HelpTicket.destroy_all
    help_ticket_params.map{ |p| create_ticket(p) }
  end

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
      },
      {
        'Subject' => "Boom boom",
        'Created By' => '<EMAIL>',
      },
      {
        'Subject' => "Working hard",
        'Created By' => company_user2.contributor_id,
      }
    ]
  }

  context 'with All date range ' do
    let(:query_params) {
      {
        filter_type: 'All',
        date_filter: '1',
        workspaces: company.workspaces,
      }
    }

    it 'returns all tickets' do
      expect(results.length).to eq(3)
    end

    it 'returns the tickets in order' do
      expect(results.pluck(:id).sort).to eq(HelpTicket.all.pluck(:id).sort)
    end
  end

  context 'with years date range' do
    let!(:created) {
      boom.update_column('created_at', 2.year.ago)
      greatest.update_column('created_at', 6.months.ago)
    }
    let(:query_params) {
      {
        filter_type: 'years',
        date_filter: '1',
        workspaces: company.workspaces,
      }
    }

    it 'returns 2 tickets' do
      expect(results.length).to eq(2)
    end

    it 'returns the first ticket' do
      expect(results.pluck(:id).sort).to eq([greatest.id, working.id].sort)
    end
  end

  context 'with current month date range' do
    let!(:created) {
      boom.update_column('created_at', 2.year.ago)
      greatest.update_column('created_at', 6.months.ago)
    }
    let(:query_params) {
      {
        filter_type: 'current_month',
        date_filter: '1',
        workspaces: company.workspaces,
      }
    }

    it 'returns one ticket' do
      expect(results.length).to eq(1)
    end

    it 'returns the first ticket' do
      expect(results.first).to eq(working)
    end
  end

  context 'with created today date range' do
    let!(:created) {
      boom.created_at = 1.month.ago
      boom.save!
      greatest.created_at = 2.days.ago
      greatest.save!
    }

    let(:query_params) {
      {
        filter_type: 'created_today',
        date_filter: '1',
        workspaces: company.workspaces,
      }
    }

    it 'returns one ticket' do
      expect(results.length).to eq(1)
    end

    it 'returns the first ticket' do
      expect(results.first).to eq(working)
    end
  end

  context 'with calendar year date range' do
    let!(:created) {
      Timecop.freeze(Date.strptime('2021-08-23').to_date) do 
        boom.update_column('created_at', 1.month.ago)
        greatest.update_column('created_at', 1.year.ago)
        working.update_column('created_at', 1.hour.ago)
      end
    }
    let(:results) { 
      Timecop.freeze(Date.strptime('2021-08-23').to_date) do 
        subject.call(base) 
      end
    }
    let(:query_params) {
      {
        filter_type: 'calendar_year',
        date_filter: '1',
        workspaces: company.workspaces,
      }
    }

    it 'returns 2 tickets' do
      expect(results.length).to eq(2)
    end

    it 'returns the first ticket' do
      expect(results.pluck(:id).sort).to eq([boom.id, working.id].sort)
    end
  end

  context 'with fiscal year date range' do
    context "and a fiscal year set for the company" do
      let!(:created) {
        company.fiscal_year = Date.strptime('2021-09-01').to_date
        company.save!
        Timecop.freeze(Date.strptime('2021-08-23').to_date) do 
          boom.update_column('created_at', 1.month.ago)
          greatest.update_column('created_at', 1.year.ago)
          working.update_column('created_at', 1.hour.ago)
        end
      }
      let(:query_params) {
        {
          filter_type: 'calendar_year',
          date_filter: '1',
          workspaces: company.workspaces,
        }
      }
      let(:results) { 
        Timecop.freeze(Date.strptime('2021-08-23').to_date) do 
          subject.call(base) 
        end
      }

      it 'returns 2 tickets' do
        expect(results.length).to eq(2)
      end

      it 'returns the first ticket' do
        expect(results.pluck(:id).sort).to eq([boom.id, working.id].sort)
      end
    end

    context "and a fiscal year not set for the company" do
      let!(:created) {
        Timecop.freeze(Date.strptime('2021-08-23').to_date) do 
          boom.update_column('created_at', 9.month.ago)
          greatest.update_column('created_at', 1.year.ago)
          working.update_column('created_at', 1.hour.ago)
        end
      }
      let(:query_params) {
        {
          filter_type: 'calendar_year',
          date_filter: '1',
          workspaces: company.workspaces,
        }
      }
      let(:results) { 
        Timecop.freeze(Date.strptime('2021-08-23').to_date) do 
          subject.call(base) 
        end
      }

      it 'returns 1 ticket' do
        expect(results.length).to eq(1)
      end

      it 'returns the first ticket' do
        expect(results.first).to eq(working)
      end
    end
  end

  context 'with custom date range' do
    let!(:created) {
      boom.created_at = 1.month.ago
      boom.save!
      greatest.created_at = 3.days.ago
      greatest.save!
      working.created_at = 1.days.ago
      working.save!
    }

    let(:query_params) {
      {
        custom_date_filter: "{\"startDate\":\"#{ 5.days.ago }\",\"endDate\":\"#{ 2.days.ago }\",\"filter\":\"custom_date\", \"name\":\"Custom date\"}",
        date_filter: '3',
        workspaces: company.workspaces,
      }
    }

    it 'returns one ticket' do
      expect(results.length).to eq(1)
    end

    it 'returns the second ticket' do
      expect(results.first).to eq(greatest)
    end
  end

  context 'with custom date range older than 1 week' do
    let!(:created) {
      boom.created_at = 1.month.ago
      boom.save!
      greatest.created_at = 3.days.ago
      greatest.save!
      working.created_at = 1.days.ago
      working.save!
    }

    let(:query_params) {
      {
        custom_date_filter: "{\"startDate\":\"\",\"endDate\":\"#{ 7.days.ago }\",\"filter\":\"custom_date\"}",
        date_filter: '3',
        workspaces: company.workspaces,
      }
    }

    it 'returns one ticket' do
      expect(results.length).to eq(1)
    end

    it 'returns the second ticket' do
      expect(results.first).to eq(boom)
    end
  end
end
