require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::DateSortClause do
  before { date_field }
  create_company_and_user

  subject { described_class.new(query_params) }
  let(:company_user2) { create(:company_user, company: company)  }

  let(:base) { HelpTicket.all }
  let(:results) { subject.call(base) }
  let(:boom) { CustomFormValue.find_by(value_str: 'Boom boom').module }
  let(:greatest) { CustomFormValue.find_by(value_str: 'Greatest Moment').module }
  let(:working_hard) { CustomFormValue.find_by(value_str: 'Working hard').module }
  let(:custom_form) { company.custom_forms.find_by(company_module: 'helpdesk') }
  let(:date) { CustomFormField.field_attribute_types[:date] }
  let(:date_field) { CustomFormField.create(name: 'date', label: 'Date', field_attribute_type: date, custom_form: custom_form) }
  let!(:help_tickets) do
    HelpTicket.destroy_all
    help_ticket_params.map{ |p| create_ticket(p) }
  end

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
        'Date' => '2022-10-5'
      },
      {
        'Subject' => "Boom boom",
        'Created By' => company_user.contributor_id,
        'Date' => nil
      },
      {
        'Subject' => "Working hard",
        'Created By' => company_user2.contributor_id,
        'Date' => '2021-10-5'
      }
    ]
  }

  describe '#call' do
    context 'date_column asc' do
      let(:query_params) {
        {
          sort: "date_column date asc",
        }
      }

      let(:expected_result) { [ working_hard.id, greatest.id, boom.id ] }
      it 'should successfully get result' do
        expect(results).to be_present
        expect(results.length).to eq(3)
      end

      it 'sort tickets in ascending order from their respective dates' do
        expect(results.pluck(:id)).to eq(expected_result)
      end
    end

    context 'date_column desc' do
      let(:query_params) {
        {
          sort: "date_column date desc",
        }
      }

      let(:expected_result) { [ greatest.id, working_hard.id, boom.id ] }
      it 'should successfully get result' do
        expect(results).to be_present
        expect(results.length).to eq(3)
      end

      it 'sort tickets in ascending order from their respective dates' do
        expect(results.pluck(:id)).to eq(expected_result)
      end
    end
  end
end
