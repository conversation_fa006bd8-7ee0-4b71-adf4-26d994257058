require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::ScopedUserClause do
  create_company_and_user
  let!(:company2) { create(:company, reseller_company_id: company.id) }

  subject { described_class.new(query_params) }
  let(:company_user2) { 
    cu = create(:company_user, company: company)  
    cu.contributor.expanded_privileges << ExpandedPrivilege.new(name: 'HelpTicket', permission_type: 'scoped', workspace: company.default_workspace)
    cu
  }
  let(:company_user3) { 
    cu = create(:company_user, company: company2, user: company_user2.user)  
    cu.contributor.expanded_privileges << ExpandedPrivilege.new(name: 'HelpTicket', permission_type: 'scoped', workspace: company2.default_workspace)
    cu
  }
  let(:company_user4) { 
    cu = create(:company_user, company: company2, user: user)  
    cu.contributor.expanded_privileges << ExpandedPrivilege.new(name: 'HelpTicket', permission_type: 'scoped', workspace: company2.default_workspace)
    cu
  }

  let(:base) { HelpTicket.all }
  let(:results) { subject.call(base) }
  let(:boom) { CustomFormValue.find_by(value_str: 'Boom boom').module }
  let(:greatest) { CustomFormValue.find_by(value_str: 'Greatest Moment').module }
  let!(:custom_form) { company.custom_forms.where(company_module:"helpdesk").first }
  let!(:custom_form2) { company2.custom_forms.where(company_module:"helpdesk").first }
  let(:company2_help_ticket) {
    ticket_params = {
      'Subject' => "Testing Ticket",
      'Created By' => company_user3.contributor_id,
      'Status' => 'Open',
      company: company2,
      workspace: company2.default_workspace,
      custom_form: custom_form2
    }
    create_ticket(ticket_params)
  }

  let!(:help_tickets) do
    HelpTicket.destroy_all
    custom_form.workspace = company.default_workspace
    custom_form.save!
    tickets = help_ticket_params.map{ |p| create_ticket(p) }
    tickets.each do |t| 
      t.workspace = company.default_workspace
      t.save!
    end
    tickets
  end

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
        'Status' => 'Closed',
      },
      {
        'Subject' => "Boom boom",
        'Created By' => company_user2.contributor_id,
        'Priority' => 'Open',
        'Followers' => company_user.contributor_id
      }
    ]
  }

  let(:company_help_ticket) {
    ticket_params = {
      'Subject' => "Testing Ticket",
      'Created By' => company_user4.contributor_id,
      'Status' => 'Open',
      company: company2,
      workspace: company2.default_workspace,
      custom_form: custom_form2
    }
    create_ticket(ticket_params)
  }

  context 'with workspace user filter' do
    let(:query_params) {
      {
        company_user: company_user,
        workspaces: company.workspaces,
      }
    }

    it 'returns all tickets' do
      expect(results.order(:id).to_a).to eq([ greatest, boom ])
    end
  end

  context 'with scoped user filter' do
    let(:query_params) {
      {
        scoped_contributor_id: company_user2.contributor_id,
        scoped_group_contributor_ids: [company_user2.contributor_id, company_user3.contributor_id],
        workspaces: company.workspaces,
        current_company: company,
        current_workspace: company.default_workspace,
      }
    }

    it 'returns scoped tickets' do
      expect(results.to_a).to eq([ boom ])
    end
  end

  context 'with involved user filter' do
    let(:query_params) {
      {
        involved_contributor_id: company_user2.contributor_id,
        workspaces: company.workspaces,
      }
    }

    it 'returns involved tickets' do
      expect(results.order(:id).to_a).to eq([ greatest, boom ])
    end
  end

  context 'with parent and child company user and without company filter' do
    let(:query_params) {
      {
        scoped_contributor_id: [company_user2.contributor_id, company_user3.contributor_id],
        scoped_group_contributor_ids: [company_user2.contributor_id, company_user3.contributor_id],
        current_company: company,
        current_workspace: company.default_workspace,
      }
    }

    it 'returns both parent and child tickets' do
      company2_help_ticket.save!
      expect(results.uniq.pluck(:id).sort).to eq([boom.id, company2_help_ticket.id].sort)
    end
  end

  context 'return tickets where user is added as follower' do
    let(:query_params) {
      {
        scoped_contributor_id: company_user.contributor_id,
        scoped_group_contributor_ids: [company_user2.contributor_id, company_user3.contributor_id],
        workspaces: company.workspaces,
        current_company: company,
        current_workspace: company.default_workspace
      }
    }

    it 'when settting is enable return 3 tickets' do
      HelpdeskSetting.joins(:default_helpdesk_setting)
      .find_by(default_helpdesk_setting: { setting_type: 'allow_followers_see_tickets' }, company: company, workspace: company.default_workspace)
      .update!(enabled: true)

      company.reload
      expect(results.count).to eq(3)
    end
  end

  context 'return all tickets' do
    let(:company_ids) { Company.all.pluck(:id) }
    let(:query_params) {
      {
        multi_companies_filter: company_ids,
        scoped_contributor_id: CompanyUser.where(user_id: user.id, company_id: company_ids).pluck(:contributor_id),
        involved_contributor_id: company_user.contributor_id
      }
    }
    it 'when multi companies filter will apply' do
      company_help_ticket.save!
      expect(results.count).to eq(3)
    end
  end
end
