require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::PriorityClause do
  create_company_and_user

  subject { described_class.new(query_params) }
  let(:company_user2) { create(:company_user, company: company)  }

  let(:base) { HelpTicket.all }
  let(:results) { subject.call(base) }
  let(:boom) { CustomFormValue.find_by(value_str: 'Boom boom').module }
  let(:greatest) { CustomFormValue.find_by(value_str: 'Greatest Moment').module }
  let(:query_params) { {} }

  let!(:help_tickets) do
    HelpTicket.destroy_all
    help_ticket_params.map{ |p| create_ticket(p) }
  end

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
        'Priority' => 'High',
      },
      {
        'Subject' => "Boom boom",
        'Created By' => '<EMAIL>',
        'Priority' => 'Low',
      }
    ]
  }

  context 'with a priorty' do
    let(:query_params) { 
      {
        priorities: ['High'],
      } 
    }
    it 'should return only one ticket' do
      expect(results.length).to eq(1)
    end

    it 'should include the correct prioritized ticket' do
      expect(results.first).to eq(greatest)
    end
  end

  context 'without a priorty' do
    it 'should return all tickets' do
      expect(results.length).to eq(2)
    end
  end
end
