require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::DateCreatedSortClause do
  create_company_and_user

  subject { described_class.new(query_params) }
  let(:company_user2) { create(:company_user, company: company)  }

  let(:base) { HelpTicket.all }
  let(:results) { subject.call(base) }
  let(:boom) { CustomFormValue.find_by(value_str: 'Boom boom').module }
  let(:greatest) { CustomFormValue.find_by(value_str: 'Greatest Moment').module }
  let(:working_hard) { CustomFormValue.find_by(value_str: 'Working hard').module }

  let!(:help_tickets) do
    HelpTicket.destroy_all
    help_ticket_params.map{ |p| create_ticket(p) }
  end

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
        'created_at' => 1.year.ago
      },
      {
        'Subject' => "Boom boom",
        'Created By' => company_user.contributor_id,
        'created_at' => 2.year.ago
      },
      {
        'Subject' => "Working hard",
        'Created By' => company_user2.contributor_id,
        'created_at' => 1.day.ago
      }
    ]
  }

  describe '#call' do
    context 'created_at asc' do
      let(:query_params) {
        {
          sort: "created_at asc",
        }
      }

      let(:expected_result) { [ boom.id, greatest.id, working_hard.id ] }
      it 'should successfully get result' do
        expect(results).to be_present
        expect(results.length).to eq(3)
      end

      it 'sort tickets in ascending order from their respective dates' do
        expect(results.pluck(:id)).to eq(expected_result)
      end
    end

    context 'created_at desc' do
      let(:query_params) {
        {
          sort: "created_at desc",
        }
      }

      let(:expected_result) { [ working_hard.id, greatest.id, boom.id ] }
      it 'should successfully get result' do
        expect(results).to be_present
        expect(results.length).to eq(3)
      end

      it 'sort tickets in ascending order from their respective dates' do
        expect(results.pluck(:id)).to eq(expected_result)
      end
    end
  end
end
