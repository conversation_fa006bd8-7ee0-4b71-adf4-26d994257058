require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::CustomFormClause do
  create_company_and_user

  subject { described_class.new(query_params) }

  let(:contract) { create(:contract, company: company) }
  let(:vendor) { create(:vendor, company: company) }
  let(:telecom) { create(:telecom_service, company: company) }
  let(:managed_asset) { create(:managed_asset, company: company) }

  let(:boom) { CustomFormValue.find_by(value_str: 'Boom boom').module }
  let(:greatest) { CustomFormValue.find_by(value_str: 'Greatest Moment').module }
  let(:working) { CustomFormValue.find_by(value_str: 'Working Hard').module }

  let!(:custom_form) { 
    f = company.custom_forms.helpdesk.first
    f.custom_form_fields << CustomFormField.new(label: 'Vendor', name: 'vendor', field_attribute_type: 'vendor_list')
    f.custom_form_fields << CustomFormField.new(label: 'Telecom', name: 'telecom', field_attribute_type: 'telecom_list')
    f.custom_form_fields << CustomFormField.new(label: 'Contract', name: 'contract', field_attribute_type: 'contract_list')
    f.custom_form_fields << CustomFormField.new(label: 'Staff', name: 'staff', field_attribute_type: 'people_list')
    f.custom_form_fields << CustomFormField.new(label: 'Asset', name: 'asset', field_attribute_type: 'asset_list')
  }

  let(:base) { HelpTicket.all }
  let(:results) { subject.call(base) }

  let!(:help_tickets) do
    HelpTicket.destroy_all
    help_ticket_params.map{ |p| create_ticket(p) }
  end

  context 'with a telecom service' do
    let(:help_ticket_params) {
      [
        {
          'Subject' => "Greatest Moment",
        },
        {
          'Subject' => "Boom boom",
          'Telecom' => telecom.id
        },
        {
          'Subject' => "Working Hard",
        }
      ]
    }
    let(:query_params) {
      {
        filter_by_field: ["{\"id\":#{telecom.id}, \"name\": \"Telecom\"}"],
      }
    }

    it 'returns 1 ticket' do
      expect(results.length).to eq(1)
    end

    it 'returns the telecom ticket' do
      expect(results[0]).to eq(boom)
    end
  end

  context 'with a vendor' do
    let(:help_ticket_params) {
      [
        {
          'Subject' => "Greatest Moment",
        },
        {
          'Subject' => "Boom boom",
          'Vendor' => vendor.id
        },
        {
          'Subject' => "Working Hard",
        }
      ]
    }
    let(:query_params) {
      {
        filter_by_field: ["{\"id\":#{vendor.id}, \"name\": \"Vendor\"}"],
      }
    }

    it 'returns 1 ticket' do
      expect(results.length).to eq(1)
    end

    it 'returns the vendor ticket' do
      expect(results[0]).to eq(boom)
    end
  end

  context 'with an asset' do
    let(:help_ticket_params) {
      [
        {
          'Subject' => "Greatest Moment",
        },
        {
          'Subject' => "Boom boom",
          'Asset' => managed_asset.id
        },
        {
          'Subject' => "Working Hard",
        }
      ]
    }
    let(:query_params) {
      {
        filter_by_field: ["{\"id\":#{managed_asset.id}, \"name\": \"Asset\" }"],
      }
    }

    it 'returns 1 ticket' do
      expect(results.length).to eq(1)
    end

    it 'returns the asset ticket' do
      expect(results[0]).to eq(boom)
    end
  end

  context 'with a contract' do
    let(:help_ticket_params) {
      [
        {
          'Subject' => "Greatest Moment",
        },
        {
          'Subject' => "Boom boom",
          'Contract' => contract.id
        },
        {
          'Subject' => "Working Hard",
        }
      ]
    }
    let(:query_params) {
      {
        filter_by_field: ["{\"id\":#{contract.id}, \"name\": \"Contract\"}"],
      }
    }

    it 'returns 1 ticket' do
      expect(results.length).to eq(1)
    end

    it 'returns the contract ticket' do
      expect(results[0]).to eq(boom)
    end
  end

  context 'with a staff' do
    let(:help_ticket_params) {
      [
        {
          'Subject' => "Greatest Moment",
        },
        {
          'Subject' => "Boom boom",
          'Staff' => company_user.contributor_id
        },
        {
          'Subject' => "Working Hard",
        }
      ]
    }
    let(:query_params) {
      {
        filter_by_field: ["{\"id\":#{company_user.contributor_id},\"name\":\"People is #{company_user.name}\"}"],
      }
    }

    it 'returns 1 ticket' do
      expect(results.length).to eq(1)
    end

    it 'returns the staff ticket' do
      expect(results[0]).to eq(boom)
    end
  end
end
