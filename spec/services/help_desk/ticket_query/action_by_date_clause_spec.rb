require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::ActionByDateClause do
  create_company_and_user

  subject { described_class.new(query_params) }
  let(:company_user2) { create(:company_user, company: company)  }

  let(:base) { HelpTicket.all }
  let(:results) { subject.call(base) }
  let(:boom) { CustomFormValue.find_by(value_str: 'Boom boom').module }
  let(:greatest) { CustomFormValue.find_by(value_str: 'Greatest Moment').module }
  let!(:custom_form) { company.custom_forms.last }

  let!(:help_tickets) do
    HelpTicket.destroy_all
    custom_form.workspace = company.default_workspace
    custom_form.save!
    help_ticket_params.map{ |p| create_ticket(p) }
  end

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
        'Status' => 'Closed',
      },
      {
        'Subject' => "Boom boom",
        'Created By' => '<EMAIL>',
        'Status' => 'Open',
      }
    ]
  }

  context 'with closed by date range filter' do
    let!(:status) {
      greatest.update_column(:closed_at, 3.days.ago)
    }

    let(:query_params) {
      {
        closed_by_dates: [ 5.days.ago, 2.days.ago, 'closed_by_date' ],
        workspaces: company.workspaces,
      }
    }

    it 'returns one ticket' do
      expect(results.length).to eq(1)
    end

    it 'returns the ticket with Closed status' do
      expect(results.first).to eq(greatest)
    end
  end

  context 'without closed by date range filter' do
    let(:query_params) {
      {
        workspace_ids: company.workspaces.pluck(:id),
      }
    }

    it 'returns all tickets' do
      expect(results.length).to eq(2)
    end
  end
end
