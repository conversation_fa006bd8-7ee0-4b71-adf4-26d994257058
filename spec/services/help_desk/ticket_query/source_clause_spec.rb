require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::SourceClause do
  create_company_and_user

  subject { described_class.new(query_params) }
  let(:company_user2) { create(:company_user, company: company)  }

  let(:base) { HelpTicket.all }
  let(:results) { subject.call(base) }
  let(:sources) { ["Manually Added", "Email", "Slack", "Auto Generated", "MS Teams", "Splitted"] }

  let(:boom) { CustomFormValue.find_by(value_str: 'Boom boom').module }
  let(:greatest) { CustomFormValue.find_by(value_str: 'Greatest Moment').module }
  let(:working) { CustomFormValue.find_by(value_str: 'Working Hard').module }
  let(:laptop) { CustomFormValue.find_by(value_str: 'Laptop is not working.').module }

  let(:query_params) { {} }

  let!(:help_tickets) do
    HelpTicket.destroy_all
    help_ticket_params.map{ |p| create_ticket(p) }
  end

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
        'source' =>  sources.index('Manually Added'),
      },
      {
        'Subject' => "Boom boom",
        'Created By' => '<EMAIL>',
        'source' => sources.index('Splitted'),
      },
      {
        'Subject' => "Working Hard",
        'Created By' => '<EMAIL>',
        'source' => sources.index('Email'),
      },
      {
        'Subject' => 'Laptop is not working.',
        'Created By' => '<EMAIL>',
        'source' => sources.index('Slack'),
      }
    ]
  }

  context 'which has a source Manually Added' do
    let(:query_params) {
      {
        source: sources.index("Manually Added"),
      }
    }

    it 'should return only one ticket' do
      expect(results.length).to eq(1)
    end

    it 'should include the ticket which has a source manually added' do
      expect(results.first).to eq(greatest)
    end
  end

  context 'which has a source Splitted' do
    let(:query_params) {
      {
        source: sources.index("Splitted"),
      }
    }
    it 'should return only one ticket' do
      expect(results.length).to eq(1)
    end

    it 'should include the ticket which has a source splitted' do
      expect(results.first).to eq(boom)
    end
  end

  context 'which has a source Email' do
    let(:query_params) {
      {
        source: sources.index("Email"),
      }
    }
    it 'should return only one ticket' do
      expect(results.length).to eq(1)
    end

    it 'should include the ticket which has a source email' do
      expect(results.first).to eq(working)
    end
  end

  context 'which has more than one source' do
    let(:query_params) do
      {  
        source: ['Manually Added', 'Slack'].map { |src| sources.index(src) }
      }
    end
    it 'should return multiple tickets' do
      expect(results.length).to eq(2)
    end

    it 'should return those tickets whose sources are included' do
      expect(results.find_by(source: query_params[:source][0])).to eq(greatest)
      expect(results.find_by(source: query_params[:source][1])).to eq(laptop)
    end
  end
end
