require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::StaffListSortClause do
  create_company_and_user

  subject { described_class.new(query_params) }
  let(:company_user2) { 
    cu = create(:company_user, company: company)  
    cu.user.first_name = 'Aaalf'
    cu.user.last_name = 'Aaandrews'
    cu.user.save!
    cu
  }
  let(:company_user1) {
    company_user.user.first_name = 'Doug'
    company_user.user.last_name = '<PERSON>'
    company_user.user.save!
    company_user
  }

  let(:base) do
    initial_query = HelpTicket.all.select("t.id FROM (SELECT DISTINCT ON(help_tickets.id) COALESCE(created_users.first_name, staff_list_groups.name) AS name, help_tickets.id ")
    expanded_privileges_join = """
    INNER JOIN expanded_privileges scoped_privileges
     ON 
      help_tickets.workspace_id = scoped_privileges.workspace_id
        AND scoped_privileges.contributor_id IN (#{company_user1.contributor_id})
        AND scoped_privileges.name = 'HelpTicket'
        LEFT OUTER JOIN custom_form_values scoped_values
        ON 
          scoped_values.module_id = help_tickets.id
            AND scoped_values.module_type = 'HelpTicket'
      LEFT OUTER JOIN custom_form_fields scoped_fields
        ON 
          scoped_fields.id = scoped_values.custom_form_field_id
    """
    initial_query.joins(expanded_privileges_join)
  end
  

  let(:results) { subject.call(base) }

  let!(:help_tickets) do
    HelpTicket.destroy_all
    help_ticket_params.map{ |p| create_ticket(p) }
  end

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => '<EMAIL>',
      },
      {
        'Subject' => "Boom boom",
        'Created By' => '<EMAIL>',
      },
      {
        'Subject' => "Working Hard",
        'Created By' => '<EMAIL>',
      }
    ]
  }

  context 'created_by desc' do
    let(:query_params) {
      {
        sort: "created_by desc",
        type: "people_list",
        user: company_user.user,
      }
    }

    it 'returns 3 tickets' do
      expect(results.length).to eq(3)
    end

    it 'returns the correct order' do
    end
  end

  context 'created_by asc' do
    let(:query_params) {
      {
        sort: "created_by asc",
        type: "people_list",
        user: company_user.user,
      }
    }

    let(:expected_ids) {
      ids = []
      ids << CustomFormValue.joins(:custom_form_field).find_by(custom_form_fields: { name: 'subject' }, value_str: "Greatest Moment").module_id
      ids << CustomFormValue.joins(:custom_form_field).find_by(custom_form_fields: { name: 'subject' }, value_str: "Boom boom").module_id
      ids << CustomFormValue.joins(:custom_form_field).find_by(custom_form_fields: { name: 'subject' }, value_str: "Working Hard").module_id
      ids
    }

    it 'returns 3 tickets' do
      expect(results.length).to eq(3)
    end

    it 'returns the correct order' do
      expect(results.first.id).to eq(expected_ids.first)
      expect(results.second.id).to eq(expected_ids.second)
      expect(results.third.id).to eq(expected_ids.third)
    end
  end

  context 'created_by desc' do
    let(:query_params) {
      {
        sort: "created_by desc",
        type: "people_list",
        user: company_user.user,
      }
    }

    let(:expected_ids) {
      ids = []
      ids << CustomFormValue.joins(:custom_form_field).find_by(custom_form_fields: { name: 'subject' }, value_str: "Greatest Moment").module_id
      ids << CustomFormValue.joins(:custom_form_field).find_by(custom_form_fields: { name: 'subject' }, value_str: "Boom boom").module_id
      ids << CustomFormValue.joins(:custom_form_field).find_by(custom_form_fields: { name: 'subject' }, value_str: "Working Hard").module_id
      ids
    }

    it 'returns 3 tickets' do
      expect(results.length).to eq(3)
    end

    it 'returns the correct order' do
      expect(results.first.id).to eq(expected_ids.first)
      expect(results.second.id).to eq(expected_ids.second)
      expect(results.third.id).to eq(expected_ids.third)
    end
  end
end
