require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::TagClause do
  create_company_and_user

  subject { described_class.new(query_params) }
  let(:company_user2) { create(:company_user, company: company)  }
  let(:custom_form) { company.custom_forms.helpdesk.first }

  let(:base) { HelpTicket.all.select("help_tickets.id") }
  let(:results) { subject.call(base) }
  let(:tag) { CustomFormField.field_attribute_types[:tag] }
  let!(:tag_field) { CustomFormField.create(name: 'tag', label: 'tag', field_attribute_type: tag, custom_form: custom_form) }
  let(:boom) { CustomFormValue.find_by(value_str: 'Boom boom').module }
  let(:greatest) { CustomFormValue.find_by(value_str: 'Greatest Moment').module }
  let(:working) { CustomFormValue.find_by(value_str: 'Working Hard').module }

  let(:query_params) { {} }

  let!(:help_tickets) do
    HelpTicket.destroy_all
    help_ticket_params.map{ |p| create_ticket(p) }
  end

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
        'tag' => 'Business Service',
      },
      {
        'Subject' => "Boom boom",
        'Created By' => '<EMAIL>',
        'tag' => 'SaaS',
      },
      {
        'Subject' => "Working Hard",
        'Created By' => '<EMAIL>',
      }
    ]
  }

  context 'with a tag of Business Service' do
    let(:query_params) {
      {
        tag: 'Business Service',
      }
    }

    it 'should return only one ticket' do
      expect(results.length).to eq(1)
    end

    it 'should include the ticket with tag Business Service' do
      expect(results.first).to eq(greatest)
    end
  end

  context 'with a tag of SaaS' do
    let(:query_params) {
      {
        tag: 'SaaS',
      }
    }
    it 'should return only one ticket' do
      expect(results.length).to eq(1)
    end

    it 'should include the ticket with tag SaaS' do
      expect(results.first).to eq(boom)
    end
  end

  context 'without any tag' do
    it 'should return all tickets' do
      expect(results.length).to eq(3)
    end
  end
end
