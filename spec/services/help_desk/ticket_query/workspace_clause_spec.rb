require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::WorkspaceClause do
  create_company_and_user

  subject { described_class.new(query_params) }
  let(:company_user2) { create(:company_user, company: company)  }

  let(:base) { HelpTicket.all }
  let(:results) { subject.call(base) }
  let(:workspace2) { create(:workspace, company: company) }
  let(:workspace3) { create(:workspace, company: company) }
  let(:boom) { CustomFormValue.find_by(value_str: 'Boom boom').module }
  let(:working) { CustomFormValue.find_by(value_str: 'Working Hard').module }
  let(:greatest) { CustomFormValue.find_by(value_str: 'Greatest Moment').module }
  let!(:custom_form) { company.custom_forms.last }

  let!(:help_tickets) do
    HelpTicket.destroy_all
    custom_form.workspace = company.default_workspace
    custom_form.save!
    help_ticket_params.map{ |p| create_ticket(p) }
    greatest.workspace = company.default_workspace
    greatest.save!
    boom.workspace = workspace2
    boom.save!
    working.workspace = workspace3
    working.save!
    HelpTicket.all
  end

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
      },
      {
        'Subject' => "Boom boom",
        'Created By' => '<EMAIL>',
      },
      {
        'Subject' => "Working Hard",
        'Created By' => company_user.contributor_id,
      }
    ]
  }

  context 'with a single workspace' do
    let(:query_params) {
      {
        workspaces: company.workspaces.where(id: company.default_workspace.id),
      }
    }

    it 'returns correct tickets' do
      expect(results.to_a).to eq([greatest])
    end
  end

  context 'with multiple workspaces' do
    let(:query_params) {
      {
        workspaces: company.workspaces.where.not(id: workspace3.id),
      }
    }

    it 'returns correct tickets' do
      expect(results.pluck(:id).sort).to eq([boom.id, greatest.id].sort)
    end
  end
end
