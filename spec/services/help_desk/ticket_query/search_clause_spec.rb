require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::SearchClause do
  create_company_and_user

  subject { described_class.new(query_params) }
  let(:company_user2) { create(:company_user, company: company)  }

  let(:base) { HelpTicket.all }
  let(:results) { subject.call(base) }
  let(:boom) { CustomFormValue.find_by(value_str: 'Boom boom').module }
  let(:greatest) { CustomFormValue.find_by(value_str: 'Greatest Moment').module }
  let!(:custom_form) { company.custom_forms.last }

  let!(:help_tickets) do
    HelpTicket.destroy_all
    custom_form.workspace = company.default_workspace
    custom_form.save!
    help_ticket_params.map{ |p| create_ticket(p) }
    sql = "UPDATE custom_form_values SET value_str_tokens = to_tsvector('simple', coalesce(value_str, '')) WHERE value_str IS NOT NULL;"
    ActiveRecord::Base.connection.execute sql
    HelpTicket.all
  end

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
        'Status' => 'In Progress',
        'Description' => '<div>This is a wonderful card.</div>'
      },
      {
        'Subject' => "Boom boom",
        'Created By' => '<EMAIL>',
        'Status' => 'Open',
        'Description' => '<div>Tick tick tick.</div>'
      }
    ]
  }

  context 'searching the description' do
    let(:query_params) {
      {
        search_terms: "tick",
        workspace_ids: company.workspaces.pluck(:id),
        company_user: company_user
      }
    }

    it 'returns the correct ticket' do
      expect(results.to_a).to eq([ boom ])
    end
  end

  context 'searching the subject' do
    let(:query_params) {
      {
        search_terms: "Moment",
        workspace_ids: company.workspaces.pluck(:id),
        company_user: company_user
      }
    }

    it 'returns the correct ticket' do
      expect(results.to_a).to eq([ greatest ])
    end
  end

  context 'searching the ticket number' do
    let(:query_params) {
      {
        search_terms: "#{boom.ticket_number}",
        workspace_ids: company.workspaces.pluck(:id),
        company_user: company_user
      }
    }

    it 'with exact ticket number returns the correct ticket' do
      expect(results.to_a).to eq([ boom ])
    end

    it "returns the tickets whose ticket number matches with partial ticket number" do
      greatest.update_columns(ticket_number: "11#{boom.ticket_number}")
    
      tickets = [boom, greatest]
      expect(results.find(tickets.map(&:id))).to all(be_present)
    end
  end
end
