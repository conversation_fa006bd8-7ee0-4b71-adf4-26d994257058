require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::InvolvedClause do
  create_company_and_user

  subject { described_class.new(query_params) }

  let(:base) { HelpTicket.all.select("help_tickets.id") }
  let(:results) { subject.call(base) }
  let(:company_user1) { create(:company_user, company: company) }
  let(:company_user2) { create(:company_user, company: company)  }

  let!(:help_tickets) do
    HelpTicket.destroy_all
    help_ticket_params.map{ |p| create_ticket(p) }
  end

  let(:greatest) { CustomFormValue.find_by(value_str: 'Greatest Moment').module }
  let(:working_hard) { CustomFormValue.find_by(value_str: 'Working Hard').module }
  let(:boom) { CustomFormValue.find_by(value_str: 'Boom boom').module }

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
        'Assigned To' => company_user.contributor_id,
        'Followers' => company_user.contributor_id,
        'Status' => 'In Progress',
        'Priority' => "low",
        'Description' => "What a great day!",
      },
      {
        'Subject' => "Working Hard",
        'Created By' => company_user2.contributor_id,
        'Assigned To' => company_user1.contributor_id,
        'Status' => 'In Progress',
        'Priority' => "low",
        'Description' => "Dogs are funny.",
      },
      {
        'Subject' => "Boom boom",
        'Created By' => '<EMAIL>',
        'Followers' => nil,
        'Impacted Devices' => nil,
        'Status' => 'Open',
        'Priority' => "medium",
        'Description' => "The red robin sits in the tree.",
      }
    ]
  }

  describe '#call' do
    context "for assigned to values" do
      context "with no assigned to values" do
        let(:query_params) {
          {
            ticket_limit: 25,
            ticket_offset: 0,
          }
        }
        
        it 'returns one ticket' do
          expect(results).to be_present
          expect(results.length).to eq(3)
        end
      end

      context "with assigned_contributor_ids" do
        let(:query_params) {
          {
            assigned_contributor_ids: [ company_user.contributor_id ],
            ticket_limit: 25,
            ticket_offset: 0,
          }
        }

        it 'returns one ticket' do
          expect(results).to be_present
          expect(results.length).to eq(1)
        end

        it 'returns all of the correct data' do
          expected_id = CustomFormValue.find_by(value_str: "Greatest Moment").module_id
          actual_id = results.first.id
          expect(actual_id).to eq(expected_id)
        end
      end

      context "with an unassigned contributor id" do
        let(:query_params) {
          {
            assigned_contributor_ids: [ 'unassigned' ],
          }
        }

        it 'returns one ticket' do
          # the search tests fail but the searching works when used.
          # if the old code for searching is used; the test pass, but the search fails.
          # : (
          expect(results).to be_present
          expect(results.length).to eq(1)
        end

        it 'returns all of the correct data' do
          # the search tests fail but the searching works when used.
          # if the old code for searching is used; the test pass, but the search fails.
          # : (
          ticket = CustomFormValue.find_by(value_str: "Boom boom").module
          data = results.first
          expect(data['id']).to eq(ticket.id)
        end
      end

      context "with an unassigned and a real contributor id" do
        let(:query_params) {
          {
            assigned_contributor_ids: [ 'unassigned', company_user.contributor_id ],
          }
        }

        it 'returns two tickets' do
          # the search tests fail but the searching works when used.
          # if the old code for searching is used; the test pass, but the search fails.
          # : (
          expect(results).to be_present
          expect(results.length).to eq(2)
        end

        it 'returns correct id\'s' do
          # the search tests fail but the searching works when used.
          # if the old code for searching is used; the test pass, but the search fails.
          # : (
          expected_ids = [ greatest.id, boom.id ].sort
          actual_ids = results.pluck(:id).sort
          expect(actual_ids).to eq(expected_ids)
        end
      end
    end

    context "for assigned to values" do
      context 'with a real contributor id' do
        let(:query_params) {
          {
            created_by_ids: [ company_user.contributor_id ],
          }
        }

        it 'returns one ticket' do
          expect(results.length).to eq(1)
        end

        it 'returns the first ticket' do
          expect(results.first).to eq(help_tickets.first)
        end
      end
    end
  end
end
