require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::CompletedFilterClause do
  create_company_and_user

  subject { described_class.new(query_params) }

  let(:base) { HelpTicket.all.select("help_tickets.id") }
  let(:results) { subject.call(base) }

  let!(:help_tickets) do
    HelpTicket.destroy_all
    help_ticket_params.map{ |p| create_ticket(p) }
  end

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
        'Assigned To' => company_user.contributor_id,
        'Followers' => company_user.contributor_id,
        'Status' => 'In Progress',
        'Priority' => "low",
        'Description' => "What a great day!",
      },
      {
        'Subject' => "Boom boom",
        'Created By' => company_user.contributor_id,
        'Followers' => nil,
        'Impacted Devices' => nil,
        'Status' => 'Open',
        'Priority' => "medium",
        'Description' => "The red robin sits in the tree.",
      }
    ]
  }

  describe 'with filter type completed_this_week' do
    let(:query_params) { { completed_ticket_filter: 'completed_this_week' } }

    context "with a task completed today" do
      before do
        help_tickets.first.project_tasks.create!(completed_at: Time.zone.now)
      end

      it 'returns one ticket' do
        expect(results.length).to eq(1)
      end

      it 'returns the first ticket' do
        expect(results.first).to eq(help_tickets.first)
      end
    end

    context "with a task completed last week" do
      before do
        help_tickets.first.project_tasks.create!(completed_at: 8.days.ago)
      end

      it 'returns no tickets' do
        expect(results).to be_blank
      end
    end
  end

  describe 'with filter type completed_this_month' do
    let(:query_params) { { completed_ticket_filter: 'completed_this_month' } }

    # TODO: FLAKY TEST CASE
    # context "with a task completed today" do
    #   before do
    #     help_ticket = help_tickets.first
    #     help_ticket.project_tasks.destroy_all
    #     help_ticket.project_tasks.create!(completed_at: Time.current)
    #     help_ticket.reload
    #   end

    # it 'returns one ticket' do
    #   expect(results.length).to eq(1)
    # end

    # it 'returns the first ticket' do
    #   expect(results.first).to eq(help_tickets.first)
    # end
    # end

    context "with a task completed last week" do
      before do
        help_tickets.first.project_tasks.create!(completed_at: 32.days.ago)
      end

      it 'returns no tickets' do
        expect(results).to be_blank
      end
    end
  end
end
