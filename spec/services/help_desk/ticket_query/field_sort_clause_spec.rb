require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::FieldSortClause do
  create_company_and_user

  subject { described_class.new(query_params) }

  let(:custom_form) { company.custom_forms.helpdesk.first }
  let(:vendor_list) { CustomFormField.field_attribute_types[:vendor_list] }
  let(:asset_list) { CustomFormField.field_attribute_types[:asset_list] }
  let(:location_list) { CustomFormField.field_attribute_types[:location_list] }
  let(:list) { CustomFormField.field_attribute_types[:list] }
  let(:text) { CustomFormField.field_attribute_types[:text] }

  let!(:vendor_field) { CustomFormField.create(name: 'vendor', label: 'Vendor', field_attribute_type: vendor_list, custom_form: custom_form) }
  let!(:asset_field) { CustomFormField.create(name: 'asset', label: 'Asset', field_attribute_type: asset_list, custom_form: custom_form) }
  let!(:list_field) { CustomFormField.create(name: 'list', label: 'List', field_attribute_type: list, custom_form: custom_form) }
  let!(:location_field) { 
    f = CustomFormField.find_by(name: 'location_list', field_attribute_type: location_list, custom_form: custom_form) 
    CustomFormValue.where(custom_form_field: f).destroy_all
    f
  }
  let!(:text_field) { CustomFormField.create(name: 'text', label: 'Text', field_attribute_type: text, custom_form: custom_form) }

  let(:vendor1) { create(:vendor, name: "Cookies by Rashid", company: company) }
  let(:vendor2) { create(:vendor, name: "Naeem's IT Repair", company: company) }
  let(:asset1) { create(:managed_asset, name: "Laptop", company: company) }
  let(:asset2) { create(:managed_asset, name: "PC", company: company) }

  let(:working_hard) { CustomFormValue.joins(:custom_form_field).find_by(custom_form_fields: { name: 'subject' }, value_str: "Working Hard").module_id }
  let(:greatest_moment) { CustomFormValue.joins(:custom_form_field).find_by(custom_form_fields: { name: 'subject' }, value_str: "Greatest Moment").module_id }
  let(:boom_boom) { CustomFormValue.joins(:custom_form_field).find_by(custom_form_fields: { name: 'subject' }, value_str: "Boom boom").module_id }

  let(:dev_pit) { 
    location = create(:location, company: company) 
    form = company.custom_forms.find_by(company_module: 'location')
    field = form.custom_form_fields.find_by(name: 'name')
    location.custom_form_values.destroy_all
    location.custom_form_values << CustomFormValue.new(custom_form: form, value_str: "Dev Pit", custom_form_field: field)
    location
  }
  let(:ground) { 
    location = create(:location, company: company) 
    form = company.custom_forms.find_by(company_module: 'location')
    field = form.custom_form_fields.find_by(name: 'name')
    location.custom_form_values.destroy_all
    location.custom_form_values << CustomFormValue.new(custom_form: form, value_str: "Ground", custom_form_field: field)
    location
  }
  let(:home) { 
    location = create(:location, company: company) 
    form = company.custom_forms.find_by(company_module: 'location')
    field = form.custom_form_fields.find_by(name: 'name')
    location.custom_form_values.destroy_all
    location.custom_form_values << CustomFormValue.new(custom_form: form, value_str: "Home", custom_form_field: field)
    location
  }
  let(:company_user2) { 
    cu = create(:company_user, company: company)  
    cu.user.first_name = 'Aaalf'
    cu.user.last_name = 'Aaandrews'
    cu.user.save!
    cu
  }
  let(:company_user1) {
    company_user.user.first_name = 'Doug'
    company_user.user.last_name = 'Jones'
    company_user.user.save!
    company_user
  }

  let(:base) { HelpTicket.all.select("help_tickets.id").group("help_tickets.id") }
  let(:results) { subject.call(base) }

  let!(:help_tickets) do
    HelpTicket.destroy_all
    tickets = help_ticket_params.map{ |p| create_ticket(p) }

    # remove any data that was created by the default factory
    form = company.custom_forms.helpdesk.first
    field = form.custom_form_fields.find_by(name: 'location_list')
    loc = company.locations.order(:created_at).first
    CustomFormValue.where(custom_form_field: field, value_int: loc).destroy_all

    tickets
  end

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user1.contributor_id,
        'Vendor' => vendor1.id,
        'Asset' => asset2.id,
        'Location List' => home.id,
        'Text' => 'bang!'
      },
      {
        'Subject' => "Boom boom",
        'Created By' => '<EMAIL>',
        'Vendor' => vendor2.id,
        'Staff' => company_user2.contributor_id,
        'Location List' => dev_pit.id,
        'List' => 'dog'
      },
      {
        'Subject' => "Working Hard",
        'Created By' => company_user2.contributor_id,
        'Asset' => asset1.id,
        'Staff' => company_user1.contributor_id,
        'List' => 'cat',
        'Text' => 'Booyah!',
        'Location List' => ground.id
      }
    ]
  }

  context 'vendor asc' do
    let(:query_params) {
      {
        sort: "vendor asc",
        company: company,
        type: 'vendor_list'
      }
    }

    let(:expected_ids) {
      [ greatest_moment, boom_boom, working_hard ]
    }

    it 'returns 3 tickets' do
      expect(results.length).to eq(3)
    end

    it 'returns the correct order' do
      expect(results.pluck(:id)).to eq(expected_ids)
    end
  end

  context 'vendor desc' do
    let(:query_params) {
      {
        sort: "vendor desc",
        company: company,
        type: 'vendor_list'
      }
    }

    let(:expected_ids) {
      [ working_hard,  boom_boom, greatest_moment ]
    }

    it 'returns 3 tickets' do
      expect(results.length).to eq(3)
    end

    it 'returns the correct order' do
      expect(results.pluck(:id)).to eq(expected_ids)
    end
  end

  context 'location asc' do
    let(:query_params) {
      {
        sort: "location_list asc",
        company: company,
        workspaces: company.workspaces,
        type: "location_list"
      }
    }

    let(:expected_ids) {
      [ boom_boom, working_hard, greatest_moment ]
    }

    it 'returns 3 tickets with correct order' do
      # There is something going on with this spec that I haven't figured out yet.
      # I tested this out manually, and it's working.  I'll fix this later when I have a moment.
      expect(results.length).to eq(3)
      expect(results.pluck(:id)).to eq(expected_ids)
    end
  end

  context 'location desc' do
    let(:query_params) {
      {
        sort: "location_list desc",
        company: company,
        workspaces: company.workspaces,
        type: "location_list"
      }
    }

    let(:expected_ids) {
      [ greatest_moment, working_hard , boom_boom ]
    }

    it 'returns 3 tickets and check the correct order' do
      expect(results.length).to eq(3)
      expect(results.pluck(:id)).to eq(expected_ids)
    end
  end

  context 'list asc' do
    let(:query_params) {
      {
        sort: "list asc",
        company: company,
        workspaces: company.workspaces,
        type: "list"
      }
    }

    let(:expected_ids) {
      [ working_hard, boom_boom, greatest_moment ]
    }

    it 'returns 3 tickets' do
      expect(results.length).to eq(3)
    end

    it 'returns the correct order' do
      expect(results.pluck(:id)).to eq(expected_ids)
    end
  end

  context 'list desc' do
    let(:query_params) {
      {
        sort: "list desc",
        company: company,
        workspaces: company.workspaces,
        type: "list"
      }
    }

    let(:expected_ids) {
      [ boom_boom, working_hard, greatest_moment ]
    }

    it 'returns 3 tickets' do
      expect(results.length).to eq(3)
    end

    it 'returns the correct order' do
      expect(results.pluck(:id)).to eq(expected_ids)
    end
  end

  context 'text asc' do
    let(:query_params) {
      {
        sort: "text asc",
        company: company,
        workspaces: company.workspaces,
        type: "text"
      }
    }

    let(:expected_ids) {
      [ greatest_moment, working_hard, boom_boom ]
    }

    it 'returns 3 tickets' do
      expect(results.length).to eq(3)
    end

    it 'returns the correct order' do
      expect(results.pluck(:id)).to match_array(expected_ids)
    end
  end

  context 'text desc' do
    let(:query_params) {
      {
        sort: "text desc",
        company: company,
        workspaces: company.workspaces,
        type: "text"
      }
    }

    let(:expected_ids) {
      [ working_hard, greatest_moment, boom_boom ]
    }

    it 'returns 3 tickets' do
      expect(results.length).to eq(3)
    end

    it 'returns the correct order' do
      expect(results.pluck(:id)).to match_array(expected_ids)
    end
  end
end
