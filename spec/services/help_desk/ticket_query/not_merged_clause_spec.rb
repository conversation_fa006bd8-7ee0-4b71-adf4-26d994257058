require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe HelpDesk::TicketQuery::NotMergedClause do
  create_company_and_user

  subject { described_class.new(query_params) }
  let(:company_user2) { create(:company_user, company: company)  }

  let(:base) { HelpTicket.all }
  let(:results) { subject.call(base) }
  let(:boom) { CustomFormValue.find_by(value_str: 'Boom boom').module }
  let(:greatest) { CustomFormValue.find_by(value_str: 'Greatest Moment').module }

  let!(:help_tickets) do
    HelpTicket.destroy_all
    help_ticket_params.map{ |p| create_ticket(p) }
  end

  let(:help_ticket_params) {
    [
      {
        'Subject' => "Greatest Moment",
        'Created By' => company_user.contributor_id,
      },
      {
        'Subject' => "Boom boom",
        'Created By' => '<EMAIL>',
      }
    ]
  }

  context 'with a merged ticket' do
    let!(:merged_ticket) { 
      greatest.active_ticket_id = boom.id
      greatest.save!
    }
    context 'without search terms present' do
      let(:query_params) { {} }

      it 'should exclude the merged ticket' do
        expect(results.length).to eq(1)
      end

      it 'returns the unmerged ticket' do
        expect(results.first).to eq(boom)
      end
    end

    context 'with search terms present' do
      let(:query_params) {
        {
          search_terms: 'lorem ipsum',
          statuses: 'Closed'
        }
      }
  
      it 'returns all tickets' do
        expect(results.length).to eq(2)
      end
    end
  end
end
