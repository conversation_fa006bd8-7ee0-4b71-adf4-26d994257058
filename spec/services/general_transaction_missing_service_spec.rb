require 'rails_helper'

describe GeneralTransactionMissingService do
  let!(:company) { FactoryBot.create(:company) }

  Timecop.freeze(Date.today)

  context "adding all missing transactions up to the current date" do
    let!(:vendor) { FactoryBot.create(:vendor, company_id: company.id) }
    let!(:transaction) {
      FactoryBot.create(:general_transaction,
                        vendor_id: vendor.id,
                        recurring: true,
                        status: :recognized,
                        company_id: company.id,
                        amount: 200,
                        recurrance_end_date: Date.today + 2.years,
                        transaction_date: Date.today - 4.months
      )
    }

    let!(:later_transaction) {
      FactoryBot.create(:general_transaction,
                        vendor_id: vendor.id,
                        status: :archived,
                        company_id: company.id,
                        transaction_recurred_from_id: transaction.id,
                        amount: 200,
                        transaction_date: transaction.transaction_date + 2.months
      )
    }

    subject { described_class.new(Date.today) }

    before do
      Timecop.freeze(Date.today)
      subject.check_missing_transactions
    end

    after do
      Timecop.return
    end

    it "should create the missing transactions with today as the transaction date" do
      subject.check_missing_transactions
      expect(company.general_transactions.count).to eq(5)
      expect(company.general_transactions.last.transaction_date).to eq(Date.today)
    end

    it "should skip the month that already has a transaction even if archived" do
      transaction_months = company.general_transactions.pluck(:transaction_date).map{ |t| t.month }
      expect(transaction_months).to eq(transaction_months.uniq)
    end
  end

  context "adding all missing transactions up to the current date" do
    let!(:vendor2) { FactoryBot.create(:vendor, company_id: company.id, name: "Another Vendor") }
    let!(:transaction2) {
      FactoryBot.create(:general_transaction,
                        vendor_id: vendor2.id,
                        recurring: true,
                        status: :recognized,
                        company_id: company.id,
                        amount: 100,
                        recurrance_end_date: Date.today + 2.years,
                        transaction_date: Date.today - 4.months
      )
    }

    subject { described_class.new(Date.today - 2.months) }

    before do
      Timecop.freeze(Date.today)
    end

    after do
      Timecop.return
    end

    it "should create the missing transactions only up to the specified date" do
      subject.check_missing_transactions
      expect(company.general_transactions.count).to eq(3)
      expect(company.general_transactions.last.transaction_date).to eq(Date.today - 2.months)
    end
  end
end
