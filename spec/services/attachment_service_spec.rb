require 'rails_helper'

describe AttachmentService do
  let(:html_string) { "<div><figure data-trix-attachment='{\"attachmentId\":99,\"contentType\":\"image/jpeg\",\"filename\":\"clement-h-95YRwf6CNw8-unsplash.jpg\",\"filesize\":2038885,\"height\":4000,\"target\":\"_blank\",\"url\":\"https://s3.amazonaws.com/nulodgic-development/attachment_uploads/attachments/000/000/099/original/clement-h-95YRwf6CNw8-unsplash.jpg?1602872702\",\"width\":6000}' data-trix-content-type=\"image/jpeg\" data-trix-attributes='{\"caption\":\"The laptop in question\",\"presentation\":\"gallery\"}' class=\"attachment attachment--preview attachment--jpg\"><img src=\"https://s3.amazonaws.com/nulodgic-development/attachment_uploads/attachments/000/000/099/original/clement-h-95YRwf6CNw8-unsplash.jpg?1602872702\" width=\"1200\" height=\"800\"><figcaption class=\"attachment__caption small font-italic\">The laptop in question</figcaption></figure></div>" }
  subject { described_class.new({ html: html_string, max_width: 600 }) }

  context "resizing an image width and height" do
    it "should return a new HTML string" do
      response = subject.resize_image_html
      # Note the difference in the <img> width and height attributes.
      expect(response).to eq("<div><figure data-trix-attachment='{\"attachmentId\":99,\"contentType\":\"image/jpeg\",\"filename\":\"clement-h-95YRwf6CNw8-unsplash.jpg\",\"filesize\":2038885,\"height\":4000,\"target\":\"_blank\",\"url\":\"https://s3.amazonaws.com/nulodgic-development/attachment_uploads/attachments/000/000/099/original/clement-h-95YRwf6CNw8-unsplash.jpg?1602872702\",\"width\":6000}' data-trix-content-type=\"image/jpeg\" data-trix-attributes='{\"caption\":\"The laptop in question\",\"presentation\":\"gallery\"}' class=\"attachment attachment--preview attachment--jpg\"><img src=\"https://s3.amazonaws.com/nulodgic-development/attachment_uploads/attachments/000/000/099/original/clement-h-95YRwf6CNw8-unsplash.jpg?1602872702\" width=\"100%\" height=\"100%\"><figcaption class=\"attachment__caption small font-italic\">The laptop in question</figcaption></figure></div>")
    end
  end
end