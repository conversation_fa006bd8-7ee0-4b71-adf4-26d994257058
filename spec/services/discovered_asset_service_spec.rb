# frozen_string_literal: true

require 'rails_helper'

describe 'creates a discovered asset' do
  let!(:company_user) { FactoryBot.create(:company_user) }
  let!(:company) { company_user.company }
  let(:display_name) { 'DESKTOP-MLNFTHJ' }
  let(:serial_no) { 'CNTE1980' }
  let(:expected_asset) {
    DiscoveredAsset.new(
      display_name: "#{display_name}",
      mac_address: "60:45:CB:9A:58:1F",
      ip_address: "************",
      manufacturer: "System manufacturer",
      os_name: "Microsoft Windows 10 Home",
      os_version: "10.0.17134",
      os_serial_no: "00326-10870-80315-AA643",
      machine_serial_no: "#{serial_no}"
    )
  }
  let(:json_payload) do
    json_string =<<END
    {
      "managedAsset": {
        "companyUserGuid": "#{company_user.guid}",
        "companyGuid": "#{company.guid}",
        "macAddress": "60:45:CB:9A:58:1F",
        "assetType": null,
        "processorCores": "8 Cores",
        "processorName": "AMD Ryzen 7 1700X Eight-Core Processor ",
        "memory": "32 GB",
        "osName": "Microsoft Windows 10 Home",
        "osVersion": "10.0.17134",
        "osSerialNo": "00326-10870-80315-AA643",
        "ipAddress": "************",
        "windowProductKey": null,
        "windowsInstallationDate": "2018/05/27-20:05:22",
        "diskSpace": "932 GB",
        "manufacturer": "System manufacturer",
        "model": "System Product Name",
        "displayName": "#{display_name}",
        "pcSerialNo": "#{serial_no}",
        "deviceType": "Desktop"
      }
    }
END
    JSON.parse(json_string)
  end

  let!(:probe_location) { company.probe_locations.create(
                                                  computer_name: "#{display_name}",
                                                  mac_addresses: ["60:45:CB:9A:58:1F"],
                                                  ip_address: "************",
                                                  machine_serial_number: "#{serial_no}"
                                                ) }
  subject { DiscoveredAssetService.new(json_payload) }

  context 'with an existing discovered asset' do
    let!(:managed_asset) do
      desktop = CompanyAssetType.find_by(name: 'Desktop')
      asset = company.discovered_assets.create(display_name: display_name, machine_serial_no: serial_no)
      asset
    end

    it 'should update the existing discovered asset' do
      subject.update(probe_location)
      expect(company.discovered_assets.where(display_name: display_name).count).to eq(1)
      actual = company.discovered_assets.find_by(display_name: display_name)
      expect(actual.probe_location).to eq(probe_location)
      assert_assets_equal(actual)
    end
  end

  context 'without an existing discovered asset' do
    it 'should create a new discovered asset' do
      company.discovered_assets.destroy_all
      subject.update(probe_location)
      expect(company.discovered_assets.where(display_name: display_name).count).to eq(1)
      actual = company.discovered_assets.find_by(display_name: display_name)
      expect(actual.probe_location).to eq(probe_location)
      assert_assets_equal(actual)
    end
  end

  def assert_assets_equal(actual)
    %w{
      display_name
      mac_addresses
      ip_address
      manufacturer
      machine_serial_no
      os_name
      os_version
      os_serial_no
    }.each do |attr|
      expect(expected_asset.send(attr).presence).to eq(actual.send(attr).presence)
    end
  end
end
