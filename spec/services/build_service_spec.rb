# frozen_string_literal: true

require 'rails_helper'

describe 'build service' do
  let!(:company_user) { FactoryBot.create(:company_user) }
  let!(:company) { company_user.company }
  let!(:headers) {
    {
      :content_type => 'application/json'
    }
  }

  subject { BuildService.new(app_download) }

  %w{ selfdiscovery windowsagent networkdiscovery }.each do |app_name|
    context "with application #{app_name}" do
      let(:app_download) { FactoryBot.create(:app_download, 
                                             company_user_guid: company_user.guid, 
                                             company_guid: company.guid,
                                             application: app_name ) 
      }
      let(:json_request) {
        json_body =<<END
    {
      "application": "#{app_name}",
      "callbackUrl": "",
      "companyGuid": "#{company.guid}",
      "companyUserGuid": "#{company_user.guid}"
    }
END
        json_body
      }

      it 'send the appropriate message to the build service' do
        expect(RestClient).to receive(:post).
                              with(Rails.application.credentials.build_url, json_request, headers).
                              and_return(OpenStruct.new(body: ''))
        subject.build
      end
    end
  end
end
