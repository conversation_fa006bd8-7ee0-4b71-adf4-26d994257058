require 'rails_helper'
include CompanyUserHelper

RSpec.describe CustomFormFieldsService, type: :service do
  create_company_and_user

  let!(:workspace) { company.workspaces.first }
  let!(:custom_form) { company.custom_forms.helpdesk.first }
  let!(:status_field) do
    create(:custom_form_field,
           custom_form: custom_form,
           name: 'Status',
           label: 'Status Field',
           field_attribute_type: 'status',
           options: ["Open", "Closed", "In Progress"])
  end
  let(:params) { { id: status_field.id, options: ['Open', 'Closed'] } }

  subject { described_class.new(params, user, company, workspace) }

  describe '#get_form_fields' do
    it 'returns a list of available field types' do
      result = subject.get_form_fields
      expect(result).to include({ field_type: 'status' })
      expect(result.count).to eq(25)
    end
  end

  describe '#show_form_field' do
    context 'with valid options' do
      it 'returns no errors' do
        errors = subject.show_form_field
        expect(errors).to be_empty
      end
    end

    context 'when removing default options' do
      it 'returns an error' do
        params = ActionController::Parameters.new(
          id: status_field.id,
          options: ['Open', 'Closed', { "name" => "Invalid" }.to_json]
        )
        subject = described_class.new(params, user, company, workspace)
        errors = subject.show_form_field
        expect(errors).to include("Reserved keywords and duplicate names are not allowed. Please use different names.")
      end
    end
  end

  describe '#update_form_field' do
    context 'with valid field_id' do
      let(:update_params) { { field: { options: ['Updated Option'] } } }

      it 'updates the field successfully' do
        allow(subject).to receive(:field_id).and_return(status_field.id)
        allow(subject).to receive(:field_params).and_return(update_params[:field])
        result = subject.update_form_field
        expect(result[:field]).to eq(status_field)
        expect(status_field.reload.options).to include('Updated Option')
      end
    end
  end

  describe '#fetch_all_form_fields' do
    context 'when workspace has fields' do
      it 'returns all fields scoped to the workspace' do
        result = subject.fetch_all_form_fields
        expect(result[:fields]).to include(
          { name: status_field.name, order_position: status_field.order_position, field_attribute_type: status_field.field_attribute_type }
        )
      end
    end
  end
end
