require 'rails_helper'
require 'ostruct'
include CompanyUserHelper
include HelpTicketHelper

describe CustomForms::ValueUpdate do
  create_company_and_user
  subject { described_class.new(help_ticket, company_user) }
  let!(:result) { subject.call(params) }
  let(:custom_form) { company.custom_forms.helpdesk.first }
  let!(:company_user2) { create(:company_user, company: company_user.company) }
  let!(:company_user3) { create(:company_user, company: company_user.company) }

  describe '#call' do
    context "with a singular valued field" do
      context "with params with a label" do
        let(:params) {
          {
            name: 'priority',
            value: 'medium',
          }
        }
        let!(:help_ticket) do
          HelpTicket.destroy_all
          create_ticket(help_ticket_params)
        end
        context "with custom form values" do
          context "with a custom form value id" do
            let(:form_field) {
              custom_form.custom_form_fields.find_by(name: 'priority')
            }
            let(:params) {
              {
                custom_form_value_id: help_ticket.custom_form_values.find_by(custom_form_field: form_field).id,
                value: 'medium',
              }
            }
            let(:help_ticket_params) {
              {
                'Subject' => "Test Ticket",
                'Created By' => company_user.contributor_id.to_s,
                'Assigned To' => nil,
                'Status' => 'Open',
                'Priority' => "low",
                'Description' => "This is a test ticket.",
              }
            }
            it 'returns true' do
              expect(result).to be_truthy
            end

            it 'creates a custom form value' do
              expect(subject.form_value).to be_present
              my_ticket = help_ticket.reload
              my_field = my_ticket.custom_form.custom_form_fields.find_by(name: 'priority')
              value = my_ticket.custom_form_values.find_by(custom_form_field: my_field)
              expect(value.value).to eq("medium")
            end
          end

          context "without a custom form value id" do
            let(:help_ticket_params) {
              {
                'Subject' => "Test Ticket",
                'Created By' => company_user.contributor_id.to_s,
                'Assigned To' => nil,
                'Status' => 'Open',
                'Priority' => "low",
                'Description' => "This is a test ticket.",
              }
            }
            it 'returns true' do
              expect(result).to be_truthy
            end

            it 'creates a custom form value' do
              expect(subject.form_value).to be_present
              my_ticket = help_ticket.reload
              form_field = my_ticket.custom_form.custom_form_fields.find_by(name: 'priority')
              value = my_ticket.custom_form_values.find_by(custom_form_field: form_field)
              expect(value.value).to eq("medium")
            end
          end
        end

        context "without custom form values" do
          let(:help_ticket_params) {
            {
              'Subject' => "Test Ticket",
              'Created By' => company_user.contributor_id.to_s,
              'Assigned To' => nil,
              'Status' => 'Open',
              'Description' => "This is a test ticket.",
            }
          }
          it 'returns true' do
            expect(result).to be_truthy
          end

          it 'creates a custom form value' do
            expect(subject.form_value).to be_present
            my_ticket = help_ticket.reload
            form_field = my_ticket.custom_form.custom_form_fields.find_by(name: 'priority')
            value = my_ticket.custom_form_values.find_by(custom_form_field: form_field)
            expect(value.value).to eq("medium")
          end
        end
      end

      context "with params with a custom_form_field_id" do
        let(:custom_form_field_id) {
          custom_form.custom_form_fields.find_by(name: 'priority').id
        }

        let(:params) {
          {
            custom_form_field_id: custom_form_field_id,
            value: 'medium',
          }
        }

        let!(:help_ticket) do
          HelpTicket.destroy_all
          create_ticket(help_ticket_params)
        end

        context "with custom form values" do
          let(:help_ticket_params) {
            {
              'Subject' => "Test Ticket",
              'Created By' => company_user.contributor_id.to_s,
              'Assigned To' => nil,
              'Status' => 'Open',
              'Priority' => "low",
              'Description' => "This is a test ticket.",
            }
          }

          it 'returns true' do
            expect(result).to be_truthy
          end

          it 'creates a custom form value' do
            expect(subject.form_value).to be_present
            my_ticket = help_ticket.reload
            form_field = my_ticket.custom_form.custom_form_fields.find_by(name: 'priority')
            value = my_ticket.custom_form_values.find_by(custom_form_field: form_field)
            expect(value.value).to eq("medium")
          end
        end

        context "without custom form values" do
          let(:help_ticket_params) {
            {
              'Subject' => "Test Ticket",
              'Created By' => company_user.contributor_id.to_s,
              'Assigned To' => nil,
              'Status' => 'Open',
              'Description' => "This is a test ticket.",
            }
          }
          it 'returns true' do
            expect(result).to be_truthy
          end

          it 'creates a custom form value' do
            expect(subject.form_value).to be_present
            my_ticket = help_ticket.reload
            form_field = my_ticket.custom_form.custom_form_fields.find_by(name: 'priority')
            value = my_ticket.custom_form_values.find_by(custom_form_field: form_field)
            expect(value.value).to eq("medium")
          end
        end
      end
    end

    context "with a multiple valued field" do
      let(:contributor_id) { company.company_users.first.contributor_id }
      context "with params with a label" do
        let(:params) {
          {
            name: 'assigned_to',
            value: contributor_id,
          }
        }
        let!(:help_ticket) do
          HelpTicket.destroy_all
          create_ticket(help_ticket_params)
        end

        context "with custom form values" do
          context "with a custom form value id" do
            let(:form_field) {
              help_ticket.custom_form.custom_form_fields.find_by(name: 'assigned_to')
            }
            let(:params) {
              {
                custom_form_value_id: help_ticket.custom_form_values.find_by(custom_form_field: form_field).id,
                value: company.company_users.first.contributor_id,
              }
            }
            let(:help_ticket_params) {
              {
                'Subject' => "Test Ticket",
                'Created By' => company_user.contributor_id.to_s,
                'Assigned To' => [contributor_id],
                'Status' => 'Open',
                'Description' => "This is a test ticket.",
              }
            }
            it 'returns true' do
              expect(result).to be_truthy
            end

            it 'creates a custom form value' do
              expect(subject.form_value).to be_present
              my_ticket = help_ticket.reload
              my_field = my_ticket.custom_form.custom_form_fields.find_by(name: 'assigned_to')
              value = my_ticket.custom_form_values.find_by(custom_form_field: my_field)
              expect(value.value).to eq(contributor_id)
            end
          end

          context "without a custom form value id" do
            let(:help_ticket_params) {
              {
                'Subject' => "Test Ticket",
                'Created By' => company_user.contributor_id.to_s,
                'Assigned To' => [company_user2.contributor_id],
                'Status' => 'Open',
                'Description' => "This is a test ticket.",
              }
            }
            it 'returns true' do
              expect(result).to be_truthy
            end

            it 'creates a custom form value' do
              expect(subject.form_value).to be_present
              my_ticket = create_ticket(help_ticket_params)
              form_field = my_ticket.custom_form.custom_form_fields.find_by(name: 'assigned_to')
              value = my_ticket.custom_form_values.find_by(custom_form_field: form_field)
              expect(value.value).to eq(company_user2.contributor_id)
            end
          end
        end

        context "without custom form values" do
          let(:help_ticket_params) {
            {
              'Subject' => "Test Ticket",
              'Created By' => company_user.contributor_id.to_s,
              'Assigned To' => nil,
              'Status' => 'Open',
              'Description' => "This is a test ticket.",
            }
          }

          it 'returns true' do
            expect(result).to be_truthy
          end

          it 'creates a custom form value' do
            expect(subject.form_value).to be_present
            my_ticket = help_ticket.reload
            form_field = my_ticket.custom_form.custom_form_fields.find_by(name: 'assigned_to')
            values = my_ticket.custom_form_values.where(custom_form_field: form_field).order(:created_at)
            expect(values.count).to eq(1)
            expect(values.last.value).to eq(contributor_id)
          end
        end
      end

      context "with params with a custom_form_field_id" do
        let(:custom_form_field_id) {
          custom_form.custom_form_fields.find_by(name: 'assigned_to').id
        }
        let(:params) {
          {
            custom_form_field_id: custom_form_field_id,
            value: contributor_id,
          }
        }
        let!(:help_ticket) do
          HelpTicket.destroy_all
          create_ticket(help_ticket_params)
        end

        context "with custom form values" do
          let!(:help_ticket_params) {
            {
              'Subject' => "Test Ticket",
              'Created By' => company_user.contributor_id.to_s,
              'Assigned To' => [company_user3.contributor_id],
              'Status' => 'Open',
              'Description' => "This is a test ticket.",
            }
          }
          it 'returns true' do
            expect(result).to be_truthy
          end

          it 'creates a custom form value' do
            HelpTicket.destroy_all
            my_ticket = create_ticket(help_ticket_params)
            form_field = my_ticket.custom_form.custom_form_fields.find_by(name: 'assigned_to')
            value = my_ticket.custom_form_values.find_by(custom_form_field: form_field)
            expect(subject.form_value).to be_present
            expect(value.value).to eq(company_user3.contributor_id)
          end
        end

        context "without custom form values" do
          let(:help_ticket_params) {
            {
              'Subject' => "Test Ticket",
              'Created By' => company_user.contributor_id.to_s,
              'Assigned To' => nil,
              'Status' => 'Open',
              'Description' => "This is a test ticket.",
            }
          }
          it 'returns true' do
            expect(result).to be_truthy
          end

          it 'creates a custom form value' do
            HelpTicket.destroy_all
            my_ticket = create_ticket(help_ticket_params)
            form_field = my_ticket.custom_form.custom_form_fields.find_by(name: 'assigned_to')
            values = my_ticket.custom_form_values.where(custom_form_field: form_field).order(:created_at)
            expect(values.count).to eq(0)
          end
        end
      end
    end
  end
end
