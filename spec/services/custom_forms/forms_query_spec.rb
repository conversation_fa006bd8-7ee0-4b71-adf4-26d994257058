require 'rails_helper'
require 'ostruct'
include CompanyUserHelper

describe CustomForms::FormsQuery do
  create_company_and_user

  let(:workspace) { company.workspaces.first }
  let!(:general_request) {
    company.custom_forms.helpdesk.order(:created_at).first
  }
  let!(:account_request) { 
    CustomForm.create(company_id: company.id,
                      company_module: "helpdesk",
                      form_name: "Account Request",
                      workspace_id: workspace.id,
                      order: 1) 
  }
  let!(:asset_request) { 
    CustomForm.create(company_id: company.id,
                      company_module: "helpdesk",
                      form_name: "Asset Request",
                      workspace_id: workspace.id,
                      order: 2)
  }
  let!(:hardware_request) { 
    CustomForm.create(company_id: company.id,
                      company_module: "helpdesk",
                      form_name: "Hardware Request",
                      workspace_id: workspace.id,
                      is_active: false,
                      order: 4)
  }
  let!(:base_location) {
    company.custom_forms.location.find_by(form_name: "Base Location")
  }
  let!(:satellite_office) {
    CustomForm.create(company_id: company.id,
                      company_module: "location",
                      form_name: "Satellite Office",
                      is_active: false,
                      order: 2)
  }
  let(:query_params) do
    my_params = {
      company_id: company.id,
      status: status,
      company_module: company_module,
    }
    my_params[:workspace_id] = company.workspaces.first.id if company_module == 'helpdesk'
    my_params
  end
  let(:result) { subject.call }

  subject { described_class.new(query_params) }

  describe '#call' do
    context "with helpdesk company module " do
      let(:company_module) { "helpdesk" }

      context "with 'all' status params" do
        let(:status) { 'all' }

        it 'returns all of the custom forms ordered by the order attribute' do
          expect(result.count).to eq(4)
          expect(result.first).to eq(account_request)
          expect(result.second).to eq(asset_request)
          expect(result.third).to eq(hardware_request)
          expect(result.fourth).to eq(general_request)
        end
      end

      context "with 'active' status params" do
        let(:status) { 'active' }

        it 'returns all of the custom forms' do
          expect(result.count).to eq(3)
          expect(result.include?(hardware_request)).to eq(false)
        end
      end

      context "with 'archived' status params" do
        let(:status) { 'archived' }

        it 'returns all of the custom forms' do
          expect(result.count).to eq(1)
          expect(result.first).to eq(hardware_request)
        end
      end
    end

    context "with location company module" do
      let(:company_module) { "location" }

      context "with 'all' status params" do
        let(:status) { 'all' }

        it 'returns all of the custom forms ordered by the order attribute' do
          expect(result.count).to eq(2)
          expect(result.first).to eq(base_location)
          expect(result.second).to eq(satellite_office)
        end
      end

      context "with 'active' status params" do
        let(:status) { 'active' }

        it 'returns all of the custom forms' do
          expect(result.count).to eq(1)
          expect(result.first).to eq(base_location)
        end
      end

      context "with 'archived' status params" do
        let(:status) { 'archived' }

        it 'returns all of the custom forms' do
          expect(result.count).to eq(1)
          expect(result.first).to eq(satellite_office)
        end
      end
    end
  end
end
