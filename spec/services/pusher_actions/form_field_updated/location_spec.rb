require 'rails_helper'
include CompanyUserHelper
include LocationHelper

RSpec.describe PusherActions::FormFieldUpdated::Location do
  describe 'form_field_updated pusher events' do
    create_company_and_user
    let!(:location) { create(:location, custom_form: company.custom_forms.where(company_module: "location").first, company: company) }
    let(:form) { company.custom_forms.find_by(company_module: "location") }
    let(:field) { form.custom_form_fields.find_by(name: "name") }
    let(:custom_form_value) { 
      build(:custom_form_value, 
            company: company, 
            custom_form: form,
            value_int: nil,
            value_str: "my new location",
            custom_form_field: field,
            module: location
      )
    }

    it "should return the array of pusher event" do
      result = described_class.new(custom_form_value).call
      result = result[0]
      expect(result[:channel]).to eq("location=#{location.guid}")
      expect(result[:payload][:id]).to eq(field.id)
    end
  end
end
