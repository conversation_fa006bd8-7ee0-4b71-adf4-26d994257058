require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

RSpec.describe PusherActions::FormFieldUpdated::HelpTicket do
  describe 'form_field_updated pusher events' do
    create_company_and_user
    let(:ticket) { create_ticket }
    let(:form) { ticket.company.custom_forms.find_by(company_module: "helpdesk") }
    let(:field) { form.custom_form_fields.find_by(name: "subject") }
    let(:priority_field) { form.custom_form_fields.find_by(name: "priority") }
    let(:custom_form_value) { 
      build(:custom_form_value, 
            company: company, 
            custom_form: form,
            value_int: nil,
            value_str: "new ticket",
            custom_form_field: field,
            module: ticket
      )
    }
    let(:priority_custom_form_value) { 
      build(:custom_form_value, 
            company: company, 
            custom_form: form,
            value_int: nil,
            value_str: "low",
            custom_form_field: priority_field,
            help_ticket: ticket
      )
    }
    
    before do
      TicketSession.create(help_ticket_id: ticket.id)
    end

    it "should return the array of pusher event" do
      result = described_class.new(custom_form_value).call
      result = result[0]
      expect(result[:channel]).to eq("help_ticket=#{ticket.guid}")
      expect(result[:payload][:id]).to eq(field.id)
    end

    it "should return the array of pusher event for priority" do
      result = PusherActions::HelpTickets::TicketDueTime.new(priority_custom_form_value).call
      priority_result = result[0]
      expect(priority_result[:channel]).to eq("help_ticket=#{ticket.guid}")
    end
  end
end
