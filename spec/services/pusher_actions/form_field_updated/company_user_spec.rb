require 'rails_helper'
include CompanyUserHelper

RSpec.describe PusherActions::FormFieldUpdated::CompanyUser do
  describe 'form_field_updated pusher events' do
    create_company_and_user
    let(:form) { company.custom_forms.find_by(company_module: "company_user") }
    let(:field) { form.custom_form_fields.find_by(name: "location") }
    let(:custom_form_value) { 
      build(:custom_form_value, 
            company: company, 
            custom_form: form,
            value_int: nil,
            value_str: "test location",
            custom_form_field: field,
            module: company_user
      )
    }
    it "should return the array of pusher event" do
      result = described_class.new(custom_form_value).call
      result = result[0]
      expect(result[:channel]).to eq("company_user=#{company_user.guid}")
      expect(result[:payload][:id]).to eq(field.id)
    end
  end
end
