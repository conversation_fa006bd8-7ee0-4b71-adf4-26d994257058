require 'rails_helper'
include CompanyUserHelper

RSpec.describe PusherActions::HelpTickets::PermissionsUpdated do
  describe 'permissions_updated pusher events' do
    create_company_and_user
    let(:workspace) { company.default_workspace }
    let(:payload) { { contributor_id: company_user.contributor_id, workspace_id: workspace.id } }
    
    it "should return the array of pusher event for company_user permissions" do
      result = (described_class.new(payload).call)[0]
      expect(result[:channel]).to eq("workspace=#{workspace.id}")
      expect(result[:payload][:contributor_id]).to eq(company_user.contributor_id)
    end
  end
end
