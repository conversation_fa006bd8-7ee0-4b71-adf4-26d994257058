# frozen_string_literal: true

require 'rails_helper'

describe AccessCheck do
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: granted_access_at) }
  let(:company) { company_user.company }
  let(:user) { company_user.user }
  let(:granted_access_at) { 1.day.ago }
  let(:admins) { company.groups.find_by(name: 'Admins') }

  subject { AccessCheck.new(user, company) }

  context "common?" do
    context "with a normal user" do
      context "with access granted" do
        it "should pass check" do
          expect(subject.common?).to be true
        end
      end

      context "without access granted" do
        let(:granted_access_at) { nil }

        it "should not pass check" do
          expect(subject.common?).to be false
        end
      end
    end

    context "with an admin" do
      let!(:admin_members) { GroupMember.create(contributor: company_user.contributor, group: admins) }

      context "with access granted" do
        it "should pass check" do
          expect(subject.common?).to be true
        end
      end

      context "without access granted" do
        let(:granted_access_at) { false }

        it "should not pass check" do
          expect(subject.common?).to be false
        end
      end
    end

    context "with a super admin" do
      let(:company_user) { nil }
      let(:company) { create(:company) }
      let(:user) { create(:user, super_admin: true) }

      it "should pass check" do
        expect(subject.common?).to be true
      end
    end
  end

  context "admin?" do
    context "with a normal user" do
      context "with access granted" do
        it "should pass check" do
          expect(subject.admin?).to be false
        end
      end

      context "without access granted" do
        let(:granted_access_at) { nil }

        it "should not pass check" do
          expect(subject.admin?).to be false
        end
      end
    end

    context "with an admin" do
      let!(:admin_members) { GroupMember.create(contributor: company_user.contributor, group: admins) }

      context "with access granted" do
        it "should pass check" do
          expect(subject.admin?).to be true
        end
      end

      context "without access granted" do
        let(:granted_access_at) { false }

        it "should not pass check" do
          expect(subject.admin?).to be false
        end
      end
    end

    context "with a super admin" do
      let(:company_user) { nil }
      let(:company) { create(:company) }
      let(:user) { create(:user, super_admin: true) }

      it "should pass check" do
        expect(subject.admin?).to be true
      end
    end
  end
end