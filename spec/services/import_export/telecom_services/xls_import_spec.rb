require 'rails_helper'

describe ImportExport::TelecomServiceImport do
  let(:company) { FactoryBot.create(:company, :with_locations) }

  context "with false headers" do
    let!(:path) { "spec/services/import_export/telecom_services/test_telecom_service_upload_incorrect_headers.xlsx" }
    let!(:file) { File.open(Rails.root.join(path)) }

    subject do
      ImportExport::TelecomServiceImport.new(file, company.id)
    end

    let!(:import) {
      subject.process_file
    }

    it "should have a company, and return true" do
      expect(XlsImport.find(subject.xls_import_id).company).to be_present
    end

    it "should have service_names as invalid header" do
      expect(subject.worksheet_data["Telecom Service"][:header][1][:valid]).to be_falsey
      expect(subject.worksheet_data["Telecom Service"][:header][1][:error]).to eq("Service Names in Telecom Service sheet is not a valid header.")
    end

    it "should have correct suggestions for invalid headers" do
      expect(subject.worksheet_data["Telecom Service"][:suggestions].length).to eq(1)
      expect(subject.worksheet_data["Telecom Service"][:suggestions][0]).to eq(:service_name)
    end

    it "should have incorrect rows less than equal to 4" do
      expect(subject.worksheet_data["Telecom Service"][:incorrect].length <= 4).to be_truthy
    end
  end

  context "with correct headers" do
    let!(:path) { "spec/services/import_export/telecom_services/test_telecom_service_upload_with_valid_headers.xlsx" }
    let!(:file) { File.open(Rails.root.join(path)) }

    subject do
      ImportExport::TelecomServiceImport.new(file, company.id)
    end

    let!(:import) {
      subject.process_file
    }

    it "should have a company, and return true" do
      expect(XlsImport.find(subject.xls_import_id).company).to be_present
    end

    it "should have all headers valid" do
      subject.worksheet_data["Telecom Service"][:header].each do |header|
        expect(header[:valid] || header["valid"]).to be_truthy
      end
    end
  end
end
