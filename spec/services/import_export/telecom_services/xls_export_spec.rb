# frozen_string_literal: true

require 'rails_helper'

describe ImportExport::TelecomServices::XlsExport do
  context "with good data" do
    let(:company) { FactoryBot.create(:company, :with_locations) }
    let!(:telecom_service) { FactoryBot.create(:telecom_service,
                                               :with_contracts,
                                               telecom_provider: provider,
                                               company: company) }
    let!(:provider) { FactoryBot.create(:telecom_provider, name: 'AT&T', company: company) }
    subject { ImportExport::TelecomServices::XlsExport.new(company) }

    it "should pass check" do
      workbook = subject.export
      worksheet = workbook.worksheets[0]
      expect(worksheet.sheet_data.rows.size).to eq 2
      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][2].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][3].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][4].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][5].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][6].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][0].value).to eq("telecom_provider")
      expect(worksheet.sheet_data.rows[0][1].value).to eq("service_name")
      expect(worksheet.sheet_data.rows[1][1].value).to eq(telecom_service.name)
      expect(worksheet.sheet_data.rows[1][11].value).to eq(telecom_service.contract.name)
    end
  end

  context "with an archived service" do
    let!(:telecom_service) { FactoryBot.create(:telecom_service,
                                               :with_contracts,
                                               archived_at: 1.day.ago,
                                               telecom_provider: provider,
                                               company: company) }
    let!(:provider) { FactoryBot.create(:telecom_provider, name: 'Verizon', company: company) }
    let(:company) { FactoryBot.create(:company, :with_locations) }
    subject { ImportExport::TelecomServices::XlsExport.new(company) }

    it "should pass check" do
      workbook = subject.export
      worksheet = workbook.worksheets[0]
      expect(worksheet.sheet_data.rows.size).to eq 1
      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][2].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][3].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][4].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][5].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][6].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][0].value).to eq("telecom_provider")
      expect(worksheet.sheet_data.rows[0][1].value).to eq("service_name")
    end
  end

  context "with an archived telecom provider" do
    let!(:telecom_service) { FactoryBot.create(:telecom_service,
                                               :with_contracts,
                                               telecom_provider: provider,
                                               company: company) }
    let!(:provider) { FactoryBot.create(:telecom_provider,
                                        name: 'Testy Telco',
                                        company: company,
                                        archived: true) }
    subject { ImportExport::TelecomServices::XlsExport.new(company) }
    let(:company) { FactoryBot.create(:company, :with_locations) }

    it "should pass check" do
      workbook = subject.export
      worksheet = workbook.worksheets[0]
      expect(worksheet.sheet_data.rows.size).to eq 1
      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][2].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][3].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][4].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][5].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][6].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][0].value).to eq("telecom_provider")
      expect(worksheet.sheet_data.rows[0][1].value).to eq("service_name")
    end
  end

  context "with a recommended telecom provider" do
    let!(:telecom_service) { FactoryBot.create(:telecom_service,
                                               :with_contracts,
                                               telecom_provider: provider,
                                               company: company) }
    let!(:provider) { FactoryBot.create(:telecom_provider, company: company, status: :recommended) }
    subject { ImportExport::TelecomServices::XlsExport.new(company) }
    let(:company) { FactoryBot.create(:company, :with_locations) }

    it "should pass check" do
      workbook = subject.export
      worksheet = workbook.worksheets[0]
      expect(worksheet.sheet_data.rows.size).to eq 1
      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][2].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][3].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][4].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][5].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][6].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][0].value).to eq("telecom_provider")
      expect(worksheet.sheet_data.rows[0][1].value).to eq("service_name")
    end
  end
end
