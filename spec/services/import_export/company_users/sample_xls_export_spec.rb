# frozen_string_literal: true

require 'rails_helper'

describe ImportExport::CompanyUsers::SampleXlsExport do
  subject { described_class.new }

  context "with good data" do
    it "should pass check" do
      workbook = subject.export
      expect(workbook.worksheets.size).to eq(1)
      worksheet = workbook.worksheets[0]
      expect(worksheet.sheet_data.rows.size).to eq 2
      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][2].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[1][0].value).to eq("Joe")
      expect(worksheet.sheet_data.rows[1][1].value).to eq("Sample")
      expect(worksheet.sheet_data.rows[1][2].value).to eq("<EMAIL>")
      expect(worksheet.sheet_data.rows[1][3].value).to eq("111-222-3344")
    end
  end
end
