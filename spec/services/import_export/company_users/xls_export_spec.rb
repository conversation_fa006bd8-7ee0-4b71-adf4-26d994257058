# frozen_string_literal: true

require 'rails_helper'

describe ImportExport::CompanyUsers::XlsExport do
  let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
  let!(:company_user) { FactoryBot.create(:company_user,
                                          company: company,
                                          location: location,
                                          work_phone_country_code_number: "44", 
                                          custom_form_id: custom_form.id) }
  let(:company) { FactoryBot.create(:company, :with_locations) }
  let(:location) { company.locations.first }

  subject { ImportExport::CompanyUsers::XlsExport.new(company) }

  context "with good data" do
    it "should pass check" do
      workbook = subject.export
      worksheet = workbook.worksheets[0]
      expect(worksheet.sheet_data.rows.size).to eq 2
      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][2].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][3].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[1][0].value).to eq(company_user.first_name)
      expect(worksheet.sheet_data.rows[1][1].value).to eq(company_user.last_name)
      expect(worksheet.sheet_data.rows[1][2].value).to eq(company_user.email)
      expect(worksheet.sheet_data.rows[1][3].value).to eq("+44 #{company_user.work_phone}")
      expect(worksheet.sheet_data.rows[1][4].value).to eq(company_user.work_phone_extension)
      expect(worksheet.sheet_data.rows[1][5].value).to eq(company_user.mobile_phone)
      expect(worksheet.sheet_data.rows[1][6].value).to eq(company_user.title)
      expect(worksheet.sheet_data.rows[1][7].value).to eq(company_user.department)
      expect(worksheet.sheet_data.rows[1][8].value).to eq(company_user.location.name)
    end
  end
end