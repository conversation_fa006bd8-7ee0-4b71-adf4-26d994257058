# frozen_string_literal: true

require 'rails_helper'

describe ImportExport::CompanyUserImport do
  let(:company) { FactoryBot.create(:company, :with_locations) }

  context "with false headers" do
    let!(:path) { "spec/services/import_export/company_users/test_staff_upload_incorrect_headers.xlsx" }
    let!(:file) { File.open(Rails.root.join(path)) }

    subject do
      ImportExport::CompanyUserImport.new(file, company.id)
    end

    let!(:import) {
      subject.process_file
    }

    it "should have a company, and return true" do
      expect(XlsImport.find(subject.xls_import_id).company).to be_present
    end

    it "should have emails as invalid header" do
      expect(subject.worksheet_data["Staff"][:header][2][:valid]).to be_falsey
      expect(subject.worksheet_data["Staff"][:header][2][:error]).to eq("Emails in Staff sheet is not a valid header.")
    end

    it "should have correct suggestions for invalid headers" do
      expect(subject.worksheet_data["Staff"][:suggestions].length).to eq(1)
      expect(subject.worksheet_data["Staff"][:suggestions][0]).to eq(:email)
    end

    it "should have incorrect rows less than equal to 5" do
      expect(subject.worksheet_data["Staff"][:incorrect].length <= 5).to be_truthy
    end
  end

  context "with correct headers" do
    let!(:path) { "spec/services/import_export/company_users/test_staff_upload_with_valid_headers.xlsx" }
    let!(:file) { File.open(Rails.root.join(path)) }

    subject do
      ImportExport::CompanyUserImport.new(file, company.id)
    end

    let!(:import) {
      subject.process_file
    }

    it "should have a company, and return true" do
      expect(XlsImport.find(subject.xls_import_id).company).to be_present
    end

    it "should have all headers valid" do
      subject.worksheet_data["Staff"][:header].each do |header|
        expect(header[:valid] || header["valid"]).to be_truthy
      end
    end
  end
end