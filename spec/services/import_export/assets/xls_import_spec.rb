# frozen_string_literal: true

require 'rails_helper'

describe ImportExport::Assets do
  let(:company_asset_type) {FactoryBot.create(:company_asset_type, company_id: company.id)}
  let!(:managed_asset) { FactoryBot.create(:managed_asset,
                                           asset_type: company_asset_type,
                                           name: "<PERSON>'s desktop",
                                           mac_addresses: ["AA:AA:AA:AA:AA:AA:AC", "BB:BB:BB:BB:BB:BN"],
                                           company: company,
                                           location: location) }
  let(:company) { FactoryBot.create(:company, :with_locations) }
  let(:managed_by_user) { FactoryBot.create(:user, email: '<EMAIL>')}
  let(:used_by_user) { FactoryBot.create(:user, email: '<EMAIL>')}
  let(:location) { company.locations.first }
  let(:desktop) { CompanyAssetType.find_by(name: 'Desktop') }
  let!(:company_user1) { FactoryBot.create(:company_user, company: company, user: managed_by_user) }
  let!(:company_user2) { FactoryBot.create(:company_user, company: company, user: used_by_user) }
  let!(:depreciation1) { create(:depreciation, name: "straight line", depreciation_type: :straight_line, company_id: company.id) }
  let!(:depreciation2) { create(:depreciation, name: "Declining Balance, 5 years", depreciation_type: :declining_balance, company_id: company.id) }
  let!(:depreciation3) { create(:depreciation, name: "sum of years", depreciation_type: :sum_of_years_digits, company_id: company.id) }

  context "with false headers" do
    let!(:path) { "spec/services/import_export/assets/test_assets_upload_incorrect_header.xlsx" }
    let!(:file) { File.open(Rails.root.join(path)) }

    subject do
      ImportExport::AssetImport.new(file, company.id)
    end

    let!(:import) {
      subject.process_file
    }

    it "should have a company, and return true" do
      expect(XlsImport.find(subject.xls_import_id).company).to be_present
    end

    it "should have asset_types as invalid header" do
      expect(subject.worksheet_data["Laptop"][:header][1][:valid]).to be_falsey
      expect(subject.worksheet_data["Laptop"][:header][1][:error]).to eq("Asset Types in Laptop sheet is not a valid header.")
    end

    it "should have correct suggestions for invalid headers" do
      expect(subject.worksheet_data["Laptop"][:suggestions].length).to eq(5)
      expect(subject.worksheet_data["Laptop"][:suggestions][0]).to eq(:asset_type)
    end

    it "should have incorrect rows less than equal to 6" do
      expect(subject.worksheet_data["Laptop"][:incorrect].length <= 6).to be_truthy
    end
  end

  context "with correct headers" do
    path = "spec/services/import_export/assets/test_assets_with_valid_headers.xlsx"
    file = File.open(Rails.root.join(path))

    subject do
      ImportExport::AssetImport.new(file, company.id)
    end

    let!(:import) {
      subject.process_file
    }

    it "should have all headers valid" do
      data = subject.worksheet_data
      data.keys.each_with_index do |worksheet|
        data[worksheet][:header].each do |header|
          expect(header[:valid] || header["valid"]).to be_truthy
        end
      end
    end
  end

  context "with formatting errors" do
    let!(:path) { "spec/services/import_export/assets/test_assets_with_formatting_error.xlsx" }
    let!(:file) { File.open(Rails.root.join(path)) }
    subject do
      ImportExport::AssetImport.new(file, company.id)
    end

    let!(:import) {
      subject.process_file
    }

    it "should have a company, and return true" do
      expect(XlsImport.find(subject.xls_import_id).company).to be_present
    end

    it "should point out cost formatting error" do
      data = subject.worksheet_data
      data.keys.each_with_index do |worksheet|
        data[worksheet][:incorrect].each do |row|
          row.each_with_index do |col|
            if col["purchase_price"]
              expect(col[:valid]).to be_falsey
              expect(col[:error]).to eq("purchase_price values should be numbers")
            else
              expect(col[:valid]).to be_truthy
            end
          end
        end
      end
    end
  end
end
