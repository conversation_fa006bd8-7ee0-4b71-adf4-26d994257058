# frozen_string_literal: true

require 'rails_helper'

describe ImportExport::Assets::XlsExport do
  let!(:desktop) { CompanyAssetType.find_by(name: '<PERSON>ptop') }

  let!(:company1) { FactoryBot.create(:company, :with_locations) }
  let!(:location1) { company1.locations.first }

  let!(:managed_asset4) { FactoryBot.create(:managed_asset, company: company1, name: "asset4", manufacturer: "Man4", location: location1) }
  let!(:managed_asset5) { FactoryBot.create(:managed_asset, company: company1, name: "asset5", manufacturer: "Man5", location: location1) }

  context "with applied analytics filter" do
    it "should return assets according to manufacturers filters in export file" do
      params = {
        analytics_export: true,
        analytics_filters: {
          analytics_asset_manufacturers: ["Man4", "Man5"],
        }
      }

      subject = ImportExport::Assets::XlsExport.new(company1, {}, params.to_json)

      workbook = subject.export
      worksheet = workbook.worksheets[1]

      expect(worksheet.sheet_data.rows.size).to eq 3
      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][2].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][21].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][22].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][24].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][23].value).to eq("alert_dates")
      expect(worksheet.sheet_data.rows[0][24].value).to eq("purchase_price")
      expect(worksheet.sheet_data.rows[0][25].value).to eq("salvage")
      expect(worksheet.sheet_data.rows[0][26].value).to eq("depreciation")
      expect(worksheet.sheet_data.rows[0][27].value).to eq("status")
      expect(worksheet.sheet_data.rows[0][28].value).to eq("po")
      expect(worksheet.sheet_data.rows[1][24].value).to eq(5000.0)
      expect(worksheet.sheet_data.rows[1][0].value).to eq(managed_asset4.name)
      expect(worksheet.sheet_data.rows[2][0].value).to eq(managed_asset5.name)
      expect(worksheet.sheet_data.rows[1][10].value).to eq(managed_asset4.manufacturer)
      expect(worksheet.sheet_data.rows[2][10].value).to eq(managed_asset5.manufacturer)
    end
  end

  context "with applied analytics filter" do
    it "should return assets according to location filters in export file" do
      params = {
        analytics_export: true,
        analytics_filters: {
          fetch_all_locations_data: true,
        }
      }

      subject = ImportExport::Assets::XlsExport.new(company1, {}, params.to_json)

      workbook = subject.export
      worksheet = workbook.worksheets[1]

      asset_names = worksheet.sheet_data.rows[1..].map { |row| row[0].value }

      expect(worksheet.sheet_data.rows.size).to eq 3
      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][2].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][21].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][22].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][24].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][23].value).to eq("alert_dates")
      expect(worksheet.sheet_data.rows[0][24].value).to eq("purchase_price")
      expect(worksheet.sheet_data.rows[0][25].value).to eq("salvage")
      expect(worksheet.sheet_data.rows[0][26].value).to eq("depreciation")
      expect(worksheet.sheet_data.rows[0][27].value).to eq("status")
      expect(worksheet.sheet_data.rows[0][28].value).to eq("po")
      expect(worksheet.sheet_data.rows[1][24].value).to eq(5000.0)
      expect(asset_names).to match_array([managed_asset4.name, managed_asset5.name])
      expect(worksheet.sheet_data.rows[1][17].value).to eq(managed_asset4.location.name)
      expect(worksheet.sheet_data.rows[2][17].value).to eq(managed_asset5.location.name)
    end
  end

  context "with applied analytics filter" do
    it "should return assets according to aasset type filters in export file" do
      params = {
        analytics_export: true,
        analytics_filters: {
          fetch_all_asset_type_data: true,
        }
      }

      subject = ImportExport::Assets::XlsExport.new(company1, {}, params.to_json)

      workbook = subject.export
      worksheet = workbook.worksheets[1]

      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][2].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][21].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][22].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][24].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][23].value).to eq("alert_dates")
      expect(worksheet.sheet_data.rows[0][24].value).to eq("purchase_price")
      expect(worksheet.sheet_data.rows[0][25].value).to eq("salvage")
      expect(worksheet.sheet_data.rows[0][26].value).to eq("depreciation")
      expect(worksheet.sheet_data.rows[0][27].value).to eq("status")
      expect(worksheet.sheet_data.rows[0][28].value).to eq("po")
    end
  end

  context "with applied analytics filter" do
    it "should return assets according to all status filters in export file" do
      params = {
        analytics_export: true,
        analytics_filters: {
          fetch_all_status_data: true,
        }
      }

      subject = ImportExport::Assets::XlsExport.new(company1, {}, params.to_json)

      workbook = subject.export
      worksheet = workbook.worksheets[1]

      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][2].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][21].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][22].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][24].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][23].value).to eq("alert_dates")
      expect(worksheet.sheet_data.rows[0][24].value).to eq("purchase_price")
      expect(worksheet.sheet_data.rows[0][25].value).to eq("salvage")
      expect(worksheet.sheet_data.rows[0][26].value).to eq("depreciation")
      expect(worksheet.sheet_data.rows[0][27].value).to eq("status")
      expect(worksheet.sheet_data.rows[0][28].value).to eq("po")
    end
  end
end

describe ImportExport::Assets::XlsExport do  
  before do
    ManagedAsset.destroy_all
  end

  let!(:managed_asset) {
    asset = FactoryBot.create(:managed_asset,
                              :laptop,
                              company: company,
                              location: location)
    asset.tags = ['Big']
    asset.managed_asset_tags.map(&:save!)
    asset
  }
  let!(:managed_asset1) {
    FactoryBot.create(:managed_asset, company: company, name: "asset1", manufacturer: "Man1", location: location)
  }
  let!(:managed_asset2) {
    FactoryBot.create(:managed_asset, company: company, name: "asset2", manufacturer: "Man2", location: location)
  }
  let!(:managed_asset3) {
    FactoryBot.create(:managed_asset, company: company, name: "asset3", manufacturer: "Man3", location: location)
  }
  let!(:asset_with_computer_detail1) {
    ManagedAsset.find_by_name('asset1').hardware_detail.update_columns(disk_encryption: "On", memory: "8gb", hard_drive: "300tb")
  }

  let!(:asset_with_computer_detail2) {
    ManagedAsset.find_by_name('asset2').hardware_detail.update_columns(disk_encryption: "On", memory: "24gb", hard_drive: "600tb")
  }

  let!(:asset_with_software1) {
    ManagedAsset.find_by_name('asset1').asset_softwares.find_or_create_by(name: "OS 1", software_type: "Application")
  }

  let!(:asset_with_software2) {
    ManagedAsset.find_by_name('asset2').asset_softwares.find_or_create_by(name: "OS 2", software_type: "Application")
  }

  let!(:company) { FactoryBot.create(:company, :with_locations) }
  let!(:location) { company.locations.first }
  let!(:desktop) { CompanyAssetType.find_by(name: 'Laptop') }

  context "with good data" do
    subject { ImportExport::Assets::XlsExport.new(company) }

    it "should pass check" do
      workbook = subject.export
      worksheet = workbook.worksheets[0]
      expect(worksheet.sheet_data.rows.size).to eq 2
      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][2].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][21].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][22].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][24].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][24].value).to eq("purchase_price")
      expect(worksheet.sheet_data.rows[0][23].value).to eq("alert_dates")
      expect(worksheet.sheet_data.rows[1][0].value).to eq(managed_asset.name)
      expect(worksheet.sheet_data.rows[1][1].value).to eq(managed_asset.asset_type.name)
      expect(worksheet.sheet_data.rows[1][10].value).to eq(managed_asset.manufacturer)
      expect(worksheet.sheet_data.rows[1][18].raw_value).to eq("Development")
      expect(worksheet.sheet_data.rows[1][17].value).to eq(managed_asset.location.name)
      expect(worksheet.sheet_data.rows[1][24].raw_value).to eq(managed_asset.cost.purchase_price)
      expect(worksheet.sheet_data.rows[1][13].value).to eq(managed_asset.tags.join(","))
    end
  end

  context "with applied analytics filter" do
    let(:analytics_params) do
      {
        analytics_export: true,
        analytics_filters: {
          fetch_all_device_encryption_data: true,
          analytics_memory_filter: ['lessThan16', '16GB_32GB'],
          analytics_storage_filter: ['256GB_512GB', '512GB_1024GB'],
        }
      }
    end
    subject { ImportExport::Assets::XlsExport.new(company, {}, analytics_params.to_json) }

    it "should return assets according to applied hardware details filters in export file" do
      workbook = subject.export
      worksheet = workbook.worksheets[1]
      expect(worksheet.sheet_data.rows.size).to eq 3
      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][2].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][21].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][22].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][24].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][23].value).to eq("alert_dates")
      expect(worksheet.sheet_data.rows[0][24].value).to eq("purchase_price")
      expect(worksheet.sheet_data.rows[0][25].value).to eq("salvage")
      expect(worksheet.sheet_data.rows[0][26].value).to eq("depreciation")
      expect(worksheet.sheet_data.rows[0][27].value).to eq("status")
      expect(worksheet.sheet_data.rows[0][28].value).to eq("po")
      expect(worksheet.sheet_data.rows[1][0].value).to eq(managed_asset1.name)
      expect(worksheet.sheet_data.rows[2][0].value).to eq(managed_asset2.name)
      expect(worksheet.sheet_data.rows[1][1].value).to eq(managed_asset1.asset_type.name)
      expect(worksheet.sheet_data.rows[2][1].value).to eq(managed_asset2.asset_type.name)
      expect(worksheet.sheet_data.rows[1][10].value).to eq(managed_asset1.manufacturer)
      expect(worksheet.sheet_data.rows[2][10].value).to eq(managed_asset2.manufacturer)
      expect(worksheet.sheet_data.rows[1][17].value).to eq(managed_asset1.location.name)
      expect(worksheet.sheet_data.rows[2][17].value).to eq(managed_asset2.location.name)
    end
  end

  context "with applied analytics filter" do
    let(:analytics_params) do
      {
        analytics_export: true,
        analytics_filters: {
          analytics_installed_software: ['OS 1'],
          analytics_not_installed_software: ['OS 2']
        }
      }
    end
    subject { ImportExport::Assets::XlsExport.new(company, {}, analytics_params.to_json) }

    it "should return assets according to filtered softwares in them in export file" do
      workbook = subject.export
      worksheet = workbook.worksheets[1]

      expect(worksheet.sheet_data.rows.size).to eq 2
      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][2].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][21].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][22].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][24].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][23].value).to eq("alert_dates")
      expect(worksheet.sheet_data.rows[0][24].value).to eq("purchase_price")
      expect(worksheet.sheet_data.rows[0][25].value).to eq("salvage")
      expect(worksheet.sheet_data.rows[0][26].value).to eq("depreciation")
      expect(worksheet.sheet_data.rows[0][27].value).to eq("status")
      expect(worksheet.sheet_data.rows[0][28].value).to eq("po")
      expect(worksheet.sheet_data.rows[1][0].value).to eq(managed_asset1.name)
      expect(worksheet.sheet_data.rows[1][1].value).to eq(managed_asset1.asset_type.name)
      expect(worksheet.sheet_data.rows[1][10].value).to eq(managed_asset1.manufacturer)
      expect(worksheet.sheet_data.rows[1][17].value).to eq(managed_asset1.location.name)
    end
  end

  context "with applied analytics filter" do
    let(:analytics_params) do
      {
        analytics_export: true,
        analytics_filters: {
          fetch_all_sources_data: true,
          fetch_all_os_data: true,
        }
      }
    end
    subject { ImportExport::Assets::XlsExport.new(company, {}, analytics_params.to_json) }

    it "should not return any asset in export file" do
      workbook = subject.export
      worksheet = workbook.worksheets[1]

      expect(worksheet.sheet_data.rows.size).to eq 1
      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][2].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][21].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][22].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][24].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][23].value).to eq("alert_dates")
      expect(worksheet.sheet_data.rows[0][24].value).to eq("purchase_price")
      expect(worksheet.sheet_data.rows[0][25].value).to eq("salvage")
      expect(worksheet.sheet_data.rows[0][26].value).to eq("depreciation")
      expect(worksheet.sheet_data.rows[0][27].value).to eq("status")
      expect(worksheet.sheet_data.rows[0][28].value).to eq("po")
    end
  end

  context "with applied analytics filter" do
    let(:analytics_params) do
      {
        analytics_export: true,
        analytics_filters: {
          fetch_all_warranty_data: true,
        }
      }
    end
    subject { ImportExport::Assets::XlsExport.new(company, {}, analytics_params.to_json) }

    it "should have only column names" do
      workbook = subject.export
      worksheet = workbook.worksheets[1]

      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][2].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][21].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][22].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][24].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[0][23].value).to eq("alert_dates")
      expect(worksheet.sheet_data.rows[0][24].value).to eq("purchase_price")
      expect(worksheet.sheet_data.rows[0][25].value).to eq("salvage")
      expect(worksheet.sheet_data.rows[0][26].value).to eq("depreciation")
      expect(worksheet.sheet_data.rows[0][27].value).to eq("status")
      expect(worksheet.sheet_data.rows[0][28].value).to eq("po")
    end
  end
end
