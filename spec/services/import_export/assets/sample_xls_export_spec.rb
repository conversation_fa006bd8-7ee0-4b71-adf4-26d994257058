# frozen_string_literal: true

require 'rails_helper'

describe ImportExport::Assets::SampleXlsExport do
  let(:company) { create(:company) }
  subject { described_class.new(company)}

  context "with good data" do
    it "should pass check" do
      workbook = subject.export
      expect(workbook.worksheets.size).to eq(22)
      worksheet = workbook.worksheets[0]
      expect(worksheet.sheet_data.rows.size).to eq 2
      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][2].is_bolded).to be_nil
      expect(worksheet.sheet_data.rows[1][0].value).to eq("Yoda's MacBook Pro")
      expect(worksheet.sheet_data.rows[1][1].value).to eq("Laptop")
      expect(worksheet.sheet_data.rows[1][2].value).to be_present
      expect(worksheet.sheet_data.rows[1][12].value).to eq("macOS Catalina;")
      expect(worksheet.sheet_data.rows[1][20].value).to eq("10/25/2018")
      expect(worksheet.sheet_data.rows[1][21].raw_value).to eq(44494.0)
      workbook.worksheets.each do |worksheet|
        expect(worksheet.sheet_data.rows.size).to eq(6).or eq(3).or eq(2)
      end
    end
  end
end
