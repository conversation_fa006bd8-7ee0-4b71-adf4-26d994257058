# frozen_string_literal: true

require 'rails_helper'
include CompanyUserHelper

describe ImportExport::CustomEntity::XlsExport do
  create_company_and_user

  describe 'Help ticket' do
    let!(:help_ticket_activity) { FactoryBot.create(:help_ticket_activity) }

    let(:custom_form) { company.default_workspace.custom_forms.first }

    let!(:help_ticket) do
      ticket = FactoryBot.create(:help_ticket, company: company, custom_form: custom_form, workspace: workspace, help_ticket_activities: [help_ticket_activity])
      subject = ticket.custom_form.custom_form_fields.find_by(name: 'subject')
      value = CustomFormValue.create(module: ticket, custom_form_field: subject, company: company, value_str: "this is the subject field")
      ticket
    end

    let(:workspace) { create(:workspace) }

    let(:options) { { company_module: "helpdesk", class: "HelpTicket" } }

    subject { described_class.new(company, options, nil, nil, custom_form.workspace) }

    context "with good data" do
      it "should pass check" do
        workbook = subject.export
        worksheet = workbook.worksheets[0]
        expect(worksheet.sheet_data.rows.size).to eq 2
        expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
        expect(worksheet.sheet_data.rows[0][4].is_bolded).to eq(true)
        expect(worksheet.sheet_data.rows[0][0].value).to eq("ticket_number")
        expect(worksheet.sheet_data.rows[0][1].value).to eq("subject")
        expect(worksheet.sheet_data.rows[0][2].value).to eq("priority")
        expect(worksheet.sheet_data.rows[0][3].value).to eq("status")
        expect(worksheet.sheet_data.rows[0][4].value).to eq("created_by")
        expect(worksheet.sheet_data.rows[0][5].value).to eq("assigned_to")
        expect(worksheet.sheet_data.rows[0][6].value).to eq("raise_this_request_on_behalf_of")
        expect(worksheet.sheet_data.rows[0][7].value).to eq("impacted_devices")
        expect(worksheet.sheet_data.rows[0][8].value).to eq("followers")
        expect(worksheet.sheet_data.rows[0][9].value).to eq("location_list")
        expect(worksheet.sheet_data.rows[0][10].value).to eq("description")

        subject_value = help_ticket.custom_form_values.joins(:custom_form_field).find_by("custom_form_fields.name = ?", "subject")
        expect(worksheet.sheet_data.rows[1][1].value).to eq(subject_value.value_str)
      end
    end
  end

  describe 'Location' do
    let!(:location_custom_form) { company.custom_forms.find_by(form_name: 'Base Location', company_module: "location") }

    let(:options) { { company_module: "location", class: "Location" } }

    let!(:location) do
      Location.destroy_all
      location = FactoryBot.create(:location, company: company, custom_form: location_custom_form)
      name = location.custom_form.custom_form_fields.find_by(name: 'name')
      name_value = CustomFormValue.create(module: location, custom_form_field: name, company: company, value_str: "this is location field name")

      location
    end

    subject { described_class.new(company, options, nil, nil) }

    context "with good data " do
      it "should pass check" do
        workbook = subject.export
        worksheet = workbook.worksheets[0]
        expect(worksheet.sheet_data.rows.size).to eq 2
        expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
        expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
        expect(worksheet.sheet_data.rows[0][2].is_bolded).to eq(true)
        expect(worksheet.sheet_data.rows[0][3].is_bolded).to eq(true)
        expect(worksheet.sheet_data.rows[0][0].value).to eq('name')
        expect(worksheet.sheet_data.rows[0][1].value).to eq('address')
        expect(worksheet.sheet_data.rows[0][2].value).to eq('city')
        expect(worksheet.sheet_data.rows[0][3].value).to eq('zip')
        expect(worksheet.sheet_data.rows[0][4].value).to eq('address_2')
        expect(worksheet.sheet_data.rows[0][5].value).to eq('state')
        expect(worksheet.sheet_data.rows[0][6].value).to eq('country_name')
        expect(worksheet.sheet_data.rows[0][7].value).to eq('phone_number')
        expect(worksheet.sheet_data.rows[0][8].value).to eq('notes')

        name_field_value = location.custom_form_values.joins(:custom_form_field).find_by("custom_form_fields.name = ?", "name")
        expect(worksheet.sheet_data.rows[1][0].value).to eq(name_field_value.value_str)
      end
    end

    describe 'Staff user' do
      let!(:custom_form) { company.custom_forms.find_by(form_name: 'Teammates', company_module: "company_user") }

      let(:options) { { company_module: "company_user", class: "CompanyUser" } }

      let!(:company_user) do
        company_user = FactoryBot.create(:company_user, company: company, custom_form: custom_form)
        company_user
      end

      subject { described_class.new(company, options, nil, nil) }

      context "with good data" do
        it "should pass check" do
          workbook = subject.export
          worksheet = workbook.worksheets[0]
          expect(worksheet.sheet_data.rows.size).to eq 2
          expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
          expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
          expect(worksheet.sheet_data.rows[0][2].is_bolded).to eq(true)
          expect(worksheet.sheet_data.rows[0][0].value).to eq('first_name')
          expect(worksheet.sheet_data.rows[0][1].value).to eq('last_name')
          expect(worksheet.sheet_data.rows[0][2].value).to eq('email')
          expect(worksheet.sheet_data.rows[0][3].value).to eq('mobile_phone')
          expect(worksheet.sheet_data.rows[0][4].value).to eq('work_phone')
          expect(worksheet.sheet_data.rows[0][5].value).to eq('extension')
          expect(worksheet.sheet_data.rows[0][6].value).to eq('title')
          expect(worksheet.sheet_data.rows[0][7].value).to eq('department')
          expect(worksheet.sheet_data.rows[0][8].value).to eq('location')
          expect(worksheet.sheet_data.rows[0][9].value).to eq('supervisor')

          expect(worksheet.sheet_data.rows[1][0].value).to eq(company_user.user.first_name)
          expect(worksheet.sheet_data.rows[1][1].value).to eq(company_user.user.last_name)
          expect(worksheet.sheet_data.rows[1][2].value).to eq(company_user.user.email)
        end
      end
    end
  end
end
