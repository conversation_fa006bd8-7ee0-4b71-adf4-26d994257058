require 'rails_helper'

describe ImportExport::CompanyModuleImport do
  let(:company) { FactoryBot.create(:company, :with_locations) }

  context "with false headers" do
    let!(:path) { "spec/services/import_export/help_tickets/test_helpdesk_upload_incorrect_headers.xlsx" }
    let!(:file) { File.open(Rails.root.join(path)) }

    subject do
      described_class.new(file, company.id, options: { company_module: "helpdesk", class: "HelpTicket" }, workspace: company.workspaces.first)
    end

    let!(:import) {
      subject.process_file
    }

    it "should have a company, and return true" do
      expect(XlsImport.find(subject.xls_import_id).company).to be_present
    end

    it "should have priority as invalid header" do
      expect(subject.worksheet_data["It General Request"][:header][1][:valid]).to be_falsey
      expect(subject.worksheet_data["It General Request"][:header][1][:error]).to eq("Prio in It General Request sheet is not a valid header.")
    end

    it "should have correct suggestions for invalid headers" do
      expect(subject.worksheet_data["It General Request"][:suggestions].length).to eq(2)
      expect(subject.worksheet_data["It General Request"][:suggestions][0]).to eq(:priority)
      expect(subject.worksheet_data["It General Request"][:suggestions][1]).to eq(:followers)
    end

    it "should have incorrect rows less than equal to 5" do
      expect(subject.worksheet_data["It General Request"][:incorrect].length <= 5).to be_truthy
    end
  end

  context "with correct headers" do
    let!(:path) { "spec/services/import_export/help_tickets/test_helpdesk_upload.xlsx" }
    let!(:file) { File.open(Rails.root.join(path)) }

    subject do
      described_class.new(file, company.id, options: { company_module: "helpdesk", class: "HelpTicket" }, workspace: company.workspaces.first)
    end

    let!(:import) {
      subject.process_file
    }

    it "should have a company, and return true" do
      expect(XlsImport.find(subject.xls_import_id).company).to be_present
    end

    it "should have all headers valid" do
      subject.worksheet_data["It General Request"][:header].each do |header|
        expect(header[:valid] || header["valid"]).to be_truthy
      end
    end
  end
end
