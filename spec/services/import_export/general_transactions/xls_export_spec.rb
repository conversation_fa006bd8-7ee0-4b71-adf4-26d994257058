# frozen_string_literal: true

require 'rails_helper'

describe ImportExport::GeneralTransactions::XlsExport do
  let(:company) { FactoryBot.create(:company, :with_locations) }
  let!(:vendor) { create(:vendor, name: "This is a RSpec vendor", company: company) }
  let!(:general_transaction){FactoryBot.create(:general_transaction,
                                               company: company,
                                               transaction_date: '01/01/2020',
                                               amount: 5000,
                                               vendor:vendor,
                                               status: "recognized",
                                               recurring: true
                                              )}
  let!(:general_transaction_2){FactoryBot.create(:general_transaction,
                                                 company: company,
                                                 transaction_date: '02/02/2020',
                                                 amount: 4000,
                                                 vendor:vendor,
                                                 status: "recognized",
                                                 recurring: false
                                              )}
  let(:recurring_mapping) { { 'recurring_transaction' => :recurring_transaction } }
  let(:non_recurring_mapping) { { 'non_recurring_transaction' => :non_recurring_transaction } }

  let(:recurring_options) do
    {
      columns: { recurring_transaction: %w{
        vendor transaction_name product_description amount start_date end_date tags billing_frequency location
      }},
      attributes: { recurring_transaction: %w{
        vendor product_name product amount transaction_date end_date general_transaction_tags billing_frequency location_name
      }},
      transaction_type: 'recurring_transaction'
    }
  end

  let(:non_recurring_options) do
    {
      columns: { non_recurring_transaction: %w{
        vendor transaction_name product_description amount start_date tags location
      }},
      attributes: { non_recurring_transaction: %w{
        vendor product_name product amount transaction_date general_transaction_tags location_name
      }},
      transaction_type: 'non_recurring_transaction'
    }
  end

  context 'recurring transaction with good data' do
    subject { ImportExport::GeneralTransactions::XlsExport.new(company, recurring_options, nil, recurring_mapping) }

    it 'should pass check' do
      workbook = subject.export
      worksheet = workbook.worksheets[0]
      expect(worksheet.sheet_data.rows.size).to eq 2
      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][2].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][3].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][4].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][5].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][6].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][7].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][0].value).to eq("vendor")
      expect(worksheet.sheet_data.rows[0][5].value).to eq("end_date")
      expect(worksheet.sheet_data.rows[0][7].value).to eq("billing_frequency")
    end
  end

  context 'non recurring transaction with good data' do
    subject { ImportExport::GeneralTransactions::XlsExport.new(company, non_recurring_options, nil, non_recurring_mapping) }

    it 'should pass check' do
      workbook = subject.export
      worksheet = workbook.worksheets[0]
      expect(worksheet.sheet_data.rows.size).to eq 2
      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][2].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][3].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][4].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][5].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][6].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][1].value).to eq("transaction_name")
      expect(worksheet.sheet_data.rows[0][5].value).to eq("tags")
    end
  end
end
