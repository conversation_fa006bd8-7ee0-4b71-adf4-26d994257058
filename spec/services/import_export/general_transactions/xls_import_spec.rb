require 'rails_helper'

describe ImportExport::GeneralTransactionImport do
  let(:company) { FactoryBot.create(:company, :with_locations) }
  let!(:options) do
    {
      columns: %w{
        vendor transaction_name product_description amount start_date end_date tags billing_frequency location notes
      },
      attributes: %w{
        vendor product_name product amount transaction_date end_date general_transaction_tags billing_frequency location_name notes
      },
      transaction_type: 'recurring_transaction'
    }
  end

  context "with bad headers" do
    let!(:path) { "spec/services/import_export/general_transactions/test_general_transactions_upload_incorrect_headers.xlsx" }
    let!(:file) { File.open(Rails.root.join(path)) }

    subject do
      ImportExport::GeneralTransactionImport.new(file, company.id, options: options)
    end

    let!(:import) {
      subject.process_file
    }

    it "should have a company, and return true" do
      expect(XlsImport.find(subject.xls_import_id).company).to be_present
    end

    it "should have asset_types as invalid header" do
      expect(subject.worksheet_data["Transactions"][:header][1][:valid]).to be_falsey
      expect(subject.worksheet_data["Transactions"][:header][1][:error]).to eq("Bad Header in Transactions sheet is not a valid header.")
    end

    it "should have correct suggestions for invalid headers" do
      expect(subject.worksheet_data["Transactions"][:suggestions].length).to eq(4)
      expect(subject.worksheet_data["Transactions"][:suggestions][0]).to eq(:transaction_name)
    end

    it "should have incorrect rows to equal 3" do
      expect(subject.worksheet_data["Transactions"][:incorrect].length).to eq(3)
    end
  end

  context "with correct headers" do
    let!(:path) { "spec/services/import_export/general_transactions/test_general_transactions_with_valid_headers.xlsx" }
    let!(:file) { File.open(Rails.root.join(path)) }

    subject do
      ImportExport::GeneralTransactionImport.new(file, company.id, options: options)
    end

    let!(:import) {
      subject.process_file
    }

    it "should have a company, and return true" do
      expect(XlsImport.find(subject.xls_import_id).company).to be_present
    end

    it "should have all headers valid" do
      subject.worksheet_data["Transactions"][:header].each do |header|
        expect(header[:valid] || header["valid"]).to be_truthy
      end
    end
  end
end
