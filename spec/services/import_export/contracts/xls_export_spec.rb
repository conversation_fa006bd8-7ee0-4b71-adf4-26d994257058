# frozen_string_literal: true

require 'rails_helper'

describe ImportExport::Contracts::XlsExport do
  let!(:contract) { FactoryBot.create(:contract,
                                      company: company,
                                      contract_type: 'open_ended') }
  let!(:contract_2) { FactoryBot.create(:contract,
                                        company: company,
                                        name: 'C2',
                                        contract_type: 'fixed_term') }
  let(:company) { FactoryBot.create(:company, :with_locations) }
  let(:open_ended_mapping) { { 'open_ended_contract' => :open_ended_contract } }
  let(:fixed_term_mapping) { { 'fixed_term_contract' => :fixed_term_contract } }

  let(:open_ended_options) do
    {
      columns: { open_ended_contract: %w{
        vendor name start_date alert_dates monthly_cost total_cost category locations departments staff_contacts notes contract_tags status
      }},
      attributes: { open_ended_contract: %w{
        vendor_name name start_date_formatted alert_dates_formatted monthly_cost contract_value_amount category_name locations_name departments_name staff_contacts notes contract_tags status
      }},
      contract_type: 'open_ended_contract'
    }
  end

  let(:fixed_term_options) do
    {
      columns: { fixed_term_contract: %w{
        vendor name start_date end_date notice_period alert_dates monthly_cost total_cost category locations departments staff_contacts notes contract_tags status
      }},
      attributes: { fixed_term_contract: %w{
        vendor_name name start_date_formatted end_date_formatted notice_period alert_dates_formatted monthly_cost contract_value_amount category_name locations_name departments_name staff_contacts notes contract_tags status
      }},
      contract_type: 'fixed_term_contract'
    }
  end

  context 'open ended contract with good data' do
    subject { ImportExport::Contracts::XlsExport.new(company, open_ended_options, nil, open_ended_mapping) }

    it 'should pass check' do
      workbook = subject.export
      worksheet = workbook.worksheets[0]
      expect(worksheet.sheet_data.rows.size).to eq 2
      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][2].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][3].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][4].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][5].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][6].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][1].value).to eq('name')
      expect(worksheet.sheet_data.rows[1][1].value).to eq(contract.name)
      expect(worksheet.sheet_data.rows[0][12].value).to eq('status')
    end
  end

  context 'fixed term contract with good data' do
    subject { ImportExport::Contracts::XlsExport.new(company, fixed_term_options, nil, fixed_term_mapping) }

    it 'should pass check' do
      workbook = subject.export
      worksheet = workbook.worksheets[0]
      expect(worksheet.sheet_data.rows.size).to eq 2
      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][2].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][3].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][4].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][6].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][5].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].value).to eq('name')
      expect(worksheet.sheet_data.rows[1][1].value).to eq(contract_2.name)
      expect(worksheet.sheet_data.rows[0][14].value).to eq('status')
    end
  end
end
