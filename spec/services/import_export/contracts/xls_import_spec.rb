require 'rails_helper'

describe ImportExport::ContractImport do
  let!(:contract) { FactoryBot.create(:contract,
                                      name: "<PERSON>'s desktop",
                                      company: company) }
  let(:company) { FactoryBot.create(:company, :with_locations) }
  let!(:options) do
    {
      columns: %w{
        vendor name start_date alert_dates monthly_cost total_cost category locations departments staff_contacts notes contract_tags status
      },
      attributes: %w{
        vendor_name name start_date_formatted alert_dates_formatted monthly_cost contract_value_amount category_name locations_name departments_name staff_contacts notes contract_tags status
      },
      contract_type: 'open_ended_contract'
    }
  end

  context "with false headers" do
    let!(:path) { "spec/services/import_export/contracts/test_contracts_upload_incorrect_headers.xlsx" }
    let!(:file) { File.open(Rails.root.join(path)) }

    subject do
      ImportExport::ContractImport.new(file, company.id, options: options)
    end

    let!(:import) {
      subject.process_file
    }

    it "should have a company, and return true" do
      expect(XlsImport.find(subject.xls_import_id).company).to be_present
    end

    it "should have contract_type as invalid header" do
      expect(subject.worksheet_data["Contract"][:header][2][:valid]).to be_falsey
      expect(subject.worksheet_data["Contract"][:header][2][:error]).to eq("Contract Type in Contract sheet is not a valid header.")
    end

    it "should have correct suggestions for invalid headers" do
      expect(subject.worksheet_data["Contract"][:suggestions].length).to eq(3)
      expect(subject.worksheet_data["Contract"][:suggestions][0]).to eq(:contract_term)
    end

    it "should have incorrect rows less than equal to 5" do
      expect(subject.worksheet_data["Contract"][:incorrect].length <= 5).to be_truthy
    end
  end

  context "with correct headers" do
    let!(:path) { "spec/services/import_export/contracts/test_contracts_with_valid_headers.xlsx" }
    let!(:file) { File.open(Rails.root.join(path)) }

    subject do
      ImportExport::ContractImport.new(file, company.id, options: options)
    end

    let!(:import) {
      subject.process_file
    }

    it "should have a company, and return true" do
      expect(XlsImport.find(subject.xls_import_id).company).to be_present
    end

    it "should have all headers valid" do
      subject.worksheet_data["Contract"][:header].each do |header|
        expect(header[:valid] || header["valid"]).to be_truthy
      end
    end
  end
end
