# frozen_string_literal: true

require 'rails_helper'

describe ImportExport::Locations::XlsExport do
  let!(:company) { FactoryBot.create(:company) }
  let!(:location) { FactoryBot.create(:location,
                                      :phone_number,
                                      company: company,
                                      phone_number_country_code_number: "44") }

  subject { ImportExport::Locations::XlsExport.new(company.reload) }

  context "with good data" do
    it "should pass check" do
      workbook = subject.export
      worksheet = workbook.worksheets[0]
      expect(worksheet.sheet_data.rows.size).to eq 2
      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][3].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][5].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[1][0].value).to eq(location.name)
      expect(worksheet.sheet_data.rows[1][1].value).to eq(location.address)
      expect(worksheet.sheet_data.rows[1][2].value).to eq(location.address2)
      expect(worksheet.sheet_data.rows[1][3].value).to eq(location.city)
      expect(worksheet.sheet_data.rows[1][4].value).to eq(location.state)
      expect(worksheet.sheet_data.rows[1][5].value).to eq(location.zip)
      expect(worksheet.sheet_data.rows[1][6].value).to eq(location.country_name)
      expect(worksheet.sheet_data.rows[1][7].value).to eq("+44 #{location.phone_number}")
    end
  end
end