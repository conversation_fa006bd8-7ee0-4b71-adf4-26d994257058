require 'rails_helper'

describe ImportExport::CompanyModuleImport do
  let(:company) { FactoryBot.create(:company) }

  context "with false headers" do
    let!(:path) { "spec/services/import_export/locations/test_locations_upload_bad_header.xlsx" }
    let!(:file) { File.open(Rails.root.join(path)) }

    subject do
      ImportExport::CompanyModuleImport.new(file, company.id, options: { company_module: "location", class: "Location" })
    end

    let!(:import) {
      subject.process_file
    }

    it "should have a company, and return true" do
      expect(XlsImport.find(subject.xls_import_id).company).to be_present
    end

    it "should have bad_header as invalid header" do
      expect(subject.worksheet_data["Base Location"][:header][1][:valid]).to be_falsey
      expect(subject.worksheet_data["Base Location"][:header][1][:error]).to eq("Add in Base Location sheet is not a valid header.")
    end

    it "should have correct suggestions for invalid headers" do
      expect(subject.worksheet_data["Base Location"][:suggestions].length).to eq(5)
      expect(subject.worksheet_data["Base Location"][:suggestions][0]).to eq(:address)
    end

    it "should have incorrect rows equal to 1" do
      expect(subject.worksheet_data["Base Location"][:incorrect].length).to eq(2)
    end
  end

  context "with correct headers" do
    let!(:path) { "spec/services/import_export/locations/test_locations_upload.xlsx" }
    let!(:file) { File.open(Rails.root.join(path)) }

    subject do
      ImportExport::CompanyModuleImport.new(file, company.id, options: { company_module: "location", class: "Location" })
    end

    let!(:import) {
      subject.process_file
    }

    it "should have a company, and return true" do
      expect(XlsImport.find(subject.xls_import_id).company).to be_present
    end

    it "should have all headers valid" do
      subject.worksheet_data["Base Location"][:header].each do |header|
        expect(header[:valid] || header["valid"]).to be_truthy
      end
    end
  end
end
