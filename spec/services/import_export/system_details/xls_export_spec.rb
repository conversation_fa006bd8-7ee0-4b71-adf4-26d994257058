require 'rails_helper'

describe ImportExport::SystemDetails::XlsExport do
  let!(:company) { FactoryBot.create(:company, :with_locations) }
  let!(:managed_asset) { FactoryBot.create(:managed_asset, company: company) }

  let(:disk_info_params) do
    {
      managed_asset_id: managed_asset.id,
      detail_category: "disk_info",
      detail_data:  [
        {
          "id"=>"\\\\.\\PHYSICALDRIVE0",
          "name"=>"\\\\.\\PHYSICALDRIVE0",
          "type"=>"SCSI",
          "index"=>"0",
          "model"=>"NVMe BC711 NVMe SK hynix 256GB",
          "pnpId"=>"SCSI\\DISK&VEN_NVME&PROD_BC711_NVME_SK_HY\\4&317B085&0&020000",
          "space"=>"238 GB",
          "systemId"=>"134D5FDE-6FC8-42D7-8B48-EEB9DF1208AA",
          "partitions"=>"6",
          "description"=>"Disk drive",
          "manufacturer"=>"(Standard disk drives)",
          "serialNumber"=>"   FYA8N052412008P13_00000001.",
          "collectionTime"=>"11/08/2023 02:55 pm"
        }
      ]
    }
  end

  let(:crash_reports_params) do
    {
      managed_asset_id: managed_asset.id,
      detail_category: "crash_reports",
      detail_data:  [
        {"name"=>"CloudServicesTopic_2023-10-24-011851_devoss-MacBook-Pro.diag"},
        {"name"=>"CloudServicesTopic_2023-10-20-191710_devoss-MacBook-Pro.diag"}
      ]
    }
  end

  let(:chrome_extensions_params) do
    {
      managed_asset_id: managed_asset.id,
      detail_category: "chrome_extensions_info",
      detail_data:  [
        {"name"=>"Web Store", "version"=>"0.2", "profileName"=>"Danny"}
      ]
    }
  end

  let!(:system_detail1) { SystemDetail.create(disk_info_params) }
  let!(:system_detail2) { SystemDetail.create(crash_reports_params) }
  let!(:system_detail3) { SystemDetail.create(chrome_extensions_params) }

  let!(:options1) do
    {
      columns: {
        disk_info: %w{
          id name type index model pnp_id space system_id partitions description
          manufacturer serial_number collection_time
        }
      },
      attributes: {
        disk_info: %w{
          id name type index model pnp_id space system_id partitions description
          manufacturer serial_number collection_time
        }
      },
      system_detail_category: 'disk_info',
      managed_asset_id: managed_asset.id
    }
  end

  let!(:options2) do
    {
      columns: {
        crash_reports: %w{ name }
      },
      attributes: {
        crash_reports: %w{ name }
      },
      system_detail_category: 'crash_reports',
      managed_asset_id: managed_asset.id
    }
  end

  let!(:options3) do
    {
      columns: {
        chrome_extensions_info: %w{ name version profile_name },
      },
      attributes: {
        chrome_extensions_info: %w{ name version profile_name },
      },
      system_detail_category: 'chrome_extensions_info',
      managed_asset_id: managed_asset.id
    }
  end

  context "Disk Info export with good data" do
    subject { ImportExport::SystemDetails::XlsExport.new(company, options1, nil, nil, nil) }

    it "should pass check" do
      workbook = subject.export
      worksheet = workbook.worksheets[0]
      expect(worksheet.sheet_data.rows.size).to eq 2
      expect(worksheet.sheet_data.rows[1][0].value).to eq(system_detail1.detail_data.first["id"])
      expect(worksheet.sheet_data.rows[1][1].value).to eq(system_detail1.detail_data.first["name"])
      expect(worksheet.sheet_data.rows[1][2].value).to eq(system_detail1.detail_data.first["type"])
      expect(worksheet.sheet_data.rows[1][3].value).to eq(system_detail1.detail_data.first["index"])
      expect(worksheet.sheet_data.rows[1][4].value).to eq(system_detail1.detail_data.first["model"])
      expect(worksheet.sheet_data.rows[1][5].value).to eq(system_detail1.detail_data.first["pnpId"])
      expect(worksheet.sheet_data.rows[1][6].value).to eq(system_detail1.detail_data.first["space"])
      expect(worksheet.sheet_data.rows[1][7].value).to eq(system_detail1.detail_data.first["systemId"])
      expect(worksheet.sheet_data.rows[1][8].value).to eq(system_detail1.detail_data.first["partitions"])
      expect(worksheet.sheet_data.rows[1][9].value).to eq(system_detail1.detail_data.first["description"])
      expect(worksheet.sheet_data.rows[1][10].value).to eq(system_detail1.detail_data.first["manufacturer"])
    end
  end

  context "Crash reports export with good data" do
    subject { ImportExport::SystemDetails::XlsExport.new(company, options2, nil, nil, nil) }

    it "should pass check" do
      workbook = subject.export
      worksheet = workbook.worksheets[0]
      expect(worksheet.sheet_data.rows.size).to eq 3
      expect(worksheet.sheet_data.rows[1][0].value).to eq(system_detail2.detail_data[0]["name"])
      expect(worksheet.sheet_data.rows[2][0].value).to eq(system_detail2.detail_data[1]["name"])
    end
  end

  context "Chrome extensions info export with good data" do
    subject { ImportExport::SystemDetails::XlsExport.new(company, options3, nil, nil, nil) }

    it "should pass check" do
      workbook = subject.export
      worksheet = workbook.worksheets[0]
      expect(worksheet.sheet_data.rows.size).to eq 2
      expect(worksheet.sheet_data.rows[1][0].value).to eq(system_detail3.detail_data.first["name"])
      expect(worksheet.sheet_data.rows[1][1].value).to eq(system_detail3.detail_data.first["version"])
      expect(worksheet.sheet_data.rows[1][2].value).to eq(system_detail3.detail_data.first["profileName"])
    end
  end
end
