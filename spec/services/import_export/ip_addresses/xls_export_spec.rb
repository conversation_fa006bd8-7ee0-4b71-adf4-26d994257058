# frozen_string_literal: true

require 'rails_helper'

describe ImportExport::IpAddresses::XlsExport do
  let(:company) { FactoryBot.create(:company, :with_locations) }
  let(:telecom_service) { FactoryBot.create(:telecom_service, company: company) }
  let(:telecom_provider) { FactoryBot.create(:telecom_provider, company: company) }
  let(:location) { company.locations.first }
  let!(:ip_address) { FactoryBot.create(:ip_address,
                                        telecom_provider: telecom_provider,
                                        telecom_service: telecom_service,
                                        company: company,
                                        location: location) }

  subject { ImportExport::IpAddresses::XlsExport.new(company.reload) }

  context "with good data" do
    it "should pass check" do
      workbook = subject.export
      worksheet = workbook.worksheets[0]
      expect(worksheet.sheet_data.rows.size).to eq 2
      # ip friendly_name telecom_provider_name location_name telecom_service_name notes
      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to be_nil

      expect(worksheet.sheet_data.rows[1][0].value).to eq(ip_address.ip)
      expect(worksheet.sheet_data.rows[1][1].value).to eq(ip_address.friendly_name)
      expect(worksheet.sheet_data.rows[1][2].value).to eq(ip_address.telecom_provider.name)
      expect(worksheet.sheet_data.rows[1][3].value).to eq(ip_address.telecom_service.name)
      expect(worksheet.sheet_data.rows[1][4].value).to eq(ip_address.location.name)
      expect(worksheet.sheet_data.rows[1][5].value).to eq(ip_address.notes)
    end
  end
end