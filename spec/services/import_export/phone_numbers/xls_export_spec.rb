# frozen_string_literal: true

require 'rails_helper'

describe ImportExport::PhoneNumbers::XlsExport do
  let(:company) { FactoryBot.create(:company, :with_locations) }
  let(:telecom_service) { FactoryBot.create(:telecom_service, company: company) }
  let(:telecom_provider) { FactoryBot.create(:telecom_provider, company: company) }
  let(:location) { company.locations.first }
  let!(:phone_number) { FactoryBot.create(:phone_number, country_code: 'US', country_code_number: '1',
                                          telecom_provider: telecom_provider,
                                          telecom_service: telecom_service,
                                          company: company,
                                          location: location) }

  subject { ImportExport::PhoneNumbers::XlsExport.new(company.reload) }

  context "with good data" do
    it "should pass check" do
      workbook = subject.export
      worksheet = workbook.worksheets[0]
      expect(worksheet.sheet_data.rows.size).to eq 2
      # number friendly_name telecom_provider_name location_name telecom_service_name notes
      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to be_nil
      
      expect(worksheet.sheet_data.rows[1][0].raw_value).to eq(phone_number.number.to_i)
      expect(worksheet.sheet_data.rows[1][1].value).to eq(phone_number.friendly_name)
      expect(worksheet.sheet_data.rows[1][2].value).to eq(phone_number.telecom_provider.name)
      expect(worksheet.sheet_data.rows[1][3].value).to eq(phone_number.telecom_service.name)
      expect(worksheet.sheet_data.rows[1][4].value).to eq(phone_number.location.name)
      expect(worksheet.sheet_data.rows[1][5].value).to eq(phone_number.notes)
      expect(worksheet.sheet_data.rows[1][6].value).to eq(phone_number.country_code)
      expect(worksheet.sheet_data.rows[1][7].raw_value).to eq(phone_number.country_code_number.to_i)
    end
  end
end
