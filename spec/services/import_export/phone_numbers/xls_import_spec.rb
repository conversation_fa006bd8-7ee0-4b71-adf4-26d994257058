# frozen_string_literal: true

require 'rails_helper'

describe ImportExport::PhoneNumberImport do
  let(:company) { FactoryBot.create(:company, :with_locations) }

  context "with false headers" do
    let!(:path) {"spec/services/import_export/phone_numbers/test_phone_numbers_upload_incorrect_headers.xlsx" }
    let!(:file) { File.open(Rails.root.join(path)) }

    subject do
      ImportExport::PhoneNumberImport.new(file, company.id)
    end

    let!(:import) {
      subject.process_file
    }

    it "should have a company, and return true" do
      expect(XlsImport.find(subject.xls_import_id).company).to be_present
    end

    it "should have numbers as invalid header" do
      expect(subject.worksheet_data["Phone Number"][:header][0][:valid]).to be_falsey
      expect(subject.worksheet_data["Phone Number"][:header][0][:error]).to eq("Numbers in Phone Number sheet is not a valid header.")
    end

    it "should have correct suggestions for invalid headers" do
      expect(subject.worksheet_data["Phone Number"][:suggestions].length).to eq(1)
      expect(subject.worksheet_data["Phone Number"][:suggestions][0]).to eq(:number)
    end

    it "should have incorrect rows less than equal to 4" do
      expect(subject.worksheet_data["Phone Number"][:incorrect].length <= 4).to be_truthy
    end
  end

  context "with correct headers" do
    let!(:path) { "spec/services/import_export/phone_numbers/test_phone_numbers_upload_with_valid_headers.xlsx" }
    let!(:file) { File.open(Rails.root.join(path)) }

    subject do
      ImportExport::PhoneNumberImport.new(file, company.id)
    end

    let!(:import) {
      subject.process_file
    }

    it "should have a company, and return true" do
      expect(XlsImport.find(subject.xls_import_id).company).to be_present
    end

    it "should have all headers valid" do
      subject.worksheet_data["Phone Number"][:header].each do |header|
        expect(header[:valid] || header["valid"]).to be_truthy
      end
    end
  end
end