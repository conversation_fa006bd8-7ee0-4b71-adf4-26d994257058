# frozen_string_literal: true

require 'rails_helper'

describe ImportExport::Vendors::XlsExport do
  context "with good data" do
    let(:company) { create(:company, :with_locations) }
    let!(:vendor) { create(:vendor, name: "This is a RSpec vendor", company: company) }

    subject { ImportExport::Vendors::XlsExport.new(company) }

    it "should pass check" do
      workbook = subject.export
      worksheet = workbook.worksheets[0]
      expect(worksheet.sheet_data.rows.size).to eq 2
      expect(worksheet.sheet_data.rows[0][0].is_bolded).to eq(true)
      expect(worksheet.sheet_data.rows[0][1].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][2].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][3].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][4].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][5].is_bolded).to eq(nil)
      expect(worksheet.sheet_data.rows[0][0].value).to eq("name")
      expect(worksheet.sheet_data.rows[0][1].value).to eq("url")
    end
  end
end
