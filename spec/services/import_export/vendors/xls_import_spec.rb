require 'rails_helper'

describe ImportExport::VendorImport do
  let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
  let(:company) { create(:company, :with_locations) }
  let!(:user) { create(:user, email: "<EMAIL>") }
  let!(:company_user) { create(:company_user, company: company, user: user, custom_form_id: custom_form.id) }

  context "with correct headers" do
    let!(:path) { "spec/services/import_export/vendors/test_vendors_upload.xlsx" }
    let!(:file) { File.open(Rails.root.join(path)) }

    subject do
      ImportExport::VendorImport.new(file, company.id)
    end

    let!(:import) {
      subject.process_file
    }

    it "should have a company, and return true" do
      expect(XlsImport.find(subject.xls_import_id).company).to be_present
    end

    it "should have all headers valid" do
      subject.worksheet_data['Vendor Info'][:header].each do |header|
        expect(header[:valid] || header["valid"]).to be_truthy
      end
    end
  end

  context "with incorrect headers" do
    let!(:path) { "spec/services/import_export/vendors/test_vendors_upload_bad_header.xlsx" }
    let!(:file) { File.open(Rails.root.join(path)) }

    subject do
      ImportExport::VendorImport.new(file, company.id)
    end

    let!(:import) {
      subject.process_file
    }

    it "should have a company, and return true" do
      expect(XlsImport.find(subject.xls_import_id).company).to be_present
    end

    it "should have asset_types as invalid header" do
      expect(subject.worksheet_data["Vendor Info"][:header][1][:valid]).to be_falsey
      expect(subject.worksheet_data["Vendor Info"][:header][1][:error]).to eq("Bad Header in Vendor Info sheet is not a valid header.")
    end

    it "should have incorrect rows less than equal to 2" do
      expect(subject.worksheet_data["Vendor Info"][:incorrect].length).to eq(1)
    end
  end
end
