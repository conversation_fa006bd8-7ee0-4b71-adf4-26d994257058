require 'rails_helper'

describe GeneralTransactionRecurrenceService do
  let!(:company) { FactoryBot.create(:company) }
  let!(:vendor) { FactoryBot.create(:vendor, company_id: company.id) }

  context "creating a transaction" do
    let!(:transaction) {
      FactoryBot.create(:general_transaction,
                        vendor_id: vendor.id,
                        recurring: true,
                        status: :recognized,
                        company_id: company.id,
                        amount: 200,
                        recurrance_end_date: Date.today + 2.years,
                        transaction_date: Date.today - 1.month
      )
    }

    subject { described_class.new(company) }

    it "should create a new transaction with today as the transaction date" do
      subject.check_recurring_transactions
      expect(company.general_transactions.count).to eq(2)
      expect(company.general_transactions.last.transaction_date).to eq(Date.today)
    end
  end

  context "creating a transaction on the last day of the month with months that have different number of days" do
    let!(:end_of_month_transaction) {
      FactoryBot.create(:general_transaction,
                        vendor_id: vendor.id,
                        recurring: true,
                        status: :recognized,
                        company_id: company.id,
                        amount: 100,
                        recurrance_end_date: "#{Date.today.year}-01-31".to_date + 2.years,
                        transaction_date: "#{Date.today.year}-01-31".to_date
      )
    }

    subject { described_class.new(company, "#{Date.today.year}-01-31".to_date.next_month) }

    it "should create a new transaction for the end of Febuary" do
      subject.check_recurring_transactions
      expect(company.general_transactions.count).to eq(2)
      expect(company.general_transactions.last.transaction_date).to eq("#{Date.today.year}-02-#{Date.leap?(Date.today.year) ? '29' : '28'}".to_date)
    end
  end
end
