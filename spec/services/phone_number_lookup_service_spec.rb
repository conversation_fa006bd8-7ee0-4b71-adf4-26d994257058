require 'rails_helper'

describe PhoneNumberLookupService do
  subject { described_class.new({number: "+***********"}) }
    account_sid = Rails.application.credentials.twilio[:account_sid]
    auth_token = Rails.application.credentials.twilio[:auth_token]
    @client = Twilio::REST::Client.new(account_sid, auth_token)

  context "Phone Number Lookup Service on call" do
=begin
    # We need to uncomment this once <PERSON><PERSON><PERSON> is back
    it 'stubs retrieve number info' do
      response = subject.call()
      expect(response.success?).to eq(true)
    end
=end
  end
end
