require 'rails_helper'
include CompanyUserHelper

RSpec.describe Twilio::SendSms do
  create_company_and_user
  let(:number) { '**********' }
  let(:message) { 'Hello, Twi<PERSON>!' }

  subject { described_class.new(number, message, company) }

  describe '#send_sms' do
    it 'sends an SMS using the customer care number' do
      client = instance_double(Twilio::REST::Client)
      messages = instance_double(Twilio::REST::Api::V2010::AccountContext::MessageList)

      expect(Twilio::REST::Client).to receive(:new)
        .with(Rails.application.credentials.twilio[:account_sid], Rails.application.credentials.twilio[:auth_token])
        .and_return(client)
      expect(client).to receive(:messages).and_return(messages)
      expect(messages).to receive(:create).with(
        from: Rails.application.credentials.twilio[:customer_care_number],
        to: number,
        body: message
      )

      subject.send_sms
    end
  end

  describe '#send_code' do
    it 'sends an SMS using the MFA number' do
      client = instance_double(Twilio::REST::Client)
      messages = instance_double(Twilio::REST::Api::V2010::AccountContext::MessageList)

      expect(Twilio::REST::Client).to receive(:new)
        .with(Rails.application.credentials.twilio[:account_sid], Rails.application.credentials.twilio[:auth_token])
        .and_return(client)
      expect(client).to receive(:messages).and_return(messages)
      expect(messages).to receive(:create).with(
        from: Rails.application.credentials.twilio[:mfa_number],
        to: number,
        body: message
      )

      subject.send_code
    end
  end
end
