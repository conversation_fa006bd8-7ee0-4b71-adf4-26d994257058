require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

describe ReportsData::ReportData do
  now = Time.zone.local(2022, 6, 5, 13, 10, 0)
  
  before do
    Timecop.freeze(now)
  end

  after do
    Timecop.return
  end

  create_company_and_user

  let(:today) {
    now.to_date
  }

  let(:time_frame_all) {
    { timeframe_selection: "all_time" }
  }    

  let(:time_frame_year) {
    { timeframe_selection: "year" }
  }   
  let(:time_frame_custom)  {
    { timeframe_selection: "custom",
      start_date: today - 1.year,
      end_date: today + 1.year }
  }
  

  let(:contract_module) { "contracts" }
  let(:vendor_module) { "vendors" }
  let(:help_ticket_module) { "help_tickets" }

  context "contracts" do
    let!(:contract1) { 
      Timecop.freeze(now) { create(:contract, company: company) }
    }
    let!(:contract2) { 
      Timecop.freeze(now) { create(:contract, name: "Test2", company: company) }
    }

    let!(:alert_dates) { create(:alert_date, date: (today - 1.year )) }

    let!(:contract3) { create(:contract, name: "Test3",
      start_date: today - 2.year,
      end_date: today,
      company: company) }

    let!(:subject1) { ReportsData::ReportData.new(contract_module, time_frame_all, company) }
    let!(:subject2) { ReportsData::ReportData.new(contract_module, time_frame_year, company) }
    let!(:subject3) { ReportsData::ReportData.new(contract_module, time_frame_custom, company) }

    it "should pass all checks for all_time" do
      data = subject1.fetch_records
      record = data.pluck("name")

      expect(data.length).to eq(3)
      expect(record).to include("Test3", "Test2", "Internet")
      expect(data.find(contract3.id).start_date.to_date).to eq(today - 2.year)
      expect(data.find(contract3.id).end_date.to_date).to eq(today)
      expect(data.find(contract2.id).start_date.to_date).to eq(today)
      expect(data.find(contract2.id).end_date.to_date).to eq(today + 1.year)
      expect(data.find(contract1.id).start_date.to_date).to eq(today)
      expect(data.find(contract1.id).end_date.to_date).to eq(today + 1.year)
    end

    it "should pass all checks for current year" do
      data = subject2.fetch_records
      record = data.pluck("name")

      expect(data.length).to eq(1)
      expect(record).to include("Test3")
      expect(data[0].start_date.to_date).to eq(today.to_date - 2.year)
      expect(data[0].end_date.to_date).to eq(today)
    end

    it "should pass all checks for custom time frame" do
      data = subject3.fetch_records
      record = data.pluck("name")

      expect(data.length).to eq(2)
      expect(record).to include("Test2", "Internet")
      expect(data[0].start_date.to_date).to eq(today)
      expect(data[0].end_date.to_date).to eq(today + 1.year)
      expect(data[1].start_date.to_date).to eq(today)
      expect(data[1].end_date.to_date).to eq(today + 1.year)
    end
  end

  context "vendors" do
    let!(:vendor1) { create(:vendor, company: company, created_at: now) }
    let!(:vendor2) { create(:vendor, name: "Test2", company: company, created_at: now) }
    let!(:vendor3) { create(:vendor, name: "Test3", created_at: today - 2.year, updated_at: today, company: company) }

    let!(:subject1) { ReportsData::ReportData.new(vendor_module, time_frame_all, company) }
    let!(:subject2) { ReportsData::ReportData.new(vendor_module, time_frame_year, company) }
    let!(:subject3) { ReportsData::ReportData.new(vendor_module, time_frame_custom, company) }

    it "should pass all checks for all_time" do
      data = subject1.fetch_records
      record = data.pluck("name")

      expect(data.length).to eq(3)
      expect(record).to include("Test3", "Test2", "Test Vendor")
      expect(data.find(vendor3.id).created_at.to_date).to eq(today - 2.year)
      expect(data.find(vendor2.id).created_at.to_date).to eq(today)
      expect(data.find(vendor1.id).created_at.to_date).to eq(today)
    end

    it "should pass all checks for current year" do
      data = subject2.fetch_records
      record = data.pluck("name")

      expect(data.length).to eq(2)
      expect(record).to include("Test2", "Test Vendor")
      expect(data[0].created_at.to_date).to eq(today)
    end

    it "should pass all checks for custom time frame" do
      data = subject3.fetch_records
      record = data.pluck("name")

      expect(data.length).to eq(2)
      expect(record).to include("Test2", "Test Vendor")
      expect(data[0].created_at.to_date).to eq(today)
    end
  end

  context "help tickets" do
    let!(:help_ticket1) {
      ticket_params = {
        'Subject' => "Broken Monitor",
        'Created By' => company_user.contributor_id,
        'Assigned To' => company_user.contributor_id,
        'Status' => 'Open',
        'Priority' => "medium",
        'Description' => "The red robin sits in the tree.",
      }
      ticket = create_ticket(ticket_params)
      ticket.company = company
      ticket.workspace = workspace
      ticket.created_at = today - 2.years
      ticket.save!
      ticket
    }
    let!(:help_ticket2) {
      ticket_params = {
        'Subject' => "Laptop broken",
        'Created By' => company_user.contributor_id,
        'Assigned To' => company_user.contributor_id,
        'Status' => 'Open',
        'Priority' => "medium",
        'Description' => "The red robin sits in the tree.",
      }
      ticket = create_ticket(ticket_params)
      ticket.company = company
      ticket.workspace = workspace
      ticket.created_at = today
      ticket.save!
      ticket
    }
    let!(:help_ticket3) {
      ticket_params = {
        'Subject' => "SSD not working",
        'Created By' => company_user.contributor_id,
        'Assigned To' => company_user.contributor_id,
        'Status' => 'Open',
        'Priority' => "medium",
        'Description' => "The red robin sits in the tree.",
      }
      ticket = create_ticket(ticket_params)
      ticket.company = company
      ticket.workspace = workspace
      ticket.created_at = today + 1.year
      ticket.save!
      ticket
    }

    let!(:subject4) { ReportsData::ReportData.new(help_ticket_module, time_frame_all, company) }
    let!(:subject5) { ReportsData::ReportData.new(help_ticket_module, time_frame_year, company) }
    let!(:subject6) { ReportsData::ReportData.new(help_ticket_module, time_frame_custom, company) }

    ticket_expected_subject = ["SSD not working", "Broken Monitor", "Laptop broken"]

    it "should pass all checks for all_time" do
      data = subject4.fetch_records
      ssd_ticket = data.detect { |ticket| ticket.subject == "SSD not working" }

      expect(data.length).to eq(3)
      expect(ticket_expected_subject.include?(data[0].subject)).to eq(true)
      expect(ticket_expected_subject.include?(data[1].subject)).to eq(true)
      expect(ticket_expected_subject.include?(data[2].subject)).to eq(true)
      expect(ssd_ticket.created_at.to_date).to eq(today + 1.year)
    end

    it "should pass all checks for current year" do
      data = subject5.fetch_records

      expect(data.length).to eq(1)
      expect(ticket_expected_subject.include?(data[0].subject)).to eq(true)
      expect(data[0].created_at.to_date).to eq(today)
    end

    it "should pass all checks for custom time frame" do
      data = subject6.fetch_records
      ssd_ticket = data.detect { |ticket| ticket.subject == "SSD not working" }

      expect(data.length).to eq(2)
      expect(ticket_expected_subject.include?(data[0].subject)).to eq(true)
      expect(ssd_ticket.created_at.to_date).to eq(today + 1.year)
    end
  end
end
