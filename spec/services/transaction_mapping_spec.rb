require 'rails_helper'

describe 'maps a transaction' do
  let(:company) { FactoryBot.create(:company) }
  let(:company_user) { company.company_users.first }
  let!(:general_transaction) { FactoryBot.create(:general_transaction, company: company, category_id: '') }

  subject { TransactionMapping.new({ company: company, company_user: company_user, transaction: general_transaction, primary_integration_flag: true }, 'Plaid') }

  context "#is_excluded?" do
    context "if vendor is payroll" do
      let!(:general_transaction) { FactoryBot.create(:general_transaction,
                                                     company: company,
                                                     category_id: '',
                                                     amount: 6000,
                                                     product_name: '<PERSON><PERSON>')
      }
      it "should return true if the vendor is a payroll vendor" do
        expect(subject.send(:is_excluded?)).to be_truthy
      end
    end

    context "if vendor is payroll but name is wonky" do
      let!(:general_transaction) { FactoryBot.create(:general_transaction,
                                                     company: company,
                                                     category_id: '',
                                                     amount: 6000,
                                                     product_name: 'GUSTO DES:TAX 451859 ID:6semjnib7qs INDN:Stratum Inc. CO ID:1453942850 CCD')
      }
      it "should return true if the vendor is a payroll vendor" do
        expect(subject.send(:is_excluded?)).to be_truthy
      end
    end

    context "if amount is too little" do
      let!(:general_transaction) { FactoryBot.create(:general_transaction,
                                                     company: company,
                                                     category_id: '',
                                                     amount: 100,
                                                     product_name: 'Gusto')
      }
      it "should return false if the amount is too small" do
        expect(subject.send(:is_excluded?)).to be_falsey
      end
    end

    context "vendor is not a payroll vendor" do
      let!(:general_transaction) { FactoryBot.create(:general_transaction,
                                                     company: company,
                                                     category_id: '',
                                                     amount: 6000,
                                                     product_name: '')
      }
      it "should return false if the vendor is not a payroll vendor" do
        expect(subject.send(:is_excluded?)).to be_falsey
      end
    end
  end

  context "#is_excluded_term?" do
    context "transaction is a sweep" do
      let!(:general_transaction) { FactoryBot.create(:general_transaction,
                                                     company: company,
                                                     category_id: '',
                                                     amount: 10000,
                                                     product_name: 'Sweep Money Market')
      }
      it "should return true if the transaction is a sweep" do
        expect(subject.send(:is_excluded?)).to be_truthy
      end
    end

    context "transaction is interest" do
      let!(:general_transaction) { FactoryBot.create(:general_transaction,
                                                     company: company,
                                                     category_id: '',
                                                     amount: 100,
                                                     product_name: 'Interest for Account 293283')
      }
      it "should return true if the transaction is interest" do
        expect(subject.send(:is_excluded?)).to be_truthy
      end
    end

    context "transaction is sweep-interest" do
      let!(:general_transaction) { FactoryBot.create(:general_transaction,
                                                     company: company,
                                                     category_id: '',
                                                     amount: 100,
                                                     product_name: 'Sweep-Interest')
      }
      it "should return true if the transaction is sweep-interest" do
        expect(subject.send(:is_excluded?)).to be_truthy
      end
    end
  end

  context "#is_excluded_phrase?" do
    context "transaction contains 'ONLINE PAYMENT'" do
      let!(:general_transaction) { FactoryBot.create(:general_transaction,
                                                     company: company,
                                                     category_id: '',
                                                     amount: 10000,
                                                     product_name: 'ONLINE PAYMENT - THANK YOU')
      }
      it "should return true if the transaction includes online payment" do
        expect(subject.send(:is_excluded?)).to be_truthy
      end
    end

    context "transaction contains 'PAYMENT RECEIVED'" do
      let!(:general_transaction) { FactoryBot.create(:general_transaction,
                                                     company: company,
                                                     category_id: '',
                                                     amount: 10000,
                                                     product_name: 'PAYMENT RECEIVED - THANK YOU')
      }
      it "should return true if the transaction includes online payment" do
        expect(subject.send(:is_excluded?)).to be_truthy
      end
    end
  end

  context "with a previous mapped transaction" do
    it 'should map to the existing vendor' do
      other = Category.find_by(name: 'Other', module_type: 'company')
      vendor = FactoryBot.create(:vendor, company: company, name: 'Big Test Vendor')
      old_transaction = FactoryBot.create(:general_transaction,
                                          company_id: company.id,
                                          category_id: other.id,
                                          status: 'recognized',
                                          product_name: 'Big Test Vendor',
                                          vendor_id: vendor.id
        )
      general_transaction.update(product_name: 'Big Test Vendor')
      old_transaction.general_transaction_tags.create(tag: "TEST", company_id: company.id)
      subject.match_vendor
      vendor = general_transaction.vendor
      expect(vendor).to be_present
      expect(vendor.name).to eq('Big Test Vendor')
      expect(general_transaction.status).to eq('recognized')
      expect(general_transaction.general_transaction_tags.count).to eq(1)
      expect(general_transaction.general_transaction_tags.first.tag).to eq("default")
    end

    it 'should map to existing product of the vendor' do
      other = Category.find_by(name: 'Other', module_type: 'company')
      vendor = FactoryBot.create(:vendor, company: company, name: 'Big Test Vendor')
      product = FactoryBot.create(:product, company: company, vendor: vendor)
      old_transaction = FactoryBot.create(:general_transaction,
                                          company_id: company.id,
                                          category_id: other.id,
                                          status: 'recognized',
                                          product_name: 'Big Test Vendor',
                                          vendor_id: vendor.id,
                                          product_id: product.id)

      mapping = FactoryBot.create(:transaction_products_mapping,
                                  transaction_name: 'Big Test Vendor',
                                  preference: 0,
                                  company: company,
                                  product_id: product.id)

      general_transaction.update(product_name: 'Big Test Vendor')
      old_transaction.general_transaction_tags.create(tag: "TEST", company_id: company.id)
      subject.match_vendor
      vendor = general_transaction.vendor
      product = general_transaction.product
      expect(vendor).to be_present
      expect(product).to be_present
      expect(product.name).to eq('MyProduct')
      expect(vendor.name).to eq('Big Test Vendor')
      expect(general_transaction.status).to eq('recognized')
      expect(general_transaction.general_transaction_tags.count).to eq(1)
      expect(general_transaction.general_transaction_tags.first.tag).to eq("default")
    end
  end

  context "when a transaction has an abbreviation at the end" do
    it "should match the right vendor with an LLC" do
      other = company.categories.find_by(name: 'Other')
      expected_vendor = FactoryBot.create(:vendor, name: 'This Vendor', company_id: company.id, category_id: other.id)
      subject.transaction.update(product_name: 'This Vendor, LLC')
      subject.match_vendor
      general_transaction = subject.transaction
      vendor = general_transaction.vendor
      expect(general_transaction.status).to eq('recognized')
      expect(vendor).to be_present
      expect(vendor.id).to eq(expected_vendor.id)
    end

    it "should match the right vendor with a ltd. (with a period)" do
      other = company.categories.find_by(name: 'Other')
      expected_vendor = FactoryBot.create(:vendor, name: 'Limited Vendor', company_id: company.id, category_id: other.id)
      subject.transaction.update(product_name: 'Limited Vendor, ltd.')
      subject.match_vendor
      general_transaction = subject.transaction
      vendor = general_transaction.vendor
      expect(general_transaction.status).to eq('recognized')
      expect(vendor).to be_present
      expect(vendor.id).to eq(expected_vendor.id)
    end
  end

  context "when a transaction maps to a default vendor" do
    let!(:subject3) { TransactionMapping.new({ company: company, company_user: company_user, transaction: general_transaction, primary_integration_flag: true }, 'Quickbooks') }

    it 'should create a new discovered vendor from the default' do
      other = company.categories.find_by(name: 'Other')
      general_transaction.update(product_name: 'Grasshopper')
      subject.match_vendor
      discovered_vendor = general_transaction.discovered_vendor
      expect(discovered_vendor).to be_present
      expect(discovered_vendor.name).to eq('Grasshopper')
      expect(discovered_vendor.category_id).to eq(other.id)
      expect(general_transaction.status).to eq('unconfirmed')
    end

    it 'should create a new vendor from the default' do
      other = company.categories.find_by(name: 'Other')
      general_transaction.update(product_name: 'Grasshopper')
      subject3.match_vendor
      vendor = general_transaction.vendor
      expect(vendor).to be_present
      expect(vendor.name).to eq('Grasshopper')
      expect(vendor.category_id).to eq(other.id)
      expect(general_transaction.status).to eq('recognized')
      expect(general_transaction.general_transaction_tags).to be_present
      expect(general_transaction.general_transaction_tags.first.tag).to eq('SaaS')
    end
  end

  context "when a transaction maps to an existing vendor" do
    it 'should map to the existing vendor' do
      other = company.categories.find_by(name: 'Other')
      expected_vendor = FactoryBot.create(:vendor, name: 'Big Test Vendor', company_id: company.id, category_id: other.id)
      subject.transaction.update(product_name: 'Big Test Vendor')
      subject.match_vendor
      general_transaction = subject.transaction
      vendor = general_transaction.vendor
      expect(general_transaction.status).to eq('recognized')
      expect(vendor).to be_present
      expect(vendor.id).to eq(expected_vendor.id)
      expect(vendor.category_id).to eq(other.id)
      expect(vendor.name).to eq('Big Test Vendor')
      expect(general_transaction.general_transaction_tags.count).to eq(1)
      expect(general_transaction.general_transaction_tags.first.tag).to eq("default")
    end
  end

  context "when a transaction is close to an existing vendor but not exactly" do
    it 'should create a vendor' do
      general_transaction.category_id = "19019000"
      general_transaction.product_name = 'Grasshopper Big'
      subject.match_vendor
      vendor = general_transaction.vendor
      expect(vendor).to_not be_present
      expect(general_transaction).to_not be_recognized
    end
  end

  context "when a transaction has a merchant and that discovered vendor does not exist" do
    let!(:subject1) { TransactionMapping.new({ company: company, company_user: company_user, transaction: general_transaction, primary_integration_flag: true, merchant: "Microsoft" }, source: 'Plaid') }

    it 'should create a discovered vendor' do
      subject1.match_vendor
      discovered_vendor = general_transaction.discovered_vendor
      expect(discovered_vendor).to be_present
      expect(discovered_vendor.name).to eq("Microsoft")
    end
  end

  context "when a transaction has a merchant and that discovered vendor does exist" do
    let!(:discovered_vendor) { FactoryBot.create(:discovered_vendor, name: "Apple", company_id: company.id) }
    let!(:subject2) { TransactionMapping.new({ company: company, company_user: company_user, transaction: general_transaction, primary_integration_flag: true, merchant: "Apple" }, source: 'Plaid') }

    it 'should not create a vendor' do
      subject2.match_vendor
      expect(discovered_vendor.new_record?).to be_falsey
      expect(discovered_vendor.general_transactions.count).to eq(1)
    end
  end

  context "when a transaction is close to a default vendor but not exactly" do
    it 'should create a vendor' do
      general_transaction.category_id = "19019000"
      general_transaction.product_name = '15 Fiver'
      subject.match_vendor
      vendor = general_transaction.vendor
      expect(vendor).to_not be_present
      expect(general_transaction).to_not be_recognized
    end
  end

  context "when a transaction maps to an existing vendor with a newly added default tag" do
    it 'should include that tag' do
      other = Category.find_by(name: 'Other', module_type: 'company')
      vendor = FactoryBot.create(:vendor, name: 'Big Test Vendor', company_id: company.id)
      # Create existing transaction
      FactoryBot.create(:general_transaction,
                        company_id: company.id,
                        category_id: other.id,
                        status: 'recognized',
                        product_name: 'Big Test Vendor',
                        vendor_id: vendor.id
      )
      subject.transaction.update(product_name: 'Big Test Vendor')
      general_transaction = subject.transaction
      subject.match_vendor
      expect(general_transaction.general_transaction_tags.count).to eq(1)
      expect(general_transaction.general_transaction_tags.first.tag).to eq("default")
    end
  end
end