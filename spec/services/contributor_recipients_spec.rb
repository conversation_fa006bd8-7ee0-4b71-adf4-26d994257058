# frozen_string_literal: true

require 'rails_helper'

describe ContributorRecipients do
  let(:granted_access_at) { 1.day.ago }

  context "with a simple list of contributors" do
    let(:company_user) { FactoryBot.create(:company_user, granted_access_at: granted_access_at) }
    let(:user) { company_user.user }
    let(:company) { company_user.company }

    context "#flatten" do
      subject { described_class.flatten(contributors) }
      let!(:group) { create(:group, company: company) }
      let!(:group_members) { [
        GroupMember.create(group: group, contributor: company_user.contributor)
      ] }
      let(:contributors) {
        [
          group.contributor
        ]
      }
      it "should return just the company user contributors" do
        expected = Set.new([group.contributor, company_user.contributor])
        actual = Set.new(subject)
        expect(actual).to eq(expected)
      end
    end
  end

  context "with a circular dependency of contributors" do
    let(:company) { company_user1.company }
    let!(:company_user1) { FactoryBot.create(:company_user, granted_access_at: granted_access_at) }
    let!(:company_user2) { FactoryBot.create(:company_user, granted_access_at: granted_access_at) }
    let!(:group1) { create(:group, name: 'Group1', company: company) }
    let!(:group1_members) { [
      GroupMember.create(group: group1, contributor: company_user1.contributor),
      GroupMember.create(group: group1, contributor: group2.contributor)
    ] }
    let!(:group2) { create(:group, name: 'Group2', company: company) }
    let!(:group2_members) { [
      GroupMember.create(group: group2, contributor: company_user2.contributor),
      GroupMember.create(group: group2, contributor: group2.contributor)
    ] }

    context "#flatten" do
      subject { described_class.flatten(contributors) }
      let(:contributors) {
        [
          group1.contributor,
          group2.contributor
        ]
      }
      it "should return just the company user contributors" do
        expected = [
          group1.contributor,
          group2.contributor,
          company_user1.contributor,
          company_user2.contributor
        ]
        expect(Set.new(subject)).to eq(Set.new(expected))
      end
    end

    context "#groups" do
      subject { described_class.groups(contributors) }

      let(:contributors) {
        [
          group1.contributor,
          group2.contributor,
          company_user1.contributor,
          company_user2.contributor
        ]
      }

      it "should return just the company user contributors" do
        expect(Set.new(subject)).to eq(Set.new([group1.contributor, group2.contributor]))
      end
    end

    context "#company_users" do
      subject { described_class.company_users(contributors) }

      let(:contributors) {
        [
          group1.contributor,
          group2.contributor,
          company_user1.contributor,
          company_user2.contributor
        ]
      }

      it "should return just the company user contributors" do
        expect(Set.new(subject)).to eq(Set.new([company_user1.contributor, company_user2.contributor]))
      end
    end
  end
end

