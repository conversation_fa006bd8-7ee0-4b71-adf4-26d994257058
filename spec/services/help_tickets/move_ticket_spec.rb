require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

describe HelpTickets::MoveTicket do
  create_company_and_user

  subject { described_class.new(params, company, company_user, workspace1, ticket_id) }
  let(:workspace1) { company.default_workspace }
  let(:workspace2) { create(:workspace, company: company) }
  let(:custom_form) { company.default_workspace.custom_forms.first }
  let(:custom_form2) { CustomForm.create(company: company, company_module: 'helpdesk', form_name: 'Form2', custom_form_fields_attributes: custom_form_fields_attributes) }
  let!(:company_user2) { create(:company_user, company: company_user.company) }
  let!(:help_ticket) {
    ticket_params = {
      'Subject' => "First",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => "medium",
      workspace_id: workspace1.id
    }
    create_ticket(ticket_params)
  }

  let!(:help_ticket2) {
    ticket_params = {
      'Subject' => "Second",
      'Created By' => company_user2.contributor_id,
      'Assigned To' => [company_user2.contributor_id],
      'Followers' => [company_user2.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'In Progress',
      'Priority' => "high",
      'Checkbox' => "Desktop",
      workspace_id: workspace1.id
    }
    create_ticket(ticket_params)
  }

  let!(:help_ticket3) {
    ticket_params = {
      'Subject' => "Second",
      'Created By' => company_user2.contributor_id,
      'Assigned To' => [company_user2.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'In Progress',
      'Priority' => "high",
      'Checkbox' => "Desktop",
      workspace_id: workspace1.id
    }
    t = create_ticket(ticket_params)

    help_ticket2.update(active_ticket_id: t.id)
    t
  }

  let(:custom_form_fields_attributes) {
    [
      {
        field_attribute_type: "text", 
        label:"Subject",
        name:"subject",
      },
      {
        field_attribute_type: "priority",
        label: "Priority",
        name: "priority",
        options: CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS,
      },
      {
        field_attribute_type: "status",
        label: "Status",
        name: "status",
        options: CustomFormFieldTemplate::DEFAULT_STATUS_OPTIONS, 
      },
      {
        field_attribute_type: "people_list",
        label: "Created By",
        name: "created_by",
      },
      {
        field_attribute_type: "people_list",
        label: "Assigned To",
        name: "assigned_to",
      }
    ]
  }

  describe "#move_ticket" do
    context "with valid params" do
      let(:ticket_id) { help_ticket2.id }
      let(:params) {
        {
          'target_workspace_id' => workspace2.id,
          'target_custom_form_id' => custom_form2.id,
          'help_ticket_id' => help_ticket2.id,
          'proceed' => true
        }
      }
      let(:results) { subject.call }

      it "will move ticket into workspace 2" do
        results
        new_ticket = HelpTicket.find_by(workspace_id: workspace2.id)
        status = new_ticket.custom_form_values.includes(:custom_form_field).find { |value| value.custom_form_field.name == "status" }.value_str
        priority = new_ticket.custom_form_values.includes(:custom_form_field).find { |value| value.custom_form_field.name == "priority" }.value_str
        created_by = new_ticket.custom_form_values.includes(:custom_form_field).find { |value| value.custom_form_field.name == "created_by" }.value_int
        assigned_to = new_ticket.custom_form_values.includes(:custom_form_field).find { |value| value.custom_form_field.name == "assigned_to" }.value_int
        expect(new_ticket.subject).to eq("Second")
        expect(status).to eq("In Progress")
        expect(priority).to eq("high")
        expect(created_by).to eq(company_user2.contributor_id)
        expect(assigned_to).to eq(company_user2.contributor_id)
      end
    end

    context "with field not present in another workspace custom form" do
      let(:ticket_id) { help_ticket.id }

      let(:params) { 
        { 
          'target_workspace_id' => workspace2.id,
          'target_custom_form_id' => custom_form2.id,
          'help_ticket_id' => help_ticket.id,
          'proceed' => true
        }
      }
      let(:results) { subject.call }

      it "with lost data, will move ticket into workspace 2" do
        results
        new_ticket = HelpTicket.find_by(workspace_id: workspace2.id)
        status = new_ticket.custom_form_values.includes(:custom_form_field).find { |value| value.custom_form_field.name == "status" }.value_str
        priority = new_ticket.custom_form_values.includes(:custom_form_field).find { |value| value.custom_form_field.name == "priority" }.value_str
        created_by = new_ticket.custom_form_values.includes(:custom_form_field).find { |value| value.custom_form_field.name == "created_by" }.value_int
        assigned_to = new_ticket.custom_form_values.includes(:custom_form_field).find { |value| value.custom_form_field.name == "assigned_to" }.value_int
        followers = new_ticket.custom_form_values.includes(:custom_form_field).find { |value| value.custom_form_field.name == "followers" }
        expect(new_ticket.subject).to eq("First")
        expect(status).to eq("Open")
        expect(priority).to eq("medium")
        expect(created_by).to eq(company_user.contributor_id)
        expect(assigned_to).to eq(company_user.contributor_id)
        expect(followers).to eq(nil)
      end
    end

    context "with valid params" do
      let(:ticket_id) { help_ticket2.id }

      let(:params) { 
        {
          'target_workspace_id' => workspace2.id,
          'target_custom_form_id' => custom_form2.id,
          'help_ticket_id' => help_ticket2.id,
          'proceed' => false 
        }
      }
      let(:results) { subject.call }

      it "will show the fields whose data will be lost if ticket is moved" do
        fields = results[:missing_fields]
        expect(fields[0]).to eq("Followers")
      end
    end
  end
end
