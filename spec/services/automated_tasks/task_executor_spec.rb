# frozen_string_literal: true

require 'rails_helper'
include Help<PERSON>icketHelper

describe AutomatedTasks::TaskExecutor do
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:company) {
    my_company = company_user.company
    my_company
  }
  let(:workspace) { company.default_workspace }
  let(:user) { company_user.user }
  let(:help_ticket) {
    create_ticket(ticket_params)
  }
  let(:help_ticket_comment) { HelpTicketComment.create(help_ticket: help_ticket, comment_body: "<div>Comment</div>") }
  let(:custom_form) { company.default_workspace.custom_forms.first }
  let(:form_field) { custom_form.custom_form_fields.find_by(name: "subject") }

  let!(:automated_task) do
    task = AutomatedTasks::AutomatedTask.new(workspace: workspace, order: 2)
    task_event = AutomatedTasks::TaskEvent.new
    event_type = AutomatedTasks::EventType.find_by(name: "{a ticket} is created")
    task_event.event_type = event_type
    object = AutomatedTasks::EventDetail.new
    object.event_subject_type = event_type.children.find_by(name: "/a ticket/ with {a form field}")
    object.value = {
      custom_forms: [
        {
          id: custom_form.id,
          form_name: "Base Ticket"
        }
      ],
      form_field: {
        name: "subject",
        field_attribute_type: "text"
      },
      text: "AAAA"
    }.to_json
    task_event.event_details << object
    task.task_events << task_event

    task_action = AutomatedTasks::TaskAction.new
    task_action.action_type = AutomatedTasks::ActionType.find_by(name: 'set a [form field]',
                                                                 module: 'help_tickets',
                                                                 model: 'HelpTicket')
    task_action.value = action_value.to_json
    task_action.save!
    task.task_actions << task_action
    task.save!
    task
  end
  let(:job_args) { AutomatedTasks::TaskActionsWorker.jobs.map { |job| job['args'] } }

  subject { described_class.new(nil, help_ticket).call }

  context "with priority action" do
    let(:action_field) { custom_form.custom_form_fields.find_by(name: 'priority') }
    let(:action_value) {
      {
        custom_forms: [
          {
            id: custom_form.id,
            name: custom_form.form_name,
          }
        ],
        form_field: {
          name: action_field.name,
          field_attribute_type: action_field.field_attribute_type,
        },
        form_value: "medium",
      }
    }

    let(:ticket_params) {
      {
        'Subject' => "[AAAA] Test Subject",
        'Created By' => company_user.contributor_id,
        'Status' => 'Open',
        'Priority' => "low",
        company: company,
      }
    }

    it "executes the task action" do
      subject
      perform_actions
      form_value = help_ticket.custom_form_values.find_by(custom_form_field: action_field)
      expect(form_value&.value_str).to eq('medium')
    end

    it "create a ticket activity" do
      subject
      perform_actions
      activity = help_ticket.help_ticket_activities.first
      expect(activity).to be_present
    end
  end

  context "with impacted devices" do
    context "without existing impacted devices" do
      let(:action_field) { custom_form.custom_form_fields.find_by(name: 'impacted_devices') }
      let(:managed_asset) { create(:managed_asset, company: company) }
      let(:action_value) {
        {
          custom_forms: [
            {
              id: custom_form.id,
              name: custom_form.form_name,
            }
          ],
          form_field: {
            name: action_field.name,
            field_attribute_type: action_field.field_attribute_type,
          },
          form_value: [ managed_asset.id ],
        }
      }

      let(:ticket_params) {
        {
          'Subject' => "[AAAA] Test Subject",
          'Created By' => company_user.contributor_id,
          'Status' => 'Open',
          'Priority' => "low",
          company: company,
        }
      }

      it "executes the task action" do
        subject
        perform_actions
        form_value = help_ticket.custom_form_values.find_by(custom_form_field: action_field)
        expect(form_value&.value_int).to eq(managed_asset.id)
      end

      it "create a ticket activity" do
        subject
        perform_actions
        activity = help_ticket.help_ticket_activities.first
        expect(activity).to be_present
      end
    end

    context "with existing impacted devices" do
      let(:action_field) { custom_form.custom_form_fields.find_by(name: 'impacted_devices') }
      let(:managed_asset) { create(:managed_asset, company: company) }
      let(:action_value) {
        {
          custom_forms: [
            {
              id: custom_form.id,
              name: custom_form.form_name,
            }
          ],
          form_field: {
            name: action_field.name,
            field_attribute_type: action_field.field_attribute_type,
          },
          form_value: [ managed_asset.id ],
        }
      }

      let(:ticket_params) {
        {
          'Subject' => "[AAAA] Test Subject",
          'Created By' => company_user.contributor_id,
          'Impacted Devices' => [managed_asset.id],
          'Status' => 'Open',
          'Priority' => "low",
          company: company,
        }
      }

      it "executes the task action" do
        ticket = help_ticket.reload
        form_value = ticket.custom_form_values.find_by(custom_form_field: action_field)
        expect(form_value&.value_int).to eq(managed_asset.id)
        form_values = ticket.custom_form_values.where(custom_form_field: action_field)
        expect(form_values.count).to eq(1)
      end

      it "create a ticket activity" do
        ticket = help_ticket.reload
        activity = ticket.help_ticket_activities.where.not(activity_type: 'source').first
        expect(activity).to be_blank
      end
    end
  end

  context "with direct assignment assigned to" do
    let(:action_field) { custom_form.custom_form_fields.find_by(name: 'assigned_to') }
    let(:contributor_id) { company.company_users.first.contributor_id }
    let(:action_value) {
      {
        custom_forms: [
          {
            id: custom_form.id,
            name: custom_form.form_name,
          }
        ],
        form_field: {
          name: action_field.name,
          field_attribute_type: action_field.field_attribute_type,
        },
        form_value: {
          contributors: [ contributor_id ],
          routing: "Direct Assignment",
        }
      }
    }

    context "without existing assigned to" do
      let(:ticket_params) {
        {
          'Subject' => "[AAAA] Test Subject",
          'Created By' => company_user.contributor_id,
          'Status' => 'Open',
          'Priority' => "low",
          company: company,
        }
      }

      it "executes the task action" do
        subject
        perform_actions
        form_value = help_ticket.custom_form_values.find_by(custom_form_field: action_field)
        expect(form_value&.value_int).to eq(contributor_id)
      end

      it "create a ticket activity" do
        subject
        perform_actions
        activity = help_ticket.help_ticket_activities.first
        expect(activity).to be_present
      end
    end
  end

  context "with assigned to" do
    let(:action_field) { custom_form.custom_form_fields.find_by(name: 'assigned_to') }
    let(:contributor_id) { company.company_users.first.contributor_id }
    let(:action_value) {
      {
        custom_forms: [
          {
            id: custom_form.id,
            name: custom_form.form_name,
          }
        ],
        form_field: {
          name: action_field.name,
          field_attribute_type: action_field.field_attribute_type,
        },
        form_value: {
          contributors: [ contributor_id ],
          routing: "Round Robin",
        }
      }
    }

    context "without existing assigned to" do
      let(:ticket_params) {
        {
          'Subject' => "[AAAA] Test Subject",
          'Created By' => company_user.contributor_id,
          'Status' => 'Open',
          'Priority' => "low",
          company: company,
        }
      }

      it "executes the task action" do
        subject
        perform_actions
        form_value = CustomFormValue.find_by(module: help_ticket, custom_form_field: action_field)

        expect(form_value&.value_int).to eq(contributor_id)
      end

      it "create a ticket activity" do
        subject
        perform_actions
        activity = help_ticket.help_ticket_activities.first
        expect(activity).to be_present
      end
    end

    context "with existing assignments" do
      let(:ticket_params) {
        {
          'Subject' => "[AAAA] Test Subject",
          'Created By' => company_user.contributor_id,
          'Assigned To' => [contributor_id],
          'Status' => 'Open',
          'Priority' => "low",
          company: company,
        }
      }

      it "executes the task action" do
        subject
        form_value = help_ticket.custom_form_values.find_by(custom_form_field: action_field)
        expect(form_value&.value_int).to eq(contributor_id)
        form_values = help_ticket.custom_form_values.where(custom_form_field: action_field)
        expect(form_values.count).to eq(1)
      end

      it "create a ticket activity" do
        subject
        perform_actions
        activity = help_ticket.help_ticket_activities.where.not(activity_type: 'source').first
        expect(activity.activity_type).to eq("notification")
      end
    end
  end

  context "with a non-matching task" do
    let(:action_field) { custom_form.custom_form_fields.find_by(name: 'priority') }
    let(:action_value) {
      {
        custom_forms: [
          {
            id: custom_form.id,
            name: custom_form.form_name,
          }
        ],
        form_field: {
          name: action_field.name,
          field_attribute_type: action_field.field_attribute_type,
        },
        form_value: "medium",
      }
    }

    let(:ticket_params) {
      {
        'Subject' => "Test Subject",
        'Created By' => company_user.contributor_id,
        company: company,
      }
    }

    it "executes the task action" do
      subject
      form_value = help_ticket.custom_form_values.find_by(custom_form_field: action_field)
      expect(form_value&.value_str).to be_blank
    end

    it "create a ticket activity" do
      subject
      perform_actions
      activity = help_ticket.help_ticket_activities.where.not(activity_type: 'source').first
      expect(activity.activity_type).to eq("notification")
    end
  end

  context "with a faulty task before a good task" do
    let(:action_field) { custom_form.custom_form_fields.find_by(name: 'priority') }
    let(:ticket_subject) { "[AAAA] Test Subject"}
    let(:bad_subject) do
      AutomatedTasks::ObjectSubjectType.find_or_create_by(name: "test subject ",
                                                          key: 'ticket',
                                                          subject_class: 'BadSubject',
                                                          icon: 'genuicon-envelope-o')
    end
    let(:action_value) {
      {
        custom_forms: [
          {
            id: custom_form.id,
            name: custom_form.form_name,
          }
        ],
        form_field: {
          name: action_field.name,
          field_attribute_type: action_field.field_attribute_type,
        },
        form_value: "medium",
      }
    }

    let!(:automated_task2) do
      task = AutomatedTasks::AutomatedTask.new(workspace: company.default_workspace, order: 2)
      task_event = AutomatedTasks::TaskEvent.new
      event_type = AutomatedTasks::EventType.find_by(name: "{a ticket} is created")
      task_event.event_type = event_type
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = bad_subject
      object.value = {
        custom_form: {
          id: custom_form.id,
          form_name: "Base Ticket"
        },
        form_field: {
          id: form_field.id,
          label: "Subject",
          field_attribute_type: "text"
        },
        text: "AAAA"
      }.to_json
      task_event.event_details << object
      task.task_events << task_event

      task_action = AutomatedTasks::TaskAction.new
      task_action.action_type = AutomatedTasks::ActionType.find_by(name: 'set a [form field]',
                                                                   module: 'help_tickets',
                                                                   model: 'HelpTicket')
      task_action.value = action_value.to_json
      task.task_actions << task_action
      task.save!
      task
    end

    let(:ticket_params) {
      {
        'Subject' => "[AAAA] Test Subject",
        'Created By' => company_user.contributor_id,
        company: company,
      }
    }

    it "executes the task action" do
      subject
      perform_actions
      form_value = help_ticket.custom_form_values.find_by(custom_form_field: action_field)
      expect(form_value).to be_present
      expect(form_value.value_str).to eq('medium')
    end

    it "create a ticket activity" do
      subject
      perform_actions
      activity = help_ticket.help_ticket_activities.first
      expect(activity).to be_present
    end
  end

  context "when notifications are mute" do
    let(:action_field) { custom_form.custom_form_fields.find_by(name: 'priority') }
    let(:action_value) {
      {
        custom_forms: [
          {
            id: custom_form.id,
            name: custom_form.form_name,
          }
        ],
        form_field: {
          name: action_field.name,
          field_attribute_type: action_field.field_attribute_type,
        },
        form_value: "medium",
      }
    }

    let(:ticket_params) {
      {
        'Subject' => "Test Subject",
        'Created By' => company_user.contributor_id,
        company: company,
      }
    }

    it "should not send email when notifications are mute" do
      help_ticket.mute_notification = true
      help_ticket.save
      subject
      expect(ApplicationMailer).not_to receive(:mail)
    end

    it "should not send email when notification is mute for a comment" do
      help_ticket_comment.mute_notification = true
      help_ticket_comment.save
      subject
      expect(ApplicationMailer).not_to receive(:mail)
    end
  end

  def perform_actions
    job_args.each do |args|
      AutomatedTasks::TaskActionsWorker.new.perform(*args)
    end
  end
end

module AutomatedTasks
  module Subjects
    class BadSubject
      include AutomatedTasks::Concerns::Eventing

      def match?(object)
        raise "Boom"
      end
    end
  end
end