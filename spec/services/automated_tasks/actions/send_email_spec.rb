# frozen_string_literal: true

require 'rails_helper'

describe AutomatedTasks::Actions::SendEmail do
  let(:user_att) do
    {
      email: "<EMAIL>",
      password: "password2",
      first_name: "<PERSON>",
      last_name: "<PERSON><PERSON>"
    }
  end
  let!(:user_1) { create(:user, user_att) }
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:company_user2) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:out_of_office_user) { FactoryBot.create(:company_user, user_id: user_1.id, out_of_office: true) }
  let(:agent) {
    agents_group = company.groups.find_by(name: 'Help Desk Agents')
    agent = FactoryBot.create(:company_user, company: company, granted_access_at: 1.day.ago)
    agents_group.group_members.create(contributor: agent.contributor)
    agent
  }
  let(:workspace) { company.default_workspace }
  let(:joe_user) {
    my_user = FactoryBot.create(:company_user, company: company, granted_access_at: 1.day.ago)
    my_user
  }
  let(:assigned_to) { custom_form.custom_form_fields.find_by(name: 'assigned_to') }
  let(:followers) { custom_form.custom_form_fields.find_by(name: 'followers') }
  let(:created_by) { custom_form.custom_form_fields.find_by(name: 'created_by') }
  let(:ticket_subject) { custom_form.custom_form_fields.find_by(name: 'subject') }
  let(:company) { company_user.company }
  let(:custom_form) {
    f = company.custom_forms.helpdesk.first
    f.custom_form_fields.create!(name: 'followers_1', label: 'Followers', field_attribute_type: 'people_list')
    f
  }
  let(:user) { company_user.user }
  let(:help_ticket) {
    ticket = create(:help_ticket, company: company, custom_form: custom_form, workspace: workspace)
    ticket.custom_form_values.create(custom_form_field: assigned_to, value_int: agent.contributor_id)
    ticket.custom_form_values.create(custom_form_field: created_by, value_int: company_user.contributor_id)
    ticket.custom_form_values.create(custom_form_field: followers, value_int: joe_user.contributor_id)
    ticket.custom_form_values.create(custom_form_field: ticket_subject, value_str: 'Test Email')
    ticket
  }
  let(:automated_task) { AutomatedTasks::AutomatedTask.create(workspace: workspace) }
  let(:private_comment) { HelpTicketComment.create(help_ticket: help_ticket, contributor: company_user.contributor, comment_body: "original_comment", private_contributor_ids: [company_user.contributor_id, company_user2.contributor_id], private_flag: true) }
  let(:public_comment) { HelpTicketComment.create(help_ticket: help_ticket, contributor: company_user.contributor, comment_body: "original_comment", private_flag: false) }
  subject { described_class.new(task_action, help_ticket) }

  context "assigned" do
    let!(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: "send an [email]")
      value_data = {
        target: 'assigned',
        subject: 'Test Email',
        body: '<div>This is an email.</div>',
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      value: value_data.to_json,
                      automated_task: automated_task
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'sends the email' do
        mailer = TaskActionMailer.action_email(task_action, help_ticket, [agent.email])
        data = JSON.parse(task_action.value)
        email_data = AutomatedTasks::Actions::EmailActionService.new(help_ticket, task_action, [agent.email]).email_data(data, agent.email)
        expect(AutomatedTasks::TaskActionMailerWorker).to receive(:perform_async)
      .with(task_action.id, help_ticket.id, help_ticket.class.name, [agent.user.email], email_data).and_return(mailer)
        subject.call
      end
    end
  end

  context "agents" do
    let!(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: "send an [email]")
      value_data = {
        target: 'agents',
        subject: 'Test Email',
        body: '<div>This is an email.</div>',
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      value: value_data.to_json,
                      automated_task: automated_task
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'sends the email' do
        agent
        ticket = help_ticket.reload
        data = JSON.parse(task_action.value)
        email_data = AutomatedTasks::Actions::EmailActionService.new(help_ticket, task_action, [agent.user.email]).email_data(data, agent.user.email)
        mailer = TaskActionMailer.action_email(task_action, ticket, [agent.user.email])
        expect(AutomatedTasks::TaskActionMailerWorker).to receive(:perform_async)
      .with(task_action.id, ticket.id, ticket.class.name, [agent.user.email], email_data).and_return(mailer)
        subject.call
      end
    end
  end

  context "mentioned" do
    let(:comment_body) { '<div><!--block-->The laptop is not working <a href="http://' + company.subdomain + '
                    .com/company/users/' + company_user.id.to_s + '">John Doe</a></div>' }
    let(:comment) { HelpTicketComment.create(help_ticket: help_ticket, contributor: company_user.contributor, comment_body: comment_body, private_flag: false) }
    let!(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: "send an [email]")
      value_data = {
        target: 'mentioned',
        subject: 'Comment Added Email',
        body: '<div>Comment is added to the ticket.</div>',
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      value: value_data.to_json,
                      automated_task: automated_task
                    )
      task_action.save!
      task_action
    end

    it 'sends email to users mentioned in the comment' do
      send_email = AutomatedTasks::Actions::SendEmail.new(task_action, comment)
      recipients = send_email.recipients
      expect(recipients).to be_present
      data = JSON.parse(task_action.value)
      email_data = AutomatedTasks::Actions::EmailActionService.new(comment, task_action, [recipients.first]).email_data(data, recipients.first)
      mailer = TaskActionMailer.action_email(task_action, comment, [recipients.first], email_data)
      expect(AutomatedTasks::TaskActionMailerWorker).to receive(:perform_async)
      .with(task_action.id, comment.id, comment.class.name, [recipients.first], email_data).and_return(mailer)
      send_email.call
    end
  end

  context "related" do
    let!(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: "send an [email]")
      value_data = {
        target: 'related',
        subject: 'Test Email',
        body: '<div>This is an email.</div>',
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      value: value_data.to_json,
                      automated_task: automated_task
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      let(:guest_user) { create(:guest, company: company, workspace: workspace, email: "<EMAIL>") }
      it 'sends the email' do
        agent
        help_ticket.custom_form_values.create(custom_form_field: created_by, value_int: guest_user.contributor_id)
        ticket = help_ticket.reload
        mailer = TaskActionMailer.action_email(task_action, ticket, [agent.user.email])
        allow_any_instance_of(AutomatedTasks::TaskActionMailerWorker).to receive(:perform).and_return(mailer)
        expect(subject.recipients).to include(CompanyUser.find_by(contributor_id: ticket.followers.first).email)
        expect(subject.recipients).to include(CompanyUser.find_by(contributor_id: ticket.assigned_users.first).email)
        expect(subject.recipients).to include(Guest.find_by(contributor_id: guest_user.contributor_id).email)
      end
    end
  end

  context "creator" do
    context "with a user" do
      let!(:task_action) do
        action_type = AutomatedTasks::ActionType.find_by(name: "send an [email]")
        value_data = {
          target: 'ticket_creator',
          subject: 'Test Email',
          body: '<div>This is an email.</div>',
        }
        task_action = AutomatedTasks::TaskAction.new(
                        action_type: action_type,
                        value: value_data.to_json,
                        automated_task: automated_task
                      )
        task_action.save!
        task_action
      end

      context "#call" do
        it 'sends the email' do
          data = JSON.parse(task_action.value)
          email_data = AutomatedTasks::Actions::EmailActionService.new(help_ticket, task_action, [company_user.user.email]).email_data(data, company_user.user.email)
          mailer = TaskActionMailer.action_email(task_action, help_ticket, [company_user.user.email], email_data)
          expect(AutomatedTasks::TaskActionMailerWorker).to receive(:perform_async)
      .with(task_action.id, help_ticket.id, help_ticket.class.name, [company_user.user.email], email_data).and_return(mailer)
          subject.call
        end
      end

      context "send_comment_email" do
        let(:private_comment) { HelpTicketComment.create(help_ticket: help_ticket, contributor: company_user.contributor, comment_body: "original_comment", private_contributor_ids: [company_user2.contributor_id], private_flag: true) }

        it 'does not send email to non-private comment users' do
          send_email = AutomatedTasks::Actions::SendEmail.new(task_action, private_comment)
          expect(send_email.recipients.count).to eq(0)
        end

        it 'sends email for public comments' do
          send_email = AutomatedTasks::Actions::SendEmail.new(task_action, public_comment)
          recipients = send_email.recipients
          expect(recipients).to be_present
          mailer = TaskActionMailer.action_email(task_action, public_comment, [recipients.first])
          data = JSON.parse(task_action.value)
          email_data = AutomatedTasks::Actions::EmailActionService.new(public_comment, task_action, [recipients.first]).email_data(data, recipients.first)
          expect(AutomatedTasks::TaskActionMailerWorker).to receive(:perform_async)
      .with(task_action.id, public_comment.id, public_comment.class.name, [recipients.first], email_data).and_return(mailer)
          send_email.call
        end

        context "with a user that is listed in the private comment" do
          let(:private_comment) { HelpTicketComment.create(help_ticket: help_ticket, contributor: company_user.contributor, comment_body: "original_comment", private_contributor_ids: [company_user.contributor_id, company_user2.contributor_id], private_flag: true) }

          it 'does send email to private comment users' do
            send_email = AutomatedTasks::Actions::SendEmail.new(task_action, private_comment)
            expect(send_email.recipients).to eq([company_user.email])
          end
        end
      end
    end
  end

  context "creator_email" do
    context "with a guest user" do
      let(:guest_user) { create(:guest, company: company, workspace: workspace, email: "<EMAIL>") }
      let(:help_ticket) do
        ticket = create(:help_ticket, company: company, workspace: workspace)
        ticket.custom_form_values.create(custom_form_field: created_by, value_int: guest_user.contributor_id)
        ticket.custom_form_values.create(custom_form_field: ticket_subject, value_str: 'Test Email')
        ticket
      end

      let!(:task_action) do
        action_type = AutomatedTasks::ActionType.find_by(name: "send an [email]")
        value_data = {
          target: 'ticket_creator',
          subject: 'Test Email',
          body: '<div>This is an email.</div>',
        }
        task_action = AutomatedTasks::TaskAction.new(
                        action_type: action_type,
                        value: value_data.to_json,
                        automated_task: automated_task
                      )
        task_action.save!
        task_action
      end

      context "#call" do
        it 'sends the email' do
          mailer = TaskActionMailer.action_email(task_action, help_ticket, ['<EMAIL>'])
          data = JSON.parse(task_action.value)
          email_data = AutomatedTasks::Actions::EmailActionService.new(help_ticket, task_action, ['<EMAIL>']).email_data(data, '<EMAIL>')
          expect(AutomatedTasks::TaskActionMailerWorker).to receive(:perform_async)
      .with(task_action.id, help_ticket.id, help_ticket.class.name, ['<EMAIL>'], email_data).and_return(mailer)
          subject.call
        end
      end
      context "send_comment_email" do
        it 'does not send email for private comments' do
          send_email = AutomatedTasks::Actions::SendEmail.new(task_action, private_comment)
          expect(send_email.recipients.count).to eq(0)
        end

        it 'sends email for public comments' do
          send_email = AutomatedTasks::Actions::SendEmail.new(task_action, public_comment)
          recipients = send_email.recipients
          data = JSON.parse(task_action.value)
          email_data = AutomatedTasks::Actions::EmailActionService.new(public_comment, task_action, [recipients.first]).email_data(data, recipients.first)
          expect(recipients).to be_present
          mailer = TaskActionMailer.action_email(task_action, public_comment, [recipients.first])
          expect(AutomatedTasks::TaskActionMailerWorker).to receive(:perform_async)
      .with(task_action.id, public_comment.id, public_comment.class.name, [recipients.first], email_data).and_return(mailer)
          send_email.call
        end
      end
    end
  end

  context "specified" do
    let(:help_ticket) do
      ticket = create(:help_ticket, company: company, priority: 'low', workspace: workspace)
      ticket.custom_form_values.create(custom_form_field: ticket_subject, value_str: 'Test Email')
      ticket
    end
    let!(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: "send an [email]")
      value_data = {
        target: 'specified',
        recipients: '<EMAIL>',
        subject: 'Test Email',
        body: '<div>This is an email.</div>',
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      value: value_data.to_json,
                      automated_task: automated_task
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'sends the email' do
        Rails.cache.clear # To avoid flaky test cases
        mailer = TaskActionMailer.action_email(task_action, help_ticket, ['<EMAIL>'])
        data = JSON.parse(task_action.value)
        email_data = AutomatedTasks::Actions::EmailActionService.new(help_ticket, task_action, ['<EMAIL>']).email_data(data, '<EMAIL>')
        expect(AutomatedTasks::TaskActionMailerWorker).to receive(:perform_async)
      .with(task_action.id, help_ticket.id, help_ticket.class.name, ['<EMAIL>'], email_data).and_return(mailer)
        subject.call
      end
    end
  end

  context "multiple target types" do
    let(:help_ticket) {
      ticket = create(:help_ticket, company: company, custom_form: custom_form, workspace: workspace)
      ticket.custom_form_values.create(custom_form_field: assigned_to, value_int: agent.contributor_id)
      ticket.custom_form_values.create(custom_form_field: created_by, value_int: agent.contributor_id)
      ticket.custom_form_values.create(custom_form_field: ticket_subject, value_str: 'Test Email')
      ticket
    }
    let!(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: "send an [email]")
      value_data = {
        target: 'assigned,ticket_creator',
        subject: 'Test Email',
        body: '<div>This is an email.</div>',
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      value: value_data.to_json,
                      automated_task: automated_task
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'sends the email' do
        mailer = TaskActionMailer.action_email(task_action, help_ticket, [agent.user.email])
        data = JSON.parse(task_action.value)
        email_data = AutomatedTasks::Actions::EmailActionService.new(help_ticket, task_action, [agent.user.email]).email_data(data, agent.user.email)
        expect(AutomatedTasks::TaskActionMailerWorker).to receive(:perform_async)
      .with(task_action.id, help_ticket.id, help_ticket.class.name, [agent.user.email], email_data).and_return(mailer)
        subject.call
      end
    end
  end

  context "private" do
    let!(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: "send an [email]")
      value_data = {
        target: 'private',
        subject: 'Test Email',
        body: '<div>This is an email.</div>',
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      value: value_data.to_json,
                      automated_task: automated_task
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'sends the email' do
        send_email = AutomatedTasks::Actions::SendEmail.new(task_action, private_comment)
        mailer1 = TaskActionMailer.action_email(task_action, private_comment, [company_user.email, company_user2.email])
        data = JSON.parse(task_action.value)
        email_data = AutomatedTasks::Actions::EmailActionService.new(help_ticket, task_action, [company_user.email, company_user2.email]).email_data(data, nil)
        expect(AutomatedTasks::TaskActionMailerWorker).to receive(:perform_async)
      .with(task_action.id, private_comment.id, private_comment.class.name, [company_user.email, company_user2.email], email_data).and_return(mailer1)
        send_email.call
      end
    end
  end

  context "helpdesk custom email" do
    let!(:helpdesk_email) { HelpdeskCustomEmail.create(email: "<EMAIL>", company: company) }
    let(:help_ticket) do
      ticket = create(:help_ticket, company: company, priority: 'low', workspace: workspace)
      ticket.custom_form_values.create(custom_form_field: ticket_subject, value_str: 'Test Email')
      ticket
    end
    let!(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: "send an [email]")
      value_data = {
        target: 'specified',
        recipients: '<EMAIL>',
        subject: 'Test Email',
        body: '<div>This is an email.</div>',
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      value: value_data.to_json,
                      automated_task: automated_task
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'creates log for helpdesk custom email' do
        Rails.cache.clear # To avoid flaky test cases
        data = JSON.parse(task_action.value)
        email_data = AutomatedTasks::Actions::EmailActionService.new(help_ticket, task_action, ['<EMAIL>']).email_data(data, '<EMAIL>')
        mailer = TaskActionMailer.action_email(task_action, help_ticket, ['<EMAIL>'], email_data)
        mailer.deliver_now!
        expect(AutomatedTasks::TaskActionMailerWorker).to receive(:perform_async)
      .with(task_action.id, help_ticket.id, help_ticket.class.name, ['<EMAIL>'], email_data).and_return(mailer)
        subject.call
        email_log_type = Logs::EmailLog.where(receiver_emails: ["<EMAIL>"]).first.email_type
        expect(email_log_type).to eq("custom_email")
      end
    end
  end

  context "out of office user" do
    let(:help_ticket) do
      ticket = create(:help_ticket, company: company, workspace: workspace)
      ticket.custom_form_values.create(custom_form_field: assigned_to, value_int: agent.contributor_id)
      ticket.custom_form_values.create(custom_form_field: assigned_to, value_int: out_of_office_user.contributor_id)
      ticket
    end
    let!(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: "send an [email]")
      value_data = {
        target: 'assigned',
        subject: 'Test Email',
        body: '<div>This is an email.</div>',
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      value: value_data.to_json,
                      automated_task: automated_task
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'does not send an email to the out of office user' do
        send_email = AutomatedTasks::Actions::SendEmail.new(task_action, help_ticket)
        recipients = send_email.recipients
        expect(recipients.count).to eq(1)
        expect(recipients).not_to include(user_1.email)
      end
    end
  end

  context 'self action notification' do
    let(:help_ticket) do
      ticket = create(:help_ticket, company: company, workspace: workspace)
      ticket.custom_form_values.create(custom_form_field: assigned_to, value_int: agent.contributor_id)
      ticket.custom_form_values.create(custom_form_field: created_by, value_int: company_user.contributor_id)
      ticket
    end

    let!(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: "send an [email]")
      value_data = {
        target: 'assigned,ticket_creator',
        subject: 'Test Email',
        body: '<div>This is an email.</div>',
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      value: value_data.to_json,
                      automated_task: automated_task
                    )
      task_action.save!
      task_action
    end

    it 'will not send email to user who performed the action if settings are disabled' do
      HelpdeskSetting.joins(:default_helpdesk_setting)
                     .find_by(default_helpdesk_setting: { setting_type: 'allow_self_action_notification' }, company: company, workspace: workspace)
                     .update(enabled: false)

      company.reload
      send_email = AutomatedTasks::Actions::SendEmail.new(task_action, help_ticket)
      recipients = send_email.recipients
      expect(recipients).not_to include(company_user.email)
      expect(recipients).to include(agent.email)
    end

    it 'will send email to user who performed the action if settings are enabled' do
      HelpdeskSetting.joins(:default_helpdesk_setting)
                     .find_by(default_helpdesk_setting: { setting_type: 'allow_self_action_notification' }, company: company, workspace: workspace)
                     .update(enabled: true, selected_option: "admins_agents, other_users")
      send_email = AutomatedTasks::Actions::SendEmail.new(task_action, help_ticket)
      recipients = send_email.recipients
      expect(recipients).to include(company_user.email)
      expect(recipients).to include(agent.email)
    end
  end

  context 'Followers email notification' do
    let(:help_ticket) do
      ticket = create(:help_ticket, company: company, workspace: workspace)
      ticket.custom_form_values.create(custom_form_field: followers, value_int: company_user.contributor_id)
      ticket.custom_form_values.create(custom_form_field: created_by, value_int: agent.contributor_id)
      ticket
    end

    let!(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: "send an [email]")
      value_data = {
        target: 'impacted',
        subject: 'Test Email',
        body: '<div>This is an email.</div>',
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      value: value_data.to_json,
                      automated_task: automated_task
                    )
      task_action.save!
      task_action
    end

    it 'will not send email to user who performed the action if settings are disabled' do
      company.reload
      send_email = AutomatedTasks::Actions::SendEmail.new(task_action, help_ticket)
      recipients = send_email.recipients
      expect(recipients).to include(company_user.email)
      expect(recipients).not_to include(agent.email)
    end
  end
end
