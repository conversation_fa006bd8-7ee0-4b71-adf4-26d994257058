# frozen_string_literal: true

require 'rails_helper'
include HelpT<PERSON>etHelper

describe AutomatedTasks::Actions::AddAlert do
  let(:workspace) { company.default_workspace }
  let(:automated_task) { AutomatedTasks::AutomatedTask.create(workspace: workspace) }
  let(:action_type) { AutomatedTasks::ActionType.find_by(name: "create an [alert]") }
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let!(:agent) {
    cu = FactoryBot.create(:company_user, company: company, granted_access_at: 1.day.ago)
    agent_group = company.groups.find_by(name: 'Help Desk Agents')
    agent_group.group_members.create(contributor: cu.contributor)
    cu
  }
  let!(:admin) {
    cu = FactoryBot.create(:company_user, company: company, granted_access_at: 1.day.ago)
    admins_group = company.groups.find_by(name: 'Admins')
    admins_group.group_members.create(contributor: cu.contributor)
    cu
  }
  let(:company) { company_user.company }
  let(:user) { company_user.user }
  let(:message) { "Alert {ticket number}" }
  subject { described_class.new(task_action, help_ticket).call }
  let(:help_ticket) {
    ticket_params = {
      'Subject' => "Test Ticekt",
      'Created By' => company_user.contributor_id,
      'Assigned To' => company_user.contributor_id,
      'Priority' => "low",
      'ticket_number' => 122,
      'message_id' => "8l60nj148tok84jf659cnj0e86gea2jg9jdkgtg1",
      company: company
    }
    create_ticket(ticket_params)
  }

  describe "assigned" do
    let!(:task_action) do
      value_data = {
        target: 'assigned',
        message: message,
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      automated_task: automated_task,
                      value: value_data.to_json
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'creates an alert' do
        subject
        alert = ActionableAlert.last
        expect(alert.message).to eq("Alert #{help_ticket.ticket_number}")
        expect(alert.contributor_actionable_alerts.count).to eq(1)
        alert_link = alert.contributor_actionable_alerts.first
        expect(alert_link.dismissed_at).to be_blank
      end
    end
  end

  context "agents" do
    let!(:task_action) do
      value_data = {
        target: 'agents',
        message: message,
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      automated_task: automated_task,
                      value: value_data.to_json
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'creates an alert' do
        subject
        alert = ActionableAlert.last
        expect(alert.message).to eq("Alert #{help_ticket.ticket_number}")
        expect(alert.contributor_actionable_alerts.count).to eq(1)
        alert_link = alert.contributor_actionable_alerts.first
        expect(alert_link.dismissed_at).to be_blank
        expect(alert_link.contributor_id).to eq(agent.contributor_id)
      end
    end
  end

  context "creator" do
    context "with a user" do
      let(:help_ticket) {
        ticket_params = {
          'Subject' => "First one",
          'Created By' => company_user.contributor_id,
          'Assigned To' => [company_user.contributor_id],
          'Followers' => [company_user.contributor_id],
          'Impacted Devices' => [],
          'Status' => 'Open',
          'Priority' => "medium",
          'Description' => "The red robin sits in the tree.",
          company: company
        }
        create_ticket(ticket_params)
      }
      let!(:task_action) do
        value_data = {
          target: 'creator',
          message: message,
        }
        task_action = AutomatedTasks::TaskAction.new(
                        action_type: action_type,
                        automated_task: automated_task,
                        value: value_data.to_json
                      )
        task_action.save!
        task_action
      end

      context "#call" do
        it 'creates an alert' do
          subject
          alert = ActionableAlert.last
          expect(alert.message).to eq("Alert #{help_ticket.ticket_number}")
          expect(alert.contributor_actionable_alerts.count).to eq(1)
          alert_link = alert.contributor_actionable_alerts.first
          expect(alert_link.dismissed_at).to be_blank
          expect(alert_link.contributor_id).to eq(help_ticket.creators[0].contributor_id)
        end
      end
    end

    context "from an email" do
      let(:help_ticket) {
        ticket_params = {
          'Subject' => "Subject",
          'Created By' => "<EMAIL>",
          'Assigned To' => [company_user.contributor_id],
          'Followers' => [company_user.contributor_id],
          'Impacted Devices' => [],
          'Status' => 'Open',
          'Priority' => "low",
          'Description' => "The red robin sits in the tree.",
          company: company
        }
        create_ticket(ticket_params)
      }
      let!(:task_action) do
        value_data = {
          target: 'creator',
          message: message,
        }
        task_action = AutomatedTasks::TaskAction.new(
                        action_type: action_type,
                        automated_task: automated_task,
                        value: value_data.to_json
                      )
        task_action.save!
        task_action
      end

      context "#call" do
        it 'does not create an alert' do
          ActionableAlert.destroy_all
          subject
          alert = ActionableAlert.last
          expect(alert).to be_blank
        end
      end
    end
  end

  context "specified" do
    let!(:task_action) do
      value_data = {
        target: 'specified',
        contributors: [{ id: company_user.contributor_id }],
        message: message,
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      automated_task: automated_task,
                      value: value_data.to_json
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'creates an alert' do
        subject
        alert = ActionableAlert.last
        expect(alert.message).to eq("Alert #{help_ticket.ticket_number}")
        expect(alert.contributor_actionable_alerts.count).to eq(1)
        alert_link = alert.contributor_actionable_alerts.first
        expect(alert_link.dismissed_at).to be_blank
        expect(alert_link.contributor_id).to eq(company_user.contributor_id)
      end
    end
  end

  context "admins" do
    let!(:task_action) do
      value_data = {
        target: 'admins',
        message: message,
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      automated_task: automated_task,
                      value: value_data.to_json
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'creates an alert' do
        subject
        alert = ActionableAlert.last
        expect(alert.message).to eq("Alert #{help_ticket.ticket_number}")
        expect(alert.contributor_actionable_alerts.count).to eq(1)
        alert_link = alert.contributor_actionable_alerts.first
        expect(alert_link.dismissed_at).to be_blank
        expect(alert_link.contributor_id).to eq(admin.contributor_id)
      end
    end
  end
end
