# frozen_string_literal: true

require 'rails_helper'
include <PERSON><PERSON>icketHelper

describe AutomatedTasks::Actions::AssignAgent do
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:company) { company_user.company }
  let(:user) { company_user.user }
  let(:subject_field) { custom_form.custom_form_fields.find_by(name: "subject") }
  let(:custom_form) { company.default_workspace.custom_forms.first }
  let(:automated_task) { AutomatedTasks::AutomatedTask.create(workspace: company.default_workspace) }
  let!(:assigned_to_field) { custom_form.custom_form_fields.find_by(name: "assigned_to") }
  let(:status) { custom_form.custom_form_fields.find_by(name: "status")}
  let(:contributor_id) { company_user.contributor_id }
  let(:workspace) { company.default_workspace }
  let(:help_ticket) {
    ticket_params = {
      'Subject' => "Test Subject 1",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      company: company,
      workspace: workspace
    }
    create_ticket(ticket_params)
  }
  let(:help_ticket2) {
    ticket_params = {
      'Subject' => "Test Subject 2",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      company: company,
      workspace: workspace
    }
    create_ticket(ticket_params)
  }
  let(:comment) { HelpTicketComment.create(help_ticket: help_ticket, comment_body: comment_body) }
  let(:comment_body) do
    """
      <div>this is a
        <a href=\"https://#{company.subdomain}.gogenuity-staging.com/company/users/#{company_user.id}\">BigDog User</a>
        <a href=\"https://bengal-technologies.gogenuity-staging.com/vendors/408\">Codeship</a>
        <a href=\"https://bengal-technologies.gogenuity-staging.com/company/groups/#{group.id}/edit\">#{group.name}</a>
        <a href=\"https://bengal-technologies.gogenuity-staging.com/managed_assets/123946\">Darlene's iPhone</a>
        <a href=\"https://bengal-technologies.gogenuity-staging.com/help_tickets/2893\">Email for help desk is slow</a>
      </div>
    """
  end
  let(:company_user2) do
    create(:company_user, company: company, granted_access_at: 1.day.ago)
  end
  let(:group) { create(:group, company: company) }
  let!(:member) { create(:group_member, group: group, contributor: company_user2.contributor) }

  subject { described_class.new(task_action, help_ticket) }

  context "with direct assignment of a single user" do
    let!(:task_action) do
      # This might seem weird, but with custom forms, the assignment is done through setting a form field
      action_type = AutomatedTasks::ActionType.find_by(name: 'set a [form field]',
                                                       module: 'help_tickets',
                                                       model: 'HelpTicket',
                                                       action_class: 'SetFormField')
      value_data = {
        custom_forms: [
          {
            id: help_ticket.custom_form.id,
            name: help_ticket.custom_form.form_name,
          }
        ],
        form_field: {
          name: assigned_to_field.name,
          field_attribute_type: assigned_to_field.field_attribute_type,
        },
        form_value: {
          contributors: [ contributor_id ],
          routing: "Direct Assignment",
        }
      }
      AutomatedTasks::TaskAction.create(action_type: action_type, value: value_data.to_json, automated_task_id: automated_task.id)
    end

    it "assigns the ticket" do
      assigned_values = help_ticket.custom_form_values.includes(:custom_form_field).where(custom_form_fields: { name: "assigned_to" })
      assigned_values.destroy_all
      subject.call
      ticket = help_ticket.reload
      assigned_values = ticket.assigned_users
      expect(assigned_values.length).to eq(1)
    end

    it "logs the activity" do
      assigned_values = help_ticket.custom_form_values.includes(:custom_form_field).where(custom_form_fields: { name: "assigned_to" })
      assigned_values.destroy_all
      subject.call
      ticket = help_ticket.reload
      people_list_activity = ticket.help_ticket_activities.where(activity_type: 'people_list')
      expect(people_list_activity.length).to eq(1)
    end
  end

  context "with direct assignment of group" do
    let(:group) { company.groups.create(name: 'Group 1') }
    let(:contributor_id) { group.contributor_id }
    let!(:task_action) do
      # This might seem weird, but with custom forms, the assignment is done through setting a form field
      action_type = AutomatedTasks::ActionType.find_by(name: 'set a [form field]',
                                                       module: 'help_tickets',
                                                       model: 'HelpTicket',
                                                       action_class: 'SetFormField')
      value_data = {
        custom_forms: [
          {
            id: help_ticket.custom_form.id,
            name: help_ticket.custom_form.form_name,
          }
        ],
        form_field: {
          name: assigned_to_field.name,
          field_attribute_type: assigned_to_field.field_attribute_type,
        },
        form_value: {
          contributors: [ contributor_id ],
          routing: "Direct Assignment",
        }
      }
      AutomatedTasks::TaskAction.create(action_type: action_type, value: value_data.to_json, automated_task_id: automated_task.id)
    end

    it "assigns the ticket" do
      assigned_values = help_ticket.custom_form_values.includes(:custom_form_field).where(custom_form_fields: { name: "assigned_to" })
      assigned_values.destroy_all
      subject.call
      ticket = help_ticket.reload
      assigned_values = ticket.assigned_users
      expect(assigned_values.length).to eq(1)
      expect(assigned_values.first).to eq(group.contributor_id)
    end

    it "logs the activity" do
      assigned_values = help_ticket.custom_form_values.includes(:custom_form_field).where(custom_form_fields: { name: "assigned_to" })
      assigned_values.destroy_all
      subject.call
      ticket = help_ticket.reload
      people_list_activity = ticket.help_ticket_activities.where(activity_type: 'people_list')
      expect(people_list_activity.length).to eq(1)
    end
  end

  context "with round robin assignment" do
    let!(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: 'set a [form field]',
                                                       module: 'help_tickets',
                                                       model: 'HelpTicket',
                                                       action_class: 'SetFormField')
      value_data = {
        custom_forms: [
          {
            id: help_ticket.custom_form.id,
            name: help_ticket.custom_form.form_name,
          }
        ],
        form_field: {
          name: assigned_to_field.name,
          field_attribute_type: assigned_to_field.field_attribute_type,
        },
        form_value: {
          contributors: [ company_user.contributor_id, group.contributor_id ],
          routing: "Round Robin",
        }
      }
      AutomatedTasks::TaskAction.create(action_type: action_type, value: value_data.to_json, automated_task_id: automated_task.id)
    end

    it "assigns the ticket" do
      assigned_values = help_ticket.custom_form_values.includes(:custom_form_field).where(custom_form_fields: { name: "assigned_to" })
      assigned_values.destroy_all
      subject.call
      ticket = help_ticket.reload
      assigned_values = ticket.custom_form_values.includes(:custom_form_field).where(custom_form_fields: { name: "assigned_to" })
      expect(assigned_values.length).to eq(1)
      expect(assigned_values.first.value_int).to eq(company_user.contributor_id)

      assigned_values = help_ticket2.custom_form_values.includes(:custom_form_field).where(custom_form_fields: { name: "assigned_to" })
      assigned_values.destroy_all
      described_class.new(task_action, help_ticket2).call
      ticket = help_ticket2.reload
      assigned_values = ticket.custom_form_values.includes(:custom_form_field).where(custom_form_fields: { name: "assigned_to" })
      expect(assigned_values.length).to eq(1)
      expect(assigned_values.first.value_int).to eq(company_user2.contributor_id)
    end

    it "logs the activity" do
      subject.call
      ticket = help_ticket.reload
      people_list_activity = ticket.help_ticket_activities.where(activity_type: 'people_list')
      expect(people_list_activity.length).to eq(1)
    end
  end

  context "with least used assignment" do
    let(:company_user2) do
      create(:company_user, company: company, granted_access_at: 1.day.ago)
    end
    let(:group) { create(:group, company: company) }
    let!(:member) { create(:group_member, group: group, contributor: company_user2.contributor) }

    let!(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: 'set a [form field]',
                                                       module: 'help_tickets',
                                                       model: 'HelpTicket',
                                                       action_class: 'SetFormField')
      value_data = {
        custom_forms: [
          {
            id: help_ticket.custom_form.id,
            name: help_ticket.custom_form.form_name,
          }
        ],
        form_field: {
          name: assigned_to_field.name,
          field_attribute_type: assigned_to_field.field_attribute_type,
        },
        form_value: {
          contributors: [ company_user.contributor_id, group.contributor_id ],
          routing: "Least Used",
        }
      }
      AutomatedTasks::TaskAction.create(action_type: action_type, value: value_data.to_json, automated_task_id: automated_task.id)
    end

    context "with no assignments" do
      it "assigns the ticket" do
        subject.call
        ticket = help_ticket.reload
        expect(ticket.custom_form_values.includes(:custom_form_field).where(custom_form_fields: { name: "assigned_to" }).length).to eq(1)
        assigned_value = ticket.custom_form_values.includes(:custom_form_field).where(custom_form_fields: { name: "assigned_to" }).first
        expect(assigned_value.value_int).to eq(company_user.contributor_id)
      end

      it "logs the activity" do
        subject.call
        ticket = help_ticket.reload
        people_list_activity = ticket.help_ticket_activities.where(activity_type: 'people_list')
        expect(people_list_activity.length).to eq(1)
      end
    end

    context "with assignments" do
      3.times do |idx|
        let!("ticket_a#{idx}") do
          ticket = create(:help_ticket, company: company, custom_form: custom_form, workspace: workspace)
          ticket.custom_form_values.create!(custom_form_field: subject_field, value_str: "Test Subject")
          ticket.custom_form_values.create!(custom_form_field: assigned_to_field, value_int: company_user.contributor_id)
          ticket.custom_form_values.create!(custom_form_field: status, value_str: 'Open')
          ticket
        end
      end

      2.times do |idx|
        let!("ticket_b#{idx}") do
          ticket = create(:help_ticket, company: company, custom_form: custom_form, workspace: workspace)
          ticket.custom_form_values.create!(custom_form_field: subject_field, value_str: "Test Subject")
          ticket.custom_form_values.create!(custom_form_field: assigned_to_field, value_int: company_user2.contributor_id)
          ticket.custom_form_values.create!(custom_form_field: status, value_str: 'Open')
          ticket
        end
      end

      it "assigns the ticket" do
        subject.call
        ticket = help_ticket.reload
        assigned_values = ticket.custom_form_values.includes(:custom_form_field).where(custom_form_fields: { name: "assigned_to" })
        expect(assigned_values.count).to eq(1)
        expect(assigned_values.first.value_int).to eq(company_user.contributor_id)
      end

      it "logs the activity" do
        subject.call
        ticket = help_ticket.reload
        people_list_activity = ticket.help_ticket_activities.where(activity_type: 'people_list')
        expect(people_list_activity.length).to eq(1)
      end
    end
  end

  context 'with mention in comment' do
    let!(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: 'set a [form field]',
                                                       module: 'help_tickets',
                                                       model: 'HelpTicketComment',
                                                       action_class: 'SetFormField')
      value_data = {
        custom_forms: [{
          id: help_ticket.custom_form.id,
          name: help_ticket.custom_form.form_name,
        }],
        form_field: {
          name: assigned_to_field.name,
          field_attribute_type: assigned_to_field.field_attribute_type,
        },
        form_value: { contributors: [ contributor_id ] },
        mention_type: 'any'
      }
      AutomatedTasks::TaskAction.create(action_type: action_type,
                                        value: value_data.to_json,
                                        automated_task_id: automated_task.id)
    end

    it 'assigns every mentioned user or group' do
      assigned_values = help_ticket.custom_form_values
                                   .includes(:custom_form_field)
                                   .where(custom_form_fields: { name: 'assigned_to' })
      assigned_values.destroy_all
      described_class.new(task_action, comment).call
      ticket = help_ticket.reload
      assigned_values = ticket.assigned_users
      expect(assigned_values.length).to eq(2)
      [group.contributor_id, company_user.contributor_id].each do |id|
        expect(assigned_values.find { |val| val == id }).to be_present
      end
    end
  end
end

