require 'rails_helper'
include <PERSON><PERSON><PERSON>etHelper

describe AutomatedTasks::Actions::SetFormFieldArray do
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:company) { company_user.company }
  let(:custom_form) { company.default_workspace.custom_forms.first }
  let(:automated_task) { AutomatedTasks::AutomatedTask.create(workspace: company.default_workspace) }
  let(:followers_field) { custom_form.custom_form_fields.find_by(name: 'followers', label: 'Followers', field_attribute_type: 'people_list') }
  let(:comment) { HelpTicketComment.create(help_ticket: help_ticket, comment_body: comment_body) }
  let(:help_ticket) do
    ticket_params = {
      'Subject' => 'Test Subject',
      'Created By' => company_user.contributor_id,
      company: company,
    }
    create_ticket(ticket_params)
  end
  let(:comment_body) do
    """
      <div>this is a
        <a href=\"https://#{company.subdomain}.gogenuity-staging.com/company/users/#{company_user.id}\">BigDog User</a>
        <a href=\"https://bengal-technologies.gogenuity-staging.com/vendors/408\">Codeship</a>
        <a href=\"https://bengal-technologies.gogenuity-staging.com/company/groups/#{group.id}/edit\">#{group.name}</a>
        <a href=\"https://bengal-technologies.gogenuity-staging.com/managed_assets/123946\">Darlene's iPhone</a>
        <a href=\"https://bengal-technologies.gogenuity-staging.com/help_tickets/2893\">Email for help desk is slow</a>
      </div>
    """
  end
  let(:group) { create(:group, company: company) }

  subject { described_class.new(task_action, comment) }

  context "with users or groups mentioned in comment" do
    let(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: 'set a [form field]',
                                                       module: 'help_tickets',
                                                       model: 'HelpTicketComment',
                                                       action_class: 'SetFormField')
      value_data = {
        custom_forms: [
          {
            id: help_ticket.custom_form.id,
            name: help_ticket.custom_form.form_name,
            is_active: help_ticket.custom_form.is_active,
          }
        ],
        form_field: {
          name: followers_field.name,
          field_attribute_type: followers_field.field_attribute_type,
        },
        form_value: [],
        mention_type: 'any'
      }
      task_action = AutomatedTasks::TaskAction.new(action_type: action_type,
                                                   value: value_data.to_json,
                                                   automated_task: automated_task)
      task_action.save!
      task_action
    end

    it 'assigns every mentioned user or group to the field' do
      followers = help_ticket.custom_form_values
                                  .includes(:custom_form_field)
                                  .where(custom_form_fields: { name: followers_field.name })
      followers.destroy_all
      subject.call
      ticket = help_ticket.reload
      followers = help_ticket.followers
      expect(followers.length).to eq(2)
      [group.contributor_id, company_user.contributor_id].each do |id|
        expect(followers.find { |val| val == id }).to be_present
      end
    end
  end
end

