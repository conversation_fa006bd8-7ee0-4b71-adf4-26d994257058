# frozen_string_literal: true

require 'rails_helper'

describe AutomatedTasks::Actions::AddComment do
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:company) { company_user.company }
  let(:user) { company_user.user }
  let!(:task_action) do
    action_type = AutomatedTasks::ActionType.find_by(name: "add a [comment]")
    task_action = AutomatedTasks::TaskAction.new(
                    action_type: action_type,
                    value: "{\"comment\":\"This is a comment.\"}"
                  )
    task_action.save!
    task_action
  end

  subject { described_class.new(task_action, help_ticket).call }

  context "with a normal ticket" do
    let(:help_ticket) { create(:help_ticket, company: company) }

    it "creates a comment" do
      subject
      actual = help_ticket.reload
      expect(actual.help_ticket_comments).to be_present
      expect(actual.help_ticket_comments.size).to eq(1)
    end

    it "creates a help ticket activity" do
      subject
      actual = help_ticket.reload
      activity = actual.help_ticket_activities.order(created_at: :desc).first
      expect(activity).to be_present
      expect(activity.data['previous_body']).to be_nil
      expect(activity.data['new_comment_body']).to eq('This is a comment.')
    end
  end
end
