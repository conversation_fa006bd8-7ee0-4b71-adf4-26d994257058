require 'rails_helper'

include <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

describe AutomatedTasks::Actions::SetFormField do
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:company_user2) { FactoryBot.create(:company_user, company: company, granted_access_at: 1.day.ago) }
  let(:company) { company_user.company }
  let(:subject_field) { custom_form.custom_form_fields.find_by(name: 'subject') }
  let(:assigned_to_field) { custom_form.custom_form_fields.find_by(name: 'assigned_to') }
  let(:followers_field) { custom_form.custom_form_fields.find_by(name: 'followers', label: 'Followers', field_attribute_type: 'people_list') }
  let(:custom_form) { company.default_workspace.custom_forms.helpdesk.first }
  let(:workspace) { company.default_workspace }
  let(:automated_task) { AutomatedTasks::AutomatedTask.create(workspace: workspace)}
  let(:help_ticket) do
    ticket_params = {
      'Subject' => 'Test Subject',
      'Created By' => company_user.contributor_id,
      company: company,
    }
    create_ticket(ticket_params)
  end

  subject { described_class.new(task_action, help_ticket) }

  context "with a scalar value field" do 
    let(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: 'set a [form field]',
                                                       module: 'help_tickets',
                                                       model: 'CustomFormValue',
                                                       action_class: 'SetFormField')
      value_data = {
        custom_forms: [
          {
            id: help_ticket.custom_form.id,
            name: help_ticket.custom_form.form_name,
            is_active: help_ticket.custom_form.is_active,
          }
        ],
        form_field: {
          name: subject_field.name,
          field_attribute_type: subject_field.field_attribute_type,
        },
        form_value: '{ticket_subject} {ticket_number} - {ticket_created_by}',
      }
      task_action = AutomatedTasks::TaskAction.new(action_type: action_type,
                                                   value: value_data.to_json,
                                                   automated_task: automated_task)
      task_action.save!
      task_action
    end

    it 'will interpolate subject field value' do
      expected_value = "#{help_ticket.subject} #{help_ticket.ticket_number} - #{company_user.name}"
      subject.call
      ticket = help_ticket.reload
      ticket_subject_value = help_ticket.custom_form_values.includes(:custom_form_field).where(custom_form_fields: { name: 'subject' }).first
      custom_form = help_ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: 'subject' })
      expect(ticket_subject_value.value_str).to eq(expected_value)
      expect(AutomatedTasks::EventRouting.new).to respond_to(:perform)
    end
  end

  context "with assigned_to" do
    let!(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: 'set a [form field]',
                                                       module: 'help_tickets',
                                                       model: 'CustomFormValue',
                                                       action_class: 'SetFormField')
      value_data = {
        custom_forms: [
          {
            id: help_ticket.custom_form.id,
            name: help_ticket.custom_form.form_name,
            is_active: help_ticket.custom_form.is_active,
          }
        ],
        form_field: {
          name: assigned_to_field.name,
          field_attribute_type: assigned_to_field.field_attribute_type,
        },
        form_value: {
          contributors: [company_user2.contributor_id],
          routing: 'Round Robin'
        },
      }
      task_action = AutomatedTasks::TaskAction.new(action_type: action_type,
                                                   value: value_data.to_json,
                                                   automated_task: automated_task)
      task_action.save!
      task_action
    end
    it 'will set the assign to field' do
      subject.call
      help_ticket.reload
      id = help_ticket.custom_form_values.includes(:custom_form_field).where(custom_form_fields: { name: 'assigned_to' }).map(&:value_int).sort
      custom_form = help_ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: 'assigned_to' })
      expect(id).to eq([company_user2.contributor_id])
      expect(AutomatedTasks::EventRouting.new).to respond_to(:perform)
    end
  end

  context "with an array value field that is not assigned_to" do
    let(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: 'set a [form field]',
                                                       module: 'help_tickets',
                                                       model: 'CustomFormValue',
                                                       action_class: 'SetFormField')
      value_data = {
        custom_forms: [
          {
            id: help_ticket.custom_form.id,
            name: help_ticket.custom_form.form_name,
            is_active: help_ticket.custom_form.is_active,
          }
        ],
        form_field: {
          name: followers_field.name,
          field_attribute_type: followers_field.field_attribute_type,
        },
        form_value: [company_user2.contributor_id, company_user.contributor_id],
      }
      task_action = AutomatedTasks::TaskAction.new(action_type: action_type,
                                                   value: value_data.to_json,
                                                   automated_task: automated_task)
      task_action.save!
      task_action
    end

    it 'will set imapacted users field value' do
      subject.call
      help_ticket.reload
      ids = help_ticket.custom_form_values.includes(:custom_form_field).where(custom_form_fields: { name: 'followers' }).map(&:value_int).sort
      custom_form = help_ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: 'followers' })
      expect(ids).to contain_exactly(company_user.contributor_id, company_user2.contributor_id)
      expect(AutomatedTasks::EventRouting.new).to respond_to(:perform)
    end
  end
end
