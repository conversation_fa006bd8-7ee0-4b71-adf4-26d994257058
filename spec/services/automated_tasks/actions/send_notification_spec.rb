# frozen_string_literal: true

require 'rails_helper'
include Help<PERSON><PERSON>etHelper

describe AutomatedTasks::Actions::SendNotification do
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:agent) {
    agents_group = company.groups.find_by(name: 'Help Desk Agents')
    agent = FactoryBot.create(:company_user, company: company, granted_access_at: 1.day.ago)
    agents_group.group_members.create(contributor: agent.contributor)
    agent
  }
  let(:admin) {
    admin_group = company.groups.find_by(name: 'Admins')
    admin_user = FactoryBot.create(:company_user, company: company, granted_access_at: 1.day.ago)
    admin_group.group_members.create(contributor: admin_user.contributor)
    admin_user
  }
  let(:joe_user) {
    my_user = FactoryBot.create(:company_user, company: company, granted_access_at: 1.day.ago)
    my_user
  }
  let(:assigned_to) { custom_form.custom_form_fields.find_by(name: 'assigned_to') }
  let(:followers) { custom_form.custom_form_fields.find_by(name: 'raise_this_request_on_behalf_of') }
  let(:created_by) { custom_form.custom_form_fields.find_by(name: 'created_by') }
  let(:company) { company_user.company }
  let(:workspace) { company.default_workspace }
  let(:custom_form) { company.default_workspace.custom_forms.first }
  let(:user) { company_user.user }
  let!(:help_ticket_params) {
    {
      'Subject' => 'Base Ticket',
      'Created By' => company_user.contributor_id,
      'Assigned To' => agent.contributor_id,
      'Followers' => joe_user.contributor_id,
      'Status' => 'Open',
      'Priority' => 'low',
      'Description' => 'This is a test ticket.',
      company: company,
      workspace: workspace,
      custom_form_id: custom_form.id,
    }
  }
  let!(:help_ticket) { create_ticket(help_ticket_params) }
  let(:automated_task) { AutomatedTasks::AutomatedTask.create(workspace: company.default_workspace) }
  let(:public_comment) { HelpTicketComment.create(help_ticket_id: help_ticket.id, contributor: company_user.contributor, comment_body: "original_comment", private_flag: false) }
  subject { described_class.new(task_action, help_ticket).call }

  context "assigned" do
    let!(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: "send a [Desktop notification]")
      value_data = {
        target: 'assigned',
        message: '<div>Send notification.</div>',
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      value: value_data.to_json,
                      automated_task: automated_task
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'sends the notification' do
        send_notification = AutomatedTasks::Actions::SendNotification.new(task_action, public_comment)
        recipients = send_notification.recipients
        expect(recipients).to be_present
        expect(recipients.length).to eq(1)
        expect{send_notification.call}.not_to raise_error
      end
    end
  end

  context "creator" do
    context "with a user" do
      let!(:task_action) do
        action_type = AutomatedTasks::ActionType.find_by(name: "send a [Desktop notification]")
        value_data = {
          target: 'creator',
          message: '<div>Send notification.</div>',
        }
        task_action = AutomatedTasks::TaskAction.new(
                        action_type: action_type,
                        value: value_data.to_json,
                        automated_task: automated_task
                      )
        task_action.save!
        task_action
      end

      context "#call" do
        it 'sends the notification' do
          send_notification = AutomatedTasks::Actions::SendNotification.new(task_action, public_comment)
          recipients = send_notification.recipients
          expect(recipients).to be_present
          expect(recipients.length).to eq(1)
          expect{send_notification.call}.not_to raise_error
        end
      end
    end
  end

  context "agents" do
    let!(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: "send a [Desktop notification]")
      value_data = {
        target: 'agents',
        message: '<div>Send notification.</div>',
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      value: value_data.to_json,
                      automated_task: automated_task
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'sends the email' do
        send_notification = AutomatedTasks::Actions::SendNotification.new(task_action, public_comment)
        recipients = send_notification.recipients
        expect(recipients).to be_present
        expect(recipients.length).to eq(1)
        expect{send_notification.call}.not_to raise_error
      end
    end
  end

  context "related" do
    let!(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: "send a [Desktop notification]")
      value_data = {
        target: 'related',
        message: '<div>Send notification.</div>',
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      value: value_data.to_json,
                      automated_task: automated_task
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'sends the notification ' do
        send_notification = AutomatedTasks::Actions::SendNotification.new(task_action, public_comment)
        recipients = send_notification.recipients
        expect(recipients).to be_present
        expect{send_notification.call}.not_to raise_error
      end
    end
  end

  context "multiple target types" do
    let!(:help_ticket) { create_ticket(help_ticket_params.merge!('Assigned To' => agent.contributor_id, 'Created By' => agent.contributor_id)) }
    let!(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: "send a [Desktop notification]")
      value_data = {
        target: 'assigned,creator',
        message: '<div>This is an email.</div>',
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      value: value_data.to_json,
                      automated_task: automated_task
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'sends the notification ' do
        send_notification = AutomatedTasks::Actions::SendNotification.new(task_action, public_comment)
        recipients = send_notification.recipients
        expect(recipients).to be_present
        expect(recipients.length).to eq(1)
        expect{send_notification.call}.not_to raise_error
      end
    end

    context "self action notification" do
      let(:comment3) { HelpTicketComment.create(help_ticket_id: help_ticket.id, contributor: agent.contributor, comment_body: "test self action", private_flag: false) }

      let!(:task_action) do
        action_type = AutomatedTasks::ActionType.find_by(name: "send a [Desktop notification]")
        value_data = {
          target: 'creator',
          message: '<div>Send notification.</div>',
        }
        task_action = AutomatedTasks::TaskAction.new(
                        action_type: action_type,
                        value: value_data.to_json,
                        automated_task: automated_task
                      )
        task_action.save!
        task_action
      end

      it 'will not send notification to user who performed the action if settings are disabled' do
        HelpdeskSetting.joins(:default_helpdesk_setting)
                       .find_by(default_helpdesk_setting: { setting_type: 'allow_self_action_notification' }, company: company, workspace: workspace)
                       .update(enabled: false)

        company.reload
        recipients = AutomatedTasks::Actions::SendNotification.new(task_action, comment3).recipients
        expect(recipients).not_to include(agent.email)
      end

      it 'will send notification to user who performed the action if settings are enabled' do
        HelpdeskSetting.joins(:default_helpdesk_setting)
                     .find_by(default_helpdesk_setting: { setting_type: 'allow_self_action_notification' }, company: company, workspace: workspace)
                     .update(enabled: true, selected_option: "admins_agents, other_users")
        recipients = AutomatedTasks::Actions::SendNotification.new(task_action, comment3).recipients
        expect(recipients).to include(agent.email)
      end
    end
  end

  context "admins" do
    let!(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: "send a [Desktop notification]")
      value_data = {
        target: 'admins',
        message: '<div>Send notification.</div>',
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      value: value_data.to_json,
                      automated_task: automated_task
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'sends the email' do
        admin
        send_notification = AutomatedTasks::Actions::SendNotification.new(task_action, public_comment)
        recipients = send_notification.recipients
        expect(recipients).to be_present
        expect(recipients.length).to eq(1)
        expect(recipients[0]).to eq(admin.email)
        expect{send_notification.call}.not_to raise_error
      end

      it 'creates an helpticket activity' do
        admin
        send_notification = AutomatedTasks::Actions::SendNotification.new(task_action, public_comment).call
        activity = HelpTicketActivity.find_by_activity_type('notification')
        expect(activity).to be_present
      end
    end
  end
end


