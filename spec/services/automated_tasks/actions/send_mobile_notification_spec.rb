# frozen_string_literal: true

require 'rails_helper'

describe AutomatedTasks::Actions::SendMobileNotification do
  include HelpTicketHelper

  let(:user) { company_user.user }
  let!(:user_1) { create(:user, { last_name: '<PERSON><PERSON>', first_name: '<PERSON>', password: 'password2', email: '<EMAIL>' }) }
  let!(:user_2) { create(:user, { last_name: '<PERSON>', first_name: '<PERSON>', password: 'password3', email: '<EMAIL>' }) }
  let!(:user_device_2) { create(:user_device, user: user_2 )}

  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:company_user2) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:joe_user) { FactoryBot.create(:company_user, company: company, granted_access_at: 1.day.ago) }
  let(:out_of_office_user) { FactoryBot.create(:company_user, user_id: user_1.id, out_of_office: true) }
  let(:company_user1) { FactoryBot.create(:company_user, user_id: user_2.id, granted_access_at: 1.day.ago) }

  let(:agent) {
    agents_group = company.groups.find_by(name: 'Help Desk Agents')
    agents_group.group_members.create(contributor: company_user1.contributor)
    company_user1
  }
  let(:admin) {
    admin_group = company.groups.find_by(name: 'Admins')
    admin_group.group_members.create(contributor: company_user.contributor)
    company_user
  }

  let(:company) { company_user.company }
  let(:workspace) { company.default_workspace }
  let(:custom_form) { company.custom_forms.helpdesk.first }

  let(:created_by) { custom_form.custom_form_fields.find_by(name: 'created_by') }
  let(:assigned_to) { custom_form.custom_form_fields.find_by(name: 'assigned_to') }
  let(:followers) { custom_form.custom_form_fields.find_by(name: 'followers') }

  let(:help_ticket) {
    ticket_params = {
      'Subject' => "Ticket 1",
      'Status' => 'Open',
      company: company,
      custom_form_id: custom_form.id,
    }
    ticket = create_ticket(ticket_params)
    ticket.company = company
    ticket.save!
    ticket.custom_form_values.create(custom_form_field: assigned_to, value_int: agent.contributor_id)
    ticket.custom_form_values.create(custom_form_field: created_by, value_int: company_user.contributor_id)
    ticket.custom_form_values.create(custom_form_field: followers, value_int: joe_user.contributor_id)
    ticket
  }

  let(:automated_task) { AutomatedTasks::AutomatedTask.create(workspace: workspace) }
  let(:public_comment) { HelpTicketComment.create(help_ticket: help_ticket, contributor: company_user.contributor, comment_body: 'original_comment', private_flag: false) }
  let(:private_comment) { HelpTicketComment.create(help_ticket: help_ticket, contributor: company_user.contributor, comment_body: 'original_comment', private_contributor_ids: [company_user.contributor_id, company_user2.contributor_id], private_flag: true) }

  subject { described_class.new(task_action, help_ticket).call }

  context 'assigned' do
    let!(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: 'send an [Mobile Notification]')
      value_data = { target: 'assigned',  message: 'Please send this notification to mobile app device' }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      value: value_data.to_json,
                      automated_task: automated_task
                    )
      task_action.save!
      task_action
    end

    context '#call' do
      it 'sends the notification to mobile app' do
        allow_any_instance_of(Fcm::SendMobileNotification).to receive(:call).and_return(true)
        subject
      end
    end
  end

  context 'agents' do
    let!(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: 'send an [Mobile Notification]')
      value_data = { target: 'agents', message: 'Please send this notification to mobile app device' }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      value: value_data.to_json,
                      automated_task: automated_task
                    )
      task_action.save!
      task_action
    end

    context '#call' do
      it 'sends the notification to mobile app' do
        agent
        ticket = help_ticket.reload
        allow_any_instance_of(Fcm::SendMobileNotification).to receive(:call).and_return(true)
        subject
      end
    end
  end

  context "creator" do
    context "with a user" do
      let!(:task_action) do
        action_type = AutomatedTasks::ActionType.find_by(name: "send an [Mobile Notification]")
        value_data = { target: 'creator', message: "Please send this notification to mobile app device with ticket_number: \#{ticket_number} and ticket_subject: {ticket_subject}. Where commenter \#{commenter} and comment_body: \#{comment_body}" }
        task_action = AutomatedTasks::TaskAction.new(
                        action_type: action_type,
                        value: value_data.to_json,
                        automated_task: automated_task
                      )
        task_action.save!
        task_action
      end

      context "#call with help_ticket" do
        it 'sends the notification to mobile app' do
          allow_any_instance_of(Fcm::SendMobileNotification).to receive(:call).and_return(true)
          subject
        end
      end

      context "#call with tikcet_comment" do
        it 'sends the notification to mobile app' do
          allow_any_instance_of(Fcm::SendMobileNotification).to receive(:call).and_return(true)
          described_class.new(task_action, public_comment).call
        end
      end
    end
  end

  context 'admins' do
    let!(:task_action) do
      action_type = AutomatedTasks::ActionType.find_by(name: 'send an [Mobile Notification]')
      value_data = { target: 'admins', message: 'Please send this notification to mobile app device' }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      value: value_data.to_json,
                      automated_task: automated_task
                    )
      task_action.save!
      task_action
    end

    context '#call' do
      it 'sends the notification to mobile app' do
        admin
        ticket = help_ticket.reload
        allow_any_instance_of(Fcm::SendMobileNotification).to receive(:call).and_return(true)
        subject
      end
    end
  end
end
