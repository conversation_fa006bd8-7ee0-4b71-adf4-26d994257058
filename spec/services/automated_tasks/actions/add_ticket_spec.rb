require 'rails_helper'

describe AutomatedTasks::Actions::AddTicket do

  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:subject_field) { custom_form.custom_form_fields.find_by(name: 'subject') }
  let(:priority) { custom_form.custom_form_fields.find_by(name: 'priority') }
  let(:status) { custom_form.custom_form_fields.find_by(name: 'status') }
  let(:assigned_to) { custom_form.custom_form_fields.find_by(name: 'assigned_to') }
  let(:created_by) { custom_form.custom_form_fields.find_by(name: 'created_by') }
  let(:company) { company_user.company }
  let(:workspace) { company.default_workspace }
  let(:custom_form) { workspace.custom_forms.first }
  let(:execution_date) { AutomatedTasks::ExecutionDate.new(date: company_date, company_id: company.id, automated_task: automated_task) }
  let(:company_date) { Time.zone.now.in_time_zone(TZInfo::Timezone.get(company.timezone || "America/Chicago")).to_date }

  let(:automated_task) { AutomatedTasks::AutomatedTask.create(workspace: workspace) }
  subject { described_class.new(task_action, execution_date).call }

  let!(:task_action) do
    action_type = AutomatedTasks::ActionType.find_by(name: "add a [ticket]")
    value_data = {
      "attachment_ids"=>[],
      "form_id" => custom_form.id,
      "values" => [
        { "custom_form_field_id" => priority.id,
          "value_str"=>"low"
        },
        {
          "custom_form_field_id" => status.id,
          "value_str"=>"Open"
        },
        {
          "custom_form_field_id"=> subject_field.id,
          "value_str"=>"New Ticket"
        },
        {
          "custom_form_field_id" => created_by.id,
          "value_int"=> company_user.contributor_id
        },
        {
          "custom_form_field_id"=> assigned_to.id,
          "value_int"=> company_user.contributor_id
        }
      ],
      "source" => "auto_generated"
    }
    task_action = AutomatedTasks::TaskAction.new(
                    action_type: action_type,
                    value: value_data.to_json,
                    automated_task: automated_task
                  )
    task_action.save!
    task_action
  end

  context "#call" do
    it 'add a ticket' do
      subject
      help_ticket = workspace.help_tickets.first
      expect(workspace.help_tickets.count).to eq(1)
      expect(help_ticket.subject).to eq('New Ticket')
      expect(help_ticket.priority).to eq('low')
      expect(help_ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: "status" }).value_str).to eq('Open')
      expect(help_ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: "created_by" }).value_int).to eq(company_user.contributor_id)
      expect(help_ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: "assigned_to" }).value_int).to eq(company_user.contributor_id)
      expect(help_ticket.source).to eq('auto_generated')
    end
  end
end
