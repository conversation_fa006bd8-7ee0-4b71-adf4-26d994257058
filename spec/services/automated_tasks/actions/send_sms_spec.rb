# frozen_string_literal: true

require 'rails_helper'
include <PERSON><PERSON>icketHelper

describe AutomatedTasks::Actions::SendSms do
  let(:company) { create(:company) }
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago, company: company) }
  let(:custom_form) { company.custom_forms.company_user.find_by(default: true) }
  let(:workspace) { company.default_workspace }
  let(:automated_task) { AutomatedTasks::AutomatedTask.create(workspace: workspace) }
  let(:action_type) { AutomatedTasks::ActionType.find_by(name: "send a [sms]") }
  let(:help_desk_agents) { company.groups.find_by(name: 'Help Desk Agents') }
  let!(:agent) {
    cu = FactoryBot.create(:company_user, company: company, granted_access_at: 1.day.ago)
    cu.contributor.group_members << GroupMember.new(group: help_desk_agents)
    cu
  }
  let(:admin) {
    admin_group = company.groups.find_by(name: 'Admins')
    admin_user = FactoryBot.create(:company_user, company: company, granted_access_at: 1.day.ago)
    admin_group.group_members.create(contributor: admin_user.contributor)
    admin_user
  }
  let(:user) { company_user.user }
  let(:message) { "Boom!!" }
  subject { described_class.new(task_action, help_ticket).call }
  let(:help_ticket) {
    ticket_params = {
      'Subject' => "Test Ticekt",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [agent.contributor_id, admin.contributor_id],
      'Priority' => "low",
      company: company,
    }
    create_ticket(ticket_params)
  }

  let(:help_ticket_external_creator) {
    ticket_params = {
      'Subject' => "Test Ticekt",
      'Created By' => "<EMAIL>",
      'Assigned To' => [agent.contributor_id],
      'Priority' => "low",
      company: company,
    }
    create_ticket(ticket_params)
  }

  let!(:phone_field) { custom_form.custom_form_fields.find_by(name: "mobile_phone") }

  let!(:user_phone_value) { create(:custom_form_value,
                                   custom_form_field: phone_field,
                                   value_str: 'US,(*************',
                                   custom_form: custom_form,
                                   module: company_user,
                                   company: company) }

  let!(:agent_phone_value) { create(:custom_form_value,
                                    custom_form_field: phone_field,
                                    value_str: 'US,(*************',
                                    custom_form: custom_form,
                                    module: agent,
                                    company: company) }
  let!(:admin_phone_value) { create(:custom_form_value,
                                    custom_form_field: phone_field,
                                    value_str: 'US,(*************',
                                    custom_form: custom_form,
                                    module: admin,
                                    company: company) }

  describe "assigned" do
    let!(:task_action) do
      value_data = {
        target: 'assigned',
        message: message,
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      automated_task: automated_task,
                      value: value_data.to_json
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'sends an sms' do
        msg = AutomatedTasks::StringInterpolate.new(help_ticket).call(message)
        allow_any_instance_of(Twilio::SendSms).to receive(:send_sms).and_return(true)
        subject
      end
    end
  end

  context "agents" do
    let!(:task_action) do
      value_data = {
        target: 'agents',
        message: message,
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      automated_task: automated_task,
                      value: value_data.to_json
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'sends a message' do
        msg = AutomatedTasks::StringInterpolate.new(help_ticket).call(message)
        allow_any_instance_of(Twilio::SendSms).to receive(:send_sms).and_return(true)
        subject
      end
    end
  end

  context "creator" do
    context "with a user" do
      let(:help_ticket) {
        ticket_params = {
          'Subject' => "First one",
          'Created By' => company_user.contributor_id,
          'Assigned To' => [company_user.contributor_id],
          'Followers' => [company_user.contributor_id],
          'Impacted Devices' => [],
          'Status' => 'Open',
          'Priority' => "low",
          'Description' => "The red robin sits in the tree.",
          company: company,
        }
        create_ticket(ticket_params)
      }
      let!(:task_action) do
        value_data = {
          target: 'creator',
          message: message,
        }
        task_action = AutomatedTasks::TaskAction.new(
                        action_type: action_type,
                        automated_task: automated_task,
                        value: value_data.to_json
                      )
        task_action.save!
        task_action
      end

      context "#call" do
        it 'sends a message' do
          msg = AutomatedTasks::StringInterpolate.new(help_ticket).call(message)
          allow_any_instance_of(Twilio::SendSms).to receive(:send_sms).and_return(true)
          subject
        end
      end
    end
  end

  context "specified" do
    let!(:task_action) do
      value_data = {
        target: 'specified',
        recipients: '+12223334455',
        message: message,
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      automated_task: automated_task,
                      value: value_data.to_json
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'sends an sms' do
        msg = AutomatedTasks::StringInterpolate.new(help_ticket).call(message)
        allow_any_instance_of(Twilio::SendSms).to receive(:send_sms).and_return(true)
        subject
      end
    end
  end

  context "Ticket with comment" do
    let(:message_body) { "subject {ticket_subject}" }
    let!(:ticket_comment) { FactoryBot.create(:help_ticket_comment, contributor_id: company_user.contributor_id, help_ticket_id: help_ticket.id) }
    let!(:interpolate_message) { AutomatedTasks::StringInterpolate.new(ticket_comment).call(message_body) }
    let!(:task_action) do
      value_data = {
        target: 'specified',
        recipients: '+12223334466',
        message: message_body
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      automated_task: automated_task,
                      value: value_data.to_json
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'sends an sms' do
        allow_any_instance_of(Twilio::SendSms).to receive(:send_sms).and_return(true)
        subject
      end
    end
  end

  context "specified" do
    let!(:task_action) do
      value_data = {
        target: 'specified',
        recipients: '+12223334455,+12223334456',
        message: message,
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      automated_task: automated_task,
                      value: value_data.to_json
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'recipients' do
        send_sms_obj = described_class.new(task_action, help_ticket)
        expect(send_sms_obj.recipients.length).to eq(2)
      end
    end
  end

  context "creator" do
    let!(:task_action) do
      value_data = {
        target: 'creator',
        message: message,
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      automated_task: automated_task,
                      value: value_data.to_json
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'recipients' do
        send_sms_obj = described_class.new(task_action, help_ticket)
        expect(send_sms_obj.recipients.length).to eq(1)
      end
    end
  end

  context "assigned" do
    let!(:task_action) do
      value_data = {
        target: 'assigned',
        message: message,
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      automated_task: automated_task,
                      value: value_data.to_json
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'recipients' do
        send_sms_obj = described_class.new(task_action, help_ticket)
        expect(send_sms_obj.recipients.length).to eq(1)
      end
    end
  end

  context "Ticket with external creator" do
    let!(:task_action) do
      value_data = {
        target: 'creator',
        message: message,
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      automated_task: automated_task,
                      value: value_data.to_json
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'recipients' do
        send_sms_obj = described_class.new(task_action, help_ticket_external_creator)
        expect(send_sms_obj.recipients.length).to eq(0)
      end
    end
  end

  context "Ticket with external creator" do
    let!(:task_action) do
      value_data = {
        target: 'creator',
        message: message,
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      automated_task: automated_task,
                      value: value_data.to_json
                    )
      task_action.save!
      task_action
    end

    it 'will not send sms to user who performed the action if settings are disabled' do
      HelpdeskSetting.joins(:default_helpdesk_setting)
                     .find_by(default_helpdesk_setting: { setting_type: 'allow_self_action_notification' }, company: company, workspace: workspace)
                     .update(enabled: false)

      send_recipients = described_class.new(task_action, help_ticket).recipients
      expect(send_recipients).not_to include(help_ticket.creator_numbers[0])
    end

    it 'will send sms to user who performed the action if settings are enabled' do
      HelpdeskSetting.joins(:default_helpdesk_setting)
                     .find_by(default_helpdesk_setting: { setting_type: 'allow_self_action_notification' }, company: company, workspace: workspace)
                     .update(enabled: true, selected_option: "admins_agents, other_users")
      send_recipients = described_class.new(task_action, help_ticket).recipients
      expect(send_recipients).to include(help_ticket.creator_numbers[0])
    end
  end

  context "admins" do
    let!(:task_action) do
      value_data = {
        target: 'admins',
        message: message,
      }
      task_action = AutomatedTasks::TaskAction.new(
                      action_type: action_type,
                      automated_task: automated_task,
                      value: value_data.to_json
                    )
      task_action.save!
      task_action
    end

    context "#call" do
      it 'sends a message' do
        msg = AutomatedTasks::StringInterpolate.new(help_ticket).call(message)
        allow_any_instance_of(Twilio::SendSms).to receive(:send_sms).and_return(true)
        subject
      end
    end
  end
end

