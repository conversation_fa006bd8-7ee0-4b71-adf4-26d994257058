require 'rails_helper'
include Company<PERSON>serHelper

describe AutomatedTasks::Actions::SendMsTeamsMessage do
  include HelpTicketHelper

  create_company_and_user

  let!(:config) { create(:ms_teams_config, company: company) }
  let(:workspace) { company.default_workspace }
  let(:automated_task) { AutomatedTasks::AutomatedTask.create(workspace: workspace) }
  let!(:task_action) do
    action_type = AutomatedTasks::ActionType.find_by(name: 'send a [MS Teams message]')
    task_action = AutomatedTasks::TaskAction.new(
                    action_type: action_type,
                    automated_task: automated_task,
                    value: "{\"message\":\"This is an automated task alert\",\"ms_teams_config\":{\"id\":#{config.id}}}"
                  )
    task_action.save!
    task_action
  end

  subject { described_class.new(task_action, help_ticket).call }

  context 'send an message on MS Teams' do
    let(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "helpdesk") }

    let(:help_ticket) {
      ticket_params = {
        'Subject' => "Ticket 1",
        'Status' => 'Open',
        company: company,
        custom_form_id: custom_form.id,
      }
      ticket = create_ticket(ticket_params)
      ticket.company = company
      ticket.save!
      ticket
    }

    it 'will send a message to MS Teams' do
      allow_any_instance_of(Integrations::MsTeams::Client).to receive(:send_message).and_return(true)
      expect(subject.data['activity_label']).to eq("Ms team message")
    end
  end
end
