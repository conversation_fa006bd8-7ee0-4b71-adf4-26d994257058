require 'rails_helper'
include CompanyUserHelper

describe AutomatedTasks::Actions::SendSlackMessage do
  include HelpTicketHelper
  include ::Integrations::Helpdesk::Slack::SlackHelperMethods

  create_company_and_user

  let(:workspace) { company.default_workspace }
  let(:automated_task) { AutomatedTasks::AutomatedTask.create(workspace: workspace) }
  let(:slack_config) do
    Integrations::Slack::Config.create!(company: company,
                                        team_id: 'B09VVKA1K8Q',
                                        team_name: 'GoGenuity',
                                        channel_id: 'X12KMSC971Q',
                                        channel_name: '#helpdesk',
                                        access_token: 'mock_access_token',
                                        workspace: company.default_workspace)
  end

  let!(:task_action) do
    action_type = AutomatedTasks::ActionType.find_by(name: 'send a [Slack message]')
    task_action = AutomatedTasks::TaskAction.new(
                    action_type: action_type,
                    automated_task: automated_task,
                    value: "{\"subject\":\"Ticket created\",\"body\":\"This is an automated task alert\",\"slack_config\":{\"id\":#{slack_config.id},\"team_name\":\"#{slack_config.team_name}\",\"#{slack_config.channel_name}\":\"#helpdesk\"}}"
                  )
    task_action.save!
    task_action
  end

  subject { described_class.new(task_action, help_ticket).call }

  context "send an alert on Slack" do
    let(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "helpdesk") }
    
    let!(:help_ticket) {
      ticket_params = {
        'Subject' => "Ticket 1",
        'Status' => 'Open',
        company: company,
        custom_form_id: custom_form.id,
      }
      ticket = create_ticket(ticket_params)
      ticket.company = company
      ticket.save!
      ticket
    }

    let(:msg_response) { OpenStruct.new(ts: '*********.000200') }
    let(:ticket_detail_attachment) do
      [
        {
          "type": "section",
          "text": {
            "type": "mrkdwn",
            "text": "#{JSON.parse(task_action.value)['body']}"
          }
        }
      ]
    end

    let(:ticket_detail_block) do
      [
        {
          "type": "section",
          "text": {
            "type": "mrkdwn",
            "text": "#{JSON.parse(task_action.value)['body']}"
          }
        }
      ]
    end

    it "will attach Slack message thread with ticket" do
      expect(help_ticket.slack_message_ids.size).to eq(0)

      if slack_feature_access_pending?(slack_config.company_id)
        allow_any_instance_of(Integrations::Slack::Client).to receive(:ticket_detail_block).and_return(:ticket_detail_block)
      else 
        allow_any_instance_of(Integrations::Slack::Client).to receive(:ticket_detail_attachment).and_return(:ticket_detail_attachment) 
      end
      
      allow_any_instance_of(Integrations::Slack::Client).to receive(:send_message).and_return(msg_response)
      allow_any_instance_of(Integrations::Slack::Client).to receive(:client).and_return(nil)
      subject
      actual = help_ticket.reload
      expect(help_ticket.slack_message_ids.size).to eq(1)
    end
  end
end
