# frozen_string_literal: true

require 'rails_helper'
include <PERSON><PERSON>icketHelper

describe AutomatedTasks::TimedTaskExecutor do
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:company) {
    my_company = company_user.company
    my_company
  }
  let!(:stub_pusher) { Pusher.stub(:trigger) }
  let(:task_datetime) { Time.zone.now }
  let(:day) {company.created_at.day}
  let(:month) {company.created_at.month}
  let(:workspace) { company.default_workspace }
  let(:user) { company_user.user }
  let(:help_ticket) {
    create_ticket(ticket_params)
  }

  let(:custom_form) { company.custom_forms.first }
  let(:form_field) { custom_form.custom_form_fields.find_by(name: "subject") }

  let(:automated_task) do
    task = AutomatedTasks::AutomatedTask.new(workspace: workspace, order: 2)
    task_event = AutomatedTasks::TaskEvent.new
    event_type = AutomatedTasks::EventType.find_by(name: "{a date} occurs")
    task_event.event_type = event_type
    object = AutomatedTasks::EventDetail.new
    object.event_subject_type = event_type.children.find_by(name: "/a date/ that is on the [date of the year]")
    task_date = Time.local(task_datetime.year, month, day).in_time_zone(workspace.business_hour.timezone || "America/Chicago")
    object.value = {
      year_dates: [task_date.strftime("%Y-%m-%d")]
    }.to_json
    task_event.event_details << object
    task.task_events << task_event

    task_action = AutomatedTasks::TaskAction.new
    task_action.action_type = AutomatedTasks::ActionType.find_by(name: 'create an [alert]',
                                                                 module: 'help_tickets',
                                                                 model: 'HelpTicket')
    task_action.value = action_value.to_json
    task_action.save!
    task.task_actions << task_action
    task.save!
    task
  end

  let(:action_value) {
    {
      "target": "specific",
      "message": "<div>ok</div>",
      "contributors": [ { id: company.company_users.first.contributor_id } ],
    }
  }

  let(:ticket_params) {
    {
      'Subject' => "[AAAA] Test Subject",
      'Created By' => company_user.contributor_id,
      'Status' => 'Open',
      'Priority' => "low",
      company: company,
    }
  }

  let(:job_args) { AutomatedTasks::TaskExecutorWorker.jobs.map { |job| job['args'] } }

  subject { described_class.new.call }

  context "with a date ready to execute" do
    before do
      freeze_date = Time.local(task_datetime.year, month, day).in_time_zone(workspace.business_hour.timezone || "America/Chicago")
      Timecop.freeze(freeze_date)
      automated_task
    end

    after do
      Timecop.return
    end

    it "executes the task action" do
      subject
      job_args.each do |args|
        AutomatedTasks::TaskExecutorWorker.new.perform(*args)
      end
      expect(ActionableAlert.last).to be_present
      expect(ActionableAlert.last.message).to eq("<div>ok</div>")
    end
  end

  context "with duplicate execution" do
    before do
      freeze_date = Time.local(task_datetime.year, 11, 15).in_time_zone("America/Chicago")
      if freeze_date < task_datetime
        freeze_date += 1.year
      end
      Timecop.freeze(freeze_date)
      automated_task
    end

    after do
      Timecop.return
    end

    it "does not execute the task action" do
      AutomatedTasks::ExecutionDate.create!(date: DateTime.now.in_time_zone(workspace.business_hour.timezone || "America/Chicago").to_date, company: company, automated_task: automated_task)
      subject
      expect(ActionableAlert.last).to be_blank
    end
  end
end

module AutomatedTasks
  module Subjects
    class BadSubject
      include AutomatedTasks::Concerns::Eventing

      def match?(object)
        raise "Boom"
      end
    end
  end
end
