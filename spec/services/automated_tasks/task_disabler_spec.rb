require 'rails_helper'

RSpec.describe AutomatedTasks::DependentAutomatedTasksService, type: :service do
  let!(:company) { FactoryBot.create(:company, name: "Test Company", default_logo_url: "https://example.com/logo.png") }
  let!(:workspace) { FactoryBot.create(:workspace, company: company) }
  
  let!(:user_single) { FactoryBot.create(:user, email: "<EMAIL>") }
  let!(:company_user_single) { FactoryBot.create(:company_user, company: company, user: user_single) }
  
  let!(:user_1) { FactoryBot.create(:user, email: "<EMAIL>") }
  let!(:user_2) { FactoryBot.create(:user, email: "<EMAIL>") }
  let!(:company_user1) { FactoryBot.create(:company_user, company: company, user: user_1) }
  let!(:company_user2) { FactoryBot.create(:company_user, company: company, user: user_2) }

  let!(:custom_form) { FactoryBot.create(:custom_form) }
  let!(:custom_form_field1) { FactoryBot.create(:custom_form_field, custom_form: custom_form, label: "custom_form_field_1", field_attribute_type: "text") }
  let!(:custom_form_field2) { FactoryBot.create(:custom_form_field, custom_form: custom_form, label: "custom_form_field_2", field_attribute_type: "text", required: true) }

  describe "#call" do
    context "when the only contributor of a SetFormField task is deleted" do
      let!(:automated_task_single) do
        AutomatedTasks::AutomatedTask.create!(workspace: workspace,
                                              force_disabled: false,
                                              task_actions: [
                                                AutomatedTasks::TaskAction.new(
                                                  action_type: AutomatedTasks::ActionType.new(action_class: "SetFormField"),
                                                  value: { "contributors" => [{ "id" => company_user_single.contributor_id }] }.to_json
                                                )
                                              ]
                                            )
      end

      it "disables the automated task" do
        service = AutomatedTasks::DependentAutomatedTasksService.new([workspace.id],
                                                                     company_user_single.contributor_id,
                                                                     [company_user_single],
                                                                     false,
                                                                     true,
                                                                     false)

        expect(automated_task_single.force_disabled).to eq(false)
        service.call
        automated_task_single.reload
        expect(automated_task_single.force_disabled).to eq(true)
      end
    end

    let(:service) do
      AutomatedTasks::DependentAutomatedTasksService.new([workspace.id],
                                                         [company_user1.contributor_id],
                                                         [],
                                                         false,
                                                         true,
                                                         true)
    end

    context "when one of multiple contributors of an automated task with SendEmail action is deleted" do
      let!(:automated_task_multi) do
        AutomatedTasks::AutomatedTask.create!(workspace: workspace,
                                              force_disabled: false,
                                              task_actions: [
                                                AutomatedTasks::TaskAction.new(
                                                  action_type: AutomatedTasks::ActionType.new(action_class: "SendEmail"),
                                                  value: { "contributors" => [{ "id" => company_user1.contributor_id }, { "id" => company_user2.contributor_id }] }.to_json
                                                )
                                              ]
                                            )
      end

      it "removes the deleted contributor associated with SendEmail action" do

        service.call
        automated_task_multi.reload

        updated_contributors = JSON.parse(automated_task_multi.task_actions.first.value)["contributors"]
        expect(updated_contributors.map { |c| c["id"] }).not_to include(company_user1.contributor_id)
      end
    end

    context "when one of multiple contributors of an automated task with AddTicket action is deleted" do
      let!(:automated_task_add_ticket) do
        AutomatedTasks::AutomatedTask.create!(workspace: workspace,
                                              task_actions: [
                                                AutomatedTasks::TaskAction.new(
                                                  action_type: AutomatedTasks::ActionType.new(action_class: "AddTicket"),
                                                  value: {
                                                    "values" => [
                                                      { "custom_form_field_id" => custom_form_field1.id, "value_int" => company_user1.contributor_id },
                                                      { "custom_form_field_id" => custom_form_field2.id, "value_int" => company_user2.contributor_id }
                                                    ]
                                                  }.to_json
                                                )
                                              ]
                                            )
      end

      it "removes the deleted contributor associated with AddTicket action" do

        service.call
        automated_task_add_ticket.reload

        updated_values = JSON.parse(automated_task_add_ticket.task_actions.first.value)["values"]
        expect(updated_values.map { |v| v["value_int"] }).not_to include(company_user1.contributor_id)
      end
    end

    context "when one of multiple contributors of an automated task with SetFormField action is deleted" do
      let(:automated_task_set_form) { AutomatedTasks::AutomatedTask.create!(workspace: workspace) }
      let!(:task_action) do
        action_type = AutomatedTasks::ActionType.create!(action_class: "SetFormField",
                                                         name: "set a [form field]")
        value_data = {
                      "form_field" => {
                        "name" => "assigned_to",
                        "field_attribute_type" => "people_list",
                        "label" => "assigned to",
                        "options" => []
                      },
                      "form_value" => {
                        "contributors" => [
                          company_user1.contributor_id,
                          company_user2.contributor_id
                        ],
                        "routing" => "Round Robin"
                      }
                    }
        AutomatedTasks::TaskAction.create!(action_type: action_type,
                                           value: value_data.to_json,
                                           automated_task: automated_task_set_form)
      end

      it "removes the deleted contributor associated with SetFormField action" do
        service.call

        task_action.reload
      
        updated_task_value = JSON.parse(task_action.value)
        expect(updated_task_value["form_value"]["contributors"]).not_to include(company_user1.contributor_id)
      end
    end
  end
end

