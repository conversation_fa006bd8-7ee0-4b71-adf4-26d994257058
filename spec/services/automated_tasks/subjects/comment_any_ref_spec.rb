# frozen_string_literal: true

require 'rails_helper'
include HelpTicketHelper

describe AutomatedTasks::Subjects::CommentAnyRef do
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:company) { company_user.company }
  let!(:company_asset_type) { CompanyAssetType.create(name: 'Other', company: company) }
  let(:user) { company_user.user }
  let(:help_ticket) {
    ticket_params = {
      'Subject' => "Test Subject",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      workspace: company.default_workspace,
    }
    create_ticket(ticket_params)
  }
  let(:comment) { HelpTicketComment.new(help_ticket: help_ticket, comment_body: comment_body) }
  let(:comment_have_no_mention) { HelpTicketComment.new(help_ticket: help_ticket, comment_body: comment_body_have_no_mention) }
  let(:asset) { create(:managed_asset, company: company, asset_type: company_asset_type) }
  let(:comment_body) {
    "<div>this is <a href=\"http://#{company.subdomain}.#{Rails.application.credentials.root_domain}:3000/vendors/408\">Codeship</a></div>"
  }
  let(:comment_body_have_no_mention) {
    "<div>Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod
    tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam,
    quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo
    consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse
    cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non
    proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</div>"
  }

  subject { described_class.new(event_subject) }

  context "Comment body have mentions" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/mention/ of any type")
      object.save!

      object
    end

    it "returns true" do
      comment.save!
      expect(subject.match?(comment)).to be true
    end
  end

  context "comment body have no mentions" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a comment/ which includes a [mention]")
      object.save!

      object
    end

    it "returns false" do
      comment.save!
      expect(subject.match?(comment_have_no_mention)).to be false
    end
  end
end

