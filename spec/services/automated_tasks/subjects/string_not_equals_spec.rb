# frozen_string_literal: true

require 'rails_helper'

describe AutomatedTasks::Subjects::StringNotEquals do
  subject { described_class.new(event_subject) }
  let!(:event_subject) do
    object = AutomatedTasks::EventDetail.new
    object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a string/ does not equal [text]")
    object.value = "{\"text\":\"this is it\"}"
    object.save!

    object
  end

  context "with an equal string" do
    it "returns false" do
      expect(subject.match?("this is it")).to be false
    end
  end

  context "with a non-equals string" do
    it "returns true" do
      expect(subject.match?("This should not trigger the conditions needed.")).to be true
    end
  end
end

