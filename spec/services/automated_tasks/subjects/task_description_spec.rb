require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

describe AutomatedTasks::Subjects::TaskDescription do
  create_company_and_user
  subject { described_class.new(event_subject) }
  let!(:project_task) { create(:project_task, help_ticket: help_ticket, company: company, description: "hello") }
  let(:help_ticket) {
    ticket_params = {
      'Subject' => "Test Subject",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
    }
    create_ticket(ticket_params)
  }

  context "with a matching description" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "a project task that has a {description}")
      object.value = "{\"text\":\"hello\"}"
      object.save!

      object
    end

    it "should match the description" do
      expect(subject.match?(project_task)).to be true
    end
  end

  context "with a non-matching description" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "a project task that has a {description}")
      object.value = "{\"text\":\"testing\"}"
      object.save!

      object
    end

    it "should not match the description" do
      expect(subject.match?(project_task)).to be false
    end
  end
end
