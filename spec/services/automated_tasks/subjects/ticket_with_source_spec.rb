require 'rails_helper'
include Help<PERSON>icketHelper

describe AutomatedTasks::Subjects::TicketWithSource do
  let(:company_user) { FactoryBot.create(:company_user) }
  let(:company) { company_user.company }
  let(:help_ticket) {
    ticket_params = {
      'Subject' => 'Test Subject',
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => 'medium',
      'Description' => 'The red robin sits in the tree.',
      'source' => 'slack',
      workspace: company.default_workspace,
    }
    create_ticket(ticket_params)
  }

  subject { described_class.new(event_subject) }

  context 'with a matching source of ticket' do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: '/a ticket/ {with source}')
      object.value = "{\"source\":\"slack\"}"
      object.save!

      object
    end

    it 'returns true' do
      expect(subject.match?(help_ticket)).to be true
    end
  end

  context 'with a non-matching source of ticket' do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: '/a ticket/ {with source}')
      object.value = "{\"source\":\"ms_teams\"}"
      object.save!

      object
    end

    it 'returns false' do
      expect(subject.match?(help_ticket)).to be false
    end
  end
end
