# frozen_string_literal: true

require 'rails_helper'
include Help<PERSON>icketHelper

describe AutomatedTasks::Subjects::TicketFormField do
  let(:company) { company_user.company }
  let(:workspace) { company.default_workspace }
  let(:user) { company_user.user }
  let(:custom_form) { help_ticket.custom_form }
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:company_user2) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:guest_user) { create(:guest, company: company, workspace: workspace, email: "<EMAIL>") }
  let(:guest_user2) { create(:guest, company: company, workspace: workspace, email: "<EMAIL>") }

  let(:ticket_params) {
    {
      'Subject' => "Test Subject",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => "high",
      'Description' => "The red robin sits in the tree.",
      workspace: workspace,
    }
  }
  subject { AutomatedTasks::Subjects::TicketFormField.new(event_subject) }

  let(:help_ticket) {
    create_ticket(ticket_params)
  }

  context "with a specific custom form" do
    let(:help_ticket) {
      ticket_params['Status'] = 'Closed'
      create_ticket(ticket_params)
    }
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a ticket/ with {a form field}")
      json = {
        custom_forms: [{
          id: custom_form.id,
          form_name: custom_form.form_name,
        }],
        form_field: {
          name: 'status',
          field_attribute_type: 'status',
        },
        status: 'Closed',
      }
      object.value = json.to_json
      object.save!

      object
    end

    it "returns true for the help ticket" do
      expect(subject.match?(help_ticket)).to be_truthy
    end
  end

  context "with all custom forms" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a ticket/ with {a form field}")
      json = {
        custom_forms: [{
          id: 'all',
          form_name: 'all',
        }],
        form_field: {
          name: 'status',
          field_attribute_type: 'status',
        },
        status: 'Closed',
      }
      object.value = json.to_json
      object.save!

      object
    end

    it "returns true for the help ticket" do
      expect(subject.match?(help_ticket)).to be_falsy
    end
  end

  context "with a specific custom form" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a ticket/ with {a form field}")
      json = {
        custom_forms: [{
          id: custom_form.id,
          form_name: custom_form.form_name,
        }],
        form_field: {
          name: 'priority',
          field_attribute_type: 'priority',
        },
        priority: 'high',
      }
      object.value = json.to_json
      object.save!

      object
    end

    it "returns true for the help ticket" do
      expect(subject.match?(help_ticket)).to be_truthy
    end
  end

  context "without custom form" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a ticket/ with {a form field}")
      json = {
        custom_forms: [{
          id: 'id does not exist',
          form_name: 'name does not exist',
        }],
        form_field: {
          name: 'priority',
          field_attribute_type: 'priority',
        },
        priority: 'high',
      }
      object.value = json.to_json
      object.save!

      object
    end

    it "returns true for the help ticket" do
      expect(subject.match?(help_ticket)).to be_falsy
    end
  end

  context "without custom field" do
    let(:help_ticket) {
      ticket_params['Status'] = 'In Progress'
      create_ticket(ticket_params)
      HelpTicket.last
    }
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a ticket/ with {a form field}")
      json = {
        custom_forms: [{
          id: custom_form.id,
          form_name: custom_form.form_name,
        }],
        form_field: {
          name: "status", 
          field_attribute_type: "status",
          options: ["Open", "In Progress", "Closed"],
          label: "status"
        },
        negate: false,
        any: false,
        partial_match: false, 
        status: 'Closed',
      }
      object.value = json.to_json
      object.save!

      object
    end

    it "returns true for the help ticket" do
      expect(subject.match?(help_ticket)).to be_falsy
    end
  end

  context "with any value" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a ticket/ with {a form field}")
      json = {
        custom_forms: [{
          id: custom_form.id,
          form_name: custom_form.form_name,
        }],
        form_field: {
          name: 'priority',
          field_attribute_type: 'priority',
        },
        any: true,
      }
      object.value = json.to_json
      object.save!

      object
    end

    it "returns true for the help ticket" do
      expect(subject.match?(help_ticket)).to be_truthy
    end
  end

  context "with negate value" do
    let(:help_ticket) {
      ticket_params['Priority'] = 'low'
      create_ticket(ticket_params)
    }
    let(:help_ticket2) {
      ticket_params['Priority'] = 'high'
      create_ticket(ticket_params)
    }
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a ticket/ with {a form field}")
      json = {
        custom_forms: [{
          id: custom_form.id,
          form_name: custom_form.form_name,
        }],
        form_field: {
          name: 'priority',
          field_attribute_type: 'priority',
        },
        negate: true,
        priority: 'high',
      }
      object.value = json.to_json
      object.save!

      object
    end

    it "returns true for the help ticket" do
      expect(subject.match?(help_ticket)).to be_truthy
    end

    it "returns true for the help ticket" do
      expect(subject.match?(help_ticket2)).to be_falsy
    end
  end

  context "with a specific domain user in staff field" do
    let(:help_ticket) {
      ticket_params['Created By'] = guest_user.contributor_id
      create_ticket(ticket_params)
    }

    let(:help_ticket_2) {
      ticket_params['Created By'] = guest_user2.contributor_id
      create_ticket(ticket_params)
    }

    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a ticket/ with {a form field}")
      json = {
        custom_forms: [{
          id: custom_form.id,
          form_name: custom_form.form_name,
        }],
        form_field: {
          name: 'created_by',
          field_attribute_type: 'people_list',
        },
        partial_match: true,
        partial_value: 'domain.com',
      }
      object.value = json.to_json
      object.save!

      object
    end

    it "returns true for the help ticket" do
      expect(subject.match?(help_ticket)).to be_truthy
    end

    it "returns false for the help ticket created using another domain" do
      expect(subject.match?(help_ticket_2)).to be_falsy
    end
  end

  context "with a company user in staff field" do
    let(:help_ticket) {
      ticket_params['Created By'] = company_user.contributor_id
      create_ticket(ticket_params)
    }

    let(:help_ticket_2) {
      ticket_params['Created By'] = company_user2.contributor_id
      create_ticket(ticket_params)
    }

    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a ticket/ with {a form field}")
      json = {
        custom_forms: [{
          id: custom_form.id,
          form_name: custom_form.form_name,
        }],
        form_field: {
          name: 'created_by',
          field_attribute_type: 'people_list',
        },
        partial_match: false,
        contributors: [company_user.contributor_id],
      }
      object.value = json.to_json
      object.save!

      object
    end

    it "returns true for the help ticket" do
      expect(subject.match?(help_ticket)).to be_truthy
    end

    it "returns false for the help ticket created by another user" do
      expect(subject.match?(help_ticket_2)).to be_falsy
    end
  end

  context "Show Help Ticket with Tag" do
    let(:custom_form) { company.custom_forms.helpdesk.first }
    let!(:tag) {
      tag = CustomFormField.new(label: "Tag",
                                field_attribute_type: "tag",
                                order_position: 10,
                                options: "[\"First\",\"Second\",\"Third\"]",
                                required: false,
                                note: nil,
                                default_value: "First",
                                private: false,
                                permit_view_default: true,
                                permit_edit_default: true,
                                name: "tag",
                                custom_form_id: custom_form.id
                              )
      tag.field_position = FieldPosition.new(custom_form_field_id: tag.id, position: "right")
      custom_form.custom_form_fields << tag
      tag
    }
    let!(:help_ticket1) {
      ticket_params['Tag'] = "Second"
      create_ticket(ticket_params)
    }

    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a ticket/ with {a form field}")
      json = {
        custom_forms: [{
          id: custom_form.id,
          form_name: custom_form.form_name,
        }],
        form_field: {
          name: 'tag',
          field_attribute_type: 'tag',
        },
        text: ["Second"],
        partial_match: true,
        contributors: [company_user.contributor_id],
      }
      object.value = json.to_json
      object.save!

      object
    end

    it "will check tag field" do
      value_str = help_ticket1.custom_form_values.find_by(custom_form_field_id: tag.id).value_str
      expect(value_str).to eq("Second")
    end

    it "returns true for the help ticket" do
      expect(subject.match?(help_ticket1)).to be_truthy
    end
  end
end
