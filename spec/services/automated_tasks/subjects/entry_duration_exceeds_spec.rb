# frozen_string_literal: true

require 'rails_helper'

describe AutomatedTasks::Subjects::EntryDurationExceeds do
  let(:company) { company_user.company }
  let(:user) { company_user.user }
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:time_spent) { build(:time_spent, company_user: company_user, time_spent: '3h') }

  subject { described_class.new(event_subject) }

  context "with a lower duration" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "a time entry exceed [duration]")
      object.value = "{\"duration\":\"3 hours 15 minutes\"}"
      object.save!

      object
    end

    it "returns false" do
      expect(subject.match?(time_spent)).to be false
    end
  end

  context "with a higher duration" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "a time entry exceed [duration]")
      object.value = "{\"duration\":\"2 hr 15 min\"}"
      object.save!

      object
    end

    it "returns true" do
      expect(subject.match?(time_spent)).to be true
    end
  end
end

