# frozen_string_literal: true

require 'rails_helper'
include <PERSON>TicketHelper

describe AutomatedTasks::Subjects::CommentAssetRef do
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:company) { company_user.company }
  let!(:company_asset_type) { CompanyAssetType.create(name: 'Other', company: company) }
  let(:user) { company_user.user }
  let(:help_ticket) {
    ticket_params = {
      'Subject' => "Test Subject",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      company: company
    }
    create_ticket(ticket_params)
  }
  let(:comment) { HelpTicketComment.new(help_ticket: help_ticket, comment_body: comment_body) }
  let(:asset) { create(:managed_asset, company: company, asset_type: company_asset_type) }
  let(:comment_body) {
    "<div>this is a <a href=\"https://#{company.subdomain}.gogenuity-staging.com/company/users/#{company_user.id}\">BigDog User</a> 
    <a href=\"https://#{company.subdomain}.gogenuity-staging.com/vendors/408\">Codeship</a> 
    <a href=\"https://#{company.subdomain}.gogenuity-staging.com/contracts/234\">My Contract</a> 
    <a href=\"https://#{company.subdomain}.gogenuity-staging.com/managed_assets/#{asset.id}\">#{asset.name}</a>
    <a href=\"https://#{company.subdomain}.gogenuity-staging.com/help_tickets/2893\">Email for help desk is slow</a></div>"
  }

  subject { described_class.new(event_subject) }

  context "with a matching asset" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a comment/ that references [an asset]")
      object.value = "{\"assets\":[{\"id\":#{asset.id},\"name\":\"#{asset.name}\"}]}"
      object.save!

      object
    end

    it "returns true" do
      comment.save!
      expect(subject.match?(comment)).to be true
    end
  end

  context "with a non-matching contract" do
    let(:asset2) {
      FactoryBot.create(:managed_asset, company: company, name: 'My Asset', asset_type: company_asset_type)
    }
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a comment/ that references [an asset]")
      object.value = "{\"assets\":[{\"id\":#{asset2.id},\"name\":\"#{asset2.name}\"}]}"
      object.save!

      object
    end

    it "returns true" do
      comment.save!
      expect(subject.match?(comment)).to be false
    end
  end

  context "with a missing note" do
    let(:comment_body) { nil }

    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a comment/ that references [an asset]")
      object.value = "{\"assets\":[{\"id\":#{asset.id},\"name\":\"#{asset.name}\"}]}"
      object.save!

      object
    end

    it "returns false" do
      comment.save!
      expect(subject.match?(comment)).to be false
    end
  end
end
