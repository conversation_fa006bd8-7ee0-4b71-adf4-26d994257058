# frozen_string_literal: true

require 'rails_helper'

describe AutomatedTasks::Subjects::EntryCreatedBy do
  let(:company) { company_user.company }
  let(:user) { company_user.user }
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:time_spent) { build(:time_spent, company_user: company_user) }
  let!(:event_subject) do
    object = AutomatedTasks::EventDetail.new
    object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "a time entry is created by or includes [a user or group]")
    object.value = "{\"contributors\":[#{company_user.contributor_id}]}"
    object.save!

    object
  end

  subject { described_class.new(event_subject) }

  context "with a matching contributor" do
    it "returns true" do
      expect(subject.match?(time_spent)).to be true
    end
  end

  context "with a diferent contributor" do
    let(:company_user2) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
    let(:time_spent2) { build(:time_spent, company_user: company_user2) }
    it "returns false" do
      expect(subject.match?(time_spent2)).to be false
    end
  end
end

