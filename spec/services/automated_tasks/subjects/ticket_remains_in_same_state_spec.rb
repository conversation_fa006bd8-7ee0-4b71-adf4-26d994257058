require 'rails_helper'
include <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

describe AutomatedTasks::Subjects::TicketRemainsInSameState do
  let(:company_user) { FactoryBot.create(:company_user) }
  let(:company) { company_user.company }
  let(:workspace) { company.default_workspace }

  let!(:automated_task) do
    task = AutomatedTasks::AutomatedTask.new(workspace: workspace)
    task_event = AutomatedTasks::TaskEvent.new
    event_type = AutomatedTasks::EventType.find_by(name: '{a ticket} remains in the same state')
    task_event.event_type = event_type
    object = AutomatedTasks::EventDetail.new(value: "{\"custom_forms\":[{\"id\":\"all\",\"is_active\":true,\"form_name\":\"Any Form\"}],\"form_field\":{\"field_attribute_type\":\"status\",\"name\":\"status\",\"label\":\"Status\",\"options\":[\"Open\",\"In Progress\",\"Closed\"]},\"negate\":false,\"any\":false,\"partial_match\":false,\"status\":\"Open\",\"time_interval\":\"1\"}")
    object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: '/a ticket/ with {a form field}')
    task_event.event_details << object
    task.task_events << task_event
    task.save!
    task
  end

  let!(:help_ticket_1) { create_ticket(ticket_params('Open')) }

  subject { AutomatedTasks::Subjects::TicketRemainsInSameState.new(automated_task.task_events.first.event_details.first) }

  context 'ticket remains in the same state' do
    it 'returns true and updates the status of scheduled AT to executed' do
      status_field = help_ticket_1.custom_form.custom_form_fields.find_by(name: "status")
      status_cf_val = help_ticket_1.custom_form_values.find_by(custom_form_field: status_field)
      status_cf_val.scheduled_automated_tasks.find_by(status: 'pending').update_columns(trigger_at: DateTime.now - 1.hour)
      status_cf_val.update_columns(updated_at: DateTime.now - 2.hour)
      expect(subject.match?(status_cf_val)).to be true
      expect(status_cf_val.scheduled_automated_tasks.find_by(status: 'executed')).to be_present
    end
  end

  context 'ticket does not remain in same state' do
    it 'returns false and update status of scheduled AT to expired' do
      status_cf_val = help_ticket_1.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: 'status' })
      status_cf_val.scheduled_automated_tasks.find_by(status: 'pending').update_columns(trigger_at: DateTime.now - 1.hour)
      status_cf_val.update_columns(updated_at: DateTime.now - 2.hour)
      status_cf_val.update_columns(value_str: 'Closed')
      expect(subject.match?(status_cf_val)).to be false
      expect(help_ticket_1.scheduled_automated_tasks.find_by(status: 'expired')).to be_present
    end
  end

  def ticket_params(status)
    {
      'Subject' => 'Laptop is dead.',
      'created_at' => 1.hour.ago,
      'updated_at' => 1.hour.ago,
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => status,
      'Priority' => 'medium',
      'Description' => 'Seems like RAM issue',
      workspace: workspace,
    }
  end
end
