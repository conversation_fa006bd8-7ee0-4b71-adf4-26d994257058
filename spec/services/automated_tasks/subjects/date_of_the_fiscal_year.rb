require 'rails_helper'

describe AutomatedTasks::Subjects::DateOfTheFiscalYear do
  let(:company) { create(:company) }
  let(:workspace) { company.default_workspace }

  describe  "Start of the fiscal year option " do
    let(:value_json) { "{\"fiscal_value\": \"start of the fiscal year\"}" }
    let!(:task_event) do
      event_type = AutomatedTasks::EventType.find_by(name: "{a date} occurs")
      event = AutomatedTasks::TaskEvent.create(event_type: event_type)

      subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a date/ that is on the [date of fiscal year]")
      detail = AutomatedTasks::EventDetail.create(event_subject_type: subject_type, value: value_json)
      event.event_details << detail
      event
    end
    let(:event_criteria) { AutomatedTasks::TaskExecutor.new(nil, nil).event(task_event) }

    context "when executed on a the right start date of the fiscal year" do
      it "returns true" do
        Timecop.freeze(Date.strptime('2021-01-01').to_date) do 
          execution_date = AutomatedTasks::ExecutionDate.new(date: Time.zone.now, company: company)
          expect(event_criteria.match?(execution_date)).to be true
        end
      end
    end

    context "when executed on a the wrong fiscal year start date" do
      it "returns false" do
        Timecop.freeze(Date.strptime('2020-10-30').to_date) do
          execution_date = AutomatedTasks::ExecutionDate.new(date: Time.zone.now, company: company)
          expect(event_criteria.match?(execution_date)).to be false
        end
      end
    end
  end

  describe  "End of the fiscal year option " do
    let(:value_json) { "{\"fiscal_value\": \"end of the fiscal year\"}" }
    let!(:task_event) do
      event_type = AutomatedTasks::EventType.find_by(name: "{a date} occurs")
      event = AutomatedTasks::TaskEvent.create(event_type: event_type)

      subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a date/ that is on the [date of fiscal year]")
      detail = AutomatedTasks::EventDetail.create(event_subject_type: subject_type, value: value_json)
      event.event_details << detail
      event
    end
    let(:event_criteria) { AutomatedTasks::TaskExecutor.new(nil, nil).event(task_event) }

    context "when executed on a the right end date of the fiscal year" do
      it "returns true" do
        Timecop.freeze(Date.strptime('2020-12-31').to_date) do 
          execution_date = AutomatedTasks::ExecutionDate.new(date: Time.zone.now, company: company)
          expect(event_criteria.match?(execution_date)).to be true
        end
      end
    end

    context "when executed on a the wrong fiscal year end date" do
      it "returns false" do
        Timecop.freeze(Date.strptime('2020-10-10').to_date) do
          execution_date = AutomatedTasks::ExecutionDate.new(date: Time.zone.now, company: company)
          expect(event_criteria.match?(execution_date)).to be false
        end
      end
    end
  end
end
