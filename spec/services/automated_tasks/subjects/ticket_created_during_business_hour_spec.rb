require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

describe AutomatedTasks::Subjects::TicketCreatedDuringBusinessHour do
  create_company_and_user

  let(:default_business_days) { ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"] }
  let(:default_start_time) { "09:00" }
  let(:default_end_time) { "17:00" }

  def time_with_in_business_hours
    opening_time = default_start_time.in_time_zone(company.timezone)
    closing_time = default_end_time.in_time_zone(company.timezone)
    rand(opening_time..closing_time)
  end

  let!(:automated_task) do
    task = AutomatedTasks::AutomatedTask.new(workspace: workspace)
    task_event = AutomatedTasks::TaskEvent.new
    event_type = AutomatedTasks::EventType.find_by(name: '{a ticket} is created')
    task_event.event_type = event_type
    object = AutomatedTasks::EventDetail.new(value: "{\"hours\":\"business_hours\"}")
    object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: '/a ticket/ {during}')
    task_event.event_details << object
    task.task_events << task_event
    task.save!
    task
  end

  let(:help_ticket_1) { create_ticket(ticket_params(time_with_in_business_hours)) }
  let(:help_ticket_2) { create_ticket(ticket_params('Mon, 07 Aug 2023 23:51:54.********* UTC +00:00')) }

  subject { described_class.new(automated_task.task_events.first.event_details.first) }

  context 'ticket created with in business hour' do
    it 'returns true' do
      expect(subject.match?(help_ticket_1)).to be true
    end
  end

  context 'ticket not created with in business hour' do
    it 'returns false' do
      expect(subject.match?(help_ticket_2)).to be false
    end
  end

  def ticket_params(created_at)
    {
      'Subject' => 'Laptop is dead.',
      'created_at' => created_at,
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => 'medium',
      'Description' => 'Seems like RAM issue',
      workspace: workspace,
    }
  end
end
