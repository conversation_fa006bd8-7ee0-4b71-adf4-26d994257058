# frozen_string_literal: true

require 'rails_helper'
include <PERSON><PERSON>icketHelper

describe AutomatedTasks::Subjects::TicketUser do
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:company) { company_user.company }
  let(:user) { company_user.user }
  let(:help_ticket) {
    ticket_params = {
      'Subject' => "Test Subject",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      company: company,
    }
    create_ticket(ticket_params)
  }

  subject { AutomatedTasks::Subjects::TicketUser.new(event_subject) }

  context "with a matching contributor" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "a ticket from [a user or group]")
      object.value = "{\"contributors\":[#{company_user.contributor_id}]}"
      object.save!

      object
    end

    it "returns true" do
      help_ticket.save!
      expect(subject.match?(help_ticket)).to be true
    end
  end

  context "with a matching task" do
    let(:company_user2) { FactoryBot.create(:company_user, company: company, granted_access_at: 1.day.ago) }
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "a ticket from [a user or group]")
      object.value = "{\"contributors\":[{\"id\":#{company_user2.contributor_id},\"name\":\"#{company_user2.full_name}\"}]}"
      object.save!

      object
    end

    it "executes the task action" do
      help_ticket.save!
      expect(subject.match?(help_ticket)).to be false
    end
  end
end

