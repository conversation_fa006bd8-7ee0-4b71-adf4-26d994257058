# frozen_string_literal: true

require 'rails_helper'
include <PERSON>TicketHelper

describe AutomatedTasks::Subjects::CommentText do
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:company) { company_user.company }
  let(:user) { company_user.user }
  let(:help_ticket) {
    ticket_params = {
      'Subject' => "Test Subject",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      workspace: company.default_workspace,
    }
    create_ticket(ticket_params)
  }
  let(:comment) { HelpTicketComment.new(help_ticket: help_ticket, comment_body: comment_body) }
  let(:asset) { create(:managed_asset, company: company) }
  let(:comment_body) { "This is a comment." }

  subject { described_class.new(event_subject) }

  context "with matching text" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "a comment with [text]")
      object.value = "{\"text\":\"comment\"}"
      object.save!

      object
    end

    it "returns true" do
      comment.save!
      expect(subject.match?(comment)).to be true
    end
  end

  context "with non-matching comment" do
    let(:asset2) { FactoryBot.create(:managed_asset, company: company, name: 'My Asset') }
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "a comment with [text]")
      object.value = "{\"text\":\"abcdefg\"}"
      object.save!

      object
    end

    it "returns true" do
      comment.save!
      expect(subject.match?(comment)).to be false
    end
  end

  context "with a missing note" do
    let(:comment_body) { nil }

    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "a comment with [text]")
      object.value = "{\"text\":\"abcdefg\"}"
      object.save!

      object
    end

    it "returns false" do
      comment.save!
      expect(subject.match?(comment)).to be false
    end
  end
end
