# frozen_string_literal: true

require 'rails_helper'

describe AutomatedTasks::Subjects::StringContains do
  subject { described_class.new(event_subject) }
  let!(:event_subject) do
    object = AutomatedTasks::EventDetail.new
    object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a string/ contains [text]")
    object.value = "{\"text\":\"match\"}"
    object.save!

    object
  end

  context "with a matching string" do
    it "returns true" do
      expect(subject.match?("This should match up with the given text.")).to be true
    end
  end

  context "with a non-matching string" do
    it "returns false" do
      expect(subject.match?("This should not trigger the conditions needed.")).to be false
    end
  end
end

