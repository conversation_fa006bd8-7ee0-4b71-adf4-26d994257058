# frozen_string_literal: true

require 'rails_helper'

describe AutomatedTasks::Subjects::PriorityHigher do
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:company) { company_user.company }
  let(:user) { company_user.user }

  subject { described_class.new(event_subject) }

  context "with a lower priority" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "a priority is set to [priority] or higher")
      object.value = "{\"priority\":\"medium\"}"
      object.save!

      object
    end

    it "returns true" do
      expect(subject.match?('low')).to be false
    end
  end

  context "with a higher priority" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "a priority is set to [priority] or higher")
      object.value = "{\"priority\":\"medium\"}"
      object.save!

      object
    end

    it "returns true" do
      expect(subject.match?('high')).to be true
    end
  end

  context "with an equal priority" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "a priority is set to [priority] or higher")
      object.value = "{\"priority\":\"medium\"}"
      object.save!

      object
    end

    it "returns true" do
      expect(subject.match?('medium')).to be true
    end
  end
end

