# frozen_string_literal: true

require 'rails_helper'
include HelpTicketHelper

describe AutomatedTasks::Subjects::CommentUserRef do
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:company) { company_user.company }
  let(:user) { company_user.user }
  let(:group) do
    my_group = Group.create(name: "Group", company: company)
  end
  let(:help_ticket) {
    ticket_params = {
      'Subject' => "Test Subject",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      company: company
    }
    create_ticket(ticket_params)
  }
  let(:comment) { HelpTicketComment.new(help_ticket: help_ticket, comment_body: comment_body) }
  let(:comment_body) {
    """
      <div>this is a
        <a href=\"https://#{company.subdomain}.gogenuity-staging.com/company/users/#{company_user.id}\">BigDog User</a>
        <a href=\"https://bengal-technologies.gogenuity-staging.com/vendors/408\">Codeship</a>
        <a href=\"https://bengal-technologies.gogenuity-staging.com/company/groups/#{group.id}/edit\">#{group.name}</a>
        <a href=\"https://bengal-technologies.gogenuity-staging.com/managed_assets/123946\">Darlene's iPhone</a>
        <a href=\"https://bengal-technologies.gogenuity-staging.com/help_tickets/2893\">Email for help desk is slow</a>
      </div>
    """
  }

  subject { described_class.new(event_subject) }

  context "with a matching user contributor" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a comment/ which includes a mention to [a user or group]")
      object.value = "{\"contributors\":[#{company_user.contributor_id}]}"
      object.save!

      object
    end

    it "returns true" do
      comment.save!
      expect(subject.match?(comment)).to be true
    end
  end

  context "with a matching group contributor" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a comment/ which includes a mention to [a user or group]")
      object.value = "{\"contributors\":[#{group.contributor_id}]}"
      object.save!

      object
    end

    it "returns true" do
      comment.save!
      expect(subject.match?(comment)).to be true
    end
  end

  context "with a non-matching contributor" do
    let(:company_user2) { FactoryBot.create(:company_user, company: company, granted_access_at: 1.day.ago) }
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a comment/ which includes a mention to [a user or group]")
      object.value = "{\"contributors\":[#{company_user2.contributor_id}]}"
      object.save!

      object
    end

    it "returns true" do
      comment.save!
      expect(subject.match?(comment)).to be false
    end
  end

  context 'with any user or group contributor mentioned in comment' do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a comment/ which includes a mention to [a user or group]")
      object.value = "{\"mention_type\":\"any\"}"
      object.save!

      object
    end

    it 'returns true' do
      comment.save!
      expect(subject.match?(comment)).to be true
    end
  end

  context "with missing note" do
    let(:comment_body) { nil }

    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a comment/ which includes a mention to [a user or group]")
      object.value = "{\"contributors\":[#{company_user.contributor_id}]}"
      object.save!

      object
    end

    it "returns false" do
      comment.save!
      expect(subject.match?(comment)).to be false
    end
  end
end

