# frozen_string_literal: true

require 'rails_helper'

describe AutomatedTasks::Subjects::EmailDomainEquals do
  subject { described_class.new(event_subject) }
  let!(:event_subject) do
    object = AutomatedTasks::EventDetail.new
    object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/an email/ equals [text]")
    object.value = value_json
    object.save!

    object
  end

  context "when a domain is given" do
    let(:value_json) { "{\"text\":\"disney.com\"}" }

    context "with an equal domain" do
      it "returns true" do
        expect(subject.match?("<EMAIL>")).to be true
      end
    end

    context "with a non-equal domain" do
      it "returns false" do
        expect(subject.match?("<EMAIL>")).to be false
      end
    end
  end

  context "when an email address is given" do
    let(:value_json) { "{\"text\":\"<EMAIL>\"}" }

    context "with an equal domain" do
      it "returns true" do
        expect(subject.match?("<EMAIL>")).to be true
      end
    end

    context "with a non-equal domain" do
      it "returns false" do
        expect(subject.match?("<EMAIL>")).to be false
      end
    end
  end
end

