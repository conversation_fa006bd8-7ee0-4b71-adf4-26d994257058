# frozen_string_literal: true

require 'rails_helper'

describe AutomatedTasks::Subjects::DayOfTheWeek do
  let(:company) { create(:company) }
  subject { described_class.new(event_subject) }
  let(:value_json) { "{\"days\": [1, 4]}" }
  let(:workspace) { company.default_workspace }
  let!(:task_event) do
    event_type = AutomatedTasks::EventType.find_by(name: "{a date} occurs")
    event = AutomatedTasks::TaskEvent.create(event_type: event_type)

    subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a date/ that is on a [day of the week]")
    detail = AutomatedTasks::EventDetail.create(event_subject_type: subject_type, value: value_json)
    event.event_details << detail
    event
  end
  let(:event_criteria) { AutomatedTasks::TaskExecutor.new(nil, nil).event(task_event) }

  context "when executed on a the right day of the week" do
    it "returns true" do
      Timecop.freeze(Date.strptime('2021-08-23').to_date) do 
        execution_date = AutomatedTasks::ExecutionDate.new(date: Time.zone.now, company: company)
        expect(event_criteria.match?(execution_date)).to be true
      end
    end
  end

  context "when executed on a the wrong day of the week" do
    it "returns false" do
      Timecop.freeze(Date.strptime('2021-08-24').to_date) do 
        execution_date = AutomatedTasks::ExecutionDate.new(date: Time.zone.now, company: company)
        expect(event_criteria.match?(execution_date)).to be false
      end
    end
  end
end
