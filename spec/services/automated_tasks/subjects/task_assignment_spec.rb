require 'rails_helper'
include CompanyUserHelper
include HelpTicketHelper

describe AutomatedTasks::Subjects::TaskAssignment do
  create_company_and_user
  subject { described_class.new(event_subject) }
  let(:company_user2) { FactoryBot.create(:company_user, company: company, granted_access_at: 1.day.ago) }
  let!(:project_task) { create(:project_task, help_ticket: help_ticket, company: company) }
  let!(:task_assignee) { TaskAssignee.create(project_task: project_task, contributor: company_user.contributor) }
  let(:help_ticket) {
    ticket_params = {
      'Subject' => "Test Subject",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
    }
    create_ticket(ticket_params)
  }

  context "with a matching contributor" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "a project task that is assigned to [a user or group]")
      object.value = "{\"contributors\":[#{company_user.contributor_id}]}"
      object.save!

      object
    end

    it "should match the contributor" do
      expect(subject.match?(project_task)).to be true
    end
  end

  context "with a non-matching contributor" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "a project task that is assigned to [a user or group]")
      object.value = "{\"contributors\":[#{company_user2.contributor_id}]}"
      object.save!

      object
    end

    it "should not match the contributor" do
      expect(subject.match?(project_task)).to be false
    end
  end
end
