# frozen_string_literal: true

require 'rails_helper'

describe AutomatedTasks::Subjects::EmailEquals do
  subject { described_class.new(event_subject) }
  let!(:event_subject) do
    object = AutomatedTasks::EventDetail.new
    object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/an email address/ equals [text]")
    object.value = "{\"text\":\"Mickey Mouse <<EMAIL>>\"}"
    object.save!

    object
  end

  context "with an equal email" do
    it "returns true" do
      expect(subject.match?("<EMAIL>")).to be true
    end
  end

  context "with a non-equals string" do
    it "returns false" do
      expect(subject.match?("<EMAIL>")).to be false
    end
  end
end

