require 'rails_helper'
include <PERSON><PERSON><PERSON>et<PERSON>elper

describe AutomatedTasks::Subjects::TicketWithSourceAndField do
  let(:company_user) { FactoryBot.create(:company_user) }
  let(:company) { company_user.company }
  let(:help_ticket) {
    ticket_params = {
      'Subject' => 'Test Subject',
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => 'medium',
      'Description' => 'The red robin sits in the tree.',
      'source' => 'slack',
      workspace: company.default_workspace,
    }
    create_ticket(ticket_params)
  }

  subject { described_class.new(event_subject) }

  context 'with a matching source of ticket' do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: '/a ticket/ {with source}')
      object.value = "{\"custom_forms\":[{\"id\":\"all\",\"is_active\":true,\"form_name\":\"Any Form\"}],\"form_field\":{\"name\":\"status\",\"field_attribute_type\":\"status\",\"label\":\"status\",\"options\":[\"Open\",\"In Progress\",\"Closed\"]},\"negate\":null,\"any\":true,\"partial_match\":false,\"status\":null,\"source\":\"slack\"}"
      object.save!

      object
    end

    it 'and matching form field condition it returns true' do
      cfv = help_ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: 'status' })
      expect(subject.match?(cfv)).to be true
    end

    it 'and non-matching form field condition it returns false' do
      cfv = help_ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: 'description' })
      expect(subject.match?(cfv)).to be false
    end
  end

  context 'with a non-matching source of ticket' do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: '/a ticket/ {with source}')
      object.value = "{\"custom_forms\":[{\"id\":\"all\",\"is_active\":true,\"form_name\":\"Any Form\"}],\"form_field\":{\"name\":\"status\",\"field_attribute_type\":\"status\",\"label\":\"status\",\"options\":[\"Open\",\"In Progress\",\"Closed\"]},\"negate\":null,\"any\":true,\"partial_match\":false,\"status\":null,\"source\":\"ms_teams\"}"
      object.save!

      object
    end

    it 'and matching form field condition it returns false' do
      cfv = help_ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: 'status' })
      expect(subject.match?(cfv)).to be false
    end

    it 'and non-matching form field condition it returns false' do
      cfv = help_ticket.custom_form_values.includes(:custom_form_field).find_by(custom_form_fields: { name: 'description' })
      expect(subject.match?(cfv)).to be false
    end
  end
end
