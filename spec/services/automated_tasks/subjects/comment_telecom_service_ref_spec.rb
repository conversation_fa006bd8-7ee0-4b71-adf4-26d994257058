# frozen_string_literal: true

require 'rails_helper'
include HelpTicketHelper

describe AutomatedTasks::Subjects::CommentTelecomServiceRef do
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:company) { company_user.company }
  let(:telecom_provider) { FactoryBot.create(:telecom_provider, company: company)}
  let!(:telecom_service) { FactoryBot.create(:telecom_service, service_type: "data_service", telecom_provider: telecom_provider, company: company) }
  let(:user) { company_user.user }
  let(:help_ticket) {
    ticket_params = {
      'Subject' => "Test Subject",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      company: company
    }
    create_ticket(ticket_params)
  }
  let(:comment) { HelpTicketComment.new(help_ticket: help_ticket, comment_body: comment_body) }
  let(:comment_body) {
    "<div>this is a <a href=\"https://#{company.subdomain}.gogenuity-staging.com/company/users/#{company_user.id}\">BigDog User</a> 
    <a href=\"https://#{company.subdomain}.gogenuity-staging.com/vendors/408\">Codeship</a> 
    <a href=\"https://#{company.subdomain}.gogenuity-staging.com/contracts/234\">My Contract</a> 
    <a href=\"https://#{company.subdomain}.gogenuity-staging.com/telecom_services/#{telecom_service.id}\">Telecomservice</a></div>"
  }

  subject { described_class.new(event_subject) }

  context "with a matching telecom service" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a comment/ that references [an telecom service]")
      object.value = "{\"telecom_services\":[{\"id\":#{telecom_service.id},\"name\":\"#{telecom_service.name}\"}]}"
      object.save!

      object
    end

    it "returns true" do
      comment.save!
      expect(subject.match?(comment)).to be true
    end
  end

  context "with a non-matching telecom service" do
    let!(:telecom_service2) { FactoryBot.create(:telecom_service, service_type: "data_service", telecom_provider: telecom_provider, company: company) }

    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a comment/ that references [an telecom_service]")
      object.value = "{\"telecom_services\":[{\"id\":#{telecom_service2.id},\"name\":\"#{telecom_service2.name}\"}]}"
      object.save!

      object
    end

    it "returns true" do
      comment.save!
      expect(subject.match?(comment)).to be false
    end
  end

  context "with a missing note" do
    let(:comment_body) { nil }

    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a comment/ that references [an telecom_service]")
      object.value = "{\"telecom_services\":[{\"id\":#{telecom_service.id},\"name\":\"#{telecom_service.name}\"}]}"
      object.save!

      object
    end

    it "returns false" do
      comment.save!
      expect(subject.match?(comment)).to be false
    end
  end
end

