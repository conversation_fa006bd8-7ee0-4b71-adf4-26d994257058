# frozen_string_literal: true

require 'rails_helper'

describe AutomatedTasks::Subjects::FormValueEqual do
  let(:company) { create(:company) }
  let(:custom_form) { company.default_workspace.custom_forms.first }
  let(:ticket1) {
    ticket = create(:help_ticket, company: company, custom_form: custom_form)
    ticket.custom_form_values.create(custom_form_field: subject_field, value_str: 'Dogs are cool Ticket 1')
    ticket.custom_form_values.create(custom_form_field: priority, value_str: 'medium')
    ticket
  }
  let(:ticket2) {
    ticket = create(:help_ticket, company: company, custom_form: custom_form)
    ticket.custom_form_values.create(custom_form_field: subject_field, value_str: 'Blah blah blah')
    ticket.custom_form_values.create(custom_form_field: priority, value_str: 'low')
    ticket
  }
  let(:subject_field) { custom_form.custom_form_fields.find_by(name: "subject") }
  let(:priority) { custom_form.custom_form_fields.find_by(name: "priority") }
  subject { described_class.new(event_subject) }

  context "text" do
    let!(:event_subject) do
      field_object = AutomatedTasks::EventDetail.new
      field_object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a ticket/ with {a form field}")
      field_object.value = {
        custom_form: {
          id: custom_form.id,
          form_name: custom_form.form_name,
        },
        form_field: {
          id: subject_field.id,
          label: subject_field.label,
          field_attribute_type: subject_field.field_attribute_type,
        },
      }.to_json
      field_object.save!

      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = field_object.event_subject_type.children.find_by(name: "/a form field/ that does contain [a form value]")
      object.value = "{\"text\":\"Dogs are cool\"}"
      object.save!

      object
    end

    context "with an equal value" do
      it "returns true" do
        subject_values = ticket1.custom_form_values.where(custom_form_field: subject_field)
        expect(subject.match?(subject_values)).to be true
      end
    end

    context "with a non-equals string" do
      it "returns false" do
        subject_values = ticket2.custom_form_values.where(custom_form_field: subject_field)
        expect(subject.match?(subject_values)).to be false
      end
    end
  end

  context "priority" do
    let!(:event_subject) do
      field_object = AutomatedTasks::EventDetail.new
      field_object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a ticket/ with {a form field}")
      field_object.value = {
        custom_form: {
          id: custom_form.id,
          form_name: custom_form.form_name,
        },
        form_field: {
          id: subject_field.id,
          label: subject_field.label,
          field_attribute_type: subject_field.field_attribute_type,
        },
      }.to_json
      field_object.save!

      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = field_object.event_subject_type.children.find_by(name: "/a form field/ that does contain [a form value]")
      object.value = "{\"priority\":\"medium\"}"
      object.save!

      object
    end

    context "with an equal value" do
      it "returns true" do
        priority_values = ticket1.custom_form_values.where(custom_form_field: priority)
        expect(subject.match?(priority_values)).to be true
      end
    end

    context "with a non-equals string" do
      it "returns false" do
        priority_values = ticket2.custom_form_values.where(custom_form_field: priority)
        expect(subject.match?(priority_values)).to be false
      end
    end
  end
end
