# frozen_string_literal: true

require 'rails_helper'
include <PERSON><PERSON>icketHelper

describe AutomatedTasks::Subjects::AttachmentExcluded do
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:company_user2) { FactoryBot.create(:company_user, company: company, granted_access_at: 1.day.ago) }
  let(:company) { company_user.company }
  let(:user) { company_user.user }
  let(:custom_form) { company.custom_forms.find_by(company_module: "helpdesk") }
  let(:attachment_field) { custom_form.custom_form_fields.find_by(name: 'attachments') }
  let(:help_ticket) {
    ticket_params = {
      'Subject' => "Test Subject",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
      workspace: company.default_workspace,
    }
    create_ticket(ticket_params)
  }
  let(:custom_form_value) { CustomFormValue.create(module: help_ticket, custom_form_field: attachment_field) }
  let(:attached_file) { File.open(Rails.root.join("README.md")) }
  let(:attachment) { build(:custom_form_attachment, custom_form_value: custom_form_value, attachment: { io: attached_file, filename: 'README/md' }, company: company) }

  subject { described_class.new(event_subject) }

  context "with a matching contributor" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/an attachment/ that filename excludes [text]")
      object.value = "{\"text\": \"README\"}"
      object.save!

      object
    end

    it "returns false" do
      attachment.save!
      expect(subject.match?(attachment)).to be false
    end
  end

  context "with a non-matching contributor" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/an attachment/ that filename excludes [text]")
      object.value = "{\"text\": \"ZAEEM\"}"
      object.save!

      object
    end

    it "returns true" do
      attachment.save!
      expect(subject.match?(attachment)).to be true
    end
  end
end

