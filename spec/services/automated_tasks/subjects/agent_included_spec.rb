# frozen_string_literal: true

require 'rails_helper'
include <PERSON><PERSON>icketHelper

describe AutomatedTasks::Subjects::AgentIncluded do
  let(:company_user) { FactoryBot.create(:company_user, granted_access_at: 1.day.ago) }
  let(:company_user2) { FactoryBot.create(:company_user, company: company, granted_access_at: 1.day.ago) }
  let(:company) { company_user.company }
  let(:user) { company_user.user }
  let(:help_ticket) {
    ticket_params = {
      'Subject' => "Test Subject",
      'Created By' => company_user.contributor_id,
      'Assigned To' => [company_user.contributor_id],
      'Followers' => [company_user.contributor_id],
      'Impacted Devices' => [],
      'Status' => 'Open',
      'Priority' => "medium",
      'Description' => "The red robin sits in the tree.",
    }
    create_ticket(ticket_params)
  }

  subject { described_class.new(event_subject) }

  context "with a matching contributor" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "a comment that references [an asset]")
      object.value = "{\"contributors\":[#{company_user.contributor_id}]}"
      object.save!

      object
    end
  end

  context "with a non-matching contributor" do
    let!(:event_subject) do
      object = AutomatedTasks::EventDetail.new
      object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "a comment that references [an asset]")
      object.value = "{\"contributors\":[#{company_user2.contributor_id}]}"
      object.save!

      object
    end
  end
end

