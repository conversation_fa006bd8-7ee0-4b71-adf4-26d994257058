# frozen_string_literal: true

require 'rails_helper'

describe AutomatedTasks::Subjects::StringEquals do
  subject { described_class.new(event_subject) }
  let!(:event_subject) do
    object = AutomatedTasks::EventDetail.new
    object.event_subject_type = AutomatedTasks::ObjectSubjectType.find_by(name: "/a string/ equals [text]")
    object.value = "{\"text\":\"this is it\"}"
    object.save!

    object
  end

  context "with an equal string" do
    it "returns true" do
      expect(subject.match?("this is it")).to be true
    end
  end

  context "with a non-equals string" do
    it "returns false" do
      expect(subject.match?("This should not trigger the conditions needed.")).to be false
    end
  end
end

