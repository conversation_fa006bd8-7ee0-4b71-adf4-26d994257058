# frozen_string_literal: true

require 'rails_helper'

describe AutomatedTasks::StringInterpolate do
  let(:company) { create(:company) }
  let(:custom_form) { company.default_workspace.custom_forms.first }
  let(:subject_field) { custom_form.custom_form_fields.find_by(name: "subject") }
  let(:priority_field) { custom_form.custom_form_fields.find_by(name: "priority") }
  let(:name) { "Cool Ticket" }
  let(:help_ticket) {
    ticket = create(:help_ticket, company: company)
    ticket.custom_form_values << CustomFormValue.new(custom_form_field: subject_field, value_str: name)
    ticket
  }
  let(:help_ticket2) { create(:help_ticket, company: company) }
  let(:company_user1) { create(:company_user, company: company) }
  let(:company_user2) { create(:company_user, company: company) }
  let(:company_user3) { create(:company_user, company: company) }
  let(:company_user4) { create(:company_user, company: company) }
  let(:company_user5) { create(:company_user, company: company) }

  let!(:comment1) { 
    HelpTicketComment.create(
      help_ticket: help_ticket, 
      comment_body: "Boom!", 
      private_flag: true, 
      contributor_id: company_user1.contributor_id, 
      private_contributor_ids: [company_user1.contributor_id, company_user2.contributor_id],
      created_at: Time.zone.parse("2022-06-17")
    ) 
  }
  let!(:comment2) { 
    HelpTicketComment.create(
      help_ticket: help_ticket, 
      comment_body: "Hurrah!", 
      private_flag: true, 
      contributor_id: company_user1.contributor_id, 
      private_contributor_ids: [company_user1.contributor_id, company_user3.contributor_id],
      created_at: Time.zone.parse("2022-07-18")
    ) 
  }
  let!(:comment3) { 
    HelpTicketComment.create(
      help_ticket: help_ticket, 
      comment_body: "Boomrah!", 
      private_flag: true, 
      contributor_id: company_user1.contributor_id, 
      private_contributor_ids: [company_user1.contributor_id, company_user2.contributor_id],
      created_at: Time.zone.parse("2022-08-19")
    ) 
  }
  let!(:comment4) { 
    HelpTicketComment.create(
      help_ticket: help_ticket,
      comment_body: "David Warner!", 
      private_flag: true, 
      contributor_id: company_user4.contributor_id,
      created_at: Time.zone.parse("2022-09-20")
    )
  }
  let!(:comment5) { 
    HelpTicketComment.create(
      help_ticket: help_ticket2, 
      comment_body: "Resolution!", 
      private_flag: true, 
      contributor_id: company_user5.contributor_id,
      resolution_flag: true
    )
  }

  subject { described_class.new(help_ticket) }

  context "with a valid substituation" do
    context "with a simple string" do
      let(:test_text) {
        "This ticket {ticket subject} is cool."
      }
      let(:expected_text) {
        "This ticket #{name} is cool."
      }

      it "executes the task action" do
        actual = subject.call(test_text)
        expect(actual).to eq(expected_text)
      end
    end

    context "with a complex string" do
      subject { described_class.new(priority_value) }
      let(:priority_value) { CustomFormValue.create(custom_form_field: priority_field, value_str: 'high', module: help_ticket) }
      let(:company_subdomain) { company.subdomain }
      let(:help_ticket_guid) { help_ticket.guid }
      let(:help_ticket_number) { help_ticket.ticket_number }
      let(:test_text) {
        "\n<div>\n  <div>\n    <span style=\"font-size: 1.5rem;\">{field_name} has been changed to {value} for ticket {ticket_subject} (\#{ticket_number}).</span>\n    <br/>\n    <br/>\n    {ticket_button}\n    <br/>\n    <br/>\n  </div>\n</div>\n"
      }
      let(:expected_text) {
        "\n<div>\n  <div>\n    <span style=\"font-size: 1.5rem;\">priority has been changed to high for ticket Cool Ticket (\##{help_ticket_number}).</span>\n    <br/>\n    <br/>\n    <a class=\"btn-template\" href=\"http://#{company_subdomain}.localhost:3000/preview_help_tickets/#{help_ticket_guid}\">Go to ticket</a>\n    <br/>\n    <br/>\n  </div>\n</div>\n"
      }
      it "executes the task action" do
        actual = subject.call(test_text)
        expect(actual).to eq(expected_text)
      end
    end

    context "with a help ticket comment" do
      subject { described_class.new(comment) }
      let(:comment) { HelpTicketComment.create(help_ticket: help_ticket, comment_body: "Boom!", email: '<EMAIL>') }
      let(:company_subdomain) { company.subdomain }
      let(:help_ticket_guid) { help_ticket.guid }
      let(:help_ticket_number) { help_ticket.ticket_number }
      let(:test_text) {
        "\n<div>\n  <div>\n    <span style=\"font-size: 1.5rem;\">{commenter} has added comment {comment_body} for ticket {ticket_subject} (\#{ticket_number}).</span>\n    <br/>\n    <br/>\n    {ticket_button}\n    <br/>\n    <br/>\n  </div>\n</div>\n"
      }
      let(:expected_text) {
        "\n<div>\n  <div>\n    <span style=\"font-size: 1.5rem;\"><EMAIL> has added comment Boom! for ticket Cool Ticket (\##{help_ticket_number}).</span>\n    <br/>\n    <br/>\n    <a class=\"btn-template\" href=\"http://#{company_subdomain}.localhost:3000/preview_help_tickets/#{help_ticket_guid}\">Go to ticket</a>\n    <br/>\n    <br/>\n  </div>\n</div>\n"
      }
      it "executes the task action" do
        actual = subject.call(test_text)
        expect(actual).to eq(expected_text)
      end
    end

    context "with private help ticket comments" do
      subject { described_class.new(comment3,company_user3.email) }
      let(:test_text) {
        "<div><!--block-->{ticket_comments}</div>"
      }
      let(:expected_text) {
        """<div><!--block-->
          <table cellpadding='0' cellspacing='0' width='100%' style='padding: 10px;'>
            <tr>
              <td width='60' style='width: 60px; vertical-align: top;'>
                <table width='50' height='50' style='width: 50px; height: 50px; background-color: orange; color: white; text-align: center; vertical-align: middle; border-radius: 50%;'>
                  <tr>
                    <td style='height: 50px; width: 50px; background-color: orange; color: white; border-radius: 50%; text-align: center; display: table-cell; vertical-align: middle;'>
                      #{comment4.contributor && comment4.contributor.name.split.map(&:first).join.upcase}
                    </td>
                  </tr>
                </table>
              </td>
              <td width='10'></td>
              <td style='max-width: 430px; vertical-align: middle;'>
                <div>
                  #{comment4[:comment_body]}
                </div>
                <table width='100%'>
                  <tr>
                    <td style='font-size: 12px; color: #6C757D;'>
                      #{comment4.contributor&.name}, #{comment4[:created_at].in_time_zone(company.default_workspace.business_hour.timezone).strftime("%d %b %I:%M %p")}
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        </div>"""
      }
      it "executes the task action" do
        actual = subject.call(test_text)
        expect(actual).to eq(expected_text)
      end
    end

    context "without help ticket private comments" do
      subject { described_class.new(comment3,company_user4.email) }
      let(:test_text) {
        "<div><!--block-->{ticket_comments}</div>"
      }
      let(:expected_text) {
        """<div><!--block-->
          <table cellpadding='0' cellspacing='0' width='100%' style='padding: 10px;'>
            <tr>
              <td width='60' style='width: 60px; vertical-align: top;'>
                <table width='50' height='50' style='width: 50px; height: 50px; background-color: orange; color: white; text-align: center; vertical-align: middle; border-radius: 50%;'>
                  <tr>
                    <td style='height: 50px; width: 50px; background-color: orange; color: white; border-radius: 50%; text-align: center; display: table-cell; vertical-align: middle;'>
                      #{comment4.contributor && comment4.contributor.name.split.map(&:first).join.upcase}
                    </td>
                  </tr>
                </table>
              </td>
              <td width='10'></td>
              <td style='max-width: 430px; vertical-align: middle;'>
                <div>
                  #{comment4[:comment_body]}
                </div>
                <table width='100%'>
                  <tr>
                    <td style='font-size: 12px; color: #6C757D;'>
                      #{comment4.contributor&.name}, #{comment4[:created_at].in_time_zone(company.default_workspace.business_hour.timezone).strftime("%d %b %I:%M %p")}
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        </div>"""
      }
      it "executes the task action" do
        actual = subject.call(test_text)
        expect(actual).to eq(expected_text)
      end
    end
  end

  context "without a valid substituation" do
    let(:test_text) {
      "This ticket {boom} is cool."
    }

    it "executes the task action" do
      actual = subject.call(test_text)
      expect(actual).to eq(test_text)
    end
  end

  context "when resolution is true" do
    subject { described_class.new(comment5, company_user5) }

    let(:test_text) {
      "<div><{comment_body}</div>"
    }

    let(:expected_text) {
      "<div><Resolution!<div style='font-size: 0.875rem; margin-top: 0.2rem'> Ticket has been marked as <b> Closed </b> </div></div>"
    }

    it "Appends closed ticket text" do
      response = subject.call(test_text)
      expect(response).to eq(expected_text)
    end
  end
end
