require 'rails_helper'

RSpec.describe ForgotPasswordsService do
  let(:user) { create(:user) }
  subject { described_class.new(user) }

  describe '#send_reset_password_code' do
    context 'when user exists' do
      before do
        allow(subject).to receive_message_chain(:cognito_service, :send_forgot_password_request).and_return(true)
      end

      it 'returns success' do
        result = subject.send_reset_password_code
        expect(result).to eq({ success: true })
      end
    end

    context 'when user does not exist' do
      let(:user) { nil }

      it 'returns error' do
        result = subject.send_reset_password_code
        expect(result).to eq({ error: 'User does not exist.' })
      end
    end

    context 'when there is an error sending the email' do
      before do
        allow(subject).to receive_message_chain(:cognito_service, :send_forgot_password_request).and_return(false)
      end

      it 'returns error' do
        result = subject.send_reset_password_code
        expect(result).to eq({ error: 'Sorry, there was an error sending the email. Please try again.' })
      end
    end
  end

  describe '#update_password' do
    let(:params) { { user: { email: '<EMAIL>' } } }

    context 'when user exists' do
      before do
        allow(subject).to receive_message_chain(:cognito_service, :confirm_forgot_password).and_return(true)
      end

      it 'returns success' do
        result = subject.update_password(params)
        expect(result).to eq({ success: true })
      end
    end

    context 'when user does not exist' do
      let(:user) { nil }

      it 'returns error' do
        result = subject.update_password(params)
        expect(result).to eq({ error: 'User does not exist.' })
      end
    end

    context 'when there is an error updating the password' do
      before do
        allow(subject).to receive_message_chain(:cognito_service, :confirm_forgot_password).and_return(false)
      end

      it 'returns error' do
        result = subject.update_password(params)
        expect(result).to eq({ error: 'Sorry, there was an error updating the password. Please try again with a new verification code.' })
      end
    end
  end
end
