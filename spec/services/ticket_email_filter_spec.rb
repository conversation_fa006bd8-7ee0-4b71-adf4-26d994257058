# frozen_string_literal: true

require 'rails_helper'

describe HelpDesk::InboundEmail::TicketEmailFilter do
  subject { described_class.new(workspace_ids) }
  let(:domain) { ticket_email.from[(ticket_email.from.index("@") + 1)..-1] }
  let!(:workspace_ids) { company.workspaces.pluck(:id) }
  let(:workspace) { company.workspaces.first }
  let!(:company_user) { FactoryBot.create(:company_user) }
  let!(:company) { company_user.company }

  context "with no emails" do
    it 'will return no emails' do
      emails = subject.filter
      expect(emails).to be_empty
    end
  end

  context "with an email" do
    let!(:ticket_email) { 
      FactoryBot.create(:ticket_email, 
                        company: company, 
                        workspace: workspace, 
                        message_id: '1234',
                        s3_message_id: '2asd121Ijimk12iu212qw1')
    }

    context "with no blocked entities" do
      it 'will return no emails' do
        emails = subject.filter
        expect(emails).to be_present
        expect(emails.length).to eq(1)
        email_json = dull_email(ticket_email.as_json.merge({"attachments" => [], "first_name" => nil, "last_name" => nil}))
        expect(dull_email(emails.first)).to eq(email_json)
      end
    end

    context "with an email blocked entities" do
      let!(:blocked_entity) { company.blocked_entities.create!(entity: ticket_email.from, entity_type: 'email', workspace: workspace) }

      it 'will return no emails' do
        emails = subject.filter
        expect(emails).to be_empty
      end
    end

    context "with an domain blocked entities" do
      let!(:blocked_entity) { company.blocked_entities.create!(entity: domain, entity_type: 'domain', workspace: workspace) }

      it 'will return no emails' do
        emails = subject.filter
        expect(emails).to be_empty
      end
    end
  end

  context "with emails" do
    let!(:ticket_email) { 
      FactoryBot.create(:ticket_email, 
                        from: '<EMAIL>',
                        company: company, 
                        workspace: company.default_workspace,
                        message_id: '234',
                        s3_message_id: '2asd1212312dsADSSd121')
    }
    let!(:ticket_email2) { 
      FactoryBot.create(:ticket_email, 
                        company: company, 
                        from: '<EMAIL>',
                        workspace: company.default_workspace,
                        message_id: '455',
                        s3_message_id: '2asd1212312dsADSSsqwd121')
    }

    context "with no blocked entities" do
      it 'will return no emails' do
        emails = subject.filter
        expect(emails).to be_present
        expect(emails.length).to eq(2)
        expected = emails.map do |e|
          e["attachments"] = []
          e["first_name"] = nil
          e["last_name"] = nil
          dull_email(e)
        end
        actual = company.ticket_emails.order(created_at: :desc).as_json.map do |e|
          e["attachments"] = []
          e["first_name"] = nil
          e["last_name"] = nil
          dull_email(e)
        end
        expect(emails).to eq(actual)
      end
    end

    context "with an email blocked entities" do
      let!(:blocked_entity) { 
        company.blocked_entities.create!(entity: ticket_email.from, 
                                         workspace: workspace,
                                         entity_type: 'email') 
      }

      it 'will return second email' do
        emails = subject.filter
        expect(emails).to be_present
        expect(emails.length).to eq(1)
        expected = emails.first
        results = ticket_email2.as_json
        results["attachments"] = []
        results["first_name"] = nil
        results["last_name"] = nil
        expect(dull_email(expected)).to eq(dull_email(results))
      end
    end

    context "with an domain blocked entities" do
      let!(:blocked_entity) { 
        company.blocked_entities.create!(entity: domain,
                                         workspace: workspace,
                                         entity_type: 'domain') 
      }

      it 'will return second email' do
        emails = subject.filter
        expect(emails).to be_present
        expect(emails.length).to eq(1)
        email_json = dull_email(ticket_email2.as_json.merge({"attachments" => [], "first_name" => nil, "last_name" => nil}))
        expect(dull_email(emails.first)).to eq(email_json)
      end
    end
  end

  def dull_email(email)
    email.delete("created_at")
    email.delete("updated_at")
    email["workspace"] = {
      id: workspace.id,
      name: workspace.name,
    }
    email["company"] = {
      id: company.id,
      name: company.name,
    }
    email
  end
end
