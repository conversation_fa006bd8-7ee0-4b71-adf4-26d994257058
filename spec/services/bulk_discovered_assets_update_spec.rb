require 'rails_helper'
include <PERSON><PERSON><PERSON><PERSON><PERSON>per

describe BulkDiscoveredAssetsUpdate do
  describe "bulk update assets" do
    create_company_and_user
    context "bulk update discovered assets" do
      let(:company1) { create(:company) }
      let(:user) { create(:user) }
      let(:company_user) { create(:company_user, user: user, company: company1) }
      let!(:discovered_asset1) { create(:discovered_asset, company: company1, display_name: "192.88.77", mac_addresses: ['2C:53:B3 X2:W2:L2'], machine_serial_no: "CND5033423", manufacturer:"hp", source: 1) }
      let!(:discovered_asset2) { create(:discovered_asset, company: company1, display_name: "iphone asset", mac_addresses: ['2C:53:B3 X2:W2:L3'], machine_serial_no: "CND5339876", manufacturer: "dells", source: 1) }
      let!(:assets_list) { [discovered_asset1, discovered_asset2] }

      it "will bulk import discovered assets to managed assets" do
        expected_asset_list = described_class.new(assets_list, company1, company_user).import_assets
        asset = company1.discovered_assets
        expect(asset.first.managed_asset).to be_present
        expect(asset.last.managed_asset).to be_present
      end

      it "will bulk delete discovered assets to managed assets" do
        expected_asset_list = described_class.new(company1.discovered_assets, company1, company_user).delete_assets
        asset = company1.discovered_assets
        expect(asset.first).not_to be_present
        expect(asset.last).not_to be_present
      end

      it "will bulk archive discovered assets to managed assets" do
        expected_asset_list = described_class.new(company1.discovered_assets, company1, company_user).archive_assets
        asset = company1.discovered_assets
        expect(asset.first.status).to eq("ignored")
        expect(asset.last.status).to eq("ignored")
      end

      it "will bulk unarchive discovered assets to managed assets" do
        expected_asset_list = described_class.new(company1.discovered_assets, company1, company_user).unarchive_assets
        asset = company1.discovered_assets
        expect(asset.first.status).to eq("ready_for_import")
        expect(asset.last.status).to eq("ready_for_import")
      end
    end
  end
end
