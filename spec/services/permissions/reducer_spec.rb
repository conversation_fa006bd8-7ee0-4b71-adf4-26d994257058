# frozen_string_literal: true

require 'rails_helper'

describe Permissions::Reducer do
  let!(:custom_form) { FactoryBot.create(:custom_form, company: company, company_module: "company_user", description: "Test Description") }
  let!(:company_user) { FactoryBot.create(:company_user, company: company, custom_form_id: custom_form.id) }
  let!(:company_user2) { FactoryBot.create(:company_user, company: company, custom_form_id: custom_form.id) }
  let!(:company_user3) { FactoryBot.create(:company_user, company: company, custom_form_id: custom_form.id) }
  let!(:company) { FactoryBot.create(:company) }
  let(:workspace) { company.workspaces.first }
  subject { Permissions::Reducer.new(company_user).calculate }

  context "without a single group" do
    it "should return 1 permission" do
      expect(subject.permissions).to be_present
      expect(subject.permissions['HelpTicket'][workspace.id]).to eq(['basicread', ["Everyone"]])
    end
  end

  context "with a simple group" do
    let!(:group) {
      g = Group.create(name: 'Group', company: company)
      g.group_members << GroupMember.new(contributor: company_user.contributor)
      g.contributor.privileges << Privilege.new(permission_type: 'read', name: 'CompanyUser')
      g
    }
    subject { Permissions::Reducer.new(company_user).calculate }

    it "should have set the permission" do
      expect(subject.permissions.size).to eq(2)
      expect(subject.permissions['HelpTicket'][workspace.id]).to eq(["basicread", ["Everyone"]])
      expect(subject.permissions['CompanyUser']['root']).to eq(["read", ["Group"]])
    end
  end

  context "with a circular recusive groups" do
    let!(:group1) {
      g = Group.create(name: 'Group', company: company)
      g.contributor.privileges << Privilege.new(permission_type: 'read', name: 'CompanyUser')
      g.contributor.privileges << Privilege.new(permission_type: 'read', name: 'ManagedAsset')
      g
    }
    let!(:group2) {
      g = Group.create(name: 'Another Group', company: company)
      g.contributor.privileges << Privilege.new(permission_type: 'write', name: 'CompanyUser')
      g.contributor.privileges << Privilege.new(permission_type: 'write', name: 'HelpTicket', workspace: workspace)
      g
    }

    before do
      group1.group_members << GroupMember.new(contributor: group2.contributor)
      group2.group_members << GroupMember.new(contributor: group1.contributor)
      group1.group_members << GroupMember.new(contributor: company_user.contributor)
    end

    it "should have set the permission" do
      expect(subject.permissions.size).to eq(3)
      expect(subject.permissions['CompanyUser']['root']).to eq(["write", ["Another Group"]])
      expect(subject.permissions['ManagedAsset']['root']).to eq(["read", ["Group"]])
      expect(subject.permissions['HelpTicket'][workspace.id]).to eq(["write", ["Another Group"]])
    end
  end

  context "when the company user doesn't belong to any of the groups" do
    let!(:group1) {
      g = Group.create(name: 'Group', company: company)
      g.contributor.privileges << Privilege.new(permission_type: 'read', name: 'CompanyUser')
      g.contributor.privileges << Privilege.new(permission_type: 'read', name: 'ManagedAsset')
      g
    }
    let!(:group2) {
      g = Group.create(name: 'Another Group', company: company)
      g.contributor.privileges << Privilege.new(permission_type: 'write', name: 'CompanyUser')
      g.contributor.privileges << Privilege.new(permission_type: 'write', name: 'HelpTicket', workspace: workspace)
      g
    }

    before do
      group1.group_members << GroupMember.new(contributor: group2.contributor)
      group2.group_members << GroupMember.new(contributor: group1.contributor)
    end

    it "should have just the default permissions" do
      expect(subject.permissions).to be_present
      expect(subject.permissions.size).to eq(1)
      expect(subject.permissions['HelpTicket']).to eq({ workspace.id=>["basicread", ["Everyone"]] })
    end
  end

  context "when the Everyone group has the privileges" do
    let!(:group1) {
      g = Group.create(name: 'Group', default: true, include_all: true, company: company)
      g.contributor.privileges << Privilege.new(permission_type: 'read', name: 'CompanyUser')
      g.contributor.privileges << Privilege.new(permission_type: 'read', name: 'ManagedAsset')
      company_user.company.groups << g
      g
    }

    it "should have set the permission" do
      expect(subject.permissions.size).to eq(3)
      expect(subject.permissions['HelpTicket']).to eq({ workspace.id=>["basicread", ["Everyone"]] })
      expect(subject.permissions['CompanyUser']).to eq({ "root"=>["read", ["Group"]] })
      expect(subject.permissions['ManagedAsset']).to eq({ "root"=>["read", ["Group"]] })
    end
  end

  context "with differing workspace privilege" do
    let(:workspace2) { Workspace.create(company: company, name: 'Workspace' ) }
    let!(:group1) {
      g = Group.create(name: 'Group', company: company)
      g.contributor.privileges << Privilege.create!(permission_type: 'read', workspace: workspace, name: 'HelpTicket', contributor: g.contributor)
      g
    }
    let!(:group2) {
      g = Group.create(name: 'Another Group', company: company)
      g.contributor.privileges << Privilege.create!(permission_type: 'write', workspace: workspace2, name: 'HelpTicket', contributor: g.contributor)
      g
    }

    before do
      group1.group_members << GroupMember.new(contributor: group2.contributor)
      group2.group_members << GroupMember.new(contributor: group1.contributor)
      group1.group_members << GroupMember.new(contributor: company_user.contributor)
    end

    it "should have set the reduced permission" do
      expect(subject.permissions['HelpTicket']).to eq({ workspace.id=>["read", ["Group"]], workspace2.id=>["write", ["Another Group"]] })
    end
  end

  context "with an overriding workspace privilege" do
    let!(:group1) {
      g = Group.create(name: 'Group', company: company)
      g.contributor.privileges << Privilege.new(permission_type: 'read', name: 'HelpTicket', workspace: workspace)
      g
    }
    let!(:group2) {
      g = Group.create(name: 'Another Group', company: company)
      g.contributor.privileges << Privilege.new(permission_type: 'write', workspace: workspace, name: 'HelpTicket')
      g
    }

    before do
      group2.group_members << GroupMember.new(contributor: group1.contributor)
      group1.group_members << GroupMember.new(contributor: company_user.contributor)
    end

    it "should have set the permission to just write" do
      # if you have write, you have read/write to everything
      expect(subject.permissions['HelpTicket']).to eq({ workspace.id=>["write", ["Another Group"]] })
    end
  end
end
