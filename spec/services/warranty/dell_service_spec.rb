# frozen_string_literal: true

require 'rails_helper'

describe Warranty::DellService do
  let(:company) { create(:company) }
  let(:warranty_date) { subject[:date] }
  let(:subject) {
    described_class.new(company).warranty_expiry_date(
      machine_serial_number: 'ABC123',
      product_number: '123456'
    )
  }

  context "#warranty_expiry_date" do
    context "with a good response" do
      let(:date) { Date.today }
      let(:token) { 
        OpenStruct.new({ 
          body: "{\"access_token\": \"#{SecureRandom.hex(32)}\"}"
        })
      }
      let(:response) { 
        OpenStruct.new({ 
          body: "[ { \"id\": \"*********\", \"entitlements\": [ {  \"endDate\": \"#{date}\", \"serviceLevelCode\": \"NU\" }] }]"
        })
      }
      before do
        expect(RestClient).to receive(:get).and_return(response)
        expect(RestClient).to receive(:post).and_return(token)
      end

      it "should return date" do
        expect(warranty_date).to be_present
        expect(Date.parse(warranty_date)).to eq(date)
      end

      it "should create a successful ApiEvent" do
        subject
        expect(Logs::ApiEvent.count).to eq(1)
        expect(Logs::ApiEvent.first.status).to eq('success')
      end
    end

    context "with a bad response" do
      let(:token) { 
        OpenStruct.new({ 
          body: "{\"access_token\": \"#{SecureRandom.hex(32)}\"}"
        })
      }
      let(:response) { 
        OpenStruct.new({ 
          body: '[ { "id": "*********", "entitlements": [ {  "endDate": null } ] } ]'
        })
      }
      before(:each) do
        expect(RestClient).to receive(:get).and_return(response)
        expect(RestClient).to receive(:post).and_return(token)
      end

      it "should return an error message" do
        expect(subject[:message]).to be_present
      end

      it "should not return date" do
        expect(warranty_date).to be_nil
      end

      it "should create an error ApiEvent" do
        subject
        expect(Logs::ApiEvent.count).to eq(1)
        expect(Logs::ApiEvent.first.status).to eq('error')
      end
    end

    context "with a really bad error" do
      let(:token) { 
        OpenStruct.new({ 
          body: "{\"access_token\": \"#{SecureRandom.hex(32)}\"}"
        })
      }
      before(:each) do
        expect(RestClient).to receive(:get).and_raise("This is an error")
        expect(RestClient).to receive(:post).and_return(token)
      end

      it "should return an error message" do
        expect(subject[:message]).to be_present
      end

      it "should return date" do
        expect(warranty_date).to be_nil
      end

      it "should create a successful ApiEvent" do
        subject
        expect(Logs::ApiEvent.count).to eq(1)
        expect(Logs::ApiEvent.first.status).to eq('error')
      end
    end
  end
end