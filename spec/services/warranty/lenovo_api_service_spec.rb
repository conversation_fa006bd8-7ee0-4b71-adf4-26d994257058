require 'rails_helper'

describe Warranty::LenovoApiService do
  let(:company) { create(:company) }
  let(:subject) do
    described_class.new(company).warranty_expiry_date({
      machine_serial_number: 'ABC123'
    })
  end

  context '#warranty_expiry_date' do
    context 'with a good response' do
      let(:date) { Date.parse('2999-01-29T00:00:00Z') }
      let(:response) do
        {
          "Serial": "ABC123",
          "Product": "THINKPAD-L-SERIES-LAPTOPS",
          "InWarranty": true,
          "Purchased": "0001-01-01T00:00:00",
          "Shipped": "2019-01-27T00:00:00Z",
          "Country": "IN",
          "UpgradeUrl": "https://support.lenovo.com/warrantylookup/upgradewarranty",
          "Warranty": [
            {
              "ID": "AS1",
              "Name": "5Y On-site, 9X5 (APOS)",
              "Description": "This product has a five year limited warranty which includes a warranty upgrade.",
              "Type": "UPGRADE",
              "Start": "2019-01-30T00:00:00Z",
              "End": "2999-01-29T00:00:00Z"
            }
          ]
        }.to_json
      end

      it 'should return date and create a successfull ApiEvent' do
        expect(RestClient).to receive(:get).and_return(response)
        expect(subject[:date]).to be_present
        expect(Date.parse(subject[:date])).to eq(date)
        event = Logs::ApiEvent.find_by(class_name: "Warranty::LenovoApiService", status: "success")
        expect(event).to be_present
      end
    end

    context 'with a bad response' do
      let(:response) do
        {
          "Serial": "ABC123",
          "ErrorCode": 100,
          "ErrorMessage": "Serial Not Found"
        }.to_json
      end

      it 'should not return date and create an error ApiEvent' do
        expect(RestClient).to receive(:get).and_return(response)
        expect(subject[:date]).to be_nil
        expect(subject[:message]).to eq('Serial Not Found')
        event = Logs::ApiEvent.find_by(class_name: "Warranty::LenovoApiService", status: "error")
        expect(event).to be_present
      end
    end
  end
end
