require 'rails_helper'

describe WarrantyInfoService do
  subject { described_class.new(managed_asset) }

  context "test api call for HP PCs" do
    let(:managed_asset) { FactoryBot.create(:managed_asset, :laptop, manufacturer: 'hp') }

    it 'stubs POST request to HP warranty api' do
      expiry = "2017-06-27"
      _hash = subject.get_warranty_expiry_date( { machine_serial_number: "5CD6171GHT" } )
    end
  end

  context "test api call for DELL PCs" do
    let(:managed_asset) { FactoryBot.create(:managed_asset, :laptop, manufacturer: 'dell') }
  end
end
