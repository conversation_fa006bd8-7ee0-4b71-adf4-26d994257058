require 'rails_helper'
require 'faker'

describe "perform cognito operations" do
  let!(:domain_validator) { 
    DomainValidator.any_instance.stub(:validate).and_return(true)
  }
  let(:user_attributes) do
    {
      email: Faker::Internet.email,
      password: "password2",
      first_name: "<PERSON>",
      last_name: "<PERSON>",
      guid: "7c2a931c-2a50-4e04-85cf-5e1169f6bf79",
      terms_of_services: true
    }
  end

  let(:user_2_attributes) do
    {
      email: Faker::Internet.email,
      password: "password2",
      first_name: "<PERSON>",
      last_name: "<PERSON>",
      guid: "7c2a931c-2a50-9iu9-85cf-5e1169f6bf79",
      terms_of_services: true
    }
  end

  let!(:user) { create(:user, user_attributes) }

  context "user signing up" do
    it "will sign-up new user with good params" do
      client = CognitoService.new(user)
      resp = client.sign_up_user
      expect(resp.user.username).to be_present
    end
  end

  context "forgot password" do
    it "will sign-up local valid user to cognito and send forgot password request" do
      client = CognitoService.new(user)
      resp = client.forgot_password
      user.reload

      expect(user.encrypted_password).to eq(nil)
      expect(resp).to be_present
      expect(resp.code_delivery_details.delivery_medium).to eq("EMAIL")
      
      # delete user from cognito pool
      expect(client.delete_cognito_user).not_to be_nil
    end

    it "will send forgot password request to cognito user" do
      #creating new cognito user
      user_2 = User.create(user_2_attributes)
      client = CognitoService.new(user_2)
      resp = client.sign_up_user
      expect(resp.user.username).to be_present
      
      #saving cognito guid to user
      user_2.guid = resp.user.username
      user_2.save
      
      #sending forgot password code
      resp = client.forgot_password
      expect(resp).to be_present
      expect(resp.code_delivery_details.delivery_medium).to eq("EMAIL")

      # delete user from cognito pool
      expect(client.delete_cognito_user).not_to be_nil
    end
  end
end