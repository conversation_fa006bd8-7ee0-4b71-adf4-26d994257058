require "rails_helper"

describe ApplicationHelper do
  describe "#free_trial_days with days remaining" do
    let!(:company) { create(:company) }

    helper do
      def current_company; end
    end

    before do
      allow(helper).to receive(:current_company).and_return(company)
    end

    it "returns company free trail days left" do
      expect(helper.free_trial_days).to eq(28)
    end
  end

  describe "#free_trial_days with no days remaining" do
    let!(:company) { create(:company, created_at: Date.today - 40.days) }

    helper do
      def current_company; end
    end

    before do
      allow(helper).to receive(:current_company).and_return(company)
    end

    xit "returns 0" do
      expect(helper.free_trial_days).to eq(0)
    end
  end

  describe "#free_trial_days with active subscription" do
    let!(:company) { create(:company) }
    let!(:subscription_plan) { SubscriptionPlan.create(name: "Basic Plan", price: 2999, interval: "month", status: "active", stripe_identifier: 'basic-plan') }
    let!(:subscription) { create(:subscription, company: company, status: Subscription.statuses[:active], subscription_plan_id: subscription_plan.id) }

    helper do
      def current_company; end
    end

    before do
      allow(helper).to receive(:current_company).and_return(company)
    end

    it "returns nil" do
      expect(helper.free_trial_days).to eq(nil)
    end
  end
end