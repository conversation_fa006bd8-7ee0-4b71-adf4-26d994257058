require 'rails_helper'

describe InsightHelper do
  describe 'fiscal year' do
    let!(:company) { create(:company, fiscal_year: Date.strptime('2001-10-01').to_datetime) }
    helper do
      def current_company; end
    end

    before do
      allow(helper).to receive(:current_company).and_return(company)
    end

    it 'will check active fiscal month' do
      expect(helper.fiscal_year_start).to eq(company.fiscal_year)
    end

    it 'should return fiscal pivots months' do
      expect(helper.pivot_fiscal_months).to match_array([10, 11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9])
    end

    it 'should return fiscal year quarters' do
      expect(helper.quarters).to match_array([[10, 11, 12], [1, 2, 3], [4, 5, 6], [7, 8, 9]])
    end

    it 'will return date range between fiscal quater' do
      company_fiscal_year = Time.current.beginning_of_year + (company.fiscal_year.month - 1).months
      expect(helper.date_range.to_s).to eq((company_fiscal_year...Time.current.end_of_year).to_s)
    end

    it 'will return current month' do
      pivot_months = [10, 11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
      expect(helper.current_fiscal_month).to eq(pivot_months.find_index(Time.current.month - 1))
    end

    it 'will check year is fiscal ' do
      expect(helper.company_fiscal_month).to eq(10)
    end
  end

  describe 'not fiscal_year' do
    let!(:company) { create(:company)}
    helper do
      def current_company; end
    end

    before do
      allow(helper).to receive(:current_company).and_return(company)
    end

    it 'will check fiscal month' do
      expect(helper.fiscal_year_start.to_date).to eq(DateTime.now.beginning_of_year.to_date)
    end

    it 'should return fiscal pivots months' do
      expect(helper.pivot_fiscal_months).to match_array([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11])
    end

    it 'should return fiscal year quarters' do
      expect(helper.quarters).to match_array([[1, 2, 3], [4, 5, 6], [7, 8, 9], [10, 11, 12]])
    end

    it 'will return date range between fiscal quater' do
      expect(helper.date_range).to eq(Date.today.all_year)
    end
  end
end
