require 'rails_helper'
include CompanyUserHelper

describe PermissionsHelper do
  create_company_and_user

  describe 'fetch permission' do
    it 'returns all permissions of company user' do
      privileges = helper.fetch_comapny_user_permissions(company_user)
      modules = [
                  'CompanyUser',
                  'Contract',
                  'TelecomService',
                  'ManagedAsset',
                  'Monitoring',
                  'Vendor',
                  "MobileApp",
                  'HelpTicket'
                ]

      expect(privileges.length).to eq(8)
      expect(privileges.pluck(:name).sort).to eq(modules.sort)

      privileges.each do |privilege|
        if privilege[:name] == 'HelpTicket'
          expect(privilege[:permission].length).to eq(1)
          expect(privilege[:permission].first[:name]).to eq('Helpdesk')
          expect(privilege[:permission].first[:permission]).to eq('Write All')
          expect(privilege[:permission].class).to eq(Array)
        else
          expect(privilege[:permission]).to eq('Write All')
          expect(privilege[:permission].class).to eq(String)
        end
      end
    end
  end
end
