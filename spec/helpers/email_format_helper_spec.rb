require 'rails_helper'

describe EmailFormatHelper do
  describe 'check in current company' do
    let(:company) { create(:company) }
    let!(:email_format) { create(:email_format, company: company) }

    before do
      @current_company = company
    end

    it 'has header text' do
      expect(helper.header_text).to_not be_empty
      expect(helper.header_text).to eq('Header text')
    end

    it 'has footer text' do
      expect(helper.footer_text).to_not be_empty
      expect(helper.footer_text).to eq('Footer text')
    end
  end

  describe 'check in company ' do
    let(:company) { create(:company) }
    let!(:email_format) { create(:email_format, company: company) }

    before do
      @company = company
    end

    it 'has header text' do
      expect(helper.header_text).to_not be_empty
      expect(helper.header_text).to eq('Header text')
    end

    it 'has footer text' do
      expect(helper.footer_text).to_not be_empty
      expect(helper.footer_text).to eq('Footer text')
    end
  end
end
