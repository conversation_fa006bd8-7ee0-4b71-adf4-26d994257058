require 'rails_helper'

include CompanyUserHelper
include MobileLoginHelper

RSpec.describe AuthorizeApiRequest do
  create_company_and_user

  let(:headers) { { 'Authorization' => token } }
  let(:token) { token_generator(user.id) }

  describe '#call', :skip_login do
    context 'when the token is valid' do
      it 'returns the user' do
        command = described_class.new(headers)

        expect(command.call).to be_success
        expect(command.call.result).to eq(user)
      end
    end

    context 'when the token is invalid' do
      let(:token) { 'invalid token' }

      it 'returns an error' do
        command = described_class.new(headers)

        expect { command.call }.to raise_error(JWT::DecodeError, 'Not enough or too many segments')
      end
    end

    context 'when the token is missing' do
      let(:headers) { {} }

      it 'returns an error' do
        command = described_class.new(headers)

        expect { command.call }.to raise_error(JWT::DecodeError, 'Nil JSON web token')
      end
    end
  end
end
