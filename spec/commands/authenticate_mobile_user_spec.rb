require 'rails_helper'

include Company<PERSON>serHelper

describe AuthenticateMobileUser do
  create_company_and_user

  let(:blank_params) { { email: user.email, password: '' } }
  let(:valid_params) { { email: user.email, password: 'password' } }
  let(:client_stub) { instance_double(Aws::CognitoIdentityProvider::Client) }
  let(:invalid_params) { { email: user.email, password: 'invalid_password' } }

  let(:company_attributes) do
    {
      subdomain: 'test1',
      name: "Example Company1",
      phone_number: "**********"
    }
  end

  let(:second_company) { create(:company, company_attributes) }
  let(:company_user_2) { create(:company_user, user: user, company: second_company, granted_access_at: 1.month.ago) }

  describe '#call', :skip_login do
    context 'with valid params' do
      subject { described_class.call(valid_params[:email], valid_params[:password]) }

      before do
        allow(Aws::CognitoIdentityProvider::Client).to receive(:new).and_return(client_stub)
      end

      it 'returns an array containing a token and a boolean indicating if the user has only 1 company' do
        expect_any_instance_of(AuthenticateMobileUser).to receive(:validate_from_cognito).and_return("success")

        expect(subject.result).to be_an(Array)
        expect(subject.result.length).to eq(3)
        expect(subject.result[0]).to be_a(String)
        expect(subject.result[1]).to eq(false)
      end

      it 'returns an array containing a token and a boolean indicating if the user has multiple companies' do
        expect_any_instance_of(AuthenticateMobileUser).to receive(:validate_from_cognito).and_return("success")
        company_user_2

        expect(subject.result).to be_an(Array)
        expect(subject.result.length).to eq(3)
        expect(subject.result[0]).to be_a(String)
        expect(subject.result[1]).to eq(true)
      end

      it 'returns an array containing a nil if the user has zero company' do
        expect_any_instance_of(AuthenticateMobileUser).to receive(:user_companies).and_return(0)
        expect_any_instance_of(AuthenticateMobileUser).to receive(:validate_from_cognito).and_return(true)

        expect(subject.result).to be_an(Array)
        expect(subject.result.length).to eq(1)
        expect(subject.result[0]).to eq([nil])
      end
    end

    context 'with invalid params' do
      it 'returns an array containing nil if validate_from_cognito return fail' do
        allow(Aws::CognitoIdentityProvider::Client).to receive(:new).and_return(client_stub)
        expect_any_instance_of(AuthenticateMobileUser).to receive(:validate_from_cognito).and_return(false)

        subject = described_class.call(invalid_params[:email], invalid_params[:password])

        expect(subject.result).to eq([[nil]])
        expect(subject.errors[:user_authentication]).to eq(["invalid credentials"])
      end

      it 'returns an array containing nil if password is blank' do
        subject = described_class.call(blank_params[:email], blank_params[:password])

        expect(subject.result).to eq([[nil]])
        expect(subject.errors[:user_authentication]).to eq(["invalid credentials"])
      end
    end
  end

  describe '#current_user', :skip_login do
    context 'when email is present' do
      subject { described_class.new(user.email, 'password').current_user }

      it 'returns the user with the specified email' do
        expect(subject).to eq(user)
      end
    end

    context 'when email is not present' do
      subject { described_class.new(nil, 'password').current_user }

      it 'returns nil' do
        expect(subject).to be_nil
      end
    end
  end
end
