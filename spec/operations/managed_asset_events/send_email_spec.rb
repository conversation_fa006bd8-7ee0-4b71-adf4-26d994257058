require 'rails_helper'
include CompanyUserHelper

describe ManagedAssetEvents::SendEmail do
  create_company_and_user

  let(:managed_asset) { FactoryBot.create(:managed_asset, company: company, alert_dates: [AlertDate.new(date: Date.today, recipients: [company_user.contributor_id])]) }

  it "Sends email to alert recipients" do
    mail_args = {
      sender_email: "<EMAIL>",
      receiver_email: company_user.email,
      subject: "#{managed_asset.name} Alert",
      managed_asset: managed_asset,
      notes: managed_asset.alert_dates.last.notes.present? ?  managed_asset.alert_dates.last.notes : "An alert for #{managed_asset.name}."
    }
    mailer = ManagedAssetMailer.event_email(mail_args)
    expect(ManagedAssetMailer).to receive(:event_email).with(mail_args).and_return(mailer)
    described_class.execute()
  end
end
