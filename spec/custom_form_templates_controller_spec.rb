require 'rails_helper'
include CompanyUserHelper

RSpec.describe CustomFormTemplatesController, type: :controller do
  create_company_and_user

  describe "GET #index" do
    context "when company_module is present" do
      let!(:blank_state_form) { create(:custom_form_template, company_module: 'company_user', form_name: 'Blank State1') }
      let!(:teammates_form) { create(:custom_form_template, company_module: 'company_user', form_name: 'Partners1') }
      let!(:partners_form) { create(:custom_form_template, company_module: 'company_user', form_name: 'Teammates1') }
      let!(:contractors_form) { create(:custom_form_template, company_module: 'company_user', form_name: 'Contractors1') }

      it "filters forms by company_module and sorts by form_name" do
        get :index, params: { company_module: 'company_user' }

        expect(response).to have_http_status(:ok)
        parsed_response = JSON.parse(response.body)
        expect(parsed_response.count).to eq(8)
        expect(parsed_response[0]["form_name"]).to eq("Blank State")
        expect(parsed_response[1]["form_name"]).to eq("Teammates")
        expect(parsed_response[2]["form_name"]).to eq("Partners")
        expect(parsed_response[3]["form_name"]).to eq("Contractors")
        expect(parsed_response[4]["form_name"]).to eq("Blank State1")
        expect(parsed_response[5]["form_name"]).to eq("Partners1")
        expect(parsed_response[6]["form_name"]).to eq("Teammates1")
        expect(parsed_response[7]["form_name"]).to eq("Contractors1")
      end
    end
  end

  describe "GET #show" do
    context "when the form is found" do
      let!(:form) { create(:custom_form_template, company_module: 'company_user') }

      it "returns the form with the given id" do
        get :show, params: { id: form.id }
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)["id"]).to eq(form.id)
      end
    end

    context "when the form is not found" do
      it "returns a 404 status" do
        get :show, params: { id: 0 }
        expect(response).to have_http_status(:not_found)
        expect(JSON.parse(response.body)).to be_empty
      end
    end
  end
end
