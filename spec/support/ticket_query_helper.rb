module Ticket<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  def setup_tickets
    company = Company.find_by(subdomain: 'test-company')
    user1 = FactoryBot.create(:user, email: '<EMAIL>', first_name: '<PERSON>')
    user2 = FactoryBot.create(:user, email: '<EMAIL>', first_name: '<PERSON>')
    user3 = FactoryBot.create(:user, email: '<EMAIL>', first_name: '<PERSON>')

    # <PERSON> is a normal user
    george = FactoryBot.create(:company_user, company: company, user: user1)

    # <PERSON> and <PERSON> are agent types
    fred = FactoryBot.create(:company_user, company: company, user: user2)
    larry = FactoryBot.create(:company_user, company: company, user: user3)

    loc_form = company.custom_forms.find_by(company_module: :location)
    loc_1 = FactoryBot.build(:location, company: company, name: 'location1', custom_form: loc_form)
    loc_1.save(validate: false)
    loc_2 = FactoryBot.build(:location, company: company, name: 'location2', custom_form: loc_form)
    loc_2.save(validate: false)
    loc_3 = FactoryBot.build(:location, company: company, name: 'location3', custom_form: loc_form)
    loc_3.save(validate: false)
    managed_asset_1 = FactoryBot.create(:managed_asset, company: company, name: 'Asset 1')
    managed_asset_2 = FactoryBot.create(:managed_asset, company: company, name: 'Asset 2')
    managed_asset_3 = FactoryBot.create(:managed_asset, company: company, asset_tag: 'Ticket asset', name: 'Asset 3')
    telecom_provider = FactoryBot.create(:telecom_provider, company: company)
    telecom_1 = FactoryBot.create(:telecom_service, service_type: "data_service", telecom_provider: telecom_provider, company: company, name: 'Telecom 1')
    telecom_2 = FactoryBot.create(:telecom_service, service_type: "data_service", telecom_provider: telecom_provider, company: company, name: 'Telecom 2')
    telecom_3 = FactoryBot.create(:telecom_service, service_type: "data_service", telecom_provider: telecom_provider, company: company, name: 'Telecom 3')
    telecom_4 = FactoryBot.create(:telecom_service, service_type: "data_service", telecom_provider: telecom_provider, company: company, name: 'Telecom 4')
    vendor_1 = FactoryBot.create(:vendor, name: 'vendor1', company: company)
    vendor_2 = FactoryBot.create(:vendor, name: 'vendor2', company: company)
    vendor_3 = FactoryBot.create(:vendor, name: 'vendor3', company: company)
    vendor_4 = FactoryBot.create(:vendor, name: 'vendor4', company: company)
    contract_1 = FactoryBot.create(:contract, name: 'contract1', company: company)
    contract_2 = FactoryBot.create(:contract, name: 'contract2', company: company)
    contract_3 = FactoryBot.create(:contract, name: 'contract3', company: company)

    help_ticket_params = [
      {
        'Subject' => "One",
        'Created By' => george.contributor_id,
        'Assigned To' => [larry.contributor_id, fred.contributor_id],
        'Followers' => george.contributor_id,
        'Status' => 'In Progress',
        'Priority' => "low",
        'Description' => "What a great day!",
        'Contract' => [contract_1.id, contract_2.id],
        'Vendor' => [vendor_1.id, vendor_2.id],
        'Telecom' => [telecom_1.id, telecom_2.id],
        'Impacted Devices' => [managed_asset_1.id, managed_asset_2.id],
        'Location' => [loc_2.id, loc_3.id],
        'created_at' => 6.months.ago,
      },
      {
        'Subject' => "Two",
        'Created By' => george.contributor_id,
        'Assigned To' => fred.contributor_id,
        'Status' => 'Open',
        'Priority' => "medium",
        'Description' => "The red robin sits in the tree.",
        'Contract' => contract_3.id,
        'Vendor' => vendor_3.id,
        'Telecom' => telecom_3.id,
        'Impacted Devices' => managed_asset_3.id,
        'Location' => loc_3.id,
        'created_at' => 10.months.ago,
      },
      {
        'Subject' => "Three",
        'Created By' => george.contributor_id,
        'Followers' => nil,
        'Impacted Devices' => nil,
        'Vendor' => vendor_4.id,
        'Status' => 'Closed',
        'Priority' => "high",
        'Description' => "Its raining heavily.",
        'created_at' =>  Date.today.beginning_of_month - 1
      },
      {
        'Subject' => "Fourth",
        'Created By' => george.contributor_id,
        'Status' => 'In Progress',
        'Priority' => "high",
        'Description' => "The coffee stinks.",
        'created_at' => Date.today.beginning_of_month + 1
      },
      {
        'Subject' => "Fifth",
        'Created By' => larry.contributor_id,
        'Assigned To' => larry.contributor_id,
        'Status' => 'Archived',
        'Priority' => "medium",
        'Telecom' => telecom_4.id,
        'Description' => "This is a ticket.",
        'created_at' => 4.months.ago,
      },
      {
        'Subject' => "Sixth",
        'Created By' => george.contributor_id,
        'Assigned To' => george.contributor_id,
        'Status' => 'Open',
        'Priority' => "medium",
        'Description' => "The coffee maker is broken.",
        'created_at' => 1.year.ago,
      },
      {
        'Subject' => "Seventh",
        'Created By' => "<EMAIL>",
        'Assigned To' => larry.contributor_id,
        'Status' => 'Open',
        'Priority' => "low",
        'Description' => "A small dog is drinking coffee.",
        'created_at' => Date.today
      }
    ]
    help_ticket_params.map{ |p| create_ticket(p) }
    task1 = FactoryBot.create(:project_task, description: "This is first task", help_ticket_id: HelpTicket.first.id, due_at: DateTime.now + 3.day)
    task2 = FactoryBot.create(:project_task, description: "This is second task", help_ticket_id: HelpTicket.second.id, due_at: DateTime.now + 4.day)
    task3 = FactoryBot.create(:project_task, description: "This is third task", help_ticket_id: HelpTicket.third.id, due_at: DateTime.now + 15.day)
    task4 = FactoryBot.create(:project_task, description: "This is fourth task", help_ticket_id: HelpTicket.fifth.id, completed_at: DateTime.now)
    ht_a1 = FactoryBot.create(:help_ticket_activity, help_ticket: HelpTicket.first, activity_type: "status", owner: george, created_at: DateTime.now - 5.day)
    ht_a2 = FactoryBot.create(:help_ticket_activity, help_ticket: HelpTicket.third, activity_type: "status", owner: george, created_at: DateTime.now - 1.day)
  end
end
