module LocationHelper
  def location_helper_params(params)
    my_company = params[:company] || Company.find_by(subdomain: 'test-company')
    raise "Must have a company to create a location" unless my_company

    custom_form = params[:custom_form] || company.custom_forms.location.first
    return unless custom_form
    {
      company_id: my_company.id,
      custom_form: custom_form,
    }
  end

  def create_location(params = {})
    location_params = location_helper_params(params)
    location_params[:created_at] = params['created_at'].present? ? params['created_at'] : DateTime.now
    loc = FactoryBot.create(:location, location_params)
    custom_form = location_params[:custom_form]
    custom_form.custom_form_fields.each do |field|
      value = params[field.label]
      if value
        if value.is_a?(Array)
          value.each { |v| loc.custom_form_values << CustomFormValue.create(
            module_type: 'Location',
            module_id: loc.id,
            value: v, custom_form: custom_form,
            custom_form_field: field)
          }
        else
          loc.custom_form_values << CustomFormValue.create(
            module_type: 'Location',
            module_id: loc.id,
            value: value,
            custom_form: custom_form,
            custom_form_field: field)
        end
      end
    end
    loc.save(validate: false)
    loc
  end
end
