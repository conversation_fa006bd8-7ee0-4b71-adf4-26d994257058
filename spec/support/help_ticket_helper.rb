module HelpTicketHelper
  def workspace
    company.default_workspace
  end

  def ticket_helper_params(params)
    my_company = params[:company] || Company.find_by(subdomain: 'test-company')
    my_workspace = params[:workspace] || my_company&.default_workspace
    raise "Must have a workspace to create a ticket" unless my_workspace
    my_company ||= my_workspace.company
    raise "Must have a company to create a ticket" unless my_company

    custom_form = params[:custom_form] || my_workspace.custom_forms.find_by(company_module: 'helpdesk')
    raise "Must have a custom form" unless custom_form
    {
      company_id: my_company.id,
      custom_form: custom_form,
      workspace_id: my_workspace.id
    }
  end

  def create_custom_form(workspace)
    template = CustomFormTemplate.find_by(form_name: "IT General Request")
    attrs = template.attributes.except('id', 'created_at', 'updated_at', 'image_url', 'description')
    company = workspace.company
    attrs['company'] = company
    attrs['workspace'] = workspace
    attrs['form_name'] = 'IT General Request'
    attrs['default'] = true
    custom_form = workspace.custom_forms.find_by(default: true) || workspace.custom_forms.find_or_initialize_by(attrs)
    if !custom_form.id? && workspace.custom_forms.pluck(:id).compact.blank?
      email = "#{workspace.name.camelcase.gsub(/\s/, '').underscore}@#{company.subdomain}.#{Rails.application.credentials.root_domain}"
      custom_form.helpdesk_custom_form = HelpdeskCustomForm.new(email: email, show_in_open_portal: true)
      custom_form.save!
      fields = template.custom_form_field_templates.order('order_position ASC')
      fields.each_with_index do |field, idx|
        attrs = field.attributes.except('id', 'created_at', 'updated_at', 'custom_form_template_id')
        attrs[:custom_form_id] = custom_form.id
        attrs[:order_position] = idx
        if field.name === "created_by"
          attrs[:private] = false
        else
          attrs[:private] = ["people_list", "asset_list", "location_list", "contract_list", "vendor_list", "telecom_list"].include?( attrs["field_attribute_type"] )
        end
        new_field = custom_form.custom_form_fields.create(attrs)
        field_position = field.field_position_template
        if field_position
          FieldPosition.create(custom_form_field_id: new_field.id, position: field_position.position)
        end
      end
      custom_form.save!
    end
    custom_form
  end

  def create_ticket(params = {})
    ticket_params = ticket_helper_params(params)
    ticket_params[:ticket_number] = params["ticket_number"] if params["ticket_number"]
    ticket_params[:message_id] = params["message_id"] if params["message_id"]
    ticket_params[:created_at] = params['created_at'].present? ? params['created_at'] : DateTime.now
    ticket_params[:mute_notification] = params[:mute_notification].present?
    ticket_params[:status] = params["Status"] if params["Status"].present?
    ticket_params[:closed_at] = DateTime.now if ticket_params[:status] == 'Closed'
    ticket = FactoryBot.build(:help_ticket, ticket_params)
    ticket.skip_ticket_number_generation = params[:skip_save_ticket_number] if params[:skip_save_ticket_number].present?
    ticket[:source] = params["source"] if params["source"].present?
    custom_form = ticket_params[:custom_form]
    custom_form.custom_form_fields.each do |field|
      value = params[field.label]
      if value
        if value.is_a?(Array)
          value.each { |v|
            ticket.custom_form_values << CustomFormValue.create(
              module_type: 'HelpTicket',
              module_id: ticket.id,
              help_ticket: ticket,
              value: v,
              custom_form: custom_form,
              custom_form_field: field)
          }
        else
          ticket.custom_form_values << CustomFormValue.create(
            module_type: 'HelpTicket',
            module_id: ticket.id,
            help_ticket: ticket,
            value: value,
            custom_form: custom_form,
            custom_form_field: field)
        end
      end
    end
    ticket.save(validate: false)
    ticket
  end
end