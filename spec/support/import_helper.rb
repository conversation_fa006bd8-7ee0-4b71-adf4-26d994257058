module Import<PERSON><PERSON><PERSON>
  def trigger_import_worker(sheet, company_id, creator_id, entity, xls_import_id, run_pusher, options = nil, workspace_id = nil)
    import_params = {
      options: options,
      company_id: company_id,
      creator_id: creator_id,
      entity: entity,
      xls_import_id: xls_import_id,
      run_pusher: run_pusher,
      sheet_id: sheet.id,
      workspace_id: workspace_id
    }
    sheet.xls_import_rows.ready_for_import.each_slice(10) do |rows|
      import_params[:row_ids] = rows.pluck(:id)
      ImportProcessWorker.new.perform(import_params.to_json)
    end
  end
end
