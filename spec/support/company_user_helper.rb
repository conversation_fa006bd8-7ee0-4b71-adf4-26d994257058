require "./spec/support/login_helper.rb"

RSpec.configure do |c|
  c.include LoginHelper
end

module CompanyUserHelper
  def create_company_and_user(params={})
    let!(:domain_validator) {
      allow_any_instance_of(DomainValidator).to receive(:validate).and_return(true)
    }

    let!(:company) {
      subdomain = params[:subdomain] || 'test-company'
      my_company = Company.find_by(subdomain: subdomain) || FactoryBot.create(:company, subdomain: subdomain)
      if my_company.locations.blank?
        custom_form = my_company.create_default_location_custom_base_form
        name_field = custom_form.custom_form_fields.find_by(name: 'name')
        loc = build(:location, custom_form: custom_form)
        loc.custom_form_values << CustomFormValue.new(value_str: "HQ", custom_form_field: name_field, module: loc)
        my_company.locations << loc
      end
      my_company
    }

    let(:workspace) { company.workspaces.first }

    if params[:email]
      let!(:user) do
        my_user = create(:user, email: params[:email])
        c_user = create(:company_user, company: company, user_id: my_user.id)
        my_user.company_users << c_user
        my_user
      end
    else
      let!(:user) do
        my_user = create(:user)
        c_user = create(:company_user, company: company, user_id: my_user.id)
        my_user.company_users << c_user
        admins.group_members << GroupMember.new(contributor: c_user.contributor)
        # We need to manually call the expanded_privileges/populate call
        # since sidekiq jobs are disabled for specs
        ExpandedPrivileges::Populate.new(c_user.contributor).call
        my_user
      end
    end

    let(:company_user) { CompanyUser.where(user_id: user.id).first }
    let(:admins) { company.groups.find_by(name: 'Admins') }

    before do |example|
      if @request
        @request.host = "#{company.subdomain}.#{Rails.application.credentials.root_domain}"
        @request.headers['X-Genuity-Company-Id'] = "#{company.id}"
        @request.headers['X-Genuity-Workspace-Id'] = "#{workspace.id}"
        login_user unless example.metadata[:skip_login]
      end
    end
  end

  def create_msp_and_user(params={})
    if params[:subdomain].present?
      let!(:company) { create(:company, :with_locations, subdomain: params[:subdomain]) }
    else
      let!(:company) { create(:company, :with_locations) }
    end

    let!(:child_company) { create(:company, :with_locations, parent_company: company) }

    if params[:email]
      let!(:user) do
        my_user = create(:user, email: params[:email])
        my_user.company_users << create(:company_user, user: my_user, company: company)
        my_user.company_users << create(:company_user, user: my_user, company: child_company)
        my_user
      end
    else
      let!(:user) do
        my_user = create(:user)
        my_user.company_users << create(:company_user, user: my_user, company: company)
        my_user.company_users << create(:company_user, user: my_user, company: child_company)
        my_user
      end
    end
  end
end
